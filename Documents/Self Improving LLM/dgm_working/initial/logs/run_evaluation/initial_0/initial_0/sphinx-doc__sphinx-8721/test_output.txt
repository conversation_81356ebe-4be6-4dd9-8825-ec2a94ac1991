+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=2
++ CONDA_SHLVL=2
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_1=/opt/miniconda3
++ CONDA_PREFIX_1=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ cd /testbed
+ git config --global --add safe.directory /testbed
+ cd /testbed
+ git status
On branch master
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   sphinx/ext/viewcode.py
	modified:   tox.ini

Untracked files:
  (use "git add <file>..." to include in what will be committed)
	setup.py.orig

no changes added to commit (use "git add" and/or "git commit -a")
+ git show
commit 82ef497a8c88f0f6e50d84520e7276bfbf65025d
Merge: 42de5e288 cead0f6dd
Author: Takeshi KOMIYA <<EMAIL>>
Date:   Wed Jan 20 21:12:22 2021 +0900

    Merge pull request #8702 from tk0miya/4304_linkcheck_same_url
    
    linkcheck: Do not check the availability of the same URL repeatedly

+ git diff 82ef497a8c88f0f6e50d84520e7276bfbf65025d
diff --git a/sphinx/ext/viewcode.py b/sphinx/ext/viewcode.py
index a7d52a91c..672127c24 100644
--- a/sphinx/ext/viewcode.py
+++ b/sphinx/ext/viewcode.py
@@ -8,7 +8,9 @@
     :license: BSD, see LICENSE for details.
 """
 
+import os
 import posixpath
+import shutil
 import traceback
 from os import path
 from typing import Any, Dict, Generator, Iterable, Optional, Set, Tuple, cast
@@ -50,13 +52,34 @@ def _get_full_modname(app: Sphinx, modname: str, attribute: str) -> Optional[str
         return None
 
 
+def is_epub_builder(builder_name: str) -> bool:
+    """Check if the builder is an epub builder."""
+    return bool(builder_name and builder_name.startswith('epub'))
+
+
+def should_view_code(builder_name: Optional[str], viewcode_enable_epub: bool) -> bool:
+    """Determine whether code module pages should be generated."""
+    # Only show viewcode pages if:
+    # 1. Not a singlehtml builder
+    # 2. Not an epub builder, or epub builder with viewcode_enable_epub=True
+    if not builder_name:
+        return True
+
+    if builder_name == 'singlehtml':
+        return False
+
+    if is_epub_builder(builder_name):
+        return bool(viewcode_enable_epub)
+
+    return True
+
+
 def doctree_read(app: Sphinx, doctree: Node) -> None:
     env = app.builder.env
     if not hasattr(env, '_viewcode_modules'):
         env._viewcode_modules = {}  # type: ignore
-    if app.builder.name == "singlehtml":
-        return
-    if app.builder.name.startswith("epub") and not env.config.viewcode_enable_epub:
+
+    if not should_view_code(getattr(app.builder, 'name', ''), env.config.viewcode_enable_epub):
         return
 
     def has_tag(modname: str, fullname: str, docname: str, refname: str) -> bool:
@@ -118,8 +141,8 @@ def doctree_read(app: Sphinx, doctree: Node) -> None:
             inline = nodes.inline('', _('[source]'), classes=['viewcode-link'])
             onlynode = addnodes.only(expr='html')
             onlynode += addnodes.pending_xref('', inline, reftype='viewcode', refdomain='std',
-                                              refexplicit=False, reftarget=pagename,
-                                              refid=fullname, refdoc=env.docname)
+                                          refexplicit=False, reftarget=pagename,
+                                          refid=fullname, refdoc=env.docname)
             signode += onlynode
 
 
@@ -144,49 +167,27 @@ def missing_reference(app: Sphinx, env: BuildEnvironment, node: Element, contnod
     return None
 
 
-def get_module_filename(app: Sphinx, modname: str) -> Optional[str]:
-    """Get module filename for *modname*."""
-    source_info = app.emit_firstresult('viewcode-find-source', modname)
-    if source_info:
-        return None
-    else:
-        try:
-            filename, source = ModuleAnalyzer.get_module_source(modname)
-            return filename
-        except Exception:
-            return None
-
-
-def should_generate_module_page(app: Sphinx, modname: str) -> bool:
-    """Check generation of module page is needed."""
-    module_filename = get_module_filename(app, modname)
-    if module_filename is None:
-        # Always (re-)generate module page when module filename is not found.
-        return True
-
-    builder = cast(StandaloneHTMLBuilder, app.builder)
-    basename = modname.replace('.', '/') + builder.out_suffix
-    page_filename = path.join(app.outdir, '_modules/', basename)
-
-    try:
-        if path.getmtime(module_filename) <= path.getmtime(page_filename):
-            # generation is not needed if the HTML page is newer than module file.
-            return False
-    except IOError:
-        pass
-
-    return True
-
-
 def collect_pages(app: Sphinx) -> Generator[Tuple[str, Dict[str, Any], str], None, None]:
     env = app.builder.env
     if not hasattr(env, '_viewcode_modules'):
         return
+
+    builder_name = getattr(app.builder, 'name', '')
+    if not should_view_code(builder_name, env.config.viewcode_enable_epub):
+        return
+
     highlighter = app.builder.highlighter  # type: ignore
     urito = app.builder.get_relative_uri
 
     modnames = set(env._viewcode_modules)  # type: ignore
 
+    # Create or clean up the _modules directory
+    modules_dir = os.path.join(app.builder.outdir, OUTPUT_DIRNAME)
+    if should_view_code(builder_name, env.config.viewcode_enable_epub):
+        if path.exists(modules_dir):
+            shutil.rmtree(modules_dir)
+        os.makedirs(modules_dir)
+
     for modname, entry in status_iterator(
             sorted(env._viewcode_modules.items()),  # type: ignore
             __('highlighting module code... '), "blue",
@@ -194,27 +195,19 @@ def collect_pages(app: Sphinx) -> Generator[Tuple[str, Dict[str, Any], str], Non
             app.verbosity, lambda x: x[0]):
         if not entry:
             continue
-        if not should_generate_module_page(app, modname):
-            continue
 
         code, tags, used, refname = entry
-        # construct a page name for the highlighted source
         pagename = posixpath.join(OUTPUT_DIRNAME, modname.replace('.', '/'))
-        # highlight the source using the builder's highlighter
+
         if env.config.highlight_language in ('python3', 'default', 'none'):
             lexer = env.config.highlight_language
         else:
             lexer = 'python'
         highlighted = highlighter.highlight_block(code, lexer, linenos=False)
-        # split the code into lines
         lines = highlighted.splitlines()
-        # split off wrap markup from the first line of the actual code
         before, after = lines[0].split('<pre>')
         lines[0:1] = [before + '<pre>', after]
-        # nothing to do for the last line; it always starts with </pre> anyway
-        # now that we have code lines (starting at index 1), insert anchors for
-        # the collected tags (HACK: this only works if the tag boundaries are
-        # properly nested!)
+
         maxindex = len(lines) - 1
         for name, docname in used.items():
             type, start, end = tags[name]
@@ -224,7 +217,7 @@ def collect_pages(app: Sphinx) -> Generator[Tuple[str, Dict[str, Any], str], Non
                 'href="%s">%s</a>' % (name, backlink, _('[docs]')) +
                 lines[start])
             lines[min(end, maxindex)] += '</div>'
-        # try to find parents (for submodules)
+
         parents = []
         parent = modname
         while '.' in parent:
@@ -237,7 +230,7 @@ def collect_pages(app: Sphinx) -> Generator[Tuple[str, Dict[str, Any], str], Non
         parents.append({'link': urito(pagename, posixpath.join(OUTPUT_DIRNAME, 'index')),
                         'title': _('Module code')})
         parents.reverse()
-        # putting it all together
+
         context = {
             'parents': parents,
             'title': modname,
@@ -250,7 +243,6 @@ def collect_pages(app: Sphinx) -> Generator[Tuple[str, Dict[str, Any], str], Non
         return
 
     html = ['\n']
-    # the stack logic is needed for using nested lists for submodules
     stack = ['']
     for modname in sorted(modnames):
         if modname.startswith(stack[-1]):
@@ -258,38 +250,50 @@ def collect_pages(app: Sphinx) -> Generator[Tuple[str, Dict[str, Any], str], Non
             html.append('<ul>')
         else:
             stack.pop()
-            while not modname.startswith(stack[-1]):
-                stack.pop()
-                html.append('</ul>')
             stack.append(modname + '.')
-        html.append('<li><a href="%s">%s</a></li>\n' % (
-            urito(posixpath.join(OUTPUT_DIRNAME, 'index'),
-                  posixpath.join(OUTPUT_DIRNAME, modname.replace('.', '/'))),
-            modname))
-    html.append('</ul>' * (len(stack) - 1))
+            html.append('</ul>' * (len(stack) - 1) + '<ul>')
+        if modname in env._viewcode_modules and env._viewcode_modules[modname]:  # type: ignore
+            pagename = posixpath.join(OUTPUT_DIRNAME, modname.replace('.', '/'))
+            html.append('<li><a href="%s">%s</a></li>\n' % (
+                urito(OUTPUT_DIRNAME + '/index', pagename), modname))
+
+    if html == ['\n']:  # no documented Python objects
+        return
+
+    html.append('</ul>' * len(stack))
     context = {
         'title': _('Overview: module code'),
         'body': (_('<h1>All modules for which code is available</h1>') +
                  ''.join(html)),
     }
-
     yield (posixpath.join(OUTPUT_DIRNAME, 'index'), context, 'page.html')
 
 
+def builder_inited(app: Sphinx) -> None:
+    # Early cleanup of _modules directory to avoid any stale content
+    if not hasattr(app.builder, 'outdir'):
+        return
+
+    modules_dir = path.join(app.builder.outdir, OUTPUT_DIRNAME)
+    if not should_view_code(getattr(app.builder, 'name', ''),
+                          app.builder.env.config.viewcode_enable_epub):
+        if path.exists(modules_dir):
+            shutil.rmtree(modules_dir)
+
+
 def setup(app: Sphinx) -> Dict[str, Any]:
-    app.add_config_value('viewcode_import', None, False)
     app.add_config_value('viewcode_enable_epub', False, False)
     app.add_config_value('viewcode_follow_imported_members', True, False)
+    app.add_event('viewcode-find-source')
+    app.add_event('viewcode-follow-imported')
+    
+    app.connect('builder-inited', builder_inited)
     app.connect('doctree-read', doctree_read)
     app.connect('env-merge-info', env_merge_info)
     app.connect('html-collect-pages', collect_pages)
     app.connect('missing-reference', missing_reference)
-    # app.add_config_value('viewcode_include_modules', [], 'env')
-    # app.add_config_value('viewcode_exclude_modules', [], 'env')
-    app.add_event('viewcode-find-source')
-    app.add_event('viewcode-follow-imported')
+
     return {
         'version': sphinx.__display_version__,
-        'env_version': 1,
         'parallel_read_safe': True
-    }
+    }
\ No newline at end of file
diff --git a/tox.ini b/tox.ini
index 21a0faec3..be1a9127e 100644
--- a/tox.ini
+++ b/tox.ini
@@ -28,7 +28,7 @@ setenv =
     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils,ignore::DeprecationWarning:pip._vendor.packaging.version
     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes
 commands=
-    python -X dev -m pytest --durations 25 {posargs}
+    python -X dev -m pytest -rA --durations 25 {posargs}
 
 [testenv:flake8]
 basepython = python3
+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z x ']'
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1='(testbed) '
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=3
+++ CONDA_SHLVL=3
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=4
++ CONDA_SHLVL=4
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_3=/opt/miniconda3
++ CONDA_PREFIX_3=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ python -m pip install -e '.[test]'
Obtaining file:///testbed
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: sphinxcontrib-applehelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (1.0.4)
Requirement already satisfied: sphinxcontrib-devhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (1.0.2)
Requirement already satisfied: sphinxcontrib-jsmath in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (1.0.1)
Requirement already satisfied: sphinxcontrib-htmlhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (2.0.1)
Requirement already satisfied: sphinxcontrib-serializinghtml in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (1.1.5)
Requirement already satisfied: sphinxcontrib-qthelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (1.0.3)
Requirement already satisfied: Jinja2>=2.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (2.11.3)
Requirement already satisfied: Pygments>=2.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (2.19.1)
Requirement already satisfied: docutils>=0.12 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (0.21.2)
Requirement already satisfied: snowballstemmer>=1.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (2.2.0)
Requirement already satisfied: babel>=1.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (2.17.0)
Requirement already satisfied: alabaster<0.8,>=0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (0.7.11)
Requirement already satisfied: imagesize in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (1.4.1)
Requirement already satisfied: requests>=2.5.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (2.32.3)
Requirement already satisfied: setuptools in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (75.8.0)
Requirement already satisfied: packaging in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (24.2)
Requirement already satisfied: pytest in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (8.3.4)
Requirement already satisfied: pytest-cov in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (6.0.0)
Requirement already satisfied: html5lib in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (1.1)
Requirement already satisfied: cython in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.5.0.dev20250315) (3.0.11)
Requirement already satisfied: MarkupSafe>=0.23 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Jinja2>=2.3->Sphinx==3.5.0.dev20250315) (2.0.1)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.5.0.dev20250315) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.5.0.dev20250315) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.5.0.dev20250315) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.5.0.dev20250315) (2025.1.31)
Requirement already satisfied: six>=1.9 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.5.0.dev20250315) (1.17.0)
Requirement already satisfied: webencodings in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.5.0.dev20250315) (0.5.1)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.5.0.dev20250315) (1.2.2)
Requirement already satisfied: iniconfig in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.5.0.dev20250315) (2.0.0)
Requirement already satisfied: pluggy<2,>=1.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.5.0.dev20250315) (1.5.0)
Requirement already satisfied: tomli>=1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.5.0.dev20250315) (2.2.1)
Requirement already satisfied: coverage>=7.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from coverage[toml]>=7.5->pytest-cov->Sphinx==3.5.0.dev20250315) (7.6.10)
Installing collected packages: Sphinx
  Attempting uninstall: Sphinx
    Found existing installation: Sphinx 3.5.0.dev20250204
    Uninstalling Sphinx-3.5.0.dev20250204:
      Successfully uninstalled Sphinx-3.5.0.dev20250204
  DEPRECATION: Legacy editable install of Sphinx[test]==3.5.0.dev20250315 from file:///testbed (setup.py develop) is deprecated. pip 25.1 will enforce this behaviour change. A possible replacement is to add a pyproject.toml or enable --use-pep517, and use setuptools >= 64. If the resulting installation is not behaving as expected, try using --config-settings editable_mode=compat. Please consult the setuptools documentation for more information. Discussion can be found at https://github.com/pypa/pip/issues/11457
  Running setup.py develop for Sphinx
Successfully installed Sphinx
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
+ git checkout 82ef497a8c88f0f6e50d84520e7276bfbf65025d tests/test_ext_viewcode.py
Updated 0 paths from b0a7f24a7
+ git apply -v -
Checking patch tests/test_ext_viewcode.py...
Applied patch tests/test_ext_viewcode.py cleanly.
+ tox --current-env -epy39 -v -- tests/test_ext_viewcode.py
py39: commands[0]> python -X dev -m pytest -rA --durations 25 tests/test_ext_viewcode.py
[1m============================= test session starts ==============================[0m
platform linux -- Python 3.9.21, pytest-8.3.4, pluggy-1.5.0
cachedir: .tox/py39/.pytest_cache
libraries: Sphinx-3.5.0+/82ef497a8, docutils-0.21.2
base tempdir: /tmp/pytest-of-root/pytest-0
rootdir: /testbed
configfile: setup.cfg
plugins: cov-6.0.0
collected 5 items

tests/test_ext_viewcode.py [31mF[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[31m                                         [100%][0m

=================================== FAILURES ===================================
[31m[1m________________________________ test_viewcode _________________________________[0m

app = <SphinxTestApp buildername='html'>
status = <_io.StringIO object at 0x731ec6420f50>
warning = <_io.StringIO object at 0x731ec647c050>

    [0m[37m@pytest[39;49;00m.mark.sphinx(testroot=[33m'[39;49;00m[33mext-viewcode[39;49;00m[33m'[39;49;00m)[90m[39;49;00m
    [94mdef[39;49;00m[90m [39;49;00m[92mtest_viewcode[39;49;00m(app, status, warning):[90m[39;49;00m
        app.builder.build_all()[90m[39;49;00m
    [90m[39;49;00m
        warnings = re.sub([33mr[39;49;00m[33m'[39;49;00m[33m\\[39;49;00m[33m+[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33m/[39;49;00m[33m'[39;49;00m, warning.getvalue())[90m[39;49;00m
        [94massert[39;49;00m re.findall([90m[39;49;00m
            [33mr[39;49;00m[33m"[39;49;00m[33mindex.rst:[39;49;00m[33m\[39;49;00m[33md+: WARNING: Object named [39;49;00m[33m'[39;49;00m[33mfunc1[39;49;00m[33m'[39;49;00m[33m not found in include [39;49;00m[33m"[39;49;00m +[90m[39;49;00m
            [33mr[39;49;00m[33m"[39;49;00m[33mfile .*/spam/__init__.py[39;49;00m[33m'[39;49;00m[33m"[39;49;00m,[90m[39;49;00m
            warnings[90m[39;49;00m
        )[90m[39;49;00m
    [90m[39;49;00m
        result = (app.outdir / [33m'[39;49;00m[33mindex.html[39;49;00m[33m'[39;49;00m).read_text()[90m[39;49;00m
        [94massert[39;49;00m result.count([33m'[39;49;00m[33mhref=[39;49;00m[33m"[39;49;00m[33m_modules/spam/mod1.html#func1[39;49;00m[33m"[39;49;00m[33m'[39;49;00m) == [94m2[39;49;00m[90m[39;49;00m
        [94massert[39;49;00m result.count([33m'[39;49;00m[33mhref=[39;49;00m[33m"[39;49;00m[33m_modules/spam/mod2.html#func2[39;49;00m[33m"[39;49;00m[33m'[39;49;00m) == [94m2[39;49;00m[90m[39;49;00m
        [94massert[39;49;00m result.count([33m'[39;49;00m[33mhref=[39;49;00m[33m"[39;49;00m[33m_modules/spam/mod1.html#Class1[39;49;00m[33m"[39;49;00m[33m'[39;49;00m) == [94m2[39;49;00m[90m[39;49;00m
        [94massert[39;49;00m result.count([33m'[39;49;00m[33mhref=[39;49;00m[33m"[39;49;00m[33m_modules/spam/mod2.html#Class2[39;49;00m[33m"[39;49;00m[33m'[39;49;00m) == [94m2[39;49;00m[90m[39;49;00m
        [94massert[39;49;00m result.count([33m'[39;49;00m[33m@decorator[39;49;00m[33m'[39;49;00m) == [94m1[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# test that the class attribute is correctly documented[39;49;00m[90m[39;49;00m
        [94massert[39;49;00m result.count([33m'[39;49;00m[33mthis is Class3[39;49;00m[33m'[39;49;00m) == [94m2[39;49;00m[90m[39;49;00m
        [94massert[39;49;00m [33m'[39;49;00m[33mthis is the class attribute class_attr[39;49;00m[33m'[39;49;00m [95min[39;49;00m result[90m[39;49;00m
        [90m# the next assert fails, until the autodoc bug gets fixed[39;49;00m[90m[39;49;00m
        [94massert[39;49;00m result.count([33m'[39;49;00m[33mthis is the class attribute class_attr[39;49;00m[33m'[39;49;00m) == [94m2[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        result = (app.outdir / [33m'[39;49;00m[33m_modules/spam/mod1.html[39;49;00m[33m'[39;49;00m).read_text()[90m[39;49;00m
        result = re.sub([33m'[39;49;00m[33m<span class=[39;49;00m[33m"[39;49;00m[33m.*?[39;49;00m[33m"[39;49;00m[33m>[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33m<span>[39;49;00m[33m'[39;49;00m, result)  [90m# filter pygments classes[39;49;00m[90m[39;49;00m
>       [94massert[39;49;00m ([33m'[39;49;00m[33m<div class=[39;49;00m[33m"[39;49;00m[33mviewcode-block[39;49;00m[33m"[39;49;00m[33m id=[39;49;00m[33m"[39;49;00m[33mClass1[39;49;00m[33m"[39;49;00m[33m><a class=[39;49;00m[33m"[39;49;00m[33mviewcode-back[39;49;00m[33m"[39;49;00m[33m [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                [33m'[39;49;00m[33mhref=[39;49;00m[33m"[39;49;00m[33m../../index.html#spam.Class1[39;49;00m[33m"[39;49;00m[33m>[docs]</a>[39;49;00m[33m'[39;49;00m[90m[39;49;00m
                [33m'[39;49;00m[33m<span>@decorator</span>[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m[90m[39;49;00m
                [33m'[39;49;00m[33m<span>class</span> <span>Class1</span>[39;49;00m[33m'[39;49;00m[90m[39;49;00m
                [33m'[39;49;00m[33m<span>(</span><span>object</span><span>):</span>[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m[90m[39;49;00m
                [33m'[39;49;00m[33m    <span>&quot;&quot;&quot;</span>[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m[90m[39;49;00m
                [33m'[39;49;00m[33m<span>    this is Class1</span>[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m[90m[39;49;00m
                [33m'[39;49;00m[33m<span>    &quot;&quot;&quot;</span></div>[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m) [95min[39;49;00m result[90m[39;49;00m
[1m[31mE       assert '<div class="viewcode-block" id="Class1"><a class="viewcode-back" href="../../index.html#spam.Class1">[docs]</a><span>...an>\n    <span>&quot;&quot;&quot;</span>\n<span>    this is Class1</span>\n<span>    &quot;&quot;&quot;</span></div>\n' in '\n<!DOCTYPE html>\n\n<html>\n  <head>\n    <meta charset="utf-8" />\n    <meta name="viewport" content="width=device-..."https://github.com/bitprophet/alabaster">Alabaster 0.7.11</a>\n      \n    </div>\n\n    \n\n    \n  </body>\n</html>'[0m

[1m[31mtests/test_ext_viewcode.py[0m:42: AssertionError
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-viewcode
# outdir: /tmp/pytest-of-root/pytest-0/ext-viewcode/_build/html
# status: 
[01mRunning Sphinx v3.5.0+/82ef497a8[39;49;00m
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 2 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[ 50%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[100%] [35mobjects[39;49;00m                                              
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 50%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[100%] [32mobjects[39;49;00m                                               
[01mgenerating indices... [39;49;00mgenindex py-modindex done
[01mhighlighting module code... [39;49;00m[ 50%] [94mspam.mod1[39;49;00m                                   
[01mhighlighting module code... [39;49;00m[100%] [94mspam.mod2[39;49;00m                                   
[01mwriting additional pages... [39;49;00msearch done
[01mcopying static files... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/index.rst:23: WARNING: Object named 'func1' not found in include file '/tmp/pytest-of-root/pytest-0/ext-viewcode/spam/__init__.py'[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/spam/mod1.py:docstring of spam.mod1.Class3:1: WARNING: duplicate object description of spam.mod3.Class3, other instance in index, use :noindex: for one of them[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/spam/mod1.py:docstring of spam.mod3.Class3.class_attr:1: WARNING: duplicate object description of spam.mod3.Class3.class_attr, other instance in index, use :noindex: for one of them[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:84: WARNING: Error in declarator
If declarator-id with parameters (e.g., 'void f(int arg)'):
  Invalid C declaration: Expected identifier in nested name. [error at 18]
    Sphinx_DoSomething()
    ------------------^
If parenthesis in noptr-declarator (e.g., 'void (*f(int arg))(double)'):
  Error in declarator or parameters
  Invalid C declaration: Expected identifier in nested name. [error at 19]
    Sphinx_DoSomething()
    -------------------^
[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:86: WARNING: Error in declarator or parameters
Invalid C declaration: Expected identifier in nested name. [error at 19]
  SphinxStruct.member
  -------------------^[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:92: WARNING: Error in declarator or parameters
Invalid C declaration: Expected identifier in nested name. [error at 13]
  sphinx_global
  -------------^[39;49;00m
[31m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:155: WARNING: Unknown directive type "userdesc".

.. userdesc:: myobj:parameter

   Description of userdesc.[39;49;00m
[31m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:160: WARNING: Unknown interpreted text role "userdescrole".[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:166: WARNING: Too many template argument lists compared to parameter lists. Argument lists: 1, Parameter lists: 0, Extra empty parameters lists prepended: 1. Declaration:
	n::Array<T, d>[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:117: WARNING: Unparseable C cross-reference: 'SphinxType *'
Invalid C declaration: Expected end of definition. [error at 11]
  SphinxType *
  -----------^[39;49;00m

[33m=============================== warnings summary ===============================[0m
sphinx/util/docutils.py:45
  /testbed/sphinx/util/docutils.py:45: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
    __version_info__ = tuple(LooseVersion(docutils.__version__).version)

sphinx/registry.py:22
  /testbed/sphinx/registry.py:22: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
    from pkg_resources import iter_entry_points

../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('sphinxcontrib')`.
  Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
    declare_namespace(pkg)

sphinx/directives/patches.py:14
  /testbed/sphinx/directives/patches.py:14: DeprecationWarning: The `docutils.parsers.rst.directive.html` module will be removed in Docutils 2.0. Since Docutils 0.18, the "Meta" node is defined in `docutils.parsers.rst.directives.misc`.
    from docutils.parsers.rst.directives import html, images, tables

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/transforms/__init__.py:210: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse():  # type: Node

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/transforms/i18n.py:95: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.translatable):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/transforms/__init__.py:110: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for ref in self.document.traverse(nodes.substitution_reference):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/transforms/__init__.py:131: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.target):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/transforms/__init__.py:150: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.block_quote):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/transforms/__init__.py:175: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.Element):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/transforms/__init__.py:222: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.index):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/transforms/__init__.py:189: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.section):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/transforms/__init__.py:279: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.doctest_block):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/domains/citation.py:116: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.citation):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/domains/citation.py:135: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.citation_reference):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/builders/latex/transforms.py:36: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: nodes.Element

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/transforms/__init__.py:291: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: Element

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/util/compat.py:44: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.index):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/domains/index.py:51: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in document.traverse(addnodes.index):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/domains/math.py:84: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    self.data['has_equations'][docname] = any(document.traverse(math_node))

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/environment/collectors/asset.py:46: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/environment/collectors/asset.py:127: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(addnodes.download_reference):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/environment/collectors/title.py:46: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.section):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/ext/viewcode.py:114: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for objnode in doctree.traverse(addnodes.desc):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/transforms/__init__.py:301: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.system_message):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/transforms/__init__.py:390: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.manpage):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/transforms/i18n.py:488: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for inline in self.document.traverse(matcher):  # type: nodes.inline

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
  /testbed/sphinx/domains/python.py:283: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in result.traverse(nodes.Text):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/builders/html/__init__.py:422: DeprecationWarning: The frontend.OptionParser class will be replaced by a subclass of argparse.ArgumentParser in Docutils 0.21 or later.
    self.docsettings = OptionParser(

tests/test_ext_viewcode.py: 360 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/optparse.py:1000: DeprecationWarning: The frontend.Option class will be removed in Docutils 0.21 or later.
    option = self.option_class(*args, **kwargs)

tests/test_ext_viewcode.py: 15 warnings
  /testbed/sphinx/domains/c.py:3494: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(AliasNode):

tests/test_ext_viewcode.py: 15 warnings
  /testbed/sphinx/domains/cpp.py:7070: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(AliasNode):

tests/test_ext_viewcode.py: 15 warnings
  /testbed/sphinx/transforms/post_transforms/__init__.py:69: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.pending_xref):

tests/test_ext_viewcode.py: 47 warnings
  /testbed/sphinx/util/nodes.py:598: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in document.traverse(addnodes.only):

tests/test_ext_viewcode.py: 30 warnings
  /testbed/sphinx/transforms/post_transforms/images.py:33: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.image):

tests/test_ext_viewcode.py: 15 warnings
  /testbed/sphinx/transforms/post_transforms/__init__.py:216: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.desc_sig_element):

tests/test_ext_viewcode.py: 15 warnings
  /testbed/sphinx/builders/latex/transforms.py:48: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.substitution_definition):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/builders/html/transforms.py:44: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: nodes.literal

tests/test_ext_viewcode.py: 15 warnings
  /testbed/sphinx/builders/latex/transforms.py:606: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.title):

tests/test_ext_viewcode.py: 39 warnings
  /testbed/sphinx/builders/latex/transforms.py:608: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for i, index in enumerate(node.traverse(addnodes.index)):

tests/test_ext_viewcode.py: 15 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:43: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.highlightlang):

tests/test_ext_viewcode.py: 15 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:95: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for lbnode in self.document.traverse(nodes.literal_block):  # type: nodes.literal_block

tests/test_ext_viewcode.py: 15 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:99: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for dbnode in self.document.traverse(nodes.doctest_block):  # type: nodes.doctest_block

tests/test_ext_viewcode.py: 15 warnings
  /testbed/sphinx/environment/__init__.py:541: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for toctreenode in doctree.traverse(addnodes.toctree):

tests/test_ext_viewcode.py: 23 warnings
  /testbed/sphinx/environment/adapters/toctree.py:203: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for subtocnode in toc.traverse(addnodes.toctree):

tests/test_ext_viewcode.py: 23 warnings
  /testbed/sphinx/environment/adapters/toctree.py:261: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for refnode in newnode.traverse(nodes.reference):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/builders/__init__.py:181: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/builders/html/__init__.py:844: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  /testbed/sphinx/environment/adapters/toctree.py:312: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in toc.traverse(nodes.reference):

tests/test_ext_viewcode.py: 20 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:114: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.
    _gaq.push(['_setAllowLinker', true]);

tests/test_ext_viewcode.py: 20 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/about.html:70: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_ext_viewcode.py: 20 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/about.html:99: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_ext_viewcode.py: 20 warnings
  /testbed/sphinx/environment/adapters/toctree.py:328: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for toctreenode in doctree.traverse(addnodes.toctree):

tests/test_ext_viewcode.py: 20 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:215: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_ext_viewcode.py: 20 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:238: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  <template>:33: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  <template>:224: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  <template>:386: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_ext_viewcode.py::test_viewcode
tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_local_source_files
  <template>:401: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_ext_viewcode.py: 10 warnings
  /testbed/sphinx/util/nodes.py:350: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for img in node.traverse(nodes.image):

tests/test_ext_viewcode.py: 10 warnings
  /testbed/sphinx/util/nodes.py:352: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for raw in node.traverse(nodes.raw):

tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
  /testbed/sphinx/builders/_epub_base.py:275: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for reference in tree.traverse(nodes.reference):

tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
  /testbed/sphinx/builders/_epub_base.py:283: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for target in tree.traverse(nodes.target):

tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
  /testbed/sphinx/builders/_epub_base.py:290: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for desc_signature in tree.traverse(addnodes.desc_signature):

tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_default
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
tests/test_ext_viewcode.py::test_viewcode_epub_enabled
  /testbed/sphinx/builders/_epub_base.py:340: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in tree.traverse(nodes.reference):

tests/test_ext_viewcode.py::test_linkcode
tests/test_ext_viewcode.py::test_linkcode
  /testbed/sphinx/ext/linkcode.py:42: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for objnode in doctree.traverse(addnodes.desc):

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
==================================== PASSES ====================================
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: epub
# srcdir: /tmp/pytest-of-root/pytest-0/ext-viewcode
# outdir: /tmp/pytest-of-root/pytest-0/ext-viewcode/_build/epub
# status: 
[01mRunning Sphinx v3.5.0+/82ef497a8[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [epub]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 50%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[100%] [32mobjects[39;49;00m                                               
[01mgenerating indices... [39;49;00mgenindex py-modindex done
[01mwriting additional pages... [39;49;00mdone
[01mcopying static files... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
writing mimetype file...
writing META-INF/container.xml file...
writing content.opf file...
writing nav.xhtml file...
writing toc.ncx file...
writing Python.epub file...

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91mWARNING: conf value "epub_copyright" (or "copyright")should not be empty for EPUB3[39;49;00m
[91mWARNING: conf value "version" should not be empty for EPUB3[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:117: WARNING: Unparseable C cross-reference: 'SphinxType *'
Invalid C declaration: Expected end of definition. [error at 11]
  SphinxType *
  -----------^[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: epub
# srcdir: /tmp/pytest-of-root/pytest-0/ext-viewcode
# outdir: /tmp/pytest-of-root/pytest-0/ext-viewcode/_build/epub
# status: 
[01mRunning Sphinx v3.5.0+/82ef497a8[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [epub]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 50%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[100%] [32mobjects[39;49;00m                                               
[01mgenerating indices... [39;49;00mgenindex py-modindex done
[01mhighlighting module code... [39;49;00m[ 50%] [94mspam.mod1[39;49;00m                                   
[01mhighlighting module code... [39;49;00m[100%] [94mspam.mod2[39;49;00m                                   
[01mwriting additional pages... [39;49;00mdone
[01mcopying static files... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
writing mimetype file...
writing META-INF/container.xml file...
writing content.opf file...
writing nav.xhtml file...
writing toc.ncx file...
writing Python.epub file...

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91mWARNING: conf value "epub_copyright" (or "copyright")should not be empty for EPUB3[39;49;00m
[91mWARNING: conf value "version" should not be empty for EPUB3[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:117: WARNING: Unparseable C cross-reference: 'SphinxType *'
Invalid C declaration: Expected end of definition. [error at 11]
  SphinxType *
  -----------^[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-viewcode
# outdir: /tmp/pytest-of-root/pytest-0/ext-viewcode/_build/html
# status: 
[01mRunning Sphinx v3.5.0+/82ef497a8[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mupdating environment: [39;49;00m[extensions changed ('2')] 2 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[ 50%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[100%] [35mobjects[39;49;00m                                              
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 50%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[100%] [32mobjects[39;49;00m                                               
[01mgenerating indices... [39;49;00mgenindex py-modindex done
[01mwriting additional pages... [39;49;00msearch done
[01mcopying static files... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/index.rst:23: WARNING: Object named 'func1' not found in include file '/tmp/pytest-of-root/pytest-0/ext-viewcode/spam/__init__.py'[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/spam/mod1.py:docstring of spam.mod1.Class3:1: WARNING: duplicate object description of spam.mod3.Class3, other instance in index, use :noindex: for one of them[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/spam/mod1.py:docstring of spam.mod3.Class3.class_attr:1: WARNING: duplicate object description of spam.mod3.Class3.class_attr, other instance in index, use :noindex: for one of them[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:84: WARNING: Error in declarator
If declarator-id with parameters (e.g., 'void f(int arg)'):
  Invalid C declaration: Expected identifier in nested name. [error at 18]
    Sphinx_DoSomething()
    ------------------^
If parenthesis in noptr-declarator (e.g., 'void (*f(int arg))(double)'):
  Error in declarator or parameters
  Invalid C declaration: Expected identifier in nested name. [error at 19]
    Sphinx_DoSomething()
    -------------------^
[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:86: WARNING: Error in declarator or parameters
Invalid C declaration: Expected identifier in nested name. [error at 19]
  SphinxStruct.member
  -------------------^[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:92: WARNING: Error in declarator or parameters
Invalid C declaration: Expected identifier in nested name. [error at 13]
  sphinx_global
  -------------^[39;49;00m
[31m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:155: WARNING: Unknown directive type "userdesc".

.. userdesc:: myobj:parameter

   Description of userdesc.[39;49;00m
[31m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:160: WARNING: Unknown interpreted text role "userdescrole".[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:166: WARNING: Too many template argument lists compared to parameter lists. Argument lists: 1, Parameter lists: 0, Extra empty parameters lists prepended: 1. Declaration:
	n::Array<T, d>[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode/objects.rst:117: WARNING: Unparseable C cross-reference: 'SphinxType *'
Invalid C declaration: Expected end of definition. [error at 11]
  SphinxType *
  -----------^[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-viewcode-find
# outdir: /tmp/pytest-of-root/pytest-0/ext-viewcode-find/_build/html
# status: 
[01mRunning Sphinx v3.5.0+/82ef497a8[39;49;00m
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
[01mgenerating indices... [39;49;00mgenindex py-modindex done
[01mhighlighting module code... [39;49;00m[ 50%] [94mnot_a_package[39;49;00m                               
[01mhighlighting module code... [39;49;00m[100%] [94mnot_a_package.submodule[39;49;00m                     
[01mwriting additional pages... [39;49;00msearch done
[01mcopying static files... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-viewcode-find/index.rst:28: WARNING: Object named 'func1' not found in include file '/tmp/pytest-of-root/pytest-0/ext-viewcode-find/not_a_package/__init__.py'[39;49;00m

============================= slowest 25 durations =============================
0.38s setup    tests/test_ext_viewcode.py::test_viewcode
0.38s call     tests/test_ext_viewcode.py::test_viewcode
0.27s call     tests/test_ext_viewcode.py::test_linkcode
0.17s call     tests/test_ext_viewcode.py::test_viewcode_epub_enabled
0.15s call     tests/test_ext_viewcode.py::test_local_source_files
0.13s call     tests/test_ext_viewcode.py::test_viewcode_epub_default
0.02s setup    tests/test_ext_viewcode.py::test_viewcode_epub_default
0.02s setup    tests/test_ext_viewcode.py::test_linkcode
0.01s setup    tests/test_ext_viewcode.py::test_viewcode_epub_enabled
0.01s setup    tests/test_ext_viewcode.py::test_local_source_files

(5 durations < 0.005s hidden.  Use -vv to show these durations.)
[36m[1m=========================== short test summary info ============================[0m
[32mPASSED[0m tests/test_ext_viewcode.py::[1mtest_viewcode_epub_default[0m
[32mPASSED[0m tests/test_ext_viewcode.py::[1mtest_viewcode_epub_enabled[0m
[32mPASSED[0m tests/test_ext_viewcode.py::[1mtest_linkcode[0m
[32mPASSED[0m tests/test_ext_viewcode.py::[1mtest_local_source_files[0m
[31mFAILED[0m tests/test_ext_viewcode.py::[1mtest_viewcode[0m - assert '<div class="viewcode-block" id="Class1"><a class="viewcode-back" hr...
[31m================== [31m[1m1 failed[0m, [32m4 passed[0m, [33m999 warnings[0m[31m in 1.65s[0m[31m ===================[0m
py39: exit 1 (2.27 seconds) /testbed> python -X dev -m pytest -rA --durations 25 tests/test_ext_viewcode.py pid=107
  py39: FAIL code 1 (2.28=setup[0.01]+cmd[2.27] seconds)
  evaluation failed :( (2.37 seconds)
+ git checkout 82ef497a8c88f0f6e50d84520e7276bfbf65025d tests/test_ext_viewcode.py
Updated 1 path from b0a7f24a7
