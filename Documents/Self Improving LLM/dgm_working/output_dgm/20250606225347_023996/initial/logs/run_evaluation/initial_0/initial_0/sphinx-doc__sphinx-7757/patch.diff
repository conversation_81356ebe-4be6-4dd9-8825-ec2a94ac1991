diff --git a/setup.py b/setup.py
index 5e822fe9b..77b63df38 100644
--- a/setup.py
+++ b/setup.py
@@ -15,22 +15,22 @@ if sys.version_info < (3, 5):
     sys.exit(1)
 
 install_requires = [
-    'sphinxcontrib-applehelp',
-    'sphinxcontrib-devhelp',
+    'sphinxcontrib-applehelp<=1.0.7',
+    'sphinxcontrib-devhelp<=1.0.5',
     'sphinxcontrib-jsmath',
-    'sphinxcontrib-htmlhelp',
-    'sphinxcontrib-serializinghtml',
-    'sphinxcontrib-qthelp',
-    'Jinja2>=2.3',
+    'sphinxcontrib-htmlhelp<=2.0.4',
+    'sphinxcontrib-serializinghtml<=1.1.9',
+    'sphinxcontrib-qthelp<=1.0.6',
+    'Jinja2<3.0',
     'Pygments>=2.0',
     'docutils>=0.12',
     'snowballstemmer>=1.1',
     'babel>=1.3',
-    'alabaster>=0.7,<0.8',
+    'alabaster>=0.7,<0.7.12',
     'imagesize',
     'requests>=2.5.0',
     'setuptools',
-    'packaging',
+    'packaging', 'markupsafe<=2.0.1',
 ]
 
 extras_require = {
diff --git a/sphinx/domains/_arglist.py b/sphinx/domains/_arglist.py
new file mode 100644
index 000000000..920d312c8
--- /dev/null
+++ b/sphinx/domains/_arglist.py
@@ -0,0 +1,48 @@
+# Test changes
+def _parse_arglist(arglist: str) -> addnodes.desc_parameterlist:
+    """Parse a list of arguments using AST parser"""
+    params = addnodes.desc_parameterlist(arglist)
+    sig = signature_from_str('(%s)' % arglist)
+    last_kind = None
+    for param in sig.parameters.values():
+        if param.kind != param.POSITIONAL_ONLY and last_kind == param.POSITIONAL_ONLY:
+            # PEP-570: Separator for Positional Only Parameter: /
+            params += addnodes.desc_parameter('', '', addnodes.desc_sig_operator('', '/'))
+        if param.kind == param.KEYWORD_ONLY and last_kind in (param.POSITIONAL_OR_KEYWORD,
+                                                              param.POSITIONAL_ONLY,
+                                                              None):
+            # PEP-3102: Separator for Keyword Only Parameter: *
+            params += addnodes.desc_parameter('', '', addnodes.desc_sig_operator('', '*'))
+
+        node = addnodes.desc_parameter()
+        if param.kind == param.VAR_POSITIONAL:
+            node += addnodes.desc_sig_operator('', '*')
+            node += addnodes.desc_sig_name('', param.name)
+        elif param.kind == param.VAR_KEYWORD:
+            node += addnodes.desc_sig_operator('', '**')
+            node += addnodes.desc_sig_name('', param.name)
+        else:
+            node += addnodes.desc_sig_name('', param.name)
+
+        if param.annotation is not param.empty:
+            children = _parse_annotation(param.annotation)
+            node += addnodes.desc_sig_punctuation('', ':')
+            node += nodes.Text(' ')
+            node += addnodes.desc_sig_name('', '', *children)  # type: ignore
+        if param.default is not param.empty:
+            node += addnodes.desc_sig_operator('', '=')
+            node += nodes.Text(' ')  # Add space after =
+            default_value = str(param.default)
+            if isinstance(param.default, str):
+                default_value = repr(param.default)
+            node += nodes.inline('', default_value, classes=['default_value'],
+                             support_smartquotes=False)
+
+        params += node
+        last_kind = param.kind
+
+    if last_kind == Parameter.POSITIONAL_ONLY:
+        # PEP-570: Separator for Positional Only Parameter: /
+        params += addnodes.desc_parameter('', '', addnodes.desc_sig_operator('', '/'))
+
+    return params
\ No newline at end of file
