{"instance_id": "django__django-12155", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/django/contrib/admindocs/utils.py b/django/contrib/admindocs/utils.py\nindex 4c0e7e2a56..9a81bf2e56 100644\n--- a/django/contrib/admindocs/utils.py\n+++ b/django/contrib/admindocs/utils.py\n@@ -34,8 +34,14 @@ def trim_docstring(docstring):\n         return ''\n     # Convert tabs to spaces and split into lines\n     lines = docstring.expandtabs().splitlines()\n-    indent = min(len(line) - len(line.lstrip()) for line in lines if line.lstrip())\n-    trimmed = [lines[0].lstrip()] + [line[indent:].rstrip() for line in lines[1:]]\n+    # Determine indentation from lines after the first line\n+    tail_lines = lines[1:]\n+    indented_tail_lines = [line for line in tail_lines if line.lstrip()]\n+    if indented_tail_lines:\n+        indent = min(len(line) - len(line.lstrip()) for line in indented_tail_lines)\n+    else:\n+        indent = 0\n+    trimmed = [lines[0].lstrip()] + [line[indent:].rstrip() for line in tail_lines]\n     return \"\\n\".join(trimmed).strip()\n \n \n@@ -237,4 +243,4 @@ def replace_unnamed_groups(pattern):\n         final_pattern.append(pattern[prev_end:])\n         return ''.join(final_pattern)\n     else:\n-        return pattern\n+        return pattern\n\\ No newline at end of file\n", "proposed_model_patches": []}