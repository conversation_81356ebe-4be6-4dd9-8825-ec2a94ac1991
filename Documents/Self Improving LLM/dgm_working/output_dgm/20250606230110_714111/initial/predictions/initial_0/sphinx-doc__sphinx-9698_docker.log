2025-03-15 01:48:46,283 - Thread<PERSON>oolExecutor-4_3 - INFO - No existing container with name sweb.eval.sphinx-doc__sphinx-9698.20250315_014846_275503 found.
2025-03-15 01:48:46,284 - ThreadPoolExecutor-4_3 - INFO - Environment image sweb.env.x86_64.c6d251a05e0af7688b64fd:latest found for sphinx-doc__sphinx-9698
Building instance image sweb.eval.x86_64.sphinx-doc__sphinx-9698:latest for sphinx-doc__sphinx-9698
2025-03-15 01:48:46,288 - ThreadPoolExecutor-4_3 - INFO - Image sweb.eval.x86_64.sphinx-doc__sphinx-9698:latest already exists, skipping build.
2025-03-15 01:48:46,290 - ThreadPoolExecutor-4_3 - INFO - Creating container for sphinx-doc__sphinx-9698...
2025-03-15 01:48:46,352 - Thread<PERSON>oolExecutor-4_3 - INFO - Container for sphinx-doc__sphinx-9698 created: f95b817fd0bbbdfd42fb656b9213b1ece59faf3f6f958bb1292b79621514264f
2025-03-15 01:48:46,543 - ThreadPoolExecutor-4_3 - INFO - Copying coding_agent.py to container at /dgm/coding_agent.py
2025-03-15 01:48:46,546 - ThreadPoolExecutor-4_3 - INFO - Successfully copied coding_agent.py to container
2025-03-15 01:48:46,597 - ThreadPoolExecutor-4_3 - INFO - Copying requirements.txt to container at /dgm/requirements.txt
2025-03-15 01:48:46,599 - ThreadPoolExecutor-4_3 - INFO - Successfully copied requirements.txt to container
2025-03-15 01:48:46,649 - ThreadPoolExecutor-4_3 - INFO - Copying pytest.ini to container at /dgm/pytest.ini
2025-03-15 01:48:46,651 - ThreadPoolExecutor-4_3 - INFO - Successfully copied pytest.ini to container
2025-03-15 01:48:46,715 - ThreadPoolExecutor-4_3 - INFO - Copying tools to container at /dgm/tools
2025-03-15 01:48:46,717 - ThreadPoolExecutor-4_3 - INFO - Successfully copied tools to container
2025-03-15 01:48:46,788 - ThreadPoolExecutor-4_3 - INFO - Copying utils to container at /dgm/utils
2025-03-15 01:48:46,791 - ThreadPoolExecutor-4_3 - INFO - Successfully copied utils to container
2025-03-15 01:48:46,844 - ThreadPoolExecutor-4_3 - INFO - Copying tests to container at /dgm/tests
2025-03-15 01:48:46,846 - ThreadPoolExecutor-4_3 - INFO - Successfully copied tests to container
2025-03-15 01:48:46,907 - ThreadPoolExecutor-4_3 - INFO - Copying prompts to container at /dgm/prompts
2025-03-15 01:48:46,909 - ThreadPoolExecutor-4_3 - INFO - Successfully copied prompts to container
2025-03-15 01:48:46,960 - ThreadPoolExecutor-4_3 - INFO - Copying llm.py to container at /dgm/llm.py
2025-03-15 01:48:46,962 - ThreadPoolExecutor-4_3 - INFO - Successfully copied llm.py to container
2025-03-15 01:48:47,009 - ThreadPoolExecutor-4_3 - INFO - Copying llm_withtools.py to container at /dgm/llm_withtools.py
2025-03-15 01:48:47,011 - ThreadPoolExecutor-4_3 - INFO - Successfully copied llm_withtools.py to container
2025-03-15 01:48:47,012 - ThreadPoolExecutor-4_3 - INFO - Setting up environment
2025-03-15 01:48:47,065 - ThreadPoolExecutor-4_3 - INFO - Copying swe_bench/predictions/nerf_editwholefiles_med_0/sphinx-doc__sphinx-9698_eval.sh to container at /eval.sh
2025-03-15 01:48:47,067 - ThreadPoolExecutor-4_3 - INFO - Successfully copied swe_bench/predictions/nerf_editwholefiles_med_0/sphinx-doc__sphinx-9698_eval.sh to container
2025-03-15 01:48:52,017 - ThreadPoolExecutor-4_3 - INFO - Container output: + source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=2
++ CONDA_SHLVL=2
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_1=/opt/miniconda3
++ CONDA_PREFIX_1=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ cd /testbed
+ git config --global --add safe.directory /testbed
+ cd /testbed
+ git status
On branch master
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   setup.py
	modified:   tox.ini

no changes added to commit (use "git add" and/or "git commit -a")
+ git show
commit f050a7775dfc9000f55d023d36d925a8d02ccfa8
Merge: 0f0b93df9 2760b3bb7
Author: Takeshi KOMIYA <<EMAIL>>
Date:   Sat Oct 9 14:48:31 2021 +0900

    Merge pull request #9715 from tk0miya/test_with_py311-dev
    
    CI: Install the latest py package on testing with py3.11-dev

+ git diff f050a7775dfc9000f55d023d36d925a8d02ccfa8
diff --git a/setup.py b/setup.py
index 3dbc8746b..31bd48c0a 100644
--- a/setup.py
+++ b/setup.py
@@ -15,22 +15,22 @@ if sys.version_info < (3, 6):
     sys.exit(1)
 
 install_requires = [
-    'sphinxcontrib-applehelp',
-    'sphinxcontrib-devhelp',
+    'sphinxcontrib-applehelp<=1.0.7',
+    'sphinxcontrib-devhelp<=1.0.5',
     'sphinxcontrib-jsmath',
-    'sphinxcontrib-htmlhelp>=2.0.0',
-    'sphinxcontrib-serializinghtml>=1.1.5',
-    'sphinxcontrib-qthelp',
-    'Jinja2>=2.3',
+    'sphinxcontrib-htmlhelp>=2.0.0,<=2.0.4',
+    'sphinxcontrib-serializinghtml>=1.1.5,<=1.1.9',
+    'sphinxcontrib-qthelp<=1.0.6',
+    'Jinja2<3.0',
     'Pygments>=2.0',
     'docutils>=0.14,<0.18',
     'snowballstemmer>=1.1',
     'babel>=1.3',
-    'alabaster>=0.7,<0.8',
+    'alabaster>=0.7,<0.7.12',
     'imagesize',
     'requests>=2.5.0',
     'setuptools',
-    'packaging',
+    'packaging', 'markupsafe<=2.0.1',
 ]
 
 extras_require = {
diff --git a/tox.ini b/tox.ini
index 00c8e13bd..11d3352df 100644
--- a/tox.ini
+++ b/tox.ini
@@ -22,14 +22,14 @@ deps =
     du15: docutils==0.15.*
     du16: docutils==0.16.*
     du17: docutils==0.17.*
-    py311: git+https://github.com/pytest-dev/py
+    py311: git+https://github.com/pytest -rA-dev/py
 extras =
     test
 setenv =
     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils,ignore::DeprecationWarning:pip._vendor.packaging.version
     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes
 commands=
-    python -X dev -m pytest --durations 25 {posargs}
+    python -X dev -m pytest -rA --durations 25 {posargs}
 
 [testenv:du-latest]
 commands =
+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z x ']'
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1='(testbed) '
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=3
+++ CONDA_SHLVL=3
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=4
++ CONDA_SHLVL=4
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_3=/opt/miniconda3
++ CONDA_PREFIX_3=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ python -m pip install -e '.[test]'
Obtaining file:///testbed
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: sphinxcontrib-applehelp<=1.0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (1.0.4)
Requirement already satisfied: sphinxcontrib-devhelp<=1.0.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (1.0.2)
Requirement already satisfied: sphinxcontrib-jsmath in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (1.0.1)
Requirement already satisfied: sphinxcontrib-htmlhelp<=2.0.4,>=2.0.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (2.0.1)
Requirement already satisfied: sphinxcontrib-serializinghtml<=1.1.9,>=1.1.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (1.1.5)
Requirement already satisfied: sphinxcontrib-qthelp<=1.0.6 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (1.0.3)
Requirement already satisfied: Jinja2<3.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (2.11.3)
Requirement already satisfied: Pygments>=2.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (2.19.1)
Requirement already satisfied: docutils<0.18,>=0.14 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (0.17.1)
Requirement already satisfied: snowballstemmer>=1.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (2.2.0)
Requirement already satisfied: babel>=1.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (2.17.0)
Requirement already satisfied: alabaster<0.7.12,>=0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (0.7.11)
Requirement already satisfied: imagesize in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (1.4.1)
Requirement already satisfied: requests>=2.5.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (2.32.3)
Requirement already satisfied: setuptools in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (75.8.0)
Requirement already satisfied: packaging in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (24.2)
Requirement already satisfied: markupsafe<=2.0.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (2.0.1)
Requirement already satisfied: pytest in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (8.3.4)
Requirement already satisfied: pytest-cov in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (6.0.0)
Requirement already satisfied: html5lib in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (1.1)
Requirement already satisfied: cython in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.3.0.dev20250315) (3.0.11)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==4.3.0.dev20250315) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==4.3.0.dev20250315) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==4.3.0.dev20250315) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==4.3.0.dev20250315) (2025.1.31)
Requirement already satisfied: six>=1.9 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==4.3.0.dev20250315) (1.17.0)
Requirement already satisfied: webencodings in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==4.3.0.dev20250315) (0.5.1)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==4.3.0.dev20250315) (1.2.2)
Requirement already satisfied: iniconfig in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==4.3.0.dev20250315) (2.0.0)
Requirement already satisfied: pluggy<2,>=1.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==4.3.0.dev20250315) (1.5.0)
Requirement already satisfied: tomli>=1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==4.3.0.dev20250315) (2.2.1)
Requirement already satisfied: coverage>=7.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from coverage[toml]>=7.5->pytest-cov->Sphinx==4.3.0.dev20250315) (7.6.10)
Installing collected packages: Sphinx
  Attempting uninstall: Sphinx
    Found existing installation: Sphinx 4.3.0.dev20250204
    Uninstalling Sphinx-4.3.0.dev20250204:
      Successfully uninstalled Sphinx-4.3.0.dev20250204
  DEPRECATION: Legacy editable install of Sphinx[test]==4.3.0.dev20250315 from file:///testbed (setup.py develop) is deprecated. pip 25.1 will enforce this behaviour change. A possible replacement is to add a pyproject.toml or enable --use-pep517, and use setuptools >= 64. If the resulting installation is not behaving as expected, try using --config-settings editable_mode=compat. Please consult the setuptools documentation for more information. Discussion can be found at https://github.com/pypa/pip/issues/11457
  Running setup.py develop for Sphinx
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
Successfully installed Sphinx
+ git checkout f050a7775dfc9000f55d023d36d925a8d02ccfa8 tests/test_domain_py.py
Updated 0 paths from 478617fd4
+ git apply -v -
Checking patch tests/test_domain_py.py...
Applied patch tests/test_domain_py.py cleanly.
+ tox --current-env -epy39 -v -- tests/test_domain_py.py
py39: commands[0]> python -X dev -m pytest -rA --durations 25 tests/test_domain_py.py
[1m============================= test session starts ==============================[0m
platform linux -- Python 3.9.21, pytest-8.3.4, pluggy-1.5.0
cachedir: .tox/py39/.pytest_cache
libraries: Sphinx-4.3.0+/f050a7775, docutils-0.17.1
base tempdir: /tmp/pytest-of-root/pytest-0
rootdir: /testbed
configfile: setup.cfg
plugins: cov-6.0.0
collected 46 items

tests/test_domain_py.py [32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[31mF[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[31m   [100%][0m

=================================== FAILURES ===================================
[31m[1m____________________________ test_pymethod_options _____________________________[0m

app = <SphinxTestApp buildername='html'>

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mtest_pymethod_options[39;49;00m(app):[90m[39;49;00m
        text = ([33m"[39;49;00m[33m.. py:class:: Class[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m   .. py:method:: meth1[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m   .. py:method:: meth2[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m      :classmethod:[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m   .. py:method:: meth3[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m      :staticmethod:[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m   .. py:method:: meth4[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m      :async:[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m   .. py:method:: meth5[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m      :property:[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m   .. py:method:: meth6[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m      :abstractmethod:[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m   .. py:method:: meth7[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m      :final:[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m)[90m[39;49;00m
        domain = app.env.get_domain([33m'[39;49;00m[33mpy[39;49;00m[33m'[39;49;00m)[90m[39;49;00m
        doctree = restructuredtext.parse(app, text)[90m[39;49;00m
        assert_node(doctree, (addnodes.index,[90m[39;49;00m
                              [desc, ([desc_signature, ([desc_annotation, ([33m"[39;49;00m[33mclass[39;49;00m[33m"[39;49;00m, desc_sig_space)],[90m[39;49;00m
                                                        [desc_name, [33m"[39;49;00m[33mClass[39;49;00m[33m"[39;49;00m])],[90m[39;49;00m
                                      [desc_content, (addnodes.index,[90m[39;49;00m
                                                      desc,[90m[39;49;00m
                                                      addnodes.index,[90m[39;49;00m
                                                      desc,[90m[39;49;00m
                                                      addnodes.index,[90m[39;49;00m
                                                      desc,[90m[39;49;00m
                                                      addnodes.index,[90m[39;49;00m
                                                      desc,[90m[39;49;00m
                                                      addnodes.index,[90m[39;49;00m
                                                      desc,[90m[39;49;00m
                                                      addnodes.index,[90m[39;49;00m
                                                      desc,[90m[39;49;00m
                                                      addnodes.index,[90m[39;49;00m
                                                      desc)])]))[90m[39;49;00m
    [90m[39;49;00m
        [90m# method[39;49;00m[90m[39;49;00m
        assert_node(doctree[[94m1[39;49;00m][[94m1[39;49;00m][[94m0[39;49;00m], addnodes.index,[90m[39;49;00m
                    entries=[([33m'[39;49;00m[33msingle[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mmeth1() (Class method)[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mClass.meth1[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33m'[39;49;00m, [94mNone[39;49;00m)])[90m[39;49;00m
        assert_node(doctree[[94m1[39;49;00m][[94m1[39;49;00m][[94m1[39;49;00m], ([desc_signature, ([desc_name, [33m"[39;49;00m[33mmeth1[39;49;00m[33m"[39;49;00m],[90m[39;49;00m
                                                         [desc_parameterlist, ()])],[90m[39;49;00m
                                       [desc_content, ()]))[90m[39;49;00m
        [94massert[39;49;00m [33m'[39;49;00m[33mClass.meth1[39;49;00m[33m'[39;49;00m [95min[39;49;00m domain.objects[90m[39;49;00m
        [94massert[39;49;00m domain.objects[[33m'[39;49;00m[33mClass.meth1[39;49;00m[33m'[39;49;00m] == ([33m'[39;49;00m[33mindex[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mClass.meth1[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mmethod[39;49;00m[33m'[39;49;00m, [94mFalse[39;49;00m)[90m[39;49;00m
    [90m[39;49;00m
        [90m# :classmethod:[39;49;00m[90m[39;49;00m
        assert_node(doctree[[94m1[39;49;00m][[94m1[39;49;00m][[94m2[39;49;00m], addnodes.index,[90m[39;49;00m
                    entries=[([33m'[39;49;00m[33msingle[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mmeth2() (Class class method)[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mClass.meth2[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33m'[39;49;00m, [94mNone[39;49;00m)])[90m[39;49;00m
        assert_node(doctree[[94m1[39;49;00m][[94m1[39;49;00m][[94m3[39;49;00m], ([desc_signature, ([desc_annotation, ([33m"[39;49;00m[33mclassmethod[39;49;00m[33m"[39;49;00m, desc_sig_space)],[90m[39;49;00m
                                                         [desc_name, [33m"[39;49;00m[33mmeth2[39;49;00m[33m"[39;49;00m],[90m[39;49;00m
                                                         [desc_parameterlist, ()])],[90m[39;49;00m
                                       [desc_content, ()]))[90m[39;49;00m
        [94massert[39;49;00m [33m'[39;49;00m[33mClass.meth2[39;49;00m[33m'[39;49;00m [95min[39;49;00m domain.objects[90m[39;49;00m
        [94massert[39;49;00m domain.objects[[33m'[39;49;00m[33mClass.meth2[39;49;00m[33m'[39;49;00m] == ([33m'[39;49;00m[33mindex[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mClass.meth2[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mmethod[39;49;00m[33m'[39;49;00m, [94mFalse[39;49;00m)[90m[39;49;00m
    [90m[39;49;00m
        [90m# :staticmethod:[39;49;00m[90m[39;49;00m
        assert_node(doctree[[94m1[39;49;00m][[94m1[39;49;00m][[94m4[39;49;00m], addnodes.index,[90m[39;49;00m
                    entries=[([33m'[39;49;00m[33msingle[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mmeth3() (Class static method)[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mClass.meth3[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33m'[39;49;00m, [94mNone[39;49;00m)])[90m[39;49;00m
        assert_node(doctree[[94m1[39;49;00m][[94m1[39;49;00m][[94m5[39;49;00m], ([desc_signature, ([desc_annotation, ([33m"[39;49;00m[33mstatic[39;49;00m[33m"[39;49;00m, desc_sig_space)],[90m[39;49;00m
                                                         [desc_name, [33m"[39;49;00m[33mmeth3[39;49;00m[33m"[39;49;00m],[90m[39;49;00m
                                                         [desc_parameterlist, ()])],[90m[39;49;00m
                                       [desc_content, ()]))[90m[39;49;00m
        [94massert[39;49;00m [33m'[39;49;00m[33mClass.meth3[39;49;00m[33m'[39;49;00m [95min[39;49;00m domain.objects[90m[39;49;00m
        [94massert[39;49;00m domain.objects[[33m'[39;49;00m[33mClass.meth3[39;49;00m[33m'[39;49;00m] == ([33m'[39;49;00m[33mindex[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mClass.meth3[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mmethod[39;49;00m[33m'[39;49;00m, [94mFalse[39;49;00m)[90m[39;49;00m
    [90m[39;49;00m
        [90m# :async:[39;49;00m[90m[39;49;00m
        assert_node(doctree[[94m1[39;49;00m][[94m1[39;49;00m][[94m6[39;49;00m], addnodes.index,[90m[39;49;00m
                    entries=[([33m'[39;49;00m[33msingle[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mmeth4() (Class method)[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mClass.meth4[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33m'[39;49;00m, [94mNone[39;49;00m)])[90m[39;49;00m
        assert_node(doctree[[94m1[39;49;00m][[94m1[39;49;00m][[94m7[39;49;00m], ([desc_signature, ([desc_annotation, ([33m"[39;49;00m[33masync[39;49;00m[33m"[39;49;00m, desc_sig_space)],[90m[39;49;00m
                                                         [desc_name, [33m"[39;49;00m[33mmeth4[39;49;00m[33m"[39;49;00m],[90m[39;49;00m
                                                         [desc_parameterlist, ()])],[90m[39;49;00m
                                       [desc_content, ()]))[90m[39;49;00m
        [94massert[39;49;00m [33m'[39;49;00m[33mClass.meth4[39;49;00m[33m'[39;49;00m [95min[39;49;00m domain.objects[90m[39;49;00m
        [94massert[39;49;00m domain.objects[[33m'[39;49;00m[33mClass.meth4[39;49;00m[33m'[39;49;00m] == ([33m'[39;49;00m[33mindex[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mClass.meth4[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mmethod[39;49;00m[33m'[39;49;00m, [94mFalse[39;49;00m)[90m[39;49;00m
    [90m[39;49;00m
        [90m# :property:[39;49;00m[90m[39;49;00m
>       assert_node(doctree[[94m1[39;49;00m][[94m1[39;49;00m][[94m8[39;49;00m], addnodes.index,[90m[39;49;00m
                    entries=[([33m'[39;49;00m[33msingle[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mmeth5 (Class property)[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mClass.meth5[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33m'[39;49;00m, [94mNone[39;49;00m)])[90m[39;49;00m

[1m[31mtests/test_domain_py.py[0m:758: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

node = <index: >, cls = <class 'sphinx.addnodes.index'>, xpath = ''
kwargs = {'entries': [('single', 'meth5 (Class property)', 'Class.meth5', '', None)]}
key = 'entries'
value = [('single', 'meth5 (Class property)', 'Class.meth5', '', None)]

    [0m[94mdef[39;49;00m[90m [39;49;00m[92massert_node[39;49;00m(node: Node, [96mcls[39;49;00m: Any = [94mNone[39;49;00m, xpath: [96mstr[39;49;00m = [33m"[39;49;00m[33m"[39;49;00m, **kwargs: Any) -> [94mNone[39;49;00m:[90m[39;49;00m
        [94mif[39;49;00m [96mcls[39;49;00m:[90m[39;49;00m
            [94mif[39;49;00m [96misinstance[39;49;00m([96mcls[39;49;00m, [96mlist[39;49;00m):[90m[39;49;00m
                assert_node(node, [96mcls[39;49;00m[[94m0[39;49;00m], xpath=xpath, **kwargs)[90m[39;49;00m
                [94mif[39;49;00m [96mcls[39;49;00m[[94m1[39;49;00m:]:[90m[39;49;00m
                    [94mif[39;49;00m [96misinstance[39;49;00m([96mcls[39;49;00m[[94m1[39;49;00m], [96mtuple[39;49;00m):[90m[39;49;00m
                        assert_node(node, [96mcls[39;49;00m[[94m1[39;49;00m], xpath=xpath, **kwargs)[90m[39;49;00m
                    [94melse[39;49;00m:[90m[39;49;00m
                        [94massert[39;49;00m [96misinstance[39;49;00m(node, nodes.Element), \
                            [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m does not have any children[39;49;00m[33m'[39;49;00m % xpath[90m[39;49;00m
                        [94massert[39;49;00m [96mlen[39;49;00m(node) == [94m1[39;49;00m, \
                            [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m has [39;49;00m[33m%d[39;49;00m[33m child nodes, not one[39;49;00m[33m'[39;49;00m % (xpath, [96mlen[39;49;00m(node))[90m[39;49;00m
                        assert_node(node[[94m0[39;49;00m], [96mcls[39;49;00m[[94m1[39;49;00m:], xpath=xpath + [33m"[39;49;00m[33m[0][39;49;00m[33m"[39;49;00m, **kwargs)[90m[39;49;00m
            [94melif[39;49;00m [96misinstance[39;49;00m([96mcls[39;49;00m, [96mtuple[39;49;00m):[90m[39;49;00m
                [94massert[39;49;00m [96misinstance[39;49;00m(node, ([96mlist[39;49;00m, nodes.Element)), \
                    [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m does not have any items[39;49;00m[33m'[39;49;00m % xpath[90m[39;49;00m
                [94massert[39;49;00m [96mlen[39;49;00m(node) == [96mlen[39;49;00m([96mcls[39;49;00m), \
                    [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m has [39;49;00m[33m%d[39;49;00m[33m child nodes, not [39;49;00m[33m%r[39;49;00m[33m'[39;49;00m % (xpath, [96mlen[39;49;00m(node), [96mlen[39;49;00m([96mcls[39;49;00m))[90m[39;49;00m
                [94mfor[39;49;00m i, nodecls [95min[39;49;00m [96menumerate[39;49;00m([96mcls[39;49;00m):[90m[39;49;00m
                    path = xpath + [33m"[39;49;00m[33m[[39;49;00m[33m%d[39;49;00m[33m][39;49;00m[33m"[39;49;00m % i[90m[39;49;00m
                    assert_node(node[i], nodecls, xpath=path, **kwargs)[90m[39;49;00m
            [94melif[39;49;00m [96misinstance[39;49;00m([96mcls[39;49;00m, [96mstr[39;49;00m):[90m[39;49;00m
                [94massert[39;49;00m node == [96mcls[39;49;00m, [33m'[39;49;00m[33mThe node [39;49;00m[33m%r[39;49;00m[33m is not [39;49;00m[33m%r[39;49;00m[33m: [39;49;00m[33m%r[39;49;00m[33m'[39;49;00m % (xpath, [96mcls[39;49;00m, node)[90m[39;49;00m
            [94melse[39;49;00m:[90m[39;49;00m
                [94massert[39;49;00m [96misinstance[39;49;00m(node, [96mcls[39;49;00m), \
                    [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m is not subclass of [39;49;00m[33m%r[39;49;00m[33m: [39;49;00m[33m%r[39;49;00m[33m'[39;49;00m % (xpath, [96mcls[39;49;00m, node)[90m[39;49;00m
    [90m[39;49;00m
        [94mif[39;49;00m kwargs:[90m[39;49;00m
            [94massert[39;49;00m [96misinstance[39;49;00m(node, nodes.Element), \
                [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m does not have any attributes[39;49;00m[33m'[39;49;00m % xpath[90m[39;49;00m
    [90m[39;49;00m
            [94mfor[39;49;00m key, value [95min[39;49;00m kwargs.items():[90m[39;49;00m
                [94massert[39;49;00m key [95min[39;49;00m node, \
                    [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m does not have [39;49;00m[33m%r[39;49;00m[33m attribute: [39;49;00m[33m%r[39;49;00m[33m'[39;49;00m % (xpath, key, node)[90m[39;49;00m
>               [94massert[39;49;00m node[key] == value, \
                    [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m[[39;49;00m[33m%s[39;49;00m[33m] is not [39;49;00m[33m%r[39;49;00m[33m: [39;49;00m[33m%r[39;49;00m[33m'[39;49;00m % (xpath, key, value, node[key])[90m[39;49;00m
[1m[31mE               AssertionError: The node[entries] is not [('single', 'meth5 (Class property)', 'Class.meth5', '', None)]: [('single', 'meth5() (Class property)', 'Class.meth5', '', None)][0m

[1m[31msphinx/testing/util.py[0m:82: AssertionError
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

[33m=============================== warnings summary ===============================[0m
sphinx/util/docutils.py:44
  /testbed/sphinx/util/docutils.py:44: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
    __version_info__ = tuple(LooseVersion(docutils.__version__).version)

sphinx/highlighting.py:67
  /testbed/sphinx/highlighting.py:67: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
    if tuple(LooseVersion(pygmentsversion).version) <= (2, 7, 4):

sphinx/registry.py:24
  /testbed/sphinx/registry.py:24: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
    from pkg_resources import iter_entry_points

../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('sphinxcontrib')`.
  Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
    declare_namespace(pkg)

tests/test_domain_py.py: 33 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:114: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.
    _gaq.push(['_setAllowLinker', true]);

tests/test_domain_py.py: 33 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/about.html:70: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py: 33 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/about.html:99: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py: 33 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:215: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py: 33 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:238: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_domain_py_xrefs_abbreviations
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_domain_py_canonical
tests/test_domain_py.py::test_python_python_use_unqualified_type_names
tests/test_domain_py.py::test_python_python_use_unqualified_type_names_disabled
  <template>:33: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_domain_py_xrefs_abbreviations
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_domain_py_canonical
tests/test_domain_py.py::test_python_python_use_unqualified_type_names
tests/test_domain_py.py::test_python_python_use_unqualified_type_names_disabled
  <template>:224: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_domain_py_xrefs_abbreviations
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_domain_py_canonical
tests/test_domain_py.py::test_python_python_use_unqualified_type_names
tests/test_domain_py.py::test_python_python_use_unqualified_type_names_disabled
  <template>:386: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_domain_py_xrefs_abbreviations
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_domain_py_canonical
tests/test_domain_py.py::test_python_python_use_unqualified_type_names
tests/test_domain_py.py::test_python_python_use_unqualified_type_names_disabled
  <template>:401: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
==================================== PASSES ====================================
[32m[1m_____________________________ test_domain_py_xrefs _____________________________[0m
----------------------------- Captured stdout call -----------------------------
[<pending_xref: <literal...>>, <pending_xref: <literal...>>]
<pending_xref py:class="B" py:module="test.extra" refdoc="module_option" refdomain="py" refexplicit="False" reftarget="foo" reftype="meth" refwarn="False"><literal classes="xref py py-meth">foo()</literal></pending_xref>
<pending_xref py:class="B" py:module="test.extra" refdoc="module_option" refdomain="py" refexplicit="False" reftarget="foo" reftype="meth" refwarn="False"><literal classes="xref py py-meth">foo()</literal></pending_xref>
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: dummy
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/dummy
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m
[01mbuilding [dummy]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 6 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[ 16%] [35mabbr[39;49;00m                                                 
[01mreading sources... [39;49;00m[ 33%] [35mcanonical[39;49;00m                                            
[01mreading sources... [39;49;00m[ 50%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 66%] [35mmodule[39;49;00m                                               
[01mreading sources... [39;49;00m[ 83%] [35mmodule_option[39;49;00m                                        
[01mreading sources... [39;49;00m[100%] [35mroles[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 16%] [32mabbr[39;49;00m                                                  
[01mwriting output... [39;49;00m[ 33%] [32mcanonical[39;49;00m                                             
[01mwriting output... [39;49;00m[ 50%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 66%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 83%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 16%] [32mabbr[39;49;00m                                                  
[01mwriting output... [39;49;00m[ 33%] [32mcanonical[39;49;00m                                             
[01mwriting output... [39;49;00m[ 50%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 66%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 83%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 
[01mgenerating indices... [39;49;00mgenindex py-modindex done
[01mwriting additional pages... [39;49;00msearch done
[01mcopying static files... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: dummy
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/dummy
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [dummy]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 16%] [32mabbr[39;49;00m                                                  
[01mwriting output... [39;49;00m[ 33%] [32mcanonical[39;49;00m                                             
[01mwriting output... [39;49;00m[ 50%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 66%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 83%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 16%] [32mabbr[39;49;00m                                                  
[01mwriting output... [39;49;00m[ 33%] [32mcanonical[39;49;00m                                             
[01mwriting output... [39;49;00m[ 50%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 66%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 83%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 
[01mgenerating indices... [39;49;00mgenindex py-modindex done
[01mwriting additional pages... [39;49;00msearch done
[01mcopying static files... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: dummy
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/dummy
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [dummy]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 16%] [32mabbr[39;49;00m                                                  
[01mwriting output... [39;49;00m[ 33%] [32mcanonical[39;49;00m                                             
[01mwriting output... [39;49;00m[ 50%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 66%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 83%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 6 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[ 16%] [35mabbr[39;49;00m                                                 
[01mreading sources... [39;49;00m[ 33%] [35mcanonical[39;49;00m                                            
[01mreading sources... [39;49;00m[ 50%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 66%] [35mmodule[39;49;00m                                               
[01mreading sources... [39;49;00m[ 83%] [35mmodule_option[39;49;00m                                        
[01mreading sources... [39;49;00m[100%] [35mroles[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 16%] [32mabbr[39;49;00m                                                  
[01mwriting output... [39;49;00m[ 33%] [32mcanonical[39;49;00m                                             
[01mwriting output... [39;49;00m[ 50%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 66%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 83%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 
[01mgenerating indices... [39;49;00mgenindex py-modindex done
[01mwriting additional pages... [39;49;00msearch done
[01mcopying static files... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 

[32m[1m____________________________ test_parse_annotation _____________________________[0m
----------------------------- Captured stdout call -----------------------------
[<pending_xref: <#text: 'Callable'>>, <desc_sig_punctuation: <#text: '['>>, <desc_sig_punctuation: <#text: '['>>, <pending_xref: <#text: 'int'>>, <desc_sig_punctuation: <#text: ','>>, <desc_sig_space: <#text: ' '>>, <pending_xref: <#text: 'int'>>, <desc_sig_punctuation: <#text: ']'>>, <desc_sig_punctuation: <#text: ','>>, <desc_sig_space: <#text: ' '>>, <pending_xref: <#text: 'int'>>, <desc_sig_punctuation: <#text: ']'>>]
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:1: WARNING: duplicate object description of hello, other instance in index, use :noindex: for one of them[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:1: WARNING: duplicate object description of hello, other instance in index, use :noindex: for one of them[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:1: WARNING: duplicate object description of hello, other instance in index, use :noindex: for one of them[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:3: WARNING: duplicate object description of _io.StringIO, other instance in index, use :noindex: for one of them[39;49;00m

[32m[1m_____________________________ test_info_field_list _____________________________[0m
----------------------------- Captured stdout call -----------------------------
<document source="/tmp/pytest-of-root/pytest-0/root/index.rst"><target ids="['module-example']" ismod="True"/><index entries="[('pair', 'module; example', 'module-example', '', None)]"/><index entries="[('single', 'Class (class in example)', 'example.Class', '', None)]"/><desc classes="py class" desctype="class" domain="py" noindex="False" objtype="class"><desc_signature class="" classes="sig sig-object" fullname="Class" ids="example.Class" module="example"><desc_annotation xml:space="preserve">class<desc_sig_space classes="w"> </desc_sig_space></desc_annotation><desc_addname classes="sig-prename descclassname" xml:space="preserve">example.</desc_addname><desc_name classes="sig-name descname" xml:space="preserve">Class</desc_name></desc_signature><desc_content><field_list><field><field_name>Parameters</field_name><field_body><bullet_list><list_item><paragraph><literal_strong py:class="Class" py:module="example" refspecific="True">name</literal_strong> (<pending_xref py:class="Class" py:module="example" refdomain="py" refexplicit="False" refspecific="True" reftarget="str" reftype="class"><literal_emphasis>str</literal_emphasis></pending_xref>) -- blah blah</paragraph></list_item><list_item><paragraph><literal_strong py:class="Class" py:module="example" refspecific="True">age</literal_strong> (<pending_xref py:class="Class" py:module="example" refdomain="py" refexplicit="False" refspecific="True" reftarget="int" reftype="class"><literal_emphasis>int</literal_emphasis></pending_xref>) -- blah blah</paragraph></list_item><list_item><paragraph><literal_strong py:class="Class" py:module="example" refspecific="True">items</literal_strong> (<pending_xref py:class="Class" py:module="example" refdomain="py" refexplicit="False" refspecific="True" reftarget="Tuple" reftype="class"><literal_emphasis>Tuple</literal_emphasis></pending_xref><literal_emphasis>[</literal_emphasis><pending_xref py:class="Class" py:module="example" refdomain="py" refexplicit="False" refspecific="True" reftarget="str" reftype="class"><literal_emphasis>str</literal_emphasis></pending_xref><literal_emphasis>, </literal_emphasis><literal_emphasis>...</literal_emphasis><literal_emphasis>]</literal_emphasis>) -- blah blah</paragraph></list_item><list_item><paragraph><literal_strong py:class="Class" py:module="example" refspecific="True">params</literal_strong> (<pending_xref py:class="Class" py:module="example" refdomain="py" refexplicit="False" refspecific="True" reftarget="Dict" reftype="class"><literal_emphasis>Dict</literal_emphasis></pending_xref><literal_emphasis>[</literal_emphasis><pending_xref py:class="Class" py:module="example" refdomain="py" refexplicit="False" refspecific="True" reftarget="str" reftype="class"><literal_emphasis>str</literal_emphasis></pending_xref><literal_emphasis>, </literal_emphasis><pending_xref py:class="Class" py:module="example" refdomain="py" refexplicit="False" refspecific="True" reftarget="str" reftype="class"><literal_emphasis>str</literal_emphasis></pending_xref><literal_emphasis>]</literal_emphasis>) -- blah blah</paragraph></list_item></bullet_list></field_body></field></field_list></desc_content></desc></document>
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m

# warning: 
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:1: WARNING: duplicate object description of f, other instance in index, use :noindex: for one of them[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:2: WARNING: duplicate object description of g, other instance in index, use :noindex: for one of them[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py-python_use_unqualified_type_names
# outdir: /tmp/pytest-of-root/pytest-0/domain-py-python_use_unqualified_type_names/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m
[01mbuilding [mo]: [39;49;00mtargets for 0 po files that are out of date
[01mbuilding [html]: [39;49;00mtargets for 1 source files that are out of date
[01mupdating environment: [39;49;00m[new config] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
[01mgenerating indices... [39;49;00mgenindex done
[01mwriting additional pages... [39;49;00msearch done
[01mcopying static files... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone
[01mbuild succeeded.[39;49;00m

The HTML pages are in ../tmp/pytest-of-root/pytest-0/domain-py-python_use_unqualified_type_names/_build/html.

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py-python_use_unqualified_type_names
# outdir: /tmp/pytest-of-root/pytest-0/domain-py-python_use_unqualified_type_names/_build/html
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [mo]: [39;49;00mtargets for 0 po files that are out of date
[01mbuilding [html]: [39;49;00mtargets for 0 source files that are out of date
[01mupdating environment: [39;49;00m[config changed ('python_use_unqualified_type_names')] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
[01mgenerating indices... [39;49;00mgenindex done
[01mwriting additional pages... [39;49;00msearch done
[01mcopying static files... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone
[01mbuild succeeded.[39;49;00m

The HTML pages are in ../tmp/pytest-of-root/pytest-0/domain-py-python_use_unqualified_type_names/_build/html.

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: dummy
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py-xref-warning
# outdir: /tmp/pytest-of-root/pytest-0/domain-py-xref-warning/_build/dummy
# status: 
[01mRunning Sphinx v4.3.0+/f050a7775[39;49;00m
[01mbuilding [mo]: [39;49;00mtargets for 0 po files that are out of date
[01mbuilding [dummy]: [39;49;00mtargets for 1 source files that are out of date
[01mupdating environment: [39;49;00m[new config] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
[01mbuild succeeded, 2 warnings.[39;49;00m

The dummy builder generates no files.

# warning: 
[91m/tmp/pytest-of-root/pytest-0/domain-py-xref-warning/index.rst:6: WARNING: undefined label: no-label[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-py-xref-warning/index.rst:6: WARNING: Failed to create a cross reference. A title or caption not found: existing-label[39;49;00m

============================= slowest 25 durations =============================
0.36s setup    tests/test_domain_py.py::test_domain_py_xrefs
0.28s call     tests/test_domain_py.py::test_domain_py_canonical
0.21s call     tests/test_domain_py.py::test_domain_py_xrefs_abbreviations
0.20s call     tests/test_domain_py.py::test_resolve_xref_for_properties
0.12s call     tests/test_domain_py.py::test_python_python_use_unqualified_type_names
0.12s call     tests/test_domain_py.py::test_python_python_use_unqualified_type_names_disabled
0.09s call     tests/test_domain_py.py::test_domain_py_xrefs
0.05s setup    tests/test_domain_py.py::test_parse_annotation
0.04s call     tests/test_domain_py.py::test_canonical_definition_skip
0.03s call     tests/test_domain_py.py::test_domain_py_objects
0.02s setup    tests/test_domain_py.py::test_info_field_list_var
0.02s setup    tests/test_domain_py.py::test_pydata_signature
0.02s setup    tests/test_domain_py.py::test_pydata_signature_old
0.02s setup    tests/test_domain_py.py::test_pyexception_signature
0.01s setup    tests/test_domain_py.py::test_pyfunction_signature_full_py38
0.01s setup    tests/test_domain_py.py::test_pydata
0.01s setup    tests/test_domain_py.py::test_pyfunction_with_union_type_operator
0.01s setup    tests/test_domain_py.py::test_info_field_list_piped_type
0.01s setup    tests/test_domain_py.py::test_pydecoratormethod_signature
0.01s setup    tests/test_domain_py.py::test_canonical_definition_skip
0.01s setup    tests/test_domain_py.py::test_pyclass_options
0.01s setup    tests/test_domain_py.py::test_domain_py_xrefs_abbreviations
0.01s setup    tests/test_domain_py.py::test_pymethod_options
0.01s setup    tests/test_domain_py.py::test_pyfunction
0.01s setup    tests/test_domain_py.py::test_pyfunction_signature_full
[36m[1m=========================== short test summary info ============================[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_function_signatures[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_domain_py_xrefs[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_domain_py_xrefs_abbreviations[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_domain_py_objects[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_resolve_xref_for_properties[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_domain_py_find_obj[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_domain_py_canonical[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_get_full_qualified_name[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_parse_annotation[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_parse_annotation_Literal[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyfunction_signature[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyfunction_signature_full[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyfunction_signature_full_py38[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyfunction_with_number_literals[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyfunction_with_union_type_operator[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_optional_pyfunction_signature[0mdgm
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyexception_signature[0m
[32mPASSED[0m tests/test_domadgm.py::[1mtest_pydata_signature[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pydata_signature_old[0m
[32mPASSED[0m tests/test_dodgmpy.py::[1mtest_pydata_with_union_type_operator[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyobject_prefix[0m
[32mPASSED[0m tests/test_domdgmy.py::[1mtest_pydata[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyfunction[0m
[32mPASSED[0m tests/test_dgmn_py.py::[1mtest_pyclass_options[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyclassmethod[0m
[32mPASSED[0m tests/test_ddgm_py.py::[1mtest_pystaticmethod[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyattribute[0m
[32mPASSED[0m tests/test_domain_pydgm[1mtest_pyproperty[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pydecorator_signature[0m
[32mPASSED[0m tests/test_dodgmpy.py::[1mtest_pydecoratormethod_signature[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_canonical[0m
[32mPASSED[0m tests/test_ddgm_py.py::[1mtest_canonical_definition_overrides[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_canonical_definition_skip[0m
[32mPASSED[0m tests/test_dgmn_py.py::[1mtest_canonical_duplicated[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_info_field_list[0m
[32mPASSED[0m tests/test_domadgm.py::[1mtest_info_field_list_piped_type[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_info_field_list_var[0m
[32mPASSED[0m tests/test_domaidgmpy::[1mtest_module_index[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_module_index_submodule[0m
[32mPASSED[0m tests/test_domain_pdgm:[1mtest_module_index_not_collapsed[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_modindex_common_prefix[0m
[32mPASSED[0m tests/testdgmin_py.py::[1mtest_noindexentry[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_python_python_use_unqualified_type_names[0m
[32mPASSED[0m tests/test_dodgmpy.py::[1mtest_python_python_use_unqualified_type_names_disabled[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_warn_missing_reference[0m
[31mFAILED[0m tests/test_ddgm_py.py::[1mtest_pymethod_options[0m - AssertionError: The node[entries] is not [('single', 'meth5 (Class property...
[31m================== [31m[1m1 failed[0m, [32m45 passed[0m, [33m192 warnings[0m[31m in 2.52s[0m[31m ==================[0m
py39: exit 1 (3.08 seconds) /testbeddgmhon -X dev -m pytest -rA --durations 25 tests/test_domain_py.py pid=112
  py39: FAIL code 1 (3.09=setup[0.01]+cmd[3.08] seconds)
  evaluation failed :( (3.18 seconds)dgm
+ git checkout f050a7775dfc9000f55d023d36d925a8d02ccfa8 tests/test_domain_py.py
Updated 1 path from 478617fd4dgm

2025-03-15 01:48:52,070 - ThreadPoolExecutor-4_3 - INFO - Container output: 
2025-03-15 01:48:52,071 - ThreadPoolExecutor-4_dgmNFO - Installing more requirements
2025-03-15 01:49:11,974 - ThreadPoolExecutor-4_3 - INFO - Container output: Collecting datasets (from -r /guava/requirements.txt (line 1))
  Downloading datasets-3.4.0-py3-none-any.whl.metadgm(19 kB)
Collecting anthropic (from -r /guava/requirements.txt (line 2))
  Downloading anthropic-0.49.0-py3-nondgm.whl.metadata (24 kB)
Collecting backoff (from -r /guava/requirements.txt (line 4))
  Downloading backoff-2.2.1-py3-none-any.whl.metadata (14 kB)
Collecting botocore (from -r /guava/requirementsdgm(line 5))
  Downloading botocore-1.37.13-py3-none-any.whl.metadata (5.7 kB)
Collecting boto3 (from -r /guava/requirementdgm (line 6))
  Downloading boto3-1.37.13-py3-none-any.whl.metadata (6.7 kB)
Collecting openai (from -r /guava/requirements.txt (line 7))
  Downloading openai-1.66.3-py3-none-adgml.metadata (25 kB)
Collecting beautifulsoup4 (from -r /guava/requirements.txt (line 10))
  Downloading beautifulsoup4-4.13.3-py3-none-any.whldgmdata (3.8 kB)
Collecting chardet (from -r /guava/requirements.txt (line 11))
  Using cached chardet-5.2.0-py3-none-any.whl.metadata (3.4 kB)dgm
Collecting docker (from -r /guava/requirements.txt (line 12))
  Downloading docker-7.1.0-py3-none-anydgmmetadata (3.8 kB)
Collecting ghapi (from -r /guava/requirements.txt (line 13))
  Downloading ghapi-1.0.6-py3-none-any.whl.metadata (13dgm
Collecting GitPython (from -r /guava/requirements.txt (line 14))
  Downloading GitPython-3.1.44-py3-none-any.whl.metadata (13 kB)dgm
Collecting pre-commit (from -r /guava/requidgmts.txt (line 15))
  Downloading pre_commit-4.1.0-py2.py3-none-any.whl.metadata (1.3 kB)
Collecting python-dotenv (from -r /guava/requiredgm.txt (line 16))
  Downloading python_dotenv-1.0.1-py3-none-any.whl.metadata (23 kB)
Collecting rich (from -r /guava/requirements.txt (line 17))dgm
  Downloading rich-13.9.4-py3-none-any.whl.metadadgm8 kB)
Collecting unidiff (from -r /guava/requirements.txt (line 18))
  Downloading unidiff-0.7.5-py2.py3-none-any.whldgmdata (4.6 kB)
Collecting pytest (from -r /guava/requirements.txt (line 21))
  Downloading pytest-8.3.5-py3-none-any.whl.metadatdgm6 kB)
Collecting pytest-asyncio (from -r /guava/requirements.txt (line 22))
  Downloading pytest_asyncio-0.25.3-py3-dgmany.whl.metadata (3.9 kB)
Collecting filelock (from datasets->-r /guava/requirements.txt (line 1))
  Downloading filelock-3.18.0-py3-none-any.whl.metadata (2.dgm
Collecting numpy>=1.17 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading numpy-2.2.3-cp311-cp311-manylinux_2_17_xdgm.manylinux2014_x86_64.whl.metadata (62 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.0/62.0 kB 15.0 MB/s eta 0:00:00
Collecting pyarrow>=15.0.0 (from datasets->-r /guava/requirdgms.txt (line 1))
  Downloading pyarrow-19.0.1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (3.3 kB)
Collecting dill<0.3.9,>=0.3.0 (from datasets->-r /guava/requirements.txt (line 1))dgm
  Downloading dill-0.3.8-py3-none-any.whl.metadata (10 dgm
Collecting pandas (from datasets->-r /guava/requirements.txt (line 1))
  Downloading pandas-2.2.3-cp311-cp311-manylinux_2_dgm6_64.manylinux2014_x86_64.whl.metadata (89 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 89.9/89.9 kB 19.7 MB/s eta 0:00:00
Collecting requests>=2.32.2 (from datasets->dgmuava/requirements.txt (line 1))
  Using cached requests-2.32.3-py3-none-any.whl.metadata (4.6 kB)
Collecting tqdm>=4.66.3 (from datasets->-r /guavdgmuirements.txt (line 1))
  Downloading tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━dgm/57.7 kB 15.3 MB/s eta 0:00:00
Collecting xxhash (from datasets->-r /guava/requirements.txt (line 1))
  Downloading xxhash-3.5.0-cp311-cp311-manylinux_dgmx86_64.manylinux2014_x86_64.whl.metadata (12 kB)
Collecting multiprocess<0.70.17 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading multiprocess-0.70.16-py311-none-anydgmmetadata (7.2 kB)
Collecting fsspec<=2024.12.0,>=2023.1.0 (from fsspec[http]<=2024.12.0,>=2023.1.0->datasets->-r /guava/requirements.txt (line 1))
  Downloading fsspec-2024.12.0-py3-none-any.whl.metaddgm11 kB)
Collecting aiohttp (from datasets->-r /guava/requirements.txt (line 1))
  Downloading aiohttp-3.11.13-cp311-cp311-manylindgm17_x86_64.manylinux2014_x86_64.whl.metadata (7.7 kB)
Collecting huggingface-hub>=0.24.0 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading huggingface_hub-0.29.3-py3-none-any.wdgmtadata (13 kB)
Requirement already satisfied: packaging in /opt/miniconda3/lib/python3.11/site-packages (from datasets->-r /guava/requirements.txt (line 1)) (23.1)
Collecting pyyaml>=5.1 (from datasets->dgmuava/requirements.txt (line 1))
  Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.1 kB)
Collecting anyio<5,>=3.5.0 (from anthropic->dgmuava/requirements.txt (line 2))
  Downloading anyio-4.8.0-py3-none-any.whl.metadata (4.6 kB)
Requirement already satisfied: distro<2,>=1.7.0 in /opt/miniconda3/lib/python3.11/site-packages (from anthropic->-r /guava/requdgmnts.txt (line 2)) (1.8.0)
Collecting httpx<1,>=0.23.0 (from anthropic->-r /guava/requiremedgmxt (line 2))
  Downloading httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Collecting jiter<1,>=0.4.0 (from anthropic->-r /guava/reqdgments.txt (line 2))
  Downloading jiter-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.2 kB)
Collecting pydantic<3,>=1.9.0 (from anthropic->-r /guadgmquirements.txt (line 2))
  Downloading pydantic-2.10.6-py3-none-any.whl.metadata (30 kB)
Collecting sniffio (from anthropic->-r /guava/requirementsdgm(line 2))
  Downloading sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting typing-extensions<5,>=4.10 (from anthropic->-r /gdgmrequirements.txt (line 2))
  Downloading typing_extensions-4.12.2-py3-none-any.whl.metadata (3.0 kB)
Collecting jmespath<2.0.0,>=0.7.1 (from botocore->-r /guadgmquirements.txt (line 5))
  Downloading jmespath-1.0.1-py3-none-any.whl.metadata (7.6 kB)
Collecting python-dateutil<3.0.0,>=2.1 (from botocore->-r dgma/requirements.txt (line 5))
  Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Requirement already satisfied: urllib3!=2.2.0,<3,>=1.25.4 in /opt/miniconda3/lib/python3.11/site-packages (from botocore->-r /guava/requirements.txt (line 5)) (1.26.18)
Collecting s3transfer<0.12.0,>=0.11.0 (from boto3->-r /guava/requdgmnts.txt (line 6))
  Downloading s3transfer-0.11.4-py3-none-any.whl.metadata (1.7 kB)
Collecting soupsieve>1.2 (from beautifulsoup4->-r /guava/requirements.txt (line 10))dgm
  Downloading soupsieve-2.6-py3-none-any.whl.metadata (4.6 kB)dgm
Collecting fastcore>=1.7.2 (from ghapi->-r /guava/requirements.txt (line 13))
  Downloading fastcore-1.7.29-py3-none-any.whl.metadata (3.6 kB)dgm
Collecting gitdb<5,>=4.0.1 (from GitPython->-r /guava/requirements.txt (line 14))
  Downloading gitdb-4.0.12-py3-none-any.whl.metadata (1.2 kB)dgm
Collecting cfgv>=2.0.0 (from pre-commit->-r /guava/requirements.txt (line 15))
  Downloading cfgv-3.4.0-py2.py3-none-any.whl.metadata (8.5 kB)dgm
Collecting identify>=1.0.0 (from pre-commit->-r /guava/requirements.txt (line 15))
  Downloading identify-2.6.9-py2.py3-none-any.whl.metadata (4.4 kB)dgm
Collecting nodeenv>=0.11.1 (from pre-commit->-r /guava/requirements.txt (line 15))
  Downloading nodeenv-1.9.1-py2.py3-none-any.whl.metadata (21 kB)dgm
Collecting virtualenv>=20.10.0 (from pre-commit->-r /guava/requirements.txt (line 15))
  Downloading virtualenv-20.29.3-py3-none-any.whl.metadata (4.5 kB)dgm
Collecting markdown-it-py>=2.2.0 (from rich->-r /guava/requirements.txt dgm 17))
  Downloading markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)
Collecting pygments<3.0.0,>=2.13.0 (from rich->-r /guava/requirements.txt (line 17))dgm
  Using cached pygments-2.19.1-py3-none-any.whl.metadgm(2.5 kB)
Collecting iniconfig (from pytest->-r /guava/requirements.txt (line 21))
  Using cached iniconfig-2.0.0-py3-none-any.whl.metadadgm.6 kB)
Collecting pluggy<2,>=1.5 (from pytest->-r /guava/requirements.txt (line 21))
  Using cached pluggy-1.5.0-py3-none-any.whl.metadata (4.8 kB)
Requirement already satisfied: idna>=2.8 in /opt/miniconda3/lib/python3.11/site-packages (from anyio<5,>=3.5.0->anthropic->-r /guava/requirements.txt (line 2)) (3.4)
Collecting aiohappyeyeballs>=2.3.0 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl.metadata (5.9 kB)
Collecting aiosignal>=1.1.2 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading aiosignal-1.3.2-py2.py3-none-any.whl.metadata (3.8 kB)
Collecting attrs>=17.3.0 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading attrs-25.3.0-py3-none-any.whl.metadata (10 kB)
Collecting frozenlist>=1.1.1 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading frozenlist-1.5.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (13 kB)
Collecting multidict<7.0,>=4.5 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading multidict-6.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.0 kB)
Collecting propcache>=0.2.0 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading propcache-0.3.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (10 kB)
Collecting yarl<2.0,>=1.17.0 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading yarl-1.18.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (69 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 69.2/69.2 kB 12.8 MB/s eta 0:00:00
Collecting smmap<6,>=3.0.1 (from gitdb<5,>=4.0.1->GitPython->-r /guava/requirements.txt (line 14))
  Downloading smmap-5.0.2-py3-none-any.whl.metadata (4.3 kB)
Requirement already satisfied: certifi in /opt/miniconda3/lib/python3.11/site-packages (from httpx<1,>=0.23.0->anthropic->-r /guava/requirements.txt (line 2)) (2023.11.17)
Collecting httpcore==1.* (from httpx<1,>=0.23.0->anthropic->-r /guava/requirements.txt (line 2))
  Downloading httpcore-1.0.7-py3-none-any.whl.metadata (21 kB)
Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->anthropic->-r /guava/requirements.txt (line 2))
  Downloading h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)
Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich->-r /guava/requirements.txt (line 17))
  Downloading mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)
Collecting annotated-types>=0.6.0 (from pydantic<3,>=1.9.0->anthropic->-r /guava/requirements.txt (line 2))
  Downloading annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.27.2 (from pydantic<3,>=1.9.0->anthropic->-r /guava/requirements.txt (line 2))
  Downloading pydantic_core-2.27.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.6 kB)
Collecting six>=1.5 (from python-dateutil<3.0.0,>=2.1->botocore->-r /guava/requirements.txt (line 5))
  Using cached six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/lib/python3.11/site-packages (from requests>=2.32.2->datasets->-r /guava/requirements.txt (line 1)) (2.0.4)
Collecting distlib<1,>=0.3.7 (from virtualenv>=20.10.0->pre-commit->-r /guava/requirements.txt (line 15))
  Using cached distlib-0.3.9-py2.py3-none-any.whl.metadata (5.2 kB)
Requirement already satisfied: platformdirs<5,>=3.9.1 in /opt/miniconda3/lib/python3.11/site-packages (from virtualenv>=20.10.0->pre-commit->-r /guava/requirements.txt (line 15)) (3.10.0)
Collecting pytz>=2020.1 (from pandas->datasets->-r /guava/requirements.txt (line 1))
  Downloading pytz-2025.1-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.7 (from pandas->datasets->-r /guava/requirements.txt (line 1))
  Downloading tzdata-2025.1-py2.py3-none-any.whl.metadata (1.4 kB)
Downloading datasets-3.4.0-py3-none-any.whl (487 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 487.4/487.4 kB 74.9 MB/s eta 0:00:00
Downloading anthropic-0.49.0-py3-none-any.whl (243 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 243.4/243.4 kB 58.7 MB/s eta 0:00:00
Downloading backoff-2.2.1-py3-none-any.whl (15 kB)
Downloading botocore-1.37.13-py3-none-any.whl (13.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.4/13.4 MB 160.3 MB/s eta 0:00:00
Downloading boto3-1.37.13-py3-none-any.whl (139 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 139.6/139.6 kB 31.3 MB/s eta 0:00:00
Downloading openai-1.66.3-py3-none-any.whl (567 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 567.4/567.4 kB 113.9 MB/s eta 0:00:00
Downloading beautifulsoup4-4.13.3-py3-none-any.whl (186 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 186.0/186.0 kB 54.4 MB/s eta 0:00:00
Using cached chardet-5.2.0-py3-none-any.whl (199 kB)
Downloading docker-7.1.0-py3-none-any.whl (147 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 147.8/147.8 kB 46.8 MB/s eta 0:00:00
Downloading ghapi-1.0.6-py3-none-any.whl (62 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.4/62.4 kB 19.4 MB/s eta 0:00:00
Downloading GitPython-3.1.44-py3-none-any.whl (207 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 207.6/207.6 kB 61.2 MB/s eta 0:00:00
Downloading pre_commit-4.1.0-py2.py3-none-any.whl (220 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 220.6/220.6 kB 37.7 MB/s eta 0:00:00
Downloading python_dotenv-1.0.1-py3-none-any.whl (19 kB)
Downloading rich-13.9.4-py3-none-any.whl (242 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 242.4/242.4 kB 69.7 MB/s eta 0:00:00
Downloading unidiff-0.7.5-py2.py3-none-any.whl (14 kB)
Downloading pytest-8.3.5-py3-none-any.whl (343 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 343.6/343.6 kB 87.1 MB/s eta 0:00:00
Downloading pytest_asyncio-0.25.3-py3-none-any.whl (19 kB)
Downloading anyio-4.8.0-py3-none-any.whl (96 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 96.0/96.0 kB 31.9 MB/s eta 0:00:00
Downloading cfgv-3.4.0-py2.py3-none-any.whl (7.2 kB)
Downloading dill-0.3.8-py3-none-any.whl (116 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 116.3/116.3 kB 39.7 MB/s eta 0:00:00
Downloading fastcore-1.7.29-py3-none-any.whl (84 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 84.2/84.2 kB 18.3 MB/s eta 0:00:00
Downloading fsspec-2024.12.0-py3-none-any.whl (183 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 183.9/183.9 kB 50.6 MB/s eta 0:00:00
Downloading aiohttp-3.11.13-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.7/1.7 MB 154.6 MB/s eta 0:00:00
Downloading gitdb-4.0.12-py3-none-any.whl (62 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.8/62.8 kB 18.8 MB/s eta 0:00:00
Downloading httpx-0.28.1-py3-none-any.whl (73 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 73.5/73.5 kB 27.5 MB/s eta 0:00:00
Downloading httpcore-1.0.7-py3-none-any.whl (78 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.6/78.6 kB 23.9 MB/s eta 0:00:00
Downloading huggingface_hub-0.29.3-py3-none-any.whl (468 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 469.0/469.0 kB 91.5 MB/s eta 0:00:00
Downloading identify-2.6.9-py2.py3-none-any.whl (99 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 99.1/99.1 kB 33.2 MB/s eta 0:00:00
Downloading jiter-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (351 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 351.8/351.8 kB 87.5 MB/s eta 0:00:00
Downloading jmespath-1.0.1-py3-none-any.whl (20 kB)
Downloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 87.5/87.5 kB 31.4 MB/s eta 0:00:00
Downloading multiprocess-0.70.16-py311-none-any.whl (143 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 143.5/143.5 kB 35.9 MB/s eta 0:00:00
Downloading nodeenv-1.9.1-py2.py3-none-any.whl (22 kB)
Downloading numpy-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (16.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 16.4/16.4 MB 128.5 MB/s eta 0:00:00
Using cached pluggy-1.5.0-py3-none-any.whl (20 kB)
Downloading pyarrow-19.0.1-cp311-cp311-manylinux_2_28_x86_64.whl (42.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 42.1/42.1 MB 71.1 MB/s eta 0:00:00
Downloading pydantic-2.10.6-py3-none-any.whl (431 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 431.7/431.7 kB 85.3 MB/s eta 0:00:00
Downloading pydantic_core-2.27.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 146.4 MB/s eta 0:00:00
Using cached pygments-2.19.1-py3-none-any.whl (1.2 MB)
Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 229.9/229.9 kB 55.5 MB/s eta 0:00:00
Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (762 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 763.0/763.0 kB 129.6 MB/s eta 0:00:00
Using cached requests-2.32.3-py3-none-any.whl (64 kB)
Downloading s3transfer-0.11.4-py3-none-any.whl (84 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 84.4/84.4 kB 26.8 MB/s eta 0:00:00
Downloading sniffio-1.3.1-py3-none-any.whl (10 kB)
Downloading soupsieve-2.6-py3-none-any.whl (36 kB)
Downloading tqdm-4.67.1-py3-none-any.whl (78 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.5/78.5 kB 14.2 MB/s eta 0:00:00
Downloading typing_extensions-4.12.2-py3-none-any.whl (37 kB)
Downloading virtualenv-20.29.3-py3-none-any.whl (4.3 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 4.3/4.3 MB 225.6 MB/s eta 0:00:00
Downloading filelock-3.18.0-py3-none-any.whl (16 kB)
Using cached iniconfig-2.0.0-py3-none-any.whl (5.9 kB)
Downloading pandas-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (13.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.1/13.1 MB 187.2 MB/s eta 0:00:00
Downloading xxhash-3.5.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (194 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 194.8/194.8 kB 45.2 MB/s eta 0:00:00
Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
Downloading aiosignal-1.3.2-py2.py3-none-any.whl (7.6 kB)
Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
Downloading attrs-25.3.0-py3-none-any.whl (63 kB)dgm
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 63.8/63.8 kB 22.7 MB/s eta 0:00:00
Using cached distlib-0.3.9-py2.py3-none-any.whl (468 kB)
Downloading frozenlist-1.5.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (274 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 274.9/274.9 kB 77.3 MB/s eta 0:00:00
Downloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)
Downloading multidict-6.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (129 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 129.0/129.0 kB 36.9 MB/s eta 0:00:00
Downloading propcache-0.3.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (231 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 231.3/231.3 kB 73.7 MB/s eta 0:00:00
Downloading pytz-2025.1-py2.py3-none-any.whl (507 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 507.9/507.9 kB 111.2 MB/s eta 0:00:00
Using cached six-1.17.0-py2.py3-none-any.whl (11 kB)
Downloading smmap-5.0.2-py3-none-any.whl (24 kB)
Downloading tzdata-2025.1-py2.py3-none-any.whl (346 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 346.8/346.8 kB 84.1 MB/s eta 0:00:00
Downloading yarl-1.18.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (344 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 344.1/344.1 kB 72.1 MB/s eta 0:00:00
Downloading h11-0.14.0-py3-none-any.whl (58 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 58.3/58.3 kB 13.3 MB/s eta 0:00:00
Installing collected packages: unidiff, pytz, distlib, xxhash, tzdata, typing-extensions, tqdm, soupsieve, sniffio, smmap, six, requests, pyyaml, python-dotenv, pygments, pyarrow, propcache, pluggy, numpy, nodeenv, multidict, mdurl, jmespath, jiter, iniconfig, identify, h11, fsspec, frozenlist, filelock, fastcore, dill, chardet, cfgv, backoff, attrs, annotated-types, aiohappyeyeballs, yarl, virtualenv, python-dateutil, pytest, pydantic-core, multiprocess, markdown-it-py, huggingface-hub, httpcore, gitdb, ghapi, docker, beautifulsoup4, anyio, aiosignal, rich, pytest-asyncio, pydantic, pre-commit, pandas, httpx, GitPython, botocore, aiohttp, s3transfer, openai, anthropic, datasets, boto3
  Attempting uninstall: tqdm
    Found existing installation: tqdm 4.65.0
    Uninstalling tqdm-4.65.0:
      Successfully uninstalled tqdm-4.65.0
  Attempting uninstall: requests
    Found existing installation: requests 2.31.0
    Uninstalling requests-2.31.0:
      Successfully uninstalled requests-2.31.0
  Attempting uninstall: pluggy
    Found existing installation: pluggy 1.0.0
    Uninstalling pluggy-1.0.0:
      Successfully uninstalled pluggy-1.0.0
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
Successfully installed GitPython-3.1.44 aiohappyeyeballs-2.6.1 aiohttp-3.11.13 aiosignal-1.3.2 annotated-types-0.7.0 anthropic-0.49.0 anyio-4.8.0 attrs-25.3.0 backoff-2.2.1 beautifulsoup4-4.13.3 boto3-1.37.13 botocore-1.37.13 cfgv-3.4.0 chardet-5.2.0 datasets-3.4.0 dill-0.3.8 distlib-0.3.9 docker-7.1.0 fastcore-1.7.29 filelock-3.18.0 frozenlist-1.5.0 fsspec-2024.12.0 ghapi-1.0.6 gitdb-4.0.12 h11-0.14.0 httpcore-1.0.7 httpx-0.28.1 huggingface-hub-0.29.3 identify-2.6.9 iniconfig-2.0.0 jiter-0.9.0 jmespath-1.0.1 markdown-it-py-3.0.0 mdurl-0.1.2 multidict-6.1.0 multiprocess-0.70.16 nodeenv-1.9.1 numpy-2.2.3 openai-1.66.3 pandas-2.2.3 pluggy-1.5.0 pre-commit-4.1.0 propcache-0.3.0 pyarrow-19.0.1 pydantic-2.10.6 pydantic-core-2.27.2 pygments-2.19.1 pytest-8.3.5 pytest-asyncio-0.25.3 python-dateutil-2.9.0.post0 python-dotenv-1.0.1 pytz-2025.1 pyyaml-6.0.2 requests-2.32.3 rich-13.9.4 s3transfer-0.11.4 six-1.17.0 smmap-5.0.2 sniffio-1.3.1 soupsieve-2.6 tqdm-4.67.1 typing-extensions-4.12.2 tzdata-2025.1 unidiff-0.7.5 virtualenv-20.29.3 xxhash-3.5.0 yarl-1.18.3

2025-03-15 01:49:11,977 - ThreadPoolExecutor-4_3 - INFO - Running the agent
2025-03-15 01:51:15,015 - ThreadPoolExecutor-4_3 - INFO - Container output: Using Amazon Bedrock with model us.anthropic.claude-3-5-sonnet-20241022-v2:0.

2025-03-15 01:51:15,018 - ThreadPoolExecutor-4_3 - INFO - Copying output files back to host
2025-03-15 01:51:15,112 - ThreadPoolExecutor-4_3 - INFO - Copying from container /guava/sphinx-doc__sphinx-9698.md to local path swe_bench/predictions/nerf_editwholefiles_med_0/sphinx-doc__sphinx-9698.md
2025-03-15 01:51:15,132 - ThreadPoolExecutor-4_3 - INFO - Successfully copied from container to swe_bench/predictions/nerf_editwholefiles_med_0/sphinx-doc__sphinx-9698.md
2025-03-15 01:51:15,173 - ThreadPoolExecutor-4_3 - INFO - Getting model_patch
2025-03-15 01:51:15,219 - ThreadPoolExecutor-4_3 - INFO - Container output: diff --git a/setup.py b/setup.py
index 3dbc8746b..31bd48c0a 100644
--- a/setup.py
+++ b/setup.py
@@ -15,22 +15,22 @@ if sys.version_info < (3, 6):
     sys.exit(1)
 
 install_requires = [
-    'sphinxcontrib-applehelp',
-    'sphinxcontrib-devhelp',
+    'sphinxcontrib-applehelp<=1.0.7',
+    'sphinxcontrib-devhelp<=1.0.5',
     'sphinxcontrib-jsmath',
-    'sphinxcontrib-htmlhelp>=2.0.0',
-    'sphinxcontrib-serializinghtml>=1.1.5',
-    'sphinxcontrib-qthelp',
-    'Jinja2>=2.3',
+    'sphinxcontrib-htmlhelp>=2.0.0,<=2.0.4',
+    'sphinxcontrib-serializinghtml>=1.1.5,<=1.1.9',
+    'sphinxcontrib-qthelp<=1.0.6',
+    'Jinja2<3.0',
     'Pygments>=2.0',
     'docutils>=0.14,<0.18',
     'snowballstemmer>=1.1',
     'babel>=1.3',
-    'alabaster>=0.7,<0.8',
+    'alabaster>=0.7,<0.7.12',
     'imagesize',
     'requests>=2.5.0',
     'setuptools',
-    'packaging',
+    'packaging', 'markupsafe<=2.0.1',
 ]
 
 extras_require = {
diff --git a/tox.ini b/tox.ini
index 00c8e13bd..11d3352df 100644
--- a/tox.ini
+++ b/tox.ini
@@ -22,14 +22,14 @@ deps =
     du15: docutils==0.15.*
     du16: docutils==0.16.*
     du17: docutils==0.17.*
-    py311: git+https://github.com/pytest-dev/py
+    py311: git+https://github.com/pytest -rA-dev/py
 extras =
     test
 setenv =
     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils,ignore::DeprecationWarning:pip._vendor.packaging.version
     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes
 commands=
-    python -X dev -m pytest --durations 25 {posargs}
+    python -X dev -m pytest -rA --durations 25 {posargs}
 
 [testenv:du-latest]
 commands =

2025-03-15 01:51:15,264 - ThreadPoolExecutor-4_3 - INFO - Attempting to stop container sweb.eval.sphinx-doc__sphinx-9698.20250315_014846_275503...
2025-03-15 01:51:30,810 - ThreadPoolExecutor-4_3 - INFO - Attempting to remove container sweb.eval.sphinx-doc__sphinx-9698.20250315_014846_275503...
2025-03-15 01:51:31,116 - ThreadPoolExecutor-4_3 - INFO - Container sweb.eval.sphinx-doc__sphinx-9698.20250315_014846_275503 removed.
