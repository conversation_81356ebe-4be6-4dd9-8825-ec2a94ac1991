{"sphinx-doc__sphinx-9229": {"patch_is_None": false, "patch_exists": true, "patch_successfully_applied": true, "resolved": false, "tests_status": {"FAIL_TO_PASS": {"success": [], "failure": ["tests/test_ext_autodoc_autoclass.py::test_class_alias_having_doccomment"]}, "PASS_TO_PASS": {"success": ["tests/test_ext_autodoc_autoclass.py::test_classes", "tests/test_ext_autodoc_autoclass.py::test_instance_variable", "tests/test_ext_autodoc_autoclass.py::test_inherited_instance_variable", "tests/test_ext_autodoc_autoclass.py::test_uninitialized_attributes", "tests/test_ext_autodoc_autoclass.py::test_undocumented_uninitialized_attributes", "tests/test_ext_autodoc_autoclass.py::test_decorators", "tests/test_ext_autodoc_autoclass.py::test_properties", "tests/test_ext_autodoc_autoclass.py::test_slots_attribute", "tests/test_ext_autodoc_autoclass.py::test_show_inheritance_for_subclass_of_generic_type", "tests/test_ext_autodoc_autoclass.py::test_class_doc_from_class", "tests/test_ext_autodoc_autoclass.py::test_class_doc_from_init", "tests/test_ext_autodoc_autoclass.py::test_class_doc_from_both", "tests/test_ext_autodoc_autoclass.py::test_class_alias"], "failure": []}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}}}