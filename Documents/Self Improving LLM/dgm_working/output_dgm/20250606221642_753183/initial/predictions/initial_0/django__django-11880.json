{"instance_id": "django__django-11880", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/django/forms/fields.py b/django/forms/fields.py\nindex 03cd8af8d7..4cc3133ca0 100644\n--- a/django/forms/fields.py\n+++ b/django/forms/fields.py\n@@ -200,6 +200,7 @@ class Field:\n         memo[id(self)] = result\n         result.widget = copy.deepcopy(self.widget, memo)\n         result.validators = self.validators[:]\n+        result.error_messages = copy.deepcopy(self.error_messages, memo)\n         return result\n \n \ndiff --git a/tests/forms_tests/test_fields_deepcopy.py b/tests/forms_tests/test_fields_deepcopy.py\nnew file mode 100644\nindex 0000000000..f6a85f443d\n--- /dev/null\n+++ b/tests/forms_tests/test_fields_deepcopy.py\n@@ -0,0 +1,35 @@\n+from django.forms import CharField\n+from django.test import SimpleTestCase\n+import copy\n+\n+\n+class FieldDeepCopyTests(SimpleTestCase):\n+    def test_field_deepcopy_error_messages(self):\n+        \"\"\"\n+        Test that deepcopy of a Field properly copies error messages.\n+        \"\"\"\n+        f1 = CharField(error_messages={'required': 'First field required'})\n+        f2 = copy.deepcopy(f1)\n+        \n+        # Modify error message of f2\n+        f2.error_messages['required'] = 'Second field required'\n+        \n+        # The error messages should be different\n+        self.assertEqual(f1.error_messages['required'], 'First field required')\n+        self.assertEqual(f2.error_messages['required'], 'Second field required')\n+        \n+    def test_field_deepcopy_nested_error_messages(self):\n+        \"\"\"\n+        Test that deepcopy of a Field properly copies nested error messages.\n+        \"\"\"\n+        f1 = CharField(error_messages={\n+            'nested': {'key': 'Original value'}\n+        })\n+        f2 = copy.deepcopy(f1)\n+        \n+        # Modify nested error message of f2\n+        f2.error_messages['nested']['key'] = 'Modified value'\n+        \n+        # The nested error messages should be different\n+        self.assertEqual(f1.error_messages['nested']['key'], 'Original value')\n+        self.assertEqual(f2.error_messages['nested']['key'], 'Modified value')\n\\ No newline at end of file\n", "proposed_model_patches": []}