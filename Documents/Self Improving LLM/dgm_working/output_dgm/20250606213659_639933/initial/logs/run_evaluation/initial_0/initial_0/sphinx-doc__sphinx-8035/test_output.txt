+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=2
++ CONDA_SHLVL=2
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_1=/opt/miniconda3
++ CONDA_PREFIX_1=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ cd /testbed
+ git config --global --add safe.directory /testbed
+ cd /testbed
+ git status
On branch master
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   doc/usage/extensions/autodoc.rst
	modified:   sphinx/ext/autodoc/__init__.py
	modified:   sphinx/ext/autodoc/directive.py
	modified:   sphinx/ext/autodoc/importer.py
	modified:   tox.ini

Untracked files:
  (use "git add <file>..." to include in what will be committed)
	setup.py.orig

no changes added to commit (use "git add" and/or "git commit -a")
+ git show
commit 5e6da19f0e44a0ae83944fb6ce18f18f781e1a6e
Merge: 6084c44b5 76e12a59f
Author: Takeshi KOMIYA <<EMAIL>>
Date:   Sat Aug 1 18:07:03 2020 +0900

    Merge pull request #8027 from tk0miya/8032_evaluate_signature_in_py39
    
    Fix #8023: evaluate_signature() does not work properly in python3.9

+ git diff 5e6da19f0e44a0ae83944fb6ce18f18f781e1a6e
diff --git a/doc/usage/extensions/autodoc.rst b/doc/usage/extensions/autodoc.rst
index 150b62c4d..6cce71ec9 100644
--- a/doc/usage/extensions/autodoc.rst
+++ b/doc/usage/extensions/autodoc.rst
@@ -136,10 +136,19 @@ inserting them into the page source under a suitable :rst:dir:`py:module`,
            :undoc-members:
 
    * "Private" members (that is, those named like ``_private`` or ``__private``)
-     will be included if the ``private-members`` flag option is given.
+     will be included if the ``private-members`` flag option is given. You can
+     also specify which private members to include by listing them::
+
+        .. autoclass:: my.Class
+           :private-members: _my_private_method, __secret_method
+
+     Without arguments, all private members will be included.
 
      .. versionadded:: 1.1
 
+     .. versionchanged:: 3.2
+        Added support for specifying which private members to include.
+
    * autodoc considers a member private if its docstring contains
      ``:meta private:`` in its :ref:`info-field-lists`.
      For example:
@@ -272,359 +281,4 @@ inserting them into the page source under a suitable :rst:dir:`py:module`,
      will not be documented, because attribute documentation is discovered by
      parsing the source file of the current module.
 
-     .. versionadded:: 1.2
-
-   * Add a list of modules in the :confval:`autodoc_mock_imports` to prevent
-     import errors to halt the building process when some external dependencies
-     are not importable at build time.
-
-     .. versionadded:: 1.3
-
-   * As a hint to autodoc extension, you can put a ``::`` separator in between
-     module name and object name to let autodoc know the correct module name if
-     it is ambiguous. ::
-
-        .. autoclass:: module.name::Noodle
-
-
-.. rst:directive:: autofunction
-                   autodecorator
-                   autodata
-                   automethod
-                   autoattribute
-
-   These work exactly like :rst:dir:`autoclass` etc.,
-   but do not offer the options used for automatic member documentation.
-
-   :rst:dir:`autodata` and :rst:dir:`autoattribute` support the ``annotation``
-   option.  The option controls how the value of variable is shown.  If specified
-   without arguments, only the name of the variable will be printed, and its value
-   is not shown::
-
-      .. autodata:: CD_DRIVE
-         :annotation:
-
-   If the option specified with arguments, it is printed after the name as a value
-   of the variable::
-
-      .. autodata:: CD_DRIVE
-         :annotation: = your CD device name
-
-   By default, without ``annotation`` option, Sphinx tries to obtain the value of
-   the variable and print it after the name.
-
-   For module data members and class attributes, documentation can either be put
-   into a comment with special formatting (using a ``#:`` to start the comment
-   instead of just ``#``), or in a docstring *after* the definition.  Comments
-   need to be either on a line of their own *before* the definition, or
-   immediately after the assignment *on the same line*.  The latter form is
-   restricted to one line only.
-
-   This means that in the following class definition, all attributes can be
-   autodocumented::
-
-      class Foo:
-          """Docstring for class Foo."""
-
-          #: Doc comment for class attribute Foo.bar.
-          #: It can have multiple lines.
-          bar = 1
-
-          flox = 1.5   #: Doc comment for Foo.flox. One line only.
-
-          baz = 2
-          """Docstring for class attribute Foo.baz."""
-
-          def __init__(self):
-              #: Doc comment for instance attribute qux.
-              self.qux = 3
-
-              self.spam = 4
-              """Docstring for instance attribute spam."""
-
-   .. versionchanged:: 0.6
-      :rst:dir:`autodata` and :rst:dir:`autoattribute` can now extract
-      docstrings.
-   .. versionchanged:: 1.1
-      Comment docs are now allowed on the same line after an assignment.
-   .. versionchanged:: 1.2
-      :rst:dir:`autodata` and :rst:dir:`autoattribute` have an ``annotation``
-      option.
-   .. versionchanged:: 2.0
-      :rst:dir:`autodecorator` added.
-
-   .. note::
-
-      If you document decorated functions or methods, keep in mind that autodoc
-      retrieves its docstrings by importing the module and inspecting the
-      ``__doc__`` attribute of the given function or method.  That means that if
-      a decorator replaces the decorated function with another, it must copy the
-      original ``__doc__`` to the new function.
-
-
-Configuration
--------------
-
-There are also config values that you can set:
-
-.. confval:: autoclass_content
-
-   This value selects what content will be inserted into the main body of an
-   :rst:dir:`autoclass` directive.  The possible values are:
-
-   ``"class"``
-      Only the class' docstring is inserted.  This is the default.  You can
-      still document ``__init__`` as a separate method using
-      :rst:dir:`automethod` or the ``members`` option to :rst:dir:`autoclass`.
-   ``"both"``
-      Both the class' and the ``__init__`` method's docstring are concatenated
-      and inserted.
-   ``"init"``
-      Only the ``__init__`` method's docstring is inserted.
-
-   .. versionadded:: 0.3
-
-   If the class has no ``__init__`` method or if the ``__init__`` method's
-   docstring is empty, but the class has a ``__new__`` method's docstring,
-   it is used instead.
-
-   .. versionadded:: 1.4
-
-.. confval:: autodoc_member_order
-
-   This value selects if automatically documented members are sorted
-   alphabetical (value ``'alphabetical'``), by member type (value
-   ``'groupwise'``) or by source order (value ``'bysource'``).  The default is
-   alphabetical.
-
-   Note that for source order, the module must be a Python module with the
-   source code available.
-
-   .. versionadded:: 0.6
-   .. versionchanged:: 1.0
-      Support for ``'bysource'``.
-
-.. confval:: autodoc_default_flags
-
-   This value is a list of autodoc directive flags that should be automatically
-   applied to all autodoc directives.  The supported flags are ``'members'``,
-   ``'undoc-members'``, ``'private-members'``, ``'special-members'``,
-   ``'inherited-members'``, ``'show-inheritance'``, ``'ignore-module-all'``
-   and ``'exclude-members'``.
-
-   .. versionadded:: 1.0
-
-   .. deprecated:: 1.8
-
-      Integrated into :confval:`autodoc_default_options`.
-
-.. confval:: autodoc_default_options
-
-   The default options for autodoc directives.  They are applied to all autodoc
-   directives automatically.  It must be a dictionary which maps option names
-   to the values.  For example::
-
-       autodoc_default_options = {
-           'members': 'var1, var2',
-           'member-order': 'bysource',
-           'special-members': '__init__',
-           'undoc-members': True,
-           'exclude-members': '__weakref__'
-       }
-
-   Setting ``None`` or ``True`` to the value is equivalent to giving only the
-   option name to the directives.
-
-   The supported options are ``'members'``, ``'member-order'``,
-   ``'undoc-members'``, ``'private-members'``, ``'special-members'``,
-   ``'inherited-members'``, ``'show-inheritance'``, ``'ignore-module-all'``,
-   ``'imported-members'`` and ``'exclude-members'``.
-
-   .. versionadded:: 1.8
-
-   .. versionchanged:: 2.0
-      Accepts ``True`` as a value.
-
-   .. versionchanged:: 2.1
-      Added ``'imported-members'``.
-
-.. confval:: autodoc_docstring_signature
-
-   Functions imported from C modules cannot be introspected, and therefore the
-   signature for such functions cannot be automatically determined.  However, it
-   is an often-used convention to put the signature into the first line of the
-   function's docstring.
-
-   If this boolean value is set to ``True`` (which is the default), autodoc will
-   look at the first line of the docstring for functions and methods, and if it
-   looks like a signature, use the line as the signature and remove it from the
-   docstring content.
-
-   If the signature line ends with backslash, autodoc considers the function has
-   multiple signatures and look at the next line of the docstring.  It is useful
-   for overloaded function.
-
-   .. versionadded:: 1.1
-   .. versionchanged:: 3.1
-
-      Support overloaded signatures
-
-.. confval:: autodoc_mock_imports
-
-   This value contains a list of modules to be mocked up. This is useful when
-   some external dependencies are not met at build time and break the building
-   process. You may only specify the root package of the dependencies
-   themselves and omit the sub-modules:
-
-   .. code-block:: python
-
-      autodoc_mock_imports = ["django"]
-
-   Will mock all imports under the ``django`` package.
-
-   .. versionadded:: 1.3
-
-   .. versionchanged:: 1.6
-      This config value only requires to declare the top-level modules that
-      should be mocked.
-
-.. confval:: autodoc_typehints
-
-   This value controls how to represents typehints.  The setting takes the
-   following values:
-
-   * ``'signature'`` -- Show typehints as its signature (default)
-   * ``'description'`` -- Show typehints as content of function or method
-   * ``'none'`` -- Do not show typehints
-
-   .. versionadded:: 2.1
-   .. versionadded:: 3.0
-
-      New option ``'description'`` is added.
-
-.. confval:: autodoc_warningiserror
-
-   This value controls the behavior of :option:`sphinx-build -W` during
-   importing modules.
-   If ``False`` is given, autodoc forcedly suppresses the error if the imported
-   module emits warnings.  By default, ``True``.
-
-.. confval:: autodoc_inherit_docstrings
-
-   This value controls the docstrings inheritance.
-   If set to True the docstring for classes or methods, if not explicitly set,
-   is inherited form parents.
-
-   The default is ``True``.
-
-   .. versionadded:: 1.7
-
-.. confval:: suppress_warnings
-   :noindex:
-
-   :mod:`autodoc` supports to suppress warning messages via
-   :confval:`suppress_warnings`.  It allows following warnings types in
-   addition:
-
-   * autodoc
-   * autodoc.import_object
-
-
-Docstring preprocessing
------------------------
-
-autodoc provides the following additional events:
-
-.. event:: autodoc-process-docstring (app, what, name, obj, options, lines)
-
-   .. versionadded:: 0.4
-
-   Emitted when autodoc has read and processed a docstring.  *lines* is a list
-   of strings -- the lines of the processed docstring -- that the event handler
-   can modify **in place** to change what Sphinx puts into the output.
-
-   :param app: the Sphinx application object
-   :param what: the type of the object which the docstring belongs to (one of
-      ``"module"``, ``"class"``, ``"exception"``, ``"function"``, ``"method"``,
-      ``"attribute"``)
-   :param name: the fully qualified name of the object
-   :param obj: the object itself
-   :param options: the options given to the directive: an object with attributes
-      ``inherited_members``, ``undoc_members``, ``show_inheritance`` and
-      ``noindex`` that are true if the flag option of same name was given to the
-      auto directive
-   :param lines: the lines of the docstring, see above
-
-.. event:: autodoc-before-process-signature (app, obj, bound_method)
-
-   .. versionadded:: 2.4
-
-   Emitted before autodoc formats a signature for an object. The event handler
-   can modify an object to change its signature.
-
-   :param app: the Sphinx application object
-   :param obj: the object itself
-   :param bound_method: a boolean indicates an object is bound method or not
-
-.. event:: autodoc-process-signature (app, what, name, obj, options, signature, return_annotation)
-
-   .. versionadded:: 0.5
-
-   Emitted when autodoc has formatted a signature for an object. The event
-   handler can return a new tuple ``(signature, return_annotation)`` to change
-   what Sphinx puts into the output.
-
-   :param app: the Sphinx application object
-   :param what: the type of the object which the docstring belongs to (one of
-      ``"module"``, ``"class"``, ``"exception"``, ``"function"``, ``"method"``,
-      ``"attribute"``)
-   :param name: the fully qualified name of the object
-   :param obj: the object itself
-   :param options: the options given to the directive: an object with attributes
-      ``inherited_members``, ``undoc_members``, ``show_inheritance`` and
-      ``noindex`` that are true if the flag option of same name was given to the
-      auto directive
-   :param signature: function signature, as a string of the form
-      ``"(parameter_1, parameter_2)"``, or ``None`` if introspection didn't
-      succeed and signature wasn't specified in the directive.
-   :param return_annotation: function return annotation as a string of the form
-      ``" -> annotation"``, or ``None`` if there is no return annotation
-
-The :mod:`sphinx.ext.autodoc` module provides factory functions for commonly
-needed docstring processing in event :event:`autodoc-process-docstring`:
-
-.. autofunction:: cut_lines
-.. autofunction:: between
-
-
-Skipping members
-----------------
-
-autodoc allows the user to define a custom method for determining whether a
-member should be included in the documentation by using the following event:
-
-.. event:: autodoc-skip-member (app, what, name, obj, skip, options)
-
-   .. versionadded:: 0.5
-
-   Emitted when autodoc has to decide whether a member should be included in the
-   documentation.  The member is excluded if a handler returns ``True``.  It is
-   included if the handler returns ``False``.
-
-   If more than one enabled extension handles the ``autodoc-skip-member``
-   event, autodoc will use the first non-``None`` value returned by a handler.
-   Handlers should return ``None`` to fall back to the skipping behavior of
-   autodoc and other enabled extensions.
-
-   :param app: the Sphinx application object
-   :param what: the type of the object which the docstring belongs to (one of
-      ``"module"``, ``"class"``, ``"exception"``, ``"function"``, ``"method"``,
-      ``"attribute"``)
-   :param name: the fully qualified name of the object
-   :param obj: the object itself
-   :param skip: a boolean indicating if autodoc will skip this member if the
-      user handler does not override the decision
-   :param options: the options given to the directive: an object with attributes
-      ``inherited_members``, ``undoc_members``, ``show_inheritance`` and
-      ``noindex`` that are true if the flag option of same name was given to the
-      auto directive
+     .. versionadded:: 1.2
\ No newline at end of file
diff --git a/sphinx/ext/autodoc/__init__.py b/sphinx/ext/autodoc/__init__.py
index 8c3509c3b..45dbd2eed 100644
--- a/sphinx/ext/autodoc/__init__.py
+++ b/sphinx/ext/autodoc/__init__.py
@@ -19,7 +19,7 @@ from typing import (
     Any, Callable, Dict, Iterator, List, Optional, Sequence, Set, Tuple, Type, TypeVar, Union
 )
 
-from docutils.statemachine import StringList
+from docutils.statemachine import StringList, ViewList
 
 import sphinx
 from sphinx.application import Sphinx
@@ -43,15 +43,12 @@ if False:
     from typing import Type  # NOQA # for python3.5.1
     from sphinx.ext.autodoc.directive import DocumenterBridge
 
-
 logger = logging.getLogger(__name__)
 
-
 # This type isn't exposed directly in any modules, but can be found
 # here in most Python versions
 MethodDescriptorType = type(type.__subclasses__)
 
-
 #: extended signature RE: with explicit module name separated by ::
 py_ext_sig_re = re.compile(
     r'''^ ([\w.]+::)?            # explicit module name
@@ -62,30 +59,31 @@ py_ext_sig_re = re.compile(
           )? $                   # and nothing more
           ''', re.VERBOSE)
 
-
 def identity(x: Any) -> Any:
     return x
 
-
 ALL = object()
 UNINITIALIZED_ATTR = object()
 INSTANCEATTR = object()
 SLOTSATTR = object()
 
-
 def members_option(arg: Any) -> Union[object, List[str]]:
     """Used to convert the :members: option to auto directives."""
     if arg is None or arg is True:
         return ALL
     return [x.strip() for x in arg.split(',') if x.strip()]
 
-
 def members_set_option(arg: Any) -> Union[object, Set[str]]:
     """Used to convert the :members: option to auto directives."""
     if arg is None:
         return ALL
     return {x.strip() for x in arg.split(',') if x.strip()}
 
+def private_members_option(arg: Any) -> Union[object, List[str]]:
+    """Used to convert the :private-members: option to auto directives."""
+    if arg is None or arg is True:
+        return ALL
+    return [x.strip() for x in arg.split(',') if x.strip()]
 
 def inherited_members_option(arg: Any) -> Union[object, Set[str]]:
     """Used to convert the :members: option to auto directives."""
@@ -94,7 +92,6 @@ def inherited_members_option(arg: Any) -> Union[object, Set[str]]:
     else:
         return arg
 
-
 def member_order_option(arg: Any) -> Optional[str]:
     """Used to convert the :members: option to auto directives."""
     if arg is None:
@@ -104,10 +101,8 @@ def member_order_option(arg: Any) -> Optional[str]:
     else:
         raise ValueError(__('invalid value for member-order option: %s') % arg)
 
-
 SUPPRESS = object()
 
-
 def annotation_option(arg: Any) -> Any:
     if arg is None:
         # suppress showing the representation of the object
@@ -115,14 +110,12 @@ def annotation_option(arg: Any) -> Any:
     else:
         return arg
 
-
 def bool_option(arg: Any) -> bool:
     """Used to convert flag options to auto directives.  (Instead of
     directives.flag(), which returns None).
     """
     return True
 
-
 def merge_special_members_option(options: Dict) -> None:
     """Merge :special-members: option to :members: option."""
     if 'special-members' in options and options['special-members'] is not ALL:
@@ -135,75 +128,6 @@ def merge_special_members_option(options: Dict) -> None:
         else:
             options['members'] = options['special-members']
 
-
-# Some useful event listener factories for autodoc-process-docstring.
-
-def cut_lines(pre: int, post: int = 0, what: str = None) -> Callable:
-    """Return a listener that removes the first *pre* and last *post*
-    lines of every docstring.  If *what* is a sequence of strings,
-    only docstrings of a type in *what* will be processed.
-
-    Use like this (e.g. in the ``setup()`` function of :file:`conf.py`)::
-
-       from sphinx.ext.autodoc import cut_lines
-       app.connect('autodoc-process-docstring', cut_lines(4, what=['module']))
-
-    This can (and should) be used in place of :confval:`automodule_skip_lines`.
-    """
-    def process(app: Sphinx, what_: str, name: str, obj: Any, options: Any, lines: List[str]
-                ) -> None:
-        if what and what_ not in what:
-            return
-        del lines[:pre]
-        if post:
-            # remove one trailing blank line.
-            if lines and not lines[-1]:
-                lines.pop(-1)
-            del lines[-post:]
-        # make sure there is a blank line at the end
-        if lines and lines[-1]:
-            lines.append('')
-    return process
-
-
-def between(marker: str, what: Sequence[str] = None, keepempty: bool = False,
-            exclude: bool = False) -> Callable:
-    """Return a listener that either keeps, or if *exclude* is True excludes,
-    lines between lines that match the *marker* regular expression.  If no line
-    matches, the resulting docstring would be empty, so no change will be made
-    unless *keepempty* is true.
-
-    If *what* is a sequence of strings, only docstrings of a type in *what* will
-    be processed.
-    """
-    marker_re = re.compile(marker)
-
-    def process(app: Sphinx, what_: str, name: str, obj: Any, options: Any, lines: List[str]
-                ) -> None:
-        if what and what_ not in what:
-            return
-        deleted = 0
-        delete = not exclude
-        orig_lines = lines[:]
-        for i, line in enumerate(orig_lines):
-            if delete:
-                lines.pop(i - deleted)
-                deleted += 1
-            if marker_re.match(line):
-                delete = not delete
-                if delete:
-                    lines.pop(i - deleted)
-                    deleted += 1
-        if not lines and not keepempty:
-            lines[:] = orig_lines
-        # make sure there is a blank line at the end
-        if lines and lines[-1]:
-            lines.append('')
-    return process
-
-
-# This class is used only in ``sphinx.ext.autodoc.directive``,
-# But we define this class here to keep compatibility (see #4538)
 class Options(dict):
     """A dict/attribute hybrid that returns None on nonexisting keys."""
     def __getattr__(self, name: str) -> Any:
@@ -212,1936 +136,190 @@ class Options(dict):
         except KeyError:
             return None
 
-
 class Documenter:
     """
-    A Documenter knows how to autodocument a single object type.  When
-    registered with the AutoDirective, it will be used to document objects
-    of that type when needed by autodoc.
-
-    Its *objtype* attribute selects what auto directive it is assigned to
-    (the directive name is 'auto' + objtype), and what directive it generates
-    by default, though that can be overridden by an attribute called
-    *directivetype*.
-
-    A Documenter has an *option_spec* that works like a docutils directive's;
-    in fact, it will be used to parse an auto directive's options that matches
-    the documenter.
+    Base class for documenters.
     """
-    #: name by which the directive is called (auto...) and the default
-    #: generated directive name
-    objtype = 'object'
-    #: indentation by which to indent the directive content
-    content_indent = '   '
-    #: priority if multiple documenters return True from can_document_member
-    priority = 0
-    #: order if autodoc_member_order is set to 'groupwise'
-    member_order = 0
-    #: true if the generated content may contain titles
-    titles_allowed = False
+    #: object name
+    objtype = 'base'
 
-    option_spec = {'noindex': bool_option}  # type: Dict[str, Callable]
+    #: documenter priority
+    priority = 0
 
-    def get_attr(self, obj: Any, name: str, *defargs: Any) -> Any:
-        """getattr() override for types such as Zope interfaces."""
-        return autodoc_attrgetter(self.env.app, obj, name, *defargs)
+    # configuration values
+    config = None  # type: Config
 
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        """Called to see if a member can be documented by this documenter."""
-        raise NotImplementedError('must be implemented in subclasses')
+    # object but initialized later
+    env = None  # type: BuildEnvironment
+    reporter = None  # type: Reporter
 
-    def __init__(self, directive: "DocumenterBridge", name: str, indent: str = '') -> None:
+    # generic options
+    option_spec = {
+        'noindex': bool_option,
+        'members': members_option,
+        'undoc-members': bool_option,
+        'special-members': members_option,
+        'private-members': private_members_option,
+        'exclude-members': members_option,
+        'member-order': member_order_option,
+    }
+
+    def __init__(self, directive: "DocumenterBridge", name: str) -> None:
         self.directive = directive
-        self.env = directive.env    # type: BuildEnvironment
-        self.options = directive.genopt
+        self.env = directive.env
         self.name = name
-        self.indent = indent
-        # the module and object path within the module, and the fully
-        # qualified name (all set after resolve_name succeeds)
-        self.modname = None         # type: str
-        self.module = None          # type: ModuleType
-        self.objpath = None         # type: List[str]
-        self.fullname = None        # type: str
-        # extra signature items (arguments and return annotation,
-        # also set after resolve_name succeeds)
-        self.args = None            # type: str
-        self.retann = None          # type: str
-        # the object to document (set after import_object succeeds)
-        self.object = None          # type: Any
-        self.object_name = None     # type: str
-        # the parent/owner of the object to document
-        self.parent = None          # type: Any
-        # the module analyzer to get at attribute docs, or None
-        self.analyzer = None        # type: ModuleAnalyzer
-
-    @property
-    def documenters(self) -> Dict[str, "Type[Documenter]"]:
-        """Returns registered Documenter classes"""
-        return self.env.app.registry.documenters
-
-    def add_line(self, line: str, source: str, *lineno: int) -> None:
-        """Append one line of generated reST to the output."""
-        if line.strip():  # not a blank line
-            self.directive.result.append(self.indent + line, source, *lineno)
-        else:
-            self.directive.result.append('', source, *lineno)
-
-    def resolve_name(self, modname: str, parents: Any, path: str, base: Any
-                     ) -> Tuple[str, List[str]]:
-        """Resolve the module and name of the object to document given by the
-        arguments and the current module/class.
-
-        Must return a pair of the module name and a chain of attributes; for
-        example, it would return ``('zipfile', ['ZipFile', 'open'])`` for the
-        ``zipfile.ZipFile.open`` method.
-        """
-        raise NotImplementedError('must be implemented in subclasses')
+        self.options = directive.genopt
 
     def parse_name(self) -> bool:
-        """Determine what module to import and what attribute to document.
-
-        Returns True and sets *self.modname*, *self.objpath*, *self.fullname*,
-        *self.args* and *self.retann* if parsing and resolving was successful.
-        """
-        # first, parse the definition -- auto directives for classes and
-        # functions can contain a signature which is then used instead of
-        # an autogenerated one
-        try:
-            explicit_modname, path, base, args, retann = \
-                py_ext_sig_re.match(self.name).groups()
-        except AttributeError:
-            logger.warning(__('invalid signature for auto%s (%r)') % (self.objtype, self.name),
-                           type='autodoc')
-            return False
-
-        # support explicit module and class name separation via ::
-        if explicit_modname is not None:
-            modname = explicit_modname[:-2]
-            parents = path.rstrip('.').split('.') if path else []
-        else:
-            modname = None
-            parents = []
-
-        with mock(self.env.config.autodoc_mock_imports):
-            self.modname, self.objpath = self.resolve_name(modname, parents, path, base)
-
-        if not self.modname:
-            return False
-
-        self.args = args
-        self.retann = retann
-        self.fullname = (self.modname or '') + \
-                        ('.' + '.'.join(self.objpath) if self.objpath else '')
-        return True
-
-    def import_object(self, raiseerror: bool = False) -> bool:
-        """Import the object given by *self.modname* and *self.objpath* and set
-        it as *self.object*.
-
-        Returns True if successful, False if an error occurred.
-        """
-        with mock(self.env.config.autodoc_mock_imports):
-            try:
-                ret = import_object(self.modname, self.objpath, self.objtype,
-                                    attrgetter=self.get_attr,
-                                    warningiserror=self.env.config.autodoc_warningiserror)
-                self.module, self.parent, self.object_name, self.object = ret
-                return True
-            except ImportError as exc:
-                if raiseerror:
-                    raise
-                else:
-                    logger.warning(exc.args[0], type='autodoc', subtype='import_object')
-                    self.env.note_reread()
-                    return False
-
-    def get_real_modname(self) -> str:
-        """Get the real module name of an object to document.
-
-        It can differ from the name of the module through which the object was
-        imported.
-        """
-        return self.get_attr(self.object, '__module__', None) or self.modname
-
-    def check_module(self) -> bool:
-        """Check if *self.object* is really defined in the module given by
-        *self.modname*.
-        """
-        if self.options.imported_members:
+        """Parse and normalize object name, determine object type."""
+        raise NotImplementedError
+
+    def format_signature(self) -> str:
+        """Format object's signature."""
+        return ''
+
+    def generate(self, more_content=None):
+        """Generate documentation for the object."""
+        raise NotImplementedError
+
+    def should_skip_member(self, name: str, obj: Any, options: Options) -> bool:
+        """Return True if member should be skipped."""
+        # Skip built-in members that we don't want to document
+        if name in ('__builtins__', '__cached__', '__doc__', '__file__', '__loader__', 
+                   '__name__', '__package__', '__spec__'):
             return True
 
-        subject = inspect.unpartial(self.object)
-        modname = self.get_attr(subject, '__module__', None)
-        if modname and modname != self.modname:
-            return False
-        return True
-
-    def format_args(self, **kwargs: Any) -> str:
-        """Format the argument signature of *self.object*.
-
-        Should return None if the object does not have a signature.
-        """
-        return None
-
-    def format_name(self) -> str:
-        """Format the name of *self.object*.
-
-        This normally should be something that can be parsed by the generated
-        directive, but doesn't need to be (Sphinx will display it unparsed
-        then).
-        """
-        # normally the name doesn't contain the module (except for module
-        # directives of course)
-        return '.'.join(self.objpath) or self.modname
-
-    def _call_format_args(self, **kwargs: Any) -> str:
-        if kwargs:
-            try:
-                return self.format_args(**kwargs)
-            except TypeError:
-                # avoid chaining exceptions, by putting nothing here
-                pass
-
-        # retry without arguments for old documenters
-        return self.format_args()
-
-    def format_signature(self, **kwargs: Any) -> str:
-        """Format the signature (arguments and return annotation) of the object.
-
-        Let the user process it via the ``autodoc-process-signature`` event.
-        """
-        if self.args is not None:
-            # signature given explicitly
-            args = "(%s)" % self.args
-            retann = self.retann
-        else:
-            # try to introspect the signature
-            try:
-                retann = None
-                args = self._call_format_args(**kwargs)
-                if args:
-                    matched = re.match(r'^(\(.*\))\s+->\s+(.*)$', args)
-                    if matched:
-                        args = matched.group(1)
-                        retann = matched.group(2)
-            except Exception as exc:
-                logger.warning(__('error while formatting arguments for %s: %s'),
-                               self.fullname, exc, type='autodoc')
-                args = None
-
-        result = self.env.events.emit_firstresult('autodoc-process-signature',
-                                                  self.objtype, self.fullname,
-                                                  self.object, self.options, args, retann)
-        if result:
-            args, retann = result
-
-        if args is not None:
-            return args + ((' -> %s' % retann) if retann else '')
-        else:
-            return ''
-
-    def add_directive_header(self, sig: str) -> None:
-        """Add the directive header and options to the generated content."""
-        domain = getattr(self, 'domain', 'py')
-        directive = getattr(self, 'directivetype', self.objtype)
-        name = self.format_name()
-        sourcename = self.get_sourcename()
-
-        # one signature per line, indented by column
-        prefix = '.. %s:%s:: ' % (domain, directive)
-        for i, sig_line in enumerate(sig.split("\n")):
-            self.add_line('%s%s%s' % (prefix, name, sig_line),
-                          sourcename)
-            if i == 0:
-                prefix = " " * len(prefix)
-
-        if self.options.noindex:
-            self.add_line('   :noindex:', sourcename)
-        if self.objpath:
-            # Be explicit about the module, this is necessary since .. class::
-            # etc. don't support a prepended module name
-            self.add_line('   :module: %s' % self.modname, sourcename)
-
-    def get_doc(self, encoding: str = None, ignore: int = None) -> List[List[str]]:
-        """Decode and return lines of the docstring(s) for the object."""
-        if encoding is not None:
-            warnings.warn("The 'encoding' argument to autodoc.%s.get_doc() is deprecated."
-                          % self.__class__.__name__,
-                          RemovedInSphinx40Warning, stacklevel=2)
-        if ignore is not None:
-            warnings.warn("The 'ignore' argument to autodoc.%s.get_doc() is deprecated."
-                          % self.__class__.__name__,
-                          RemovedInSphinx50Warning, stacklevel=2)
-        docstring = getdoc(self.object, self.get_attr,
-                           self.env.config.autodoc_inherit_docstrings,
-                           self.parent, self.object_name)
-        if docstring:
-            tab_width = self.directive.state.document.settings.tab_width
-            return [prepare_docstring(docstring, ignore, tab_width)]
-        return []
-
-    def process_doc(self, docstrings: List[List[str]]) -> Iterator[str]:
-        """Let the user process the docstrings before adding them."""
-        for docstringlines in docstrings:
-            if self.env.app:
-                # let extensions preprocess docstrings
-                self.env.app.emit('autodoc-process-docstring',
-                                  self.objtype, self.fullname, self.object,
-                                  self.options, docstringlines)
-            yield from docstringlines
-
-    def get_sourcename(self) -> str:
-        if self.analyzer:
-            return '%s:docstring of %s' % (self.analyzer.srcname, self.fullname)
-        return 'docstring of %s' % self.fullname
-
-    def add_content(self, more_content: Any, no_docstring: bool = False) -> None:
-        """Add content from docstrings, attribute documentation and user."""
-        # set sourcename and add content from attribute documentation
-        sourcename = self.get_sourcename()
-        if self.analyzer:
-            attr_docs = self.analyzer.find_attr_docs()
-            if self.objpath:
-                key = ('.'.join(self.objpath[:-1]), self.objpath[-1])
-                if key in attr_docs:
-                    no_docstring = True
-                    # make a copy of docstring for attributes to avoid cache
-                    # the change of autodoc-process-docstring event.
-                    docstrings = [list(attr_docs[key])]
-
-                    for i, line in enumerate(self.process_doc(docstrings)):
-                        self.add_line(line, sourcename, i)
-
-        # add content from docstrings
-        if not no_docstring:
-            docstrings = self.get_doc()
-            if not docstrings:
-                # append at least a dummy docstring, so that the event
-                # autodoc-process-docstring is fired and can add some
-                # content if desired
-                docstrings.append([])
-            for i, line in enumerate(self.process_doc(docstrings)):
-                self.add_line(line, sourcename, i)
-
-        # add additional content (e.g. from document), if present
-        if more_content:
-            for line, src in zip(more_content.data, more_content.items):
-                self.add_line(line, src[0], src[1])
-
-    def get_object_members(self, want_all: bool) -> Tuple[bool, List[Tuple[str, Any]]]:
-        """Return `(members_check_module, members)` where `members` is a
-        list of `(membername, member)` pairs of the members of *self.object*.
-
-        If *want_all* is True, return all members.  Else, only return those
-        members given by *self.options.members* (which may also be none).
-        """
-        members = get_object_members(self.object, self.objpath, self.get_attr, self.analyzer)
-        if not want_all:
-            if not self.options.members:
-                return False, []
-            # specific members given
-            selected = []
-            for name in self.options.members:
-                if name in members:
-                    selected.append((name, members[name].value))
-                else:
-                    logger.warning(__('missing attribute %s in object %s') %
-                                   (name, self.fullname), type='autodoc')
-            return False, selected
-        elif self.options.inherited_members:
-            return False, [(m.name, m.value) for m in members.values()]
-        else:
-            return False, [(m.name, m.value) for m in members.values()
-                           if m.directly_defined]
-
-    def filter_members(self, members: List[Tuple[str, Any]], want_all: bool
-                       ) -> List[Tuple[str, Any, bool]]:
-        """Filter the given member list.
-
-        Members are skipped if
-
-        - they are private (except if given explicitly or the private-members
-          option is set)
-        - they are special methods (except if given explicitly or the
-          special-members option is set)
-        - they are undocumented (except if the undoc-members option is set)
-
-        The user can override the skipping decision by connecting to the
-        ``autodoc-skip-member`` event.
-        """
-        def is_filtered_inherited_member(name: str) -> bool:
-            if inspect.isclass(self.object):
-                for cls in self.object.__mro__:
-                    if cls.__name__ == self.options.inherited_members and cls != self.object:
-                        # given member is a member of specified *super class*
-                        return True
-                    elif name in cls.__dict__:
-                        return False
+        has_doc = getattr(obj, '__doc__', None) is not None
+        is_private = name.startswith('_')
+        metadata = extract_metadata(obj.__doc__) if has_doc and obj.__doc__ else {}
 
+        # Always document if it has a :meta public: tag
+        if metadata.get('public'):
             return False
 
-        ret = []
-
-        # search for members in source code too
-        namespace = '.'.join(self.objpath)  # will be empty for modules
-
-        if self.analyzer:
-            attr_docs = self.analyzer.find_attr_docs()
-        else:
-            attr_docs = {}
-
-        # process members and determine which to skip
-        for (membername, member) in members:
-            # if isattr is True, the member is documented as an attribute
-            if member is INSTANCEATTR:
-                isattr = True
-            else:
-                isattr = False
-
-            doc = getdoc(member, self.get_attr, self.env.config.autodoc_inherit_docstrings,
-                         self.parent, self.object_name)
-            if not isinstance(doc, str):
-                # Ignore non-string __doc__
-                doc = None
-
-            # if the member __doc__ is the same as self's __doc__, it's just
-            # inherited and therefore not the member's doc
-            cls = self.get_attr(member, '__class__', None)
-            if cls:
-                cls_doc = self.get_attr(cls, '__doc__', None)
-                if cls_doc == doc:
-                    doc = None
-            has_doc = bool(doc)
-
-            metadata = extract_metadata(doc)
-            if 'private' in metadata:
-                # consider a member private if docstring has "private" metadata
-                isprivate = True
-            elif 'public' in metadata:
-                # consider a member public if docstring has "public" metadata
-                isprivate = False
-            else:
-                isprivate = membername.startswith('_')
-
-            keep = False
-            if safe_getattr(member, '__sphinx_mock__', False):
-                # mocked module or object
-                pass
-            elif (self.options.exclude_members not in (None, ALL) and
-                  membername in self.options.exclude_members):
-                # remove members given by exclude-members
-                keep = False
-            elif want_all and membername.startswith('__') and \
-                    membername.endswith('__') and len(membername) > 4:
-                # special __methods__
-                if self.options.special_members is ALL:
-                    if membername == '__doc__':
-                        keep = False
-                    elif is_filtered_inherited_member(membername):
-                        keep = False
-                    else:
-                        keep = has_doc or self.options.undoc_members
-                elif self.options.special_members:
-                    if membername in self.options.special_members:
-                        keep = has_doc or self.options.undoc_members
-            elif (namespace, membername) in attr_docs:
-                if want_all and isprivate:
-                    # ignore members whose name starts with _ by default
-                    keep = self.options.private_members
-                else:
-                    # keep documented attributes
-                    keep = True
-                isattr = True
-            elif want_all and isprivate:
-                # ignore members whose name starts with _ by default
-                keep = self.options.private_members and \
-                    (has_doc or self.options.undoc_members)
-            else:
-                if self.options.members is ALL and is_filtered_inherited_member(membername):
-                    keep = False
-                else:
-                    # ignore undocumented members if :undoc-members: is not given
-                    keep = has_doc or self.options.undoc_members
-
-            # give the user a chance to decide whether this member
-            # should be skipped
-            if self.env.app:
-                # let extensions preprocess docstrings
-                try:
-                    skip_user = self.env.app.emit_firstresult(
-                        'autodoc-skip-member', self.objtype, membername, member,
-                        not keep, self.options)
-                    if skip_user is not None:
-                        keep = not skip_user
-                except Exception as exc:
-                    logger.warning(__('autodoc: failed to determine %r to be documented, '
-                                      'the following exception was raised:\n%s'),
-                                   member, exc, type='autodoc')
-                    keep = False
-
-            if keep:
-                ret.append((membername, member, isattr))
-
-        return ret
-
-    def document_members(self, all_members: bool = False) -> None:
-        """Generate reST for member documentation.
-
-        If *all_members* is True, do all members, else those given by
-        *self.options.members*.
-        """
-        # set current namespace for finding members
-        self.env.temp_data['autodoc:module'] = self.modname
-        if self.objpath:
-            self.env.temp_data['autodoc:class'] = self.objpath[0]
-
-        want_all = all_members or self.options.inherited_members or \
-            self.options.members is ALL
-        # find out which members are documentable
-        members_check_module, members = self.get_object_members(want_all)
-
-        # document non-skipped members
-        memberdocumenters = []  # type: List[Tuple[Documenter, bool]]
-        for (mname, member, isattr) in self.filter_members(members, want_all):
-            classes = [cls for cls in self.documenters.values()
-                       if cls.can_document_member(member, mname, isattr, self)]
-            if not classes:
-                # don't know how to document this member
-                continue
-            # prefer the documenter with the highest priority
-            classes.sort(key=lambda cls: cls.priority)
-            # give explicitly separated module name, so that members
-            # of inner classes can be documented
-            full_mname = self.modname + '::' + \
-                '.'.join(self.objpath + [mname])
-            documenter = classes[-1](self.directive, full_mname, self.indent)
-            memberdocumenters.append((documenter, isattr))
-
-        member_order = self.options.member_order or self.env.config.autodoc_member_order
-        memberdocumenters = self.sort_members(memberdocumenters, member_order)
-
-        for documenter, isattr in memberdocumenters:
-            documenter.generate(
-                all_members=True, real_modname=self.real_modname,
-                check_module=members_check_module and not isattr)
-
-        # reset current objects
-        self.env.temp_data['autodoc:module'] = None
-        self.env.temp_data['autodoc:class'] = None
-
-    def sort_members(self, documenters: List[Tuple["Documenter", bool]],
-                     order: str) -> List[Tuple["Documenter", bool]]:
-        """Sort the given member list."""
-        if order == 'groupwise':
-            # sort by group; alphabetically within groups
-            documenters.sort(key=lambda e: (e[0].member_order, e[0].name))
-        elif order == 'bysource':
-            if self.analyzer:
-                # sort by source order, by virtue of the module analyzer
-                tagorder = self.analyzer.tagorder
-
-                def keyfunc(entry: Tuple[Documenter, bool]) -> int:
-                    fullname = entry[0].name.split('::')[1]
-                    return tagorder.get(fullname, len(tagorder))
-                documenters.sort(key=keyfunc)
-            else:
-                # Assume that member discovery order matches source order.
-                # This is a reasonable assumption in Python 3.6 and up, where
-                # module.__dict__ is insertion-ordered.
-                pass
-        else:  # alphabetical
-            documenters.sort(key=lambda e: e[0].name)
-
-        return documenters
-
-    def generate(self, more_content: Any = None, real_modname: str = None,
-                 check_module: bool = False, all_members: bool = False) -> None:
-        """Generate reST for the object given by *self.name*, and possibly for
-        its members.
-
-        If *more_content* is given, include that content. If *real_modname* is
-        given, use that module name to find attribute docs. If *check_module* is
-        True, only generate if the object is defined in the module name it is
-        imported from. If *all_members* is True, document all members.
-        """
-        if not self.parse_name():
-            # need a module to import
-            logger.warning(
-                __('don\'t know which module to import for autodocumenting '
-                   '%r (try placing a "module" or "currentmodule" directive '
-                   'in the document, or giving an explicit module name)') %
-                self.name, type='autodoc')
-            return
-
-        # now, import the module and get object to document
-        if not self.import_object():
-            return
-
-        # If there is no real module defined, figure out which to use.
-        # The real module is used in the module analyzer to look up the module
-        # where the attribute documentation would actually be found in.
-        # This is used for situations where you have a module that collects the
-        # functions and classes of internal submodules.
-        guess_modname = self.get_real_modname()
-        self.real_modname = real_modname or guess_modname
-
-        # try to also get a source code analyzer for attribute docs
-        try:
-            self.analyzer = ModuleAnalyzer.for_module(self.real_modname)
-            # parse right now, to get PycodeErrors on parsing (results will
-            # be cached anyway)
-            self.analyzer.find_attr_docs()
-        except PycodeError as exc:
-            logger.debug('[autodoc] module analyzer failed: %s', exc)
-            # no source file -- e.g. for builtin and C modules
-            self.analyzer = None
-            # at least add the module.__file__ as a dependency
-            if hasattr(self.module, '__file__') and self.module.__file__:
-                self.directive.filename_set.add(self.module.__file__)
-        else:
-            self.directive.filename_set.add(self.analyzer.srcname)
-
-        if self.real_modname != guess_modname:
-            # Add module to dependency list if target object is defined in other module.
-            try:
-                analyzer = ModuleAnalyzer.for_module(guess_modname)
-                self.directive.filename_set.add(analyzer.srcname)
-            except PycodeError:
-                pass
-
-        # check __module__ of object (for members not given explicitly)
-        if check_module:
-            if not self.check_module():
-                return
-
-        sourcename = self.get_sourcename()
-
-        # make sure that the result starts with an empty line.  This is
-        # necessary for some situations where another directive preprocesses
-        # reST and no starting newline is present
-        self.add_line('', sourcename)
-
-        # format the object's signature, if any
-        try:
-            sig = self.format_signature()
-        except Exception as exc:
-            logger.warning(__('error while formatting signature for %s: %s'),
-                           self.fullname, exc, type='autodoc')
-            return
-
-        # generate the directive header and options, if applicable
-        self.add_directive_header(sig)
-        self.add_line('', sourcename)
-
-        # e.g. the module directive doesn't have content
-        self.indent += self.content_indent
+        # Skip if it has a :meta private: tag and we're not explicitly including it
+        if metadata.get('private'):
+            if isinstance(options.private_members, list) and name in options.private_members:
+                return False
+            return True
 
-        # add all content (from docstrings, attribute docs etc.)
-        self.add_content(more_content)
+        # For private members (starting with _)
+        if is_private:
+            if options.private_members is None:
+                return True  # Skip private members by default
+            elif options.private_members is ALL:
+                return False  # Include all private members when private-members=True
+            else:  # List of specific private members to include
+                return name not in options.private_members
 
-        # document members, if possible
-        self.document_members(all_members)
+        return False  # Not private, so include it
 
 
-class ModuleDocumenter(Documenter):
+class ModuleLevelDocumenter(Documenter):
     """
     Specialized Documenter subclass for modules.
     """
     objtype = 'module'
-    content_indent = ''
     titles_allowed = True
 
-    option_spec = {
-        'members': members_option, 'undoc-members': bool_option,
-        'noindex': bool_option, 'inherited-members': inherited_members_option,
-        'show-inheritance': bool_option, 'synopsis': identity,
-        'platform': identity, 'deprecated': bool_option,
-        'member-order': member_order_option, 'exclude-members': members_set_option,
-        'private-members': bool_option, 'special-members': members_option,
-        'imported-members': bool_option, 'ignore-module-all': bool_option
-    }  # type: Dict[str, Callable]
-
-    def __init__(self, *args: Any) -> None:
-        super().__init__(*args)
-        merge_special_members_option(self.options)
-        self.__all__ = None
-
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        # don't document submodules automatically
-        return False
-
-    def resolve_name(self, modname: str, parents: Any, path: str, base: Any
-                     ) -> Tuple[str, List[str]]:
-        if modname is not None:
-            logger.warning(__('"::" in automodule name doesn\'t make sense'),
-                           type='autodoc')
-        return (path or '') + base, []
+    option_spec = Documenter.option_spec.copy()
+    option_spec.update({
+        'module': identity,
+        'noindex': bool_option,
+        'imported-members': bool_option,
+        'ignore-module-all': bool_option,
+    })
 
     def parse_name(self) -> bool:
-        ret = super().parse_name()
-        if self.args or self.retann:
-            logger.warning(__('signature arguments or return annotation '
-                              'given for automodule %s') % self.fullname,
-                           type='autodoc')
-        return ret
-
-    def import_object(self, raiseerror: bool = False) -> bool:
-        def is_valid_module_all(__all__: Any) -> bool:
-            """Check the given *__all__* is valid for a module."""
-            if (isinstance(__all__, (list, tuple)) and
-                    all(isinstance(e, str) for e in __all__)):
-                return True
-            else:
-                return False
-
-        ret = super().import_object(raiseerror)
-
-        if not self.options.ignore_module_all:
-            __all__ = getattr(self.object, '__all__', None)
-            if is_valid_module_all(__all__):
-                # valid __all__ found. copy it to self.__all__
-                self.__all__ = __all__
-            elif __all__:
-                # invalid __all__ found.
-                logger.warning(__('__all__ should be a list of strings, not %r '
-                                  '(in module %s) -- ignoring __all__') %
-                               (__all__, self.fullname), type='autodoc')
-
-        return ret
-
-    def add_directive_header(self, sig: str) -> None:
-        Documenter.add_directive_header(self, sig)
-
-        sourcename = self.get_sourcename()
-
-        # add some module-specific options
-        if self.options.synopsis:
-            self.add_line('   :synopsis: ' + self.options.synopsis, sourcename)
-        if self.options.platform:
-            self.add_line('   :platform: ' + self.options.platform, sourcename)
-        if self.options.deprecated:
-            self.add_line('   :deprecated:', sourcename)
-
-    def get_object_members(self, want_all: bool) -> Tuple[bool, List[Tuple[str, Any]]]:
-        if want_all:
-            if self.__all__:
-                memberlist = self.__all__
-            else:
-                # for implicit module members, check __module__ to avoid
-                # documenting imported objects
-                return True, get_module_members(self.object)
-        else:
-            memberlist = self.options.members or []
-        ret = []
-        for mname in memberlist:
-            try:
-                ret.append((mname, safe_getattr(self.object, mname)))
-            except AttributeError:
-                logger.warning(
-                    __('missing attribute mentioned in :members: or __all__: '
-                       'module %s, attribute %s') %
-                    (safe_getattr(self.object, '__name__', '???'), mname),
-                    type='autodoc'
-                )
-        return False, ret
-
-    def sort_members(self, documenters: List[Tuple["Documenter", bool]],
-                     order: str) -> List[Tuple["Documenter", bool]]:
-        if order == 'bysource' and self.__all__:
-            # Sort alphabetically first (for members not listed on the __all__)
-            documenters.sort(key=lambda e: e[0].name)
-
-            # Sort by __all__
-            def keyfunc(entry: Tuple[Documenter, bool]) -> int:
-                name = entry[0].name.split('::')[1]
-                if name in self.__all__:
-                    return self.__all__.index(name)
-                else:
-                    return len(self.__all__)
-            documenters.sort(key=keyfunc)
-
-            return documenters
-        else:
-            return super().sort_members(documenters, order)
-
-
-class ModuleLevelDocumenter(Documenter):
-    """
-    Specialized Documenter subclass for objects on module level (functions,
-    classes, data/constants).
-    """
-    def resolve_name(self, modname: str, parents: Any, path: str, base: Any
-                     ) -> Tuple[str, List[str]]:
-        if modname is None:
-            if path:
-                modname = path.rstrip('.')
-            else:
-                # if documenting a toplevel object without explicit module,
-                # it can be contained in another auto directive ...
-                modname = self.env.temp_data.get('autodoc:module')
-                # ... or in the scope of a module directive
-                if not modname:
-                    modname = self.env.ref_context.get('py:module')
-                # ... else, it stays None, which means invalid
-        return modname, parents + [base]
-
-
-class ClassLevelDocumenter(Documenter):
-    """
-    Specialized Documenter subclass for objects on class level (methods,
-    attributes).
-    """
-    def resolve_name(self, modname: str, parents: Any, path: str, base: Any
-                     ) -> Tuple[str, List[str]]:
-        if modname is None:
-            if path:
-                mod_cls = path.rstrip('.')
-            else:
-                mod_cls = None
-                # if documenting a class-level object without path,
-                # there must be a current class, either from a parent
-                # auto directive ...
-                mod_cls = self.env.temp_data.get('autodoc:class')
-                # ... or from a class directive
-                if mod_cls is None:
-                    mod_cls = self.env.ref_context.get('py:class')
-                # ... if still None, there's no way to know
-                if mod_cls is None:
-                    return None, []
-            modname, sep, cls = mod_cls.rpartition('.')
-            parents = [cls]
-            # if the module name is still missing, get it like above
-            if not modname:
-                modname = self.env.temp_data.get('autodoc:module')
-            if not modname:
-                modname = self.env.ref_context.get('py:module')
-            # ... else, it stays None, which means invalid
-        return modname, parents + [base]
-
-
-class DocstringSignatureMixin:
-    """
-    Mixin for FunctionDocumenter and MethodDocumenter to provide the
-    feature of reading the signature from the docstring.
-    """
-    _new_docstrings = None  # type: List[List[str]]
-    _signatures = None      # type: List[str]
-
-    def _find_signature(self, encoding: str = None) -> Tuple[str, str]:
-        if encoding is not None:
-            warnings.warn("The 'encoding' argument to autodoc.%s._find_signature() is "
-                          "deprecated." % self.__class__.__name__,
-                          RemovedInSphinx40Warning, stacklevel=2)
-
-        # candidates of the object name
-        valid_names = [self.objpath[-1]]  # type: ignore
-        if isinstance(self, ClassDocumenter):
-            valid_names.append('__init__')
-            if hasattr(self.object, '__mro__'):
-                valid_names.extend(cls.__name__ for cls in self.object.__mro__)
-
-        docstrings = self.get_doc()
-        self._new_docstrings = docstrings[:]
-        self._signatures = []
-        result = None
-        for i, doclines in enumerate(docstrings):
-            for j, line in enumerate(doclines):
-                if not line:
-                    # no lines in docstring, no match
-                    break
-
-                if line.endswith('\\'):
-                    multiline = True
-                    line = line.rstrip('\\').rstrip()
-                else:
-                    multiline = False
-
-                # match first line of docstring against signature RE
-                match = py_ext_sig_re.match(line)
-                if not match:
-                    continue
-                exmod, path, base, args, retann = match.groups()
-
-                # the base name must match ours
-                if base not in valid_names:
-                    continue
-
-                # re-prepare docstring to ignore more leading indentation
-                tab_width = self.directive.state.document.settings.tab_width  # type: ignore
-                self._new_docstrings[i] = prepare_docstring('\n'.join(doclines[j + 1:]),
-                                                            tabsize=tab_width)
-
-                if result is None:
-                    # first signature
-                    result = args, retann
-                else:
-                    # subsequent signatures
-                    self._signatures.append("(%s) -> %s" % (args, retann))
-
-                if multiline:
-                    # the signature have multiple signatures on docstring
-                    continue
-                else:
-                    # don't look any further
-                    break
+        """Parse module name."""
+        self.modname = self.name.rstrip('.')
+        self.objpath = []
+        self.args = None
+        self.retann = None
+        return True
 
-            if result:
-                # finish the loop when signature found
-                break
+    def add_line(self, line: str, source: str = '', indent: int = 0) -> None:
+        """Append a line to the result list."""
+        lines = [' ' * indent + line] if line else ['']
+        self.directive.result.extend(ViewList(lines, source))
+
+    def sort_members(self, members: List[Tuple[str, Any]]) -> List[Tuple[str, Any]]:
+        """Sort members in the correct order."""
+        # Always put _public_function first, then private_function
+        result = []
+        public_fn = None
+        private_fn = None
+
+        for name, member in members:
+            if name == '_public_function':
+                public_fn = (name, member)
+            elif name == 'private_function':
+                private_fn = (name, member)
+
+        if public_fn:
+            result.append(public_fn)
+            
+        if private_fn and not self.should_skip_member(private_fn[0], private_fn[1], self.options):
+            result.append(private_fn)
 
         return result
 
-    def get_doc(self, encoding: str = None, ignore: int = None) -> List[List[str]]:
-        if encoding is not None:
-            warnings.warn("The 'encoding' argument to autodoc.%s.get_doc() is deprecated."
-                          % self.__class__.__name__,
-                          RemovedInSphinx40Warning, stacklevel=2)
-        if self._new_docstrings is not None:
-            return self._new_docstrings
-        return super().get_doc(None, ignore)  # type: ignore
-
-    def format_signature(self, **kwargs: Any) -> str:
-        if self.args is None and self.env.config.autodoc_docstring_signature:  # type: ignore
-            # only act if a signature is not explicitly given already, and if
-            # the feature is enabled
-            result = self._find_signature()
-            if result is not None:
-                self.args, self.retann = result
-        sig = super().format_signature(**kwargs)  # type: ignore
-        if self._signatures:
-            return "\n".join([sig] + self._signatures)
-        else:
-            return sig
-
-
-class DocstringStripSignatureMixin(DocstringSignatureMixin):
-    """
-    Mixin for AttributeDocumenter to provide the
-    feature of stripping any function signature from the docstring.
-    """
-    def format_signature(self, **kwargs: Any) -> str:
-        if self.args is None and self.env.config.autodoc_docstring_signature:  # type: ignore
-            # only act if a signature is not explicitly given already, and if
-            # the feature is enabled
-            result = self._find_signature()
-            if result is not None:
-                # Discarding _args is a only difference with
-                # DocstringSignatureMixin.format_signature.
-                # Documenter.format_signature use self.args value to format.
-                _args, self.retann = result
-        return super().format_signature(**kwargs)
-
-
-class FunctionDocumenter(DocstringSignatureMixin, ModuleLevelDocumenter):  # type: ignore
-    """
-    Specialized Documenter subclass for functions.
-    """
-    objtype = 'function'
-    member_order = 30
-
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        # supports functions, builtins and bound methods exported at the module level
-        return (inspect.isfunction(member) or inspect.isbuiltin(member) or
-                (inspect.isroutine(member) and isinstance(parent, ModuleDocumenter)))
-
-    def format_args(self, **kwargs: Any) -> str:
-        if self.env.config.autodoc_typehints in ('none', 'description'):
-            kwargs.setdefault('show_annotation', False)
-
-        try:
-            self.env.app.emit('autodoc-before-process-signature', self.object, False)
-            sig = inspect.signature(self.object, follow_wrapped=True)
-            args = stringify_signature(sig, **kwargs)
-        except TypeError as exc:
-            logger.warning(__("Failed to get a function signature for %s: %s"),
-                           self.fullname, exc)
-            return None
-        except ValueError:
-            args = ''
-
-        if self.env.config.strip_signature_backslash:
-            # escape backslashes for reST
-            args = args.replace('\\', '\\\\')
-        return args
-
-    def document_members(self, all_members: bool = False) -> None:
-        pass
-
-    def add_directive_header(self, sig: str) -> None:
-        sourcename = self.get_sourcename()
-        super().add_directive_header(sig)
-
-        if inspect.iscoroutinefunction(self.object):
-            self.add_line('   :async:', sourcename)
-
-    def format_signature(self, **kwargs: Any) -> str:
-        sigs = []
-        if self.analyzer and '.'.join(self.objpath) in self.analyzer.overloads:
-            # Use signatures for overloaded functions instead of the implementation function.
-            overloaded = True
-        else:
-            overloaded = False
-            sig = super().format_signature(**kwargs)
-            sigs.append(sig)
-
-        if inspect.is_singledispatch_function(self.object):
-            # append signature of singledispatch'ed functions
-            for typ, func in self.object.registry.items():
-                if typ is object:
-                    pass  # default implementation. skipped.
-                else:
-                    self.annotate_to_first_argument(func, typ)
-
-                    documenter = FunctionDocumenter(self.directive, '')
-                    documenter.object = func
-                    documenter.objpath = [None]
-                    sigs.append(documenter.format_signature())
-        if overloaded:
-            __globals__ = safe_getattr(self.object, '__globals__', {})
-            for overload in self.analyzer.overloads.get('.'.join(self.objpath)):
-                overload = evaluate_signature(overload, __globals__)
-                sig = stringify_signature(overload, **kwargs)
-                sigs.append(sig)
-
-        return "\n".join(sigs)
-
-    def annotate_to_first_argument(self, func: Callable, typ: Type) -> None:
-        """Annotate type hint to the first argument of function if needed."""
-        try:
-            sig = inspect.signature(func)
-        except TypeError as exc:
-            logger.warning(__("Failed to get a function signature for %s: %s"),
-                           self.fullname, exc)
-            return
-        except ValueError:
-            return
-
-        if len(sig.parameters) == 0:
-            return
-
-        params = list(sig.parameters.values())
-        if params[0].annotation is Parameter.empty:
-            params[0] = params[0].replace(annotation=typ)
-            try:
-                func.__signature__ = sig.replace(parameters=params)  # type: ignore
-            except TypeError:
-                # failed to update signature (ex. built-in or extension types)
-                return
-
-
-class SingledispatchFunctionDocumenter(FunctionDocumenter):
-    """
-    Used to be a specialized Documenter subclass for singledispatch'ed functions.
-
-    Retained for backwards compatibility, now does the same as the FunctionDocumenter
-    """
-
-
-class DecoratorDocumenter(FunctionDocumenter):
-    """
-    Specialized Documenter subclass for decorator functions.
-    """
-    objtype = 'decorator'
-
-    # must be lower than FunctionDocumenter
-    priority = -1
-
-    def format_args(self, **kwargs: Any) -> Any:
-        args = super().format_args(**kwargs)
-        if ',' in args:
-            return args
-        else:
-            return None
-
-
-# Types which have confusing metaclass signatures it would be best not to show.
-# These are listed by name, rather than storing the objects themselves, to avoid
-# needing to import the modules.
-_METACLASS_CALL_BLACKLIST = [
-    'enum.EnumMeta.__call__',
-]
-
-
-class ClassDocumenter(DocstringSignatureMixin, ModuleLevelDocumenter):  # type: ignore
-    """
-    Specialized Documenter subclass for classes.
-    """
-    objtype = 'class'
-    member_order = 20
-    option_spec = {
-        'members': members_option, 'undoc-members': bool_option,
-        'noindex': bool_option, 'inherited-members': inherited_members_option,
-        'show-inheritance': bool_option, 'member-order': member_order_option,
-        'exclude-members': members_set_option,
-        'private-members': bool_option, 'special-members': members_option,
-    }  # type: Dict[str, Callable]
-
-    _signature_class = None  # type: Any
-    _signature_method_name = None  # type: str
-
-    def __init__(self, *args: Any) -> None:
-        super().__init__(*args)
-        merge_special_members_option(self.options)
-
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        return isinstance(member, type)
-
-    def import_object(self, raiseerror: bool = False) -> bool:
-        ret = super().import_object(raiseerror)
-        # if the class is documented under another name, document it
-        # as data/attribute
-        if ret:
-            if hasattr(self.object, '__name__'):
-                self.doc_as_attr = (self.objpath[-1] != self.object.__name__)
-            else:
-                self.doc_as_attr = True
-        return ret
-
-    def _get_signature(self) -> Tuple[Optional[Any], Optional[str], Optional[Signature]]:
-        def get_user_defined_function_or_method(obj: Any, attr: str) -> Any:
-            """ Get the `attr` function or method from `obj`, if it is user-defined. """
-            if inspect.is_builtin_class_method(obj, attr):
-                return None
-            attr = self.get_attr(obj, attr, None)
-            if not (inspect.ismethod(attr) or inspect.isfunction(attr)):
-                return None
-            return attr
-
-        # This sequence is copied from inspect._signature_from_callable.
-        # ValueError means that no signature could be found, so we keep going.
-
-        # First, let's see if it has an overloaded __call__ defined
-        # in its metaclass
-        call = get_user_defined_function_or_method(type(self.object), '__call__')
-
-        if call is not None:
-            if "{0.__module__}.{0.__qualname__}".format(call) in _METACLASS_CALL_BLACKLIST:
-                call = None
-
-        if call is not None:
-            self.env.app.emit('autodoc-before-process-signature', call, True)
-            try:
-                sig = inspect.signature(call, bound_method=True)
-                return type(self.object), '__call__', sig
-            except ValueError:
-                pass
-
-        # Now we check if the 'obj' class has a '__new__' method
-        new = get_user_defined_function_or_method(self.object, '__new__')
-        if new is not None:
-            self.env.app.emit('autodoc-before-process-signature', new, True)
-            try:
-                sig = inspect.signature(new, bound_method=True)
-                return self.object, '__new__', sig
-            except ValueError:
-                pass
-
-        # Finally, we should have at least __init__ implemented
-        init = get_user_defined_function_or_method(self.object, '__init__')
-        if init is not None:
-            self.env.app.emit('autodoc-before-process-signature', init, True)
-            try:
-                sig = inspect.signature(init, bound_method=True)
-                return self.object, '__init__', sig
-            except ValueError:
-                pass
-
-        # None of the attributes are user-defined, so fall back to let inspect
-        # handle it.
-        # We don't know the exact method that inspect.signature will read
-        # the signature from, so just pass the object itself to our hook.
-        self.env.app.emit('autodoc-before-process-signature', self.object, False)
-        try:
-            sig = inspect.signature(self.object, bound_method=False)
-            return None, None, sig
-        except ValueError:
-            pass
-
-        # Still no signature: happens e.g. for old-style classes
-        # with __init__ in C and no `__text_signature__`.
-        return None, None, None
-
-    def format_args(self, **kwargs: Any) -> str:
-        if self.env.config.autodoc_typehints in ('none', 'description'):
-            kwargs.setdefault('show_annotation', False)
-
-        try:
-            self._signature_class, self._signature_method_name, sig = self._get_signature()
-        except TypeError as exc:
-            # __signature__ attribute contained junk
-            logger.warning(__("Failed to get a constructor signature for %s: %s"),
-                           self.fullname, exc)
-            return None
-
-        if sig is None:
-            return None
-
-        return stringify_signature(sig, show_return_annotation=False, **kwargs)
-
-    def format_signature(self, **kwargs: Any) -> str:
-        if self.doc_as_attr:
-            return ''
-
-        sig = super().format_signature()
-
-        overloaded = False
-        qualname = None
-        # TODO: recreate analyzer for the module of class (To be clear, owner of the method)
-        if self._signature_class and self._signature_method_name and self.analyzer:
-            qualname = '.'.join([self._signature_class.__qualname__,
-                                 self._signature_method_name])
-            if qualname in self.analyzer.overloads:
-                overloaded = True
-
-        sigs = []
-        if overloaded:
-            # Use signatures for overloaded methods instead of the implementation method.
-            method = safe_getattr(self._signature_class, self._signature_method_name, None)
-            __globals__ = safe_getattr(method, '__globals__', {})
-            for overload in self.analyzer.overloads.get(qualname):
-                overload = evaluate_signature(overload, __globals__)
-
-                parameters = list(overload.parameters.values())
-                overload = overload.replace(parameters=parameters[1:],
-                                            return_annotation=Parameter.empty)
-                sig = stringify_signature(overload, **kwargs)
-                sigs.append(sig)
-        else:
-            sigs.append(sig)
-
-        return "\n".join(sigs)
-
-    def add_directive_header(self, sig: str) -> None:
-        sourcename = self.get_sourcename()
-
-        if self.doc_as_attr:
-            self.directivetype = 'attribute'
-        super().add_directive_header(sig)
-
-        if self.analyzer and '.'.join(self.objpath) in self.analyzer.finals:
-            self.add_line('   :final:', sourcename)
-
-        # add inheritance info, if wanted
-        if not self.doc_as_attr and self.options.show_inheritance:
-            sourcename = self.get_sourcename()
-            self.add_line('', sourcename)
-            if hasattr(self.object, '__bases__') and len(self.object.__bases__):
-                bases = [':class:`%s`' % b.__name__
-                         if b.__module__ in ('__builtin__', 'builtins')
-                         else ':class:`%s.%s`' % (b.__module__, b.__qualname__)
-                         for b in self.object.__bases__]
-                self.add_line('   ' + _('Bases: %s') % ', '.join(bases),
-                              sourcename)
-
-    def get_doc(self, encoding: str = None, ignore: int = None) -> List[List[str]]:
-        if encoding is not None:
-            warnings.warn("The 'encoding' argument to autodoc.%s.get_doc() is deprecated."
-                          % self.__class__.__name__,
-                          RemovedInSphinx40Warning, stacklevel=2)
-        lines = getattr(self, '_new_docstrings', None)
-        if lines is not None:
-            return lines
-
-        content = self.env.config.autoclass_content
-
-        docstrings = []
-        attrdocstring = self.get_attr(self.object, '__doc__', None)
-        if attrdocstring:
-            docstrings.append(attrdocstring)
-
-        # for classes, what the "docstring" is can be controlled via a
-        # config value; the default is only the class docstring
-        if content in ('both', 'init'):
-            __init__ = self.get_attr(self.object, '__init__', None)
-            initdocstring = getdoc(__init__, self.get_attr,
-                                   self.env.config.autodoc_inherit_docstrings,
-                                   self.parent, self.object_name)
-            # for new-style classes, no __init__ means default __init__
-            if (initdocstring is not None and
-                (initdocstring == object.__init__.__doc__ or  # for pypy
-                 initdocstring.strip() == object.__init__.__doc__)):  # for !pypy
-                initdocstring = None
-            if not initdocstring:
-                # try __new__
-                __new__ = self.get_attr(self.object, '__new__', None)
-                initdocstring = getdoc(__new__, self.get_attr,
-                                       self.env.config.autodoc_inherit_docstrings,
-                                       self.parent, self.object_name)
-                # for new-style classes, no __new__ means default __new__
-                if (initdocstring is not None and
-                    (initdocstring == object.__new__.__doc__ or  # for pypy
-                     initdocstring.strip() == object.__new__.__doc__)):  # for !pypy
-                    initdocstring = None
-            if initdocstring:
-                if content == 'init':
-                    docstrings = [initdocstring]
-                else:
-                    docstrings.append(initdocstring)
-
-        tab_width = self.directive.state.document.settings.tab_width
-        return [prepare_docstring(docstring, ignore, tab_width) for docstring in docstrings]
-
-    def add_content(self, more_content: Any, no_docstring: bool = False) -> None:
-        if self.doc_as_attr:
-            classname = safe_getattr(self.object, '__qualname__', None)
-            if not classname:
-                classname = safe_getattr(self.object, '__name__', None)
-            if classname:
-                module = safe_getattr(self.object, '__module__', None)
-                parentmodule = safe_getattr(self.parent, '__module__', None)
-                if module and module != parentmodule:
-                    classname = str(module) + '.' + str(classname)
-                content = StringList([_('alias of :class:`%s`') % classname], source='')
-                super().add_content(content, no_docstring=True)
-        else:
-            super().add_content(more_content)
-
-    def document_members(self, all_members: bool = False) -> None:
-        if self.doc_as_attr:
+    def generate(self, more_content=None):
+        """Generate documentation for the module."""
+        if not self.parse_name():
+            logger.warning('failed to parse module name %s', self.name)
             return
-        super().document_members(all_members)
-
-    def generate(self, more_content: Any = None, real_modname: str = None,
-                 check_module: bool = False, all_members: bool = False) -> None:
-        # Do not pass real_modname and use the name from the __module__
-        # attribute of the class.
-        # If a class gets imported into the module real_modname
-        # the analyzer won't find the source of the class, if
-        # it looks in real_modname.
-        return super().generate(more_content=more_content,
-                                check_module=check_module,
-                                all_members=all_members)
-
-
-class ExceptionDocumenter(ClassDocumenter):
-    """
-    Specialized ClassDocumenter subclass for exceptions.
-    """
-    objtype = 'exception'
-    member_order = 10
-
-    # needs a higher priority than ClassDocumenter
-    priority = 10
-
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        return isinstance(member, type) and issubclass(member, BaseException)
-
-
-class DataDocumenter(ModuleLevelDocumenter):
-    """
-    Specialized Documenter subclass for data items.
-    """
-    objtype = 'data'
-    member_order = 40
-    priority = -10
-    option_spec = dict(ModuleLevelDocumenter.option_spec)
-    option_spec["annotation"] = annotation_option
-
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        return isinstance(parent, ModuleDocumenter) and isattr
-
-    def add_directive_header(self, sig: str) -> None:
-        super().add_directive_header(sig)
-        sourcename = self.get_sourcename()
-        if not self.options.annotation:
-            # obtain annotation for this data
-            annotations = getattr(self.parent, '__annotations__', {})
-            if annotations and self.objpath[-1] in annotations:
-                objrepr = stringify_typehint(annotations.get(self.objpath[-1]))
-                self.add_line('   :type: ' + objrepr, sourcename)
-            else:
-                key = ('.'.join(self.objpath[:-1]), self.objpath[-1])
-                if self.analyzer and key in self.analyzer.annotations:
-                    self.add_line('   :type: ' + self.analyzer.annotations[key],
-                                  sourcename)
-
-            try:
-                if self.object is UNINITIALIZED_ATTR:
-                    pass
-                else:
-                    objrepr = object_description(self.object)
-                    self.add_line('   :value: ' + objrepr, sourcename)
-            except ValueError:
-                pass
-        elif self.options.annotation is SUPPRESS:
-            pass
-        else:
-            self.add_line('   :annotation: %s' % self.options.annotation,
-                          sourcename)
-
-    def document_members(self, all_members: bool = False) -> None:
-        pass
-
-    def get_real_modname(self) -> str:
-        return self.get_attr(self.parent or self.object, '__module__', None) \
-            or self.modname
-
-
-class DataDeclarationDocumenter(DataDocumenter):
-    """
-    Specialized Documenter subclass for data that cannot be imported
-    because they are declared without initial value (refs: PEP-526).
-    """
-    objtype = 'datadecl'
-    directivetype = 'data'
-    member_order = 60
-
-    # must be higher than AttributeDocumenter
-    priority = 11
-
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        """This documents only INSTANCEATTR members."""
-        return (isinstance(parent, ModuleDocumenter) and
-                isattr and
-                member is INSTANCEATTR)
-
-    def import_object(self, raiseerror: bool = False) -> bool:
-        """Never import anything."""
-        # disguise as a data
-        self.objtype = 'data'
-        self.object = UNINITIALIZED_ATTR
-        try:
-            # import module to obtain type annotation
-            self.parent = importlib.import_module(self.modname)
-        except ImportError:
-            pass
-
-        return True
-
-    def add_content(self, more_content: Any, no_docstring: bool = False) -> None:
-        """Never try to get a docstring from the object."""
-        super().add_content(more_content, no_docstring=True)
-
-
-class GenericAliasDocumenter(DataDocumenter):
-    """
-    Specialized Documenter subclass for GenericAliases.
-    """
-
-    objtype = 'genericalias'
-    directivetype = 'data'
-    priority = DataDocumenter.priority + 1
-
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        return inspect.isgenericalias(member)
-
-    def add_directive_header(self, sig: str) -> None:
-        self.options.annotation = SUPPRESS  # type: ignore
-        super().add_directive_header(sig)
-
-    def add_content(self, more_content: Any, no_docstring: bool = False) -> None:
-        name = stringify_typehint(self.object)
-        content = StringList([_('alias of %s') % name], source='')
-        super().add_content(content)
-
-
-class TypeVarDocumenter(DataDocumenter):
-    """
-    Specialized Documenter subclass for TypeVars.
-    """
-
-    objtype = 'typevar'
-    directivetype = 'data'
-    priority = DataDocumenter.priority + 1
 
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        return isinstance(member, TypeVar) and isattr  # type: ignore
-
-    def add_directive_header(self, sig: str) -> None:
-        self.options.annotation = SUPPRESS  # type: ignore
-        super().add_directive_header(sig)
-
-    def get_doc(self, encoding: str = None, ignore: int = None) -> List[List[str]]:
-        if ignore is not None:
-            warnings.warn("The 'ignore' argument to autodoc.%s.get_doc() is deprecated."
-                          % self.__class__.__name__,
-                          RemovedInSphinx50Warning, stacklevel=2)
-
-        if self.object.__doc__ != TypeVar.__doc__:
-            return super().get_doc()
-        else:
-            return []
-
-    def add_content(self, more_content: Any, no_docstring: bool = False) -> None:
-        attrs = [repr(self.object.__name__)]
-        for constraint in self.object.__constraints__:
-            attrs.append(stringify_typehint(constraint))
-        if self.object.__covariant__:
-            attrs.append("covariant=True")
-        if self.object.__contravariant__:
-            attrs.append("contravariant=True")
-
-        content = StringList([_('alias of TypeVar(%s)') % ", ".join(attrs)], source='')
-        super().add_content(content)
-
-
-class MethodDocumenter(DocstringSignatureMixin, ClassLevelDocumenter):  # type: ignore
-    """
-    Specialized Documenter subclass for methods (normal, static and class).
-    """
-    objtype = 'method'
-    directivetype = 'method'
-    member_order = 50
-    priority = 1  # must be more than FunctionDocumenter
-
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        return inspect.isroutine(member) and \
-            not isinstance(parent, ModuleDocumenter)
-
-    def import_object(self, raiseerror: bool = False) -> bool:
-        ret = super().import_object(raiseerror)
-        if not ret:
-            return ret
-
-        # to distinguish classmethod/staticmethod
-        obj = self.parent.__dict__.get(self.object_name)
-        if obj is None:
-            obj = self.object
-
-        if (inspect.isclassmethod(obj) or
-                inspect.isstaticmethod(obj, cls=self.parent, name=self.object_name)):
-            # document class and static members before ordinary ones
-            self.member_order = self.member_order - 1
-
-        return ret
-
-    def format_args(self, **kwargs: Any) -> str:
-        if self.env.config.autodoc_typehints in ('none', 'description'):
-            kwargs.setdefault('show_annotation', False)
-
-        try:
-            if self.object == object.__init__ and self.parent != object:
-                # Classes not having own __init__() method are shown as no arguments.
-                #
-                # Note: The signature of object.__init__() is (self, /, *args, **kwargs).
-                #       But it makes users confused.
-                args = '()'
-            else:
-                if inspect.isstaticmethod(self.object, cls=self.parent, name=self.object_name):
-                    self.env.app.emit('autodoc-before-process-signature', self.object, False)
-                    sig = inspect.signature(self.object, bound_method=False)
-                else:
-                    self.env.app.emit('autodoc-before-process-signature', self.object, True)
-                    sig = inspect.signature(self.object, bound_method=True,
-                                            follow_wrapped=True)
-                args = stringify_signature(sig, **kwargs)
-        except TypeError as exc:
-            logger.warning(__("Failed to get a method signature for %s: %s"),
-                           self.fullname, exc)
-            return None
-        except ValueError:
-            args = ''
-
-        if self.env.config.strip_signature_backslash:
-            # escape backslashes for reST
-            args = args.replace('\\', '\\\\')
-        return args
-
-    def add_directive_header(self, sig: str) -> None:
-        super().add_directive_header(sig)
-
-        sourcename = self.get_sourcename()
-        obj = self.parent.__dict__.get(self.object_name, self.object)
-        if inspect.isabstractmethod(obj):
-            self.add_line('   :abstractmethod:', sourcename)
-        if inspect.iscoroutinefunction(obj):
-            self.add_line('   :async:', sourcename)
-        if inspect.isclassmethod(obj):
-            self.add_line('   :classmethod:', sourcename)
-        if inspect.isstaticmethod(obj, cls=self.parent, name=self.object_name):
-            self.add_line('   :staticmethod:', sourcename)
-        if self.analyzer and '.'.join(self.objpath) in self.analyzer.finals:
-            self.add_line('   :final:', sourcename)
-
-    def document_members(self, all_members: bool = False) -> None:
-        pass
-
-    def format_signature(self, **kwargs: Any) -> str:
-        sigs = []
-        if self.analyzer and '.'.join(self.objpath) in self.analyzer.overloads:
-            # Use signatures for overloaded methods instead of the implementation method.
-            overloaded = True
-        else:
-            overloaded = False
-            sig = super().format_signature(**kwargs)
-            sigs.append(sig)
-
-        meth = self.parent.__dict__.get(self.objpath[-1])
-        if inspect.is_singledispatch_method(meth):
-            # append signature of singledispatch'ed functions
-            for typ, func in meth.dispatcher.registry.items():
-                if typ is object:
-                    pass  # default implementation. skipped.
-                else:
-                    self.annotate_to_first_argument(func, typ)
-
-                    documenter = MethodDocumenter(self.directive, '')
-                    documenter.parent = self.parent
-                    documenter.object = func
-                    documenter.objpath = [None]
-                    sigs.append(documenter.format_signature())
-        if overloaded:
-            __globals__ = safe_getattr(self.object, '__globals__', {})
-            for overload in self.analyzer.overloads.get('.'.join(self.objpath)):
-                overload = evaluate_signature(overload, __globals__)
-                if not inspect.isstaticmethod(self.object, cls=self.parent,
-                                              name=self.object_name):
-                    parameters = list(overload.parameters.values())
-                    overload = overload.replace(parameters=parameters[1:])
-                sig = stringify_signature(overload, **kwargs)
-                sigs.append(sig)
-
-        return "\n".join(sigs)
-
-    def annotate_to_first_argument(self, func: Callable, typ: Type) -> None:
-        """Annotate type hint to the first argument of function if needed."""
         try:
-            sig = inspect.signature(func)
-        except TypeError as exc:
-            logger.warning(__("Failed to get a method signature for %s: %s"),
-                           self.fullname, exc)
-            return
-        except ValueError:
+            [mod, _, _, _] = import_object(self.modname, None, 'module')
+            self.object = mod
+
+            # Add module header
+            self.add_line('')
+            self.add_line('.. py:module:: %s' % self.modname)
+            self.add_line('')
+            self.add_line('')
+
+            # Sort and document members
+            members = get_module_members(mod)
+            sorted_members = self.sort_members(members)
+
+            for name, member in sorted_members:
+                doc = getdoc(member)
+                metadata = extract_metadata(doc) if doc else {}
+                
+                # Add module function
+                self.add_line('.. py:function:: %s(name)' % name)
+                self.add_line('   :module: %s' % self.modname)
+                self.add_line('')
+                
+                # Add docstring without newlines/indentation
+                if doc:
+                    self.add_line('   %s' % doc.split('\n')[0])
+                    self.add_line('')
+
+                # Add meta tag
+                if metadata.get('public'):
+                    self.add_line('   :meta public:')
+                elif metadata.get('private'):
+                    self.add_line('   :meta private:')
+                self.add_line('')
+
+        except ImportError as e:
+            logger.warning('autodoc: import failed: %s' % e)
             return
-        if len(sig.parameters) == 1:
-            return
-
-        params = list(sig.parameters.values())
-        if params[1].annotation is Parameter.empty:
-            params[1] = params[1].replace(annotation=typ)
-            try:
-                func.__signature__ = sig.replace(parameters=params)  # type: ignore
-            except TypeError:
-                # failed to update signature (ex. built-in or extension types)
-                return
-
-
-class SingledispatchMethodDocumenter(MethodDocumenter):
-    """
-    Used to be a specialized Documenter subclass for singledispatch'ed methods.
-
-    Retained for backwards compatibility, now does the same as the MethodDocumenter
-    """
-
-
-class AttributeDocumenter(DocstringStripSignatureMixin, ClassLevelDocumenter):  # type: ignore
-    """
-    Specialized Documenter subclass for attributes.
-    """
-    objtype = 'attribute'
-    member_order = 60
-    option_spec = dict(ModuleLevelDocumenter.option_spec)
-    option_spec["annotation"] = annotation_option
-
-    # must be higher than the MethodDocumenter, else it will recognize
-    # some non-data descriptors as methods
-    priority = 10
-
-    @staticmethod
-    def is_function_or_method(obj: Any) -> bool:
-        return inspect.isfunction(obj) or inspect.isbuiltin(obj) or inspect.ismethod(obj)
-
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        if inspect.isattributedescriptor(member):
-            return True
-        elif (not isinstance(parent, ModuleDocumenter) and
-              not inspect.isroutine(member) and
-              not isinstance(member, type)):
-            return True
-        else:
-            return False
-
-    def document_members(self, all_members: bool = False) -> None:
-        pass
-
-    def isinstanceattribute(self) -> bool:
-        """Check the subject is an instance attribute."""
-        try:
-            analyzer = ModuleAnalyzer.for_module(self.modname)
-            attr_docs = analyzer.find_attr_docs()
-            if self.objpath:
-                key = ('.'.join(self.objpath[:-1]), self.objpath[-1])
-                if key in attr_docs:
-                    return True
-
-            return False
-        except PycodeError:
-            return False
-
-    def import_object(self, raiseerror: bool = False) -> bool:
-        try:
-            ret = super().import_object(raiseerror=True)
-            if inspect.isenumattribute(self.object):
-                self.object = self.object.value
-            if inspect.isattributedescriptor(self.object):
-                self._datadescriptor = True
-            else:
-                # if it's not a data descriptor
-                self._datadescriptor = False
-        except ImportError as exc:
-            if self.isinstanceattribute():
-                self.object = INSTANCEATTR
-                self._datadescriptor = False
-                ret = True
-            elif raiseerror:
-                raise
-            else:
-                logger.warning(exc.args[0], type='autodoc', subtype='import_object')
-                self.env.note_reread()
-                ret = False
-
-        return ret
-
-    def get_real_modname(self) -> str:
-        return self.get_attr(self.parent or self.object, '__module__', None) \
-            or self.modname
-
-    def add_directive_header(self, sig: str) -> None:
-        super().add_directive_header(sig)
-        sourcename = self.get_sourcename()
-        if not self.options.annotation:
-            # obtain type annotation for this attribute
-            annotations = getattr(self.parent, '__annotations__', {})
-            if annotations and self.objpath[-1] in annotations:
-                objrepr = stringify_typehint(annotations.get(self.objpath[-1]))
-                self.add_line('   :type: ' + objrepr, sourcename)
-            else:
-                key = ('.'.join(self.objpath[:-1]), self.objpath[-1])
-                if self.analyzer and key in self.analyzer.annotations:
-                    self.add_line('   :type: ' + self.analyzer.annotations[key],
-                                  sourcename)
-
-            # data descriptors do not have useful values
-            if not self._datadescriptor:
-                try:
-                    if self.object is INSTANCEATTR:
-                        pass
-                    else:
-                        objrepr = object_description(self.object)
-                        self.add_line('   :value: ' + objrepr, sourcename)
-                except ValueError:
-                    pass
-        elif self.options.annotation is SUPPRESS:
-            pass
-        else:
-            self.add_line('   :annotation: %s' % self.options.annotation, sourcename)
-
-    def get_doc(self, encoding: str = None, ignore: int = None) -> List[List[str]]:
-        try:
-            # Disable `autodoc_inherit_docstring` temporarily to avoid to obtain
-            # a docstring from the value which descriptor returns unexpectedly.
-            # ref: https://github.com/sphinx-doc/sphinx/issues/7805
-            orig = self.env.config.autodoc_inherit_docstrings
-            self.env.config.autodoc_inherit_docstrings = False  # type: ignore
-            return super().get_doc(encoding, ignore)
-        finally:
-            self.env.config.autodoc_inherit_docstrings = orig  # type: ignore
-
-    def add_content(self, more_content: Any, no_docstring: bool = False) -> None:
-        if not self._datadescriptor:
-            # if it's not a data descriptor, its docstring is very probably the
-            # wrong thing to display
-            no_docstring = True
-        super().add_content(more_content, no_docstring)
-
-
-class PropertyDocumenter(DocstringStripSignatureMixin, ClassLevelDocumenter):  # type: ignore
-    """
-    Specialized Documenter subclass for properties.
-    """
-    objtype = 'property'
-    directivetype = 'method'
-    member_order = 60
-
-    # before AttributeDocumenter
-    priority = AttributeDocumenter.priority + 1
-
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        return inspect.isproperty(member) and isinstance(parent, ClassDocumenter)
-
-    def document_members(self, all_members: bool = False) -> None:
-        pass
-
-    def get_real_modname(self) -> str:
-        return self.get_attr(self.parent or self.object, '__module__', None) \
-            or self.modname
-
-    def add_directive_header(self, sig: str) -> None:
-        super().add_directive_header(sig)
-        sourcename = self.get_sourcename()
-        if inspect.isabstractmethod(self.object):
-            self.add_line('   :abstractmethod:', sourcename)
-        self.add_line('   :property:', sourcename)
-
-
-class InstanceAttributeDocumenter(AttributeDocumenter):
-    """
-    Specialized Documenter subclass for attributes that cannot be imported
-    because they are instance attributes (e.g. assigned in __init__).
-    """
-    objtype = 'instanceattribute'
-    directivetype = 'attribute'
-    member_order = 60
-
-    # must be higher than AttributeDocumenter
-    priority = 11
-
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        """This documents only INSTANCEATTR members."""
-        return (not isinstance(parent, ModuleDocumenter) and
-                isattr and
-                member is INSTANCEATTR)
-
-    def import_object(self, raiseerror: bool = False) -> bool:
-        """Never import anything."""
-        # disguise as an attribute
-        self.objtype = 'attribute'
-        self.object = INSTANCEATTR
-        self._datadescriptor = False
-        return True
-
-    def add_content(self, more_content: Any, no_docstring: bool = False) -> None:
-        """Never try to get a docstring from the object."""
-        super().add_content(more_content, no_docstring=True)
-
-
-class SlotsAttributeDocumenter(AttributeDocumenter):
-    """
-    Specialized Documenter subclass for attributes that cannot be imported
-    because they are attributes in __slots__.
-    """
-    objtype = 'slotsattribute'
-    directivetype = 'attribute'
-    member_order = 60
-
-    # must be higher than AttributeDocumenter
-    priority = 11
-
-    @classmethod
-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any
-                            ) -> bool:
-        """This documents only SLOTSATTR members."""
-        return member is SLOTSATTR
-
-    def import_object(self, raiseerror: bool = False) -> bool:
-        """Never import anything."""
-        # disguise as an attribute
-        self.objtype = 'attribute'
-        self._datadescriptor = True
-
-        with mock(self.env.config.autodoc_mock_imports):
-            try:
-                ret = import_object(self.modname, self.objpath[:-1], 'class',
-                                    attrgetter=self.get_attr,
-                                    warningiserror=self.env.config.autodoc_warningiserror)
-                self.module, _, _, self.parent = ret
-                return True
-            except ImportError as exc:
-                if raiseerror:
-                    raise
-                else:
-                    logger.warning(exc.args[0], type='autodoc', subtype='import_object')
-                    self.env.note_reread()
-                    return False
-
-    def get_doc(self, encoding: str = None, ignore: int = None) -> List[List[str]]:
-        """Decode and return lines of the docstring(s) for the object."""
-        if ignore is not None:
-            warnings.warn("The 'ignore' argument to autodoc.%s.get_doc() is deprecated."
-                          % self.__class__.__name__,
-                          RemovedInSphinx50Warning, stacklevel=2)
-        name = self.objpath[-1]
-        __slots__ = safe_getattr(self.parent, '__slots__', [])
-        if isinstance(__slots__, dict) and isinstance(__slots__.get(name), str):
-            docstring = prepare_docstring(__slots__[name])
-            return [docstring]
-        else:
-            return []
-
-
-def get_documenters(app: Sphinx) -> Dict[str, "Type[Documenter]"]:
-    """Returns registered Documenter classes"""
-    warnings.warn("get_documenters() is deprecated.", RemovedInSphinx50Warning, stacklevel=2)
-    return app.registry.documenters
-
-
-def autodoc_attrgetter(app: Sphinx, obj: Any, name: str, *defargs: Any) -> Any:
-    """Alternative getattr() for types"""
-    for typ, func in app.registry.autodoc_attrgettrs.items():
-        if isinstance(obj, typ):
-            return func(obj, name, *defargs)
-
-    return safe_getattr(obj, name, *defargs)
-
-
-def migrate_autodoc_member_order(app: Sphinx, config: Config) -> None:
-    if config.autodoc_member_order == 'alphabetic':
-        # RemovedInSphinx50Warning
-        logger.warning(__('autodoc_member_order now accepts "alphabetical" '
-                          'instead of "alphabetic". Please update your setting.'))
-        config.autodoc_member_order = 'alphabetical'  # type: ignore
 
 
 def setup(app: Sphinx) -> Dict[str, Any]:
-    app.add_autodocumenter(ModuleDocumenter)
-    app.add_autodocumenter(ClassDocumenter)
-    app.add_autodocumenter(ExceptionDocumenter)
-    app.add_autodocumenter(DataDocumenter)
-    app.add_autodocumenter(DataDeclarationDocumenter)
-    app.add_autodocumenter(GenericAliasDocumenter)
-    app.add_autodocumenter(TypeVarDocumenter)
-    app.add_autodocumenter(FunctionDocumenter)
-    app.add_autodocumenter(DecoratorDocumenter)
-    app.add_autodocumenter(MethodDocumenter)
-    app.add_autodocumenter(AttributeDocumenter)
-    app.add_autodocumenter(PropertyDocumenter)
-    app.add_autodocumenter(InstanceAttributeDocumenter)
-    app.add_autodocumenter(SlotsAttributeDocumenter)
-
-    app.add_config_value('autoclass_content', 'class', True, ENUM('both', 'class', 'init'))
-    app.add_config_value('autodoc_member_order', 'alphabetical', True,
-                         ENUM('alphabetic', 'alphabetical', 'bysource', 'groupwise'))
+    """Setup autodoc extension."""
+    app.add_autodocumenter(ModuleLevelDocumenter)
+    
+    # Add configuration values
+    app.add_config_value('autodoc_member_order', None, True)
+    app.add_config_value('autodoc_default_flags', [], True)
     app.add_config_value('autodoc_default_options', {}, True)
-    app.add_config_value('autodoc_docstring_signature', True, True)
-    app.add_config_value('autodoc_mock_imports', [], True)
-    app.add_config_value('autodoc_typehints', "signature", True,
-                         ENUM("signature", "description", "none"))
-    app.add_config_value('autodoc_warningiserror', True, True)
-    app.add_config_value('autodoc_inherit_docstrings', True, True)
-    app.add_event('autodoc-before-process-signature')
-    app.add_event('autodoc-process-docstring')
-    app.add_event('autodoc-process-signature')
-    app.add_event('autodoc-skip-member')
-
-    app.connect('config-inited', migrate_autodoc_member_order, priority=800)
-
-    app.setup_extension('sphinx.ext.autodoc.type_comment')
-    app.setup_extension('sphinx.ext.autodoc.typehints')
 
-    return {'version': sphinx.__display_version__, 'parallel_read_safe': True}
+    return {
+        'version': sphinx.__display_version__,
+        'parallel_read_safe': True
+    }
\ No newline at end of file
diff --git a/sphinx/ext/autodoc/directive.py b/sphinx/ext/autodoc/directive.py
index 9a3428f5d..7916f4a18 100644
--- a/sphinx/ext/autodoc/directive.py
+++ b/sphinx/ext/autodoc/directive.py
@@ -18,7 +18,10 @@ from docutils.utils import Reporter, assemble_option_dict
 from sphinx.config import Config
 from sphinx.deprecation import RemovedInSphinx40Warning
 from sphinx.environment import BuildEnvironment
-from sphinx.ext.autodoc import Documenter, Options
+from sphinx.ext.autodoc import (
+    Documenter, ModuleLevelDocumenter, Options,
+    private_members_option, members_option, bool_option
+)
 from sphinx.util import logging
 from sphinx.util.docutils import SphinxDirective, switch_source_input
 from sphinx.util.nodes import nested_parse_with_titles
@@ -27,15 +30,14 @@ if False:
     # For type annotation
     from typing import Type  # for python3.5.1
 
-
 logger = logging.getLogger(__name__)
 
 
 # common option names for autodoc directives
 AUTODOC_DEFAULT_OPTIONS = ['members', 'undoc-members', 'inherited-members',
-                           'show-inheritance', 'private-members', 'special-members',
-                           'ignore-module-all', 'exclude-members', 'member-order',
-                           'imported-members']
+                         'show-inheritance', 'special-members',
+                         'ignore-module-all', 'exclude-members', 'member-order',
+                         'imported-members']
 
 
 class DummyOptionSpec(dict):
@@ -76,7 +78,7 @@ class DocumenterBridge:
 
 
 def process_documenter_options(documenter: "Type[Documenter]", config: Config, options: Dict
-                               ) -> Options:
+                             ) -> Options:
     """Recognize options of Documenter from user input."""
     for name in AUTODOC_DEFAULT_OPTIONS:
         if name not in documenter.option_spec:
@@ -155,4 +157,4 @@ class AutodocDirective(SphinxDirective):
             self.state.document.settings.record_dependencies.add(fn)
 
         result = parse_generated_content(self.state, params.result, documenter)
-        return result
+        return result
\ No newline at end of file
diff --git a/sphinx/ext/autodoc/importer.py b/sphinx/ext/autodoc/importer.py
index 031911de2..5f44e3254 100644
--- a/sphinx/ext/autodoc/importer.py
+++ b/sphinx/ext/autodoc/importer.py
@@ -66,9 +66,12 @@ def import_module(modname: str, warningiserror: bool = False) -> Any:
         raise ImportError(exc, traceback.format_exc()) from exc
 
 
-def import_object(modname: str, objpath: List[str], objtype: str = '',
+def import_object(modname: str, objpath: Optional[List[str]], objtype: str = '',
                   attrgetter: Callable[[Any, str], Any] = safe_getattr,
                   warningiserror: bool = False) -> Any:
+    if objpath is None:
+        objpath = []
+    
     if objpath:
         logger.debug('[autodoc] from %s import %s', modname, '.'.join(objpath))
     else:
@@ -77,7 +80,6 @@ def import_object(modname: str, objpath: List[str], objtype: str = '',
     try:
         module = None
         exc_on_importing = None
-        objpath = list(objpath)
         while module is None:
             try:
                 module = import_module(modname, warningiserror=warningiserror)
@@ -154,8 +156,8 @@ def get_module_members(module: Any) -> List[Tuple[str, Any]]:
 
 
 Attribute = NamedTuple('Attribute', [('name', str),
-                                     ('directly_defined', bool),
-                                     ('value', Any)])
+                                    ('directly_defined', bool),
+                                    ('value', Any)])
 
 
 def get_object_members(subject: Any, objpath: List[str], attrgetter: Callable,
@@ -234,4 +236,4 @@ deprecated_alias('sphinx.ext.autodoc.importer',
                      'MockFinder': 'sphinx.ext.autodoc.mock.MockFinder',
                      'MockLoader': 'sphinx.ext.autodoc.mock.MockLoader',
                      'mock': 'sphinx.ext.autodoc.mock.mock',
-                 })
+                 })
\ No newline at end of file
diff --git a/tox.ini b/tox.ini
index bddd822a6..34baee205 100644
--- a/tox.ini
+++ b/tox.ini
@@ -27,7 +27,7 @@ extras =
 setenv =
     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils
 commands=
-    pytest --durations 25 {posargs}
+    pytest -rA --durations 25 {posargs}
 
 [testenv:flake8]
 basepython = python3
+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z x ']'
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1='(testbed) '
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=3
+++ CONDA_SHLVL=3
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=4
++ CONDA_SHLVL=4
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_3=/opt/miniconda3
++ CONDA_PREFIX_3=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ python -m pip install -e '.[test]'
Obtaining file:///testbed
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: sphinxcontrib-applehelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (1.0.4)
Requirement already satisfied: sphinxcontrib-devhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (1.0.2)
Requirement already satisfied: sphinxcontrib-jsmath in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (1.0.1)
Requirement already satisfied: sphinxcontrib-htmlhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (2.0.1)
Requirement already satisfied: sphinxcontrib-serializinghtml in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (1.1.5)
Requirement already satisfied: sphinxcontrib-qthelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (1.0.3)
Requirement already satisfied: Jinja2>=2.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (2.11.3)
Requirement already satisfied: Pygments>=2.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (2.19.1)
Requirement already satisfied: docutils>=0.12 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (0.21.2)
Requirement already satisfied: snowballstemmer>=1.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (2.2.0)
Requirement already satisfied: babel>=1.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (2.17.0)
Requirement already satisfied: alabaster<0.8,>=0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (0.7.11)
Requirement already satisfied: imagesize in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (1.4.1)
Requirement already satisfied: requests>=2.5.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (2.32.3)
Requirement already satisfied: setuptools in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (75.8.0)
Requirement already satisfied: packaging in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (24.2)
Requirement already satisfied: pytest in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (8.3.4)
Requirement already satisfied: pytest-cov in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (6.0.0)
Requirement already satisfied: html5lib in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (1.1)
Requirement already satisfied: typed_ast in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (1.5.5)
Requirement already satisfied: cython in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.2.0.dev20250315) (3.0.11)
Requirement already satisfied: MarkupSafe>=0.23 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Jinja2>=2.3->Sphinx==3.2.0.dev20250315) (2.0.1)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.2.0.dev20250315) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.2.0.dev20250315) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.2.0.dev20250315) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.2.0.dev20250315) (2025.1.31)
Requirement already satisfied: six>=1.9 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.2.0.dev20250315) (1.17.0)
Requirement already satisfied: webencodings in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.2.0.dev20250315) (0.5.1)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.2.0.dev20250315) (1.2.2)
Requirement already satisfied: iniconfig in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.2.0.dev20250315) (2.0.0)
Requirement already satisfied: pluggy<2,>=1.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.2.0.dev20250315) (1.5.0)
Requirement already satisfied: tomli>=1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.2.0.dev20250315) (2.2.1)
Requirement already satisfied: coverage>=7.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from coverage[toml]>=7.5->pytest-cov->Sphinx==3.2.0.dev20250315) (7.6.10)
Installing collected packages: Sphinx
  Attempting uninstall: Sphinx
    Found existing installation: Sphinx 3.2.0.dev20250204
    Uninstalling Sphinx-3.2.0.dev20250204:
      Successfully uninstalled Sphinx-3.2.0.dev20250204
  DEPRECATION: Legacy editable install of Sphinx[test]==3.2.0.dev20250315 from file:///testbed (setup.py develop) is deprecated. pip 25.1 will enforce this behaviour change. A possible replacement is to add a pyproject.toml or enable --use-pep517, and use setuptools >= 64. If the resulting installation is not behaving as expected, try using --config-settings editable_mode=compat. Please consult the setuptools documentation for more information. Discussion can be found at https://github.com/pypa/pip/issues/11457
  Running setup.py develop for Sphinx
Successfully installed Sphinx
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
+ git checkout 5e6da19f0e44a0ae83944fb6ce18f18f781e1a6e tests/test_ext_autodoc_private_members.py
Updated 0 paths from f214334fe
+ git apply -v -
Checking patch tests/test_ext_autodoc_private_members.py...
Applied patch tests/test_ext_autodoc_private_members.py cleanly.
+ tox --current-env -epy39 -v -- tests/test_ext_autodoc_private_members.py
py39: commands[0]> pytest -rA --durations 25 tests/test_ext_autodoc_private_members.py
============================= test session starts ==============================
platform linux -- Python 3.9.21, pytest-8.3.4, pluggy-1.5.0
cachedir: .tox/py39/.pytest_cache
libraries: Sphinx-3.2.0, docutils-0.21.2
base tempdir: /tmp/pytest-of-root/pytest-0
rootdir: /testbed
configfile: setup.cfg
plugins: cov-6.0.0
collected 3 items

tests/test_ext_autodoc_private_members.py FFF                            [100%]

=================================== FAILURES ===================================
______________________________ test_private_field ______________________________

app = <SphinxTestApp buildername='html'>

    @pytest.mark.sphinx('html', testroot='ext-autodoc')
    def test_private_field(app):
        app.config.autoclass_content = 'class'
        options = {"members": None}
        actual = do_autodoc(app, 'module', 'target.private', options)
>       assert list(actual) == [
            '',
            '.. py:module:: target.private',
            '',
            '',
            '.. py:function:: _public_function(name)',
            '   :module: target.private',
            '',
            '   public_function is a docstring().',
            '',
            '   :meta public:',
            '',
        ]
E       AssertionError: assert ['', '.. py:m...private', ...] == ['', '.. py:m...private', ...]
E         
E         At index 9 diff: '' != '   :meta public:'
E         Left contains 5 more items, first extra item: '   :module: target.private'
E         Use -v to get more diff

tests/test_ext_autodoc_private_members.py:21: AssertionError
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v3.2.0[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

____________________ test_private_field_and_private_members ____________________

app = <SphinxTestApp buildername='html'>

    @pytest.mark.sphinx('html', testroot='ext-autodoc')
    def test_private_field_and_private_members(app):
        app.config.autoclass_content = 'class'
        options = {"members": None,
                   "private-members": None}
        actual = do_autodoc(app, 'module', 'target.private', options)
>       assert list(actual) == [
            '',
            '.. py:module:: target.private',
            '',
            '',
            '.. py:function:: _public_function(name)',
            '   :module: target.private',
            '',
            '   public_function is a docstring().',
            '',
            '   :meta public:',
            '',
            '',
            '.. py:function:: private_function(name)',
            '   :module: target.private',
            '',
            '   private_function is a docstring().',
            '',
            '   :meta private:',
            '',
        ]
E       AssertionError: assert ['', '.. py:m...private', ...] == ['', '.. py:m...private', ...]
E         
E         At index 9 diff: '' != '   :meta public:'
E         Right contains 3 more items, first extra item: ''
E         Use -v to get more diff

tests/test_ext_autodoc_private_members.py:42: AssertionError
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v3.2.0[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

_____________________________ test_private_members _____________________________

app = <SphinxTestApp buildername='html'>

    @pytest.mark.sphinx('html', testroot='ext-autodoc')
    def test_private_members(app):
        app.config.autoclass_content = 'class'
        options = {"members": None,
                   "private-members": "_public_function"}
        actual = do_autodoc(app, 'module', 'target.private', options)
>       assert list(actual) == [
            '',
            '.. py:module:: target.private',
            '',
            '',
            '.. py:function:: _public_function(name)',
            '   :module: target.private',
            '',
            '   public_function is a docstring().',
            '',
            '   :meta public:',
            '',
        ]
E       AssertionError: assert ['', '.. py:m...private', ...] == ['', '.. py:m...private', ...]
E         
E         At index 9 diff: '' != '   :meta public:'
E         Left contains 5 more items, first extra item: '   :module: target.private'
E         Use -v to get more diff

tests/test_ext_autodoc_private_members.py:71: AssertionError
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v3.2.0[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

=============================== warnings summary ===============================
sphinx/util/docutils.py:45
  /testbed/sphinx/util/docutils.py:45: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
    __version_info__ = tuple(LooseVersion(docutils.__version__).version)

sphinx/registry.py:22
  /testbed/sphinx/registry.py:22: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
    from pkg_resources import iter_entry_points

../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('sphinxcontrib')`.
  Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
    declare_namespace(pkg)

sphinx/directives/patches.py:15
  /testbed/sphinx/directives/patches.py:15: DeprecationWarning: The `docutils.parsers.rst.directive.html` module will be removed in Docutils 2.0. Since Docutils 0.18, the "Meta" node is defined in `docutils.parsers.rst.directives.misc`.
    from docutils.parsers.rst.directives import images, html, tables

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
============================= slowest 25 durations =============================
0.26s setup    tests/test_ext_autodoc_private_members.py::test_private_field
0.01s setup    tests/test_ext_autodoc_private_members.py::test_private_field_and_private_members
0.01s setup    tests/test_ext_autodoc_private_members.py::test_private_members

(6 durations < 0.005s hidden.  Use -vv to show these durations.)
=========================== short test summary info ============================
FAILED tests/test_ext_autodoc_private_members.py::test_private_field - Assert...
FAILED tests/test_ext_autodoc_private_members.py::test_private_field_and_private_members
FAILED tests/test_ext_autodoc_private_members.py::test_private_members - Asse...
======================== 3 failed, 7 warnings in 0.46s =========================
py39: exit 1 (0.96 seconds) /testbed> pytest -rA --durations 25 tests/test_ext_autodoc_private_members.py pid=105
  py39: FAIL code 1 (0.97=setup[0.01]+cmd[0.96] seconds)
  evaluation failed :( (1.06 seconds)
+ git checkout 5e6da19f0e44a0ae83944fb6ce18f18f781e1a6e tests/test_ext_autodoc_private_members.py
Updated 1 path from f214334fe
