{"instance_id": "django__django-12262", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/django/template/library.py b/django/template/library.py\nindex 20bc86dac8..b6ef802fee 100644\n--- a/django/template/library.py\n+++ b/django/template/library.py\n@@ -255,13 +255,16 @@ def parse_bits(parser, bits, params, varargs, varkw, defaults,\n         kwarg for kwarg in kwonly\n         if not kwonly_defaults or kwarg not in kwonly_defaults\n     ]\n+\n+    valid_param_set = set(params + list(kwonly))\n+    \n     for bit in bits:\n         # First we try to extract a potential kwarg from the bit\n         kwarg = token_kwargs([bit], parser)\n         if kwarg:\n             # The kwarg was successfully extracted\n             param, value = kwarg.popitem()\n-            if param not in params and param not in unhandled_kwargs and varkw is None:\n+            if param not in valid_param_set and varkw is None:\n                 # An unexpected keyword argument was supplied\n                 raise TemplateSyntaxError(\n                     \"'%s' received unexpected keyword argument '%s'\" %\n@@ -325,4 +328,4 @@ def import_library(name):\n     except AttributeError:\n         raise InvalidTemplateLibrary(\n             \"Module  %s does not have a variable named 'register'\" % name,\n-        )\n+        )\n\\ No newline at end of file\ndiff --git a/tests/template_tests/templatetags/kwonly_tags.py b/tests/template_tests/templatetags/kwonly_tags.py\nnew file mode 100644\nindex 0000000000..9b925f5fcd\n--- /dev/null\n+++ b/tests/template_tests/templatetags/kwonly_tags.py\n@@ -0,0 +1,13 @@\n+from django.template import Library\n+\n+register = Library()\n+\n+\n+@register.simple_tag\n+def hello(*, greeting='hello'):\n+    return f'{greeting} world'\n+\n+\n+@register.simple_tag\n+def hi(*, greeting):\n+    return f'{greeting} world'\n\\ No newline at end of file\ndiff --git a/tests/template_tests/test_kwonly_tags.py b/tests/template_tests/test_kwonly_tags.py\nnew file mode 100644\nindex 0000000000..595fc79825\n--- /dev/null\n+++ b/tests/template_tests/test_kwonly_tags.py\n@@ -0,0 +1,38 @@\n+from django.template import Context, Library, Template, TemplateSyntaxError\n+from django.test import SimpleTestCase\n+\n+register = Library()\n+\n+\n+@register.simple_tag\n+def hello(*, greeting='hello'):\n+    return f'{greeting} world'\n+\n+\n+@register.simple_tag\n+def hi(*, greeting):\n+    return f'{greeting} world'\n+\n+\n+class KwOnlyTagsTests(SimpleTestCase):\n+    def test_kwonly_with_default(self):\n+        \"\"\"Tests that keyword-only arguments with defaults work properly.\"\"\"\n+        t = Template('{% load kwonly_tags %}{% hello greeting=\"hi\" %}')\n+        output = t.render(Context({}))\n+        self.assertEqual(output, 'hi world')\n+\n+    def test_kwonly_with_default_omitted(self):\n+        \"\"\"Tests that keyword-only arguments with defaults can be omitted.\"\"\"\n+        t = Template('{% load kwonly_tags %}{% hello %}')\n+        output = t.render(Context({}))\n+        self.assertEqual(output, 'hello world')\n+\n+    def test_kwonly_without_default(self):\n+        \"\"\"Tests that required keyword-only arguments must be provided.\"\"\"\n+        with self.assertRaisesMessage(TemplateSyntaxError, \"'hi' did not receive value(s) for the argument(s): 'greeting'\"):\n+            Template('{% load kwonly_tags %}{% hi %}')\n+\n+    def test_kwonly_duplicate_arg(self):\n+        \"\"\"Tests that providing duplicate keyword arguments raises the correct error.\"\"\"\n+        with self.assertRaisesMessage(TemplateSyntaxError, \"'hi' received multiple values for keyword argument 'greeting'\"):\n+            Template('{% load kwonly_tags %}{% hi greeting=\"hi\" greeting=\"hello\" %}')\n\\ No newline at end of file\n", "proposed_model_patches": []}