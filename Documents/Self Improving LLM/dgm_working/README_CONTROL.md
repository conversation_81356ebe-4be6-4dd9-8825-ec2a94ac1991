# Darwin Gödel Machine Control System

Simple start/stop/monitor system for the Darwin Gödel Machine with progress persistence.

## 🚀 Quick Start

### Start DGM Evolution
```bash
./dgm start
# or
python dgm_control.py start
```

### Stop DGM Evolution
```bash
./dgm stop
# or
python dgm_control.py stop
```

### Check Status
```bash
./dgm status
# or
python dgm_control.py status
```

### Live Monitoring
```bash
./dgm monitor
# or
python dgm_control.py monitor
```

## 📊 Features

### ✅ **Automatic Resume**
- Automatically resumes from where it left off when restarted
- Preserves all progress across computer restarts
- No manual intervention needed

### ✅ **Performance Optimizations**
- Docker image reuse (no rebuilding every time)
- Patch-based evaluation (faster than full SWE-bench)
- Meaningful fitness scores for evolution

### ✅ **Simple Control**
- One command to start/stop
- Live monitoring with auto-refresh
- Clear status reporting

### ✅ **Progress Persistence**
- Saves state automatically
- Tracks generations, patches, scores
- Resumes from exact point of interruption

## 📈 Example Usage

```bash
# Start evolution for 50 generations
./dgm start

# Monitor progress in real-time
./dgm monitor

# Stop when needed (e.g., computer shutdown)
./dgm stop

# Later, resume from where it left off
./dgm start

# Check current status anytime
./dgm status
```

## 🔧 Configuration

The system automatically:
- Uses your local qwen3:30b-a3b model
- Runs for 50 generations by default
- Uses 2 parallel self-improvement attempts
- Saves progress to `dgm_state.json`
- Logs activity to `dgm_control.log`

## 📊 Monitoring Output

The status shows:
- **Process Status**: Running/Stopped
- **Current Generation**: X/50 progress
- **Patches Generated**: Non-empty patch count
- **Evaluation Scores**: Fitness metrics
- **Latest Activity**: Recent log entries

## 🎯 Benefits

1. **No Docker Rebuilds**: Images are reused for speed
2. **Meaningful Evaluation**: Patch-based scoring provides fitness feedback
3. **Persistent Progress**: Never lose evolution progress
4. **Simple Interface**: Just start/stop/monitor commands
5. **Autonomous Operation**: Runs continuously without intervention

## 🛠️ Troubleshooting

If DGM won't start:
```bash
# Check if already running
./dgm status

# Force stop any existing processes
pkill -f DGM_outer.py

# Try starting again
./dgm start
```

If monitoring shows errors:
```bash
# Check detailed logs
tail -f dgm_control.log

# Check DGM logs
find output_dgm -name "*.log" -exec tail {} \;
```

## 🎉 Success Indicators

Look for:
- ✅ **Non-empty patches** (>0 lines)
- ✅ **Increasing scores** (>0.0 fitness)
- ✅ **Generation progress** (advancing through 50)
- ✅ **Continuous activity** (recent log entries)

The Darwin Gödel Machine will continuously evolve and improve itself!
