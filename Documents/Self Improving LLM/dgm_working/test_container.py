#!/usr/bin/env python3
"""
Test Docker container setup to debug the Git repository issue.
"""

import docker
import os
from utils.docker_utils import build_dgm_container, remove_existing_container

def test_container():
    """Test container creation and Git setup."""
    print("🧪 Testing Docker container setup...")
    
    # Create Docker client
    client = docker.from_env()
    
    # Container settings
    image_name = "dgm"
    container_name = "dgm-test"
    root_dir = "."
    
    # Remove existing container
    remove_existing_container(client, container_name)
    
    # Build container
    print("🏗️ Building container...")
    container = build_dgm_container(
        client, root_dir, image_name, container_name,
        force_rebuild=True,  # Force rebuild to test
    )
    
    if container is None:
        print("❌ Container build failed!")
        return
    
    print("✅ Container built successfully!")
    
    # Test Git commands
    print("🔍 Testing Git setup...")
    
    try:
        # Check if Git is available
        result = container.exec_run("which git", workdir='/dgm')
        print(f"Git location: {result.output.decode().strip()}")
        
        # Check if .git directory exists
        result = container.exec_run("ls -la", workdir='/dgm')
        print("Directory contents:")
        print(result.output.decode())
        
        # Test Git status
        result = container.exec_run("git status", workdir='/dgm')
        print(f"Git status (exit code {result.exit_code}):")
        print(result.output.decode())
        
        if result.exit_code == 0:
            print("✅ Git repository is working!")
        else:
            print("❌ Git repository has issues")
            
            # Try to initialize Git
            print("🔧 Attempting to fix Git...")
            result = container.exec_run("git init", workdir='/dgm')
            print(f"Git init: {result.output.decode()}")
            
            result = container.exec_run("git config user.name 'DGM Test'", workdir='/dgm')
            result = container.exec_run("git config user.email '<EMAIL>'", workdir='/dgm')
            
            result = container.exec_run("git add .", workdir='/dgm')
            print(f"Git add: {result.output.decode()}")
            
            result = container.exec_run("git commit -m 'Initial commit'", workdir='/dgm')
            print(f"Git commit: {result.output.decode()}")
            
            # Test again
            result = container.exec_run("git status", workdir='/dgm')
            print(f"Git status after fix (exit code {result.exit_code}):")
            print(result.output.decode())
        
    except Exception as e:
        print(f"❌ Error testing Git: {e}")
    
    # Cleanup
    print("🧹 Cleaning up...")
    container.stop()
    container.remove()
    print("✅ Test complete!")

if __name__ == "__main__":
    test_container()
