#!/usr/bin/env python3
"""
Test script to verify that our modified dataset loading works.
"""

import json
import os

def test_dataset_loading():
    """Test the modified dataset loading logic."""
    print("Testing modified dataset loading...")
    
    try:
        # Test the same logic as in self_improve_step.py
        polyglot = False
        
        if polyglot:
            with open("polyglot/polyglot_benchmark_metadata.json") as f:
                dataset = json.loads(f.read())
        else:
            # Use local SWE-bench data instead of downloading from HuggingFace
            # Load the small subset for faster testing
            with open("swe_bench/subsets/small.json") as f:
                instance_ids = json.loads(f.read())
            
            # Create a minimal dataset structure with just the instance IDs
            # The actual problem data will be loaded from the SWE-bench repository
            dataset = [{"instance_id": instance_id} for instance_id in instance_ids]
        
        print(f"✓ Dataset loaded successfully!")
        print(f"Number of instances: {len(dataset)}")
        print(f"First few instances: {dataset[:3]}")
        return True
        
    except Exception as e:
        print(f"✗ Dataset loading failed: {e}")
        return False

def main():
    """Run the test."""
    print("=== Dataset Loading Test ===\n")
    
    # We're already in the dgm directory
    
    if test_dataset_loading():
        print("\n✓ Dataset loading test passed!")
        return 0
    else:
        print("\n✗ Dataset loading test failed!")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
