{"sphinx-doc__sphinx-9367": {"patch_is_None": false, "patch_exists": true, "patch_successfully_applied": true, "resolved": true, "tests_status": {"FAIL_TO_PASS": {"success": ["tests/test_pycode_ast.py::test_unparse[(1,)-(1,)]"], "failure": []}, "PASS_TO_PASS": {"success": ["tests/test_pycode_ast.py::test_unparse[a", "tests/test_pycode_ast.py::test_unparse[os.path-os.path]", "tests/test_pycode_ast.py::test_unparse[1", "tests/test_pycode_ast.py::test_unparse[b'bytes'-b'bytes']", "tests/test_pycode_ast.py::test_unparse[object()-object()]", "tests/test_pycode_ast.py::test_unparse[1234-1234_0]", "tests/test_pycode_ast.py::test_unparse[{'key1':", "tests/test_pycode_ast.py::test_unparse[...-...]", "tests/test_pycode_ast.py::test_unparse[Tuple[int,", "tests/test_pycode_ast.py::test_unparse[~", "tests/test_pycode_ast.py::test_unparse[lambda", "tests/test_pycode_ast.py::test_unparse[[1,", "tests/test_pycode_ast.py::test_unparse[sys-sys]", "tests/test_pycode_ast.py::test_unparse[1234-1234_1]", "tests/test_pycode_ast.py::test_unparse[not", "tests/test_pycode_ast.py::test_unparse[{1,", "tests/test_pycode_ast.py::test_unparse['str'-'str']", "tests/test_pycode_ast.py::test_unparse[+", "tests/test_pycode_ast.py::test_unparse[-", "tests/test_pycode_ast.py::test_unparse[(1,", "tests/test_pycode_ast.py::test_unparse[()-()]", "tests/test_pycode_ast.py::test_unparse_None", "tests/test_pycode_ast.py::test_unparse_py38[lambda", "tests/test_pycode_ast.py::test_unparse_py38[0x1234-0x1234]", "tests/test_pycode_ast.py::test_unparse_py38[1_000_000-1_000_000]"], "failure": []}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}}}