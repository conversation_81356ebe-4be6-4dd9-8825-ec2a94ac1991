{"instance_id": "django__django-15930", "model_name_or_path": "nerf_editwholefiles_0", "model_patch": "diff --git a/django/db/models/expressions.py b/django/db/models/expressions.py\nindex 822968ef56..05059f577e 100644\n--- a/django/db/models/expressions.py\n+++ b/django/db/models/expressions.py\n@@ -1299,6 +1299,9 @@ class When(Expression):\n         template_params = extra_context\n         sql_params = []\n         condition_sql, condition_params = compiler.compile(self.condition)\n+        # Handle the case of empty/negated empty IN clause\n+        if not condition_sql.strip():\n+            condition_sql = \"TRUE\"\n         template_params[\"condition\"] = condition_sql\n         sql_params.extend(condition_params)\n         result_sql, result_params = compiler.compile(self.result)\ndiff --git a/django/db/models/expressions.py.orig b/django/db/models/expressions.py.orig\nnew file mode 100644\nindex 0000000000..822968ef56\n--- /dev/null\n+++ b/django/db/models/expressions.py.orig\n@@ -0,0 +1,1803 @@\n+import copy\n+import datetime\n+import functools\n+import inspect\n+import warnings\n+from collections import defaultdict\n+from decimal import Decimal\n+from uuid import UUID\n+\n+from django.core.exceptions import EmptyResultSet, FieldError\n+from django.db import DatabaseError, NotSupportedError, connection\n+from django.db.models import fields\n+from django.db.models.constants import LOOKUP_SEP\n+from django.db.models.query_utils import Q\n+from django.utils.deconstruct import deconstructible\n+from django.utils.deprecation import RemovedInDjango50Warning\n+from django.utils.functional import cached_property\n+from django.utils.hashable import make_hashable\n+\n+\n+class SQLiteNumericMixin:\n+    \"\"\"\n+    Some expressions with output_field=DecimalField() must be cast to\n+    numeric to be properly filtered.\n+    \"\"\"\n+\n+    def as_sqlite(self, compiler, connection, **extra_context):\n+        sql, params = self.as_sql(compiler, connection, **extra_context)\n+        try:\n+            if self.output_field.get_internal_type() == \"DecimalField\":\n+                sql = \"CAST(%s AS NUMERIC)\" % sql\n+        except FieldError:\n+            pass\n+        return sql, params\n+\n+\n+class Combinable:\n+    \"\"\"\n+    Provide the ability to combine one or two objects with\n+    some connector. For example F('foo') + F('bar').\n+    \"\"\"\n+\n+    # Arithmetic connectors\n+    ADD = \"+\"\n+    SUB = \"-\"\n+    MUL = \"*\"\n+    DIV = \"/\"\n+    POW = \"^\"\n+    # The following is a quoted % operator - it is quoted because it can be\n+    # used in strings that also have parameter substitution.\n+    MOD = \"%%\"\n+\n+    # Bitwise operators - note that these are generated by .bitand()\n+    # and .bitor(), the '&' and '|' are reserved for boolean operator\n+    # usage.\n+    BITAND = \"&\"\n+    BITOR = \"|\"\n+    BITLEFTSHIFT = \"<<\"\n+    BITRIGHTSHIFT = \">>\"\n+    BITXOR = \"#\"\n+\n+    def _combine(self, other, connector, reversed):\n+        if not hasattr(other, \"resolve_expression\"):\n+            # everything must be resolvable to an expression\n+            other = Value(other)\n+\n+        if reversed:\n+            return CombinedExpression(other, connector, self)\n+        return CombinedExpression(self, connector, other)\n+\n+    #############\n+    # OPERATORS #\n+    #############\n+\n+    def __neg__(self):\n+        return self._combine(-1, self.MUL, False)\n+\n+    def __add__(self, other):\n+        return self._combine(other, self.ADD, False)\n+\n+    def __sub__(self, other):\n+        return self._combine(other, self.SUB, False)\n+\n+    def __mul__(self, other):\n+        return self._combine(other, self.MUL, False)\n+\n+    def __truediv__(self, other):\n+        return self._combine(other, self.DIV, False)\n+\n+    def __mod__(self, other):\n+        return self._combine(other, self.MOD, False)\n+\n+    def __pow__(self, other):\n+        return self._combine(other, self.POW, False)\n+\n+    def __and__(self, other):\n+        if getattr(self, \"conditional\", False) and getattr(other, \"conditional\", False):\n+            return Q(self) & Q(other)\n+        raise NotImplementedError(\n+            \"Use .bitand(), .bitor(), and .bitxor() for bitwise logical operations.\"\n+        )\n+\n+    def bitand(self, other):\n+        return self._combine(other, self.BITAND, False)\n+\n+    def bitleftshift(self, other):\n+        return self._combine(other, self.BITLEFTSHIFT, False)\n+\n+    def bitrightshift(self, other):\n+        return self._combine(other, self.BITRIGHTSHIFT, False)\n+\n+    def __xor__(self, other):\n+        if getattr(self, \"conditional\", False) and getattr(other, \"conditional\", False):\n+            return Q(self) ^ Q(other)\n+        raise NotImplementedError(\n+            \"Use .bitand(), .bitor(), and .bitxor() for bitwise logical operations.\"\n+        )\n+\n+    def bitxor(self, other):\n+        return self._combine(other, self.BITXOR, False)\n+\n+    def __or__(self, other):\n+        if getattr(self, \"conditional\", False) and getattr(other, \"conditional\", False):\n+            return Q(self) | Q(other)\n+        raise NotImplementedError(\n+            \"Use .bitand(), .bitor(), and .bitxor() for bitwise logical operations.\"\n+        )\n+\n+    def bitor(self, other):\n+        return self._combine(other, self.BITOR, False)\n+\n+    def __radd__(self, other):\n+        return self._combine(other, self.ADD, True)\n+\n+    def __rsub__(self, other):\n+        return self._combine(other, self.SUB, True)\n+\n+    def __rmul__(self, other):\n+        return self._combine(other, self.MUL, True)\n+\n+    def __rtruediv__(self, other):\n+        return self._combine(other, self.DIV, True)\n+\n+    def __rmod__(self, other):\n+        return self._combine(other, self.MOD, True)\n+\n+    def __rpow__(self, other):\n+        return self._combine(other, self.POW, True)\n+\n+    def __rand__(self, other):\n+        raise NotImplementedError(\n+            \"Use .bitand(), .bitor(), and .bitxor() for bitwise logical operations.\"\n+        )\n+\n+    def __ror__(self, other):\n+        raise NotImplementedError(\n+            \"Use .bitand(), .bitor(), and .bitxor() for bitwise logical operations.\"\n+        )\n+\n+    def __rxor__(self, other):\n+        raise NotImplementedError(\n+            \"Use .bitand(), .bitor(), and .bitxor() for bitwise logical operations.\"\n+        )\n+\n+\n+class BaseExpression:\n+    \"\"\"Base class for all query expressions.\"\"\"\n+\n+    empty_result_set_value = NotImplemented\n+    # aggregate specific fields\n+    is_summary = False\n+    _output_field_resolved_to_none = False\n+    # Can the expression be used in a WHERE clause?\n+    filterable = True\n+    # Can the expression can be used as a source expression in Window?\n+    window_compatible = False\n+\n+    def __init__(self, output_field=None):\n+        if output_field is not None:\n+            self.output_field = output_field\n+\n+    def __getstate__(self):\n+        state = self.__dict__.copy()\n+        state.pop(\"convert_value\", None)\n+        return state\n+\n+    def get_db_converters(self, connection):\n+        return (\n+            []\n+            if self.convert_value is self._convert_value_noop\n+            else [self.convert_value]\n+        ) + self.output_field.get_db_converters(connection)\n+\n+    def get_source_expressions(self):\n+        return []\n+\n+    def set_source_expressions(self, exprs):\n+        assert not exprs\n+\n+    def _parse_expressions(self, *expressions):\n+        return [\n+            arg\n+            if hasattr(arg, \"resolve_expression\")\n+            else (F(arg) if isinstance(arg, str) else Value(arg))\n+            for arg in expressions\n+        ]\n+\n+    def as_sql(self, compiler, connection):\n+        \"\"\"\n+        Responsible for returning a (sql, [params]) tuple to be included\n+        in the current query.\n+\n+        Different backends can provide their own implementation, by\n+        providing an `as_{vendor}` method and patching the Expression:\n+\n+        ```\n+        def override_as_sql(self, compiler, connection):\n+            # custom logic\n+            return super().as_sql(compiler, connection)\n+        setattr(Expression, 'as_' + connection.vendor, override_as_sql)\n+        ```\n+\n+        Arguments:\n+         * compiler: the query compiler responsible for generating the query.\n+           Must have a compile method, returning a (sql, [params]) tuple.\n+           Calling compiler(value) will return a quoted `value`.\n+\n+         * connection: the database connection used for the current query.\n+\n+        Return: (sql, params)\n+          Where `sql` is a string containing ordered sql parameters to be\n+          replaced with the elements of the list `params`.\n+        \"\"\"\n+        raise NotImplementedError(\"Subclasses must implement as_sql()\")\n+\n+    @cached_property\n+    def contains_aggregate(self):\n+        return any(\n+            expr and expr.contains_aggregate for expr in self.get_source_expressions()\n+        )\n+\n+    @cached_property\n+    def contains_over_clause(self):\n+        return any(\n+            expr and expr.contains_over_clause for expr in self.get_source_expressions()\n+        )\n+\n+    @cached_property\n+    def contains_column_references(self):\n+        return any(\n+            expr and expr.contains_column_references\n+            for expr in self.get_source_expressions()\n+        )\n+\n+    def resolve_expression(\n+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False\n+    ):\n+        \"\"\"\n+        Provide the chance to do any preprocessing or validation before being\n+        added to the query.\n+\n+        Arguments:\n+         * query: the backend query implementation\n+         * allow_joins: boolean allowing or denying use of joins\n+           in this query\n+         * reuse: a set of reusable joins for multijoins\n+         * summarize: a terminal aggregate clause\n+         * for_save: whether this expression about to be used in a save or update\n+\n+        Return: an Expression to be added to the query.\n+        \"\"\"\n+        c = self.copy()\n+        c.is_summary = summarize\n+        c.set_source_expressions(\n+            [\n+                expr.resolve_expression(query, allow_joins, reuse, summarize)\n+                if expr\n+                else None\n+                for expr in c.get_source_expressions()\n+            ]\n+        )\n+        return c\n+\n+    @property\n+    def conditional(self):\n+        return isinstance(self.output_field, fields.BooleanField)\n+\n+    @property\n+    def field(self):\n+        return self.output_field\n+\n+    @cached_property\n+    def output_field(self):\n+        \"\"\"Return the output type of this expressions.\"\"\"\n+        output_field = self._resolve_output_field()\n+        if output_field is None:\n+            self._output_field_resolved_to_none = True\n+            raise FieldError(\"Cannot resolve expression type, unknown output_field\")\n+        return output_field\n+\n+    @cached_property\n+    def _output_field_or_none(self):\n+        \"\"\"\n+        Return the output field of this expression, or None if\n+        _resolve_output_field() didn't return an output type.\n+        \"\"\"\n+        try:\n+            return self.output_field\n+        except FieldError:\n+            if not self._output_field_resolved_to_none:\n+                raise\n+\n+    def _resolve_output_field(self):\n+        \"\"\"\n+        Attempt to infer the output type of the expression.\n+\n+        As a guess, if the output fields of all source fields match then simply\n+        infer the same type here.\n+\n+        If a source's output field resolves to None, exclude it from this check.\n+        If all sources are None, then an error is raised higher up the stack in\n+        the output_field property.\n+        \"\"\"\n+        # This guess is mostly a bad idea, but there is quite a lot of code\n+        # (especially 3rd party Func subclasses) that depend on it, we'd need a\n+        # deprecation path to fix it.\n+        sources_iter = (\n+            source for source in self.get_source_fields() if source is not None\n+        )\n+        for output_field in sources_iter:\n+            for source in sources_iter:\n+                if not isinstance(output_field, source.__class__):\n+                    raise FieldError(\n+                        \"Expression contains mixed types: %s, %s. You must \"\n+                        \"set output_field.\"\n+                        % (\n+                            output_field.__class__.__name__,\n+                            source.__class__.__name__,\n+                        )\n+                    )\n+            return output_field\n+\n+    @staticmethod\n+    def _convert_value_noop(value, expression, connection):\n+        return value\n+\n+    @cached_property\n+    def convert_value(self):\n+        \"\"\"\n+        Expressions provide their own converters because users have the option\n+        of manually specifying the output_field which may be a different type\n+        from the one the database returns.\n+        \"\"\"\n+        field = self.output_field\n+        internal_type = field.get_internal_type()\n+        if internal_type == \"FloatField\":\n+            return (\n+                lambda value, expression, connection: None\n+                if value is None\n+                else float(value)\n+            )\n+        elif internal_type.endswith(\"IntegerField\"):\n+            return (\n+                lambda value, expression, connection: None\n+                if value is None\n+                else int(value)\n+            )\n+        elif internal_type == \"DecimalField\":\n+            return (\n+                lambda value, expression, connection: None\n+                if value is None\n+                else Decimal(value)\n+            )\n+        return self._convert_value_noop\n+\n+    def get_lookup(self, lookup):\n+        return self.output_field.get_lookup(lookup)\n+\n+    def get_transform(self, name):\n+        return self.output_field.get_transform(name)\n+\n+    def relabeled_clone(self, change_map):\n+        clone = self.copy()\n+        clone.set_source_expressions(\n+            [\n+                e.relabeled_clone(change_map) if e is not None else None\n+                for e in self.get_source_expressions()\n+            ]\n+        )\n+        return clone\n+\n+    def replace_references(self, references_map):\n+        clone = self.copy()\n+        clone.set_source_expressions(\n+            [\n+                expr.replace_references(references_map)\n+                for expr in self.get_source_expressions()\n+            ]\n+        )\n+        return clone\n+\n+    def copy(self):\n+        return copy.copy(self)\n+\n+    def prefix_references(self, prefix):\n+        clone = self.copy()\n+        clone.set_source_expressions(\n+            [\n+                F(f\"{prefix}{expr.name}\")\n+                if isinstance(expr, F)\n+                else expr.prefix_references(prefix)\n+                for expr in self.get_source_expressions()\n+            ]\n+        )\n+        return clone\n+\n+    def get_group_by_cols(self, alias=None):\n+        if not self.contains_aggregate:\n+            return [self]\n+        cols = []\n+        for source in self.get_source_expressions():\n+            cols.extend(source.get_group_by_cols())\n+        return cols\n+\n+    def get_source_fields(self):\n+        \"\"\"Return the underlying field types used by this aggregate.\"\"\"\n+        return [e._output_field_or_none for e in self.get_source_expressions()]\n+\n+    def asc(self, **kwargs):\n+        return OrderBy(self, **kwargs)\n+\n+    def desc(self, **kwargs):\n+        return OrderBy(self, descending=True, **kwargs)\n+\n+    def reverse_ordering(self):\n+        return self\n+\n+    def flatten(self):\n+        \"\"\"\n+        Recursively yield this expression and all subexpressions, in\n+        depth-first order.\n+        \"\"\"\n+        yield self\n+        for expr in self.get_source_expressions():\n+            if expr:\n+                if hasattr(expr, \"flatten\"):\n+                    yield from expr.flatten()\n+                else:\n+                    yield expr\n+\n+    def select_format(self, compiler, sql, params):\n+        \"\"\"\n+        Custom format for select clauses. For example, EXISTS expressions need\n+        to be wrapped in CASE WHEN on Oracle.\n+        \"\"\"\n+        if hasattr(self.output_field, \"select_format\"):\n+            return self.output_field.select_format(compiler, sql, params)\n+        return sql, params\n+\n+\n+@deconstructible\n+class Expression(BaseExpression, Combinable):\n+    \"\"\"An expression that can be combined with other expressions.\"\"\"\n+\n+    @cached_property\n+    def identity(self):\n+        constructor_signature = inspect.signature(self.__init__)\n+        args, kwargs = self._constructor_args\n+        signature = constructor_signature.bind_partial(*args, **kwargs)\n+        signature.apply_defaults()\n+        arguments = signature.arguments.items()\n+        identity = [self.__class__]\n+        for arg, value in arguments:\n+            if isinstance(value, fields.Field):\n+                if value.name and value.model:\n+                    value = (value.model._meta.label, value.name)\n+                else:\n+                    value = type(value)\n+            else:\n+                value = make_hashable(value)\n+            identity.append((arg, value))\n+        return tuple(identity)\n+\n+    def __eq__(self, other):\n+        if not isinstance(other, Expression):\n+            return NotImplemented\n+        return other.identity == self.identity\n+\n+    def __hash__(self):\n+        return hash(self.identity)\n+\n+\n+# Type inference for CombinedExpression.output_field.\n+# Missing items will result in FieldError, by design.\n+#\n+# The current approach for NULL is based on lowest common denominator behavior\n+# i.e. if one of the supported databases is raising an error (rather than\n+# return NULL) for `val <op> NULL`, then Django raises FieldError.\n+NoneType = type(None)\n+\n+_connector_combinations = [\n+    # Numeric operations - operands of same type.\n+    {\n+        connector: [\n+            (fields.IntegerField, fields.IntegerField, fields.IntegerField),\n+            (fields.FloatField, fields.FloatField, fields.FloatField),\n+            (fields.DecimalField, fields.DecimalField, fields.DecimalField),\n+        ]\n+        for connector in (\n+            Combinable.ADD,\n+            Combinable.SUB,\n+            Combinable.MUL,\n+            # Behavior for DIV with integer arguments follows Postgres/SQLite,\n+            # not MySQL/Oracle.\n+            Combinable.DIV,\n+            Combinable.MOD,\n+            Combinable.POW,\n+        )\n+    },\n+    # Numeric operations - operands of different type.\n+    {\n+        connector: [\n+            (fields.IntegerField, fields.DecimalField, fields.DecimalField),\n+            (fields.DecimalField, fields.IntegerField, fields.DecimalField),\n+            (fields.IntegerField, fields.FloatField, fields.FloatField),\n+            (fields.FloatField, fields.IntegerField, fields.FloatField),\n+        ]\n+        for connector in (\n+            Combinable.ADD,\n+            Combinable.SUB,\n+            Combinable.MUL,\n+            Combinable.DIV,\n+        )\n+    },\n+    # Bitwise operators.\n+    {\n+        connector: [\n+            (fields.IntegerField, fields.IntegerField, fields.IntegerField),\n+        ]\n+        for connector in (\n+            Combinable.BITAND,\n+            Combinable.BITOR,\n+            Combinable.BITLEFTSHIFT,\n+            Combinable.BITRIGHTSHIFT,\n+            Combinable.BITXOR,\n+        )\n+    },\n+    # Numeric with NULL.\n+    {\n+        connector: [\n+            (field_type, NoneType, field_type),\n+            (NoneType, field_type, field_type),\n+        ]\n+        for connector in (\n+            Combinable.ADD,\n+            Combinable.SUB,\n+            Combinable.MUL,\n+            Combinable.DIV,\n+            Combinable.MOD,\n+            Combinable.POW,\n+        )\n+        for field_type in (fields.IntegerField, fields.DecimalField, fields.FloatField)\n+    },\n+    # Date/DateTimeField/DurationField/TimeField.\n+    {\n+        Combinable.ADD: [\n+            # Date/DateTimeField.\n+            (fields.DateField, fields.DurationField, fields.DateTimeField),\n+            (fields.DateTimeField, fields.DurationField, fields.DateTimeField),\n+            (fields.DurationField, fields.DateField, fields.DateTimeField),\n+            (fields.DurationField, fields.DateTimeField, fields.DateTimeField),\n+            # DurationField.\n+            (fields.DurationField, fields.DurationField, fields.DurationField),\n+            # TimeField.\n+            (fields.TimeField, fields.DurationField, fields.TimeField),\n+            (fields.DurationField, fields.TimeField, fields.TimeField),\n+        ],\n+    },\n+    {\n+        Combinable.SUB: [\n+            # Date/DateTimeField.\n+            (fields.DateField, fields.DurationField, fields.DateTimeField),\n+            (fields.DateTimeField, fields.DurationField, fields.DateTimeField),\n+            (fields.DateField, fields.DateField, fields.DurationField),\n+            (fields.DateField, fields.DateTimeField, fields.DurationField),\n+            (fields.DateTimeField, fields.DateField, fields.DurationField),\n+            (fields.DateTimeField, fields.DateTimeField, fields.DurationField),\n+            # DurationField.\n+            (fields.DurationField, fields.DurationField, fields.DurationField),\n+            # TimeField.\n+            (fields.TimeField, fields.DurationField, fields.TimeField),\n+            (fields.TimeField, fields.TimeField, fields.DurationField),\n+        ],\n+    },\n+]\n+\n+_connector_combinators = defaultdict(list)\n+\n+\n+def register_combinable_fields(lhs, connector, rhs, result):\n+    \"\"\"\n+    Register combinable types:\n+        lhs <connector> rhs -> result\n+    e.g.\n+        register_combinable_fields(\n+            IntegerField, Combinable.ADD, FloatField, FloatField\n+        )\n+    \"\"\"\n+    _connector_combinators[connector].append((lhs, rhs, result))\n+\n+\n+for d in _connector_combinations:\n+    for connector, field_types in d.items():\n+        for lhs, rhs, result in field_types:\n+            register_combinable_fields(lhs, connector, rhs, result)\n+\n+\n+@functools.lru_cache(maxsize=128)\n+def _resolve_combined_type(connector, lhs_type, rhs_type):\n+    combinators = _connector_combinators.get(connector, ())\n+    for combinator_lhs_type, combinator_rhs_type, combined_type in combinators:\n+        if issubclass(lhs_type, combinator_lhs_type) and issubclass(\n+            rhs_type, combinator_rhs_type\n+        ):\n+            return combined_type\n+\n+\n+class CombinedExpression(SQLiteNumericMixin, Expression):\n+    def __init__(self, lhs, connector, rhs, output_field=None):\n+        super().__init__(output_field=output_field)\n+        self.connector = connector\n+        self.lhs = lhs\n+        self.rhs = rhs\n+\n+    def __repr__(self):\n+        return \"<{}: {}>\".format(self.__class__.__name__, self)\n+\n+    def __str__(self):\n+        return \"{} {} {}\".format(self.lhs, self.connector, self.rhs)\n+\n+    def get_source_expressions(self):\n+        return [self.lhs, self.rhs]\n+\n+    def set_source_expressions(self, exprs):\n+        self.lhs, self.rhs = exprs\n+\n+    def _resolve_output_field(self):\n+        # We avoid using super() here for reasons given in\n+        # Expression._resolve_output_field()\n+        combined_type = _resolve_combined_type(\n+            self.connector,\n+            type(self.lhs._output_field_or_none),\n+            type(self.rhs._output_field_or_none),\n+        )\n+        if combined_type is None:\n+            raise FieldError(\n+                f\"Cannot infer type of {self.connector!r} expression involving these \"\n+                f\"types: {self.lhs.output_field.__class__.__name__}, \"\n+                f\"{self.rhs.output_field.__class__.__name__}. You must set \"\n+                f\"output_field.\"\n+            )\n+        return combined_type()\n+\n+    def as_sql(self, compiler, connection):\n+        expressions = []\n+        expression_params = []\n+        sql, params = compiler.compile(self.lhs)\n+        expressions.append(sql)\n+        expression_params.extend(params)\n+        sql, params = compiler.compile(self.rhs)\n+        expressions.append(sql)\n+        expression_params.extend(params)\n+        # order of precedence\n+        expression_wrapper = \"(%s)\"\n+        sql = connection.ops.combine_expression(self.connector, expressions)\n+        return expression_wrapper % sql, expression_params\n+\n+    def resolve_expression(\n+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False\n+    ):\n+        lhs = self.lhs.resolve_expression(\n+            query, allow_joins, reuse, summarize, for_save\n+        )\n+        rhs = self.rhs.resolve_expression(\n+            query, allow_joins, reuse, summarize, for_save\n+        )\n+        if not isinstance(self, (DurationExpression, TemporalSubtraction)):\n+            try:\n+                lhs_type = lhs.output_field.get_internal_type()\n+            except (AttributeError, FieldError):\n+                lhs_type = None\n+            try:\n+                rhs_type = rhs.output_field.get_internal_type()\n+            except (AttributeError, FieldError):\n+                rhs_type = None\n+            if \"DurationField\" in {lhs_type, rhs_type} and lhs_type != rhs_type:\n+                return DurationExpression(\n+                    self.lhs, self.connector, self.rhs\n+                ).resolve_expression(\n+                    query,\n+                    allow_joins,\n+                    reuse,\n+                    summarize,\n+                    for_save,\n+                )\n+            datetime_fields = {\"DateField\", \"DateTimeField\", \"TimeField\"}\n+            if (\n+                self.connector == self.SUB\n+                and lhs_type in datetime_fields\n+                and lhs_type == rhs_type\n+            ):\n+                return TemporalSubtraction(self.lhs, self.rhs).resolve_expression(\n+                    query,\n+                    allow_joins,\n+                    reuse,\n+                    summarize,\n+                    for_save,\n+                )\n+        c = self.copy()\n+        c.is_summary = summarize\n+        c.lhs = lhs\n+        c.rhs = rhs\n+        return c\n+\n+\n+class DurationExpression(CombinedExpression):\n+    def compile(self, side, compiler, connection):\n+        try:\n+            output = side.output_field\n+        except FieldError:\n+            pass\n+        else:\n+            if output.get_internal_type() == \"DurationField\":\n+                sql, params = compiler.compile(side)\n+                return connection.ops.format_for_duration_arithmetic(sql), params\n+        return compiler.compile(side)\n+\n+    def as_sql(self, compiler, connection):\n+        if connection.features.has_native_duration_field:\n+            return super().as_sql(compiler, connection)\n+        connection.ops.check_expression_support(self)\n+        expressions = []\n+        expression_params = []\n+        sql, params = self.compile(self.lhs, compiler, connection)\n+        expressions.append(sql)\n+        expression_params.extend(params)\n+        sql, params = self.compile(self.rhs, compiler, connection)\n+        expressions.append(sql)\n+        expression_params.extend(params)\n+        # order of precedence\n+        expression_wrapper = \"(%s)\"\n+        sql = connection.ops.combine_duration_expression(self.connector, expressions)\n+        return expression_wrapper % sql, expression_params\n+\n+    def as_sqlite(self, compiler, connection, **extra_context):\n+        sql, params = self.as_sql(compiler, connection, **extra_context)\n+        if self.connector in {Combinable.MUL, Combinable.DIV}:\n+            try:\n+                lhs_type = self.lhs.output_field.get_internal_type()\n+                rhs_type = self.rhs.output_field.get_internal_type()\n+            except (AttributeError, FieldError):\n+                pass\n+            else:\n+                allowed_fields = {\n+                    \"DecimalField\",\n+                    \"DurationField\",\n+                    \"FloatField\",\n+                    \"IntegerField\",\n+                }\n+                if lhs_type not in allowed_fields or rhs_type not in allowed_fields:\n+                    raise DatabaseError(\n+                        f\"Invalid arguments for operator {self.connector}.\"\n+                    )\n+        return sql, params\n+\n+\n+class TemporalSubtraction(CombinedExpression):\n+    output_field = fields.DurationField()\n+\n+    def __init__(self, lhs, rhs):\n+        super().__init__(lhs, self.SUB, rhs)\n+\n+    def as_sql(self, compiler, connection):\n+        connection.ops.check_expression_support(self)\n+        lhs = compiler.compile(self.lhs)\n+        rhs = compiler.compile(self.rhs)\n+        return connection.ops.subtract_temporals(\n+            self.lhs.output_field.get_internal_type(), lhs, rhs\n+        )\n+\n+\n+@deconstructible(path=\"django.db.models.F\")\n+class F(Combinable):\n+    \"\"\"An object capable of resolving references to existing query objects.\"\"\"\n+\n+    def __init__(self, name):\n+        \"\"\"\n+        Arguments:\n+         * name: the name of the field this expression references\n+        \"\"\"\n+        self.name = name\n+\n+    def __repr__(self):\n+        return \"{}({})\".format(self.__class__.__name__, self.name)\n+\n+    def resolve_expression(\n+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False\n+    ):\n+        return query.resolve_ref(self.name, allow_joins, reuse, summarize)\n+\n+    def replace_references(self, references_map):\n+        return references_map.get(self.name, self)\n+\n+    def asc(self, **kwargs):\n+        return OrderBy(self, **kwargs)\n+\n+    def desc(self, **kwargs):\n+        return OrderBy(self, descending=True, **kwargs)\n+\n+    def __eq__(self, other):\n+        return self.__class__ == other.__class__ and self.name == other.name\n+\n+    def __hash__(self):\n+        return hash(self.name)\n+\n+\n+class ResolvedOuterRef(F):\n+    \"\"\"\n+    An object that contains a reference to an outer query.\n+\n+    In this case, the reference to the outer query has been resolved because\n+    the inner query has been used as a subquery.\n+    \"\"\"\n+\n+    contains_aggregate = False\n+\n+    def as_sql(self, *args, **kwargs):\n+        raise ValueError(\n+            \"This queryset contains a reference to an outer query and may \"\n+            \"only be used in a subquery.\"\n+        )\n+\n+    def resolve_expression(self, *args, **kwargs):\n+        col = super().resolve_expression(*args, **kwargs)\n+        # FIXME: Rename possibly_multivalued to multivalued and fix detection\n+        # for non-multivalued JOINs (e.g. foreign key fields). This should take\n+        # into account only many-to-many and one-to-many relationships.\n+        col.possibly_multivalued = LOOKUP_SEP in self.name\n+        return col\n+\n+    def relabeled_clone(self, relabels):\n+        return self\n+\n+    def get_group_by_cols(self, alias=None):\n+        return []\n+\n+\n+class OuterRef(F):\n+    contains_aggregate = False\n+\n+    def resolve_expression(self, *args, **kwargs):\n+        if isinstance(self.name, self.__class__):\n+            return self.name\n+        return ResolvedOuterRef(self.name)\n+\n+    def relabeled_clone(self, relabels):\n+        return self\n+\n+\n+@deconstructible(path=\"django.db.models.Func\")\n+class Func(SQLiteNumericMixin, Expression):\n+    \"\"\"An SQL function call.\"\"\"\n+\n+    function = None\n+    template = \"%(function)s(%(expressions)s)\"\n+    arg_joiner = \", \"\n+    arity = None  # The number of arguments the function accepts.\n+\n+    def __init__(self, *expressions, output_field=None, **extra):\n+        if self.arity is not None and len(expressions) != self.arity:\n+            raise TypeError(\n+                \"'%s' takes exactly %s %s (%s given)\"\n+                % (\n+                    self.__class__.__name__,\n+                    self.arity,\n+                    \"argument\" if self.arity == 1 else \"arguments\",\n+                    len(expressions),\n+                )\n+            )\n+        super().__init__(output_field=output_field)\n+        self.source_expressions = self._parse_expressions(*expressions)\n+        self.extra = extra\n+\n+    def __repr__(self):\n+        args = self.arg_joiner.join(str(arg) for arg in self.source_expressions)\n+        extra = {**self.extra, **self._get_repr_options()}\n+        if extra:\n+            extra = \", \".join(\n+                str(key) + \"=\" + str(val) for key, val in sorted(extra.items())\n+            )\n+            return \"{}({}, {})\".format(self.__class__.__name__, args, extra)\n+        return \"{}({})\".format(self.__class__.__name__, args)\n+\n+    def _get_repr_options(self):\n+        \"\"\"Return a dict of extra __init__() options to include in the repr.\"\"\"\n+        return {}\n+\n+    def get_source_expressions(self):\n+        return self.source_expressions\n+\n+    def set_source_expressions(self, exprs):\n+        self.source_expressions = exprs\n+\n+    def resolve_expression(\n+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False\n+    ):\n+        c = self.copy()\n+        c.is_summary = summarize\n+        for pos, arg in enumerate(c.source_expressions):\n+            c.source_expressions[pos] = arg.resolve_expression(\n+                query, allow_joins, reuse, summarize, for_save\n+            )\n+        return c\n+\n+    def as_sql(\n+        self,\n+        compiler,\n+        connection,\n+        function=None,\n+        template=None,\n+        arg_joiner=None,\n+        **extra_context,\n+    ):\n+        connection.ops.check_expression_support(self)\n+        sql_parts = []\n+        params = []\n+        for arg in self.source_expressions:\n+            try:\n+                arg_sql, arg_params = compiler.compile(arg)\n+            except EmptyResultSet:\n+                empty_result_set_value = getattr(\n+                    arg, \"empty_result_set_value\", NotImplemented\n+                )\n+                if empty_result_set_value is NotImplemented:\n+                    raise\n+                arg_sql, arg_params = compiler.compile(Value(empty_result_set_value))\n+            sql_parts.append(arg_sql)\n+            params.extend(arg_params)\n+        data = {**self.extra, **extra_context}\n+        # Use the first supplied value in this order: the parameter to this\n+        # method, a value supplied in __init__()'s **extra (the value in\n+        # `data`), or the value defined on the class.\n+        if function is not None:\n+            data[\"function\"] = function\n+        else:\n+            data.setdefault(\"function\", self.function)\n+        template = template or data.get(\"template\", self.template)\n+        arg_joiner = arg_joiner or data.get(\"arg_joiner\", self.arg_joiner)\n+        data[\"expressions\"] = data[\"field\"] = arg_joiner.join(sql_parts)\n+        return template % data, params\n+\n+    def copy(self):\n+        copy = super().copy()\n+        copy.source_expressions = self.source_expressions[:]\n+        copy.extra = self.extra.copy()\n+        return copy\n+\n+\n+@deconstructible(path=\"django.db.models.Value\")\n+class Value(SQLiteNumericMixin, Expression):\n+    \"\"\"Represent a wrapped value as a node within an expression.\"\"\"\n+\n+    # Provide a default value for `for_save` in order to allow unresolved\n+    # instances to be compiled until a decision is taken in #25425.\n+    for_save = False\n+\n+    def __init__(self, value, output_field=None):\n+        \"\"\"\n+        Arguments:\n+         * value: the value this expression represents. The value will be\n+           added into the sql parameter list and properly quoted.\n+\n+         * output_field: an instance of the model field type that this\n+           expression will return, such as IntegerField() or CharField().\n+        \"\"\"\n+        super().__init__(output_field=output_field)\n+        self.value = value\n+\n+    def __repr__(self):\n+        return f\"{self.__class__.__name__}({self.value!r})\"\n+\n+    def as_sql(self, compiler, connection):\n+        connection.ops.check_expression_support(self)\n+        val = self.value\n+        output_field = self._output_field_or_none\n+        if output_field is not None:\n+            if self.for_save:\n+                val = output_field.get_db_prep_save(val, connection=connection)\n+            else:\n+                val = output_field.get_db_prep_value(val, connection=connection)\n+            if hasattr(output_field, \"get_placeholder\"):\n+                return output_field.get_placeholder(val, compiler, connection), [val]\n+        if val is None:\n+            # cx_Oracle does not always convert None to the appropriate\n+            # NULL type (like in case expressions using numbers), so we\n+            # use a literal SQL NULL\n+            return \"NULL\", []\n+        return \"%s\", [val]\n+\n+    def resolve_expression(\n+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False\n+    ):\n+        c = super().resolve_expression(query, allow_joins, reuse, summarize, for_save)\n+        c.for_save = for_save\n+        return c\n+\n+    def get_group_by_cols(self, alias=None):\n+        return []\n+\n+    def _resolve_output_field(self):\n+        if isinstance(self.value, str):\n+            return fields.CharField()\n+        if isinstance(self.value, bool):\n+            return fields.BooleanField()\n+        if isinstance(self.value, int):\n+            return fields.IntegerField()\n+        if isinstance(self.value, float):\n+            return fields.FloatField()\n+        if isinstance(self.value, datetime.datetime):\n+            return fields.DateTimeField()\n+        if isinstance(self.value, datetime.date):\n+            return fields.DateField()\n+        if isinstance(self.value, datetime.time):\n+            return fields.TimeField()\n+        if isinstance(self.value, datetime.timedelta):\n+            return fields.DurationField()\n+        if isinstance(self.value, Decimal):\n+            return fields.DecimalField()\n+        if isinstance(self.value, bytes):\n+            return fields.BinaryField()\n+        if isinstance(self.value, UUID):\n+            return fields.UUIDField()\n+\n+    @property\n+    def empty_result_set_value(self):\n+        return self.value\n+\n+\n+class RawSQL(Expression):\n+    def __init__(self, sql, params, output_field=None):\n+        if output_field is None:\n+            output_field = fields.Field()\n+        self.sql, self.params = sql, params\n+        super().__init__(output_field=output_field)\n+\n+    def __repr__(self):\n+        return \"{}({}, {})\".format(self.__class__.__name__, self.sql, self.params)\n+\n+    def as_sql(self, compiler, connection):\n+        return \"(%s)\" % self.sql, self.params\n+\n+    def get_group_by_cols(self, alias=None):\n+        return [self]\n+\n+    def resolve_expression(\n+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False\n+    ):\n+        # Resolve parents fields used in raw SQL.\n+        if query.model:\n+            for parent in query.model._meta.get_parent_list():\n+                for parent_field in parent._meta.local_fields:\n+                    _, column_name = parent_field.get_attname_column()\n+                    if column_name.lower() in self.sql.lower():\n+                        query.resolve_ref(\n+                            parent_field.name, allow_joins, reuse, summarize\n+                        )\n+                        break\n+        return super().resolve_expression(\n+            query, allow_joins, reuse, summarize, for_save\n+        )\n+\n+\n+class Star(Expression):\n+    def __repr__(self):\n+        return \"'*'\"\n+\n+    def as_sql(self, compiler, connection):\n+        return \"*\", []\n+\n+\n+class Col(Expression):\n+\n+    contains_column_references = True\n+    possibly_multivalued = False\n+\n+    def __init__(self, alias, target, output_field=None):\n+        if output_field is None:\n+            output_field = target\n+        super().__init__(output_field=output_field)\n+        self.alias, self.target = alias, target\n+\n+    def __repr__(self):\n+        alias, target = self.alias, self.target\n+        identifiers = (alias, str(target)) if alias else (str(target),)\n+        return \"{}({})\".format(self.__class__.__name__, \", \".join(identifiers))\n+\n+    def as_sql(self, compiler, connection):\n+        alias, column = self.alias, self.target.column\n+        identifiers = (alias, column) if alias else (column,)\n+        sql = \".\".join(map(compiler.quote_name_unless_alias, identifiers))\n+        return sql, []\n+\n+    def relabeled_clone(self, relabels):\n+        if self.alias is None:\n+            return self\n+        return self.__class__(\n+            relabels.get(self.alias, self.alias), self.target, self.output_field\n+        )\n+\n+    def get_group_by_cols(self, alias=None):\n+        return [self]\n+\n+    def get_db_converters(self, connection):\n+        if self.target == self.output_field:\n+            return self.output_field.get_db_converters(connection)\n+        return self.output_field.get_db_converters(\n+            connection\n+        ) + self.target.get_db_converters(connection)\n+\n+\n+class Ref(Expression):\n+    \"\"\"\n+    Reference to column alias of the query. For example, Ref('sum_cost') in\n+    qs.annotate(sum_cost=Sum('cost')) query.\n+    \"\"\"\n+\n+    def __init__(self, refs, source):\n+        super().__init__()\n+        self.refs, self.source = refs, source\n+\n+    def __repr__(self):\n+        return \"{}({}, {})\".format(self.__class__.__name__, self.refs, self.source)\n+\n+    def get_source_expressions(self):\n+        return [self.source]\n+\n+    def set_source_expressions(self, exprs):\n+        (self.source,) = exprs\n+\n+    def resolve_expression(\n+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False\n+    ):\n+        # The sub-expression `source` has already been resolved, as this is\n+        # just a reference to the name of `source`.\n+        return self\n+\n+    def relabeled_clone(self, relabels):\n+        return self\n+\n+    def as_sql(self, compiler, connection):\n+        return connection.ops.quote_name(self.refs), []\n+\n+    def get_group_by_cols(self, alias=None):\n+        return [self]\n+\n+\n+class ExpressionList(Func):\n+    \"\"\"\n+    An expression containing multiple expressions. Can be used to provide a\n+    list of expressions as an argument to another expression, like a partition\n+    clause.\n+    \"\"\"\n+\n+    template = \"%(expressions)s\"\n+\n+    def __init__(self, *expressions, **extra):\n+        if not expressions:\n+            raise ValueError(\n+                \"%s requires at least one expression.\" % self.__class__.__name__\n+            )\n+        super().__init__(*expressions, **extra)\n+\n+    def __str__(self):\n+        return self.arg_joiner.join(str(arg) for arg in self.source_expressions)\n+\n+    def as_sqlite(self, compiler, connection, **extra_context):\n+        # Casting to numeric is unnecessary.\n+        return self.as_sql(compiler, connection, **extra_context)\n+\n+\n+class OrderByList(Func):\n+    template = \"ORDER BY %(expressions)s\"\n+\n+    def __init__(self, *expressions, **extra):\n+        expressions = (\n+            (\n+                OrderBy(F(expr[1:]), descending=True)\n+                if isinstance(expr, str) and expr[0] == \"-\"\n+                else expr\n+            )\n+            for expr in expressions\n+        )\n+        super().__init__(*expressions, **extra)\n+\n+    def as_sql(self, *args, **kwargs):\n+        if not self.source_expressions:\n+            return \"\", ()\n+        return super().as_sql(*args, **kwargs)\n+\n+\n+@deconstructible(path=\"django.db.models.ExpressionWrapper\")\n+class ExpressionWrapper(SQLiteNumericMixin, Expression):\n+    \"\"\"\n+    An expression that can wrap another expression so that it can provide\n+    extra context to the inner expression, such as the output_field.\n+    \"\"\"\n+\n+    def __init__(self, expression, output_field):\n+        super().__init__(output_field=output_field)\n+        self.expression = expression\n+\n+    def set_source_expressions(self, exprs):\n+        self.expression = exprs[0]\n+\n+    def get_source_expressions(self):\n+        return [self.expression]\n+\n+    def get_group_by_cols(self, alias=None):\n+        if isinstance(self.expression, Expression):\n+            expression = self.expression.copy()\n+            expression.output_field = self.output_field\n+            return expression.get_group_by_cols(alias=alias)\n+        # For non-expressions e.g. an SQL WHERE clause, the entire\n+        # `expression` must be included in the GROUP BY clause.\n+        return super().get_group_by_cols()\n+\n+    def as_sql(self, compiler, connection):\n+        return compiler.compile(self.expression)\n+\n+    def __repr__(self):\n+        return \"{}({})\".format(self.__class__.__name__, self.expression)\n+\n+\n+@deconstructible(path=\"django.db.models.When\")\n+class When(Expression):\n+    template = \"WHEN %(condition)s THEN %(result)s\"\n+    # This isn't a complete conditional expression, must be used in Case().\n+    conditional = False\n+\n+    def __init__(self, condition=None, then=None, **lookups):\n+        if lookups:\n+            if condition is None:\n+                condition, lookups = Q(**lookups), None\n+            elif getattr(condition, \"conditional\", False):\n+                condition, lookups = Q(condition, **lookups), None\n+        if condition is None or not getattr(condition, \"conditional\", False) or lookups:\n+            raise TypeError(\n+                \"When() supports a Q object, a boolean expression, or lookups \"\n+                \"as a condition.\"\n+            )\n+        if isinstance(condition, Q) and not condition:\n+            raise ValueError(\"An empty Q() can't be used as a When() condition.\")\n+        super().__init__(output_field=None)\n+        self.condition = condition\n+        self.result = self._parse_expressions(then)[0]\n+\n+    def __str__(self):\n+        return \"WHEN %r THEN %r\" % (self.condition, self.result)\n+\n+    def __repr__(self):\n+        return \"<%s: %s>\" % (self.__class__.__name__, self)\n+\n+    def get_source_expressions(self):\n+        return [self.condition, self.result]\n+\n+    def set_source_expressions(self, exprs):\n+        self.condition, self.result = exprs\n+\n+    def get_source_fields(self):\n+        # We're only interested in the fields of the result expressions.\n+        return [self.result._output_field_or_none]\n+\n+    def resolve_expression(\n+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False\n+    ):\n+        c = self.copy()\n+        c.is_summary = summarize\n+        if hasattr(c.condition, \"resolve_expression\"):\n+            c.condition = c.condition.resolve_expression(\n+                query, allow_joins, reuse, summarize, False\n+            )\n+        c.result = c.result.resolve_expression(\n+            query, allow_joins, reuse, summarize, for_save\n+        )\n+        return c\n+\n+    def as_sql(self, compiler, connection, template=None, **extra_context):\n+        connection.ops.check_expression_support(self)\n+        template_params = extra_context\n+        sql_params = []\n+        condition_sql, condition_params = compiler.compile(self.condition)\n+        template_params[\"condition\"] = condition_sql\n+        sql_params.extend(condition_params)\n+        result_sql, result_params = compiler.compile(self.result)\n+        template_params[\"result\"] = result_sql\n+        sql_params.extend(result_params)\n+        template = template or self.template\n+        return template % template_params, sql_params\n+\n+    def get_group_by_cols(self, alias=None):\n+        # This is not a complete expression and cannot be used in GROUP BY.\n+        cols = []\n+        for source in self.get_source_expressions():\n+            cols.extend(source.get_group_by_cols())\n+        return cols\n+\n+\n+@deconstructible(path=\"django.db.models.Case\")\n+class Case(SQLiteNumericMixin, Expression):\n+    \"\"\"\n+    An SQL searched CASE expression:\n+\n+        CASE\n+            WHEN n > 0\n+                THEN 'positive'\n+            WHEN n < 0\n+                THEN 'negative'\n+            ELSE 'zero'\n+        END\n+    \"\"\"\n+\n+    template = \"CASE %(cases)s ELSE %(default)s END\"\n+    case_joiner = \" \"\n+\n+    def __init__(self, *cases, default=None, output_field=None, **extra):\n+        if not all(isinstance(case, When) for case in cases):\n+            raise TypeError(\"Positional arguments must all be When objects.\")\n+        super().__init__(output_field)\n+        self.cases = list(cases)\n+        self.default = self._parse_expressions(default)[0]\n+        self.extra = extra\n+\n+    def __str__(self):\n+        return \"CASE %s, ELSE %r\" % (\n+            \", \".join(str(c) for c in self.cases),\n+            self.default,\n+        )\n+\n+    def __repr__(self):\n+        return \"<%s: %s>\" % (self.__class__.__name__, self)\n+\n+    def get_source_expressions(self):\n+        return self.cases + [self.default]\n+\n+    def set_source_expressions(self, exprs):\n+        *self.cases, self.default = exprs\n+\n+    def resolve_expression(\n+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False\n+    ):\n+        c = self.copy()\n+        c.is_summary = summarize\n+        for pos, case in enumerate(c.cases):\n+            c.cases[pos] = case.resolve_expression(\n+                query, allow_joins, reuse, summarize, for_save\n+            )\n+        c.default = c.default.resolve_expression(\n+            query, allow_joins, reuse, summarize, for_save\n+        )\n+        return c\n+\n+    def copy(self):\n+        c = super().copy()\n+        c.cases = c.cases[:]\n+        return c\n+\n+    def as_sql(\n+        self, compiler, connection, template=None, case_joiner=None, **extra_context\n+    ):\n+        connection.ops.check_expression_support(self)\n+        if not self.cases:\n+            return compiler.compile(self.default)\n+        template_params = {**self.extra, **extra_context}\n+        case_parts = []\n+        sql_params = []\n+        for case in self.cases:\n+            try:\n+                case_sql, case_params = compiler.compile(case)\n+            except EmptyResultSet:\n+                continue\n+            case_parts.append(case_sql)\n+            sql_params.extend(case_params)\n+        default_sql, default_params = compiler.compile(self.default)\n+        if not case_parts:\n+            return default_sql, default_params\n+        case_joiner = case_joiner or self.case_joiner\n+        template_params[\"cases\"] = case_joiner.join(case_parts)\n+        template_params[\"default\"] = default_sql\n+        sql_params.extend(default_params)\n+        template = template or template_params.get(\"template\", self.template)\n+        sql = template % template_params\n+        if self._output_field_or_none is not None:\n+            sql = connection.ops.unification_cast_sql(self.output_field) % sql\n+        return sql, sql_params\n+\n+    def get_group_by_cols(self, alias=None):\n+        if not self.cases:\n+            return self.default.get_group_by_cols(alias)\n+        return super().get_group_by_cols(alias)\n+\n+\n+class Subquery(BaseExpression, Combinable):\n+    \"\"\"\n+    An explicit subquery. It may contain OuterRef() references to the outer\n+    query which will be resolved when it is applied to that query.\n+    \"\"\"\n+\n+    template = \"(%(subquery)s)\"\n+    contains_aggregate = False\n+    empty_result_set_value = None\n+\n+    def __init__(self, queryset, output_field=None, **extra):\n+        # Allow the usage of both QuerySet and sql.Query objects.\n+        self.query = getattr(queryset, \"query\", queryset).clone()\n+        self.query.subquery = True\n+        self.extra = extra\n+        super().__init__(output_field)\n+\n+    def get_source_expressions(self):\n+        return [self.query]\n+\n+    def set_source_expressions(self, exprs):\n+        self.query = exprs[0]\n+\n+    def _resolve_output_field(self):\n+        return self.query.output_field\n+\n+    def copy(self):\n+        clone = super().copy()\n+        clone.query = clone.query.clone()\n+        return clone\n+\n+    @property\n+    def external_aliases(self):\n+        return self.query.external_aliases\n+\n+    def get_external_cols(self):\n+        return self.query.get_external_cols()\n+\n+    def as_sql(self, compiler, connection, template=None, query=None, **extra_context):\n+        connection.ops.check_expression_support(self)\n+        template_params = {**self.extra, **extra_context}\n+        query = query or self.query\n+        subquery_sql, sql_params = query.as_sql(compiler, connection)\n+        template_params[\"subquery\"] = subquery_sql[1:-1]\n+\n+        template = template or template_params.get(\"template\", self.template)\n+        sql = template % template_params\n+        return sql, sql_params\n+\n+    def get_group_by_cols(self, alias=None):\n+        # If this expression is referenced by an alias for an explicit GROUP BY\n+        # through values() a reference to this expression and not the\n+        # underlying .query must be returned to ensure external column\n+        # references are not grouped against as well.\n+        if alias:\n+            return [Ref(alias, self)]\n+        return self.query.get_group_by_cols()\n+\n+\n+class Exists(Subquery):\n+    template = \"EXISTS(%(subquery)s)\"\n+    output_field = fields.BooleanField()\n+\n+    def __init__(self, queryset, negated=False, **kwargs):\n+        self.negated = negated\n+        super().__init__(queryset, **kwargs)\n+\n+    def __invert__(self):\n+        clone = self.copy()\n+        clone.negated = not self.negated\n+        return clone\n+\n+    def as_sql(self, compiler, connection, template=None, **extra_context):\n+        query = self.query.exists(using=connection.alias)\n+        try:\n+            sql, params = super().as_sql(\n+                compiler,\n+                connection,\n+                template=template,\n+                query=query,\n+                **extra_context,\n+            )\n+        except EmptyResultSet:\n+            if self.negated:\n+                features = compiler.connection.features\n+                if not features.supports_boolean_expr_in_select_clause:\n+                    return \"1=1\", ()\n+                return compiler.compile(Value(True))\n+            raise\n+        if self.negated:\n+            sql = \"NOT {}\".format(sql)\n+        return sql, params\n+\n+    def select_format(self, compiler, sql, params):\n+        # Wrap EXISTS() with a CASE WHEN expression if a database backend\n+        # (e.g. Oracle) doesn't support boolean expression in SELECT or GROUP\n+        # BY list.\n+        if not compiler.connection.features.supports_boolean_expr_in_select_clause:\n+            sql = \"CASE WHEN {} THEN 1 ELSE 0 END\".format(sql)\n+        return sql, params\n+\n+\n+@deconstructible(path=\"django.db.models.OrderBy\")\n+class OrderBy(Expression):\n+    template = \"%(expression)s %(ordering)s\"\n+    conditional = False\n+\n+    def __init__(self, expression, descending=False, nulls_first=None, nulls_last=None):\n+        if nulls_first and nulls_last:\n+            raise ValueError(\"nulls_first and nulls_last are mutually exclusive\")\n+        if nulls_first is False or nulls_last is False:\n+            # When the deprecation ends, replace with:\n+            # raise ValueError(\n+            #     \"nulls_first and nulls_last values must be True or None.\"\n+            # )\n+            warnings.warn(\n+                \"Passing nulls_first=False or nulls_last=False is deprecated, use None \"\n+                \"instead.\",\n+                RemovedInDjango50Warning,\n+                stacklevel=2,\n+            )\n+        self.nulls_first = nulls_first\n+        self.nulls_last = nulls_last\n+        self.descending = descending\n+        if not hasattr(expression, \"resolve_expression\"):\n+            raise ValueError(\"expression must be an expression type\")\n+        self.expression = expression\n+\n+    def __repr__(self):\n+        return \"{}({}, descending={})\".format(\n+            self.__class__.__name__, self.expression, self.descending\n+        )\n+\n+    def set_source_expressions(self, exprs):\n+        self.expression = exprs[0]\n+\n+    def get_source_expressions(self):\n+        return [self.expression]\n+\n+    def as_sql(self, compiler, connection, template=None, **extra_context):\n+        template = template or self.template\n+        if connection.features.supports_order_by_nulls_modifier:\n+            if self.nulls_last:\n+                template = \"%s NULLS LAST\" % template\n+            elif self.nulls_first:\n+                template = \"%s NULLS FIRST\" % template\n+        else:\n+            if self.nulls_last and not (\n+                self.descending and connection.features.order_by_nulls_first\n+            ):\n+                template = \"%%(expression)s IS NULL, %s\" % template\n+            elif self.nulls_first and not (\n+                not self.descending and connection.features.order_by_nulls_first\n+            ):\n+                template = \"%%(expression)s IS NOT NULL, %s\" % template\n+        connection.ops.check_expression_support(self)\n+        expression_sql, params = compiler.compile(self.expression)\n+        placeholders = {\n+            \"expression\": expression_sql,\n+            \"ordering\": \"DESC\" if self.descending else \"ASC\",\n+            **extra_context,\n+        }\n+        params *= template.count(\"%(expression)s\")\n+        return (template % placeholders).rstrip(), params\n+\n+    def as_oracle(self, compiler, connection):\n+        # Oracle doesn't allow ORDER BY EXISTS() or filters unless it's wrapped\n+        # in a CASE WHEN.\n+        if connection.ops.conditional_expression_supported_in_where_clause(\n+            self.expression\n+        ):\n+            copy = self.copy()\n+            copy.expression = Case(\n+                When(self.expression, then=True),\n+                default=False,\n+            )\n+            return copy.as_sql(compiler, connection)\n+        return self.as_sql(compiler, connection)\n+\n+    def get_group_by_cols(self, alias=None):\n+        cols = []\n+        for source in self.get_source_expressions():\n+            cols.extend(source.get_group_by_cols())\n+        return cols\n+\n+    def reverse_ordering(self):\n+        self.descending = not self.descending\n+        if self.nulls_first:\n+            self.nulls_last = True\n+            self.nulls_first = None\n+        elif self.nulls_last:\n+            self.nulls_first = True\n+            self.nulls_last = None\n+        return self\n+\n+    def asc(self):\n+        self.descending = False\n+\n+    def desc(self):\n+        self.descending = True\n+\n+\n+class Window(SQLiteNumericMixin, Expression):\n+    template = \"%(expression)s OVER (%(window)s)\"\n+    # Although the main expression may either be an aggregate or an\n+    # expression with an aggregate function, the GROUP BY that will\n+    # be introduced in the query as a result is not desired.\n+    contains_aggregate = False\n+    contains_over_clause = True\n+    filterable = False\n+\n+    def __init__(\n+        self,\n+        expression,\n+        partition_by=None,\n+        order_by=None,\n+        frame=None,\n+        output_field=None,\n+    ):\n+        self.partition_by = partition_by\n+        self.order_by = order_by\n+        self.frame = frame\n+\n+        if not getattr(expression, \"window_compatible\", False):\n+            raise ValueError(\n+                \"Expression '%s' isn't compatible with OVER clauses.\"\n+                % expression.__class__.__name__\n+            )\n+\n+        if self.partition_by is not None:\n+            if not isinstance(self.partition_by, (tuple, list)):\n+                self.partition_by = (self.partition_by,)\n+            self.partition_by = ExpressionList(*self.partition_by)\n+\n+        if self.order_by is not None:\n+            if isinstance(self.order_by, (list, tuple)):\n+                self.order_by = OrderByList(*self.order_by)\n+            elif isinstance(self.order_by, (BaseExpression, str)):\n+                self.order_by = OrderByList(self.order_by)\n+            else:\n+                raise ValueError(\n+                    \"Window.order_by must be either a string reference to a \"\n+                    \"field, an expression, or a list or tuple of them.\"\n+                )\n+        super().__init__(output_field=output_field)\n+        self.source_expression = self._parse_expressions(expression)[0]\n+\n+    def _resolve_output_field(self):\n+        return self.source_expression.output_field\n+\n+    def get_source_expressions(self):\n+        return [self.source_expression, self.partition_by, self.order_by, self.frame]\n+\n+    def set_source_expressions(self, exprs):\n+        self.source_expression, self.partition_by, self.order_by, self.frame = exprs\n+\n+    def as_sql(self, compiler, connection, template=None):\n+        connection.ops.check_expression_support(self)\n+        if not connection.features.supports_over_clause:\n+            raise NotSupportedError(\"This backend does not support window expressions.\")\n+        expr_sql, params = compiler.compile(self.source_expression)\n+        window_sql, window_params = [], ()\n+\n+        if self.partition_by is not None:\n+            sql_expr, sql_params = self.partition_by.as_sql(\n+                compiler=compiler,\n+                connection=connection,\n+                template=\"PARTITION BY %(expressions)s\",\n+            )\n+            window_sql.append(sql_expr)\n+            window_params += tuple(sql_params)\n+\n+        if self.order_by is not None:\n+            order_sql, order_params = compiler.compile(self.order_by)\n+            window_sql.append(order_sql)\n+            window_params += tuple(order_params)\n+\n+        if self.frame:\n+            frame_sql, frame_params = compiler.compile(self.frame)\n+            window_sql.append(frame_sql)\n+            window_params += tuple(frame_params)\n+\n+        template = template or self.template\n+\n+        return (\n+            template % {\"expression\": expr_sql, \"window\": \" \".join(window_sql).strip()},\n+            (*params, *window_params),\n+        )\n+\n+    def as_sqlite(self, compiler, connection):\n+        if isinstance(self.output_field, fields.DecimalField):\n+            # Casting to numeric must be outside of the window expression.\n+            copy = self.copy()\n+            source_expressions = copy.get_source_expressions()\n+            source_expressions[0].output_field = fields.FloatField()\n+            copy.set_source_expressions(source_expressions)\n+            return super(Window, copy).as_sqlite(compiler, connection)\n+        return self.as_sql(compiler, connection)\n+\n+    def __str__(self):\n+        return \"{} OVER ({}{}{})\".format(\n+            str(self.source_expression),\n+            \"PARTITION BY \" + str(self.partition_by) if self.partition_by else \"\",\n+            str(self.order_by or \"\"),\n+            str(self.frame or \"\"),\n+        )\n+\n+    def __repr__(self):\n+        return \"<%s: %s>\" % (self.__class__.__name__, self)\n+\n+    def get_group_by_cols(self, alias=None):\n+        return []\n+\n+\n+class WindowFrame(Expression):\n+    \"\"\"\n+    Model the frame clause in window expressions. There are two types of frame\n+    clauses which are subclasses, however, all processing and validation (by no\n+    means intended to be complete) is done here. Thus, providing an end for a\n+    frame is optional (the default is UNBOUNDED FOLLOWING, which is the last\n+    row in the frame).\n+    \"\"\"\n+\n+    template = \"%(frame_type)s BETWEEN %(start)s AND %(end)s\"\n+\n+    def __init__(self, start=None, end=None):\n+        self.start = Value(start)\n+        self.end = Value(end)\n+\n+    def set_source_expressions(self, exprs):\n+        self.start, self.end = exprs\n+\n+    def get_source_expressions(self):\n+        return [self.start, self.end]\n+\n+    def as_sql(self, compiler, connection):\n+        connection.ops.check_expression_support(self)\n+        start, end = self.window_frame_start_end(\n+            connection, self.start.value, self.end.value\n+        )\n+        return (\n+            self.template\n+            % {\n+                \"frame_type\": self.frame_type,\n+                \"start\": start,\n+                \"end\": end,\n+            },\n+            [],\n+        )\n+\n+    def __repr__(self):\n+        return \"<%s: %s>\" % (self.__class__.__name__, self)\n+\n+    def get_group_by_cols(self, alias=None):\n+        return []\n+\n+    def __str__(self):\n+        if self.start.value is not None and self.start.value < 0:\n+            start = \"%d %s\" % (abs(self.start.value), connection.ops.PRECEDING)\n+        elif self.start.value is not None and self.start.value == 0:\n+            start = connection.ops.CURRENT_ROW\n+        else:\n+            start = connection.ops.UNBOUNDED_PRECEDING\n+\n+        if self.end.value is not None and self.end.value > 0:\n+            end = \"%d %s\" % (self.end.value, connection.ops.FOLLOWING)\n+        elif self.end.value is not None and self.end.value == 0:\n+            end = connection.ops.CURRENT_ROW\n+        else:\n+            end = connection.ops.UNBOUNDED_FOLLOWING\n+        return self.template % {\n+            \"frame_type\": self.frame_type,\n+            \"start\": start,\n+            \"end\": end,\n+        }\n+\n+    def window_frame_start_end(self, connection, start, end):\n+        raise NotImplementedError(\"Subclasses must implement window_frame_start_end().\")\n+\n+\n+class RowRange(WindowFrame):\n+    frame_type = \"ROWS\"\n+\n+    def window_frame_start_end(self, connection, start, end):\n+        return connection.ops.window_frame_rows_start_end(start, end)\n+\n+\n+class ValueRange(WindowFrame):\n+    frame_type = \"RANGE\"\n+\n+    def window_frame_start_end(self, connection, start, end):\n+        return connection.ops.window_frame_range_start_end(start, end)\ndiff --git a/django/db/models/expressions_fix.patch b/django/db/models/expressions_fix.patch\nnew file mode 100644\nindex 0000000000..4cb922d3a7\n--- /dev/null\n+++ b/django/db/models/expressions_fix.patch\n@@ -0,0 +1,13 @@\n+diff --git a/django/db/models/expressions.py b/django/db/models/expressions.py\n+--- a/django/db/models/expressions.py\n++++ b/django/db/models/expressions.py\n+@@ -1193,6 +1193,9 @@ class When(Expression):\n+         template_params = extra_context\n+         sql_params = []\n+         condition_sql, condition_params = compiler.compile(self.condition)\n++        # Handle the case of empty/negated empty IN clause\n++        if not condition_sql.strip():\n++            condition_sql = \"TRUE\"\n+         template_params[\"condition\"] = condition_sql\n+         sql_params.extend(condition_params)\n+         result_sql, result_params = compiler.compile(self.result)\n\\ No newline at end of file\ndiff --git a/tests/expressions_case/test_empty_in.py b/tests/expressions_case/test_empty_in.py\nnew file mode 100644\nindex 0000000000..33d50b9670\n--- /dev/null\n+++ b/tests/expressions_case/test_empty_in.py\n@@ -0,0 +1,25 @@\n+from django.db.models import BooleanField, Case, Q, Value, When\n+from django.test import TestCase\n+\n+from .models import CaseTestModel\n+\n+class EmptyInNegationTests(TestCase):\n+    @classmethod\n+    def setUpTestData(cls):\n+        CaseTestModel.objects.create(integer=1, integer2=1, string=\"1\")\n+        CaseTestModel.objects.create(integer=2, integer2=2, string=\"2\")\n+\n+    def test_negated_empty_in_case(self):\n+        \"\"\"\n+        Test that Case-When with ~Q(pk__in=[]) works correctly.\n+        The expression should evaluate to True for all rows.\n+        \"\"\"\n+        qs = CaseTestModel.objects.annotate(\n+            test=Case(\n+                When(~Q(pk__in=[]), then=Value(True)),\n+                default=Value(False),\n+                output_field=BooleanField(),\n+            )\n+        ).order_by('pk').values_list('pk', 'test')\n+\n+        self.assertQuerysetEqual(qs, [(1, True), (2, True)], transform=tuple)\n\\ No newline at end of file\n", "proposed_model_patches": []}