2025-06-06 21:36:59,698 - ThreadPoolExecutor-0_1 - INFO - No existing container with name dgm-container-20250606_213659_691361 found.
2025-06-06 21:36:59,787 - ThreadPoolExecutor-0_1 - INFO - Building the Docker image...
2025-06-06 21:38:27,550 - ThreadPoolExecutor-0_1 - INFO - Step 1/6 : FROM python:3.10-slim
2025-06-06 21:38:27,550 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,550 - ThreadPoolExecutor-0_1 - INFO - ---> 94c0d637b23a
2025-06-06 21:38:27,550 - ThreadPoolExecutor-0_1 - INFO - Step 2/6 : RUN apt-get update && apt-get install -y     build-essential     git     && apt-get clean && rm -rf /var/lib/apt/lists/*
2025-06-06 21:38:27,551 - Thr<PERSON><PERSON>oolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,551 - ThreadPoolExecutor-0_1 - INFO - ---> Using cache
2025-06-06 21:38:27,551 - ThreadPoolExecutor-0_1 - INFO - ---> 7efc52f1399d
2025-06-06 21:38:27,551 - ThreadPoolExecutor-0_1 - INFO - Step 3/6 : WORKDIR /dgm
2025-06-06 21:38:27,551 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,551 - ThreadPoolExecutor-0_1 - INFO - ---> Using cache
2025-06-06 21:38:27,551 - ThreadPoolExecutor-0_1 - INFO - ---> 486716f000e7
2025-06-06 21:38:27,551 - ThreadPoolExecutor-0_1 - INFO - Step 4/6 : COPY . .
2025-06-06 21:38:27,551 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,551 - ThreadPoolExecutor-0_1 - INFO - ---> ae5db5ff02f3
2025-06-06 21:38:27,551 - ThreadPoolExecutor-0_1 - INFO - Step 5/6 : RUN pip install --no-cache-dir -r requirements.txt
2025-06-06 21:38:27,551 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,551 - ThreadPoolExecutor-0_1 - INFO - ---> Running in 8e2c5a84bcc8
2025-06-06 21:38:27,552 - ThreadPoolExecutor-0_1 - INFO - Collecting datasets
2025-06-06 21:38:27,552 - ThreadPoolExecutor-0_1 - INFO - Downloading datasets-3.6.0-py3-none-any.whl (491 kB)
2025-06-06 21:38:27,552 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 491.5/491.5 kB 12.8 MB/s eta 0:00:00
2025-06-06 21:38:27,552 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,552 - ThreadPoolExecutor-0_1 - INFO - Collecting anthropic
2025-06-06 21:38:27,552 - ThreadPoolExecutor-0_1 - INFO - Downloading anthropic-0.52.2-py3-none-any.whl (286 kB)
2025-06-06 21:38:27,552 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 286.3/286.3 kB 16.3 MB/s eta 0:00:00
2025-06-06 21:38:27,552 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,552 - ThreadPoolExecutor-0_1 - INFO - Collecting backoff
2025-06-06 21:38:27,552 - ThreadPoolExecutor-0_1 - INFO - Downloading backoff-2.2.1-py3-none-any.whl (15 kB)
2025-06-06 21:38:27,552 - ThreadPoolExecutor-0_1 - INFO - Collecting botocore
2025-06-06 21:38:27,552 - ThreadPoolExecutor-0_1 - INFO - Downloading botocore-1.38.32-py3-none-any.whl (13.6 MB)
2025-06-06 21:38:27,552 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.6/13.6 MB 22.4 MB/s eta 0:00:00
2025-06-06 21:38:27,553 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,553 - ThreadPoolExecutor-0_1 - INFO - Collecting boto3
2025-06-06 21:38:27,553 - ThreadPoolExecutor-0_1 - INFO - Downloading boto3-1.38.32-py3-none-any.whl (139 kB)
2025-06-06 21:38:27,553 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 139.9/139.9 kB 25.1 MB/s eta 0:00:00
2025-06-06 21:38:27,553 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,553 - ThreadPoolExecutor-0_1 - INFO - Collecting openai
2025-06-06 21:38:27,553 - ThreadPoolExecutor-0_1 - INFO - Downloading openai-1.84.0-py3-none-any.whl (725 kB)
2025-06-06 21:38:27,554 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 725.5/725.5 kB 34.4 MB/s eta 0:00:00
2025-06-06 21:38:27,554 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,554 - ThreadPoolExecutor-0_1 - INFO - Collecting litellm
2025-06-06 21:38:27,554 - ThreadPoolExecutor-0_1 - INFO - Downloading litellm-1.72.1-py3-none-any.whl (8.0 MB)
2025-06-06 21:38:27,554 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 8.0/8.0 MB 27.6 MB/s eta 0:00:00
2025-06-06 21:38:27,554 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,554 - ThreadPoolExecutor-0_1 - INFO - Collecting beautifulsoup4
2025-06-06 21:38:27,554 - ThreadPoolExecutor-0_1 - INFO - Downloading beautifulsoup4-4.13.4-py3-none-any.whl (187 kB)
2025-06-06 21:38:27,554 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 187.3/187.3 kB 15.1 MB/s eta 0:00:00
2025-06-06 21:38:27,554 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,555 - ThreadPoolExecutor-0_1 - INFO - Collecting chardet
2025-06-06 21:38:27,555 - ThreadPoolExecutor-0_1 - INFO - Downloading chardet-5.2.0-py3-none-any.whl (199 kB)
2025-06-06 21:38:27,555 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 199.4/199.4 kB 20.8 MB/s eta 0:00:00
2025-06-06 21:38:27,555 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,555 - ThreadPoolExecutor-0_1 - INFO - Collecting docker
2025-06-06 21:38:27,555 - ThreadPoolExecutor-0_1 - INFO - Downloading docker-7.1.0-py3-none-any.whl (147 kB)
2025-06-06 21:38:27,555 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 147.8/147.8 kB 308.0 MB/s eta 0:00:00
2025-06-06 21:38:27,555 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,555 - ThreadPoolExecutor-0_1 - INFO - Collecting ghapi
2025-06-06 21:38:27,555 - ThreadPoolExecutor-0_1 - INFO - Downloading ghapi-1.0.6-py3-none-any.whl (62 kB)
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.4/62.4 kB 18.2 MB/s eta 0:00:00
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - Collecting GitPython
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - Downloading GitPython-3.1.44-py3-none-any.whl (207 kB)
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 207.6/207.6 kB 31.8 MB/s eta 0:00:00
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - Collecting pre-commit
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - Downloading pre_commit-4.2.0-py2.py3-none-any.whl (220 kB)
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 220.7/220.7 kB 43.8 MB/s eta 0:00:00
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - Collecting python-dotenv
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - Downloading python_dotenv-1.1.0-py3-none-any.whl (20 kB)
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - Collecting rich
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - Downloading rich-14.0.0-py3-none-any.whl (243 kB)
2025-06-06 21:38:27,556 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 243.2/243.2 kB 30.4 MB/s eta 0:00:00
2025-06-06 21:38:27,557 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,557 - ThreadPoolExecutor-0_1 - INFO - Collecting unidiff
2025-06-06 21:38:27,557 - ThreadPoolExecutor-0_1 - INFO - Downloading unidiff-0.7.5-py2.py3-none-any.whl (14 kB)
2025-06-06 21:38:27,557 - ThreadPoolExecutor-0_1 - INFO - Collecting pytest
2025-06-06 21:38:27,557 - ThreadPoolExecutor-0_1 - INFO - Downloading pytest-8.4.0-py3-none-any.whl (363 kB)
2025-06-06 21:38:27,557 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 363.8/363.8 kB 30.2 MB/s eta 0:00:00
2025-06-06 21:38:27,557 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,557 - ThreadPoolExecutor-0_1 - INFO - Collecting pytest-asyncio
2025-06-06 21:38:27,557 - ThreadPoolExecutor-0_1 - INFO - Downloading pytest_asyncio-1.0.0-py3-none-any.whl (15 kB)
2025-06-06 21:38:27,557 - ThreadPoolExecutor-0_1 - INFO - Collecting async_timeout
2025-06-06 21:38:27,558 - ThreadPoolExecutor-0_1 - INFO - Downloading async_timeout-5.0.1-py3-none-any.whl (6.2 kB)
2025-06-06 21:38:27,558 - ThreadPoolExecutor-0_1 - INFO - Collecting requests>=2.32.2
2025-06-06 21:38:27,558 - ThreadPoolExecutor-0_1 - INFO - Downloading requests-2.32.3-py3-none-any.whl (64 kB)
2025-06-06 21:38:27,558 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 64.9/64.9 kB 49.0 MB/s eta 0:00:00
2025-06-06 21:38:27,558 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,558 - ThreadPoolExecutor-0_1 - INFO - Collecting tqdm>=4.66.3
2025-06-06 21:38:27,558 - ThreadPoolExecutor-0_1 - INFO - Downloading tqdm-4.67.1-py3-none-any.whl (78 kB)
2025-06-06 21:38:27,558 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.5/78.5 kB 75.1 MB/s eta 0:00:00
2025-06-06 21:38:27,558 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,559 - ThreadPoolExecutor-0_1 - INFO - Collecting pyyaml>=5.1
2025-06-06 21:38:27,559 - ThreadPoolExecutor-0_1 - INFO - Downloading PyYAML-6.0.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (751 kB)
2025-06-06 21:38:27,559 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 751.2/751.2 kB 25.8 MB/s eta 0:00:00
2025-06-06 21:38:27,559 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,559 - ThreadPoolExecutor-0_1 - INFO - Collecting numpy>=1.17
2025-06-06 21:38:27,559 - ThreadPoolExecutor-0_1 - INFO - Downloading numpy-2.2.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (16.8 MB)
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 16.8/16.8 MB 27.6 MB/s eta 0:00:00
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - Collecting filelock
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - Downloading filelock-3.18.0-py3-none-any.whl (16 kB)
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - Collecting dill<0.3.9,>=0.3.0
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - Downloading dill-0.3.8-py3-none-any.whl (116 kB)
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 116.3/116.3 kB 14.6 MB/s eta 0:00:00
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - Collecting pyarrow>=15.0.0
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - Downloading pyarrow-20.0.0-cp310-cp310-manylinux_2_28_x86_64.whl (42.3 MB)
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 42.3/42.3 MB 23.4 MB/s eta 0:00:00
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - Collecting fsspec[http]<=2025.3.0,>=2023.1.0
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - Downloading fsspec-2025.3.0-py3-none-any.whl (193 kB)
2025-06-06 21:38:27,560 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 193.6/193.6 kB 16.6 MB/s eta 0:00:00
2025-06-06 21:38:27,561 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,561 - ThreadPoolExecutor-0_1 - INFO - Collecting packaging
2025-06-06 21:38:27,561 - ThreadPoolExecutor-0_1 - INFO - Downloading packaging-25.0-py3-none-any.whl (66 kB)
2025-06-06 21:38:27,561 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 66.5/66.5 kB 19.3 MB/s eta 0:00:00
2025-06-06 21:38:27,561 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,561 - ThreadPoolExecutor-0_1 - INFO - Collecting pandas
2025-06-06 21:38:27,561 - ThreadPoolExecutor-0_1 - INFO - Downloading pandas-2.3.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (12.3 MB)
2025-06-06 21:38:27,561 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 12.3/12.3 MB 23.5 MB/s eta 0:00:00
2025-06-06 21:38:27,561 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,561 - ThreadPoolExecutor-0_1 - INFO - Collecting huggingface-hub>=0.24.0
2025-06-06 21:38:27,561 - ThreadPoolExecutor-0_1 - INFO - Downloading huggingface_hub-0.32.4-py3-none-any.whl (512 kB)
2025-06-06 21:38:27,561 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 512.1/512.1 kB 19.1 MB/s eta 0:00:00
2025-06-06 21:38:27,561 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,562 - ThreadPoolExecutor-0_1 - INFO - Collecting multiprocess<0.70.17
2025-06-06 21:38:27,562 - ThreadPoolExecutor-0_1 - INFO - Downloading multiprocess-0.70.16-py310-none-any.whl (134 kB)
2025-06-06 21:38:27,562 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 134.8/134.8 kB 22.2 MB/s eta 0:00:00
2025-06-06 21:38:27,562 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,562 - ThreadPoolExecutor-0_1 - INFO - Collecting xxhash
2025-06-06 21:38:27,562 - ThreadPoolExecutor-0_1 - INFO - Downloading xxhash-3.5.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (194 kB)
2025-06-06 21:38:27,563 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 194.1/194.1 kB 22.7 MB/s eta 0:00:00
2025-06-06 21:38:27,563 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,563 - ThreadPoolExecutor-0_1 - INFO - Collecting pydantic<3,>=1.9.0
2025-06-06 21:38:27,563 - ThreadPoolExecutor-0_1 - INFO - Downloading pydantic-2.11.5-py3-none-any.whl (444 kB)
2025-06-06 21:38:27,563 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 444.2/444.2 kB 36.2 MB/s eta 0:00:00
2025-06-06 21:38:27,563 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,563 - ThreadPoolExecutor-0_1 - INFO - Collecting distro<2,>=1.7.0
2025-06-06 21:38:27,563 - ThreadPoolExecutor-0_1 - INFO - Downloading distro-1.9.0-py3-none-any.whl (20 kB)
2025-06-06 21:38:27,563 - ThreadPoolExecutor-0_1 - INFO - Collecting typing-extensions<5,>=4.10
2025-06-06 21:38:27,563 - ThreadPoolExecutor-0_1 - INFO - Downloading typing_extensions-4.14.0-py3-none-any.whl (43 kB)
2025-06-06 21:38:27,563 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 43.8/43.8 kB 150.6 MB/s eta 0:00:00
2025-06-06 21:38:27,564 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,564 - ThreadPoolExecutor-0_1 - INFO - Collecting httpx<1,>=0.25.0
2025-06-06 21:38:27,564 - ThreadPoolExecutor-0_1 - INFO - Downloading httpx-0.28.1-py3-none-any.whl (73 kB)
2025-06-06 21:38:27,564 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 73.5/73.5 kB 49.7 MB/s eta 0:00:00
2025-06-06 21:38:27,564 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,564 - ThreadPoolExecutor-0_1 - INFO - Collecting jiter<1,>=0.4.0
2025-06-06 21:38:27,564 - ThreadPoolExecutor-0_1 - INFO - Downloading jiter-0.10.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (352 kB)
2025-06-06 21:38:27,564 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 352.5/352.5 kB 42.1 MB/s eta 0:00:00
2025-06-06 21:38:27,564 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,564 - ThreadPoolExecutor-0_1 - INFO - Collecting anyio<5,>=3.5.0
2025-06-06 21:38:27,564 - ThreadPoolExecutor-0_1 - INFO - Downloading anyio-4.9.0-py3-none-any.whl (100 kB)
2025-06-06 21:38:27,565 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100.9/100.9 kB 36.3 MB/s eta 0:00:00
2025-06-06 21:38:27,565 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,565 - ThreadPoolExecutor-0_1 - INFO - Collecting sniffio
2025-06-06 21:38:27,565 - ThreadPoolExecutor-0_1 - INFO - Downloading sniffio-1.3.1-py3-none-any.whl (10 kB)
2025-06-06 21:38:27,565 - ThreadPoolExecutor-0_1 - INFO - Collecting urllib3!=2.2.0,<3,>=1.25.4
2025-06-06 21:38:27,565 - ThreadPoolExecutor-0_1 - INFO - Downloading urllib3-2.4.0-py3-none-any.whl (128 kB)
2025-06-06 21:38:27,565 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 128.7/128.7 kB 49.5 MB/s eta 0:00:00
2025-06-06 21:38:27,565 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,565 - ThreadPoolExecutor-0_1 - INFO - Collecting python-dateutil<3.0.0,>=2.1
2025-06-06 21:38:27,565 - ThreadPoolExecutor-0_1 - INFO - Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
2025-06-06 21:38:27,565 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 229.9/229.9 kB 39.0 MB/s eta 0:00:00
2025-06-06 21:38:27,565 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,565 - ThreadPoolExecutor-0_1 - INFO - Collecting jmespath<2.0.0,>=0.7.1
2025-06-06 21:38:27,566 - ThreadPoolExecutor-0_1 - INFO - Downloading jmespath-1.0.1-py3-none-any.whl (20 kB)
2025-06-06 21:38:27,566 - ThreadPoolExecutor-0_1 - INFO - Collecting s3transfer<0.14.0,>=0.13.0
2025-06-06 21:38:27,566 - ThreadPoolExecutor-0_1 - INFO - Downloading s3transfer-0.13.0-py3-none-any.whl (85 kB)
2025-06-06 21:38:27,566 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 85.2/85.2 kB 36.0 MB/s eta 0:00:00
2025-06-06 21:38:27,566 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,566 - ThreadPoolExecutor-0_1 - INFO - Collecting tiktoken>=0.7.0
2025-06-06 21:38:27,566 - ThreadPoolExecutor-0_1 - INFO - Downloading tiktoken-0.9.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.2 MB)
2025-06-06 21:38:27,566 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.2/1.2 MB 29.0 MB/s eta 0:00:00
2025-06-06 21:38:27,566 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,566 - ThreadPoolExecutor-0_1 - INFO - Collecting jsonschema<5.0.0,>=4.22.0
2025-06-06 21:38:27,566 - ThreadPoolExecutor-0_1 - INFO - Downloading jsonschema-4.24.0-py3-none-any.whl (88 kB)
2025-06-06 21:38:27,566 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 88.7/88.7 kB 52.4 MB/s eta 0:00:00
2025-06-06 21:38:27,566 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - Collecting aiohttp
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - Downloading aiohttp-3.12.9-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.6 MB)
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.6/1.6 MB 35.3 MB/s eta 0:00:00
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - Collecting importlib-metadata>=6.8.0
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - Downloading importlib_metadata-8.7.0-py3-none-any.whl (27 kB)
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - Collecting click
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - Downloading click-8.2.1-py3-none-any.whl (102 kB)
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 102.2/102.2 kB 29.9 MB/s eta 0:00:00
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - Collecting tokenizers
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - Downloading tokenizers-0.21.1-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.0 MB)
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 3.0/3.0 MB 27.2 MB/s eta 0:00:00
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - Collecting jinja2<4.0.0,>=3.1.2
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - Downloading jinja2-3.1.6-py3-none-any.whl (134 kB)
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 134.9/134.9 kB 12.7 MB/s eta 0:00:00
2025-06-06 21:38:27,567 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - Collecting soupsieve>1.2
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - Downloading soupsieve-2.7-py3-none-any.whl (36 kB)
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - Collecting fastcore>=1.7.2
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - Downloading fastcore-1.8.2-py3-none-any.whl (78 kB)
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.2/78.2 kB 29.0 MB/s eta 0:00:00
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - Collecting gitdb<5,>=4.0.1
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - Downloading gitdb-4.0.12-py3-none-any.whl (62 kB)
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.8/62.8 kB 290.1 MB/s eta 0:00:00
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - Collecting cfgv>=2.0.0
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - Downloading cfgv-3.4.0-py2.py3-none-any.whl (7.2 kB)
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - Collecting nodeenv>=0.11.1
2025-06-06 21:38:27,568 - ThreadPoolExecutor-0_1 - INFO - Downloading nodeenv-1.9.1-py2.py3-none-any.whl (22 kB)
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - Collecting identify>=1.0.0
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - Downloading identify-2.6.12-py2.py3-none-any.whl (99 kB)
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 99.1/99.1 kB 15.1 MB/s eta 0:00:00
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - Collecting virtualenv>=20.10.0
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - Downloading virtualenv-20.31.2-py3-none-any.whl (6.1 MB)
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 6.1/6.1 MB 29.4 MB/s eta 0:00:00
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - Collecting markdown-it-py>=2.2.0
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - Downloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 87.5/87.5 kB 24.3 MB/s eta 0:00:00
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - Collecting pygments<3.0.0,>=2.13.0
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - Downloading pygments-2.19.1-py3-none-any.whl (1.2 MB)
2025-06-06 21:38:27,569 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.2/1.2 MB 33.7 MB/s eta 0:00:00
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Collecting tomli>=1
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Downloading tomli-2.2.1-py3-none-any.whl (14 kB)
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Collecting iniconfig>=1
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Downloading iniconfig-2.1.0-py3-none-any.whl (6.0 kB)
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Collecting pluggy<2,>=1.5
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Downloading pluggy-1.6.0-py3-none-any.whl (20 kB)
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Collecting exceptiongroup>=1
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Downloading exceptiongroup-1.3.0-py3-none-any.whl (16 kB)
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Collecting idna>=2.8
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Downloading idna-3.10-py3-none-any.whl (70 kB)
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 70.4/70.4 kB 124.0 MB/s eta 0:00:00
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Collecting aiohappyeyeballs>=2.5.0
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Collecting aiosignal>=1.1.2
2025-06-06 21:38:27,570 - ThreadPoolExecutor-0_1 - INFO - Downloading aiosignal-1.3.2-py2.py3-none-any.whl (7.6 kB)
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - Collecting yarl<2.0,>=1.17.0
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - Downloading yarl-1.20.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (333 kB)
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 333.9/333.9 kB 36.8 MB/s eta 0:00:00
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - Collecting propcache>=0.2.0
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - Downloading propcache-0.3.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (206 kB)
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 206.6/206.6 kB 37.3 MB/s eta 0:00:00
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - Collecting multidict<7.0,>=4.5
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - Downloading multidict-6.4.4-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (219 kB)
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 219.1/219.1 kB 30.4 MB/s eta 0:00:00
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - Collecting frozenlist>=1.1.1
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - Downloading frozenlist-1.6.2-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (224 kB)
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 224.4/224.4 kB 28.6 MB/s eta 0:00:00
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,571 - ThreadPoolExecutor-0_1 - INFO - Collecting attrs>=17.3.0
2025-06-06 21:38:27,572 - ThreadPoolExecutor-0_1 - INFO - Downloading attrs-25.3.0-py3-none-any.whl (63 kB)
2025-06-06 21:38:27,572 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 63.8/63.8 kB 28.8 MB/s eta 0:00:00
2025-06-06 21:38:27,572 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,572 - ThreadPoolExecutor-0_1 - INFO - Collecting smmap<6,>=3.0.1
2025-06-06 21:38:27,572 - ThreadPoolExecutor-0_1 - INFO - Downloading smmap-5.0.2-py3-none-any.whl (24 kB)
2025-06-06 21:38:27,572 - ThreadPoolExecutor-0_1 - INFO - Collecting certifi
2025-06-06 21:38:27,572 - ThreadPoolExecutor-0_1 - INFO - Downloading certifi-2025.4.26-py3-none-any.whl (159 kB)
2025-06-06 21:38:27,572 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 159.6/159.6 kB 39.7 MB/s eta 0:00:00
2025-06-06 21:38:27,572 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,572 - ThreadPoolExecutor-0_1 - INFO - Collecting httpcore==1.*
2025-06-06 21:38:27,573 - ThreadPoolExecutor-0_1 - INFO - Downloading httpcore-1.0.9-py3-none-any.whl (78 kB)
2025-06-06 21:38:27,573 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.8/78.8 kB 23.9 MB/s eta 0:00:00
2025-06-06 21:38:27,573 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,573 - ThreadPoolExecutor-0_1 - INFO - Collecting h11>=0.16
2025-06-06 21:38:27,573 - ThreadPoolExecutor-0_1 - INFO - Downloading h11-0.16.0-py3-none-any.whl (37 kB)
2025-06-06 21:38:27,573 - ThreadPoolExecutor-0_1 - INFO - Collecting hf-xet<2.0.0,>=1.1.2
2025-06-06 21:38:27,573 - ThreadPoolExecutor-0_1 - INFO - Downloading hf_xet-1.1.3-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (4.8 MB)
2025-06-06 21:38:27,573 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 4.8/4.8 MB 34.3 MB/s eta 0:00:00
2025-06-06 21:38:27,573 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,573 - ThreadPoolExecutor-0_1 - INFO - Collecting zipp>=3.20
2025-06-06 21:38:27,573 - ThreadPoolExecutor-0_1 - INFO - Downloading zipp-3.22.0-py3-none-any.whl (9.8 kB)
2025-06-06 21:38:27,573 - ThreadPoolExecutor-0_1 - INFO - Collecting MarkupSafe>=2.0
2025-06-06 21:38:27,574 - ThreadPoolExecutor-0_1 - INFO - Downloading MarkupSafe-3.0.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (20 kB)
2025-06-06 21:38:27,574 - ThreadPoolExecutor-0_1 - INFO - Collecting referencing>=0.28.4
2025-06-06 21:38:27,574 - ThreadPoolExecutor-0_1 - INFO - Downloading referencing-0.36.2-py3-none-any.whl (26 kB)
2025-06-06 21:38:27,574 - ThreadPoolExecutor-0_1 - INFO - Collecting rpds-py>=0.7.1
2025-06-06 21:38:27,574 - ThreadPoolExecutor-0_1 - INFO - Downloading rpds_py-0.25.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (386 kB)
2025-06-06 21:38:27,574 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 387.0/387.0 kB 24.1 MB/s eta 0:00:00
2025-06-06 21:38:27,574 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,574 - ThreadPoolExecutor-0_1 - INFO - Collecting jsonschema-specifications>=2023.03.6
2025-06-06 21:38:27,574 - ThreadPoolExecutor-0_1 - INFO - Downloading jsonschema_specifications-2025.4.1-py3-none-any.whl (18 kB)
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Collecting mdurl~=0.1
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Downloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Collecting pydantic-core==2.33.2
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Downloading pydantic_core-2.33.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.0 MB)
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 34.2 MB/s eta 0:00:00
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Collecting annotated-types>=0.6.0
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Collecting typing-inspection>=0.4.0
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Downloading typing_inspection-0.4.1-py3-none-any.whl (14 kB)
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Collecting six>=1.5
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Downloading six-1.17.0-py2.py3-none-any.whl (11 kB)
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Collecting charset-normalizer<4,>=2
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Downloading charset_normalizer-3.4.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (149 kB)
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 149.5/149.5 kB 46.3 MB/s eta 0:00:00
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Collecting regex>=2022.1.18
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - Downloading regex-2024.11.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (781 kB)
2025-06-06 21:38:27,575 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 781.7/781.7 kB 38.4 MB/s eta 0:00:00
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Collecting distlib<1,>=0.3.7
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Downloading distlib-0.3.9-py2.py3-none-any.whl (468 kB)
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 469.0/469.0 kB 30.9 MB/s eta 0:00:00
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Collecting platformdirs<5,>=3.9.1
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Downloading platformdirs-4.3.8-py3-none-any.whl (18 kB)
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Collecting pytz>=2020.1
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Downloading pytz-2025.2-py2.py3-none-any.whl (509 kB)
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 509.2/509.2 kB 34.5 MB/s eta 0:00:00
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Collecting tzdata>=2022.7
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Downloading tzdata-2025.2-py2.py3-none-any.whl (347 kB)
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 347.8/347.8 kB 31.8 MB/s eta 0:00:00
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Installing collected packages: unidiff, pytz, distlib, zipp, xxhash, urllib3, tzdata, typing-extensions, tqdm, tomli, soupsieve, sniffio, smmap, six, rpds-py, regex, pyyaml, python-dotenv, pygments, pyarrow, propcache, pluggy, platformdirs, packaging, numpy, nodeenv, mdurl, MarkupSafe, jmespath, jiter, iniconfig, idna, identify, hf-xet, h11, fsspec, frozenlist, filelock, distro, dill, click, charset-normalizer, chardet, cfgv, certifi, backoff, attrs, async_timeout, annotated-types, aiohappyeyeballs, virtualenv, typing-inspection, requests, referencing, python-dateutil, pydantic-core, multiprocess, multidict, markdown-it-py, jinja2, importlib-metadata, httpcore, gitdb, fastcore, exceptiongroup, beautifulsoup4, aiosignal, yarl, tiktoken, rich, pytest, pydantic, pre-commit, pandas, jsonschema-specifications, huggingface-hub, GitPython, ghapi, docker, botocore, anyio, tokenizers, s3transfer, pytest-asyncio, jsonschema, httpx, aiohttp, openai, boto3, anthropic, litellm, datasets
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Successfully installed GitPython-3.1.44 MarkupSafe-3.0.2 aiohappyeyeballs-2.6.1 aiohttp-3.12.9 aiosignal-1.3.2 annotated-types-0.7.0 anthropic-0.52.2 anyio-4.9.0 async_timeout-5.0.1 attrs-25.3.0 backoff-2.2.1 beautifulsoup4-4.13.4 boto3-1.38.32 botocore-1.38.32 certifi-2025.4.26 cfgv-3.4.0 chardet-5.2.0 charset-normalizer-3.4.2 click-8.2.1 datasets-3.6.0 dill-0.3.8 distlib-0.3.9 distro-1.9.0 docker-7.1.0 exceptiongroup-1.3.0 fastcore-1.8.2 filelock-3.18.0 frozenlist-1.6.2 fsspec-2025.3.0 ghapi-1.0.6 gitdb-4.0.12 h11-0.16.0 hf-xet-1.1.3 httpcore-1.0.9 httpx-0.28.1 huggingface-hub-0.32.4 identify-2.6.12 idna-3.10 importlib-metadata-8.7.0 iniconfig-2.1.0 jinja2-3.1.6 jiter-0.10.0 jmespath-1.0.1 jsonschema-4.24.0 jsonschema-specifications-2025.4.1 litellm-1.72.1 markdown-it-py-3.0.0 mdurl-0.1.2 multidict-6.4.4 multiprocess-0.70.16 nodeenv-1.9.1 numpy-2.2.6 openai-1.84.0 packaging-25.0 pandas-2.3.0 platformdirs-4.3.8 pluggy-1.6.0 pre-commit-4.2.0 propcache-0.3.1 pyarrow-20.0.0 pydantic-2.11.5 pydantic-core-2.33.2 pygments-2.19.1 pytest-8.4.0 pytest-asyncio-1.0.0 python-dateutil-2.9.0.post0 python-dotenv-1.1.0 pytz-2025.2 pyyaml-6.0.2 referencing-0.36.2 regex-2024.11.6 requests-2.32.3 rich-14.0.0 rpds-py-0.25.1 s3transfer-0.13.0 six-1.17.0 smmap-5.0.2 sniffio-1.3.1 soupsieve-2.7 tiktoken-0.9.0 tokenizers-0.21.1 tomli-2.2.1 tqdm-4.67.1 typing-extensions-4.14.0 typing-inspection-0.4.1 tzdata-2025.2 unidiff-0.7.5 urllib3-2.4.0 virtualenv-20.31.2 xxhash-3.5.0 yarl-1.20.0 zipp-3.22.0
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - [91mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
[0m
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - [91m
[notice] A new release of pip is available: 23.0.1 -> 25.1.1
[notice] To update, run: pip install --upgrade pip
[0m
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - ---> Removed intermediate container 8e2c5a84bcc8
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - ---> e01e96dbae7a
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Step 6/6 : CMD ["tail", "-f", "/dev/null"]
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - 
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - ---> Running in a38fd115ae7a
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - ---> Removed intermediate container a38fd115ae7a
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - ---> 4def7c8c657a
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Successfully built 4def7c8c657a
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Successfully tagged dgm:latest
2025-06-06 21:38:27,576 - ThreadPoolExecutor-0_1 - INFO - Image built successfully.
2025-06-06 21:38:29,385 - ThreadPoolExecutor-0_1 - INFO - Container 'dgm-container-20250606_213659_691361' started successfully.
2025-06-06 21:38:29,462 - ThreadPoolExecutor-0_1 - INFO - Container output: fatal: not a git repository (or any of the parent directories): .git

2025-06-06 21:38:29,462 - ThreadPoolExecutor-0_1 - ERROR - Script failed with exit code 128
