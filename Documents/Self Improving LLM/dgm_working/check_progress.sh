#!/bin/bash

# Quick DGM Progress Checker
echo "🧬 DGM QUICK CHECK"
echo "=================="

# Check if DGM is running
if pgrep -f "DGM_outer.py" > /dev/null; then
    echo "🟢 DGM: RUNNING"
else
    echo "🔴 DGM: STOPPED"
fi

# Count generations
GENERATIONS=$(find output_dgm -name "generation_*" -type d | wc -l)
echo "🧬 Generations: $GENERATIONS/50"

# Count patches
TOTAL_PATCHES=$(find output_dgm -name "model_patch.diff" | wc -l)
NON_EMPTY_PATCHES=$(find output_dgm -name "model_patch.diff" -exec wc -l {} \; | awk '$1 > 0 {count++} END {print count+0}')
echo "🔧 Patches: $NON_EMPTY_PATCHES/$TOTAL_PATCHES non-empty"

# Show latest activity
echo "🕐 Latest patches:"
find output_dgm -name "model_patch.diff" -exec wc -l {} \; | tail -3

echo ""
echo "Run 'python quick_stats.py' for detailed stats"
echo "Run 'python live_monitor.py' for continuous monitoring"
