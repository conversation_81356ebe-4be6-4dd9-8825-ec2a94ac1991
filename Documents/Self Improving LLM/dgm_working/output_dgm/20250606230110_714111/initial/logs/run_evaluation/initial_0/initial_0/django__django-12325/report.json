{"django__django-12325": {"patch_is_None": false, "patch_exists": true, "patch_successfully_applied": true, "resolved": false, "tests_status": {"FAIL_TO_PASS": {"success": [], "failure": ["test_clash_parent_link (invalid_models_tests.test_relative_fields.ComplexClashTests)", "test_onetoone_with_parent_model (invalid_models_tests.test_models.OtherModelTests)"]}, "PASS_TO_PASS": {"success": [], "failure": ["test_check_constraints (invalid_models_tests.test_models.ConstraintsTests)", "test_check_constraints_required_db_features (invalid_models_tests.test_models.ConstraintsTests)", "test_accessor_clash (invalid_models_tests.test_relative_fields.SelfReferentialFKClashTests)", "test_clash_under_explicit_related_name (invalid_models_tests.test_relative_fields.SelfReferentialFKClashTests)", "test_reverse_query_name_clash (invalid_models_tests.test_relative_fields.SelfReferentialFKClashTests)", "test_complex_clash (invalid_models_tests.test_relative_fields.ComplexClashTests)", "test_list_containing_non_iterable (invalid_models_tests.test_models.IndexTogetherTests)", "test_non_iterable (invalid_models_tests.test_models.IndexTogetherTests)", "test_non_list (invalid_models_tests.test_models.IndexTogetherTests)", "test_pointing_to_fk (invalid_models_tests.test_models.IndexTogetherTests)", "test_pointing_to_m2m_field (invalid_models_tests.test_models.IndexTogetherTests)", "test_pointing_to_missing_field (invalid_models_tests.test_models.IndexTogetherTests)", "test_pointing_to_non_local_field (invalid_models_tests.test_models.IndexTogetherTests)", "test_abstract_model_children_inherit_indexes (migrations.test_state.ModelStateTests)", "test_bound_field_sanity_check (migrations.test_state.ModelStateTests)", "test_create_swappable (migrations.test_state.ModelStateTests)", "test_create_swappable_from_abstract (migrations.test_state.ModelStateTests)", "test_custom_manager_swappable (migrations.test_state.ModelStateTests)", "test_custom_model_base (migrations.test_state.ModelStateTests)", "test_explicit_index_name (migrations.test_state.ModelStateTests)", "test_fields_immutability (migrations.test_state.ModelStateTests)", "test_from_model_constraints (migrations.test_state.ModelStateTests)", "test_order_with_respect_to_private_field (migrations.test_state.ModelStateTests)", "test_repr (migrations.test_state.ModelStateTests)", "test_sanity_check_through (migrations.test_state.ModelStateTests)", "test_sanity_check_to (migrations.test_state.ModelStateTests)", "test_sanity_index_name (migrations.test_state.ModelStateTests)", "test_db_column_clash (invalid_models_tests.test_models.FieldNamesTests)", "test_ending_with_underscore (invalid_models_tests.test_models.FieldNamesTests)", "test_including_separator (invalid_models_tests.test_models.FieldNamesTests)", "test_pk (invalid_models_tests.test_models.FieldNamesTests)", "test_accessor_clash (invalid_models_tests.test_relative_fields.SelfReferentialM2MClashTests)", "test_clash_between_accessors (invalid_models_tests.test_relative_fields.SelfReferentialM2MClashTests)", "test_clash_under_explicit_related_name (invalid_models_tests.test_relative_fields.SelfReferentialM2MClashTests)", "test_reverse_query_name_clash (invalid_models_tests.test_relative_fields.SelfReferentialM2MClashTests)", "test_valid_model (invalid_models_tests.test_relative_fields.SelfReferentialM2MClashTests)", "test_list_containing_non_iterable (invalid_models_tests.test_models.UniqueTogetherTests)", "test_non_iterable (invalid_models_tests.test_models.UniqueTogetherTests)", "test_non_list (invalid_models_tests.test_models.UniqueTogetherTests)", "test_pointing_to_fk (invalid_models_tests.test_models.UniqueTogetherTests)", "test_pointing_to_m2m (invalid_models_tests.test_models.UniqueTogetherTests)", "test_pointing_to_missing_field (invalid_models_tests.test_models.UniqueTogetherTests)", "test_valid_model (invalid_models_tests.test_models.UniqueTogetherTests)", "test_fk_to_fk (invalid_models_tests.test_relative_fields.ExplicitRelatedNameClashTests)", "test_fk_to_integer (invalid_models_tests.test_relative_fields.ExplicitRelatedNameClashTests)", "test_fk_to_m2m (invalid_models_tests.test_relative_fields.ExplicitRelatedNameClashTests)", "test_m2m_to_fk (invalid_models_tests.test_relative_fields.ExplicitRelatedNameClashTests)", "test_m2m_to_integer (invalid_models_tests.test_relative_fields.ExplicitRelatedNameClashTests)", "test_m2m_to_m2m (invalid_models_tests.test_relative_fields.ExplicitRelatedNameClashTests)", "test_explicit_field_names (invalid_models_tests.test_relative_fields.M2mThroughFieldsTests)", "test_intersection_foreign_object (invalid_models_tests.test_relative_fields.M2mThroughFieldsTests)", "test_invalid_field (invalid_models_tests.test_relative_fields.M2mThroughFieldsTests)", "test_invalid_order (invalid_models_tests.test_relative_fields.M2mThroughFieldsTests)", "test_m2m_field_argument_validation (invalid_models_tests.test_relative_fields.M2mThroughFieldsTests)", "test_superset_foreign_object (invalid_models_tests.test_relative_fields.M2mThroughFieldsTests)", "test_max_name_length (invalid_models_tests.test_models.IndexesTests)", "test_name_constraints (invalid_models_tests.test_models.IndexesTests)", "test_pointing_to_fk (invalid_models_tests.test_models.IndexesTests)", "test_pointing_to_m2m_field (invalid_models_tests.test_models.IndexesTests)", "test_pointing_to_missing_field (invalid_models_tests.test_models.IndexesTests)", "test_pointing_to_non_local_field (invalid_models_tests.test_models.IndexesTests)", "test_fk_to_fk (invalid_models_tests.test_relative_fields.ReverseQueryNameClashTests)", "test_fk_to_integer (invalid_models_tests.test_relative_fields.ReverseQueryNameClashTests)", "test_fk_to_m2m (invalid_models_tests.test_relative_fields.ReverseQueryNameClashTests)", "test_m2m_to_fk (invalid_models_tests.test_relative_fields.ReverseQueryNameClashTests)", "test_m2m_to_integer (invalid_models_tests.test_relative_fields.ReverseQueryNameClashTests)", "test_m2m_to_m2m (invalid_models_tests.test_relative_fields.ReverseQueryNameClashTests)", "test_field_name_clash_with_child_accessor (invalid_models_tests.test_models.ShadowingFieldsTests)", "test_id_clash (invalid_models_tests.test_models.ShadowingFieldsTests)", "test_inheritance_clash (invalid_models_tests.test_models.ShadowingFieldsTests)", "test_multigeneration_inheritance (invalid_models_tests.test_models.ShadowingFieldsTests)", "test_multiinheritance_clash (invalid_models_tests.test_models.ShadowingFieldsTests)", "test_clash_between_accessors (invalid_models_tests.test_relative_fields.AccessorClashTests)", "test_fk_to_fk (invalid_models_tests.test_relative_fields.AccessorClashTests)", "test_fk_to_integer (invalid_models_tests.test_relative_fields.AccessorClashTests)", "test_fk_to_m2m (invalid_models_tests.test_relative_fields.AccessorClashTests)", "test_m2m_to_fk (invalid_models_tests.test_relative_fields.AccessorClashTests)", "test_m2m_to_integer (invalid_models_tests.test_relative_fields.AccessorClashTests)", "test_m2m_to_m2m (invalid_models_tests.test_relative_fields.AccessorClashTests)", "Ref #22047.", "test_no_clash_for_hidden_related_name (invalid_models_tests.test_relative_fields.AccessorClashTests)", "test_fk_to_fk (invalid_models_tests.test_relative_fields.ExplicitRelatedQueryNameClashTests)", "test_fk_to_integer (invalid_models_tests.test_relative_fields.ExplicitRelatedQueryNameClashTests)", "test_fk_to_m2m (invalid_models_tests.test_relative_fields.ExplicitRelatedQueryNameClashTests)", "test_hidden_fk_to_fk (invalid_models_tests.test_relative_fields.ExplicitRelatedQueryNameClashTests)", "test_hidden_fk_to_integer (invalid_models_tests.test_relative_fields.ExplicitRelatedQueryNameClashTests)", "test_hidden_fk_to_m2m (invalid_models_tests.test_relative_fields.ExplicitRelatedQueryNameClashTests)", "test_hidden_m2m_to_fk (invalid_models_tests.test_relative_fields.ExplicitRelatedQueryNameClashTests)", "test_hidden_m2m_to_integer (invalid_models_tests.test_relative_fields.ExplicitRelatedQueryNameClashTests)", "test_hidden_m2m_to_m2m (invalid_models_tests.test_relative_fields.ExplicitRelatedQueryNameClashTests)", "test_m2m_to_fk (invalid_models_tests.test_relative_fields.ExplicitRelatedQueryNameClashTests)", "test_m2m_to_integer (invalid_models_tests.test_relative_fields.ExplicitRelatedQueryNameClashTests)", "test_m2m_to_m2m (invalid_models_tests.test_relative_fields.ExplicitRelatedQueryNameClashTests)", "test_abstract_base (migrations.test_state.RelatedModelsTests)", "test_base (migrations.test_state.RelatedModelsTests)", "test_base_to_base_fk (migrations.test_state.RelatedModelsTests)", "test_base_to_subclass_fk (migrations.test_state.RelatedModelsTests)", "test_circle (migrations.test_state.RelatedModelsTests)", "test_direct_fk (migrations.test_state.RelatedModelsTests)", "test_direct_hidden_fk (migrations.test_state.RelatedModelsTests)", "test_direct_m2m (migrations.test_state.RelatedModelsTests)", "test_direct_m2m_self (migrations.test_state.RelatedModelsTests)", "test_fk_through_proxy (migrations.test_state.RelatedModelsTests)", "test_generic_fk (migrations.test_state.RelatedModelsTests)", "test_intermediate_m2m (migrations.test_state.RelatedModelsTests)", "test_intermediate_m2m_base (migrations.test_state.RelatedModelsTests)", "test_intermediate_m2m_extern_fk (migrations.test_state.RelatedModelsTests)", "test_intermediate_m2m_self (migrations.test_state.RelatedModelsTests)", "test_multiple_bases (migrations.test_state.RelatedModelsTests)", "test_multiple_mixed_bases (migrations.test_state.RelatedModelsTests)", "test_multiple_nested_bases (migrations.test_state.RelatedModelsTests)", "test_nested_abstract_base (migrations.test_state.RelatedModelsTests)", "test_nested_base (migrations.test_state.RelatedModelsTests)", "test_nested_fk (migrations.test_state.RelatedModelsTests)", "test_nested_proxy_base (migrations.test_state.RelatedModelsTests)", "test_proxy_base (migrations.test_state.RelatedModelsTests)", "test_two_sided (migrations.test_state.RelatedModelsTests)", "test_unrelated (migrations.test_state.RelatedModelsTests)", "test_ambiguous_relationship_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_foreign_key_to_abstract_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_foreign_key_to_isolate_apps_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_foreign_key_to_missing_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_foreign_key_to_non_unique_field (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_foreign_key_to_non_unique_field_under_explicit_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_foreign_object_to_non_unique_fields (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_invalid_related_query_name (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_m2m_to_abstract_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_many_to_many_through_isolate_apps_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_many_to_many_to_isolate_apps_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_many_to_many_to_missing_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_many_to_many_with_limit_choices_auto_created_no_warning (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_many_to_many_with_useless_options (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_missing_relationship_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_missing_relationship_model_on_model_check (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_not_swapped_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_nullable_primary_key (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_on_delete_set_default_without_default_value (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_on_delete_set_null_on_non_nullable_field (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_referencing_to_swapped_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_related_field_has_invalid_related_name (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_related_field_has_valid_related_name (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_relationship_model_missing_foreign_key (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_relationship_model_with_foreign_key_to_wrong_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_to_fields_exist (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_to_fields_not_checked_if_related_model_doesnt_exist (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_too_many_foreign_keys_in_self_referential_model (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_unique_m2m (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_valid_foreign_key_without_accessor (invalid_models_tests.test_relative_fields.RelativeFieldTests)", "test_just_order_with_respect_to_no_errors (invalid_models_tests.test_models.OtherModelTests)", "test_just_ordering_no_errors (invalid_models_tests.test_models.OtherModelTests)", "test_lazy_reference_checks (invalid_models_tests.test_models.OtherModelTests)", "test_m2m_autogenerated_table_name_clash (invalid_models_tests.test_models.OtherModelTests)", "test_m2m_field_table_name_clash (invalid_models_tests.test_models.OtherModelTests)", "test_m2m_table_name_clash (invalid_models_tests.test_models.OtherModelTests)", "test_m2m_to_concrete_and_proxy_allowed (invalid_models_tests.test_models.OtherModelTests)", "test_m2m_unmanaged_shadow_models_not_checked (invalid_models_tests.test_models.OtherModelTests)", "test_name_beginning_with_underscore (invalid_models_tests.test_models.OtherModelTests)", "test_name_contains_double_underscores (invalid_models_tests.test_models.OtherModelTests)", "test_name_ending_with_underscore (invalid_models_tests.test_models.OtherModelTests)", "test_non_valid (invalid_models_tests.test_models.OtherModelTests)", "test_onetoone_with_explicit_parent_link_parent_model (invalid_models_tests.test_models.OtherModelTests)", "test_ordering_allows_registered_lookups (invalid_models_tests.test_models.OtherModelTests)", "test_ordering_non_iterable (invalid_models_tests.test_models.OtherModelTests)", "test_ordering_pointing_multiple_times_to_model_fields (invalid_models_tests.test_models.OtherModelTests)", "test_ordering_pointing_to_foreignkey_field (invalid_models_tests.test_models.OtherModelTests)", "test_ordering_pointing_to_missing_field (invalid_models_tests.test_models.OtherModelTests)", "test_ordering_pointing_to_missing_foreignkey_field (invalid_models_tests.test_models.OtherModelTests)", "test_ordering_pointing_to_missing_related_field (invalid_models_tests.test_models.OtherModelTests)", "test_ordering_pointing_to_missing_related_model_field (invalid_models_tests.test_models.OtherModelTests)", "test_ordering_pointing_to_non_related_field (invalid_models_tests.test_models.OtherModelTests)", "test_ordering_pointing_to_related_model_pk (invalid_models_tests.test_models.OtherModelTests)", "test_ordering_pointing_to_two_related_model_field (invalid_models_tests.test_models.OtherModelTests)", "test_ordering_with_order_with_respect_to (invalid_models_tests.test_models.OtherModelTests)", "test_property_and_related_field_accessor_clash (invalid_models_tests.test_models.OtherModelTests)", "test_single_primary_key (invalid_models_tests.test_models.OtherModelTests)", "test_swappable_missing_app (invalid_models_tests.test_models.OtherModelTests)", "test_swappable_missing_app_name (invalid_models_tests.test_models.OtherModelTests)", "test_two_m2m_through_same_model_with_different_through_fields (invalid_models_tests.test_models.OtherModelTests)", "test_two_m2m_through_same_relationship (invalid_models_tests.test_models.OtherModelTests)", "test_unique_primary_key (invalid_models_tests.test_models.OtherModelTests)", "test_add_relations (migrations.test_state.StateTests)", "test_apps_bulk_update (migrations.test_state.StateTests)", "test_choices_iterator (migrations.test_state.StateTests)", "test_create (migrations.test_state.StateTests)", "test_custom_base_manager (migrations.test_state.StateTests)", "test_custom_default_manager (migrations.test_state.StateTests)", "test_custom_default_manager_added_to_the_model_state (migrations.test_state.StateTests)", "test_custom_default_manager_named_objects_with_false_migration_flag (migrations.test_state.StateTests)", "test_dangling_references_throw_error (migrations.test_state.StateTests)", "test_equality (migrations.test_state.StateTests)", "test_ignore_order_wrt (migrations.test_state.StateTests)", "test_manager_refer_correct_model_version (migrations.test_state.StateTests)", "test_no_duplicate_managers (migrations.test_state.StateTests)", "test_real_apps (migrations.test_state.StateTests)", "test_remove_relations (migrations.test_state.StateTests)", "test_render (migrations.test_state.StateTests)", "test_render_model_inheritance (migrations.test_state.StateTests)", "test_render_model_with_multiple_inheritance (migrations.test_state.StateTests)", "test_render_project_dependencies (migrations.test_state.StateTests)", "test_render_unique_app_labels (migrations.test_state.StateTests)", "test_self_relation (migrations.test_state.StateTests)"]}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}}}