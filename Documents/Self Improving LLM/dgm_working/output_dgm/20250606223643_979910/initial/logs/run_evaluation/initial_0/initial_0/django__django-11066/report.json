{"django__django-11066": {"patch_is_None": false, "patch_exists": true, "patch_successfully_applied": true, "resolved": true, "tests_status": {"FAIL_TO_PASS": {"success": ["test_existing_content_type_rename_other_database (contenttypes_tests.test_operations.ContentTypeOperationsTests)"], "failure": []}, "PASS_TO_PASS": {"success": ["test_content_type_rename_conflict (contenttypes_tests.test_operations.ContentTypeOperationsTests)", "test_existing_content_type_rename (contenttypes_tests.test_operations.ContentTypeOperationsTests)", "test_missing_content_type_rename_ignore (contenttypes_tests.test_operations.ContentTypeOperationsTests)"], "failure": []}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}}}