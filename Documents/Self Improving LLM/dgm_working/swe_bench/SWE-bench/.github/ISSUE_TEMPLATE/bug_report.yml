name: Bug Report
description: Create a report to help us reproduce and correct the bug
labels: ['bug']

body:
- type: markdown
  attributes:
    value: >
      #### Before submitting a bug, please make sure the issue hasn't been already
      addressed by searching through [the past issues](https://github.com/princeton-nlp/SWE-agent/issues).
- type: textarea
  attributes:
    label: Describe the bug
    description: >
      A clear and concise description of what the bug is.
  validations:
    required: true
- type: textarea
  attributes:
    label: Steps/Code to Reproduce
    description: >
      **Please add the full command that you were running as well as how you set up the software (docker, conda, etc.)**
      Please always copy code as text and not as a screenshot.
    placeholder: |
      ```
      Sample code/commands to reproduce the problem
      ```
  validations:
    required: true
- type: textarea
  attributes:
    label: Expected Results
    description: >
      Please paste or describe the expected results.
    placeholder: >
      Example: No error is thrown.
  validations:
    required: true
- type: textarea
  attributes:
    label: Actual Results
    description: >
      Please paste or describe the results you observe instead of the expected results.
      If you observe an error, please paste the error message including the exception traceback.
    placeholder: >
      Please paste or specifically describe the actual output or traceback.
  validations:
    required: true
- type: textarea
  attributes:
    label: System Information
    description: |
      Please tell us what OS you are using, your Python version, and the `swebench` package version if applicable.
    placeholder: >
      e.g. MacOS, Python 3.9, swebench 1.0.1
- type: markdown
  attributes:
    value: >
      Thanks for contributing 🎉!
