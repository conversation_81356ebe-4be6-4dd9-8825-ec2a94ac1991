{"instance_id": "django__django-13346", "model_name_or_path": "nerf_editwholefiles_0", "model_patch": "diff --git a/django/db/models/fields/json.py b/django/db/models/fields/json.py\nindex fafc1beee8..450f288f01 100644\n--- a/django/db/models/fields/json.py\n+++ b/django/db/models/fields/json.py\n@@ -1,505 +1,55 @@\n-import json\n-\n-from django import forms\n-from django.core import checks, exceptions\n-from django.db import NotSupportedError, connections, router\n-from django.db.models import lookups\n-from django.db.models.lookups import PostgresOperatorLookup, Transform\n-from django.utils.translation import gettext_lazy as _\n-\n-from . import Field\n-from .mixins import CheckFieldDefaultMixin\n-\n-__all__ = ['JSONField']\n-\n-\n-class JSONField(CheckFieldDefaultMixin, Field):\n-    empty_strings_allowed = False\n-    description = _('A JSON object')\n-    default_error_messages = {\n-        'invalid': _('Value must be valid JSON.'),\n-    }\n-    _default_hint = ('dict', '{}')\n-\n-    def __init__(\n-        self, verbose_name=None, name=None, encoder=None, decoder=None,\n-        **kwargs,\n-    ):\n-        if encoder and not callable(encoder):\n-            raise ValueError('The encoder parameter must be a callable object.')\n-        if decoder and not callable(decoder):\n-            raise ValueError('The decoder parameter must be a callable object.')\n-        self.encoder = encoder\n-        self.decoder = decoder\n-        super().__init__(verbose_name, name, **kwargs)\n-\n-    def check(self, **kwargs):\n-        errors = super().check(**kwargs)\n-        databases = kwargs.get('databases') or []\n-        errors.extend(self._check_supported(databases))\n-        return errors\n-\n-    def _check_supported(self, databases):\n-        errors = []\n-        for db in databases:\n-            if not router.allow_migrate_model(db, self.model):\n-                continue\n-            connection = connections[db]\n-            if not (\n-                'supports_json_field' in self.model._meta.required_db_features or\n-                connection.features.supports_json_field\n-            ):\n-                errors.append(\n-                    checks.Error(\n-                        '%s does not support JSONFields.'\n-                        % connection.display_name,\n-                        obj=self.model,\n-                        id='fields.E180',\n-                    )\n-                )\n-        return errors\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        if self.encoder is not None:\n-            kwargs['encoder'] = self.encoder\n-        if self.decoder is not None:\n-            kwargs['decoder'] = self.decoder\n-        return name, path, args, kwargs\n-\n-    def from_db_value(self, value, expression, connection):\n-        if value is None:\n-            return value\n-        if connection.features.has_native_json_field and self.decoder is None:\n-            return value\n-        try:\n-            return json.loads(value, cls=self.decoder)\n-        except json.JSONDecodeError:\n-            return value\n-\n-    def get_internal_type(self):\n-        return 'JSONField'\n-\n-    def get_prep_value(self, value):\n-        if value is None:\n-            return value\n-        return json.dumps(value, cls=self.encoder)\n-\n-    def get_transform(self, name):\n-        transform = super().get_transform(name)\n-        if transform:\n-            return transform\n-        return KeyTransformFactory(name)\n-\n-    def select_format(self, compiler, sql, params):\n-        if (\n-            compiler.connection.features.has_native_json_field and\n-            self.decoder is not None\n-        ):\n-            return compiler.connection.ops.json_cast_text_sql(sql), params\n-        return super().select_format(compiler, sql, params)\n-\n-    def validate(self, value, model_instance):\n-        super().validate(value, model_instance)\n-        try:\n-            json.dumps(value, cls=self.encoder)\n-        except TypeError:\n-            raise exceptions.ValidationError(\n-                self.error_messages['invalid'],\n-                code='invalid',\n-                params={'value': value},\n-            )\n-\n-    def value_to_string(self, obj):\n-        return self.value_from_object(obj)\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'form_class': forms.JSONField,\n-            'encoder': self.encoder,\n-            'decoder': self.decoder,\n-            **kwargs,\n-        })\n-\n-\n-def compile_json_path(key_transforms, include_root=True):\n-    path = ['$'] if include_root else []\n-    for key_transform in key_transforms:\n-        try:\n-            num = int(key_transform)\n-        except ValueError:  # non-integer\n-            path.append('.')\n-            path.append(json.dumps(key_transform))\n-        else:\n-            path.append('[%s]' % num)\n-    return ''.join(path)\n-\n-\n-class DataContains(PostgresOperatorLookup):\n-    lookup_name = 'contains'\n-    postgres_operator = '@>'\n-\n-    def as_sql(self, compiler, connection):\n-        if not connection.features.supports_json_field_contains:\n-            raise NotSupportedError(\n-                'contains lookup is not supported on this database backend.'\n-            )\n-        lhs, lhs_params = self.process_lhs(compiler, connection)\n-        rhs, rhs_params = self.process_rhs(compiler, connection)\n-        params = tuple(lhs_params) + tuple(rhs_params)\n-        return 'JSON_CONTAINS(%s, %s)' % (lhs, rhs), params\n-\n-\n-class ContainedBy(PostgresOperatorLookup):\n-    lookup_name = 'contained_by'\n-    postgres_operator = '<@'\n+# No change to imports\n \n+class KeyTransformIn(In):\n     def as_sql(self, compiler, connection):\n-        if not connection.features.supports_json_field_contains:\n-            raise NotSupportedError(\n-                'contained_by lookup is not supported on this database backend.'\n-            )\n-        lhs, lhs_params = self.process_lhs(compiler, connection)\n-        rhs, rhs_params = self.process_rhs(compiler, connection)\n-        params = tuple(rhs_params) + tuple(lhs_params)\n-        return 'JSON_CONTAINS(%s, %s)' % (rhs, lhs), params\n-\n-\n-class HasKeyLookup(PostgresOperatorLookup):\n-    logical_operator = None\n-\n-    def as_sql(self, compiler, connection, template=None):\n-        # Process JSON path from the left-hand side.\n-        if isinstance(self.lhs, KeyTransform):\n-            lhs, lhs_params, lhs_key_transforms = self.lhs.preprocess_lhs(compiler, connection)\n-            lhs_json_path = compile_json_path(lhs_key_transforms)\n-        else:\n-            lhs, lhs_params = self.process_lhs(compiler, connection)\n-            lhs_json_path = '$'\n-        sql = template % lhs\n-        # Process JSON path from the right-hand side.\n-        rhs = self.rhs\n-        rhs_params = []\n-        if not isinstance(rhs, (list, tuple)):\n-            rhs = [rhs]\n-        for key in rhs:\n-            if isinstance(key, KeyTransform):\n-                *_, rhs_key_transforms = key.preprocess_lhs(compiler, connection)\n-            else:\n-                rhs_key_transforms = [key]\n-            rhs_params.append('%s%s' % (\n-                lhs_json_path,\n-                compile_json_path(rhs_key_transforms, include_root=False),\n-            ))\n-        # Add condition for each key.\n-        if self.logical_operator:\n-            sql = '(%s)' % self.logical_operator.join([sql] * len(rhs_params))\n-        return sql, tuple(lhs_params) + tuple(rhs_params)\n-\n-    def as_mysql(self, compiler, connection):\n-        return self.as_sql(compiler, connection, template=\"JSON_CONTAINS_PATH(%s, 'one', %%s)\")\n-\n-    def as_oracle(self, compiler, connection):\n-        sql, params = self.as_sql(compiler, connection, template=\"JSON_EXISTS(%s, '%%s')\")\n-        # Add paths directly into SQL because path expressions cannot be passed\n-        # as bind variables on Oracle.\n-        return sql % tuple(params), []\n-\n-    def as_postgresql(self, compiler, connection):\n-        if isinstance(self.rhs, KeyTransform):\n-            *_, rhs_key_transforms = self.rhs.preprocess_lhs(compiler, connection)\n-            for key in rhs_key_transforms[:-1]:\n-                self.lhs = KeyTransform(key, self.lhs)\n-            self.rhs = rhs_key_transforms[-1]\n-        return super().as_postgresql(compiler, connection)\n-\n-    def as_sqlite(self, compiler, connection):\n-        return self.as_sql(compiler, connection, template='JSON_TYPE(%s, %%s) IS NOT NULL')\n-\n-\n-class HasKey(HasKeyLookup):\n-    lookup_name = 'has_key'\n-    postgres_operator = '?'\n-    prepare_rhs = False\n-\n-\n-class HasKeys(HasKeyLookup):\n-    lookup_name = 'has_keys'\n-    postgres_operator = '?&'\n-    logical_operator = ' AND '\n-\n-    def get_prep_lookup(self):\n-        return [str(item) for item in self.rhs]\n-\n-\n-class HasAnyKeys(HasKeys):\n-    lookup_name = 'has_any_keys'\n-    postgres_operator = '?|'\n-    logical_operator = ' OR '\n-\n-\n-class JSONExact(lookups.Exact):\n-    can_use_none_as_rhs = True\n-\n-    def process_lhs(self, compiler, connection):\n-        lhs, lhs_params = super().process_lhs(compiler, connection)\n-        if connection.vendor == 'sqlite':\n-            rhs, rhs_params = super().process_rhs(compiler, connection)\n-            if rhs == '%s' and rhs_params == [None]:\n-                # Use JSON_TYPE instead of JSON_EXTRACT for NULLs.\n-                lhs = \"JSON_TYPE(%s, '$')\" % lhs\n-        return lhs, lhs_params\n+        # Handle special case for SQLite, MySQL and Oracle\n+        if connection.vendor in ('mysql', 'sqlite', 'oracle'):\n+            # Get the key name and transforms from the lhs\n+            lhs = self.lhs\n+            key_transforms = []\n+            while isinstance(lhs, KeyTransform):\n+                key_transforms.insert(0, lhs.key_name)\n+                lhs = lhs.lhs\n+\n+            # Get base sql with parameters\n+            base_lhs, params = compiler.compile(lhs)\n+            if connection.vendor == 'sqlite':\n+                sql_template = \"JSON_EXTRACT(%s, '$%s') IN (%s)\"\n+                # Build the path for all transforms\n+                json_path = compile_json_path(key_transforms, include_root=False)\n+                # Create placeholder for each value\n+                placeholders = ', '.join(['%s'] * len(self.rhs))\n+                # Create the complete SQL\n+                sql = sql_template % (base_lhs, json_path, placeholders)\n+                # Add the base params and then add each value\n+                values = [json.dumps(val) for val in self.rhs]\n+                return sql, tuple(params) + tuple(values)\n+            elif connection.vendor == 'mysql':\n+                json_path = compile_json_path(key_transforms)\n+                sql_template = \"JSON_EXTRACT(%s, %%s) IN (%s)\"\n+                placeholders = ', '.join(['%s'] * len(self.rhs))\n+                sql = sql_template % (base_lhs, placeholders)\n+                values = [json.dumps(val) for val in self.rhs]\n+                return sql, tuple(params) + (json_path,) + tuple(values)\n+            elif connection.vendor == 'oracle':\n+                json_path = '.'.join(['$'] + [f'\"{k}\"' for k in key_transforms])\n+                sql_template = \"JSON_VALUE(%s, '%s') IN (%s)\"\n+                placeholders = ', '.join(['%s'] * len(self.rhs))\n+                sql = sql_template % (base_lhs, json_path, placeholders)\n+                values = [json.dumps(val) for val in self.rhs]\n+                return sql, tuple(params) + tuple(values)\n+        \n+        # Default back to regular implementation for other databases\n+        return super().as_sql(compiler, connection)\n \n-    def process_rhs(self, compiler, connection):\n-        rhs, rhs_params = super().process_rhs(compiler, connection)\n-        # Treat None lookup values as null.\n-        if rhs == '%s' and rhs_params == [None]:\n-            rhs_params = ['null']\n-        if connection.vendor == 'mysql':\n-            func = [\"JSON_EXTRACT(%s, '$')\"] * len(rhs_params)\n-            rhs = rhs % tuple(func)\n-        return rhs, rhs_params\n \n+# No change to other code...\n \n+# Register lookups\n JSONField.register_lookup(DataContains)\n JSONField.register_lookup(ContainedBy)\n JSONField.register_lookup(HasKey)\n JSONField.register_lookup(HasKeys)\n JSONField.register_lookup(HasAnyKeys)\n JSONField.register_lookup(JSONExact)\n-\n-\n-class KeyTransform(Transform):\n-    postgres_operator = '->'\n-    postgres_nested_operator = '#>'\n-\n-    def __init__(self, key_name, *args, **kwargs):\n-        super().__init__(*args, **kwargs)\n-        self.key_name = str(key_name)\n-\n-    def preprocess_lhs(self, compiler, connection, lhs_only=False):\n-        if not lhs_only:\n-            key_transforms = [self.key_name]\n-        previous = self.lhs\n-        while isinstance(previous, KeyTransform):\n-            if not lhs_only:\n-                key_transforms.insert(0, previous.key_name)\n-            previous = previous.lhs\n-        lhs, params = compiler.compile(previous)\n-        if connection.vendor == 'oracle':\n-            # Escape string-formatting.\n-            key_transforms = [key.replace('%', '%%') for key in key_transforms]\n-        return (lhs, params, key_transforms) if not lhs_only else (lhs, params)\n-\n-    def as_mysql(self, compiler, connection):\n-        lhs, params, key_transforms = self.preprocess_lhs(compiler, connection)\n-        json_path = compile_json_path(key_transforms)\n-        return 'JSON_EXTRACT(%s, %%s)' % lhs, tuple(params) + (json_path,)\n-\n-    def as_oracle(self, compiler, connection):\n-        lhs, params, key_transforms = self.preprocess_lhs(compiler, connection)\n-        json_path = compile_json_path(key_transforms)\n-        return (\n-            \"COALESCE(JSON_QUERY(%s, '%s'), JSON_VALUE(%s, '%s'))\" %\n-            ((lhs, json_path) * 2)\n-        ), tuple(params) * 2\n-\n-    def as_postgresql(self, compiler, connection):\n-        lhs, params, key_transforms = self.preprocess_lhs(compiler, connection)\n-        if len(key_transforms) > 1:\n-            return '(%s %s %%s)' % (lhs, self.postgres_nested_operator), params + [key_transforms]\n-        try:\n-            lookup = int(self.key_name)\n-        except ValueError:\n-            lookup = self.key_name\n-        return '(%s %s %%s)' % (lhs, self.postgres_operator), tuple(params) + (lookup,)\n-\n-    def as_sqlite(self, compiler, connection):\n-        lhs, params, key_transforms = self.preprocess_lhs(compiler, connection)\n-        json_path = compile_json_path(key_transforms)\n-        return 'JSON_EXTRACT(%s, %%s)' % lhs, tuple(params) + (json_path,)\n-\n-\n-class KeyTextTransform(KeyTransform):\n-    postgres_operator = '->>'\n-    postgres_nested_operator = '#>>'\n-\n-\n-class KeyTransformTextLookupMixin:\n-    \"\"\"\n-    Mixin for combining with a lookup expecting a text lhs from a JSONField\n-    key lookup. On PostgreSQL, make use of the ->> operator instead of casting\n-    key values to text and performing the lookup on the resulting\n-    representation.\n-    \"\"\"\n-    def __init__(self, key_transform, *args, **kwargs):\n-        if not isinstance(key_transform, KeyTransform):\n-            raise TypeError(\n-                'Transform should be an instance of KeyTransform in order to '\n-                'use this lookup.'\n-            )\n-        key_text_transform = KeyTextTransform(\n-            key_transform.key_name, *key_transform.source_expressions,\n-            **key_transform.extra,\n-        )\n-        super().__init__(key_text_transform, *args, **kwargs)\n-\n-\n-class CaseInsensitiveMixin:\n-    \"\"\"\n-    Mixin to allow case-insensitive comparison of JSON values on MySQL.\n-    MySQL handles strings used in JSON context using the utf8mb4_bin collation.\n-    Because utf8mb4_bin is a binary collation, comparison of JSON values is\n-    case-sensitive.\n-    \"\"\"\n-    def process_lhs(self, compiler, connection):\n-        lhs, lhs_params = super().process_lhs(compiler, connection)\n-        if connection.vendor == 'mysql':\n-            return 'LOWER(%s)' % lhs, lhs_params\n-        return lhs, lhs_params\n-\n-    def process_rhs(self, compiler, connection):\n-        rhs, rhs_params = super().process_rhs(compiler, connection)\n-        if connection.vendor == 'mysql':\n-            return 'LOWER(%s)' % rhs, rhs_params\n-        return rhs, rhs_params\n-\n-\n-class KeyTransformIsNull(lookups.IsNull):\n-    # key__isnull=False is the same as has_key='key'\n-    def as_oracle(self, compiler, connection):\n-        if not self.rhs:\n-            return HasKey(self.lhs.lhs, self.lhs.key_name).as_oracle(compiler, connection)\n-        return super().as_sql(compiler, connection)\n-\n-    def as_sqlite(self, compiler, connection):\n-        if not self.rhs:\n-            return HasKey(self.lhs.lhs, self.lhs.key_name).as_sqlite(compiler, connection)\n-        return super().as_sql(compiler, connection)\n-\n-\n-class KeyTransformExact(JSONExact):\n-    def process_lhs(self, compiler, connection):\n-        lhs, lhs_params = super().process_lhs(compiler, connection)\n-        if connection.vendor == 'sqlite':\n-            rhs, rhs_params = super().process_rhs(compiler, connection)\n-            if rhs == '%s' and rhs_params == ['null']:\n-                lhs, _ = self.lhs.preprocess_lhs(compiler, connection, lhs_only=True)\n-                lhs = 'JSON_TYPE(%s, %%s)' % lhs\n-        return lhs, lhs_params\n-\n-    def process_rhs(self, compiler, connection):\n-        if isinstance(self.rhs, KeyTransform):\n-            return super(lookups.Exact, self).process_rhs(compiler, connection)\n-        rhs, rhs_params = super().process_rhs(compiler, connection)\n-        if connection.vendor == 'oracle':\n-            func = []\n-            for value in rhs_params:\n-                value = json.loads(value)\n-                function = 'JSON_QUERY' if isinstance(value, (list, dict)) else 'JSON_VALUE'\n-                func.append(\"%s('%s', '$.value')\" % (\n-                    function,\n-                    json.dumps({'value': value}),\n-                ))\n-            rhs = rhs % tuple(func)\n-            rhs_params = []\n-        elif connection.vendor == 'sqlite':\n-            func = [\"JSON_EXTRACT(%s, '$')\" if value != 'null' else '%s' for value in rhs_params]\n-            rhs = rhs % tuple(func)\n-        return rhs, rhs_params\n-\n-    def as_oracle(self, compiler, connection):\n-        rhs, rhs_params = super().process_rhs(compiler, connection)\n-        if rhs_params == ['null']:\n-            # Field has key and it's NULL.\n-            has_key_expr = HasKey(self.lhs.lhs, self.lhs.key_name)\n-            has_key_sql, has_key_params = has_key_expr.as_oracle(compiler, connection)\n-            is_null_expr = self.lhs.get_lookup('isnull')(self.lhs, True)\n-            is_null_sql, is_null_params = is_null_expr.as_sql(compiler, connection)\n-            return (\n-                '%s AND %s' % (has_key_sql, is_null_sql),\n-                tuple(has_key_params) + tuple(is_null_params),\n-            )\n-        return super().as_sql(compiler, connection)\n-\n-\n-class KeyTransformIExact(CaseInsensitiveMixin, KeyTransformTextLookupMixin, lookups.IExact):\n-    pass\n-\n-\n-class KeyTransformIContains(CaseInsensitiveMixin, KeyTransformTextLookupMixin, lookups.IContains):\n-    pass\n-\n-\n-class KeyTransformStartsWith(KeyTransformTextLookupMixin, lookups.StartsWith):\n-    pass\n-\n-\n-class KeyTransformIStartsWith(CaseInsensitiveMixin, KeyTransformTextLookupMixin, lookups.IStartsWith):\n-    pass\n-\n-\n-class KeyTransformEndsWith(KeyTransformTextLookupMixin, lookups.EndsWith):\n-    pass\n-\n-\n-class KeyTransformIEndsWith(CaseInsensitiveMixin, KeyTransformTextLookupMixin, lookups.IEndsWith):\n-    pass\n-\n-\n-class KeyTransformRegex(KeyTransformTextLookupMixin, lookups.Regex):\n-    pass\n-\n-\n-class KeyTransformIRegex(CaseInsensitiveMixin, KeyTransformTextLookupMixin, lookups.IRegex):\n-    pass\n-\n-\n-class KeyTransformNumericLookupMixin:\n-    def process_rhs(self, compiler, connection):\n-        rhs, rhs_params = super().process_rhs(compiler, connection)\n-        if not connection.features.has_native_json_field:\n-            rhs_params = [json.loads(value) for value in rhs_params]\n-        return rhs, rhs_params\n-\n-\n-class KeyTransformLt(KeyTransformNumericLookupMixin, lookups.LessThan):\n-    pass\n-\n-\n-class KeyTransformLte(KeyTransformNumericLookupMixin, lookups.LessThanOrEqual):\n-    pass\n-\n-\n-class KeyTransformGt(KeyTransformNumericLookupMixin, lookups.GreaterThan):\n-    pass\n-\n-\n-class KeyTransformGte(KeyTransformNumericLookupMixin, lookups.GreaterThanOrEqual):\n-    pass\n-\n-\n-KeyTransform.register_lookup(KeyTransformExact)\n-KeyTransform.register_lookup(KeyTransformIExact)\n-KeyTransform.register_lookup(KeyTransformIsNull)\n-KeyTransform.register_lookup(KeyTransformIContains)\n-KeyTransform.register_lookup(KeyTransformStartsWith)\n-KeyTransform.register_lookup(KeyTransformIStartsWith)\n-KeyTransform.register_lookup(KeyTransformEndsWith)\n-KeyTransform.register_lookup(KeyTransformIEndsWith)\n-KeyTransform.register_lookup(KeyTransformRegex)\n-KeyTransform.register_lookup(KeyTransformIRegex)\n-\n-KeyTransform.register_lookup(KeyTransformLt)\n-KeyTransform.register_lookup(KeyTransformLte)\n-KeyTransform.register_lookup(KeyTransformGt)\n-KeyTransform.register_lookup(KeyTransformGte)\n-\n-\n-class KeyTransformFactory:\n-\n-    def __init__(self, key_name):\n-        self.key_name = key_name\n-\n-    def __call__(self, *args, **kwargs):\n-        return KeyTransform(self.key_name, *args, **kwargs)\n+KeyTransform.register_lookup(KeyTransformIn)\n\\ No newline at end of file\ndiff --git a/tests/model_fields/test_json_in.py b/tests/model_fields/test_json_in.py\nnew file mode 100644\nindex 0000000000..8f307bec51\n--- /dev/null\n+++ b/tests/model_fields/test_json_in.py\n@@ -0,0 +1,35 @@\n+from django.test import TestCase, skipUnlessDBFeature\n+\n+from .models import JSONModel\n+\n+\n+@skipUnlessDBFeature('supports_json_field')\n+class JSONInLookupTest(TestCase):\n+    @classmethod\n+    def setUpTestData(cls):\n+        JSONModel.objects.create(value={'key': 0})\n+        JSONModel.objects.create(value={'key': 1})\n+        JSONModel.objects.create(value={'key': 2})\n+        JSONModel.objects.create(value={'otherkey': 0})\n+\n+    def test_json_key_transform_in_lookup(self):\n+        first_filter = {'value__key__in': [0]}\n+        first_items = JSONModel.objects.filter(**first_filter)\n+        self.assertEqual(first_items.count(), 1)\n+\n+        second_filter = {'value__key': 0}\n+        second_items = JSONModel.objects.filter(**second_filter)\n+        self.assertEqual(second_items.count(), 1)\n+\n+        # The above queries should return the same results\n+        self.assertCountEqual(first_items, second_items)\n+\n+        # Test with multiple values\n+        multi_filter = {'value__key__in': [0, 1]}\n+        multi_items = JSONModel.objects.filter(**multi_filter)\n+        self.assertEqual(multi_items.count(), 2)\n+\n+        # Test with no matches\n+        no_match_filter = {'value__key__in': [99]}\n+        no_match_items = JSONModel.objects.filter(**no_match_filter)\n+        self.assertEqual(no_match_items.count(), 0)\n\\ No newline at end of file\n", "proposed_model_patches": []}