+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=2
++ CONDA_SHLVL=2
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_1=/opt/miniconda3
++ CONDA_PREFIX_1=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ cd /testbed
+ git config --global --add safe.directory /testbed
+ cd /testbed
+ git status
On branch master
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   tox.ini

Untracked files:
  (use "git add <file>..." to include in what will be committed)
	setup.py.orig

no changes added to commit (use "git add" and/or "git commit -a")
+ git show
commit 1e2ccd8f0eca0870cf6f8fce6934e2da8eba9b72
Merge: 9175da437 f2c0dfe7c
Author: Takeshi KOMIYA <<EMAIL>>
Date:   Sat Oct 3 16:13:18 2020 +0900

    Merge pull request #8007 from tk0miya/6518_autodoc_typehints_evaluation
    
    Close #6518: autodoc: Add autodoc_type_aliases

+ git diff 1e2ccd8f0eca0870cf6f8fce6934e2da8eba9b72
diff --git a/tox.ini b/tox.ini
index a61299979..e2baccc07 100644
--- a/tox.ini
+++ b/tox.ini
@@ -28,7 +28,7 @@ setenv =
     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils
     PYTEST_ADDOPTS = --color yes
 commands=
-    pytest --durations 25 {posargs}
+    pytest -rA --durations 25 {posargs}
 
 [testenv:flake8]
 basepython = python3
+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z x ']'
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1='(testbed) '
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=3
+++ CONDA_SHLVL=3
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=4
++ CONDA_SHLVL=4
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_3=/opt/miniconda3
++ CONDA_PREFIX_3=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ python -m pip install -e '.[test]'
Obtaining file:///testbed
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: sphinxcontrib-applehelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (1.0.4)
Requirement already satisfied: sphinxcontrib-devhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (1.0.2)
Requirement already satisfied: sphinxcontrib-jsmath in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (1.0.1)
Requirement already satisfied: sphinxcontrib-htmlhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (2.0.1)
Requirement already satisfied: sphinxcontrib-serializinghtml in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (1.1.5)
Requirement already satisfied: sphinxcontrib-qthelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (1.0.3)
Requirement already satisfied: Jinja2>=2.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (2.11.3)
Requirement already satisfied: Pygments>=2.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (2.19.1)
Requirement already satisfied: docutils>=0.12 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (0.21.2)
Requirement already satisfied: snowballstemmer>=1.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (2.2.0)
Requirement already satisfied: babel>=1.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (2.17.0)
Requirement already satisfied: alabaster<0.8,>=0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (0.7.11)
Requirement already satisfied: imagesize in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (1.4.1)
Requirement already satisfied: requests>=2.5.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (2.32.3)
Requirement already satisfied: setuptools in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (75.8.0)
Requirement already satisfied: packaging in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (24.2)
Requirement already satisfied: pytest in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (8.3.4)
Requirement already satisfied: pytest-cov in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (6.0.0)
Requirement already satisfied: html5lib in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (1.1)
Requirement already satisfied: typed_ast in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (1.5.5)
Requirement already satisfied: cython in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.3.0.dev20250315) (3.0.11)
Requirement already satisfied: MarkupSafe>=0.23 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Jinja2>=2.3->Sphinx==3.3.0.dev20250315) (2.0.1)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.3.0.dev20250315) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.3.0.dev20250315) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.3.0.dev20250315) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.3.0.dev20250315) (2025.1.31)
Requirement already satisfied: six>=1.9 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.3.0.dev20250315) (1.17.0)
Requirement already satisfied: webencodings in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.3.0.dev20250315) (0.5.1)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.3.0.dev20250315) (1.2.2)
Requirement already satisfied: iniconfig in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.3.0.dev20250315) (2.0.0)
Requirement already satisfied: pluggy<2,>=1.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.3.0.dev20250315) (1.5.0)
Requirement already satisfied: tomli>=1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.3.0.dev20250315) (2.2.1)
Requirement already satisfied: coverage>=7.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from coverage[toml]>=7.5->pytest-cov->Sphinx==3.3.0.dev20250315) (7.6.10)
Installing collected packages: Sphinx
  Attempting uninstall: Sphinx
    Found existing installation: Sphinx 3.3.0.dev20250204
    Uninstalling Sphinx-3.3.0.dev20250204:
      Successfully uninstalled Sphinx-3.3.0.dev20250204
  DEPRECATION: Legacy editable install of Sphinx[test]==3.3.0.dev20250315 from file:///testbed (setup.py develop) is deprecated. pip 25.1 will enforce this behaviour change. A possible replacement is to add a pyproject.toml or enable --use-pep517, and use setuptools >= 64. If the resulting installation is not behaving as expected, try using --config-settings editable_mode=compat. Please consult the setuptools documentation for more information. Discussion can be found at https://github.com/pypa/pip/issues/11457
  Running setup.py develop for Sphinx
Successfully installed Sphinx
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
+ git checkout 1e2ccd8f0eca0870cf6f8fce6934e2da8eba9b72 tests/test_build_linkcheck.py
Updated 0 paths from 6ddd64aa6
+ git apply -v -
Checking patch tests/roots/test-linkcheck-localserver/conf.py...
Checking patch tests/roots/test-linkcheck-localserver/index.rst...
Checking patch tests/test_build_linkcheck.py...
Applied patch tests/roots/test-linkcheck-localserver/conf.py cleanly.
Applied patch tests/roots/test-linkcheck-localserver/index.rst cleanly.
Applied patch tests/test_build_linkcheck.py cleanly.
+ tox --current-env -epy39 -v -- tests/roots/test-linkcheck-localserver/conf.py tests/roots/test-linkcheck-localserver/index.rst tests/test_build_linkcheck.py
py39: commands[0]> pytest -rA --durations 25 tests/roots/test-linkcheck-localserver/conf.py tests/roots/test-linkcheck-localserver/index.rst tests/test_build_linkcheck.py
[1m============================= test session starts ==============================[0m
platform linux -- Python 3.9.21, pytest-8.3.4, pluggy-1.5.0
cachedir: .tox/py39/.pytest_cache
libraries: Sphinx-3.3.0+/1e2ccd8f0, docutils-0.21.2
base tempdir: /tmp/pytest-of-root/pytest-0
rootdir: /testbed
configfile: setup.cfg
plugins: cov-6.0.0
collected 6 items

tests/test_build_linkcheck.py [32m.[0m[32m.[0m[32m.[0m[31mF[0m[32m.[0m[32m.[0m[31m                                     [100%][0m

=================================== FAILURES ===================================
[31m[1m________________________ test_raises_for_invalid_status ________________________[0m

app = <SphinxTestApp buildername='linkcheck'>
status = <_io.StringIO object at 0x7b977aada550>
warning = <_io.StringIO object at 0x7b977aada5e0>

    [0m[37m@pytest[39;49;00m.mark.sphinx([33m'[39;49;00m[33mlinkcheck[39;49;00m[33m'[39;49;00m, testroot=[33m'[39;49;00m[33mlinkcheck-localserver[39;49;00m[33m'[39;49;00m, freshenv=[94mTrue[39;49;00m)[90m[39;49;00m
    [94mdef[39;49;00m[90m [39;49;00m[92mtest_raises_for_invalid_status[39;49;00m(app, status, warning):[90m[39;49;00m
        server_thread = HttpServerThread(InternalServerErrorHandler, daemon=[94mTrue[39;49;00m)[90m[39;49;00m
        server_thread.start()[90m[39;49;00m
        [94mtry[39;49;00m:[90m[39;49;00m
            app.builder.build_all()[90m[39;49;00m
        [94mfinally[39;49;00m:[90m[39;49;00m
            server_thread.terminate()[90m[39;49;00m
        content = (app.outdir / [33m'[39;49;00m[33moutput.txt[39;49;00m[33m'[39;49;00m).read_text()[90m[39;49;00m
>       [94massert[39;49;00m content == ([90m[39;49;00m
            [33m"[39;49;00m[33mindex.rst:1: [broken] http://localhost:7777/#anchor: [39;49;00m[33m"[39;49;00m[90m[39;49;00m
            [33m"[39;49;00m[33m500 Server Error: Internal Server Error [39;49;00m[33m"[39;49;00m[90m[39;49;00m
            [33m"[39;49;00m[33mfor url: http://localhost:7777/[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
        )[90m[39;49;00m
[1m[31mE       assert "index.rst:1:...' not found\n" == 'index.rst:1:...lhost:7777/\n'[0m
[1m[31mE         [0m
[1m[31mE         Skipping 43 identical leading characters in diff, use -v to show[0m
[1m[31mE         - /#anchor: 500 Server Error: Internal Server Error for url: http://localhost:7777/[0m
[1m[31mE         + /#anchor: Anchor 'anchor' not found[0m

[1m[31mtests/test_build_linkcheck.py[0m:120: AssertionError
----------------------------- Captured stderr call -----------------------------
127.0.0.1 - - [15/Mar/2025 02:31:48] code 500, message Internal Server Error
127.0.0.1 - - [15/Mar/2025 02:31:48] "GET / HTTP/1.1" 500 -
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: linkcheck
# srcdir: /tmp/pytest-of-root/pytest-0/linkcheck-localserver
# outdir: /tmp/pytest-of-root/pytest-0/linkcheck-localserver/_build/linkcheck
# status: 
[01mRunning Sphinx v3.3.0+/1e2ccd8f0[39;49;00m
[01mbuilding [linkcheck]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
(line    1) [91mbroken    [39;49;00mhttp://localhost:7777/#anchor[91m - Anchor 'anchor' not found[39;49;00m


# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

[33m=============================== warnings summary ===============================[0m
sphinx/util/docutils.py:45
  /testbed/sphinx/util/docutils.py:45: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
    __version_info__ = tuple(LooseVersion(docutils.__version__).version)

sphinx/registry.py:22
  /testbed/sphinx/registry.py:22: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
    from pkg_resources import iter_entry_points

../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('sphinxcontrib')`.
  Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
    declare_namespace(pkg)

sphinx/directives/patches.py:15
  /testbed/sphinx/directives/patches.py:15: DeprecationWarning: The `docutils.parsers.rst.directive.html` module will be removed in Docutils 2.0. Since Docutils 0.18, the "Meta" node is defined in `docutils.parsers.rst.directives.misc`.
    from docutils.parsers.rst.directives import images, html, tables

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/__init__.py:211: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse():  # type: Node

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/i18n.py:95: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.translatable):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/__init__.py:111: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for ref in self.document.traverse(nodes.substitution_reference):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/__init__.py:132: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.target):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/__init__.py:151: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.block_quote):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/__init__.py:176: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.Element):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/__init__.py:223: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.index):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/references.py:30: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.substitution_definition):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/__init__.py:190: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.section):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/__init__.py:280: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.doctest_block):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/domains/citation.py:117: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.citation):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/domains/citation.py:136: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.citation_reference):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/builders/latex/transforms.py:37: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: nodes.Element

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/__init__.py:292: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: Element

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/util/compat.py:44: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.index):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/domains/index.py:52: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in document.traverse(addnodes.index):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/domains/math.py:85: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    self.data['has_equations'][docname] = any(document.traverse(math_node))

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/environment/collectors/asset.py:47: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/environment/collectors/asset.py:128: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(addnodes.download_reference):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/environment/collectors/title.py:46: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.section):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/__init__.py:302: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.system_message):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/__init__.py:391: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.manpage):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/i18n.py:488: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for inline in self.document.traverse(matcher):  # type: nodes.inline

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/domains/c.py:3471: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(AliasNode):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/domains/cpp.py:7042: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(AliasNode):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/post_transforms/__init__.py:71: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.pending_xref):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/util/nodes.py:598: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in document.traverse(addnodes.only):

tests/test_build_linkcheck.py: 12 warnings
  /testbed/sphinx/transforms/post_transforms/images.py:35: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.image):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/post_transforms/__init__.py:215: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.desc_sig_element):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/builders/latex/transforms.py:595: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.title):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/post_transforms/code.py:44: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.highlightlang):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/post_transforms/code.py:96: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for lbnode in self.document.traverse(nodes.literal_block):  # type: nodes.literal_block

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/transforms/post_transforms/code.py:100: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for dbnode in self.document.traverse(nodes.doctest_block):  # type: nodes.doctest_block

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/environment/__init__.py:542: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for toctreenode in doctree.traverse(addnodes.toctree):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/builders/linkcheck.py:329: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for refnode in doctree.traverse(nodes.reference):

tests/test_build_linkcheck.py::test_defaults
tests/test_build_linkcheck.py::test_defaults_json
tests/test_build_linkcheck.py::test_anchors_ignored
tests/test_build_linkcheck.py::test_raises_for_invalid_status
tests/test_build_linkcheck.py::test_auth
tests/test_build_linkcheck.py::test_linkcheck_request_headers
  /testbed/sphinx/builders/linkcheck.py:338: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for imgnode in doctree.traverse(nodes.image):

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
==================================== PASSES ====================================
[32m[1m________________________________ test_defaults _________________________________[0m
----------------------------- Captured stdout call -----------------------------
links.txt:13: [broken] https://localhost:7777/doesnotexist: HTTPSConnectionPool(host='localhost', port=7777): Max retries exceeded with url: /doesnotexist (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7b9779e247f0>: Failed to establish a new connection: [Errno 111] Connection refused'))
links.txt:15: [broken] path/to/notfound: 
links.txt:11: [broken] https://www.google.com/#top: Anchor 'top' not found
links.txt:17: [broken] https://www.google.com/image.png: 404 Client Error: Not Found for url: https://www.google.com/image.png
links.txt:18: [broken] https://www.google.com/image2.png: 404 Client Error: Not Found for url: https://www.google.com/image2.png
links.txt:12: [broken] http://www.sphinx-doc.org/en/1.7/intro.html#does-not-exist: Anchor 'does-not-exist' not found

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: linkcheck
# srcdir: /tmp/pytest-of-root/pytest-0/linkcheck
# outdir: /tmp/pytest-of-root/pytest-0/linkcheck/_build/linkcheck
# status: 
[01mRunning Sphinx v3.3.0+/1e2ccd8f0[39;49;00m
[01mbuilding [linkcheck]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mlinks[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mlinks[39;49;00m                                                 
(line   10) [32mok        [39;49;00mhttps://www.google.com#!bar
(line   13) [91mbroken    [39;49;00mhttps://localhost:7777/doesnotexist[91m - HTTPSConnectionPool(host='localhost', port=7777): Max retries exceeded with url: /doesnotexist (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7b9779e247f0>: Failed to establish a new connection: [Errno 111] Connection refused'))[39;49;00m
(line   14) [32mok        [39;49;00mconf.py
(line   15) [91mbroken    [39;49;00mpath/to/notfound[91m - [39;49;00m
(line    9) [32mok        [39;49;00mhttps://www.google.com/#!bar
(line   11) [91mbroken    [39;49;00mhttps://www.google.com/#top[91m - Anchor 'top' not found[39;49;00m
(line    3) [32mok        [39;49;00mhttps://www.w3.org/TR/2006/REC-xml-names-20060816/#defaulting
(line   17) [91mbroken    [39;49;00mhttps://www.google.com/image.png[91m - 404 Client Error: Not Found for url: https://www.google.com/image.png[39;49;00m
(line   18) [91mbroken    [39;49;00mhttps://www.google.com/image2.png[91m - 404 Client Error: Not Found for url: https://www.google.com/image2.png[39;49;00m
(line   12) [91mbroken    [39;49;00mhttp://www.sphinx-doc.org/en/1.7/intro.html#does-not-exist[91m - Anchor 'does-not-exist' not found[39;49;00m


# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:9: WARNING: Duplicate explicit target name: "example bar invalid".[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:9: WARNING: Duplicate explicit target name: "example bar invalid".[39;49;00m
[31m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:5: WARNING: Unknown target name: "http://www.sphinx-doc.org/en/1.4.8/tutorial.html#install-sphinx".[39;49;00m

[32m[1m______________________________ test_defaults_json ______________________________[0m
----------------------------- Captured stdout call -----------------------------
{"filename": "links.txt", "lineno": 10, "status": "working", "code": 0, "uri": "https://www.google.com#!bar", "info": ""}
{"filename": "links.txt", "lineno": 13, "status": "broken", "code": 0, "uri": "https://localhost:7777/doesnotexist", "info": "HTTPSConnectionPool(host='localhost', port=7777): Max retries exceeded with url: /doesnotexist (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7b97792064c0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}
{"filename": "links.txt", "lineno": 14, "status": "working", "code": 0, "uri": "conf.py", "info": ""}
{"filename": "links.txt", "lineno": 15, "status": "broken", "code": 0, "uri": "path/to/notfound", "info": ""}
{"filename": "links.txt", "lineno": 9, "status": "working", "code": 0, "uri": "https://www.google.com/#!bar", "info": ""}
{"filename": "links.txt", "lineno": 11, "status": "broken", "code": 0, "uri": "https://www.google.com/#top", "info": "Anchor 'top' not found"}
{"filename": "links.txt", "lineno": 3, "status": "working", "code": 0, "uri": "https://www.w3.org/TR/2006/REC-xml-names-20060816/#defaulting", "info": ""}
{"filename": "links.txt", "lineno": 18, "status": "broken", "code": 0, "uri": "https://www.google.com/image2.png", "info": "404 Client Error: Not Found for url: https://www.google.com/image2.png"}
{"filename": "links.txt", "lineno": 17, "status": "broken", "code": 0, "uri": "https://www.google.com/image.png", "info": "404 Client Error: Not Found for url: https://www.google.com/image.png"}
{"filename": "links.txt", "lineno": 12, "status": "broken", "code": 0, "uri": "http://www.sphinx-doc.org/en/1.7/intro.html#does-not-exist", "info": "Anchor 'does-not-exist' not found"}

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: linkcheck
# srcdir: /tmp/pytest-of-root/pytest-0/linkcheck
# outdir: /tmp/pytest-of-root/pytest-0/linkcheck/_build/linkcheck
# status: 
[01mRunning Sphinx v3.3.0+/1e2ccd8f0[39;49;00m
[01mbuilding [linkcheck]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mlinks[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mlinks[39;49;00m                                                 
(line   10) [32mok        [39;49;00mhttps://www.google.com#!bar
(line   13) [91mbroken    [39;49;00mhttps://localhost:7777/doesnotexist[91m - HTTPSConnectionPool(host='localhost', port=7777): Max retries exceeded with url: /doesnotexist (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x7b97792064c0>: Failed to establish a new connection: [Errno 111] Connection refused'))[39;49;00m
(line   14) [32mok        [39;49;00mconf.py
(line   15) [91mbroken    [39;49;00mpath/to/notfound[91m - [39;49;00m
(line    9) [32mok        [39;49;00mhttps://www.google.com/#!bar
(line   11) [91mbroken    [39;49;00mhttps://www.google.com/#top[91m - Anchor 'top' not found[39;49;00m
(line    3) [32mok        [39;49;00mhttps://www.w3.org/TR/2006/REC-xml-names-20060816/#defaulting
(line   18) [91mbroken    [39;49;00mhttps://www.google.com/image2.png[91m - 404 Client Error: Not Found for url: https://www.google.com/image2.png[39;49;00m
(line   17) [91mbroken    [39;49;00mhttps://www.google.com/image.png[91m - 404 Client Error: Not Found for url: https://www.google.com/image.png[39;49;00m
(line   12) [91mbroken    [39;49;00mhttp://www.sphinx-doc.org/en/1.7/intro.html#does-not-exist[91m - Anchor 'does-not-exist' not found[39;49;00m


# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:9: WARNING: Duplicate explicit target name: "example bar invalid".[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:9: WARNING: Duplicate explicit target name: "example bar invalid".[39;49;00m
[31m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:5: WARNING: Unknown target name: "http://www.sphinx-doc.org/en/1.4.8/tutorial.html#install-sphinx".[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: linkcheck
# srcdir: /tmp/pytest-of-root/pytest-0/linkcheck
# outdir: /tmp/pytest-of-root/pytest-0/linkcheck/_build/linkcheck
# status: 
[01mRunning Sphinx v3.3.0+/1e2ccd8f0[39;49;00m
[01mbuilding [linkcheck]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mlinks[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mlinks[39;49;00m                                                 
(line   12) [90m-ignored- [39;49;00mhttp://www.sphinx-doc.org/en/1.7/intro.html#does-not-exist
(line   13) [90m-ignored- [39;49;00mhttps://localhost:7777/doesnotexist
(line   14) [32mok        [39;49;00mconf.py
(line   15) [90m-ignored- [39;49;00mpath/to/notfound
(line   17) [90m-ignored- [39;49;00mhttps://www.google.com/image.png
(line   18) [90m-ignored- [39;49;00mhttps://www.google.com/image2.png
(line   11) [32mok        [39;49;00mhttps://www.google.com/#top
(line    9) [32mok        [39;49;00mhttps://www.google.com/#!bar
(line   10) [32mok        [39;49;00mhttps://www.google.com#!bar
(line    3) [32mok        [39;49;00mhttps://www.w3.org/TR/2006/REC-xml-names-20060816/#defaulting


# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:9: WARNING: Duplicate explicit target name: "example bar invalid".[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:9: WARNING: Duplicate explicit target name: "example bar invalid".[39;49;00m
[31m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:5: WARNING: Unknown target name: "http://www.sphinx-doc.org/en/1.4.8/tutorial.html#install-sphinx".[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: linkcheck
# srcdir: /tmp/pytest-of-root/pytest-0/linkcheck
# outdir: /tmp/pytest-of-root/pytest-0/linkcheck/_build/linkcheck
# status: 
[01mRunning Sphinx v3.3.0+/1e2ccd8f0[39;49;00m
[01mbuilding [linkcheck]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mlinks[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mlinks[39;49;00m                                                 
(line    3) [91mbroken    [39;49;00mhttps://www.w3.org/TR/2006/REC-xml-names-20060816/#defaulting[91m - 'str' object has no attribute 'iter_content'[39;49;00m
(line    9) [91mbroken    [39;49;00mhttps://www.google.com/#!bar[91m - 'str' object has no attribute 'raise_for_status'[39;49;00m
(line   10) [91mbroken    [39;49;00mhttps://www.google.com#!bar[91m - 'str' object has no attribute 'raise_for_status'[39;49;00m
(line   11) [91mbroken    [39;49;00mhttps://www.google.com/#top[91m - 'str' object has no attribute 'iter_content'[39;49;00m
(line   12) [91mbroken    [39;49;00mhttp://www.sphinx-doc.org/en/1.7/intro.html#does-not-exist[91m - 'str' object has no attribute 'iter_content'[39;49;00m
(line   13) [91mbroken    [39;49;00mhttps://localhost:7777/doesnotexist[91m - 'str' object has no attribute 'raise_for_status'[39;49;00m
(line   15) [91mbroken    [39;49;00mpath/to/notfound[91m - [39;49;00m
(line   17) [91mbroken    [39;49;00mhttps://www.google.com/image.png[91m - 'str' object has no attribute 'raise_for_status'[39;49;00m
(line   18) [91mbroken    [39;49;00mhttps://www.google.com/image2.png[91m - 'str' object has no attribute 'raise_for_status'[39;49;00m
(line   14) [32mok        [39;49;00mconf.py


# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:9: WARNING: Duplicate explicit target name: "example bar invalid".[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:9: WARNING: Duplicate explicit target name: "example bar invalid".[39;49;00m
[31m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:5: WARNING: Unknown target name: "http://www.sphinx-doc.org/en/1.4.8/tutorial.html#install-sphinx".[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: linkcheck
# srcdir: /tmp/pytest-of-root/pytest-0/linkcheck
# outdir: /tmp/pytest-of-root/pytest-0/linkcheck/_build/linkcheck
# status: 
[01mRunning Sphinx v3.3.0+/1e2ccd8f0[39;49;00m
[01mbuilding [linkcheck]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mlinks[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mlinks[39;49;00m                                                 
(line    3) [91mbroken    [39;49;00mhttps://www.w3.org/TR/2006/REC-xml-names-20060816/#defaulting[91m - 'str' object has no attribute 'iter_content'[39;49;00m
(line    9) [91mbroken    [39;49;00mhttps://www.google.com/#!bar[91m - 'str' object has no attribute 'raise_for_status'[39;49;00m
(line   10) [91mbroken    [39;49;00mhttps://www.google.com#!bar[91m - 'str' object has no attribute 'raise_for_status'[39;49;00m
(line   11) [91mbroken    [39;49;00mhttps://www.google.com/#top[91m - 'str' object has no attribute 'iter_content'[39;49;00m
(line   12) [91mbroken    [39;49;00mhttp://www.sphinx-doc.org/en/1.7/intro.html#does-not-exist[91m - 'str' object has no attribute 'iter_content'[39;49;00m
(line   13) [91mbroken    [39;49;00mhttps://localhost:7777/doesnotexist[91m - 'str' object has no attribute 'raise_for_status'[39;49;00m
(line   15) [91mbroken    [39;49;00mpath/to/notfound[91m - [39;49;00m
(line   17) [91mbroken    [39;49;00mhttps://www.google.com/image.png[91m - 'str' object has no attribute 'raise_for_status'[39;49;00m
(line   18) [91mbroken    [39;49;00mhttps://www.google.com/image2.png[91m - 'str' object has no attribute 'raise_for_status'[39;49;00m
(line   14) [32mok        [39;49;00mconf.py


# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:9: WARNING: Duplicate explicit target name: "example bar invalid".[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:9: WARNING: Duplicate explicit target name: "example bar invalid".[39;49;00m
[31m/tmp/pytest-of-root/pytest-0/linkcheck/links.txt:5: WARNING: Unknown target name: "http://www.sphinx-doc.org/en/1.4.8/tutorial.html#install-sphinx".[39;49;00m

============================= slowest 25 durations =============================
0.86s call     tests/test_build_linkcheck.py::test_defaults
0.55s call     tests/test_build_linkcheck.py::test_defaults_json
0.29s setup    tests/test_build_linkcheck.py::test_defaults
0.18s call     tests/test_build_linkcheck.py::test_anchors_ignored
0.02s call     tests/test_build_linkcheck.py::test_raises_for_invalid_status
0.01s call     tests/test_build_linkcheck.py::test_auth
0.01s call     tests/test_build_linkcheck.py::test_linkcheck_request_headers
0.01s setup    tests/test_build_linkcheck.py::test_raises_for_invalid_status
0.01s setup    tests/test_build_linkcheck.py::test_anchors_ignored
0.01s setup    tests/test_build_linkcheck.py::test_defaults_json
0.01s setup    tests/test_build_linkcheck.py::test_auth
0.01s setup    tests/test_build_linkcheck.py::test_linkcheck_request_headers

(6 durations < 0.005s hidden.  Use -vv to show these durations.)
[36m[1m=========================== short test summary info ============================[0m
[32mPASSED[0m tests/test_build_linkcheck.py::[1mtest_defaults[0m
[32mPASSED[0m tests/test_build_linkcheck.py::[1mtest_defaults_json[0m
[32mPASSED[0m tests/test_build_linkcheck.py::[1mtest_anchors_ignored[0m
[32mPASSED[0m tests/test_build_linkcheck.py::[1mtest_auth[0m
[32mPASSED[0m tests/test_build_linkcheck.py::[1mtest_linkcheck_request_headers[0m
[31mFAILED[0m tests/test_build_linkcheck.py::[1mtest_raises_for_invalid_status[0m - assert "index.rst:1:...' not found\n" == 'index.rst:1:...lhost:7777/\n'
[31m================== [31m[1m1 failed[0m, [32m5 passed[0m, [33m229 warnings[0m[31m in 2.13s[0m[31m ===================[0m
py39: exit 1 (2.62 seconds) /testbed> pytest -rA --durations 25 tests/roots/test-linkcheck-localserver/conf.py tests/roots/test-linkcheck-localserver/index.rst tests/test_build_linkcheck.py pid=106
  py39: FAIL code 1 (2.63=setup[0.01]+cmd[2.62] seconds)
  evaluation failed :( (2.72 seconds)
+ git checkout 1e2ccd8f0eca0870cf6f8fce6934e2da8eba9b72 tests/test_build_linkcheck.py
Updated 1 path from 6ddd64aa6
