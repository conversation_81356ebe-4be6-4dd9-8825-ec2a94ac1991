#!/usr/bin/env python3
"""
Monitor the progress of the Darwin Gödel Machine evolution.
"""

import os
import time
import glob
from pathlib import Path

def count_files_in_directory(directory, pattern="*"):
    """Count files matching pattern in directory."""
    if not os.path.exists(directory):
        return 0
    return len(glob.glob(os.path.join(directory, pattern)))

def get_latest_log_content(directory, lines=5):
    """Get the latest content from log files."""
    if not os.path.exists(directory):
        return "Directory not found"
    
    log_files = glob.glob(os.path.join(directory, "*.log"))
    if not log_files:
        return "No log files found"
    
    # Get the most recently modified log file
    latest_log = max(log_files, key=os.path.getmtime)
    
    try:
        with open(latest_log, 'r') as f:
            content = f.readlines()
            return ''.join(content[-lines:]) if content else "Log file is empty"
    except Exception as e:
        return f"Error reading log: {e}"

def check_patch_files():
    """Check for patch files and their sizes."""
    patch_files = glob.glob("output_dgm/*/model_patch.diff")
    if not patch_files:
        return "No patch files found yet"
    
    results = []
    for patch_file in patch_files:
        size = os.path.getsize(patch_file)
        results.append(f"{patch_file}: {size} bytes")
    
    return "\n".join(results)

def monitor_dgm_progress():
    """Monitor DGM progress continuously."""
    print("🔍 **DGM PROGRESS MONITOR**")
    print("=" * 50)
    print("Monitoring Darwin Gödel Machine evolution...")
    print("Press Ctrl+C to stop monitoring")
    print()
    
    iteration = 0
    
    try:
        while True:
            iteration += 1
            print(f"📊 **Status Check #{iteration}** - {time.strftime('%H:%M:%S')}")
            print("-" * 40)
            
            # Check output directories
            output_dirs = count_files_in_directory("output_dgm", "*")
            print(f"🗂️  Output directories: {output_dirs}")
            
            # Check initial evaluation
            initial_logs = count_files_in_directory("initial/predictions/initial_0", "*.log")
            print(f"📝 Initial evaluation logs: {initial_logs}")
            
            # Check for generation directories
            generation_dirs = glob.glob("output_dgm/generation_*")
            if generation_dirs:
                latest_gen = max(generation_dirs, key=lambda x: int(x.split('_')[-1]))
                gen_num = latest_gen.split('_')[-1]
                print(f"🧬 Latest generation: {gen_num}")
                
                # Check for patches in latest generation
                patch_info = check_patch_files()
                if "No patch files" not in patch_info:
                    print(f"🔧 Patches found:")
                    for line in patch_info.split('\n')[:3]:  # Show first 3
                        print(f"   {line}")
                    if len(patch_info.split('\n')) > 3:
                        print(f"   ... and {len(patch_info.split('\n')) - 3} more")
                else:
                    print(f"🔧 {patch_info}")
            else:
                print("🧬 No generation directories yet (still in initial evaluation)")
            
            # Check for recent log activity
            if os.path.exists("output_dgm"):
                latest_log = get_latest_log_content("output_dgm", 2)
                if "not found" not in latest_log.lower():
                    print(f"📋 Latest log activity:")
                    for line in latest_log.strip().split('\n'):
                        if line.strip():
                            print(f"   {line.strip()}")
            
            # Check if main process is still running
            try:
                with os.popen("ps aux | grep 'DGM_outer.py' | grep -v grep") as proc:
                    dgm_process = proc.read().strip()
                    if dgm_process:
                        print("✅ DGM process is running")
                    else:
                        print("❌ DGM process not found - may have completed or crashed")
                        break
            except:
                print("⚠️  Could not check process status")
            
            print()
            time.sleep(30)  # Check every 30 seconds
            
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped by user")
    except Exception as e:
        print(f"\n❌ Monitoring error: {e}")

if __name__ == "__main__":
    monitor_dgm_progress()
