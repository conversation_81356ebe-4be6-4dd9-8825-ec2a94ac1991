{"sphinx-doc__sphinx-7590": {"patch_is_None": false, "patch_exists": true, "patch_successfully_applied": true, "resolved": false, "tests_status": {"FAIL_TO_PASS": {"success": [], "failure": ["tests/test_domain_cpp.py::test_expressions"]}, "PASS_TO_PASS": {"success": ["tests/test_domain_cpp.py::test_fundamental_types", "tests/test_domain_cpp.py::test_type_definitions", "tests/test_domain_cpp.py::test_concept_definitions", "tests/test_domain_cpp.py::test_member_definitions", "tests/test_domain_cpp.py::test_function_definitions", "tests/test_domain_cpp.py::test_operators", "tests/test_domain_cpp.py::test_class_definitions", "tests/test_domain_cpp.py::test_union_definitions", "tests/test_domain_cpp.py::test_enum_definitions", "tests/test_domain_cpp.py::test_anon_definitions", "tests/test_domain_cpp.py::test_templates", "tests/test_domain_cpp.py::test_template_args", "tests/test_domain_cpp.py::test_initializers", "tests/test_domain_cpp.py::test_attributes", "tests/test_domain_cpp.py::test_xref_parsing", "tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup", "tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name", "tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok", "tests/test_domain_cpp.py::test_build_domain_cpp_semicolon", "tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl", "tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles", "tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True", "tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False", "tests/test_domain_cpp.py::test_xref_consistency"], "failure": []}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}}}