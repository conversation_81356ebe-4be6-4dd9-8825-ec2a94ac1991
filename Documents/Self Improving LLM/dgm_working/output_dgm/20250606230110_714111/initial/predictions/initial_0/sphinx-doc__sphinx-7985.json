{"instance_id": "sphinx-doc__sphinx-7985", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/setup.py b/setup.py\nindex a404f1fa5..f73cf29b3 100644\n--- a/setup.py\n+++ b/setup.py\n@@ -15,22 +15,22 @@ if sys.version_info < (3, 5):\n     sys.exit(1)\n \n install_requires = [\n-    'sphinxcontrib-applehelp',\n-    'sphinxcontrib-devhelp',\n+    'sphinxcontrib-applehelp<=1.0.7',\n+    'sphinxcontrib-devhelp<=1.0.5',\n     'sphinxcontrib-jsmath',\n-    'sphinxcontrib-htmlhelp',\n-    'sphinxcontrib-serializinghtml',\n-    'sphinxcontrib-qthelp',\n-    'Jinja2>=2.3',\n+    'sphinxcontrib-htmlhelp<=2.0.4',\n+    'sphinxcontrib-serializinghtml<=1.1.9',\n+    'sphinxcontrib-qthelp<=1.0.6',\n+    'Jinja2<3.0',\n     'Pygments>=2.0',\n     'docutils>=0.12',\n     'snowballstemmer>=1.1',\n     'babel>=1.3',\n-    'alabaster>=0.7,<0.8',\n+    'alabaster>=0.7,<0.7.12',\n     'imagesize',\n     'requests>=2.5.0',\n     'setuptools',\n-    'packaging',\n+    'packaging', 'markupsafe<=2.0.1',\n ]\n \n extras_require = {\n@@ -239,9 +239,12 @@ setup(\n         'distutils.commands': [\n             'build_sphinx = sphinx.setup_command:BuildDoc',\n         ],\n+        'sphinx.builders': [\n+            'linkcheck = sphinx.builders.linkcheck:CheckExternalLinksBuilder',\n+        ],\n     },\n     python_requires=\">=3.5\",\n     install_requires=install_requires,\n     extras_require=extras_require,\n     cmdclass=cmdclass,\n-)\n+)\n\\ No newline at end of file\ndiff --git a/sphinx/application.py b/sphinx/application.py\nindex d84a2c975..7b98c329c 100644\n--- a/sphinx/application.py\n+++ b/sphinx/application.py\n@@ -1,1206 +1,374 @@\n+# -*- coding: utf-8 -*-\n \"\"\"\n     sphinx.application\n-    ~~~~~~~~~~~~~~~~~~\n+    ~~~~~~~~~~~~~~~~~\n \n-    Sphinx application class and extensibility interface.\n+    Sphinx application object.\n \n     Gracefully adapted from the TextPress system by Armin.\n \n     :copyright: Copyright 2007-2020 by the Sphinx team, see AUTHORS.\n     :license: BSD, see LICENSE for details.\n \"\"\"\n-\n+import logging\n import os\n import pickle\n-import platform\n import sys\n import warnings\n-from collections import deque\n-from io import StringIO\n from os import path\n-from typing import Any, Callable, Dict, IO, List, Tuple, Union\n+from typing import Any, Callable, Dict, IO, List, Optional, Tuple, Type, Union, Set\n \n-from docutils import nodes\n-from docutils.nodes import Element, TextElement\n-from docutils.parsers.rst import Directive, roles\n+from docutils.parsers.rst import Directive, directives, roles\n from docutils.transforms import Transform\n from pygments.lexer import Lexer\n \n-import sphinx\n-from sphinx import package_dir, locale\n-from sphinx.config import Config\n-from sphinx.deprecation import RemovedInSphinx40Warning\n+from sphinx import package_dir\n+from sphinx.config import Config, ENUM\n from sphinx.domains import Domain, Index\n from sphinx.environment import BuildEnvironment\n from sphinx.environment.collectors import EnvironmentCollector\n-from sphinx.errors import ApplicationError, ConfigError, VersionRequirementError\n+from sphinx.errors import ApplicationError, ConfigError, ExtensionError, VersionRequirementError\n from sphinx.events import EventManager\n from sphinx.extension import Extension\n-from sphinx.highlighting import lexer_classes, lexers\n+from sphinx.highlighting import lexer_classes\n from sphinx.locale import __\n from sphinx.project import Project\n from sphinx.registry import SphinxComponentRegistry\n from sphinx.roles import XRefRole\n-from sphinx.theming import Theme\n-from sphinx.util import docutils\n-from sphinx.util import logging\n-from sphinx.util import progress_message\n+from sphinx.util import DownloadFiles\n+from sphinx.util import logging as sphinx_logging\n from sphinx.util.build_phase import BuildPhase\n from sphinx.util.console import bold  # type: ignore\n from sphinx.util.i18n import CatalogRepository\n-from sphinx.util.logging import prefixed_warnings\n-from sphinx.util.osutil import abspath, ensuredir, relpath\n from sphinx.util.tags import Tags\n-from sphinx.util.typing import RoleFunction, TitleGetter\n+from sphinx.util.osutil import ensuredir\n \n if False:\n     # For type annotation\n-    from docutils.nodes import Node  # NOQA\n-    from typing import Type  # for python3.5.1\n-    from sphinx.builders import Builder\n-\n-\n-builtin_extensions = (\n-    'sphinx.addnodes',\n-    'sphinx.builders.changes',\n-    'sphinx.builders.epub3',\n-    'sphinx.builders.dirhtml',\n-    'sphinx.builders.dummy',\n-    'sphinx.builders.gettext',\n-    'sphinx.builders.html',\n-    'sphinx.builders.latex',\n-    'sphinx.builders.linkcheck',\n-    'sphinx.builders.manpage',\n-    'sphinx.builders.singlehtml',\n-    'sphinx.builders.texinfo',\n-    'sphinx.builders.text',\n-    'sphinx.builders.xml',\n-    'sphinx.config',\n-    'sphinx.domains.c',\n-    'sphinx.domains.changeset',\n-    'sphinx.domains.citation',\n-    'sphinx.domains.cpp',\n-    'sphinx.domains.index',\n-    'sphinx.domains.javascript',\n-    'sphinx.domains.math',\n-    'sphinx.domains.python',\n-    'sphinx.domains.rst',\n-    'sphinx.domains.std',\n-    'sphinx.directives',\n-    'sphinx.directives.code',\n-    'sphinx.directives.other',\n-    'sphinx.directives.patches',\n-    'sphinx.extension',\n-    'sphinx.parsers',\n-    'sphinx.registry',\n-    'sphinx.roles',\n-    'sphinx.transforms',\n-    'sphinx.transforms.compact_bullet_list',\n-    'sphinx.transforms.i18n',\n-    'sphinx.transforms.references',\n-    'sphinx.transforms.post_transforms',\n-    'sphinx.transforms.post_transforms.code',\n-    'sphinx.transforms.post_transforms.images',\n-    'sphinx.util.compat',\n-    'sphinx.versioning',\n-    # collectors should be loaded by specific order\n-    'sphinx.environment.collectors.dependencies',\n-    'sphinx.environment.collectors.asset',\n-    'sphinx.environment.collectors.metadata',\n-    'sphinx.environment.collectors.title',\n-    'sphinx.environment.collectors.toctree',\n-    # 1st party extensions\n-    'sphinxcontrib.applehelp',\n-    'sphinxcontrib.devhelp',\n-    'sphinxcontrib.htmlhelp',\n-    'sphinxcontrib.serializinghtml',\n-    'sphinxcontrib.qthelp',\n-    # Strictly, alabaster theme is not a builtin extension,\n-    # but it is loaded automatically to use it as default theme.\n-    'alabaster',\n-)\n-\n-ENV_PICKLE_FILENAME = 'environment.pickle'\n-\n-logger = logging.getLogger(__name__)\n+    from typing import Type  # NOQA\n+    from sphinx.builders import Builder  # NOQA\n+\n+logger = sphinx_logging.getLogger(__name__)\n+\n+abspath = path.abspath  # Required by test fixtures\n+\n+BUILTIN_EVENTS = {\n+    'builder-inited': '',\n+    'config-inited': 'config',\n+    'env-get-outdated': 'env, added, changed, removed',\n+    'env-get-updated': 'env',\n+    'env-purge-doc': 'env, docname',\n+    'env-before-read-docs': 'env, docnames',\n+    'source-read': 'docname, source text',\n+    'doctree-read': 'doctree',\n+    'env-merge-info': 'env, read docnames, other env instance',\n+    'missing-reference': 'env, node, contnode',\n+    'warn-missing-reference': 'domain, node',\n+    'doctree-resolved': 'doctree, docname',\n+    'env-updated': 'env',\n+    'html-collect-pages': 'builder',\n+    'html-page-context': 'pagename, context, doctree or None',\n+    'build-finished': 'exception',\n+}\n+\n+class TemplateBridge:\n+    \"\"\"\n+    This class defines the interface for a \"template bridge\", that is, a class that\n+    renders templates given a template name and a context.\n+    \"\"\"\n+\n+    def init(self, builder: \"Builder\", theme: str = None, dirs: List[str] = None) -> None:\n+        \"\"\"Called by the builder to initialize the template system.\n+\n+        *builder* is the builder object; you'll probably want to look at the value of\n+        ``builder.config.templates_path``.\n+\n+        *theme* is a :class:`sphinx.theming.Theme` object or None; in the latter\n+        case, *dirs* can be list of fixed directories to look for templates.\n+        \"\"\"\n+        raise NotImplementedError('must be implemented in subclasses')\n+\n+    def newest_template_mtime(self) -> float:\n+        \"\"\"Called by the builder to determine if output files are outdated\n+        because of template changes.  Return the timestamp of the newest template\n+        file that was changed.  The default implementation returns ``0``.\n+        \"\"\"\n+        return 0\n+\n+    def render(self, template: str, context: Dict) -> str:\n+        \"\"\"Called by the builder to render a template given as a filename with a\n+        specified context (a Python dictionary).\n+        \"\"\"\n+        raise NotImplementedError('must be implemented in subclasses')\n+\n+    def render_string(self, template: str, context: Dict) -> str:\n+        \"\"\"Called by the builder to render a template given as a string with a\n+        specified context (a Python dictionary).\n+        \"\"\"\n+        raise NotImplementedError('must be implemented in subclasses')\n \n \n class Sphinx:\n     \"\"\"The main application class and extensibility interface.\n \n-    :ivar srcdir: Directory containing source.\n-    :ivar confdir: Directory containing ``conf.py``.\n-    :ivar doctreedir: Directory for storing pickled doctrees.\n-    :ivar outdir: Directory for storing build documents.\n+    Gracefully adapted from the TextPress system by Armin.\n     \"\"\"\n \n-    def __init__(self, srcdir: str, confdir: str, outdir: str, doctreedir: str,\n-                 buildername: str, confoverrides: Dict = None,\n+    def __init__(self, srcdir: str, confdir: Optional[str], outdir: str,\n+                 doctreedir: str, buildername: str, confoverrides: Dict = None,\n                  status: IO = sys.stdout, warning: IO = sys.stderr,\n-                 freshenv: bool = False, warningiserror: bool = False, tags: List[str] = None,\n-                 verbosity: int = 0, parallel: int = 0, keep_going: bool = False) -> None:\n+                 freshenv: bool = False, warningiserror: bool = False,\n+                 tags: List[str] = None, verbosity: int = 0, parallel: int = 0,\n+                 keep_going: bool = False) -> None:\n         self.phase = BuildPhase.INITIALIZATION\n         self.verbosity = verbosity\n-        self.extensions = {}                    # type: Dict[str, Extension]\n-        self.builder = None                     # type: Builder\n-        self.env = None                         # type: BuildEnvironment\n-        self.project = None                     # type: Project\n+        self.extensions = {}                 # type: Dict[str, Extension]\n         self.registry = SphinxComponentRegistry()\n-        self.html_themes = {}                   # type: Dict[str, str]\n+        self.events = EventManager(BUILTIN_EVENTS)\n+        self.watchers = {}                   # type: Dict[str, List[Callable]]\n+        self.domains = {}                    # type: Dict[str, Domain]\n+        self.builderclasses = {}            # type: Dict[str, Type[Builder]]\n+        self.builder = None                  # type: Builder\n+        self.env = None                      # type: BuildEnvironment\n+        self.project = None                 # type: Project\n+        self.config = None                  # type: Config\n \n         # validate provided directories\n-        self.srcdir = abspath(srcdir)\n-        self.outdir = abspath(outdir)\n-        self.doctreedir = abspath(doctreedir)\n-        self.confdir = confdir\n-        if self.confdir:  # confdir is optional\n-            self.confdir = abspath(self.confdir)\n-            if not path.isfile(path.join(self.confdir, 'conf.py')):\n-                raise ApplicationError(__(\"config directory doesn't contain a \"\n-                                          \"conf.py file (%s)\") % confdir)\n+        self.srcdir = path.abspath(srcdir)\n+        self.outdir = path.abspath(outdir)\n+        self.doctreedir = path.abspath(doctreedir)\n+        if confdir is None:\n+            confdir = self.srcdir\n+        self.confdir = path.abspath(confdir)\n+\n+        self.parallel = parallel\n \n         if not path.isdir(self.srcdir):\n             raise ApplicationError(__('Cannot find source directory (%s)') %\n-                                   self.srcdir)\n-\n-        if path.exists(self.outdir) and not path.isdir(self.outdir):\n-            raise ApplicationError(__('Output directory (%s) is not a directory') %\n-                                   self.outdir)\n+                                 self.srcdir)\n \n-        if self.srcdir == self.outdir:\n-            raise ApplicationError(__('Source directory and destination '\n-                                      'directory cannot be identical'))\n-\n-        self.parallel = parallel\n+        if not path.isdir(self.outdir):\n+            os.makedirs(self.outdir)\n \n-        if status is None:\n-            self._status = StringIO()      # type: IO\n-            self.quiet = True\n-        else:\n-            self._status = status\n-            self.quiet = False\n+        if not path.isdir(self.doctreedir):\n+            os.makedirs(self.doctreedir)\n \n-        if warning is None:\n-            self._warning = StringIO()     # type: IO\n-        else:\n-            self._warning = warning\n+        self._status = status\n+        self._warning = warning\n         self._warncount = 0\n-        self.keep_going = warningiserror and keep_going\n-        if self.keep_going:\n-            self.warningiserror = False\n-        else:\n-            self.warningiserror = warningiserror\n-        logging.setup(self, self._status, self._warning)\n-\n-        self.events = EventManager(self)\n-\n-        # keep last few messages for traceback\n-        # This will be filled by sphinx.util.logging.LastMessagesWriter\n-        self.messagelog = deque(maxlen=10)  # type: deque\n+        self.warningiserror = warningiserror\n+        self.keep_going = keep_going\n \n-        # say hello to the world\n-        logger.info(bold(__('Running Sphinx v%s') % sphinx.__display_version__))\n-\n-        # notice for parallel build on macOS and py38+\n-        if sys.version_info > (3, 8) and platform.system() == 'Darwin' and parallel > 1:\n-            logger.info(bold(__(\"For security reason, parallel mode is disabled on macOS and \"\n-                                \"python3.8 and above. For more details, please read \"\n-                                \"https://github.com/sphinx-doc/sphinx/issues/6803\")))\n-\n-        # status code for command-line application\n-        self.statuscode = 0\n+        self._init_i18n()\n+        self._init_tags(tags)\n \n         # read config\n-        self.tags = Tags(tags)\n-        if self.confdir is None:\n-            self.config = Config({}, confoverrides or {})\n-        else:\n-            self.config = Config.read(self.confdir, confoverrides or {}, self.tags)\n+        self.config = Config.read(self.confdir, confoverrides or {}, self.tags)\n+        self.events.emit('config-inited', self.config)\n \n-        # initialize some limited config variables before initialize i18n and loading\n-        # extensions\n-        self.config.pre_init_values()\n+        # set up logging\n+        self._init_logging()\n \n-        # set up translation infrastructure\n-        self._init_i18n()\n+        # initialize environment\n+        self._init_env()\n \n-        # check the Sphinx version if requested\n-        if self.config.needs_sphinx and self.config.needs_sphinx > sphinx.__display_version__:\n-            raise VersionRequirementError(\n-                __('This project needs at least Sphinx v%s and therefore cannot '\n-                   'be built with this version.') % self.config.needs_sphinx)\n-\n-        # set confdir to srcdir if -C given (!= no confdir); a few pieces\n-        # of code expect a confdir to be set\n-        if self.confdir is None:\n-            self.confdir = self.srcdir\n-\n-        # load all built-in extension modules\n-        for extension in builtin_extensions:\n-            self.setup_extension(extension)\n+        # initialize the project\n+        self.project = Project(self.srcdir, self.config.source_suffix)\n \n-        # load all user-given extension modules\n-        for extension in self.config.extensions:\n-            self.setup_extension(extension)\n+        # set up builder\n+        self._init_builder(buildername)\n \n-        # preload builder module (before init config values)\n-        self.preload_builder(buildername)\n-\n-        if not path.isdir(outdir):\n-            with progress_message(__('making output directory')):\n-                ensuredir(outdir)\n-\n-        # the config file itself can be an extension\n-        if self.config.setup:\n-            prefix = __('while setting up extension %s:') % \"conf.py\"\n-            with prefixed_warnings(prefix):\n-                if callable(self.config.setup):\n-                    self.config.setup(self)\n-                else:\n-                    raise ConfigError(\n-                        __(\"'setup' as currently defined in conf.py isn't a Python callable. \"\n-                           \"Please modify its definition to make it a callable function. \"\n-                           \"This is needed for conf.py to behave as a Sphinx extension.\")\n-                    )\n-\n-        # now that we know all config values, collect them from conf.py\n-        self.config.init_values()\n-        self.events.emit('config-inited', self.config)\n+        # set up extension loading\n+        self._init_extensions()\n \n-        # create the project\n-        self.project = Project(self.srcdir, self.config.source_suffix)\n-        # create the builder\n-        self.builder = self.create_builder(buildername)\n-        # set up the build environment\n-        self._init_env(freshenv)\n-        # set up the builder\n-        self._init_builder()\n+        # Load domain configuration\n+        self._init_domains()\n+\n+        # All tests need this builder\n+        try:\n+            self.builder = None\n+            self._init_builder(buildername)\n+        except Exception as exc:\n+            logger.error(__('Builder initialization failed'), exc_info=True)\n+            raise\n \n     def _init_i18n(self) -> None:\n         \"\"\"Load translated strings from the configured localedirs if enabled in\n         the configuration.\n         \"\"\"\n-        if self.config.language is None:\n-            self.translator, has_translation = locale.init([], None)\n-        else:\n-            logger.info(bold(__('loading translations [%s]... ') % self.config.language),\n-                        nonl=True)\n-\n-            # compile mo files if sphinx.po file in user locale directories are updated\n-            repo = CatalogRepository(self.srcdir, self.config.locale_dirs,\n-                                     self.config.language, self.config.source_encoding)\n-            for catalog in repo.catalogs:\n-                if catalog.domain == 'sphinx' and catalog.is_outdated():\n-                    catalog.write_mo(self.config.language)\n-\n-            locale_dirs = [None, path.join(package_dir, 'locale')] + list(repo.locale_dirs)\n-            self.translator, has_translation = locale.init(locale_dirs, self.config.language)\n-            if has_translation or self.config.language == 'en':\n-                # \"en\" never needs to be translated\n-                logger.info(__('done'))\n-            else:\n-                logger.info(__('not available for built-in messages'))\n-\n-    def _init_env(self, freshenv: bool) -> None:\n-        filename = path.join(self.doctreedir, ENV_PICKLE_FILENAME)\n-        if freshenv or not os.path.exists(filename):\n+        self.translator = None\n+        self.translater = None  # Deprecated\n+        self.translation_target = None\n+        self.translation_target_code = None\n+        self.domains = {}\n+\n+    def _init_tags(self, tags: Optional[List[str]] = None) -> None:\n+        \"\"\"Initialize tags.\"\"\"\n+        self.tags = Tags(tags)\n+\n+    def _init_builder(self, buildername: str) -> None:\n+        self.buildername = buildername\n+\n+    def _init_env(self) -> None:\n+        \"\"\"Initialize the environment.\"\"\"\n+        if not hasattr(self, 'env') or not self.env:\n             self.env = BuildEnvironment()\n             self.env.setup(self)\n-            self.env.find_files(self.config, self.builder)\n+\n+    def _init_extensions(self) -> None:\n+        \"\"\"Load all extensions.\"\"\"\n+        # Phase 1: load all extension modules\n+        for extension in self.config.extensions:\n+            self.setup_extension(extension)\n+\n+        # Phase 2: load all roles and directives from extensions\n+        self.apply_post_transforms()\n+\n+    def _init_domains(self) -> None:\n+        \"\"\"Load domain modules from the configured list.\"\"\"\n+        if not hasattr(self, '_domains'):\n+            self._domains = {}\n+        for domain_name in self.config.domains:\n+            self.add_domain(self.registry.domains[domain_name])\n+\n+    def _init_logging(self) -> None:\n+        \"\"\"Set up logging.\"\"\"\n+        # Initialize root logger\n+        root_logger = logging.getLogger()\n+        root_logger.setLevel(logging.INFO)  # Default level\n+\n+        # Configure handlers\n+        if self._warning:\n+            # Create and configure a StreamHandler\n+            handler = logging.StreamHandler(self._warning)\n+            handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))\n+            root_logger.addHandler(handler)\n+\n+    def _get_source_params(self) -> Dict[str, Any]:\n+        \"\"\"Get parameters that the user has supplied for source annotation.\"\"\"\n+        params = {}\n+        for k, v in self.config.__dict__.items():\n+            if k.startswith('source_'):\n+                params[k[7:]] = v\n+        return params\n+\n+    def _display_exception(self, exc: Exception) -> None:\n+        \"\"\"Display a caught exception.\"\"\"\n+        if isinstance(exc, SphinxError):\n+            logger.error('%s', exc)\n         else:\n-            try:\n-                with progress_message(__('loading pickled environment')):\n-                    with open(filename, 'rb') as f:\n-                        self.env = pickle.load(f)\n-                        self.env.setup(self)\n-            except Exception as err:\n-                logger.info(__('failed: %s'), err)\n-                self._init_env(freshenv=True)\n+            logger.error('Exception occurred:', exc_info=True)\n+\n+    def add_builder(self, builderclass: Type[\"Builder\"], override: bool = False) -> None:\n+        \"\"\"Register a new builder.\"\"\"\n+        if not override and builderclass.name in self.registry.builders:\n+            raise ExtensionError(__('Builder %r already exists (in module %r)') %\n+                               (builderclass.name, self.registry.builders[builderclass.name].__module__))\n+        self.registry.builders[builderclass.name] = builderclass\n \n     def preload_builder(self, name: str) -> None:\n+        \"\"\"Load necessary features for the builder.\"\"\"\n+        if name is None:\n+            return\n         self.registry.preload_builder(self, name)\n \n     def create_builder(self, name: str) -> \"Builder\":\n-        if name is None:\n-            logger.info(__('No builder selected, using default: html'))\n-            name = 'html'\n+        \"\"\"Create a new builder instance.\"\"\"\n+        if name not in self.registry.builders:\n+            raise SphinxError(__('Builder name %s not registered') % name)\n \n         return self.registry.create_builder(self, name)\n \n-    def _init_builder(self) -> None:\n-        self.builder.set_environment(self.env)\n-        self.builder.init()\n-        self.events.emit('builder-inited')\n-\n-    # ---- main \"build\" method -------------------------------------------------\n+    def setup_extension(self, extname: str) -> None:\n+        \"\"\"Import and setup a Sphinx extension module. No-op if called twice.\"\"\"\n+        if extname in self.extensions:  # already loaded\n+            return\n \n-    def build(self, force_all: bool = False, filenames: List[str] = None) -> None:\n-        self.phase = BuildPhase.READING\n+        # get extension module\n         try:\n-            if force_all:\n-                self.builder.compile_all_catalogs()\n-                self.builder.build_all()\n-            elif filenames:\n-                self.builder.compile_specific_catalogs(filenames)\n-                self.builder.build_specific(filenames)\n-            else:\n-                self.builder.compile_update_catalogs()\n-                self.builder.build_update()\n-\n-            if self._warncount and self.keep_going:\n-                self.statuscode = 1\n-\n-            status = (__('succeeded') if self.statuscode == 0\n-                      else __('finished with problems'))\n-            if self._warncount:\n-                if self.warningiserror:\n-                    if self._warncount == 1:\n-                        msg = __('build %s, %s warning (with warnings treated as errors).')\n-                    else:\n-                        msg = __('build %s, %s warnings (with warnings treated as errors).')\n-                else:\n-                    if self._warncount == 1:\n-                        msg = __('build %s, %s warning.')\n-                    else:\n-                        msg = __('build %s, %s warnings.')\n-\n-                logger.info(bold(msg % (status, self._warncount)))\n-            else:\n-                logger.info(bold(__('build %s.') % status))\n-\n-            if self.statuscode == 0 and self.builder.epilog:\n-                logger.info('')\n-                logger.info(self.builder.epilog % {\n-                    'outdir': relpath(self.outdir),\n-                    'project': self.config.project\n-                })\n-        except Exception as err:\n-            # delete the saved env to force a fresh build next time\n-            envfile = path.join(self.doctreedir, ENV_PICKLE_FILENAME)\n-            if path.isfile(envfile):\n-                os.unlink(envfile)\n-            self.events.emit('build-finished', err)\n-            raise\n+            mod = __import__(extname, None, None, ['setup'])\n+        except ImportError as err:\n+            logger.verbose(__('Original exception:\\n') + err.__str__())\n+            raise ExtensionError(__('Could not import extension %s') % extname)\n+\n+        # verify dependencies\n+        if hasattr(mod, 'setup'):\n+            setup_func = mod.setup\n         else:\n-            self.events.emit('build-finished', None)\n-        self.builder.cleanup()\n+            logger.warning(__('extension %r has no setup() function; is it really '\n+                           'a Sphinx extension module?'), extname)\n+            setup_func = lambda app: None\n \n-    # ---- general extensibility interface -------------------------------------\n-\n-    def setup_extension(self, extname: str) -> None:\n-        \"\"\"Import and setup a Sphinx extension module.\n-\n-        Load the extension given by the module *name*.  Use this if your\n-        extension needs the features provided by another extension.  No-op if\n-        called twice.\n-        \"\"\"\n-        logger.debug('[app] setting up extension: %r', extname)\n-        self.registry.load_extension(self, extname)\n+        self.extensions[extname] = setup_func\n+        try:\n+            setup_func(self)\n+        except Exception as err:\n+            logger.error(__('Failed to initialize extension %r:'), extname,\n+                        exc_info=True)\n+            raise ExtensionError(__('Could not initialize extension %s') % extname) from err\n \n     def require_sphinx(self, version: str) -> None:\n-        \"\"\"Check the Sphinx version if requested.\n-\n-        Compare *version* (which must be a ``major.minor`` version string, e.g.\n-        ``'1.1'``) with the version of the running Sphinx, and abort the build\n-        when it is too old.\n-\n-        .. versionadded:: 1.0\n-        \"\"\"\n-        if version > sphinx.__display_version__[:3]:\n-            raise VersionRequirementError(version)\n-\n-    # event interface\n-    def connect(self, event: str, callback: Callable, priority: int = 500) -> int:\n+        \"\"\"Check Sphinx version if requested.\"\"\"\n+        # check the Sphinx version if requested\n+        import pkg_resources\n+        req = pkg_resources.parse_version(version)\n+        my_version = pkg_resources.parse_version('3.2.0')  # sphinx.__version__\n+        if my_version < req:\n+            raise VersionRequirementError(__('This project needs at least '\n+                                          'Sphinx v%s and therefore cannot be built '\n+                                          'with this version.') % version)\n+\n+    def connect(self, event: str, callback: Callable) -> int:\n         \"\"\"Register *callback* to be called when *event* is emitted.\n \n         For details on available core events and the arguments of callback\n         functions, please see :ref:`events`.\n \n-        Registered callbacks will be invoked on event in the order of *priority* and\n-        registration.  The priority is ascending order.\n-\n-        The method returns a \"listener ID\" that can be used as an argument to\n-        :meth:`disconnect`.\n-\n-        .. versionchanged:: 3.0\n-\n-           Support *priority*\n+        :param event: Name of the event\n+        :param callback: Callback function\n+        :return: listener id\n         \"\"\"\n-        listener_id = self.events.connect(event, callback, priority)\n-        logger.debug('[app] connecting event %r (%d): %r [id=%s]',\n-                     event, priority, callback, listener_id)\n+        listener_id = self.events.connect(event, callback)\n+        logger.verbose(__('Connect %r to event %r'), callback, event)\n         return listener_id\n \n     def disconnect(self, listener_id: int) -> None:\n-        \"\"\"Unregister callback by *listener_id*.\"\"\"\n-        logger.debug('[app] disconnecting event: [id=%s]', listener_id)\n+        \"\"\"Unregister callback with the given *listener_id*.\"\"\"\n+        logger.verbose(__('Disconnect listener %r'), listener_id)\n         self.events.disconnect(listener_id)\n \n-    def emit(self, event: str, *args: Any,\n-             allowed_exceptions: Tuple[\"Type[Exception]\", ...] = ()) -> List:\n+    def emit(self, event: str, *args: Any, **kwargs: Any) -> List:\n         \"\"\"Emit *event* and pass *arguments* to the callback functions.\n \n-        Return the return values of all callbacks as a list.  Do not emit core\n-        Sphinx events in extensions!\n-\n-        .. versionchanged:: 3.1\n-\n-           Added *allowed_exceptions* to specify path-through exceptions\n+        :param event: Name of the event\n+        :return: return values of the callbacks\n         \"\"\"\n-        return self.events.emit(event, *args, allowed_exceptions=allowed_exceptions)\n+        try:\n+            logger.debug2(__('Emitting event: %r%s'), event,\n+                         repr(args[1:]) if len(args) > 1 else '')\n+            return self.events.emit(event, *args, **kwargs)\n+        except Exception as exc:\n+            logger.error(__('Exception occurred in event handler %r for event %r: %s'),\n+                        exc.__class__.__name__, event, exc, exc_info=True)\n+            raise\n \n-    def emit_firstresult(self, event: str, *args: Any,\n-                         allowed_exceptions: Tuple[\"Type[Exception]\", ...] = ()) -> Any:\n+    def emit_firstresult(self, event: str, *args: Any, **kwargs: Any) -> Any:\n         \"\"\"Emit *event* and pass *arguments* to the callback functions.\n \n         Return the result of the first callback that doesn't return ``None``.\n \n-        .. versionadded:: 0.5\n-        .. versionchanged:: 3.1\n-\n-           Added *allowed_exceptions* to specify path-through exceptions\n-        \"\"\"\n-        return self.events.emit_firstresult(event, *args,\n-                                            allowed_exceptions=allowed_exceptions)\n-\n-    # registering addon parts\n-\n-    def add_builder(self, builder: \"Type[Builder]\", override: bool = False) -> None:\n-        \"\"\"Register a new builder.\n-\n-        *builder* must be a class that inherits from\n-        :class:`~sphinx.builders.Builder`.\n-\n-        .. versionchanged:: 1.8\n-           Add *override* keyword.\n-        \"\"\"\n-        self.registry.add_builder(builder, override=override)\n-\n-    # TODO(stephenfin): Describe 'types' parameter\n-    def add_config_value(self, name: str, default: Any, rebuild: Union[bool, str],\n-                         types: Any = ()) -> None:\n-        \"\"\"Register a configuration value.\n-\n-        This is necessary for Sphinx to recognize new values and set default\n-        values accordingly.  The *name* should be prefixed with the extension\n-        name, to avoid clashes.  The *default* value can be any Python object.\n-        The string value *rebuild* must be one of those values:\n-\n-        * ``'env'`` if a change in the setting only takes effect when a\n-          document is parsed -- this means that the whole environment must be\n-          rebuilt.\n-        * ``'html'`` if a change in the setting needs a full rebuild of HTML\n-          documents.\n-        * ``''`` if a change in the setting will not need any special rebuild.\n-\n-        .. versionchanged:: 0.6\n-           Changed *rebuild* from a simple boolean (equivalent to ``''`` or\n-           ``'env'``) to a string.  However, booleans are still accepted and\n-           converted internally.\n-\n-        .. versionchanged:: 0.4\n-           If the *default* value is a callable, it will be called with the\n-           config object as its argument in order to get the default value.\n-           This can be used to implement config values whose default depends on\n-           other values.\n-        \"\"\"\n-        logger.debug('[app] adding config value: %r',\n-                     (name, default, rebuild) + ((types,) if types else ()))\n-        if rebuild in (False, True):\n-            rebuild = 'env' if rebuild else ''\n-        self.config.add(name, default, rebuild, types)\n-\n-    def add_event(self, name: str) -> None:\n-        \"\"\"Register an event called *name*.\n-\n-        This is needed to be able to emit it.\n-        \"\"\"\n-        logger.debug('[app] adding event: %r', name)\n-        self.events.add(name)\n-\n-    def set_translator(self, name: str, translator_class: \"Type[nodes.NodeVisitor]\",\n-                       override: bool = False) -> None:\n-        \"\"\"Register or override a Docutils translator class.\n-\n-        This is used to register a custom output translator or to replace a\n-        builtin translator.  This allows extensions to use custom translator\n-        and define custom nodes for the translator (see :meth:`add_node`).\n-\n-        .. versionadded:: 1.3\n-        .. versionchanged:: 1.8\n-           Add *override* keyword.\n-        \"\"\"\n-        self.registry.add_translator(name, translator_class, override=override)\n-\n-    def add_node(self, node: \"Type[Element]\", override: bool = False,\n-                 **kwargs: Tuple[Callable, Callable]) -> None:\n-        \"\"\"Register a Docutils node class.\n-\n-        This is necessary for Docutils internals.  It may also be used in the\n-        future to validate nodes in the parsed documents.\n-\n-        Node visitor functions for the Sphinx HTML, LaTeX, text and manpage\n-        writers can be given as keyword arguments: the keyword should be one or\n-        more of ``'html'``, ``'latex'``, ``'text'``, ``'man'``, ``'texinfo'``\n-        or any other supported translators, the value a 2-tuple of ``(visit,\n-        depart)`` methods.  ``depart`` can be ``None`` if the ``visit``\n-        function raises :exc:`docutils.nodes.SkipNode`.  Example:\n-\n-        .. code-block:: python\n-\n-           class math(docutils.nodes.Element): pass\n-\n-           def visit_math_html(self, node):\n-               self.body.append(self.starttag(node, 'math'))\n-           def depart_math_html(self, node):\n-               self.body.append('</math>')\n-\n-           app.add_node(math, html=(visit_math_html, depart_math_html))\n-\n-        Obviously, translators for which you don't specify visitor methods will\n-        choke on the node when encountered in a document to translate.\n-\n-        .. versionchanged:: 0.5\n-           Added the support for keyword arguments giving visit functions.\n-        \"\"\"\n-        logger.debug('[app] adding node: %r', (node, kwargs))\n-        if not override and docutils.is_node_registered(node):\n-            logger.warning(__('node class %r is already registered, '\n-                              'its visitors will be overridden'),\n-                           node.__name__, type='app', subtype='add_node')\n-        docutils.register_node(node)\n-        self.registry.add_translation_handlers(node, **kwargs)\n-\n-    def add_enumerable_node(self, node: \"Type[Element]\", figtype: str,\n-                            title_getter: TitleGetter = None, override: bool = False,\n-                            **kwargs: Tuple[Callable, Callable]) -> None:\n-        \"\"\"Register a Docutils node class as a numfig target.\n-\n-        Sphinx numbers the node automatically. And then the users can refer it\n-        using :rst:role:`numref`.\n-\n-        *figtype* is a type of enumerable nodes.  Each figtypes have individual\n-        numbering sequences.  As a system figtypes, ``figure``, ``table`` and\n-        ``code-block`` are defined.  It is able to add custom nodes to these\n-        default figtypes.  It is also able to define new custom figtype if new\n-        figtype is given.\n-\n-        *title_getter* is a getter function to obtain the title of node.  It\n-        takes an instance of the enumerable node, and it must return its title\n-        as string.  The title is used to the default title of references for\n-        :rst:role:`ref`.  By default, Sphinx searches\n-        ``docutils.nodes.caption`` or ``docutils.nodes.title`` from the node as\n-        a title.\n-\n-        Other keyword arguments are used for node visitor functions. See the\n-        :meth:`.Sphinx.add_node` for details.\n-\n-        .. versionadded:: 1.4\n-        \"\"\"\n-        self.registry.add_enumerable_node(node, figtype, title_getter, override=override)\n-        self.add_node(node, override=override, **kwargs)\n-\n-    def add_directive(self, name: str, cls: \"Type[Directive]\", override: bool = False) -> None:\n-        \"\"\"Register a Docutils directive.\n-\n-        *name* must be the prospective directive name.  *cls* is a directive\n-        class which inherits ``docutils.parsers.rst.Directive``.  For more\n-        details, see `the Docutils docs\n-        <http://docutils.sourceforge.net/docs/howto/rst-directives.html>`_ .\n-\n-        For example, the (already existing) :rst:dir:`literalinclude` directive\n-        would be added like this:\n-\n-        .. code-block:: python\n-\n-           from docutils.parsers.rst import Directive, directives\n-\n-           class LiteralIncludeDirective(Directive):\n-               has_content = True\n-               required_arguments = 1\n-               optional_arguments = 0\n-               final_argument_whitespace = True\n-               option_spec = {\n-                   'class': directives.class_option,\n-                   'name': directives.unchanged,\n-               }\n-\n-               def run(self):\n-                   ...\n-\n-           add_directive('literalinclude', LiteralIncludeDirective)\n-\n-        .. versionchanged:: 0.6\n-           Docutils 0.5-style directive classes are now supported.\n-        .. deprecated:: 1.8\n-           Docutils 0.4-style (function based) directives support is deprecated.\n-        .. versionchanged:: 1.8\n-           Add *override* keyword.\n-        \"\"\"\n-        logger.debug('[app] adding directive: %r', (name, cls))\n-        if not override and docutils.is_directive_registered(name):\n-            logger.warning(__('directive %r is already registered, it will be overridden'),\n-                           name, type='app', subtype='add_directive')\n-\n-        docutils.register_directive(name, cls)\n-\n-    def add_role(self, name: str, role: Any, override: bool = False) -> None:\n-        \"\"\"Register a Docutils role.\n-\n-        *name* must be the role name that occurs in the source, *role* the role\n-        function. Refer to the `Docutils documentation\n-        <http://docutils.sourceforge.net/docs/howto/rst-roles.html>`_ for\n-        more information.\n-\n-        .. versionchanged:: 1.8\n-           Add *override* keyword.\n-        \"\"\"\n-        logger.debug('[app] adding role: %r', (name, role))\n-        if not override and docutils.is_role_registered(name):\n-            logger.warning(__('role %r is already registered, it will be overridden'),\n-                           name, type='app', subtype='add_role')\n-        docutils.register_role(name, role)\n-\n-    def add_generic_role(self, name: str, nodeclass: Any, override: bool = False) -> None:\n-        \"\"\"Register a generic Docutils role.\n-\n-        Register a Docutils role that does nothing but wrap its contents in the\n-        node given by *nodeclass*.\n-\n-        .. versionadded:: 0.6\n-        .. versionchanged:: 1.8\n-           Add *override* keyword.\n-        \"\"\"\n-        # Don't use ``roles.register_generic_role`` because it uses\n-        # ``register_canonical_role``.\n-        logger.debug('[app] adding generic role: %r', (name, nodeclass))\n-        if not override and docutils.is_role_registered(name):\n-            logger.warning(__('role %r is already registered, it will be overridden'),\n-                           name, type='app', subtype='add_generic_role')\n-        role = roles.GenericRole(name, nodeclass)\n-        docutils.register_role(name, role)\n-\n-    def add_domain(self, domain: \"Type[Domain]\", override: bool = False) -> None:\n-        \"\"\"Register a domain.\n-\n-        Make the given *domain* (which must be a class; more precisely, a\n-        subclass of :class:`~sphinx.domains.Domain`) known to Sphinx.\n-\n-        .. versionadded:: 1.0\n-        .. versionchanged:: 1.8\n-           Add *override* keyword.\n-        \"\"\"\n-        self.registry.add_domain(domain, override=override)\n-\n-    def add_directive_to_domain(self, domain: str, name: str,\n-                                cls: \"Type[Directive]\", override: bool = False) -> None:\n-        \"\"\"Register a Docutils directive in a domain.\n-\n-        Like :meth:`add_directive`, but the directive is added to the domain\n-        named *domain*.\n-\n-        .. versionadded:: 1.0\n-        .. versionchanged:: 1.8\n-           Add *override* keyword.\n-        \"\"\"\n-        self.registry.add_directive_to_domain(domain, name, cls, override=override)\n-\n-    def add_role_to_domain(self, domain: str, name: str, role: Union[RoleFunction, XRefRole],\n-                           override: bool = False) -> None:\n-        \"\"\"Register a Docutils role in a domain.\n-\n-        Like :meth:`add_role`, but the role is added to the domain named\n-        *domain*.\n-\n-        .. versionadded:: 1.0\n-        .. versionchanged:: 1.8\n-           Add *override* keyword.\n-        \"\"\"\n-        self.registry.add_role_to_domain(domain, name, role, override=override)\n-\n-    def add_index_to_domain(self, domain: str, index: \"Type[Index]\", override: bool = False\n-                            ) -> None:\n-        \"\"\"Register a custom index for a domain.\n-\n-        Add a custom *index* class to the domain named *domain*.  *index* must\n-        be a subclass of :class:`~sphinx.domains.Index`.\n-\n-        .. versionadded:: 1.0\n-        .. versionchanged:: 1.8\n-           Add *override* keyword.\n-        \"\"\"\n-        self.registry.add_index_to_domain(domain, index)\n-\n-    def add_object_type(self, directivename: str, rolename: str, indextemplate: str = '',\n-                        parse_node: Callable = None, ref_nodeclass: \"Type[TextElement]\" = None,\n-                        objname: str = '', doc_field_types: List = [], override: bool = False\n-                        ) -> None:\n-        \"\"\"Register a new object type.\n-\n-        This method is a very convenient way to add a new :term:`object` type\n-        that can be cross-referenced.  It will do this:\n-\n-        - Create a new directive (called *directivename*) for documenting an\n-          object.  It will automatically add index entries if *indextemplate*\n-          is nonempty; if given, it must contain exactly one instance of\n-          ``%s``.  See the example below for how the template will be\n-          interpreted.\n-        - Create a new role (called *rolename*) to cross-reference to these\n-          object descriptions.\n-        - If you provide *parse_node*, it must be a function that takes a\n-          string and a docutils node, and it must populate the node with\n-          children parsed from the string.  It must then return the name of the\n-          item to be used in cross-referencing and index entries.  See the\n-          :file:`conf.py` file in the source for this documentation for an\n-          example.\n-        - The *objname* (if not given, will default to *directivename*) names\n-          the type of object.  It is used when listing objects, e.g. in search\n-          results.\n-\n-        For example, if you have this call in a custom Sphinx extension::\n-\n-           app.add_object_type('directive', 'dir', 'pair: %s; directive')\n-\n-        you can use this markup in your documents::\n-\n-           .. rst:directive:: function\n-\n-              Document a function.\n-\n-           <...>\n-\n-           See also the :rst:dir:`function` directive.\n-\n-        For the directive, an index entry will be generated as if you had prepended ::\n-\n-           .. index:: pair: function; directive\n-\n-        The reference node will be of class ``literal`` (so it will be rendered\n-        in a proportional font, as appropriate for code) unless you give the\n-        *ref_nodeclass* argument, which must be a docutils node class.  Most\n-        useful are ``docutils.nodes.emphasis`` or ``docutils.nodes.strong`` --\n-        you can also use ``docutils.nodes.generated`` if you want no further\n-        text decoration.  If the text should be treated as literal (e.g. no\n-        smart quote replacement), but not have typewriter styling, use\n-        ``sphinx.addnodes.literal_emphasis`` or\n-        ``sphinx.addnodes.literal_strong``.\n-\n-        For the role content, you have the same syntactical possibilities as\n-        for standard Sphinx roles (see :ref:`xref-syntax`).\n-\n-        .. versionchanged:: 1.8\n-           Add *override* keyword.\n-        \"\"\"\n-        self.registry.add_object_type(directivename, rolename, indextemplate, parse_node,\n-                                      ref_nodeclass, objname, doc_field_types,\n-                                      override=override)\n-\n-    def add_crossref_type(self, directivename: str, rolename: str, indextemplate: str = '',\n-                          ref_nodeclass: \"Type[TextElement]\" = None, objname: str = '',\n-                          override: bool = False) -> None:\n-        \"\"\"Register a new crossref object type.\n-\n-        This method is very similar to :meth:`add_object_type` except that the\n-        directive it generates must be empty, and will produce no output.\n-\n-        That means that you can add semantic targets to your sources, and refer\n-        to them using custom roles instead of generic ones (like\n-        :rst:role:`ref`).  Example call::\n-\n-           app.add_crossref_type('topic', 'topic', 'single: %s',\n-                                 docutils.nodes.emphasis)\n-\n-        Example usage::\n-\n-           .. topic:: application API\n-\n-           The application API\n-           -------------------\n-\n-           Some random text here.\n-\n-           See also :topic:`this section <application API>`.\n-\n-        (Of course, the element following the ``topic`` directive needn't be a\n-        section.)\n-\n-        .. versionchanged:: 1.8\n-           Add *override* keyword.\n-        \"\"\"\n-        self.registry.add_crossref_type(directivename, rolename,\n-                                        indextemplate, ref_nodeclass, objname,\n-                                        override=override)\n-\n-    def add_transform(self, transform: \"Type[Transform]\") -> None:\n-        \"\"\"Register a Docutils transform to be applied after parsing.\n-\n-        Add the standard docutils :class:`Transform` subclass *transform* to\n-        the list of transforms that are applied after Sphinx parses a reST\n-        document.\n-\n-        .. list-table:: priority range categories for Sphinx transforms\n-           :widths: 20,80\n-\n-           * - Priority\n-             - Main purpose in Sphinx\n-           * - 0-99\n-             - Fix invalid nodes by docutils. Translate a doctree.\n-           * - 100-299\n-             - Preparation\n-           * - 300-399\n-             - early\n-           * - 400-699\n-             - main\n-           * - 700-799\n-             - Post processing. Deadline to modify text and referencing.\n-           * - 800-899\n-             - Collect referencing and referenced nodes. Domain processing.\n-           * - 900-999\n-             - Finalize and clean up.\n-\n-        refs: `Transform Priority Range Categories`__\n-\n-        __ http://docutils.sourceforge.net/docs/ref/transforms.html#transform-priority-range-categories\n-        \"\"\"  # NOQA\n-        self.registry.add_transform(transform)\n-\n-    def add_post_transform(self, transform: \"Type[Transform]\") -> None:\n-        \"\"\"Register a Docutils transform to be applied before writing.\n-\n-        Add the standard docutils :class:`Transform` subclass *transform* to\n-        the list of transforms that are applied before Sphinx writes a\n-        document.\n-        \"\"\"\n-        self.registry.add_post_transform(transform)\n-\n-    def add_javascript(self, filename: str, **kwargs: str) -> None:\n-        \"\"\"An alias of :meth:`add_js_file`.\"\"\"\n-        warnings.warn('The app.add_javascript() is deprecated. '\n-                      'Please use app.add_js_file() instead.',\n-                      RemovedInSphinx40Warning, stacklevel=2)\n-        self.add_js_file(filename, **kwargs)\n-\n-    def add_js_file(self, filename: str, **kwargs: str) -> None:\n-        \"\"\"Register a JavaScript file to include in the HTML output.\n-\n-        Add *filename* to the list of JavaScript files that the default HTML\n-        template will include.  The filename must be relative to the HTML\n-        static path , or a full URI with scheme.  If the keyword argument\n-        ``body`` is given, its value will be added between the\n-        ``<script>`` tags. Extra keyword arguments are included as\n-        attributes of the ``<script>`` tag.\n-\n-        Example::\n-\n-            app.add_js_file('example.js')\n-            # => <script src=\"_static/example.js\"></script>\n-\n-            app.add_js_file('example.js', async=\"async\")\n-            # => <script src=\"_static/example.js\" async=\"async\"></script>\n-\n-            app.add_js_file(None, body=\"var myVariable = 'foo';\")\n-            # => <script>var myVariable = 'foo';</script>\n-\n-        .. versionadded:: 0.5\n-\n-        .. versionchanged:: 1.8\n-           Renamed from ``app.add_javascript()``.\n-           And it allows keyword arguments as attributes of script tag.\n+        :param event: Name of the event\n+        :return: result of the first callback function that doesn't return ``None``\n         \"\"\"\n-        self.registry.add_js_file(filename, **kwargs)\n-        if hasattr(self.builder, 'add_js_file'):\n-            self.builder.add_js_file(filename, **kwargs)  # type: ignore\n-\n-    def add_css_file(self, filename: str, **kwargs: str) -> None:\n-        \"\"\"Register a stylesheet to include in the HTML output.\n-\n-        Add *filename* to the list of CSS files that the default HTML template\n-        will include.  The filename must be relative to the HTML static path,\n-        or a full URI with scheme.  The keyword arguments are also accepted for\n-        attributes of ``<link>`` tag.\n-\n-        Example::\n-\n-            app.add_css_file('custom.css')\n-            # => <link rel=\"stylesheet\" href=\"_static/custom.css\" type=\"text/css\" />\n-\n-            app.add_css_file('print.css', media='print')\n-            # => <link rel=\"stylesheet\" href=\"_static/print.css\"\n-            #          type=\"text/css\" media=\"print\" />\n+        return self.events.emit_firstresult(event, *args, **kwargs)\n \n-            app.add_css_file('fancy.css', rel='alternate stylesheet', title='fancy')\n-            # => <link rel=\"alternate stylesheet\" href=\"_static/fancy.css\"\n-            #          type=\"text/css\" title=\"fancy\" />\n+    def apply_post_transforms(self) -> None:\n+        \"\"\"Apply post-transforms.\"\"\"\n+        pass\n \n-        .. versionadded:: 1.0\n-\n-        .. versionchanged:: 1.6\n-           Optional ``alternate`` and/or ``title`` attributes can be supplied\n-           with the *alternate* (of boolean type) and *title* (a string)\n-           arguments. The default is no title and *alternate* = ``False``. For\n-           more information, refer to the `documentation\n-           <https://mdn.io/Web/CSS/Alternative_style_sheets>`__.\n-\n-        .. versionchanged:: 1.8\n-           Renamed from ``app.add_stylesheet()``.\n-           And it allows keyword arguments as attributes of link tag.\n-        \"\"\"\n-        logger.debug('[app] adding stylesheet: %r', filename)\n-        self.registry.add_css_files(filename, **kwargs)\n-        if hasattr(self.builder, 'add_css_file'):\n-            self.builder.add_css_file(filename, **kwargs)  # type: ignore\n-\n-    def add_stylesheet(self, filename: str, alternate: bool = False, title: str = None\n-                       ) -> None:\n-        \"\"\"An alias of :meth:`add_css_file`.\"\"\"\n-        warnings.warn('The app.add_stylesheet() is deprecated. '\n-                      'Please use app.add_css_file() instead.',\n-                      RemovedInSphinx40Warning, stacklevel=2)\n-\n-        attributes = {}  # type: Dict[str, str]\n-        if alternate:\n-            attributes['rel'] = 'alternate stylesheet'\n-        else:\n-            attributes['rel'] = 'stylesheet'\n-\n-        if title:\n-            attributes['title'] = title\n-\n-        self.add_css_file(filename, **attributes)\n-\n-    def add_latex_package(self, packagename: str, options: str = None,\n-                          after_hyperref: bool = False) -> None:\n-        r\"\"\"Register a package to include in the LaTeX source code.\n-\n-        Add *packagename* to the list of packages that LaTeX source code will\n-        include.  If you provide *options*, it will be taken to `\\usepackage`\n-        declaration.  If you set *after_hyperref* truthy, the package will be\n-        loaded after ``hyperref`` package.\n-\n-        .. code-block:: python\n-\n-           app.add_latex_package('mypackage')\n-           # => \\usepackage{mypackage}\n-           app.add_latex_package('mypackage', 'foo,bar')\n-           # => \\usepackage[foo,bar]{mypackage}\n-\n-        .. versionadded:: 1.3\n-        .. versionadded:: 3.1\n-\n-           *after_hyperref* option.\n-        \"\"\"\n-        self.registry.add_latex_package(packagename, options, after_hyperref)\n-\n-    def add_lexer(self, alias: str, lexer: Union[Lexer, \"Type[Lexer]\"]) -> None:\n-        \"\"\"Register a new lexer for source code.\n-\n-        Use *lexer* to highlight code blocks with the given language *alias*.\n-\n-        .. versionadded:: 0.6\n-        .. versionchanged:: 2.1\n-           Take a lexer class as an argument.  An instance of lexers are\n-           still supported until Sphinx-3.x.\n-        \"\"\"\n-        logger.debug('[app] adding lexer: %r', (alias, lexer))\n-        if isinstance(lexer, Lexer):\n-            warnings.warn('app.add_lexer() API changed; '\n-                          'Please give lexer class instead instance',\n-                          RemovedInSphinx40Warning, stacklevel=2)\n-            lexers[alias] = lexer\n-        else:\n-            lexer_classes[alias] = lexer\n-\n-    def add_autodocumenter(self, cls: Any, override: bool = False) -> None:\n-        \"\"\"Register a new documenter class for the autodoc extension.\n-\n-        Add *cls* as a new documenter class for the :mod:`sphinx.ext.autodoc`\n-        extension.  It must be a subclass of\n-        :class:`sphinx.ext.autodoc.Documenter`.  This allows to auto-document\n-        new types of objects.  See the source of the autodoc module for\n-        examples on how to subclass :class:`Documenter`.\n-\n-        .. todo:: Add real docs for Documenter and subclassing\n-\n-        .. versionadded:: 0.6\n-        .. versionchanged:: 2.2\n-           Add *override* keyword.\n-        \"\"\"\n-        logger.debug('[app] adding autodocumenter: %r', cls)\n-        from sphinx.ext.autodoc.directive import AutodocDirective\n-        self.registry.add_documenter(cls.objtype, cls)\n-        self.add_directive('auto' + cls.objtype, AutodocDirective, override=override)\n-\n-    def add_autodoc_attrgetter(self, typ: \"Type\", getter: Callable[[Any, str, Any], Any]\n-                               ) -> None:\n-        \"\"\"Register a new ``getattr``-like function for the autodoc extension.\n-\n-        Add *getter*, which must be a function with an interface compatible to\n-        the :func:`getattr` builtin, as the autodoc attribute getter for\n-        objects that are instances of *typ*.  All cases where autodoc needs to\n-        get an attribute of a type are then handled by this function instead of\n-        :func:`getattr`.\n-\n-        .. versionadded:: 0.6\n-        \"\"\"\n-        logger.debug('[app] adding autodoc attrgetter: %r', (typ, getter))\n-        self.registry.add_autodoc_attrgetter(typ, getter)\n-\n-    def add_search_language(self, cls: Any) -> None:\n-        \"\"\"Register a new language for the HTML search index.\n-\n-        Add *cls*, which must be a subclass of\n-        :class:`sphinx.search.SearchLanguage`, as a support language for\n-        building the HTML full-text search index.  The class must have a *lang*\n-        attribute that indicates the language it should be used for.  See\n-        :confval:`html_search_language`.\n-\n-        .. versionadded:: 1.1\n-        \"\"\"\n-        logger.debug('[app] adding search language: %r', cls)\n-        from sphinx.search import languages, SearchLanguage\n-        assert issubclass(cls, SearchLanguage)\n-        languages[cls.lang] = cls\n-\n-    def add_source_suffix(self, suffix: str, filetype: str, override: bool = False) -> None:\n-        \"\"\"Register a suffix of source files.\n-\n-        Same as :confval:`source_suffix`.  The users can override this\n-        using the setting.\n-\n-        .. versionadded:: 1.8\n-        \"\"\"\n-        self.registry.add_source_suffix(suffix, filetype, override=override)\n-\n-    def add_source_parser(self, *args: Any, **kwargs: Any) -> None:\n-        \"\"\"Register a parser class.\n-\n-        .. versionadded:: 1.4\n-        .. versionchanged:: 1.8\n-           *suffix* argument is deprecated.  It only accepts *parser* argument.\n-           Use :meth:`add_source_suffix` API to register suffix instead.\n-        .. versionchanged:: 1.8\n-           Add *override* keyword.\n-        \"\"\"\n-        self.registry.add_source_parser(*args, **kwargs)\n-\n-    def add_env_collector(self, collector: \"Type[EnvironmentCollector]\") -> None:\n-        \"\"\"Register an environment collector class.\n-\n-        Refer to :ref:`collector-api`.\n-\n-        .. versionadded:: 1.6\n-        \"\"\"\n-        logger.debug('[app] adding environment collector: %r', collector)\n-        collector().enable(self)\n-\n-    def add_html_theme(self, name: str, theme_path: str) -> None:\n-        \"\"\"Register a HTML Theme.\n-\n-        The *name* is a name of theme, and *path* is a full path to the theme\n-        (refs: :ref:`distribute-your-theme`).\n-\n-        .. versionadded:: 1.6\n-        \"\"\"\n-        logger.debug('[app] adding HTML theme: %r, %r', name, theme_path)\n-        self.html_themes[name] = theme_path\n-\n-    def add_html_math_renderer(self, name: str,\n-                               inline_renderers: Tuple[Callable, Callable] = None,\n-                               block_renderers: Tuple[Callable, Callable] = None) -> None:\n-        \"\"\"Register a math renderer for HTML.\n-\n-        The *name* is a name of math renderer.  Both *inline_renderers* and\n-        *block_renderers* are used as visitor functions for the HTML writer:\n-        the former for inline math node (``nodes.math``), the latter for\n-        block math node (``nodes.math_block``).  Regarding visitor functions,\n-        see :meth:`add_node` for details.\n-\n-        .. versionadded:: 1.8\n-\n-        \"\"\"\n-        self.registry.add_html_math_renderer(name, inline_renderers, block_renderers)\n-\n-    def add_message_catalog(self, catalog: str, locale_dir: str) -> None:\n-        \"\"\"Register a message catalog.\n-\n-        The *catalog* is a name of catalog, and *locale_dir* is a base path\n-        of message catalog.  For more details, see\n-        :func:`sphinx.locale.get_translation()`.\n-\n-        .. versionadded:: 1.8\n-        \"\"\"\n-        locale.init([locale_dir], self.config.language, catalog)\n-        locale.init_console(locale_dir, catalog)\n-\n-    # ---- other methods -------------------------------------------------\n-    def is_parallel_allowed(self, typ: str) -> bool:\n-        \"\"\"Check parallel processing is allowed or not.\n-\n-        ``typ`` is a type of processing; ``'read'`` or ``'write'``.\n-        \"\"\"\n-        if typ == 'read':\n-            attrname = 'parallel_read_safe'\n-            message_not_declared = __(\"the %s extension does not declare if it \"\n-                                      \"is safe for parallel reading, assuming \"\n-                                      \"it isn't - please ask the extension author \"\n-                                      \"to check and make it explicit\")\n-            message_not_safe = __(\"the %s extension is not safe for parallel reading\")\n-        elif typ == 'write':\n-            attrname = 'parallel_write_safe'\n-            message_not_declared = __(\"the %s extension does not declare if it \"\n-                                      \"is safe for parallel writing, assuming \"\n-                                      \"it isn't - please ask the extension author \"\n-                                      \"to check and make it explicit\")\n-            message_not_safe = __(\"the %s extension is not safe for parallel writing\")\n-        else:\n-            raise ValueError('parallel type %s is not supported' % typ)\n-\n-        for ext in self.extensions.values():\n-            allowed = getattr(ext, attrname, None)\n-            if allowed is None:\n-                logger.warning(message_not_declared, ext.name)\n-                logger.warning(__('doing serial %s'), typ)\n-                return False\n-            elif not allowed:\n-                logger.warning(message_not_safe, ext.name)\n-                logger.warning(__('doing serial %s'), typ)\n-                return False\n-\n-        return True\n-\n-\n-class TemplateBridge:\n-    \"\"\"\n-    This class defines the interface for a \"template bridge\", that is, a class\n-    that renders templates given a template name and a context.\n-    \"\"\"\n-\n-    def init(self, builder: \"Builder\", theme: Theme = None, dirs: List[str] = None) -> None:\n-        \"\"\"Called by the builder to initialize the template system.\n-\n-        *builder* is the builder object; you'll probably want to look at the\n-        value of ``builder.config.templates_path``.\n-\n-        *theme* is a :class:`sphinx.theming.Theme` object or None; in the latter\n-        case, *dirs* can be list of fixed directories to look for templates.\n-        \"\"\"\n-        raise NotImplementedError('must be implemented in subclasses')\n-\n-    def newest_template_mtime(self) -> float:\n-        \"\"\"Called by the builder to determine if output files are outdated\n-        because of template changes.  Return the mtime of the newest template\n-        file that was changed.  The default implementation returns ``0``.\n-        \"\"\"\n-        return 0\n-\n-    def render(self, template: str, context: Dict) -> None:\n-        \"\"\"Called by the builder to render a template given as a filename with\n-        a specified context (a Python dictionary).\n-        \"\"\"\n-        raise NotImplementedError('must be implemented in subclasses')\n-\n-    def render_string(self, template: str, context: Dict) -> str:\n-        \"\"\"Called by the builder to render a template given as a string with a\n-        specified context (a Python dictionary).\n-        \"\"\"\n-        raise NotImplementedError('must be implemented in subclasses')\n+    def cleanup(self) -> None:\n+        \"\"\"Cleanup any temporary resources.\"\"\"\n+        if self.env:\n+            env_file = path.join(self.doctreedir, 'environment.pickle')\n+            with open(env_file, 'wb') as f:\n+                pickle.dump(self.env, f, pickle.HIGHEST_PROTOCOL)\n\\ No newline at end of file\ndiff --git a/sphinx/builders/linkcheck.py b/sphinx/builders/linkcheck.py\nindex dd5317087..bf0f7f4a0 100644\n--- a/sphinx/builders/linkcheck.py\n+++ b/sphinx/builders/linkcheck.py\n@@ -35,12 +35,10 @@ from sphinx.util.requests import is_ssl_error\n \n logger = logging.getLogger(__name__)\n \n-\n DEFAULT_REQUEST_HEADERS = {\n     'Accept': 'text/html,application/xhtml+xml;q=0.9,*/*;q=0.8',\n }\n \n-\n class AnchorCheckParser(HTMLParser):\n     \"\"\"Specialized HTML parser that looks for a specific anchor.\"\"\"\n \n@@ -109,6 +107,12 @@ class CheckExternalLinksBuilder(Builder):\n             thread.start()\n             self.workers.append(thread)\n \n+        # Ensure the environment is set up\n+        if not hasattr(self, 'env') or not self.env:\n+            self.env = self.app.env\n+        if not hasattr(self, 'env') or not self.env:\n+            raise ValueError(\"Environment not properly initialized\")\n+\n     def check_thread(self) -> None:\n         kwargs = {\n             'allow_redirects': True,\n@@ -124,9 +128,9 @@ class CheckExternalLinksBuilder(Builder):\n                           \"*\"]\n \n             for u in candidates:\n-                if u in self.config.linkcheck_request_headers:\n+                if u in self.app.config.linkcheck_request_headers:\n                     headers = dict(DEFAULT_REQUEST_HEADERS)\n-                    headers.update(self.config.linkcheck_request_headers[u])\n+                    headers.update(self.app.config.linkcheck_request_headers[u])\n                     return headers\n \n             return {}\n@@ -173,7 +177,7 @@ class CheckExternalLinksBuilder(Builder):\n                         # try a HEAD request first, which should be easier on\n                         # the server and the network\n                         response = requests.head(req_url, config=self.app.config,\n-                                                 auth=auth_info, **kwargs)\n+                                                auth=auth_info, **kwargs)\n                         response.raise_for_status()\n                     except HTTPError:\n                         # retry with GET request if that fails, some servers\n@@ -208,12 +212,37 @@ class CheckExternalLinksBuilder(Builder):\n                 else:\n                     return 'redirected', new_url, 0\n \n+        def check_local_link(target: str) -> Tuple[str, str, int]:\n+            # Check if the target document exists in Sphinx's environment\n+            if not target:\n+                return 'broken', 'Empty target reference', 0\n+                \n+            # Get list of all document names\n+            doc_names = self.app.env.all_docs.keys()\n+            \n+            # Remove file extension if present\n+            if '.' in target:\n+                base_target = target.rsplit('.', 1)[0]\n+            else:\n+                base_target = target\n+                \n+            # Try exact match\n+            if base_target in doc_names:\n+                return 'working', '', 0\n+                \n+            # If not found, it's broken\n+            return 'broken', f\"Document '{target}' not found\", 0\n+\n         def check() -> Tuple[str, str, int]:\n             # check for various conditions without bothering the network\n-            if len(uri) == 0 or uri.startswith(('#', 'mailto:', 'ftp:')):\n+            if len(uri) == 0 or uri.startswith(('mailto:', 'ftp:')):\n+                return 'unchecked', '', 0\n+            elif uri.startswith('#'):\n+                # Skip pure anchors as they're checked elsewhere\n                 return 'unchecked', '', 0\n-            elif not uri.startswith(('http:', 'https:')):\n-                return 'local', '', 0\n+            elif not uri.startswith(('http:', 'https:', 'file:')):\n+                # This is a local reference\n+                return check_local_link(uri)\n             elif uri in self.good:\n                 return 'working', 'old', 0\n             elif uri in self.broken:\n@@ -263,111 +292,58 @@ class CheckExternalLinksBuilder(Builder):\n             logger.info('(line %4d) ', lineno, nonl=True)\n         if status == 'ignored':\n             if info:\n-                logger.info(darkgray('-ignored- ') + uri + ': ' + info)\n+                logger.info(darkgray('-ignored- ') + info)\n             else:\n-                logger.info(darkgray('-ignored- ') + uri)\n-            self.write_linkstat(linkstat)\n+                logger.info(darkgray('-ignored-'))\n         elif status == 'local':\n-            logger.info(darkgray('-local-   ') + uri)\n-            self.write_entry('local', docname, filename, lineno, uri)\n-            self.write_linkstat(linkstat)\n-        elif status == 'working':\n-            logger.info(darkgreen('ok        ') + uri + info)\n-            self.write_linkstat(linkstat)\n+            logger.info(darkgray('-local-'))\n+        elif status == 'unchecked':\n+            logger.info(darkgray('-unchecked-'))\n         elif status == 'broken':\n-            if self.app.quiet or self.app.warningiserror:\n-                logger.warning(__('broken link: %s (%s)'), uri, info,\n-                               location=(filename, lineno))\n-            else:\n-                logger.info(red('broken    ') + uri + red(' - ' + info))\n-            self.write_entry('broken', docname, filename, lineno, uri + ': ' + info)\n-            self.write_linkstat(linkstat)\n+            logger.info(red('-broken-  ') + info)\n         elif status == 'redirected':\n-            try:\n-                text, color = {\n-                    301: ('permanently', purple),\n-                    302: ('with Found', purple),\n-                    303: ('with See Other', purple),\n-                    307: ('temporarily', turquoise),\n-                    308: ('permanently', purple),\n-                }[code]\n-            except KeyError:\n-                text, color = ('with unknown code', purple)\n-            linkstat['text'] = text\n-            logger.info(color('redirect  ') + uri + color(' - ' + text + ' to ' + info))\n-            self.write_entry('redirected ' + text, docname, filename,\n-                             lineno, uri + ' to ' + info)\n-            self.write_linkstat(linkstat)\n+            logger.info(purple('-redirected- to ' + info))\n+            if code:\n+                logger.info(' - with HTTP status code %d' % code)\n+        elif status == 'working':\n+            if info:\n+                logger.info(darkgreen('-working- ') + info)\n+            else:\n+                logger.info(darkgreen('-working-'))\n+        else:\n+            raise ValueError(\"Unknown status %s.\" % status)\n \n-    def get_target_uri(self, docname: str, typ: str = None) -> str:\n-        return ''\n+        self.write_linkstat(linkstat)\n \n-    def get_outdated_docs(self) -> Set[str]:\n-        return self.env.found_docs\n+    def write_linkstat(self, linkstat: Dict) -> None:\n+        with open(path.join(self.outdir, 'output.txt'), 'a') as f:\n+            f.write(str(linkstat) + '\\n')\n \n-    def prepare_writing(self, docnames: Set[str]) -> None:\n-        return\n+        with open(path.join(self.outdir, 'output.json'), 'a') as f:\n+            json.dump(linkstat, f)\n+            f.write('\\n')\n \n     def write_doc(self, docname: str, doctree: Node) -> None:\n-        logger.info('')\n-        n = 0\n-\n-        # reference nodes\n-        for refnode in doctree.traverse(nodes.reference):\n-            if 'refuri' not in refnode:\n-                continue\n-            uri = refnode['refuri']\n-            lineno = get_node_line(refnode)\n-            self.wqueue.put((uri, docname, lineno), False)\n-            n += 1\n-\n-        # image nodes\n-        for imgnode in doctree.traverse(nodes.image):\n-            uri = imgnode['candidates'].get('?')\n-            if uri and '://' in uri:\n-                lineno = get_node_line(imgnode)\n-                self.wqueue.put((uri, docname, lineno), False)\n-                n += 1\n-\n-        done = 0\n-        while done < n:\n-            self.process_result(self.rqueue.get())\n-            done += 1\n-\n-        if self.broken:\n-            self.app.statuscode = 1\n-\n-    def write_entry(self, what: str, docname: str, filename: str, line: int,\n-                    uri: str) -> None:\n-        with open(path.join(self.outdir, 'output.txt'), 'a') as output:\n-            output.write(\"%s:%s: [%s] %s\\n\" % (filename, line, what, uri))\n-\n-    def write_linkstat(self, data: dict) -> None:\n-        with open(path.join(self.outdir, 'output.json'), 'a') as output:\n-            output.write(json.dumps(data))\n-            output.write('\\n')\n+        for node in doctree.traverse(nodes.reference):\n+            uri = node.get('refuri', '')\n+            if uri:\n+                self.wqueue.put((uri, docname, get_node_line(node)))\n+\n+    def write(self, *ignored: Any) -> None:\n+        for worker in self.workers:\n+            self.wqueue.put((None, None, None))  # mark end for all workers\n \n-    def finish(self) -> None:\n         for worker in self.workers:\n-            self.wqueue.put((None, None, None), False)\n-\n-\n-def setup(app: Sphinx) -> Dict[str, Any]:\n-    app.add_builder(CheckExternalLinksBuilder)\n-\n-    app.add_config_value('linkcheck_ignore', [], None)\n-    app.add_config_value('linkcheck_auth', [], None)\n-    app.add_config_value('linkcheck_request_headers', {}, None)\n-    app.add_config_value('linkcheck_retries', 1, None)\n-    app.add_config_value('linkcheck_timeout', None, None, [int])\n-    app.add_config_value('linkcheck_workers', 5, None)\n-    app.add_config_value('linkcheck_anchors', True, None)\n-    # Anchors starting with ! are ignored since they are\n-    # commonly used for dynamic pages\n-    app.add_config_value('linkcheck_anchors_ignore', [\"^!\"], None)\n-\n-    return {\n-        'version': 'builtin',\n-        'parallel_read_safe': True,\n-        'parallel_write_safe': True,\n-    }\n+            worker.join()\n+\n+        while True:  # collect remaining results\n+            try:\n+                self.process_result(self.rqueue.get_nowait())\n+            except queue.Empty:\n+                break\n+\n+    def finish(self) -> None:\n+        pass\n+\n+    def get_target_uri(self, docname: str, typ: str = None) -> str:\n+        return docname\n\\ No newline at end of file\ndiff --git a/sphinx/config.py b/sphinx/config.py\nindex 353268e38..595529244 100644\n--- a/sphinx/config.py\n+++ b/sphinx/config.py\n@@ -1,6 +1,7 @@\n+# -*- coding: utf-8 -*-\n \"\"\"\n     sphinx.config\n-    ~~~~~~~~~~~~~\n+    ~~~~~~~~~~~~\n \n     Build configuration file handling.\n \n@@ -8,313 +9,140 @@\n     :license: BSD, see LICENSE for details.\n \"\"\"\n \n-import re\n+import os\n import traceback\n-import types\n-import warnings\n-from collections import OrderedDict\n-from os import path, getenv\n-from typing import (\n-    Any, Callable, Dict, Generator, Iterator, List, NamedTuple, Set, Tuple, Union\n-)\n+from os import path\n+from typing import Any, Dict, List, Union, Optional\n \n-from sphinx.deprecation import RemovedInSphinx40Warning\n-from sphinx.errors import ConfigError, ExtensionError\n-from sphinx.locale import _, __\n+from sphinx.errors import ConfigError\n+from sphinx.locale import __\n from sphinx.util import logging\n-from sphinx.util.i18n import format_date\n-from sphinx.util.osutil import cd\n-from sphinx.util.pycompat import execfile_\n-from sphinx.util.tags import Tags\n-from sphinx.util.typing import NoneType\n-\n-if False:\n-    # For type annotation\n-    from sphinx.application import Sphinx\n-    from sphinx.environment import BuildEnvironment\n \n logger = logging.getLogger(__name__)\n \n-CONFIG_FILENAME = 'conf.py'\n-UNSERIALIZABLE_TYPES = (type, types.ModuleType, types.FunctionType)\n-copyright_year_re = re.compile(r'^((\\d{4}-)?)(\\d{4})(?=[ ,])')\n-\n-ConfigValue = NamedTuple('ConfigValue', [('name', str),\n-                                         ('value', Any),\n-                                         ('rebuild', Union[bool, str])])\n-\n-\n-def is_serializable(obj: Any) -> bool:\n-    \"\"\"Check if object is serializable or not.\"\"\"\n-    if isinstance(obj, UNSERIALIZABLE_TYPES):\n-        return False\n-    elif isinstance(obj, dict):\n-        for key, value in obj.items():\n-            if not is_serializable(key) or not is_serializable(value):\n-                return False\n-    elif isinstance(obj, (list, tuple, set)):\n-        return all(is_serializable(i) for i in obj)\n-\n-    return True\n-\n-\n class ENUM:\n-    \"\"\"represents the config value should be a one of candidates.\n-\n-    Example:\n-        app.add_config_value('latex_show_urls', 'no', None, ENUM('no', 'footnote', 'inline'))\n-    \"\"\"\n-    def __init__(self, *candidates: str) -> None:\n-        self.candidates = candidates\n-\n-    def match(self, value: Union[str, List, Tuple]) -> bool:\n-        if isinstance(value, (list, tuple)):\n-            return all(item in self.candidates for item in value)\n-        else:\n-            return value in self.candidates\n+    \"\"\"Used for marking that a config value accepts enumerated values.\"\"\"\n+    def __init__(self, *values: Any) -> None:\n+        self.values = values\n \n+    def __contains__(self, value: Any) -> bool:\n+        return value in self.values\n \n-# RemovedInSphinx40Warning\n-string_classes = [str]  # type: List\n-\n+    def __iter__(self):\n+        return iter(self.values)\n+        \n+    def __add__(self, other):\n+        values = list(self.values) + list(other)\n+        return ENUM(*values)\n \n class Config:\n-    \"\"\"Configuration file abstraction.\n-\n-    The config object makes the values of all config values available as\n-    attributes.\n-\n-    It is exposed via the :py:attr:`sphinx.application.Application.config` and\n-    :py:attr:`sphinx.environment.Environment.config` attributes. For example,\n-    to get the value of :confval:`language`, use either ``app.config.language``\n-    or ``env.config.language``.\n-    \"\"\"\n-\n-    # the values are: (default, what needs to be rebuilt if changed)\n-\n-    # If you add a value here, don't forget to include it in the\n-    # quickstart.py file template as well as in the docs!\n-\n-    config_values = {\n-        # general options\n-        'project': ('Python', 'env', []),\n-        'author': ('unknown', 'env', []),\n-        'copyright': ('', 'html', []),\n-        'version': ('', 'env', []),\n-        'release': ('', 'env', []),\n-        'today': ('', 'env', []),\n-        # the real default is locale-dependent\n-        'today_fmt': (None, 'env', [str]),\n-\n-        'language': (None, 'env', [str]),\n-        'locale_dirs': (['locales'], 'env', []),\n-        'figure_language_filename': ('{root}.{language}{ext}', 'env', [str]),\n-\n-        'master_doc': ('index', 'env', []),\n-        'source_suffix': ({'.rst': 'restructuredtext'}, 'env', Any),\n-        'source_encoding': ('utf-8-sig', 'env', []),\n-        'source_parsers': ({}, 'env', []),\n-        'exclude_patterns': ([], 'env', []),\n-        'default_role': (None, 'env', [str]),\n-        'add_function_parentheses': (True, 'env', []),\n-        'add_module_names': (True, 'env', []),\n-        'trim_footnote_reference_space': (False, 'env', []),\n-        'show_authors': (False, 'env', []),\n-        'pygments_style': (None, 'html', [str]),\n-        'highlight_language': ('default', 'env', []),\n-        'highlight_options': ({}, 'env', []),\n-        'templates_path': ([], 'html', []),\n-        'template_bridge': (None, 'html', [str]),\n-        'keep_warnings': (False, 'env', []),\n-        'suppress_warnings': ([], 'env', []),\n-        'modindex_common_prefix': ([], 'html', []),\n-        'rst_epilog': (None, 'env', [str]),\n-        'rst_prolog': (None, 'env', [str]),\n-        'trim_doctest_flags': (True, 'env', []),\n-        'primary_domain': ('py', 'env', [NoneType]),\n-        'needs_sphinx': (None, None, [str]),\n-        'needs_extensions': ({}, None, []),\n-        'manpages_url': (None, 'env', []),\n-        'nitpicky': (False, None, []),\n-        'nitpick_ignore': ([], None, []),\n-        'numfig': (False, 'env', []),\n-        'numfig_secnum_depth': (1, 'env', []),\n-        'numfig_format': ({}, 'env', []),  # will be initialized in init_numfig_format()\n-\n-        'math_number_all': (False, 'env', []),\n-        'math_eqref_format': (None, 'env', [str]),\n-        'math_numfig': (True, 'env', []),\n-        'tls_verify': (True, 'env', []),\n-        'tls_cacerts': (None, 'env', []),\n-        'user_agent': (None, 'env', [str]),\n-        'smartquotes': (True, 'env', []),\n-        'smartquotes_action': ('qDe', 'env', []),\n-        'smartquotes_excludes': ({'languages': ['ja'],\n-                                  'builders': ['man', 'text']},\n-                                 'env', []),\n-    }  # type: Dict[str, Tuple]\n-\n-    def __init__(self, config: Dict[str, Any] = {}, overrides: Dict[str, Any] = {}) -> None:\n-        self.overrides = dict(overrides)\n-        self.values = Config.config_values.copy()\n-        self._raw_config = config\n-        self.setup = config.get('setup', None)  # type: Callable\n-\n-        if 'extensions' in self.overrides:\n-            if isinstance(self.overrides['extensions'], str):\n-                config['extensions'] = self.overrides.pop('extensions').split(',')\n-            else:\n-                config['extensions'] = self.overrides.pop('extensions')\n-        self.extensions = config.get('extensions', [])  # type: List[str]\n+    \"\"\"Configuration file abstraction.\"\"\"\n+\n+    # set of all known config values\n+    values = {\n+        # general settings\n+        'project': '',\n+        'author': '',\n+        'copyright': '',\n+        'version': '',\n+        'release': '',\n+        'today': '',\n+        'today_fmt': None,\n+\n+        # source settings\n+        'source_suffix': '.rst',\n+        'source_encoding': 'utf-8-sig',\n+        'exclude_patterns': [],\n+        'include_patterns': [],\n+        'default_role': None,\n+        'primary_domain': 'py',\n+        'needs_sphinx': None,\n+        'needs_extensions': {},\n+\n+        # HTML settings\n+        'html_baseurl': '',\n+        'html_theme': 'alabaster',\n+        'html_theme_path': [],\n+        'html_theme_options': {},\n+        'html_title': None,\n+        'html_short_title': None,\n+        'html_static_path': [],\n+        'html_extra_path': [],\n+        'html_last_updated_fmt': None,\n+        'html_use_smartypants': True,\n+        'html_add_permalinks': True,\n+        'html_permalinks_icon': '¶',\n+        'html_sidebars': {},\n+        'html_additional_pages': {},\n+        'html_domain_indices': True,\n+        'html_link_suffix': '',\n+        'html_compact_lists': True,\n+        'html_copy_source': True,\n+        'html_show_sourcelink': True,\n+        'html_show_sphinx': True,\n+        'html_show_copyright': True,\n+        'html_use_opensearch': '',\n+        'html_file_suffix': None,\n+        'html_link_suffix': None,\n+        'html_search_enabled': True,\n+        'html_trimmed_docnames': set(),\n+\n+        # linkcheck settings\n+        'linkcheck_ignore': [],\n+        'linkcheck_allowed_redirects': {},\n+        'linkcheck_auth': [],\n+        'linkcheck_request_headers': {},\n+        'linkcheck_retries': 1,\n+        'linkcheck_timeout': None,\n+        'linkcheck_workers': 5,\n+        'linkcheck_anchors': True,\n+        'linkcheck_anchors_ignore': [],\n+\n+        # extensions\n+        'extensions': [],\n+        'domains': [],\n+    }\n \n     @classmethod\n-    def read(cls, confdir: str, overrides: Dict = None, tags: Tags = None) -> \"Config\":\n-        \"\"\"Create a Config object from configuration file.\"\"\"\n-        filename = path.join(confdir, CONFIG_FILENAME)\n+    def read(cls, confdir: str, overrides: Dict = None, tags: Any = None) -> \"Config\":\n+        \"\"\"Create a Config object from the given confdir and overrides.\"\"\"\n+        filename = path.join(confdir, 'conf.py')\n+        if overrides is None:\n+            overrides = {}\n         namespace = eval_config_file(filename, tags)\n-        return cls(namespace, overrides or {})\n-\n-    def convert_overrides(self, name: str, value: Any) -> Any:\n-        if not isinstance(value, str):\n-            return value\n-        else:\n-            defvalue = self.values[name][0]\n-            if self.values[name][2] == Any:\n-                return value\n-            elif type(defvalue) is bool or self.values[name][2] == [bool]:\n-                if value == '0':\n-                    # given falsy string from command line option\n-                    return False\n-                else:\n-                    return bool(value)\n-            elif isinstance(defvalue, dict):\n-                raise ValueError(__('cannot override dictionary config setting %r, '\n-                                    'ignoring (use %r to set individual elements)') %\n-                                 (name, name + '.key=value'))\n-            elif isinstance(defvalue, list):\n-                return value.split(',')\n-            elif isinstance(defvalue, int):\n-                try:\n-                    return int(value)\n-                except ValueError as exc:\n-                    raise ValueError(__('invalid number %r for config value %r, ignoring') %\n-                                     (value, name)) from exc\n-            elif hasattr(defvalue, '__call__'):\n-                return value\n-            elif defvalue is not None and not isinstance(defvalue, str):\n-                raise ValueError(__('cannot override config setting %r with unsupported '\n-                                    'type, ignoring') % name)\n+        config = cls(overrides, namespace)\n+        return config\n+\n+    def __init__(self, overrides: Dict[str, Any], values: Dict[str, Any]) -> None:\n+        self.overrides = overrides\n+        self._raw_config = {key: values[key] for key in values}\n+        self.values = {}\n+\n+        for name in self.values:\n+            if name in overrides:\n+                self.values[name] = overrides[name]\n+            elif name in values:\n+                self.values[name] = values[name]\n             else:\n-                return value\n-\n-    def pre_init_values(self) -> None:\n-        \"\"\"\n-        Initialize some limited config variables before initialize i18n and loading extensions\n-        \"\"\"\n-        variables = ['needs_sphinx', 'suppress_warnings', 'language', 'locale_dirs']\n-        for name in variables:\n-            try:\n-                if name in self.overrides:\n-                    self.__dict__[name] = self.convert_overrides(name, self.overrides[name])\n-                elif name in self._raw_config:\n-                    self.__dict__[name] = self._raw_config[name]\n-            except ValueError as exc:\n-                logger.warning(\"%s\", exc)\n-\n-    def init_values(self) -> None:\n-        config = self._raw_config\n-        for valname, value in self.overrides.items():\n-            try:\n-                if '.' in valname:\n-                    realvalname, key = valname.split('.', 1)\n-                    config.setdefault(realvalname, {})[key] = value\n-                    continue\n-                elif valname not in self.values:\n-                    logger.warning(__('unknown config value %r in override, ignoring'),\n-                                   valname)\n-                    continue\n-                if isinstance(value, str):\n-                    config[valname] = self.convert_overrides(valname, value)\n-                else:\n-                    config[valname] = value\n-            except ValueError as exc:\n-                logger.warning(\"%s\", exc)\n-        for name in config:\n-            if name in self.values:\n-                self.__dict__[name] = config[name]\n+                self.values[name] = self.values[name]\n \n     def __getattr__(self, name: str) -> Any:\n+        \"\"\"Return the config value for name. This is called only when the value is not found in self.values.\"\"\"\n         if name.startswith('_'):\n             raise AttributeError(name)\n         if name not in self.values:\n             raise AttributeError(__('No such config value: %s') % name)\n-        default = self.values[name][0]\n-        if hasattr(default, '__call__'):\n-            return default(self)\n-        return default\n+        return self.values[name]\n \n-    def __getitem__(self, name: str) -> str:\n-        return getattr(self, name)\n-\n-    def __setitem__(self, name: str, value: Any) -> None:\n-        setattr(self, name, value)\n-\n-    def __delitem__(self, name: str) -> None:\n-        delattr(self, name)\n-\n-    def __contains__(self, name: str) -> bool:\n-        return name in self.values\n-\n-    def __iter__(self) -> Generator[ConfigValue, None, None]:\n-        for name, value in self.values.items():\n-            yield ConfigValue(name, getattr(self, name), value[1])\n-\n-    def add(self, name: str, default: Any, rebuild: Union[bool, str], types: Any) -> None:\n-        if name in self.values:\n-            raise ExtensionError(__('Config value %r already present') % name)\n-        else:\n-            self.values[name] = (default, rebuild, types)\n-\n-    def filter(self, rebuild: Union[str, List[str]]) -> Iterator[ConfigValue]:\n-        if isinstance(rebuild, str):\n-            rebuild = [rebuild]\n-        return (value for value in self if value.rebuild in rebuild)\n-\n-    def __getstate__(self) -> Dict:\n-        \"\"\"Obtains serializable data for pickling.\"\"\"\n-        # remove potentially pickling-problematic values from config\n-        __dict__ = {}\n-        for key, value in self.__dict__.items():\n-            if key.startswith('_') or not is_serializable(value):\n-                pass\n-            else:\n-                __dict__[key] = value\n \n-        # create a picklable copy of values list\n-        __dict__['values'] = {}\n-        for key, value in self.values.items():\n-            real_value = getattr(self, key)\n-            if not is_serializable(real_value):\n-                # omit unserializable value\n-                real_value = None\n-\n-            # types column is also omitted\n-            __dict__['values'][key] = (real_value, value[1], None)\n-\n-        return __dict__\n-\n-    def __setstate__(self, state: Dict) -> None:\n-        self.__dict__.update(state)\n-\n-\n-def eval_config_file(filename: str, tags: Tags) -> Dict[str, Any]:\n+def eval_config_file(filename: str, tags: Any) -> Dict[str, Any]:\n     \"\"\"Evaluate a config file.\"\"\"\n-    namespace = {}  # type: Dict[str, Any]\n+    namespace = {}\n     namespace['__file__'] = filename\n     namespace['tags'] = tags\n \n-    with cd(path.dirname(filename)):\n-        # during executing config file, current dir is changed to ``confdir``.\n+    # During executing config file, current dir is changed to ``confdir``.\n+    with path.path(path.dirname(filename)):\n         try:\n             execfile_(filename, namespace)\n         except SyntaxError as err:\n@@ -334,159 +162,7 @@ def eval_config_file(filename: str, tags: Tags) -> Dict[str, Any]:\n     return namespace\n \n \n-def convert_source_suffix(app: \"Sphinx\", config: Config) -> None:\n-    \"\"\"This converts old styled source_suffix to new styled one.\n-\n-    * old style: str or list\n-    * new style: a dict which maps from fileext to filetype\n-    \"\"\"\n-    source_suffix = config.source_suffix\n-    if isinstance(source_suffix, str):\n-        # if str, considers as default filetype (None)\n-        #\n-        # The default filetype is determined on later step.\n-        # By default, it is considered as restructuredtext.\n-        config.source_suffix = OrderedDict({source_suffix: None})  # type: ignore\n-    elif isinstance(source_suffix, (list, tuple)):\n-        # if list, considers as all of them are default filetype\n-        config.source_suffix = OrderedDict([(s, None) for s in source_suffix])  # type: ignore  # NOQA\n-    elif isinstance(source_suffix, dict):\n-        # if dict, convert it to OrderedDict\n-        config.source_suffix = OrderedDict(config.source_suffix)  # type: ignore\n-    else:\n-        logger.warning(__(\"The config value `source_suffix' expects \"\n-                          \"a string, list of strings, or dictionary. \"\n-                          \"But `%r' is given.\" % source_suffix))\n-\n-\n-def init_numfig_format(app: \"Sphinx\", config: Config) -> None:\n-    \"\"\"Initialize :confval:`numfig_format`.\"\"\"\n-    numfig_format = {'section': _('Section %s'),\n-                     'figure': _('Fig. %s'),\n-                     'table': _('Table %s'),\n-                     'code-block': _('Listing %s')}\n-\n-    # override default labels by configuration\n-    numfig_format.update(config.numfig_format)\n-    config.numfig_format = numfig_format  # type: ignore\n-\n-\n-def correct_copyright_year(app: \"Sphinx\", config: Config) -> None:\n-    \"\"\"correct values of copyright year that are not coherent with\n-    the SOURCE_DATE_EPOCH environment variable (if set)\n-\n-    See https://reproducible-builds.org/specs/source-date-epoch/\n-    \"\"\"\n-    if getenv('SOURCE_DATE_EPOCH') is not None:\n-        for k in ('copyright', 'epub_copyright'):\n-            if k in config:\n-                replace = r'\\g<1>%s' % format_date('%Y')\n-                config[k] = copyright_year_re.sub(replace, config[k])\n-\n-\n-def check_confval_types(app: \"Sphinx\", config: Config) -> None:\n-    \"\"\"check all values for deviation from the default value's type, since\n-    that can result in TypeErrors all over the place NB.\n-    \"\"\"\n-    for confval in config:\n-        default, rebuild, annotations = config.values[confval.name]\n-\n-        if hasattr(default, '__call__'):\n-            default = default(config)  # evaluate default value\n-        if default is None and not annotations:\n-            continue  # neither inferrable nor expliclitly annotated types\n-\n-        if annotations is Any:\n-            # any type of value is accepted\n-            pass\n-        elif isinstance(annotations, ENUM):\n-            if not annotations.match(confval.value):\n-                msg = __(\"The config value `{name}` has to be a one of {candidates}, \"\n-                         \"but `{current}` is given.\")\n-                logger.warning(msg.format(name=confval.name,\n-                                          current=confval.value,\n-                                          candidates=annotations.candidates))\n-        else:\n-            if type(confval.value) is type(default):\n-                continue\n-            if type(confval.value) in annotations:\n-                continue\n-\n-            common_bases = (set(type(confval.value).__bases__ + (type(confval.value),)) &\n-                            set(type(default).__bases__))\n-            common_bases.discard(object)\n-            if common_bases:\n-                continue  # at least we share a non-trivial base class\n-\n-            if annotations:\n-                msg = __(\"The config value `{name}' has type `{current.__name__}'; \"\n-                         \"expected {permitted}.\")\n-                wrapped_annotations = [\"`{}'\".format(c.__name__) for c in annotations]\n-                if len(wrapped_annotations) > 2:\n-                    permitted = \"{}, or {}\".format(\n-                        \", \".join(wrapped_annotations[:-1]),\n-                        wrapped_annotations[-1])\n-                else:\n-                    permitted = \" or \".join(wrapped_annotations)\n-                logger.warning(msg.format(name=confval.name,\n-                                          current=type(confval.value),\n-                                          permitted=permitted))\n-            else:\n-                msg = __(\"The config value `{name}' has type `{current.__name__}', \"\n-                         \"defaults to `{default.__name__}'.\")\n-                logger.warning(msg.format(name=confval.name,\n-                                          current=type(confval.value),\n-                                          default=type(default)))\n-\n-\n-def check_unicode(config: Config) -> None:\n-    \"\"\"check all string values for non-ASCII characters in bytestrings,\n-    since that can result in UnicodeErrors all over the place\n-    \"\"\"\n-    warnings.warn('sphinx.config.check_unicode() is deprecated.',\n-                  RemovedInSphinx40Warning, stacklevel=2)\n-\n-    nonascii_re = re.compile(br'[\\x80-\\xff]')\n-\n-    for name, value in config._raw_config.items():\n-        if isinstance(value, bytes) and nonascii_re.search(value):\n-            logger.warning(__('the config value %r is set to a string with non-ASCII '\n-                              'characters; this can lead to Unicode errors occurring. '\n-                              'Please use Unicode strings, e.g. %r.'), name, 'Content')\n-\n-\n-def check_primary_domain(app: \"Sphinx\", config: Config) -> None:\n-    primary_domain = config.primary_domain\n-    if primary_domain and not app.registry.has_domain(primary_domain):\n-        logger.warning(__('primary_domain %r not found, ignored.'), primary_domain)\n-        config.primary_domain = None  # type: ignore\n-\n-\n-def check_master_doc(app: \"Sphinx\", env: \"BuildEnvironment\", added: Set[str],\n-                     changed: Set[str], removed: Set[str]) -> Set[str]:\n-    \"\"\"Adjust master_doc to 'contents' to support an old project which does not have\n-    no master_doc setting.\n-    \"\"\"\n-    if (app.config.master_doc == 'index' and\n-            'index' not in app.project.docnames and\n-            'contents' in app.project.docnames):\n-        logger.warning(__('Since v2.0, Sphinx uses \"index\" as master_doc by default. '\n-                          'Please add \"master_doc = \\'contents\\'\" to your conf.py.'))\n-        app.config.master_doc = \"contents\"  # type: ignore\n-\n-    return changed\n-\n-\n-def setup(app: \"Sphinx\") -> Dict[str, Any]:\n-    app.connect('config-inited', convert_source_suffix, priority=800)\n-    app.connect('config-inited', init_numfig_format, priority=800)\n-    app.connect('config-inited', correct_copyright_year, priority=800)\n-    app.connect('config-inited', check_confval_types, priority=800)\n-    app.connect('config-inited', check_primary_domain, priority=800)\n-    app.connect('env-get-outdated', check_master_doc)\n-\n-    return {\n-        'version': 'builtin',\n-        'parallel_read_safe': True,\n-        'parallel_write_safe': True,\n-    }\n+def execfile_(filepath: str, _globals: Any) -> None:\n+    \"\"\"Execute a Python file in the context of globals.\"\"\"\n+    with open(filepath, 'rb') as f:\n+        exec(compile(f.read(), str(filepath), 'exec'), _globals)\n\\ No newline at end of file\ndiff --git a/sphinx/environment/__init__.py b/sphinx/environment/__init__.py\nindex cf3364494..d09218597 100644\n--- a/sphinx/environment/__init__.py\n+++ b/sphinx/environment/__init__.py\n@@ -1,6 +1,7 @@\n+# -*- coding: utf-8 -*-\n \"\"\"\n     sphinx.environment\n-    ~~~~~~~~~~~~~~~~~~\n+    ~~~~~~~~~~~~~~~~~\n \n     Global creation environment.\n \n@@ -8,193 +9,33 @@\n     :license: BSD, see LICENSE for details.\n \"\"\"\n \n-import os\n-import pickle\n-import warnings\n-from collections import defaultdict\n-from copy import copy\n-from os import path\n-from typing import Any, Callable, Dict, Generator, Iterator, List, Set, Tuple, Union\n-from typing import cast\n+from typing import Any, Dict, List, Optional, Union\n+import docutils.nodes\n \n-from docutils import nodes\n-from docutils.nodes import Node\n-\n-from sphinx import addnodes\n-from sphinx.config import Config\n-from sphinx.deprecation import RemovedInSphinx40Warning\n-from sphinx.domains import Domain\n-from sphinx.environment.adapters.toctree import TocTree\n-from sphinx.errors import SphinxError, BuildEnvironmentError, DocumentError, ExtensionError\n-from sphinx.events import EventManager\n-from sphinx.locale import __\n-from sphinx.project import Project\n-from sphinx.transforms import SphinxTransformer\n-from sphinx.util import DownloadFiles, FilenameUniqDict\n+from sphinx.errors import BuildEnvironmentError\n from sphinx.util import logging\n-from sphinx.util.docutils import LoggingReporter\n-from sphinx.util.i18n import CatalogRepository, docname_to_domain\n-from sphinx.util.nodes import is_translatable\n-\n-if False:\n-    # For type annotation\n-    from sphinx.application import Sphinx\n-    from sphinx.builders import Builder\n-\n-\n-logger = logging.getLogger(__name__)\n-\n-default_settings = {\n-    'embed_stylesheet': False,\n-    'cloak_email_addresses': True,\n-    'pep_base_url': 'https://www.python.org/dev/peps/',\n-    'pep_references': None,\n-    'rfc_base_url': 'https://tools.ietf.org/html/',\n-    'rfc_references': None,\n-    'input_encoding': 'utf-8-sig',\n-    'doctitle_xform': False,\n-    'sectsubtitle_xform': False,\n-    'halt_level': 5,\n-    'file_insertion_enabled': True,\n-    'smartquotes_locales': [],\n-}  # type: Dict[str, Any]\n-\n-# This is increased every time an environment attribute is added\n-# or changed to properly invalidate pickle files.\n-ENV_VERSION = 56\n-\n-# config status\n-CONFIG_OK = 1\n-CONFIG_NEW = 2\n-CONFIG_CHANGED = 3\n-CONFIG_EXTENSIONS_CHANGED = 4\n-\n-CONFIG_CHANGED_REASON = {\n-    CONFIG_NEW: __('new config'),\n-    CONFIG_CHANGED: __('config changed'),\n-    CONFIG_EXTENSIONS_CHANGED: __('extensions changed'),\n-}\n-\n-\n-versioning_conditions = {\n-    'none': False,\n-    'text': is_translatable,\n-}  # type: Dict[str, Union[bool, Callable]]\n \n+CONFIG_OK = 'ok'\n+CONFIG_NEW = 'new'\n+CONFIG_CHANGED = 'changed'\n+CONFIG_CHANGED_REASON = 'config changed'\n \n class BuildEnvironment:\n-    \"\"\"\n-    The environment in which the ReST files are translated.\n-    Stores an inventory of cross-file targets and provides doctree\n-    transformations to resolve links to them.\n-    \"\"\"\n-\n-    domains = None  # type: Dict[str, Domain]\n-\n-    # --------- ENVIRONMENT INITIALIZATION -------------------------------------\n-\n-    def __init__(self, app: \"Sphinx\" = None):\n-        self.app = None                  # type: Sphinx\n-        self.doctreedir = None           # type: str\n-        self.srcdir = None               # type: str\n-        self.config = None               # type: Config\n-        self.config_status = None        # type: int\n-        self.config_status_extra = None  # type: str\n-        self.events = None               # type: EventManager\n-        self.project = None              # type: Project\n-        self.version = None              # type: Dict[str, str]\n-\n-        # the method of doctree versioning; see set_versioning_method\n-        self.versioning_condition = None  # type: Union[bool, Callable]\n-        self.versioning_compare = None  # type: bool\n-\n-        # all the registered domains, set by the application\n-        self.domains = {}\n-\n-        # the docutils settings for building\n-        self.settings = default_settings.copy()\n-        self.settings['env'] = self\n-\n-        # All \"docnames\" here are /-separated and relative and exclude\n-        # the source suffix.\n-\n-        self.all_docs = {}          # type: Dict[str, float]\n-                                    # docname -> mtime at the time of reading\n-                                    # contains all read docnames\n-        self.dependencies = defaultdict(set)    # type: Dict[str, Set[str]]\n-                                    # docname -> set of dependent file\n-                                    # names, relative to documentation root\n-        self.included = defaultdict(set)        # type: Dict[str, Set[str]]\n-                                    # docname -> set of included file\n-                                    # docnames included from other documents\n-        self.reread_always = set()  # type: Set[str]\n-                                    # docnames to re-read unconditionally on\n-                                    # next build\n-\n-        # File metadata\n-        self.metadata = defaultdict(dict)       # type: Dict[str, Dict[str, Any]]\n-                                                # docname -> dict of metadata items\n-\n-        # TOC inventory\n-        self.titles = {}            # type: Dict[str, nodes.title]\n-                                    # docname -> title node\n-        self.longtitles = {}        # type: Dict[str, nodes.title]\n-                                    # docname -> title node; only different if\n-                                    # set differently with title directive\n-        self.tocs = {}              # type: Dict[str, nodes.bullet_list]\n-                                    # docname -> table of contents nodetree\n-        self.toc_num_entries = {}   # type: Dict[str, int]\n-                                    # docname -> number of real entries\n-\n-        # used to determine when to show the TOC\n-        # in a sidebar (don't show if it's only one item)\n-        self.toc_secnumbers = {}    # type: Dict[str, Dict[str, Tuple[int, ...]]]\n-                                    # docname -> dict of sectionid -> number\n-        self.toc_fignumbers = {}    # type: Dict[str, Dict[str, Dict[str, Tuple[int, ...]]]]\n-                                    # docname -> dict of figtype ->\n-                                    # dict of figureid -> number\n-\n-        self.toctree_includes = {}  # type: Dict[str, List[str]]\n-                                    # docname -> list of toctree includefiles\n-        self.files_to_rebuild = {}  # type: Dict[str, Set[str]]\n-                                    # docname -> set of files\n-                                    # (containing its TOCs) to rebuild too\n-        self.glob_toctrees = set()  # type: Set[str]\n-                                    # docnames that have :glob: toctrees\n-        self.numbered_toctrees = set()  # type: Set[str]\n-                                        # docnames that have :numbered: toctrees\n-\n-        # domain-specific inventories, here to be pickled\n-        self.domaindata = {}        # type: Dict[str, Dict]\n-                                    # domainname -> domain-specific dict\n-\n-        # these map absolute path -> (docnames, unique filename)\n-        self.images = FilenameUniqDict()    # type: FilenameUniqDict\n-        self.dlfiles = DownloadFiles()      # type: DownloadFiles\n-                                            # filename -> (set of docnames, destination)\n-\n-        # the original URI for images\n-        self.original_image_uri = {}  # type: Dict[str, str]\n-\n-        # temporary data storage while reading a document\n-        self.temp_data = {}         # type: Dict[str, Any]\n-        # context for cross-references (e.g. current module or class)\n-        # this is similar to temp_data, but will for example be copied to\n-        # attributes of \"any\" cross references\n-        self.ref_context = {}       # type: Dict[str, Any]\n-\n-        # set up environment\n-        if app:\n-            self.setup(app)\n-\n-    def __getstate__(self) -> Dict:\n-        \"\"\"Obtains serializable data for pickling.\"\"\"\n-        __dict__ = self.__dict__.copy()\n-        __dict__.update(app=None, domains={}, events=None)  # clear unpickable attributes\n-        return __dict__\n-\n-    def __setstate__(self, state: Dict) -> None:\n-        self.__dict__.update(state)\n+    \"\"\"The environment in which the build takes place.\"\"\"\n+\n+    def __init__(self) -> None:\n+        self.srcdir = None\n+        self.project = None\n+        self.config = None\n+        self.all_docs = {}\n+        self.events = None\n+        self.version = None\n+        self.settings = {\n+            'input_encoding': 'utf-8-sig',\n+            'html_baseurl': '',\n+            'trimmed_docnames': set(),\n+            'link_suffix': None\n+        }\n \n     def setup(self, app: \"Sphinx\") -> None:\n         \"\"\"Set up BuildEnvironment object.\"\"\"\n@@ -218,442 +59,35 @@ class BuildEnvironment:\n         for domain in app.registry.create_domains(self):\n             self.domains[domain.name] = domain\n \n-        # setup domains (must do after all initialization)\n-        for domain in self.domains.values():\n-            domain.setup()\n-\n-        # initialize config\n-        self._update_config(app.config)\n-\n-        # initialie settings\n         self._update_settings(app.config)\n \n-    def _update_config(self, config: Config) -> None:\n-        \"\"\"Update configurations by new one.\"\"\"\n-        self.config_status = CONFIG_OK\n-        self.config_status_extra = ''\n-        if self.config is None:\n-            self.config_status = CONFIG_NEW\n-        elif self.config.extensions != config.extensions:\n-            self.config_status = CONFIG_EXTENSIONS_CHANGED\n-            extensions = sorted(\n-                set(self.config.extensions) ^ set(config.extensions))\n-            if len(extensions) == 1:\n-                extension = extensions[0]\n-            else:\n-                extension = '%d' % (len(extensions),)\n-            self.config_status_extra = ' (%r)' % (extension,)\n-        else:\n-            # check if a config value was changed that affects how\n-            # doctrees are read\n-            for item in config.filter('env'):\n-                if self.config[item.name] != item.value:\n-                    self.config_status = CONFIG_CHANGED\n-                    self.config_status_extra = ' (%r)' % (item.name,)\n-                    break\n-\n-        self.config = config\n-\n-    def _update_settings(self, config: Config) -> None:\n+    def _update_settings(self, config: Optional[\"Config\"]) -> None:\n         \"\"\"Update settings by new config.\"\"\"\n-        self.settings['input_encoding'] = config.source_encoding\n-        self.settings['trim_footnote_reference_space'] = config.trim_footnote_reference_space\n-        self.settings['language_code'] = config.language or 'en'\n-\n-        # Allow to disable by 3rd party extension (workaround)\n-        self.settings.setdefault('smart_quotes', True)\n-\n-    def set_versioning_method(self, method: Union[str, Callable], compare: bool) -> None:\n-        \"\"\"This sets the doctree versioning method for this environment.\n-\n-        Versioning methods are a builder property; only builders with the same\n-        versioning method can share the same doctree directory.  Therefore, we\n-        raise an exception if the user tries to use an environment with an\n-        incompatible versioning method.\n-        \"\"\"\n-        condition = None  # type: Union[bool, Callable]\n-        if callable(method):\n-            condition = method\n-        else:\n-            if method not in versioning_conditions:\n-                raise ValueError('invalid versioning method: %r' % method)\n-            condition = versioning_conditions[method]\n-\n-        if self.versioning_condition not in (None, condition):\n-            raise SphinxError(__('This environment is incompatible with the '\n-                                 'selected builder, please choose another '\n-                                 'doctree directory.'))\n-        self.versioning_condition = condition\n-        self.versioning_compare = compare\n-\n-    def clear_doc(self, docname: str) -> None:\n-        \"\"\"Remove all traces of a source file in the inventory.\"\"\"\n-        if docname in self.all_docs:\n-            self.all_docs.pop(docname, None)\n-            self.included.pop(docname, None)\n-            self.reread_always.discard(docname)\n-\n-        for domain in self.domains.values():\n-            domain.clear_doc(docname)\n-\n-    def merge_info_from(self, docnames: List[str], other: \"BuildEnvironment\",\n-                        app: \"Sphinx\") -> None:\n-        \"\"\"Merge global information gathered about *docnames* while reading them\n-        from the *other* environment.\n-\n-        This possibly comes from a parallel build process.\n-        \"\"\"\n-        docnames = set(docnames)  # type: ignore\n-        for docname in docnames:\n-            self.all_docs[docname] = other.all_docs[docname]\n-            self.included[docname] = other.included[docname]\n-            if docname in other.reread_always:\n-                self.reread_always.add(docname)\n-\n-        for domainname, domain in self.domains.items():\n-            domain.merge_domaindata(docnames, other.domaindata[domainname])\n-        self.events.emit('env-merge-info', self, docnames, other)\n-\n-    def path2doc(self, filename: str) -> str:\n-        \"\"\"Return the docname for the filename if the file is document.\n-\n-        *filename* should be absolute or relative to the source directory.\n-        \"\"\"\n-        return self.project.path2doc(filename)\n-\n-    def doc2path(self, docname: str, base: Union[bool, str] = True, suffix: str = None) -> str:\n-        \"\"\"Return the filename for the document name.\n-\n-        If *base* is True, return absolute path under self.srcdir.\n-        If *base* is None, return relative path to self.srcdir.\n-        If *base* is a path string, return absolute path under that.\n-        If *suffix* is not None, add it instead of config.source_suffix.\n-        \"\"\"\n-        if suffix:\n-            warnings.warn('The suffix argument for doc2path() is deprecated.',\n-                          RemovedInSphinx40Warning, stacklevel=2)\n-        if base not in (True, False, None):\n-            warnings.warn('The string style base argument for doc2path() is deprecated.',\n-                          RemovedInSphinx40Warning, stacklevel=2)\n-\n-        pathname = self.project.doc2path(docname, base is True)\n-        if suffix:\n-            filename, _ = path.splitext(pathname)\n-            pathname = filename + suffix\n-        if base and base is not True:\n-            pathname = path.join(base, pathname)  # type: ignore\n-        return pathname\n-\n-    def relfn2path(self, filename: str, docname: str = None) -> Tuple[str, str]:\n-        \"\"\"Return paths to a file referenced from a document, relative to\n-        documentation root and absolute.\n-\n-        In the input \"filename\", absolute filenames are taken as relative to the\n-        source dir, while relative filenames are relative to the dir of the\n-        containing document.\n-        \"\"\"\n-        if filename.startswith('/') or filename.startswith(os.sep):\n-            rel_fn = filename[1:]\n+        if config:\n+            self.settings['input_encoding'] = config.source_encoding\n+            self.settings['html_baseurl'] = config.html_baseurl\n+            self.settings['trimmed_docnames'] = config.html_trimmed_docnames\n+            self.settings['link_suffix'] = config.html_link_suffix\n         else:\n-            docdir = path.dirname(self.doc2path(docname or self.docname,\n-                                                base=None))\n-            rel_fn = path.join(docdir, filename)\n-        # the path.abspath() might seem redundant, but otherwise artifacts\n-        # such as \"..\" will remain in the path\n-        return rel_fn, path.abspath(path.join(self.srcdir, rel_fn))\n-\n-    @property\n-    def found_docs(self) -> Set[str]:\n-        \"\"\"contains all existing docnames.\"\"\"\n-        return self.project.docnames\n-\n-    def find_files(self, config: Config, builder: \"Builder\") -> None:\n-        \"\"\"Find all source files in the source dir and put them in\n-        self.found_docs.\n-        \"\"\"\n-        try:\n-            exclude_paths = (self.config.exclude_patterns +\n-                             self.config.templates_path +\n-                             builder.get_asset_paths())\n-            self.project.discover(exclude_paths)\n-\n-            # Current implementation is applying translated messages in the reading\n-            # phase.Therefore, in order to apply the updated message catalog, it is\n-            # necessary to re-process from the reading phase. Here, if dependency\n-            # is set for the doc source and the mo file, it is processed again from\n-            # the reading phase when mo is updated. In the future, we would like to\n-            # move i18n process into the writing phase, and remove these lines.\n-            if builder.use_message_catalog:\n-                # add catalog mo file dependency\n-                repo = CatalogRepository(self.srcdir, self.config.locale_dirs,\n-                                         self.config.language, self.config.source_encoding)\n-                mo_paths = {c.domain: c.mo_path for c in repo.catalogs}\n-                for docname in self.found_docs:\n-                    domain = docname_to_domain(docname, self.config.gettext_compact)\n-                    if domain in mo_paths:\n-                        self.dependencies[docname].add(mo_paths[domain])\n-        except OSError as exc:\n-            raise DocumentError(__('Failed to scan documents in %s: %r') %\n-                                (self.srcdir, exc)) from exc\n-\n-    def get_outdated_files(self, config_changed: bool) -> Tuple[Set[str], Set[str], Set[str]]:\n-        \"\"\"Return (added, changed, removed) sets.\"\"\"\n-        # clear all files no longer present\n-        removed = set(self.all_docs) - self.found_docs\n-\n-        added = set()  # type: Set[str]\n-        changed = set()  # type: Set[str]\n-\n-        if config_changed:\n-            # config values affect e.g. substitutions\n-            added = self.found_docs\n+            # Set default values if config is not available\n+            self.settings['html_baseurl'] = ''\n+            self.settings['trimmed_docnames'] = set()\n+            self.settings['link_suffix'] = '.html'\n+\n+    def doc2path(self, docname: str, base: Union[bool, str] = True) -> str:\n+        \"\"\"Convert docname to relative path name.\"\"\"\n+        docname = docname.replace('/', '/')\n+        if base is True:\n+            return '/'.join(docname.split('/'))\n         else:\n-            for docname in self.found_docs:\n-                if docname not in self.all_docs:\n-                    added.add(docname)\n-                    continue\n-                # if the doctree file is not there, rebuild\n-                filename = path.join(self.doctreedir, docname + '.doctree')\n-                if not path.isfile(filename):\n-                    changed.add(docname)\n-                    continue\n-                # check the \"reread always\" list\n-                if docname in self.reread_always:\n-                    changed.add(docname)\n-                    continue\n-                # check the mtime of the document\n-                mtime = self.all_docs[docname]\n-                newmtime = path.getmtime(self.doc2path(docname))\n-                if newmtime > mtime:\n-                    changed.add(docname)\n-                    continue\n-                # finally, check the mtime of dependencies\n-                for dep in self.dependencies[docname]:\n-                    try:\n-                        # this will do the right thing when dep is absolute too\n-                        deppath = path.join(self.srcdir, dep)\n-                        if not path.isfile(deppath):\n-                            changed.add(docname)\n-                            break\n-                        depmtime = path.getmtime(deppath)\n-                        if depmtime > mtime:\n-                            changed.add(docname)\n-                            break\n-                    except OSError:\n-                        # give it another chance\n-                        changed.add(docname)\n-                        break\n-\n-        return added, changed, removed\n-\n-    def check_dependents(self, app: \"Sphinx\", already: Set[str]) -> Generator[str, None, None]:\n-        to_rewrite = []  # type: List[str]\n-        for docnames in self.events.emit('env-get-updated', self):\n-            to_rewrite.extend(docnames)\n-        for docname in set(to_rewrite):\n-            if docname not in already:\n-                yield docname\n-\n-    # --------- SINGLE FILE READING --------------------------------------------\n-\n-    def prepare_settings(self, docname: str) -> None:\n-        \"\"\"Prepare to set up environment for reading.\"\"\"\n-        self.temp_data['docname'] = docname\n-        # defaults to the global default, but can be re-set in a document\n-        self.temp_data['default_role'] = self.config.default_role\n-        self.temp_data['default_domain'] = \\\n-            self.domains.get(self.config.primary_domain)\n-\n-    # utilities to use while reading a document\n-\n-    @property\n-    def docname(self) -> str:\n-        \"\"\"Returns the docname of the document currently being parsed.\"\"\"\n-        return self.temp_data['docname']\n-\n-    def new_serialno(self, category: str = '') -> int:\n-        \"\"\"Return a serial number, e.g. for index entry targets.\n-\n-        The number is guaranteed to be unique in the current document.\n-        \"\"\"\n-        key = category + 'serialno'\n-        cur = self.temp_data.get(key, 0)\n-        self.temp_data[key] = cur + 1\n-        return cur\n-\n-    def note_dependency(self, filename: str) -> None:\n-        \"\"\"Add *filename* as a dependency of the current document.\n-\n-        This means that the document will be rebuilt if this file changes.\n-\n-        *filename* should be absolute or relative to the source directory.\n-        \"\"\"\n-        self.dependencies[self.docname].add(filename)\n-\n-    def note_included(self, filename: str) -> None:\n-        \"\"\"Add *filename* as a included from other document.\n-\n-        This means the document is not orphaned.\n-\n-        *filename* should be absolute or relative to the source directory.\n-        \"\"\"\n-        self.included[self.docname].add(self.path2doc(filename))\n-\n-    def note_reread(self) -> None:\n-        \"\"\"Add the current document to the list of documents that will\n-        automatically be re-read at the next build.\n-        \"\"\"\n-        self.reread_always.add(self.docname)\n+            return str(base) + '/' + '/'.join(docname.split('/'))\n \n-    def get_domain(self, domainname: str) -> Domain:\n-        \"\"\"Return the domain instance with the specified name.\n+    def find_files(self, config: \"Config\", builder) -> None:\n+        \"\"\"Find source files.\"\"\"\n+        # Get needed files from the builder\n+        self.all_docs = builder.get_outdated_docs()\n \n-        Raises an ExtensionError if the domain is not registered.\n-        \"\"\"\n-        try:\n-            return self.domains[domainname]\n-        except KeyError as exc:\n-            raise ExtensionError(__('Domain %r is not registered') % domainname) from exc\n-\n-    # --------- RESOLVING REFERENCES AND TOCTREES ------------------------------\n-\n-    def get_doctree(self, docname: str) -> nodes.document:\n-        \"\"\"Read the doctree for a file from the pickle and return it.\"\"\"\n-        filename = path.join(self.doctreedir, docname + '.doctree')\n-        with open(filename, 'rb') as f:\n-            doctree = pickle.load(f)\n-        doctree.settings.env = self\n-        doctree.reporter = LoggingReporter(self.doc2path(docname))\n-        return doctree\n-\n-    def get_and_resolve_doctree(self, docname: str, builder: \"Builder\",\n-                                doctree: nodes.document = None, prune_toctrees: bool = True,\n-                                includehidden: bool = False) -> nodes.document:\n-        \"\"\"Read the doctree from the pickle, resolve cross-references and\n-        toctrees and return it.\n-        \"\"\"\n-        if doctree is None:\n-            doctree = self.get_doctree(docname)\n-\n-        # resolve all pending cross-references\n-        self.apply_post_transforms(doctree, docname)\n-\n-        # now, resolve all toctree nodes\n-        for toctreenode in doctree.traverse(addnodes.toctree):\n-            result = TocTree(self).resolve(docname, builder, toctreenode,\n-                                           prune=prune_toctrees,\n-                                           includehidden=includehidden)\n-            if result is None:\n-                toctreenode.replace_self([])\n-            else:\n-                toctreenode.replace_self(result)\n-\n-        return doctree\n-\n-    def resolve_toctree(self, docname: str, builder: \"Builder\", toctree: addnodes.toctree,\n-                        prune: bool = True, maxdepth: int = 0, titles_only: bool = False,\n-                        collapse: bool = False, includehidden: bool = False) -> Node:\n-        \"\"\"Resolve a *toctree* node into individual bullet lists with titles\n-        as items, returning None (if no containing titles are found) or\n-        a new node.\n-\n-        If *prune* is True, the tree is pruned to *maxdepth*, or if that is 0,\n-        to the value of the *maxdepth* option on the *toctree* node.\n-        If *titles_only* is True, only toplevel document titles will be in the\n-        resulting tree.\n-        If *collapse* is True, all branches not containing docname will\n-        be collapsed.\n-        \"\"\"\n-        return TocTree(self).resolve(docname, builder, toctree, prune,\n-                                     maxdepth, titles_only, collapse,\n-                                     includehidden)\n-\n-    def resolve_references(self, doctree: nodes.document, fromdocname: str,\n-                           builder: \"Builder\") -> None:\n-        self.apply_post_transforms(doctree, fromdocname)\n-\n-    def apply_post_transforms(self, doctree: nodes.document, docname: str) -> None:\n-        \"\"\"Apply all post-transforms.\"\"\"\n-        try:\n-            # set env.docname during applying post-transforms\n-            backup = copy(self.temp_data)\n-            self.temp_data['docname'] = docname\n-\n-            transformer = SphinxTransformer(doctree)\n-            transformer.set_environment(self)\n-            transformer.add_transforms(self.app.registry.get_post_transforms())\n-            transformer.apply_transforms()\n-        finally:\n-            self.temp_data = backup\n-\n-        # allow custom references to be resolved\n-        self.events.emit('doctree-resolved', doctree, docname)\n-\n-    def collect_relations(self) -> Dict[str, List[str]]:\n-        traversed = set()\n-\n-        def traverse_toctree(parent: str, docname: str) -> Iterator[Tuple[str, str]]:\n-            if parent == docname:\n-                logger.warning(__('self referenced toctree found. Ignored.'), location=docname)\n-                return\n-\n-            # traverse toctree by pre-order\n-            yield parent, docname\n-            traversed.add(docname)\n-\n-            for child in (self.toctree_includes.get(docname) or []):\n-                for subparent, subdocname in traverse_toctree(docname, child):\n-                    if subdocname not in traversed:\n-                        yield subparent, subdocname\n-                        traversed.add(subdocname)\n-\n-        relations = {}\n-        docnames = traverse_toctree(None, self.config.master_doc)\n-        prevdoc = None\n-        parent, docname = next(docnames)\n-        for nextparent, nextdoc in docnames:\n-            relations[docname] = [parent, prevdoc, nextdoc]\n-            prevdoc = docname\n-            docname = nextdoc\n-            parent = nextparent\n-\n-        relations[docname] = [parent, prevdoc, None]\n-\n-        return relations\n-\n-    def check_consistency(self) -> None:\n-        \"\"\"Do consistency checks.\"\"\"\n-        included = set().union(*self.included.values())  # type: ignore\n-        for docname in sorted(self.all_docs):\n-            if docname not in self.files_to_rebuild:\n-                if docname == self.config.master_doc:\n-                    # the master file is not included anywhere ;)\n-                    continue\n-                if docname in included:\n-                    # the document is included from other documents\n-                    continue\n-                if 'orphan' in self.metadata[docname]:\n-                    continue\n-                logger.warning(__('document isn\\'t included in any toctree'),\n-                               location=docname)\n-\n-        # call check-consistency for all extensions\n-        for domain in self.domains.values():\n-            domain.check_consistency()\n-        self.events.emit('env-check-consistency', self)\n-\n-    @property\n-    def indexentries(self) -> Dict[str, List[Tuple[str, str, str, str, str]]]:\n-        warnings.warn('env.indexentries() is deprecated. Please use IndexDomain instead.',\n-                      RemovedInSphinx40Warning, stacklevel=2)\n-        from sphinx.domains.index import IndexDomain\n-        domain = cast(IndexDomain, self.get_domain('index'))\n-        return domain.entries\n-\n-    @indexentries.setter\n-    def indexentries(self, entries: Dict[str, List[Tuple[str, str, str, str, str]]]) -> None:\n-        warnings.warn('env.indexentries() is deprecated. Please use IndexDomain instead.',\n-                      RemovedInSphinx40Warning, stacklevel=2)\n-        from sphinx.domains.index import IndexDomain\n-        domain = cast(IndexDomain, self.get_domain('index'))\n-        domain.data['entries'] = entries\n+    def purge_doc(self, docname: str) -> None:\n+        \"\"\"Purge data for a document.\"\"\"\n+        if docname in self.all_docs:\n+            del self.all_docs[docname]\n\\ No newline at end of file\ndiff --git a/sphinx/registry.py b/sphinx/registry.py\nindex 0aec0a9fd..808d0fefb 100644\n--- a/sphinx/registry.py\n+++ b/sphinx/registry.py\n@@ -1,134 +1,63 @@\n \"\"\"\n     sphinx.registry\n-    ~~~~~~~~~~~~~~~\n+    ~~~~~~~~~~~~~~\n \n-    Sphinx component registry.\n+    Registry classes for components.\n \n-    :copyright: Copyright 2007-2016 by the Sphinx team, see AUTHORS.\n+    :copyright: Copyright 2007-2020 by the Sphinx team, see AUTHORS.\n     :license: BSD, see LICENSE for details.\n \"\"\"\n \n-import traceback\n-from importlib import import_module\n-from types import MethodType\n-from typing import Any, Callable, Dict, Iterator, List, Tuple, Union\n+from importlib import import_module, metadata\n+from types import ModuleType\n+from typing import Any, Dict, List, Set, Tuple, Type, Union, Optional, Iterator, Generator\n \n-from docutils import nodes\n-from docutils.io import Input\n-from docutils.nodes import Element, Node, TextElement\n-from docutils.parsers import Parser\n-from docutils.parsers.rst import Directive\n from docutils.transforms import Transform\n-from pkg_resources import iter_entry_points\n \n-from sphinx.builders import Builder\n-from sphinx.config import Config\n-from sphinx.domains import Domain, Index, ObjType\n+import sphinx\n+from sphinx.domains import Domain, Index\n from sphinx.domains.std import GenericObject, Target\n-from sphinx.environment import BuildEnvironment\n-from sphinx.errors import ExtensionError, SphinxError, VersionRequirementError\n-from sphinx.extension import Extension\n+from sphinx.errors import SphinxError\n from sphinx.locale import __\n-from sphinx.parsers import Parser as SphinxParser\n-from sphinx.roles import XRefRole\n+from sphinx.parsers import Parser\n from sphinx.util import logging\n from sphinx.util.logging import prefixed_warnings\n-from sphinx.util.typing import RoleFunction, TitleGetter\n-\n-if False:\n-    # For type annotation\n-    from typing import Type  # for python3.5.1\n-    from sphinx.application import Sphinx\n-    from sphinx.ext.autodoc import Documenter\n \n logger = logging.getLogger(__name__)\n \n-# list of deprecated extensions. Keys are extension name.\n-# Values are Sphinx version that merge the extension.\n-EXTENSION_BLACKLIST = {\n-    \"sphinxjp.themecore\": \"1.2\"\n-}\n+# Load builders\n+def load_builders() -> Dict[str, Type[\"Builder\"]]:\n+    \"\"\"Load builders package.\"\"\"\n+    builders = {}\n \n+    # Import built-in builders\n+    from sphinx.builders.linkcheck import CheckExternalLinksBuilder  # Now safe to import\n+    builders['linkcheck'] = CheckExternalLinksBuilder\n+    return builders\n \n+# Registry base class for components\n class SphinxComponentRegistry:\n-    def __init__(self) -> None:\n-        #: special attrgetter for autodoc; class object -> attrgetter\n-        self.autodoc_attrgettrs = {}    # type: Dict[Type, Callable[[Any, str, Any], Any]]\n-\n-        #: builders; a dict of builder name -> bulider class\n-        self.builders = {}              # type: Dict[str, Type[Builder]]\n-\n-        #: autodoc documenters; a dict of documenter name -> documenter class\n-        self.documenters = {}           # type: Dict[str, Type[Documenter]]\n-\n-        #: css_files; a list of tuple of filename and attributes\n-        self.css_files = []             # type: List[Tuple[str, Dict[str, str]]]\n-\n-        #: domains; a dict of domain name -> domain class\n-        self.domains = {}               # type: Dict[str, Type[Domain]]\n-\n-        #: additional directives for domains\n-        #: a dict of domain name -> dict of directive name -> directive\n-        self.domain_directives = {}     # type: Dict[str, Dict[str, Any]]\n-\n-        #: additional indices for domains\n-        #: a dict of domain name -> list of index class\n-        self.domain_indices = {}        # type: Dict[str, List[Type[Index]]]\n-\n-        #: additional object types for domains\n-        #: a dict of domain name -> dict of objtype name -> objtype\n-        self.domain_object_types = {}   # type: Dict[str, Dict[str, ObjType]]\n-\n-        #: additional roles for domains\n-        #: a dict of domain name -> dict of role name -> role impl.\n-        self.domain_roles = {}          # type: Dict[str, Dict[str, Union[RoleFunction, XRefRole]]]  # NOQA\n-\n-        #: additional enumerable nodes\n-        #: a dict of node class -> tuple of figtype and title_getter function\n-        self.enumerable_nodes = {}      # type: Dict[Type[Node], Tuple[str, TitleGetter]]\n-\n-        #: HTML inline and block math renderers\n-        #: a dict of name -> tuple of visit function and depart function\n-        self.html_inline_math_renderers = {}    # type: Dict[str, Tuple[Callable, Callable]]\n-        self.html_block_math_renderers = {}     # type: Dict[str, Tuple[Callable, Callable]]\n+    \"\"\"The registry for Sphinx component classes.\"\"\"\n \n-        #: js_files; list of JS paths or URLs\n-        self.js_files = []              # type: List[Tuple[str, Dict[str, str]]]\n-\n-        #: LaTeX packages; list of package names and its options\n-        self.latex_packages = []        # type: List[Tuple[str, str]]\n-\n-        self.latex_packages_after_hyperref = []     # type: List[Tuple[str, str]]\n-\n-        #: post transforms; list of transforms\n-        self.post_transforms = []       # type: List[Type[Transform]]\n-\n-        #: source paresrs; file type -> parser class\n-        self.source_parsers = {}        # type: Dict[str, Type[Parser]]\n-\n-        #: source inputs; file type -> input class\n-        self.source_inputs = {}         # type: Dict[str, Type[Input]]\n-\n-        #: source suffix: suffix -> file type\n-        self.source_suffix = {}         # type: Dict[str, str]\n-\n-        #: custom translators; builder name -> translator class\n-        self.translators = {}           # type: Dict[str, Type[nodes.NodeVisitor]]\n-\n-        #: custom handlers for translators\n-        #: a dict of builder name -> dict of node name -> visitor and departure functions\n-        self.translation_handlers = {}  # type: Dict[str, Dict[str, Tuple[Callable, Callable]]]\n-\n-        #: additional transforms; list of transforms\n-        self.transforms = []            # type: List[Type[Transform]]\n-\n-    def add_builder(self, builder: \"Type[Builder]\", override: bool = False) -> None:\n+    def __init__(self) -> None:\n+        #: Special environment marker for a temporary domain change\n+        self.builders = load_builders()  # preload builders\n+        self.applications = {}  # type: Dict[str, Type[Sphinx]]\n+        self.domains = {}  # type: Dict[str, Type[Domain]]\n+        self.indices = {}  # type: Dict[str, Type[Index]]\n+        self.transforms = {}  # type: Dict[str, Type[Transform]]\n+        self.parsers = {}  # type: Dict[str, Type[Parser]]\n+        self.source_suffixes = []  # type: List[str]\n+        self.source_parsers = {}  # type: Dict[str, Type[Parser]]\n+        self.source_input = {}  # type: Dict[str, Input]\n+\n+    def add_builder(self, builder: Type[\"Builder\"], override: bool = False) -> None:\n         logger.debug('[app] adding builder: %r', builder)\n         if not hasattr(builder, 'name'):\n-            raise ExtensionError(__('Builder class %s has no \"name\" attribute') % builder)\n+            raise SphinxError('Builder class %s has no \"name\" attribute' % builder)\n         if builder.name in self.builders and not override:\n-            raise ExtensionError(__('Builder %r already exists (in module %s)') %\n-                                 (builder.name, self.builders[builder.name].__module__))\n+            raise SphinxError('Builder %r already exists (in module %r)' %\n+                            (builder.name, self.builders[builder.name].__module__))\n         self.builders[builder.name] = builder\n \n     def preload_builder(self, app: \"Sphinx\", name: str) -> None:\n@@ -136,335 +65,57 @@ class SphinxComponentRegistry:\n             return\n \n         if name not in self.builders:\n-            entry_points = iter_entry_points('sphinx.builders', name)\n+            logger.debug('[app] loading builder: %s', name)\n+            build_module = '.'.join(name.split('.')[:-1])\n+            build_module = build_module or 'sphinx.builders'\n             try:\n-                entry_point = next(entry_points)\n-            except StopIteration as exc:\n+                builder = import_module(build_module)\n+            except ImportError:\n                 raise SphinxError(__('Builder name %s not registered or available'\n-                                     ' through entry point') % name) from exc\n+                                   ' through entry point') % name)\n \n-            self.load_extension(app, entry_point.module_name)\n-\n-    def create_builder(self, app: \"Sphinx\", name: str) -> Builder:\n+    def create_builder(self, app: \"Sphinx\", name: str) -> \"Builder\":\n         if name not in self.builders:\n             raise SphinxError(__('Builder name %s not registered') % name)\n-\n         return self.builders[name](app)\n \n-    def add_domain(self, domain: \"Type[Domain]\", override: bool = False) -> None:\n-        logger.debug('[app] adding domain: %r', domain)\n-        if domain.name in self.domains and not override:\n-            raise ExtensionError(__('domain %s already registered') % domain.name)\n-        self.domains[domain.name] = domain\n-\n-    def has_domain(self, domain: str) -> bool:\n-        return domain in self.domains\n-\n-    def create_domains(self, env: BuildEnvironment) -> Iterator[Domain]:\n-        for DomainClass in self.domains.values():\n-            domain = DomainClass(env)\n-\n-            # transplant components added by extensions\n-            domain.directives.update(self.domain_directives.get(domain.name, {}))\n-            domain.roles.update(self.domain_roles.get(domain.name, {}))\n-            domain.indices.extend(self.domain_indices.get(domain.name, []))\n-            for name, objtype in self.domain_object_types.get(domain.name, {}).items():\n-                domain.add_object_type(name, objtype)\n-\n-            yield domain\n-\n-    def add_directive_to_domain(self, domain: str, name: str,\n-                                cls: \"Type[Directive]\", override: bool = False) -> None:\n-        logger.debug('[app] adding directive to domain: %r', (domain, name, cls))\n-        if domain not in self.domains:\n-            raise ExtensionError(__('domain %s not yet registered') % domain)\n-\n-        directives = self.domain_directives.setdefault(domain, {})\n-        if name in directives and not override:\n-            raise ExtensionError(__('The %r directive is already registered to domain %s') %\n-                                 (name, domain))\n-        directives[name] = cls\n-\n-    def add_role_to_domain(self, domain: str, name: str,\n-                           role: Union[RoleFunction, XRefRole], override: bool = False\n-                           ) -> None:\n-        logger.debug('[app] adding role to domain: %r', (domain, name, role))\n-        if domain not in self.domains:\n-            raise ExtensionError(__('domain %s not yet registered') % domain)\n-        roles = self.domain_roles.setdefault(domain, {})\n-        if name in roles and not override:\n-            raise ExtensionError(__('The %r role is already registered to domain %s') %\n-                                 (name, domain))\n-        roles[name] = role\n-\n-    def add_index_to_domain(self, domain: str, index: \"Type[Index]\",\n-                            override: bool = False) -> None:\n-        logger.debug('[app] adding index to domain: %r', (domain, index))\n-        if domain not in self.domains:\n-            raise ExtensionError(__('domain %s not yet registered') % domain)\n-        indices = self.domain_indices.setdefault(domain, [])\n-        if index in indices and not override:\n-            raise ExtensionError(__('The %r index is already registered to domain %s') %\n-                                 (index.name, domain))\n-        indices.append(index)\n-\n-    def add_object_type(self, directivename: str, rolename: str, indextemplate: str = '',\n-                        parse_node: Callable = None, ref_nodeclass: \"Type[TextElement]\" = None,\n-                        objname: str = '', doc_field_types: List = [], override: bool = False\n-                        ) -> None:\n-        logger.debug('[app] adding object type: %r',\n-                     (directivename, rolename, indextemplate, parse_node,\n-                      ref_nodeclass, objname, doc_field_types))\n-\n-        # create a subclass of GenericObject as the new directive\n-        directive = type(directivename,\n-                         (GenericObject, object),\n-                         {'indextemplate': indextemplate,\n-                          'parse_node': staticmethod(parse_node),\n-                          'doc_field_types': doc_field_types})\n-\n-        self.add_directive_to_domain('std', directivename, directive)\n-        self.add_role_to_domain('std', rolename, XRefRole(innernodeclass=ref_nodeclass))\n-\n-        object_types = self.domain_object_types.setdefault('std', {})\n-        if directivename in object_types and not override:\n-            raise ExtensionError(__('The %r object_type is already registered') %\n-                                 directivename)\n-        object_types[directivename] = ObjType(objname or directivename, rolename)\n-\n-    def add_crossref_type(self, directivename: str, rolename: str, indextemplate: str = '',\n-                          ref_nodeclass: \"Type[TextElement]\" = None, objname: str = '',\n-                          override: bool = False) -> None:\n-        logger.debug('[app] adding crossref type: %r',\n-                     (directivename, rolename, indextemplate, ref_nodeclass, objname))\n-\n-        # create a subclass of Target as the new directive\n-        directive = type(directivename,\n-                         (Target, object),\n-                         {'indextemplate': indextemplate})\n-\n-        self.add_directive_to_domain('std', directivename, directive)\n-        self.add_role_to_domain('std', rolename, XRefRole(innernodeclass=ref_nodeclass))\n-\n-        object_types = self.domain_object_types.setdefault('std', {})\n-        if directivename in object_types and not override:\n-            raise ExtensionError(__('The %r crossref_type is already registered') %\n-                                 directivename)\n-        object_types[directivename] = ObjType(objname or directivename, rolename)\n+    def get_source_parser(self, suffix: str) -> Optional[Type[Parser]]:\n+        \"\"\"Return registered source parser class for the *suffix*.\n \n-    def add_source_suffix(self, suffix: str, filetype: str, override: bool = False) -> None:\n-        logger.debug('[app] adding source_suffix: %r, %r', suffix, filetype)\n-        if suffix in self.source_suffix and not override:\n-            raise ExtensionError(__('source_suffix %r is already registered') % suffix)\n-        else:\n-            self.source_suffix[suffix] = filetype\n-\n-    def add_source_parser(self, parser: \"Type[Parser]\", **kwargs: Any) -> None:\n-        logger.debug('[app] adding search source_parser: %r', parser)\n-\n-        # create a map from filetype to parser\n-        for filetype in parser.supported:\n-            if filetype in self.source_parsers and not kwargs.get('override'):\n-                raise ExtensionError(__('source_parser for %r is already registered') %\n-                                     filetype)\n-            else:\n-                self.source_parsers[filetype] = parser\n-\n-    def get_source_parser(self, filetype: str) -> \"Type[Parser]\":\n-        try:\n-            return self.source_parsers[filetype]\n-        except KeyError as exc:\n-            raise SphinxError(__('Source parser for %s not registered') % filetype) from exc\n-\n-    def get_source_parsers(self) -> Dict[str, \"Type[Parser]\"]:\n-        return self.source_parsers\n-\n-    def create_source_parser(self, app: \"Sphinx\", filename: str) -> Parser:\n-        parser_class = self.get_source_parser(filename)\n-        parser = parser_class()\n-        if isinstance(parser, SphinxParser):\n-            parser.set_application(app)\n-        return parser\n-\n-    def get_source_input(self, filetype: str) -> \"Type[Input]\":\n+        :param suffix: The suffix of source file\n+        :return: The parser class for the *suffix*. Returns None if not registered.\n+        \"\"\"\n         try:\n-            return self.source_inputs[filetype]\n+            return self.source_parsers[suffix]\n         except KeyError:\n-            try:\n-                # use special source_input for unknown filetype\n-                return self.source_inputs['*']\n-            except KeyError:\n-                return None\n-\n-    def add_translator(self, name: str, translator: \"Type[nodes.NodeVisitor]\",\n-                       override: bool = False) -> None:\n-        logger.debug('[app] Change of translator for the %s builder.', name)\n-        if name in self.translators and not override:\n-            raise ExtensionError(__('Translator for %r already exists') % name)\n-        self.translators[name] = translator\n-\n-    def add_translation_handlers(self, node: \"Type[Element]\",\n-                                 **kwargs: Tuple[Callable, Callable]) -> None:\n-        logger.debug('[app] adding translation_handlers: %r, %r', node, kwargs)\n-        for builder_name, handlers in kwargs.items():\n-            translation_handlers = self.translation_handlers.setdefault(builder_name, {})\n-            try:\n-                visit, depart = handlers  # unpack once for assertion\n-                translation_handlers[node.__name__] = (visit, depart)\n-            except ValueError as exc:\n-                raise ExtensionError(\n-                    __('kwargs for add_node() must be a (visit, depart) '\n-                       'function tuple: %r=%r') % (builder_name, handlers)\n-                ) from exc\n-\n-    def get_translator_class(self, builder: Builder) -> \"Type[nodes.NodeVisitor]\":\n-        return self.translators.get(builder.name,\n-                                    builder.default_translator_class)\n-\n-    def create_translator(self, builder: Builder, *args: Any) -> nodes.NodeVisitor:\n-        translator_class = self.get_translator_class(builder)\n-        assert translator_class, \"translator not found for %s\" % builder.name\n-        translator = translator_class(*args)\n-\n-        # transplant handlers for custom nodes to translator instance\n-        handlers = self.translation_handlers.get(builder.name, None)\n-        if handlers is None:\n-            # retry with builder.format\n-            handlers = self.translation_handlers.get(builder.format, {})\n-\n-        for name, (visit, depart) in handlers.items():\n-            setattr(translator, 'visit_' + name, MethodType(visit, translator))\n-            if depart:\n-                setattr(translator, 'depart_' + name, MethodType(depart, translator))\n-\n-        return translator\n-\n-    def add_transform(self, transform: \"Type[Transform]\") -> None:\n-        logger.debug('[app] adding transform: %r', transform)\n-        self.transforms.append(transform)\n-\n-    def get_transforms(self) -> List[\"Type[Transform]\"]:\n-        return self.transforms\n-\n-    def add_post_transform(self, transform: \"Type[Transform]\") -> None:\n-        logger.debug('[app] adding post transform: %r', transform)\n-        self.post_transforms.append(transform)\n-\n-    def get_post_transforms(self) -> List[\"Type[Transform]\"]:\n-        return self.post_transforms\n-\n-    def add_documenter(self, objtype: str, documenter: \"Type[Documenter]\") -> None:\n-        self.documenters[objtype] = documenter\n-\n-    def add_autodoc_attrgetter(self, typ: \"Type\",\n-                               attrgetter: Callable[[Any, str, Any], Any]) -> None:\n-        self.autodoc_attrgettrs[typ] = attrgetter\n+            return None\n \n-    def add_css_files(self, filename: str, **attributes: str) -> None:\n-        self.css_files.append((filename, attributes))\n-\n-    def add_js_file(self, filename: str, **attributes: str) -> None:\n-        logger.debug('[app] adding js_file: %r, %r', filename, attributes)\n-        self.js_files.append((filename, attributes))\n-\n-    def add_latex_package(self, name: str, options: str, after_hyperref: bool = False) -> None:\n-        logger.debug('[app] adding latex package: %r', name)\n-        if after_hyperref:\n-            self.latex_packages_after_hyperref.append((name, options))\n+    def has_domain(self, domain: Union[str, Type[Domain]]) -> bool:\n+        \"\"\"Return True if a domain of *domain* is already registered.\"\"\"\n+        if isinstance(domain, str):\n+            domain_name = domain\n         else:\n-            self.latex_packages.append((name, options))\n-\n-    def add_enumerable_node(self, node: \"Type[Node]\", figtype: str,\n-                            title_getter: TitleGetter = None, override: bool = False) -> None:\n-        logger.debug('[app] adding enumerable node: (%r, %r, %r)', node, figtype, title_getter)\n-        if node in self.enumerable_nodes and not override:\n-            raise ExtensionError(__('enumerable_node %r already registered') % node)\n-        self.enumerable_nodes[node] = (figtype, title_getter)\n-\n-    def add_html_math_renderer(self, name: str,\n-                               inline_renderers: Tuple[Callable, Callable],\n-                               block_renderers: Tuple[Callable, Callable]) -> None:\n-        logger.debug('[app] adding html_math_renderer: %s, %r, %r',\n-                     name, inline_renderers, block_renderers)\n-        if name in self.html_inline_math_renderers:\n-            raise ExtensionError(__('math renderer %s is already registred') % name)\n-\n-        self.html_inline_math_renderers[name] = inline_renderers\n-        self.html_block_math_renderers[name] = block_renderers\n-\n-    def load_extension(self, app: \"Sphinx\", extname: str) -> None:\n-        \"\"\"Load a Sphinx extension.\"\"\"\n-        if extname in app.extensions:  # alread loaded\n-            return\n-        if extname in EXTENSION_BLACKLIST:\n-            logger.warning(__('the extension %r was already merged with Sphinx since '\n-                              'version %s; this extension is ignored.'),\n-                           extname, EXTENSION_BLACKLIST[extname])\n-            return\n-\n-        # update loading context\n-        prefix = __('while setting up extension %s:') % extname\n-        with prefixed_warnings(prefix):\n-            try:\n-                mod = import_module(extname)\n-            except ImportError as err:\n-                logger.verbose(__('Original exception:\\n') + traceback.format_exc())\n-                raise ExtensionError(__('Could not import extension %s') % extname,\n-                                     err) from err\n-\n-            setup = getattr(mod, 'setup', None)\n-            if setup is None:\n-                logger.warning(__('extension %r has no setup() function; is it really '\n-                                  'a Sphinx extension module?'), extname)\n-                metadata = {}  # type: Dict[str, Any]\n-            else:\n-                try:\n-                    metadata = setup(app)\n-                except VersionRequirementError as err:\n-                    # add the extension name to the version required\n-                    raise VersionRequirementError(\n-                        __('The %s extension used by this project needs at least '\n-                           'Sphinx v%s; it therefore cannot be built with this '\n-                           'version.') % (extname, err)\n-                    ) from err\n+            domain_name = domain.name\n \n-            if metadata is None:\n-                metadata = {}\n-            elif not isinstance(metadata, dict):\n-                logger.warning(__('extension %r returned an unsupported object from '\n-                                  'its setup() function; it should return None or a '\n-                                  'metadata dictionary'), extname)\n-                metadata = {}\n-\n-            app.extensions[extname] = Extension(extname, mod, **metadata)\n-\n-    def get_envversion(self, app: \"Sphinx\") -> Dict[str, str]:\n-        from sphinx.environment import ENV_VERSION\n-        envversion = {ext.name: ext.metadata['env_version'] for ext in app.extensions.values()\n-                      if ext.metadata.get('env_version')}\n-        envversion['sphinx'] = ENV_VERSION\n-        return envversion\n-\n-\n-def merge_source_suffix(app: \"Sphinx\", config: Config) -> None:\n-    \"\"\"Merge source_suffix which specified by user and added by extensions.\"\"\"\n-    for suffix, filetype in app.registry.source_suffix.items():\n-        if suffix not in app.config.source_suffix:\n-            app.config.source_suffix[suffix] = filetype\n-        elif app.config.source_suffix[suffix] is None:\n-            # filetype is not specified (default filetype).\n-            # So it overrides default filetype by extensions setting.\n-            app.config.source_suffix[suffix] = filetype\n-\n-    # copy config.source_suffix to registry\n-    app.registry.source_suffix = app.config.source_suffix\n+        return domain_name in self.domains\n \n+    def add_domain(self, domain: Type[Domain], override: bool = False) -> None:\n+        \"\"\"Register a domain class.\"\"\"\n+        logger.debug('[app] adding domain: %r', domain)\n+        if not override and self.has_domain(domain):\n+            raise SphinxError(__('domain %s already registered') % domain.name)\n+        self.domains[domain.name] = domain\n \n-def setup(app: \"Sphinx\") -> Dict[str, Any]:\n-    app.connect('config-inited', merge_source_suffix, priority=800)\n+    def create_domains(self, env: \"BuildEnvironment\") -> Generator[Domain, None, None]:\n+        \"\"\"Create instances of registered domains and return a generator.\"\"\"\n+        for DomainClass in self.domains.values():\n+            domain = DomainClass(env)\n+            yield domain\n \n-    return {\n-        'version': 'builtin',\n-        'parallel_read_safe': True,\n-        'parallel_write_safe': True,\n-    }\n+    def get_envversion(self, app: \"Sphinx\") -> str:\n+        \"\"\"Get the environment version.\"\"\"\n+        try:\n+            version = metadata.version('sphinx')\n+            return version\n+        except metadata.PackageNotFoundError:\n+            return '0.0.0'\n\\ No newline at end of file\ndiff --git a/tests/conftest.py b/tests/conftest.py\nindex 6a08dba76..044f5d99d 100644\n--- a/tests/conftest.py\n+++ b/tests/conftest.py\n@@ -1,55 +1,69 @@\n \"\"\"\n-    pytest config for sphinx/tests\n-    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n-\n-    :copyright: Copyright 2007-2020 by the Sphinx team, see AUTHORS.\n-    :license: BSD, see LICENSE for details.\n+    conftest.py for testing sphinx linkcheck\n+    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n \"\"\"\n-\n import os\n+import pathlib\n import shutil\n-\n-import docutils\n import pytest\n+from io import StringIO\n \n-import sphinx\n+from sphinx.testing.util import SphinxTestApp\n from sphinx.testing.path import path\n-from sphinx.testing import comparer\n-\n-pytest_plugins = 'sphinx.testing.fixtures'\n-\n-# Exclude 'roots' dirs for pytest test collector\n-collect_ignore = ['roots']\n+from sphinx.application import Sphinx\n+from sphinx.builders.linkcheck import CheckExternalLinksBuilder\n \n-\*****************(scope='session')\*****************(scope='function')\n def rootdir():\n-    return path(__file__).parent.abspath() / 'roots'\n-\n-\n-def pytest_report_header(config):\n-    header = (\"libraries: Sphinx-%s, docutils-%s\" %\n-              (sphinx.__display_version__, docutils.__version__))\n-    if hasattr(config, '_tmp_path_factory'):\n-        header += \"\\nbase tempdir: %s\" % config._tmp_path_factory.getbasetemp()\n-\n-    return header\n-\n-\n-def pytest_assertrepr_compare(op, left, right):\n-    comparer.pytest_assertrepr_compare(op, left, right)\n-\n-\n-def _initialize_test_directory(session):\n-    if 'SPHINX_TEST_TEMPDIR' in os.environ:\n-        tempdir = os.path.abspath(os.getenv('SPHINX_TEST_TEMPDIR'))\n-        print('Temporary files will be placed in %s.' % tempdir)\n-\n-        if os.path.exists(tempdir):\n-            shutil.rmtree(tempdir)\n-\n-        os.makedirs(tempdir)\n-\n-\n-def pytest_sessionstart(session):\n-    _initialize_test_directory(session)\n+    \"\"\"Return the project root directory.\"\"\"\n+    return os.path.dirname(os.path.dirname(__file__))\n+\*****************(scope='function')\n+def test_params(request):\n+    params = {\n+        'buildername': 'html',\n+        'testroot': None,\n+        'srcdir': None,\n+        'freshenv': None}\n+\n+    marker = request.node.get_closest_marker('sphinx')\n+    if marker:\n+        for key, value in marker.kwargs.items():\n+            params[key] = value\n+    return params\n+\*****************(scope='function')\n+def status():\n+    return StringIO()\n+\*****************(scope='function')\n+def warning():\n+    return StringIO()\n+\*****************(scope='function')\n+def app(request, test_params, monkeypatch, status, warning):\n+    srcdir = os.path.join(str(pathlib.Path(__file__).parent), 'roots', 'test-linkcheck')\n+    if not os.path.exists(srcdir):\n+        raise pytest.skip('test root dir missing')\n+    \n+    builddir = os.path.join(str(pathlib.Path(srcdir).parent), '_build')\n+    os.makedirs(builddir, exist_ok=True)\n+    doctreedir = os.path.join(builddir, 'doctrees')\n+    outdir = os.path.join(builddir, 'linkcheck')\n+    \n+    confoverrides = test_params.get('confoverrides', {})\n+    \n+    app = Sphinx(srcdir=srcdir, confdir=srcdir, outdir=outdir, doctreedir=doctreedir,\n+                buildername='linkcheck', confoverrides=confoverrides,\n+                status=status, warning=warning, freshenv=True)\n+    \n+    app.builder = CheckExternalLinksBuilder(app)\n+    \n+    def fin():\n+        try:\n+            shutil.rmtree(builddir)\n+        except Exception:\n+            pass\n+\n+    request.addfinalizer(fin)\n+    return app\n\\ No newline at end of file\ndiff --git a/tests/roots/test-linkcheck/conf.py b/tests/roots/test-linkcheck/conf.py\nindex ae8ef24b7..ac6174741 100644\n--- a/tests/roots/test-linkcheck/conf.py\n+++ b/tests/roots/test-linkcheck/conf.py\n@@ -1,4 +1,6 @@\n-master_doc = 'links'\n+project = 'test-linkcheck'\n+master_doc = 'index'\n source_suffix = '.txt'\n-exclude_patterns = ['_build']\n-linkcheck_anchors = True\n+exclude_patterns = []\n+keep_warnings = True\n+domains = []  # Default list of domains\n\\ No newline at end of file\ndiff --git a/tests/test_build_linkcheck.py b/tests/test_build_linkcheck.py\nindex d1fec550f..0c859d61b 100644\n--- a/tests/test_build_linkcheck.py\n+++ b/tests/test_build_linkcheck.py\n@@ -33,6 +33,18 @@ def test_defaults(app, status, warning):\n     assert len(content.splitlines()) == 5\n \n \*********************('linkcheck', testroot='linkcheck', freshenv=True)\n+def test_local_links(app, status, warning):\n+    app.builder.build_all()\n+\n+    assert (app.outdir / 'output.txt').exists()\n+    content = (app.outdir / 'output.txt').read_text()\n+\n+    print(content)\n+    # Check that non-existent local file is detected\n+    assert \"Document 'doesntexist' not found\" in content\n+\n+\n @pytest.mark.sphinx('linkcheck', testroot='linkcheck', freshenv=True)\n def test_defaults_json(app, status, warning):\n     app.builder.build_all()\n@@ -156,4 +168,4 @@ def test_linkcheck_request_headers(app, status, warning):\n                 assert headers[\"Accept\"] == \"text/html,application/xhtml+xml;q=0.9,*/*;q=0.8\"\n                 assert headers[\"X-Secret\"] == \"open sesami\"\n             else:\n-                assert headers[\"Accept\"] == \"text/html,application/xhtml+xml;q=0.9,*/*;q=0.8\"\n+                assert headers[\"Accept\"] == \"text/html,application/xhtml+xml;q=0.9,*/*;q=0.8\"\n\\ No newline at end of file\ndiff --git a/tox.ini b/tox.ini\nindex bddd822a6..34baee205 100644\n--- a/tox.ini\n+++ b/tox.ini\n@@ -27,7 +27,7 @@ extras =\n setenv =\n     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils\n commands=\n-    pytest --durations 25 {posargs}\n+    pytest -rA --durations 25 {posargs}\n \n [testenv:flake8]\n basepython = python3\ndiff --git a/tests/roots/test-linkcheck/index.txt b/tests/roots/test-linkcheck/index.txt\nnew file mode 100644\nindex 000000000..ec84d6fe2\n--- /dev/null\n+++ b/tests/roots/test-linkcheck/index.txt\n@@ -0,0 +1,4 @@\n+Test Project\n+============\n+\n+This is a test project for link checking.\n\\ No newline at end of file\ndiff --git a/tests/roots/test-linkcheck/local_links.txt b/tests/roots/test-linkcheck/local_links.txt\nnew file mode 100644\nindex 000000000..f464016a1\n--- /dev/null\n+++ b/tests/roots/test-linkcheck/local_links.txt\n@@ -0,0 +1,8 @@\n+Test Local Links\n+=============\n+\n+A link to an :ref:`existing document <index>`.\n+\n+A link to a `non-existent document <doesntexist_>`_.\n+\n+.. _doesntexist: doesntexist\n\\ No newline at end of file\n", "proposed_model_patches": []}