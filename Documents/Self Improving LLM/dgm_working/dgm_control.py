#!/usr/bin/env python3
"""
Darwin Gödel Machine Control System
Simple start/stop/monitor system with progress persistence.
"""

import os
import sys
import time
import json
import signal
import subprocess
import threading
from pathlib import Path
from datetime import datetime

class DGMController:
    def __init__(self):
        self.dgm_process = None
        self.monitor_thread = None
        self.running = False
        self.state_file = "dgm_state.json"
        self.log_file = "dgm_control.log"
        
    def load_state(self):
        """Load previous DGM state."""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️  Could not load state: {e}")
        return {
            "last_run": None,
            "total_generations": 0,
            "total_patches": 0,
            "best_score": 0.0,
            "start_time": None
        }
    
    def save_state(self, state):
        """Save current DGM state."""
        try:
            with open(self.state_file, 'w') as f:
                json.dump(state, f, indent=2)
        except Exception as e:
            print(f"⚠️  Could not save state: {e}")
    
    def get_current_stats(self):
        """Get current DGM statistics."""
        try:
            # Run quick stats and capture output
            result = subprocess.run(['python', 'quick_stats.py'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return result.stdout
            else:
                return "Stats unavailable"
        except Exception:
            return "Stats unavailable"
    
    def is_dgm_running(self):
        """Check if DGM process is running."""
        try:
            result = subprocess.run(['pgrep', '-f', 'DGM_outer.py'], 
                                  capture_output=True, text=True)
            return len(result.stdout.strip()) > 0
        except:
            return False
    
    def start_dgm(self, max_generations=50, selfimprove_size=2):
        """Start the DGM process."""
        if self.is_dgm_running():
            print("🟢 DGM is already running!")
            return True
        
        print("🚀 Starting Darwin Gödel Machine...")
        
        # Load previous state
        state = self.load_state()
        
        # Determine continue_from parameter
        continue_from = None
        if state.get("last_run"):
            continue_from = state["last_run"]
            print(f"📂 Resuming from previous run: {continue_from}")
        
        # Build command
        cmd = [
            'python', 'DGM_outer.py',
            '--max_generation', str(max_generations),
            '--selfimprove_size', str(selfimprove_size)
        ]
        
        if continue_from:
            cmd.extend(['--continue_from', continue_from])
        
        # Start process
        try:
            # Activate virtual environment and run
            env = os.environ.copy()
            env['PATH'] = f"{os.path.join(os.getcwd(), 'venv', 'bin')}:{env['PATH']}"
            
            self.dgm_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                env=env,
                bufsize=1,
                universal_newlines=True
            )
            
            # Update state
            state["start_time"] = datetime.now().isoformat()
            self.save_state(state)
            
            print("✅ DGM started successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start DGM: {e}")
            return False
    
    def stop_dgm(self):
        """Stop the DGM process."""
        print("🛑 Stopping Darwin Gödel Machine...")
        
        # Try to stop gracefully first
        try:
            result = subprocess.run(['pkill', '-f', 'DGM_outer.py'], 
                                  capture_output=True, text=True)
            time.sleep(2)
            
            if not self.is_dgm_running():
                print("✅ DGM stopped successfully!")
                return True
            else:
                # Force kill if needed
                subprocess.run(['pkill', '-9', '-f', 'DGM_outer.py'])
                time.sleep(1)
                print("✅ DGM force stopped!")
                return True
                
        except Exception as e:
            print(f"⚠️  Error stopping DGM: {e}")
            return False
    
    def monitor_loop(self):
        """Monitor DGM progress in a loop."""
        print("📊 Starting live monitoring... (Press Ctrl+C to stop)")
        
        try:
            while self.running:
                # Clear screen
                os.system('clear' if os.name == 'posix' else 'cls')
                
                # Show header
                print("🧬 DARWIN GÖDEL MACHINE - LIVE MONITOR")
                print("=" * 60)
                print(f"🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # Show DGM status
                if self.is_dgm_running():
                    print("🟢 Status: RUNNING")
                    
                    # Show stats
                    stats = self.get_current_stats()
                    print(stats)
                else:
                    print("🔴 Status: STOPPED")
                    print("Run 'python dgm_control.py start' to begin evolution")
                
                print("\n" + "=" * 60)
                print("💡 Commands: 'python dgm_control.py [start|stop|status|monitor]'")
                print("⏱️  Next update in 30 seconds... (Ctrl+C to stop monitoring)")
                
                # Wait for next update
                time.sleep(30)
                
        except KeyboardInterrupt:
            print("\n\n🛑 Monitoring stopped by user")
            self.running = False
    
    def show_status(self):
        """Show current DGM status."""
        print("🧬 DARWIN GÖDEL MACHINE STATUS")
        print("=" * 40)
        
        if self.is_dgm_running():
            print("🟢 Status: RUNNING")
            stats = self.get_current_stats()
            print(stats)
        else:
            print("🔴 Status: STOPPED")
            
            # Show last state
            state = self.load_state()
            if state.get("last_run"):
                print(f"📂 Last run: {state['last_run']}")
                print(f"🧬 Total generations: {state.get('total_generations', 0)}")
                print(f"🔧 Total patches: {state.get('total_patches', 0)}")
                print(f"📈 Best score: {state.get('best_score', 0.0):.3f}")
            else:
                print("📂 No previous runs found")

def main():
    """Main function."""
    controller = DGMController()
    
    if len(sys.argv) < 2:
        print("🧬 Darwin Gödel Machine Control System")
        print("Usage: python dgm_control.py [start|stop|status|monitor]")
        print("")
        print("Commands:")
        print("  start   - Start DGM evolution (resumes from last run)")
        print("  stop    - Stop DGM evolution")
        print("  status  - Show current status")
        print("  monitor - Live monitoring with auto-refresh")
        return
    
    command = sys.argv[1].lower()
    
    if command == "start":
        controller.start_dgm()
    elif command == "stop":
        controller.stop_dgm()
    elif command == "status":
        controller.show_status()
    elif command == "monitor":
        controller.running = True
        try:
            controller.monitor_loop()
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped")
    else:
        print(f"❌ Unknown command: {command}")
        print("Valid commands: start, stop, status, monitor")

if __name__ == "__main__":
    main()
