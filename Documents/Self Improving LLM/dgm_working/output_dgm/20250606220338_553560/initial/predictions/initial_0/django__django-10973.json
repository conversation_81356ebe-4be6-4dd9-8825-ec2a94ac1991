{"instance_id": "django__django-10973", "model_name_or_path": "nerf_editwholefiles_0", "model_patch": "diff --git a/django/db/backends/postgresql/client.py b/django/db/backends/postgresql/client.py\nindex 7fca6eff30..da8855e5ff 100644\n--- a/django/db/backends/postgresql/client.py\n+++ b/django/db/backends/postgresql/client.py\n@@ -2,17 +2,9 @@ import os\n import signal\n import subprocess\n \n-from django.core.files.temp import NamedTemporaryFile\n from django.db.backends.base.client import BaseDatabaseClient\n \n \n-def _escape_pgpass(txt):\n-    \"\"\"\n-    Escape a fragment of a PostgreSQL .pgpass file.\n-    \"\"\"\n-    return txt.replace('\\\\', '\\\\\\\\').replace(':', '\\\\:')\n-\n-\n class DatabaseClient(BaseDatabaseClient):\n     executable_name = 'psql'\n \n@@ -34,38 +26,22 @@ class DatabaseClient(BaseDatabaseClient):\n             args += ['-p', str(port)]\n         args += [dbname]\n \n-        temp_pgpass = None\n+        # Set up environment with PGPASSWORD\n+        env = os.environ.copy()\n+        if passwd:\n+            env['PGPASSWORD'] = passwd\n+\n         sigint_handler = signal.getsignal(signal.SIGINT)\n         try:\n-            if passwd:\n-                # Create temporary .pgpass file.\n-                temp_pgpass = NamedTemporaryFile(mode='w+')\n-                try:\n-                    print(\n-                        _escape_pgpass(host) or '*',\n-                        str(port) or '*',\n-                        _escape_pgpass(dbname) or '*',\n-                        _escape_pgpass(user) or '*',\n-                        _escape_pgpass(passwd),\n-                        file=temp_pgpass,\n-                        sep=':',\n-                        flush=True,\n-                    )\n-                    os.environ['PGPASSFILE'] = temp_pgpass.name\n-                except UnicodeEncodeError:\n-                    # If the current locale can't encode the data, let the\n-                    # user input the password manually.\n-                    pass\n             # Allow SIGINT to pass to psql to abort queries.\n             signal.signal(signal.SIGINT, signal.SIG_IGN)\n-            subprocess.check_call(args)\n+            subprocess.run(args, env=env, check=True)\n         finally:\n             # Restore the original SIGINT handler.\n             signal.signal(signal.SIGINT, sigint_handler)\n-            if temp_pgpass:\n-                temp_pgpass.close()\n-                if 'PGPASSFILE' in os.environ:  # unit tests need cleanup\n-                    del os.environ['PGPASSFILE']\n+            # Clean up password from environment\n+            if 'PGPASSWORD' in env:\n+                del env['PGPASSWORD']\n \n     def runshell(self):\n-        DatabaseClient.runshell_db(self.connection.get_connection_params())\n+        DatabaseClient.runshell_db(self.connection.get_connection_params())\n\\ No newline at end of file\ndiff --git a/tests/dbshell/test_postgresql.py b/tests/dbshell/test_postgresql.py\nindex 0d4f28554d..8cc564453d 100644\n--- a/tests/dbshell/test_postgresql.py\n+++ b/tests/dbshell/test_postgresql.py\n@@ -11,23 +11,22 @@ class PostgreSqlDbshellCommandTestCase(SimpleTestCase):\n     def _run_it(self, dbinfo):\n         \"\"\"\n         That function invokes the runshell command, while mocking\n-        subprocess.call. It returns a 2-tuple with:\n+        subprocess.run. It returns a 2-tuple with:\n         - The command line list\n-        - The content of the file pointed by environment PGPASSFILE, or None.\n+        - The password from PGPASSWORD environment variable, or None.\n         \"\"\"\n-        def _mock_subprocess_call(*args):\n-            self.subprocess_args = list(*args)\n-            if 'PGPASSFILE' in os.environ:\n-                with open(os.environ['PGPASSFILE']) as f:\n-                    self.pgpass = f.read().strip()  # ignore line endings\n-            else:\n-                self.pgpass = None\n-            return 0\n+        def _mock_subprocess_run(*args, **kwargs):\n+            self.subprocess_args = list(args[0])\n+            self.subprocess_env = kwargs.get('env', {})\n+            self.pgpassword = self.subprocess_env.get('PGPASSWORD')\n+            return mock.Mock(returncode=0)\n+\n         self.subprocess_args = None\n-        self.pgpass = None\n-        with mock.patch('subprocess.call', new=_mock_subprocess_call):\n+        self.subprocess_env = None\n+        self.pgpassword = None\n+        with mock.patch('subprocess.run', new=_mock_subprocess_run):\n             DatabaseClient.runshell_db(dbinfo)\n-        return self.subprocess_args, self.pgpass\n+        return self.subprocess_args, self.pgpassword\n \n     def test_basic(self):\n         self.assertEqual(\n@@ -39,7 +38,7 @@ class PostgreSqlDbshellCommandTestCase(SimpleTestCase):\n                 'port': '444',\n             }), (\n                 ['psql', '-U', 'someuser', '-h', 'somehost', '-p', '444', 'dbname'],\n-                'somehost:444:dbname:someuser:somepassword',\n+                'somepassword',\n             )\n         )\n \n@@ -66,7 +65,7 @@ class PostgreSqlDbshellCommandTestCase(SimpleTestCase):\n                 'port': '444',\n             }), (\n                 ['psql', '-U', 'some:user', '-h', '::1', '-p', '444', 'dbname'],\n-                '\\\\:\\\\:1:444:dbname:some\\\\:user:some\\\\:password',\n+                'some:password',\n             )\n         )\n \n@@ -80,14 +79,13 @@ class PostgreSqlDbshellCommandTestCase(SimpleTestCase):\n                 'port': '444',\n             }), (\n                 ['psql', '-U', 'some\\\\user', '-h', 'somehost', '-p', '444', 'dbname'],\n-                'somehost:444:dbname:some\\\\\\\\user:some\\\\\\\\password',\n+                'some\\\\password',\n             )\n         )\n \n     def test_accent(self):\n         username = 'rôle'\n         password = 'sésame'\n-        pgpass_string = 'somehost:444:dbname:%s:%s' % (username, password)\n         self.assertEqual(\n             self._run_it({\n                 'database': 'dbname',\n@@ -97,20 +95,20 @@ class PostgreSqlDbshellCommandTestCase(SimpleTestCase):\n                 'port': '444',\n             }), (\n                 ['psql', '-U', username, '-h', 'somehost', '-p', '444', 'dbname'],\n-                pgpass_string,\n+                password,\n             )\n         )\n \n     def test_sigint_handler(self):\n-        \"\"\"SIGINT is ignored in Python and passed to psql to abort quries.\"\"\"\n-        def _mock_subprocess_call(*args):\n+        \"\"\"SIGINT is ignored in Python and passed to psql to abort queries.\"\"\"\n+        def _mock_subprocess_run(*args, **kwargs):\n             handler = signal.getsignal(signal.SIGINT)\n             self.assertEqual(handler, signal.SIG_IGN)\n \n         sigint_handler = signal.getsignal(signal.SIGINT)\n         # The default handler isn't SIG_IGN.\n         self.assertNotEqual(sigint_handler, signal.SIG_IGN)\n-        with mock.patch('subprocess.check_call', new=_mock_subprocess_call):\n+        with mock.patch('subprocess.run', new=_mock_subprocess_run):\n             DatabaseClient.runshell_db({})\n         # dbshell restores the original handler.\n-        self.assertEqual(sigint_handler, signal.getsignal(signal.SIGINT))\n+        self.assertEqual(sigint_handler, signal.getsignal(signal.SIGINT))\n\\ No newline at end of file\n", "proposed_model_patches": []}