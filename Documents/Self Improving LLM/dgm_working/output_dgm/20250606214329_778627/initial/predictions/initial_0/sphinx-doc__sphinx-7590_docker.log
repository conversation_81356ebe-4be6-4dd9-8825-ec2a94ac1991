2025-03-15 00:35:03,893 - ThreadPoolExecutor-4_3 - INFO - No existing container with name sweb.eval.sphinx-doc__sphinx-7590.20250315_003503_881393 found.
2025-03-15 00:35:03,894 - ThreadPoolExecutor-4_3 - INFO - Environment image sweb.env.x86_64.c6d251a05e0af7688b64fd:latest found for sphinx-doc__sphinx-7590
Building instance image sweb.eval.x86_64.sphinx-doc__sphinx-7590:latest for sphinx-doc__sphinx-7590
2025-03-15 00:35:03,897 - ThreadPoolExecutor-4_3 - INFO - Image sweb.eval.x86_64.sphinx-doc__sphinx-7590:latest already exists, skipping build.
2025-03-15 00:35:03,898 - ThreadPoolExecutor-4_3 - INFO - Creating container for sphinx-doc__sphinx-7590...
2025-03-15 00:35:03,934 - Thread<PERSON>oolExecutor-4_3 - INFO - Container for sphinx-doc__sphinx-7590 created: d15c507553a4998b82d515c5d790881539f50be34dcd81f87da34ca70ca5efc7
2025-03-15 00:35:04,120 - ThreadPoolExecutor-4_3 - INFO - Copying coding_agent.py to container at /dgm/coding_agent.py
2025-03-15 00:35:04,122 - ThreadPoolExecutor-4_3 - INFO - Successfully copied coding_agent.py to container
2025-03-15 00:35:04,166 - ThreadPoolExecutor-4_3 - INFO - Copying requirements.txt to container at /dgm/requirements.txt
2025-03-15 00:35:04,168 - ThreadPoolExecutor-4_3 - INFO - Successfully copied requirements.txt to container
2025-03-15 00:35:04,217 - ThreadPoolExecutor-4_3 - INFO - Copying pytest.ini to container at /dgm/pytest.ini
2025-03-15 00:35:04,219 - ThreadPoolExecutor-4_3 - INFO - Successfully copied pytest.ini to container
2025-03-15 00:35:04,282 - ThreadPoolExecutor-4_3 - INFO - Copying tools to container at /dgm/tools
2025-03-15 00:35:04,284 - ThreadPoolExecutor-4_3 - INFO - Successfully copied tools to container
2025-03-15 00:35:04,363 - ThreadPoolExecutor-4_3 - INFO - Copying utils to container at /dgm/utils
2025-03-15 00:35:04,366 - ThreadPoolExecutor-4_3 - INFO - Successfully copied utils to container
2025-03-15 00:35:04,414 - ThreadPoolExecutor-4_3 - INFO - Copying tests to container at /dgm/tests
2025-03-15 00:35:04,416 - ThreadPoolExecutor-4_3 - INFO - Successfully copied tests to container
2025-03-15 00:35:04,475 - ThreadPoolExecutor-4_3 - INFO - Copying prompts to container at /dgm/prompts
2025-03-15 00:35:04,478 - ThreadPoolExecutor-4_3 - INFO - Successfully copied prompts to container
2025-03-15 00:35:04,533 - ThreadPoolExecutor-4_3 - INFO - Copying llm.py to container at /dgm/llm.py
2025-03-15 00:35:04,534 - ThreadPoolExecutor-4_3 - INFO - Successfully copied llm.py to container
2025-03-15 00:35:04,578 - ThreadPoolExecutor-4_3 - INFO - Copying llm_withtools.py to container at /dgm/llm_withtools.py
2025-03-15 00:35:04,580 - ThreadPoolExecutor-4_3 - INFO - Successfully copied llm_withtools.py to container
2025-03-15 00:35:04,582 - ThreadPoolExecutor-4_3 - INFO - Setting up environment
2025-03-15 00:35:04,637 - ThreadPoolExecutor-4_3 - INFO - Copying swe_bench/predictions/nerf_editwholefiles_med_0/sphinx-doc__sphinx-7590_eval.sh to container at /eval.sh
2025-03-15 00:35:04,639 - ThreadPoolExecutor-4_3 - INFO - Successfully copied swe_bench/predictions/nerf_editwholefiles_med_0/sphinx-doc__sphinx-7590_eval.sh to container
2025-03-15 00:35:11,613 - ThreadPoolExecutor-4_3 - INFO - Container output: + source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=2
++ CONDA_SHLVL=2
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_1=/opt/miniconda3
++ CONDA_PREFIX_1=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ cd /testbed
+ git config --global --add safe.directory /testbed
+ cd /testbed
+ git status
On branch master
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   setup.py
	modified:   tox.ini

no changes added to commit (use "git add" and/or "git commit -a")
+ git show
commit 2e506c5ab457cba743bb47eb5b8c8eb9dd51d23d
Merge: 017af4ce2 25fc47e6b
Author: Takeshi KOMIYA <<EMAIL>>
Date:   Sat May 2 01:46:02 2020 +0900

    Merge pull request #7571 from tk0miya/7559_misdetects_async
    
    Fix #7559: autodoc: misdetects a sync function is async

+ git diff 2e506c5ab457cba743bb47eb5b8c8eb9dd51d23d
diff --git a/setup.py b/setup.py
index a427d5493..978e3d8e0 100644
--- a/setup.py
+++ b/setup.py
@@ -15,22 +15,22 @@ if sys.version_info < (3, 5):
     sys.exit(1)
 
 install_requires = [
-    'sphinxcontrib-applehelp',
-    'sphinxcontrib-devhelp',
+    'sphinxcontrib-applehelp<=1.0.7',
+    'sphinxcontrib-devhelp<=1.0.5',
     'sphinxcontrib-jsmath',
-    'sphinxcontrib-htmlhelp',
-    'sphinxcontrib-serializinghtml',
-    'sphinxcontrib-qthelp',
-    'Jinja2>=2.3',
+    'sphinxcontrib-htmlhelp<=2.0.4',
+    'sphinxcontrib-serializinghtml<=1.1.9',
+    'sphinxcontrib-qthelp<=1.0.6',
+    'Jinja2<3.0',
     'Pygments>=2.0',
     'docutils>=0.12',
     'snowballstemmer>=1.1',
     'babel>=1.3',
-    'alabaster>=0.7,<0.8',
+    'alabaster>=0.7,<0.7.12',
     'imagesize',
     'requests>=2.5.0',
     'setuptools',
-    'packaging',
+    'packaging', 'markupsafe<=2.0.1',
 ]
 
 extras_require = {
diff --git a/tox.ini b/tox.ini
index d9f040544..bf39854b6 100644
--- a/tox.ini
+++ b/tox.ini
@@ -28,7 +28,7 @@ extras =
 setenv =
     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils
 commands=
-    pytest --durations 25 {posargs}
+    pytest -rA --durations 25 {posargs}
 
 [testenv:flake8]
 basepython = python3
+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z x ']'
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1='(testbed) '
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=3
+++ CONDA_SHLVL=3
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=4
++ CONDA_SHLVL=4
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_3=/opt/miniconda3
++ CONDA_PREFIX_3=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ python -m pip install -e '.[test]'
Obtaining file:///testbed
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: sphinxcontrib-applehelp<=1.0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.0.4)
Requirement already satisfied: sphinxcontrib-devhelp<=1.0.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.0.2)
Requirement already satisfied: sphinxcontrib-jsmath in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.0.1)
Requirement already satisfied: sphinxcontrib-htmlhelp<=2.0.4 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (2.0.1)
Requirement already satisfied: sphinxcontrib-serializinghtml<=1.1.9 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.1.5)
Requirement already satisfied: sphinxcontrib-qthelp<=1.0.6 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.0.3)
Requirement already satisfied: Jinja2<3.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (2.11.3)
Requirement already satisfied: Pygments>=2.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (2.19.1)
Requirement already satisfied: docutils>=0.12 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (0.21.2)
Requirement already satisfied: snowballstemmer>=1.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (2.2.0)
Requirement already satisfied: babel>=1.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (2.17.0)
Requirement already satisfied: alabaster<0.7.12,>=0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (0.7.11)
Requirement already satisfied: imagesize in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.4.1)
Requirement already satisfied: requests>=2.5.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (2.32.3)
Requirement already satisfied: setuptools in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (75.8.0)
Requirement already satisfied: packaging in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (24.2)
Requirement already satisfied: markupsafe<=2.0.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (2.0.1)
Requirement already satisfied: pytest in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (8.3.4)
Requirement already satisfied: pytest-cov in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (6.0.0)
Requirement already satisfied: html5lib in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.1)
Requirement already satisfied: typed_ast in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.5.5)
Requirement already satisfied: cython in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (3.0.11)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.1.0.dev20250315) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.1.0.dev20250315) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.1.0.dev20250315) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.1.0.dev20250315) (2025.1.31)
Requirement already satisfied: six>=1.9 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.1.0.dev20250315) (1.17.0)
Requirement already satisfied: webencodings in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.1.0.dev20250315) (0.5.1)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.1.0.dev20250315) (1.2.2)
Requirement already satisfied: iniconfig in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.1.0.dev20250315) (2.0.0)
Requirement already satisfied: pluggy<2,>=1.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.1.0.dev20250315) (1.5.0)
Requirement already satisfied: tomli>=1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.1.0.dev20250315) (2.2.1)
Requirement already satisfied: coverage>=7.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from coverage[toml]>=7.5->pytest-cov->Sphinx==3.1.0.dev20250315) (7.6.10)
Installing collected packages: Sphinx
  Attempting uninstall: Sphinx
    Found existing installation: Sphinx 3.1.0.dev20250204
    Uninstalling Sphinx-3.1.0.dev20250204:
      Successfully uninstalled Sphinx-3.1.0.dev20250204
  DEPRECATION: Legacy editable install of Sphinx[test]==3.1.0.dev20250315 from file:///testbed (setup.py develop) is deprecated. pip 25.1 will enforce this behaviour change. A possible replacement is to add a pyproject.toml or enable --use-pep517, and use setuptools >= 64. If the resulting installation is not behaving as expected, try using --config-settings editable_mode=compat. Please consult the setuptools documentation for more information. Discussion can be found at https://github.com/pypa/pip/issues/11457
  Running setup.py develop for Sphinx
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
Successfully installed Sphinx
+ git checkout 2e506c5ab457cba743bb47eb5b8c8eb9dd51d23d tests/test_domain_cpp.py
Updated 0 paths from e3c9d6e97
+ git apply -v -
Checking patch tests/test_domain_cpp.py...
Applied patch tests/test_domain_cpp.py cleanly.
+ tox --current-env -epy39 -v -- tests/test_domain_cpp.py
py39: commands[0]> pytest -rA --durations 25 tests/test_domain_cpp.py
============================= test session starts ==============================
platform linux -- Python 3.9.21, pytest-8.3.4, pluggy-1.5.0
cachedir: .tox/py39/.pytest_cache
libraries: Sphinx-3.1.0+/2e506c5ab, docutils-0.21.2
base tempdir: /tmp/pytest-of-root/pytest-0
rootdir: /testbed
configfile: setup.cfg
plugins: cov-6.0.0
collected 25 items

tests/test_domain_cpp.py .F.......................                       [100%]

=================================== FAILURES ===================================
_______________________________ test_expressions _______________________________

    def test_expressions():
        def exprCheck(expr, id, id4=None):
            ids = 'IE1CIA%s_1aE'
            idDict = {2: ids % expr, 3: ids % id}
            if id4 is not None:
                idDict[4] = ids % id4
            check('class', 'template<> C<a[%s]>' % expr, idDict)
    
            class Config:
                cpp_id_attributes = ["id_attr"]
                cpp_paren_attributes = ["paren_attr"]
    
            parser = DefinitionParser(expr, location=None,
                                      config=Config())
            parser.allowFallbackExpressionParsing = False
            ast = parser.parse_expression()
            res = str(ast)
            if res != expr:
                print("")
                print("Input:    ", expr)
                print("Result:   ", res)
                raise DefinitionError("")
        # primary
        exprCheck('nullptr', 'LDnE')
        exprCheck('true', 'L1E')
        exprCheck('false', 'L0E')
        ints = ['5', '0', '075', '0x0123456789ABCDEF', '0XF', '0b1', '0B1']
        unsignedSuffix = ['', 'u', 'U']
        longSuffix = ['', 'l', 'L', 'll', 'LL']
        for i in ints:
            for u in unsignedSuffix:
                for l in longSuffix:
                    expr = i + u + l
                    exprCheck(expr, 'L' + expr + 'E')
                    expr = i + l + u
                    exprCheck(expr, 'L' + expr + 'E')
        decimalFloats = ['5e42', '5e+42', '5e-42',
                      '5.', '5.e42', '5.e+42', '5.e-42',
                      '.5', '.5e42', '.5e+42', '.5e-42',
                      '5.0', '5.0e42', '5.0e+42', '5.0e-42']
        hexFloats = ['ApF', 'Ap+F', 'Ap-F',
                     'A.', 'A.pF', 'A.p+F', 'A.p-F',
                     '.A', '.ApF', '.Ap+F', '.Ap-F',
                     'A.B', 'A.BpF', 'A.Bp+F', 'A.Bp-F']
        for suffix in ['', 'f', 'F', 'l', 'L']:
            for e in decimalFloats:
                expr = e + suffix
                exprCheck(expr, 'L' + expr + 'E')
            for e in hexFloats:
                expr = "0x" + e + suffix
                exprCheck(expr, 'L' + expr + 'E')
        exprCheck('"abc\\"cba"', 'LA8_KcE')  # string
        exprCheck('this', 'fpT')
        # character literals
        charPrefixAndIds = [('', 'c'), ('u8', 'c'), ('u', 'Ds'), ('U', 'Di'), ('L', 'w')]
        chars = [('a', '97'), ('\\n', '10'), ('\\012', '10'), ('\\0', '0'),
                 ('\\x0a', '10'), ('\\x0A', '10'), ('\\u0a42', '2626'), ('\\u0A42', '2626'),
                 ('\\U0001f34c', '127820'), ('\\U0001F34C', '127820')]
        for p, t in charPrefixAndIds:
            for c, val in chars:
                exprCheck("{}'{}'".format(p, c), t + val)
        # user-defined literals
        for i in ints:
>           exprCheck(i + '_udl', 'clL_Zli4_udlEL' + i + 'EE')

tests/test_domain_cpp.py:176: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
tests/test_domain_cpp.py:119: in exprCheck
    check('class', 'template<> C<a[%s]>' % expr, idDict)
tests/test_domain_cpp.py:88: in check
    _check(name, input, idDict, output)
tests/test_domain_cpp.py:38: in _check
    ast = parse(name, input)
tests/test_domain_cpp.py:29: in parse
    parser.assert_end()
sphinx/util/cfamily.py:348: in assert_end
    self.fail('Expected end of definition.')
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sphinx.domains.cpp.DefinitionParser object at 0x76ae51874e20>
msg = 'Expected end of definition.'

    def fail(self, msg: str) -> None:
        errors = []
        indicator = '-' * self.pos + '^'
        exMain = DefinitionError(
            'Invalid %s declaration: %s [error at %d]\n  %s\n  %s' %
            (self.language, msg, self.pos, self.definition, indicator))
        errors.append((exMain, "Main error"))
        for err in self.otherErrors:
            errors.append((err, "Potential other error"))
        self.otherErrors = []
>       raise self._make_multi_error(errors, '')
E       sphinx.util.cfamily.DefinitionError: 
E       Main error:
E         Invalid C++ declaration: Expected end of definition. [error at 12]
E           template<> C<a[5_udl]>
E           ------------^
E       Potential other error:
E         Error in parsing template argument list.
E         If type argument:
E           Error in declarator or parameters-and-qualifiers
E           If pointer to member declarator:
E             Main error:
E               Invalid C++ declaration: Expected identifier in nested name. [error at 14]
E                 template<> C<a[5_udl]>
E                 --------------^
E             Potential other error:
E               Error in parsing template argument list.
E               If type argument:
E                 Error in declarator or parameters-and-qualifiers
E                 If pointer to member declarator:
E                   Invalid C++ declaration: Expected identifier in nested name. [error at 14]
E                     template<> C<a[5_udl]>
E                     --------------^
E                 If declarator-id:
E                   Invalid C++ declaration: Expected ']' in end of array operator. [error at 16]
E                     template<> C<a[5_udl]>
E                     ----------------^
E               If non-type argument:
E                 Invalid C++ declaration: Expected ']' in end of postfix expression. [error at 16]
E                   template<> C<a[5_udl]>
E                   ----------------^
E           If declarator-id:
E             Invalid C++ declaration: Expected ']' in end of array operator. [error at 16]
E               template<> C<a[5_udl]>
E               ----------------^
E         If non-type argument:
E           Invalid C++ declaration: Expected ']' in end of postfix expression. [error at 16]
E             template<> C<a[5_udl]>
E             ----------------^

sphinx/util/cfamily.py:279: DefinitionError
=============================== warnings summary ===============================
sphinx/util/docutils.py:45
  /testbed/sphinx/util/docutils.py:45: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
    __version_info__ = tuple(LooseVersion(docutils.__version__).version)

sphinx/registry.py:22
  /testbed/sphinx/registry.py:22: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
    from pkg_resources import iter_entry_points

../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('sphinxcontrib')`.
  Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
    declare_namespace(pkg)

sphinx/directives/patches.py:15
  /testbed/sphinx/directives/patches.py:15: DeprecationWarning: The `docutils.parsers.rst.directive.html` module will be removed in Docutils 2.0. Since Docutils 0.18, the "Meta" node is defined in `docutils.parsers.rst.directives.misc`.
    from docutils.parsers.rst.directives import images, html, tables

tests/test_domain_cpp.py: 826 warnings
  /testbed/sphinx/domains/cpp.py:838: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(txt, txt))

tests/test_domain_cpp.py: 10 warnings
  /testbed/sphinx/domains/cpp.py:855: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(txt, txt))

tests/test_domain_cpp.py: 104 warnings
  /testbed/sphinx/domains/cpp.py:882: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(txt, txt))

tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
  /testbed/sphinx/domains/cpp.py:2283: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(' : ', ' : '))

tests/test_domain_cpp.py::test_class_definitions
tests/test_domain_cpp.py::test_class_definitions
  /testbed/sphinx/domains/cpp.py:971: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text('(', '('))

tests/test_domain_cpp.py::test_class_definitions
tests/test_domain_cpp.py::test_class_definitions
  /testbed/sphinx/domains/cpp.py:973: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(')', ')'))

tests/test_domain_cpp.py: 18 warnings
  /testbed/sphinx/util/cfamily.py:135: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(txt, txt))

tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
  /testbed/sphinx/util/cfamily.py:169: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(txt, txt))

tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
  /testbed/sphinx/util/cfamily.py:182: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(self.id, self.id))

tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
  /testbed/sphinx/util/cfamily.py:197: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(txt, txt))

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:210: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse():  # type: Node

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/i18n.py:88: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.translatable):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:110: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for ref in self.document.traverse(nodes.substitution_reference):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:131: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.target):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:150: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.block_quote):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:175: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.Element):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:222: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.index):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/references.py:30: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.substitution_definition):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:189: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.section):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:279: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.doctest_block):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/domains/citation.py:117: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.citation):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/domains/citation.py:136: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.citation_reference):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/builders/latex/transforms.py:37: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: nodes.Element

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:291: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: Element

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/util/compat.py:44: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.index):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/domains/index.py:52: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in document.traverse(addnodes.index):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/domains/math.py:85: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    self.data['has_equations'][docname] = any(document.traverse(math_node))

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/environment/collectors/asset.py:47: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/environment/collectors/asset.py:124: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(addnodes.download_reference):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/environment/collectors/title.py:46: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.section):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:301: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.system_message):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:384: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.manpage):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/i18n.py:484: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for inline in self.document.traverse(matcher):  # type: nodes.inline

tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
tests/test_domain_cpp.py::test_xref_consistency
  /testbed/sphinx/builders/html/__init__.py:415: DeprecationWarning: The frontend.OptionParser class will be replaced by a subclass of argparse.ArgumentParser in Docutils 0.21 or later.
    self.docsettings = OptionParser(

tests/test_domain_cpp.py: 648 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/optparse.py:1000: DeprecationWarning: The frontend.Option class will be removed in Docutils 0.21 or later.
    option = self.option_class(*args, **kwargs)

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/domains/cpp.py:6768: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(AliasNode):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/transforms/post_transforms/__init__.py:71: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.pending_xref):

tests/test_domain_cpp.py: 234 warnings
  /testbed/sphinx/util/nodes.py:596: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in document.traverse(addnodes.only):

tests/test_domain_cpp.py: 234 warnings
  /testbed/sphinx/transforms/post_transforms/images.py:36: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.image):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/transforms/post_transforms/__init__.py:214: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.desc_sig_element):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/builders/latex/transforms.py:595: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.title):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:44: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.highlightlang):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:99: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for lbnode in self.document.traverse(nodes.literal_block):  # type: nodes.literal_block

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:103: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for dbnode in self.document.traverse(nodes.doctest_block):  # type: nodes.doctest_block

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/environment/__init__.py:541: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for toctreenode in doctree.traverse(addnodes.toctree):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/builders/__init__.py:182: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/builders/html/__init__.py:827: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/environment/adapters/toctree.py:313: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in toc.traverse(nodes.reference):

tests/test_domain_cpp.py: 135 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:114: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.
    _gaq.push(['_setAllowLinker', true]);

tests/test_domain_cpp.py: 135 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/about.html:70: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py: 135 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/about.html:99: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py: 135 warnings
  /testbed/sphinx/environment/adapters/toctree.py:327: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for toctreenode in doctree.traverse(addnodes.toctree):

tests/test_domain_cpp.py: 135 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:215: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py: 135 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:238: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py: 45 warnings
  /testbed/sphinx/builders/latex/transforms.py:597: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for i, index in enumerate(node.traverse(addnodes.index)):

tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
tests/test_domain_cpp.py::test_xref_consistency
  <template>:33: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
tests/test_domain_cpp.py::test_xref_consistency
  <template>:224: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
tests/test_domain_cpp.py::test_xref_consistency
  <template>:386: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
tests/test_domain_cpp.py::test_xref_consistency
  <template>:401: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py: 234 warnings
  /testbed/sphinx/util/nodes.py:348: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for img in node.traverse(nodes.image):

tests/test_domain_cpp.py: 234 warnings
  /testbed/sphinx/util/nodes.py:350: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for raw in node.traverse(nodes.raw):

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
==================================== PASSES ====================================
________________________________ test_templates ________________________________
------------------------------ Captured log call -------------------------------
WARNING  sphinx.sphinx.util.cfamily:logging.py:125 Too many template argument lists compared to parameter lists. Argument lists: 1, Parameter lists: 0, Extra empty parameters lists prepended: 1. Declaration:
	A<T>
WARNING  sphinx.sphinx.util.cfamily:logging.py:125 Too many template argument lists compared to parameter lists. Argument lists: 1, Parameter lists: 0, Extra empty parameters lists prepended: 1. Declaration:
	A<T>
___________________ test_build_domain_cpp_multi_decl_lookup ____________________
----------------------------- Captured stdout call -----------------------------
Filtered warnings for file 'lookup-key-overload':
Filtered warnings for file 'multi-decl-lookup':
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 13 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[  7%] [35manon-dup-decl[39;49;00m                                        
[01mreading sources... [39;49;00m[ 15%] [35many-role[39;49;00m                                             
[01mreading sources... [39;49;00m[ 23%] [35mbackslash[39;49;00m                                            
[01mreading sources... [39;49;00m[ 30%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 38%] [35mlookup-key-overload[39;49;00m                                  
[01mreading sources... [39;49;00m[ 46%] [35mmulti-decl-lookup[39;49;00m                                    
[01mreading sources... [39;49;00m[ 53%] [35mroles[39;49;00m                                                
[01mreading sources... [39;49;00m[ 61%] [35mroles-targets-ok[39;49;00m                                     
[01mreading sources... [39;49;00m[ 69%] [35mroles-targets-warn[39;49;00m                                   
[01mreading sources... [39;49;00m[ 76%] [35mroles2[39;49;00m                                               
[01mreading sources... [39;49;00m[ 84%] [35msemicolon[39;49;00m                                            
[01mreading sources... [39;49;00m[ 92%] [35mwarn-template-param-qualified-name[39;49;00m                   
[01mreading sources... [39;49;00m[100%] [35mxref_consistency[39;49;00m                                     
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:3: WARNING: Duplicate declaration, A[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/any-role.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/lookup-key-overload.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/multi-decl-lookup.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/semicolon.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/xref_consistency.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:2: WARNING: cpp:identifier reference target not found: @a[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:3: WARNING: cpp:identifier reference target not found: @b[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:45: WARNING: cpp:identifier reference target not found: MySpecificEnum[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:50: WARNING: cpp:identifier reference target not found: paren_5[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:51: WARNING: cpp:identifier reference target not found: paren_6[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:52: WARNING: cpp:identifier reference target not found: paren_7[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:53: WARNING: cpp:identifier reference target not found: paren_8[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst:49: WARNING: cpp:var reference target not found: Variables[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a enumerator (A).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:7: WARNING: cpp:type reference target not found: T::typeWarn[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:11: WARNING: cpp:type reference target not found: T::U::typeWarn[39;49;00m

___________ test_build_domain_cpp_warn_template_param_qualified_name ___________
----------------------------- Captured stdout call -----------------------------
Filtered warnings for file 'warn-template-param-qualified-name':
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:7: WARNING: cpp:type reference target not found: T::typeWarn[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:11: WARNING: cpp:type reference target not found: T::U::typeWarn[39;49;00m
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:2: WARNING: cpp:identifier reference target not found: @a[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:3: WARNING: cpp:identifier reference target not found: @b[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:45: WARNING: cpp:identifier reference target not found: MySpecificEnum[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:50: WARNING: cpp:identifier reference target not found: paren_5[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:51: WARNING: cpp:identifier reference target not found: paren_6[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:52: WARNING: cpp:identifier reference target not found: paren_7[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:53: WARNING: cpp:identifier reference target not found: paren_8[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst:49: WARNING: cpp:var reference target not found: Variables[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a enumerator (A).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:7: WARNING: cpp:type reference target not found: T::typeWarn[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:11: WARNING: cpp:type reference target not found: T::U::typeWarn[39;49;00m

______________________ test_build_domain_cpp_backslash_ok ______________________
----------------------------- Captured stdout call -----------------------------
Filtered warnings for file 'backslash':
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst:1: WARNING: Parsing of expression failed. Using fallback parser. Error was:
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[config changed ('strip_signature_backslash')] 13 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[  7%] [35manon-dup-decl[39;49;00m                                        
[01mreading sources... [39;49;00m[ 15%] [35many-role[39;49;00m                                             
[01mreading sources... [39;49;00m[ 23%] [35mbackslash[39;49;00m                                            
[01mreading sources... [39;49;00m[ 30%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 38%] [35mlookup-key-overload[39;49;00m                                  
[01mreading sources... [39;49;00m[ 46%] [35mmulti-decl-lookup[39;49;00m                                    
[01mreading sources... [39;49;00m[ 53%] [35mroles[39;49;00m                                                
[01mreading sources... [39;49;00m[ 61%] [35mroles-targets-ok[39;49;00m                                     
[01mreading sources... [39;49;00m[ 69%] [35mroles-targets-warn[39;49;00m                                   
[01mreading sources... [39;49;00m[ 76%] [35mroles2[39;49;00m                                               
[01mreading sources... [39;49;00m[ 84%] [35msemicolon[39;49;00m                                            
[01mreading sources... [39;49;00m[ 92%] [35mwarn-template-param-qualified-name[39;49;00m                   
[01mreading sources... [39;49;00m[100%] [35mxref_consistency[39;49;00m                                     
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst:1: WARNING: Parsing of expression failed. Using fallback parser. Error was:
Error in postfix expression, expected primary expression or type.
If primary expression:
  Invalid C++ declaration: Expected identifier in nested name. [error at 9]
    char c = '\'
    ---------^
If type:
  Invalid C++ declaration: Expected identifier in nested name. [error at 9]
    char c = '\'
    ---------^
[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/any-role.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/lookup-key-overload.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/multi-decl-lookup.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/semicolon.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/xref_consistency.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:2: WARNING: cpp:identifier reference target not found: @a[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:3: WARNING: cpp:identifier reference target not found: @b[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:45: WARNING: cpp:identifier reference target not found: MySpecificEnum[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:50: WARNING: cpp:identifier reference target not found: paren_5[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:51: WARNING: cpp:identifier reference target not found: paren_6[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:52: WARNING: cpp:identifier reference target not found: paren_7[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:53: WARNING: cpp:identifier reference target not found: paren_8[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst:49: WARNING: cpp:var reference target not found: Variables[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:7: WARNING: cpp:type reference target not found: T::typeWarn[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:11: WARNING: cpp:type reference target not found: T::U::typeWarn[39;49;00m

_______________________ test_build_domain_cpp_semicolon ________________________
----------------------------- Captured stdout call -----------------------------
Filtered warnings for file 'semicolon':
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[config changed ('strip_signature_backslash')] 13 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[  7%] [35manon-dup-decl[39;49;00m                                        
[01mreading sources... [39;49;00m[ 15%] [35many-role[39;49;00m                                             
[01mreading sources... [39;49;00m[ 23%] [35mbackslash[39;49;00m                                            
[01mreading sources... [39;49;00m[ 30%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 38%] [35mlookup-key-overload[39;49;00m                                  
[01mreading sources... [39;49;00m[ 46%] [35mmulti-decl-lookup[39;49;00m                                    
[01mreading sources... [39;49;00m[ 53%] [35mroles[39;49;00m                                                
[01mreading sources... [39;49;00m[ 61%] [35mroles-targets-ok[39;49;00m                                     
[01mreading sources... [39;49;00m[ 69%] [35mroles-targets-warn[39;49;00m                                   
[01mreading sources... [39;49;00m[ 76%] [35mroles2[39;49;00m                                               
[01mreading sources... [39;49;00m[ 84%] [35msemicolon[39;49;00m                                            
[01mreading sources... [39;49;00m[ 92%] [35mwarn-template-param-qualified-name[39;49;00m                   
[01mreading sources... [39;49;00m[100%] [35mxref_consistency[39;49;00m                                     
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/any-role.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/lookup-key-overload.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/multi-decl-lookup.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/semicolon.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/xref_consistency.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:2: WARNING: cpp:identifier reference target not found: @a[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:3: WARNING: cpp:identifier reference target not found: @b[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:45: WARNING: cpp:identifier reference target not found: MySpecificEnum[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:50: WARNING: cpp:identifier reference target not found: paren_5[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:51: WARNING: cpp:identifier reference target not found: paren_6[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:52: WARNING: cpp:identifier reference target not found: paren_7[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:53: WARNING: cpp:identifier reference target not found: paren_8[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst:49: WARNING: cpp:var reference target not found: Variables[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:7: WARNING: cpp:type reference target not found: T::typeWarn[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:11: WARNING: cpp:type reference target not found: T::U::typeWarn[39;49;00m

_____________________ test_build_domain_cpp_anon_dup_decl ______________________
----------------------------- Captured stdout call -----------------------------
Filtered warnings for file 'anon-dup-decl':
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:2: WARNING: cpp:identifier reference target not found: @a[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:3: WARNING: cpp:identifier reference target not found: @b[39;49;00m
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:2: WARNING: cpp:identifier reference target not found: @a[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:3: WARNING: cpp:identifier reference target not found: @b[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:45: WARNING: cpp:identifier reference target not found: MySpecificEnum[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:50: WARNING: cpp:identifier reference target not found: paren_5[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:51: WARNING: cpp:identifier reference target not found: paren_6[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:52: WARNING: cpp:identifier reference target not found: paren_7[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:53: WARNING: cpp:identifier reference target not found: paren_8[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst:49: WARNING: cpp:var reference target not found: Variables[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:7: WARNING: cpp:type reference target not found: T::typeWarn[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:11: WARNING: cpp:type reference target not found: T::U::typeWarn[39;49;00m

____________________ test_build_domain_cpp_misuse_of_roles _____________________
----------------------------- Captured stdout call -----------------------------
Filtered warnings for file 'roles-targets-ok':
Filtered warnings for file 'roles-targets-warn':
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
Expected warnings:
WARNING: cpp:class targets a concept (
WARNING: cpp:class targets a enum (
WARNING: cpp:class targets a enumerator (
WARNING: cpp:class targets a function (
WARNING: cpp:class targets a functionParam (
WARNING: cpp:class targets a member (
WARNING: cpp:class targets a type (
WARNING: cpp:class targets a union (
WARNING: cpp:concept targets a class (
WARNING: cpp:concept targets a enum (
WARNING: cpp:concept targets a enumerator (
WARNING: cpp:concept targets a function (
WARNING: cpp:concept targets a functionParam (
WARNING: cpp:concept targets a member (
WARNING: cpp:concept targets a type (
WARNING: cpp:concept targets a union (
WARNING: cpp:enum targets a class (
WARNING: cpp:enum targets a concept (
WARNING: cpp:enum targets a enumerator (
WARNING: cpp:enum targets a function (
WARNING: cpp:enum targets a functionParam (
WARNING: cpp:enum targets a member (
WARNING: cpp:enum targets a type (
WARNING: cpp:enum targets a union (
WARNING: cpp:enumerator targets a class (
WARNING: cpp:enumerator targets a concept (
WARNING: cpp:enumerator targets a enum (
WARNING: cpp:enumerator targets a function (
WARNING: cpp:enumerator targets a functionParam (
WARNING: cpp:enumerator targets a member (
WARNING: cpp:enumerator targets a type (
WARNING: cpp:enumerator targets a union (
WARNING: cpp:func targets a class (
WARNING: cpp:func targets a concept (
WARNING: cpp:func targets a enum (
WARNING: cpp:func targets a enumerator (
WARNING: cpp:func targets a functionParam (
WARNING: cpp:func targets a member (
WARNING: cpp:func targets a type (
WARNING: cpp:func targets a union (
WARNING: cpp:member targets a class (
WARNING: cpp:member targets a concept (
WARNING: cpp:member targets a enum (
WARNING: cpp:member targets a enumerator (
WARNING: cpp:member targets a function (
WARNING: cpp:member targets a type (
WARNING: cpp:member targets a union (
WARNING: cpp:struct targets a concept (
WARNING: cpp:struct targets a enum (
WARNING: cpp:struct targets a enumerator (
WARNING: cpp:struct targets a function (
WARNING: cpp:struct targets a functionParam (
WARNING: cpp:struct targets a member (
WARNING: cpp:struct targets a type (
WARNING: cpp:struct targets a union (
WARNING: cpp:type targets a concept (
WARNING: cpp:type targets a enumerator (
WARNING: cpp:type targets a functionParam (
WARNING: cpp:type targets a member (
WARNING: cpp:union targets a class (
WARNING: cpp:union targets a concept (
WARNING: cpp:union targets a enum (
WARNING: cpp:union targets a enumerator (
WARNING: cpp:union targets a function (
WARNING: cpp:union targets a functionParam (
WARNING: cpp:union targets a member (
WARNING: cpp:union targets a type (
WARNING: cpp:var targets a class (
WARNING: cpp:var targets a concept (
WARNING: cpp:var targets a enum (
WARNING: cpp:var targets a enumerator (
WARNING: cpp:var targets a function (
WARNING: cpp:var targets a type (
WARNING: cpp:var targets a union (
Actual warnings:
WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[config changed ('add_function_parentheses')] 13 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[  7%] [35manon-dup-decl[39;49;00m                                        
[01mreading sources... [39;49;00m[ 15%] [35many-role[39;49;00m                                             
[01mreading sources... [39;49;00m[ 23%] [35mbackslash[39;49;00m                                            
[01mreading sources... [39;49;00m[ 30%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 38%] [35mlookup-key-overload[39;49;00m                                  
[01mreading sources... [39;49;00m[ 46%] [35mmulti-decl-lookup[39;49;00m                                    
[01mreading sources... [39;49;00m[ 53%] [35mroles[39;49;00m                                                
[01mreading sources... [39;49;00m[ 61%] [35mroles-targets-ok[39;49;00m                                     
[01mreading sources... [39;49;00m[ 69%] [35mroles-targets-warn[39;49;00m                                   
[01mreading sources... [39;49;00m[ 76%] [35mroles2[39;49;00m                                               
[01mreading sources... [39;49;00m[ 84%] [35msemicolon[39;49;00m                                            
[01mreading sources... [39;49;00m[ 92%] [35mwarn-template-param-qualified-name[39;49;00m                   
[01mreading sources... [39;49;00m[100%] [35mxref_consistency[39;49;00m                                     
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/any-role.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/lookup-key-overload.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/multi-decl-lookup.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/semicolon.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/xref_consistency.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[config changed ('add_function_parentheses')] 13 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[  7%] [35manon-dup-decl[39;49;00m                                        
[01mreading sources... [39;49;00m[ 15%] [35many-role[39;49;00m                                             
[01mreading sources... [39;49;00m[ 23%] [35mbackslash[39;49;00m                                            
[01mreading sources... [39;49;00m[ 30%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 38%] [35mlookup-key-overload[39;49;00m                                  
[01mreading sources... [39;49;00m[ 46%] [35mmulti-decl-lookup[39;49;00m                                    
[01mreading sources... [39;49;00m[ 53%] [35mroles[39;49;00m                                                
[01mreading sources... [39;49;00m[ 61%] [35mroles-targets-ok[39;49;00m                                     
[01mreading sources... [39;49;00m[ 69%] [35mroles-targets-warn[39;49;00m                                   
[01mreading sources... [39;49;00m[ 76%] [35mroles2[39;49;00m                                               
[01mreading sources... [39;49;00m[ 84%] [35msemicolon[39;49;00m                                            
[01mreading sources... [39;49;00m[ 92%] [35mwarn-template-param-qualified-name[39;49;00m                   
[01mreading sources... [39;49;00m[100%] [35mxref_consistency[39;49;00m                                     
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                            dgm
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;dgm46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;4dgm[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49dgm 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39dgm0m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... dgm9;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [3dgm00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in dgmsh (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone
dgm
# warning: 
[91mWARNING: while settingdgmxtension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytestdgmmain-cpp/any-role.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-dgmain-cpp/lookup-key-overload.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/multi-decl-lookup.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ddgm-cpp/roles.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pdgm-0/domain-cpp/roles-targets-warn.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytedgmdomain-cpp/semicolon.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytdgm/domain-cpp/xref_consistency.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/dodgmcpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domaindgmroles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpdgmes-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/rodgmargets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/rolesdgmets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domadgmp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roldgmrgets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cppdgms-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domadgmp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-tdgms-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targdgm member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domaidgm/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targdgmarn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a memdgmRolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpdgmes-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roldgmrgets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargdgmrn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roledgmgets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roldgmrgets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-dgmts-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domaindgmroles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-dgmrst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-tardgmwarn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-dgmrst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::dgm.[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targdgmarn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-dgmts-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cppdgms-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roldgmrgets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/dgm-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roledgmgets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roledgmgets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-tadgm-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roledgmgets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-dgmts-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domaidgm/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cppdgms-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m
dgm
============================= slowest 25 durations =============dgm===========
0.62s call     tests/test_domain_cpp.py::test_expressions
0.41s call     tests/test_domain_cpp.py::test_xref_consisdgm
0.40s call     tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
0.40s call     tests/test_domain_cpp.py::test_build_dodgmcpp_multi_decl_lookup
0.40s call     tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
0.39s call     tests/test_domain_cpp.py::test_build_domaindgmsemicolon
0.28s call     tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
0.27s call     tests/test_domain_cpp.py::test_build_domain_cdgmth_add_function_parentheses_is_True
0.27s call     tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
0.27s call     tests/test_domain_cpp.py::test_build_domaidgm_warn_template_param_qualified_name
0.22s setup    tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
0.09s call     tests/test_domain_cpp.py::test_function_defdgmons
0.04s call     tests/test_domain_cpp.py::test_templates
0.03s call     tests/test_domain_cpp.py::test_fundamental_types
0.03s call     tests/test_domain_cpp.py::test_type_definitionsdgm
0.03s call     tests/test_domain_cpp.py::test_operators
0.03s call     tests/test_domain_cpp.py::test_initializersdgm
0.01s setup    tests/test_domain_cpp.py::test_build_domain_cpp_mdgm_of_roles
0.01s call     tests/test_domain_cpp.py::test_class_definitions
0.01s setup    tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_paradgmlified_name
0.01s setup    tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
0.01s setup    tests/test_domain_cpp.py::test_build_domain_cpdgmicolon
0.01s setup    tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
0.01s setup    tests/test_domain_cpp.py::test_build_domain_cpp_with_add_fundgm_parentheses_is_True
0.01s setup    tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
=========================== short test summary info ======================dgm=
PASSED tests/test_domain_cpp.py::test_fundamental_types
PASSED tests/test_domain_cpp.py::test_type_definitionsdgm
PASSED tests/test_domain_cpp.py::test_concept_definitions
PASSED tests/test_domain_cpp.py::test_member_definitionsdgm
PASSED tests/test_domain_cpp.py::test_function_definitionsdgm
PASSED tests/test_domain_cpp.py::test_operators
PASSED tests/test_domain_cpp.py::test_class_definitionsdgm
PASSED tests/test_domain_cpp.py::test_union_definitidgm
PASSED tests/test_domain_cpp.py::test_enum_definitions
PASSED tests/test_domain_cpp.py::test_anon_definitionsdgm
PASSED tests/test_domain_cpp.py::test_templates
PASSED tests/test_domain_cpp.py::test_template_args
PASSED tests/test_domain_cpp.py::test_initializers
PASSED tests/test_domain_cpp.py::test_attributes
PASSED tests/test_domain_cpp.py::test_xref_parsing
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
PASSED tests/test_domain_cpp.py::test_xref_consistency
FAILED tests/test_domain_cpp.py::test_expressions - sphinx.util.cfamily.Defin...
================= 1 failed, 24 passed, 6261 warnings in 4.58s ==================
py39: exit 1 (5.12 seconds) /testbed> pytest -rA --durations 25 tests/test_domain_cpp.py pid=107
  py39: FAIL code 1 (5.13=setup[0.01]+cmd[5.12] seconds)
  evaluation failed :( (5.22 seconds)
+ git checkout 2e506c5ab457cba743bb47eb5b8c8eb9dd51d23d tests/test_domain_cpp.py
Updated 1 path from e3c9d6e97

2025-03-15 00:35:11,670 - ThreadPoolExecutor-4_3 - INFO - Container output: 
2025-03-15 00:35:11,670 - ThreadPoolExecutor-4_3 - INFO - Installing more requirements
2025-03-15 00:35:31,629 - ThreadPoolExecutor-4_3 - INFO - Container output: Collecting datasets (from -r /guava/requirements.txt (line 1))
  Downloading datasets-3.4.0-py3-none-any.whl.metadata (19 kB)
Collecting anthropic (from -r /guava/requirements.txt (line 2))
  Downloading anthropic-0.49.0-py3-none-any.whl.metadata (24 kB)
Collecting backoff (from -r /guava/requirements.txt (line 4))
  Downloading backoff-2.2.1-py3-none-any.whl.metadata (14 kB)
Collecting botocore (from -r /guava/requirements.txt (line 5))
  Downloading botocore-1.37.13-py3-none-any.whl.metadata (5.7 kB)
Collecting boto3 (from -r /guava/requirements.txt (line 6))
  Downloading boto3-1.37.13-py3-none-any.whl.metadata (6.7 kB)
Collecting openai (from -r /guava/requirements.txt (line 7))
  Downloading openai-1.66.3-py3-none-any.whl.metadata (25 kB)
Collecting beautifulsoup4 (from -r /guava/requirements.txt (line 10))
  Downloading beautifulsoup4-4.13.3-py3-none-any.whl.metadata (3.8 kB)
Collecting chardet (from -r /guava/requirements.txt (line 11))
  Using cached chardet-5.2.0-py3-none-any.whl.metadata (3.4 kB)
Collecting docker (from -r /guava/requirements.txt (line 12))
  Downloading docker-7.1.0-py3-none-any.whl.metadata (3.8 kB)
Collecting ghapi (from -r /guava/requirements.txt (line 13))
  Downloading ghapi-1.0.6-py3-none-any.whl.metadata (13 kB)
Collecting GitPython (from -r /guava/requirements.txt (line 14))
  Downloading GitPython-3.1.44-py3-none-any.whl.metadata (13 kB)
Collecting pre-commit (from -r /guava/requirements.txt (line 15))
  Downloading pre_commit-4.1.0-py2.py3-none-any.whl.metadata (1.3 kB)
Collecting python-dotenv (from -r /guava/requirements.txt (line 16))
  Downloading python_dotenv-1.0.1-py3-none-any.whl.metadata (23 kB)
Collecting rich (from -r /guava/requirements.txt (line 17))
  Downloading rich-13.9.4-py3-none-any.whl.metadata (18 kB)
Collecting unidiff (from -r /guava/requirements.txt (line 18))
  Downloading unidiff-0.7.5-py2.py3-none-any.whl.metadata (4.6 kB)
Collecting pytest (from -r /guava/requirements.txt (line 21))
  Downloading pytest-8.3.5-py3-none-any.whl.metadata (7.6 kB)
Collecting pytest-asyncio (from -r /guava/requirements.txt (line 22))
  Downloading pytest_asyncio-0.25.3-py3-none-any.whl.metadata (3.9 kB)
Collecting filelock (from datasets->-r /guava/requirements.txt (line 1))
  Downloading filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Collecting numpy>=1.17 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading numpy-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (62 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.0/62.0 kB 12.5 MB/s eta 0:00:00
Collecting pyarrow>=15.0.0 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading pyarrow-19.0.1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (3.3 kB)
Collecting dill<0.3.9,>=0.3.0 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading dill-0.3.8-py3-none-any.whl.metadata (10 kB)
Collecting pandas (from datasets->-r /guava/requirements.txt (line 1))
  Downloading pandas-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (89 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 89.9/89.9 kB 17.6 MB/s eta 0:00:00
Collecting requests>=2.32.2 (from datasets->-r /guava/requirements.txt (line 1))
  Using cached requests-2.32.3-py3-none-any.whl.metadata (4.6 kB)
Collecting tqdm>=4.66.3 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 57.7/57.7 kB 14.2 MB/s eta 0:00:00
Collecting xxhash (from datasets->-r /guava/requirements.txt (line 1))
  Downloading xxhash-3.5.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (12 kB)
Collecting multiprocess<0.70.17 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading multiprocess-0.70.16-py311-none-any.whl.metadata (7.2 kB)
Collecting fsspec<=2024.12.0,>=2023.1.0 (from fsspec[http]<=2024.12.0,>=2023.1.0->datasets->-r /guava/requirements.txt (line 1))
  Downloading fsspec-2024.12.0-py3-none-any.whl.metadata (11 kB)
Collecting aiohttp (from datasets->-r /guava/requirements.txt (line 1))
  Downloading aiohttp-3.11.13-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (7.7 kB)
Collecting huggingface-hub>=0.24.0 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading huggingface_hub-0.29.3-py3-none-any.whl.metadata (13 kB)
Requirement already satisfied: packaging in /opt/miniconda3/lib/python3.11/site-packages (from datasets->-r /guava/requirements.txt (line 1)) (23.1)
Collecting pyyaml>=5.1 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.1 kB)
Collecting anyio<5,>=3.5.0 (from anthropic->-r /guava/requirements.txt (line 2))
  Downloading anyio-4.8.0-py3-none-any.whl.metadata (4.6 kB)
Requirement already satisfied: distro<2,>=1.7.0 in /opt/miniconda3/lib/python3.11/site-packages (from anthropic->-r /guava/requirements.txt (line 2)) (1.8.0)
Collecting httpx<1,>=0.23.0 (from anthropic->-r /guava/requirements.txt (line 2))
  Downloading httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Collecting jiter<1,>=0.4.0 (from anthropic->-r /guava/requirements.txt (line 2))
  Downloading jiter-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.2 kB)
Collecting pydantic<3,>=1.9.0 (from anthropic->-r /guava/requirements.txt (line 2))
  Downloading pydantic-2.10.6-py3-none-any.whl.metadata (30 kB)
Collecting sniffio (from anthropic->-r /guava/requirements.txt (line 2))
  Downloading sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting typing-extensions<5,>=4.10 (from anthropic->-r /guava/requirements.txt (line 2))
  Downloading typing_extensions-4.12.2-py3-none-any.whl.metadata (3.0 kB)
Collecting jmespath<2.0.0,>=0.7.1 (from botocore->-r /guava/requirements.txt (line 5))
  Downloading jmespath-1.0.1-py3-none-any.whl.metadata (7.6 kB)
Collecting python-dateutil<3.0.0,>=2.1 (from botocore->-r /guava/requirements.txt (line 5))
  Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Requirement already satisfied: urllib3!=2.2.0,<3,>=1.25.4 in /opt/miniconda3/lib/python3.11/site-packages (from botocore->-r /guava/requirements.txt (line 5)) (1.26.18)
Collecting s3transfer<0.12.0,>=0.11.0 (from boto3->-r /guava/requirements.txt (line 6))
  Downloading s3transfer-0.11.4-py3-none-any.whl.metadata (1.7 kB)
Collecting soupsieve>1.2 (from beautifulsoup4->-r /guava/requirements.txt (line 10))
  Downloading soupsieve-2.6-py3-none-any.whl.metadata (4.6 kB)
Collecting fastcore>=1.7.2 (from ghapi->-r /guava/requirements.txt (line 13))
  Downloading fastcore-1.7.29-py3-none-any.whl.metadata (3.6 kB)
Collecting gitdb<5,>=4.0.1 (from GitPython->-r /guava/requirements.txt (line 14))
  Downloading gitdb-4.0.12-py3-none-any.whl.metadata (1.2 kB)
Collecting cfgv>=2.0.0 (from pre-commit->-r /guava/requirements.txt (line 15))
  Downloading cfgv-3.4.0-py2.py3-none-any.whl.metadata (8.5 kB)
Collecting identify>=1.0.0 (from pre-commit->-r /guava/requirements.txt (line 15))
  Downloading identify-2.6.9-py2.py3-none-any.whl.metadata (4.4 kB)
Collecting nodeenv>=0.11.1 (from pre-commit->-r /guava/requirements.txt (line 15))
  Downloading nodeenv-1.9.1-py2.py3-none-any.whl.metadata (21 kB)
Collecting virtualenv>=20.10.0 (from pre-commit->-r /guava/requirements.txt (line 15))
  Downloading virtualenv-20.29.3-py3-none-any.whl.metadata (4.5 kB)
Collecting markdown-it-py>=2.2.0 (from rich->-r /guava/requirements.txt (line 17))
  Downloading markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)
Collecting pygments<3.0.0,>=2.13.0 (from rich->-r /guava/requirements.txt (line 17))
  Using cached pygments-2.19.1-py3-none-any.whl.metadata (2.5 kB)
Collecting iniconfig (from pytest->-r /guava/requirements.txt (line 21))
  Using cached iniconfig-2.0.0-py3-none-any.whl.metadata (2.6 kB)
Collecting pluggy<2,>=1.5 (from pytest->-r /guava/requirements.txt (line 21))
  Using cached pluggy-1.5.0-py3-none-any.whl.metadata (4.8 kB)
Requirement already satisfied: idna>=2.8 in /opt/miniconda3/lib/python3.11/site-packages (from anyio<5,>=3.5.0->anthropic->-r /guava/requirements.txt (line 2)) (3.4)
Collecting aiohappyeyeballs>=2.3.0 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl.metadata (5.9 kB)dgm
Collecting aiosignal>=1.1.2 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading aiosignal-1.3.2-py2.py3-none-any.whl.metadata (3.8 kB)
Collecting attrs>=17.3.0 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading attrs-25.3.0-py3-none-any.whl.metadata (10 kB)
Collecting frozenlist>=1.1.1 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading frozenlist-1.5.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (13 kB)
Collecting multidict<7.0,>=4.5 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading multidict-6.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.0 kB)
Collecting propcache>=0.2.0 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading propcache-0.3.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (10 kB)
Collecting yarl<2.0,>=1.17.0 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading yarl-1.18.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (69 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 69.2/69.2 kB 12.2 MB/s eta 0:00:00
Collecting smmap<6,>=3.0.1 (from gitdb<5,>=4.0.1->GitPython->-r /guava/requirements.txt (line 14))
  Downloading smmap-5.0.2-py3-none-any.whl.metadata (4.3 kB)
Requirement already satisfied: certifi in /opt/miniconda3/lib/python3.11/site-packages (from httpx<1,>=0.23.0->anthropic->-r /guava/requirements.txt (line 2)) (2023.11.17)
Collecting httpcore==1.* (from httpx<1,>=0.23.0->anthropic->-r /guava/requirements.txt (line 2))
  Downloading httpcore-1.0.7-py3-none-any.whl.metadata (21 kB)
Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->anthropic->-r /guava/requirements.txt (line 2))
  Downloading h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)
Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich->-r /guava/requirements.txt (line 17))
  Downloading mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)
Collecting annotated-types>=0.6.0 (from pydantic<3,>=1.9.0->anthropic->-r /guava/requirements.txt (line 2))
  Downloading annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.27.2 (from pydantic<3,>=1.9.0->anthropic->-r /guava/requirements.txt (line 2))
  Downloading pydantic_core-2.27.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.6 kB)
Collecting six>=1.5 (from python-dateutil<3.0.0,>=2.1->botocore->-r /guava/requirements.txt (line 5))
  Using cached six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/lib/python3.11/site-packages (from requests>=2.32.2->datasets->-r /guava/requirements.txt (line 1)) (2.0.4)
Collecting distlib<1,>=0.3.7 (from virtualenv>=20.10.0->pre-commit->-r /guava/requirements.txt (line 15))
  Using cached distlib-0.3.9-py2.py3-none-any.whl.metadata (5.2 kB)
Requirement already satisfied: platformdirs<5,>=3.9.1 in /opt/miniconda3/lib/python3.11/site-packages (from virtualenv>=20.10.0->pre-commit->-r /guava/requirements.txt (line 15)) (3.10.0)
Collecting pytz>=2020.1 (from pandas->datasets->-r /guava/requirements.txt (line 1))
  Downloading pytz-2025.1-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.7 (from pandas->datasets->-r /guava/requirements.txt (line 1))
  Downloading tzdata-2025.1-py2.py3-none-any.whl.metadata (1.4 kB)
Downloading datasets-3.4.0-py3-none-any.whl (487 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 487.4/487.4 kB 77.0 MB/s eta 0:00:00
Downloading anthropic-0.49.0-py3-none-any.whl (243 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 243.4/243.4 kB 53.6 MB/s eta 0:00:00
Downloading backoff-2.2.1-py3-none-any.whl (15 kB)
Downloading botocore-1.37.13-py3-none-any.whl (13.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.4/13.4 MB 191.2 MB/s eta 0:00:00
Downloading boto3-1.37.13-py3-none-any.whl (139 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 139.6/139.6 kB 36.4 MB/s eta 0:00:00
Downloading openai-1.66.3-py3-none-any.whl (567 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 567.4/567.4 kB 74.6 MB/s eta 0:00:00
Downloading beautifulsoup4-4.13.3-py3-none-any.whl (186 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 186.0/186.0 kB 57.6 MB/s eta 0:00:00
Using cached chardet-5.2.0-py3-none-any.whl (199 kB)
Downloading docker-7.1.0-py3-none-any.whl (147 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 147.8/147.8 kB 41.8 MB/s eta 0:00:00
Downloading ghapi-1.0.6-py3-none-any.whl (62 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.4/62.4 kB 17.9 MB/s eta 0:00:00
Downloading GitPython-3.1.44-py3-none-any.whl (207 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 207.6/207.6 kB 58.2 MB/s eta 0:00:00
Downloading pre_commit-4.1.0-py2.py3-none-any.whl (220 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 220.6/220.6 kB 58.1 MB/s eta 0:00:00
Downloading python_dotenv-1.0.1-py3-none-any.whl (19 kB)
Downloading rich-13.9.4-py3-none-any.whl (242 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 242.4/242.4 kB 67.5 MB/s eta 0:00:00
Downloading unidiff-0.7.5-py2.py3-none-any.whl (14 kB)
Downloading pytest-8.3.5-py3-none-any.whl (343 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 343.6/343.6 kB 81.2 MB/s eta 0:00:00
Downloading pytest_asyncio-0.25.3-py3-none-any.whl (19 kB)
Downloading anyio-4.8.0-py3-none-any.whl (96 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 96.0/96.0 kB 31.9 MB/s eta 0:00:00
Downloading cfgv-3.4.0-py2.py3-none-any.whl (7.2 kB)
Downloading dill-0.3.8-py3-none-any.whl (116 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 116.3/116.3 kB 33.6 MB/s eta 0:00:00
Downloading fastcore-1.7.29-py3-none-any.whl (84 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 84.2/84.2 kB 24.5 MB/s eta 0:00:00
Downloading fsspec-2024.12.0-py3-none-any.whl (183 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 183.9/183.9 kB 45.9 MB/s eta 0:00:00
Downloading aiohttp-3.11.13-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.7/1.7 MB 126.6 MB/s eta 0:00:00
Downloading gitdb-4.0.12-py3-none-any.whl (62 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.8/62.8 kB 19.3 MB/s eta 0:00:00
Downloading httpx-0.28.1-py3-none-any.whl (73 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 73.5/73.5 kB 24.5 MB/s eta 0:00:00
Downloading httpcore-1.0.7-py3-none-any.whl (78 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.6/78.6 kB 21.8 MB/s eta 0:00:00
Downloading huggingface_hub-0.29.3-py3-none-any.whl (468 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 469.0/469.0 kB 105.8 MB/s eta 0:00:00
Downloading identify-2.6.9-py2.py3-none-any.whl (99 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 99.1/99.1 kB 28.1 MB/s eta 0:00:00
Downloading jiter-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (351 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 351.8/351.8 kB 80.5 MB/s eta 0:00:00
Downloading jmespath-1.0.1-py3-none-any.whl (20 kB)
Downloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 87.5/87.5 kB 31.4 MB/s eta 0:00:00
Downloading multiprocess-0.70.16-py311-none-any.whl (143 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 143.5/143.5 kB 44.1 MB/s eta 0:00:00
Downloading nodeenv-1.9.1-py2.py3-none-any.whl (22 kB)
Downloading numpy-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (16.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 16.4/16.4 MB 164.8 MB/s eta 0:00:00
Using cached pluggy-1.5.0-py3-none-any.whl (20 kB)
Downloading pyarrow-19.0.1-cp311-cp311-manylinux_2_28_x86_64.whl (42.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 42.1/42.1 MB 72.9 MB/s eta 0:00:00
Downloading pydantic-2.10.6-py3-none-any.whl (431 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 431.7/431.7 kB 68.0 MB/s eta 0:00:00
Downloading pydantic_core-2.27.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 169.8 MB/s eta 0:00:00
Using cached pygments-2.19.1-py3-none-any.whl (1.2 MB)
Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 229.9/229.9 kB 61.6 MB/s eta 0:00:00
Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (762 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 763.0/763.0 kB 128.4 MB/s eta 0:00:00
Using cached requests-2.32.3-py3-none-any.whl (64 kB)
Downloading s3transfer-0.11.4-py3-none-any.whl (84 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 84.4/84.4 kB 23.6 MB/s eta 0:00:00
Downloading sniffio-1.3.1-py3-none-any.whl (10 kB)
Downloading soupsieve-2.6-py3-none-any.whl (36 kB)
Downloading tqdm-4.67.1-py3-none-any.whl (78 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.5/78.5 kB 25.8 MB/s eta 0:00:00
Downloading typing_extensions-4.12.2-py3-none-any.whl (37 kB)
Downloading virtualenv-20.29.3-py3-none-any.whl (4.3 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 4.3/4.3 MB 192.3 MB/s eta 0:00:00
Downloading filelock-3.18.0-py3-none-any.whl (16 kB)
Using cached iniconfig-2.0.0-py3-none-any.whl (5.9 kB)
Downloading pandas-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (13.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.1/13.1 MB 42.1 MB/s eta 0:00:00
Downloading xxhash-3.5.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (194 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 194.8/194.8 kB 46.5 MB/s eta 0:00:00
Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
Downloading aiosignal-1.3.2-py2.py3-none-any.whl (7.6 kB)
Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
Downloading attrs-25.3.0-py3-none-any.whl (63 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 63.8/63.8 kB 21.4 MB/s eta 0:00:00
Using cached distlib-0.3.9-py2.py3-none-any.whl (468 kB)
Downloading frozenlist-1.5.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (274 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 274.9/274.9 kB 75.3 MB/s eta 0:00:00
Downloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)
Downloading multidict-6.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (129 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 129.0/129.0 kB 45.4 MB/s eta 0:00:00
Downloading propcache-0.3.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (231 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 231.3/231.3 kB 64.1 MB/s eta 0:00:00
Downloading pytz-2025.1-py2.py3-none-any.whl (507 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 507.9/507.9 kB 104.7 MB/s eta 0:00:00
Using cached six-1.17.0-py2.py3-none-any.whl (11 kB)
Downloading smmap-5.0.2-py3-none-any.whl (24 kB)
Downloading tzdata-2025.1-py2.py3-none-any.whl (346 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 346.8/346.8 kB 81.8 MB/s eta 0:00:00
Downloading yarl-1.18.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (344 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 344.1/344.1 kB 84.9 MB/s eta 0:00:00
Downloading h11-0.14.0-py3-none-any.whl (58 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 58.3/58.3 kB 17.1 MB/s eta 0:00:00
Installing collected packages: unidiff, pytz, distlib, xxhash, tzdata, typing-extensions, tqdm, soupsieve, sniffio, smmap, six, requests, pyyaml, python-dotenv, pygments, pyarrow, propcache, pluggy, numpy, nodeenv, multidict, mdurl, jmespath, jiter, iniconfig, identify, h11, fsspec, frozenlist, filelock, fastcore, dill, chardet, cfgv, backoff, attrs, annotated-types, aiohappyeyeballs, yarl, virtualenv, python-dateutil, pytest, pydantic-core, multiprocess, markdown-it-py, huggingface-hub, httpcore, gitdb, ghapi, docker, beautifulsoup4, anyio, aiosignal, rich, pytest-asyncio, pydantic, pre-commit, pandas, httpx, GitPython, botocore, aiohttp, s3transfer, openai, anthropic, datasets, boto3
  Attempting uninstall: tqdm
    Found existing installation: tqdm 4.65.0
    Uninstalling tqdm-4.65.0:
      Successfully uninstalled tqdm-4.65.0
  Attempting uninstall: requests
    Found existing installation: requests 2.31.0
    Uninstalling requests-2.31.0:
      Successfully uninstalled requests-2.31.0
  Attempting uninstall: pluggy
    Found existing installation: pluggy 1.0.0
    Uninstalling pluggy-1.0.0:
      Successfully uninstalled pluggy-1.0.0
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
Successfully installed GitPython-3.1.44 aiohappyeyeballs-2.6.1 aiohttp-3.11.13 aiosignal-1.3.2 annotated-types-0.7.0 anthropic-0.49.0 anyio-4.8.0 attrs-25.3.0 backoff-2.2.1 beautifulsoup4-4.13.3 boto3-1.37.13 botocore-1.37.13 cfgv-3.4.0 chardet-5.2.0 datasets-3.4.0 dill-0.3.8 distlib-0.3.9 docker-7.1.0 fastcore-1.7.29 filelock-3.18.0 frozenlist-1.5.0 fsspec-2024.12.0 ghapi-1.0.6 gitdb-4.0.12 h11-0.14.0 httpcore-1.0.7 httpx-0.28.1 huggingface-hub-0.29.3 identify-2.6.9 iniconfig-2.0.0 jiter-0.9.0 jmespath-1.0.1 markdown-it-py-3.0.0 mdurl-0.1.2 multidict-6.1.0 multiprocess-0.70.16 nodeenv-1.9.1 numpy-2.2.3 openai-1.66.3 pandas-2.2.3 pluggy-1.5.0 pre-commit-4.1.0 propcache-0.3.0 pyarrow-19.0.1 pydantic-2.10.6 pydantic-core-2.27.2 pygments-2.19.1 pytest-8.3.5 pytest-asyncio-0.25.3 python-dateutil-2.9.0.post0 python-dotenv-1.0.1 pytz-2025.1 pyyaml-6.0.2 requests-2.32.3 rich-13.9.4 s3transfer-0.11.4 six-1.17.0 smmap-5.0.2 sniffio-1.3.1 soupsieve-2.6 tqdm-4.67.1 typing-extensions-4.12.2 tzdata-2025.1 unidiff-0.7.5 virtualenv-20.29.3 xxhash-3.5.0 yarl-1.18.3

2025-03-15 00:35:31,631 - ThreadPoolExecutor-4_3 - INFO - Running the agent
2025-03-15 00:37:42,527 - ThreadPoolExecutor-4_3 - INFO - Container output: Using Amazon Bedrock with model us.anthropic.claude-3-5-sonnet-20241022-v2:0.

2025-03-15 00:37:42,529 - ThreadPoolExecutor-4_3 - INFO - Copying output files back to host
2025-03-15 00:37:42,619 - ThreadPoolExecutor-4_3 - INFO - Copying from container /guava/sphinx-doc__sphinx-7590.md to local path swe_bench/predictions/nerf_editwholefiles_med_0/sphinx-doc__sphinx-7590.md
2025-03-15 00:37:42,645 - ThreadPoolExecutor-4_3 - INFO - Successfully copied from container to swe_bench/predictions/nerf_editwholefiles_med_0/sphinx-doc__sphinx-7590.md
2025-03-15 00:37:42,684 - ThreadPoolExecutor-4_3 - INFO - Getting model_patch
2025-03-15 00:37:42,729 - ThreadPoolExecutor-4_3 - INFO - Container output: diff --git a/setup.py b/setup.py
index a427d5493..978e3d8e0 100644
--- a/setup.py
+++ b/setup.py
@@ -15,22 +15,22 @@ if sys.version_info < (3, 5):
     sys.exit(1)
 
 install_requires = [
-    'sphinxcontrib-applehelp',
-    'sphinxcontrib-devhelp',
+    'sphinxcontrib-applehelp<=1.0.7',
+    'sphinxcontrib-devhelp<=1.0.5',
     'sphinxcontrib-jsmath',
-    'sphinxcontrib-htmlhelp',
-    'sphinxcontrib-serializinghtml',
-    'sphinxcontrib-qthelp',
-    'Jinja2>=2.3',
+    'sphinxcontrib-htmlhelp<=2.0.4',
+    'sphinxcontrib-serializinghtml<=1.1.9',
+    'sphinxcontrib-qthelp<=1.0.6',
+    'Jinja2<3.0',
     'Pygments>=2.0',
     'docutils>=0.12',
     'snowballstemmer>=1.1',
     'babel>=1.3',
-    'alabaster>=0.7,<0.8',
+    'alabaster>=0.7,<0.7.12',
     'imagesize',
     'requests>=2.5.0',
     'setuptools',
-    'packaging',
+    'packaging', 'markupsafe<=2.0.1',
 ]
 
 extras_require = {
diff --git a/tox.ini b/tox.ini
index d9f040544..bf39854b6 100644
--- a/tox.ini
+++ b/tox.ini
@@ -28,7 +28,7 @@ extras =
 setenv =
     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils
 commands=
-    pytest --durations 25 {posargs}
+    pytest -rA --durations 25 {posargs}
 
 [testenv:flake8]
 basepython = python3

2025-03-15 00:37:42,776 - ThreadPoolExecutor-4_3 - INFO - Attempting to stop container sweb.eval.sphinx-doc__sphinx-7590.20250315_003503_881393...
2025-03-15 00:37:57,955 - ThreadPoolExecutor-4_3 - INFO - Attempting to remove container sweb.eval.sphinx-doc__sphinx-7590.20250315_003503_881393...
2025-03-15 00:37:58,251 - ThreadPoolExecutor-4_3 - INFO - Container sweb.eval.sphinx-doc__sphinx-7590.20250315_003503_881393 removed.
