#!/usr/bin/env python3
"""
Quick DGM Statistics Display
"""

import os
import glob
import json
import time
from datetime import datetime

def get_dgm_stats():
    """Get current DGM statistics."""
    print("🧬 **DARWIN GÖDEL MACHINE - LIVE STATS**")
    print("=" * 60)
    print(f"📅 Time: {datetime.now().strftime('%H:%M:%S')}")
    
    # Check if DGM is running
    try:
        import subprocess
        result = subprocess.run(['pgrep', '-f', 'DGM_outer.py'], 
                              capture_output=True, text=True)
        if result.stdout.strip():
            print(f"🟢 DGM Process: RUNNING (PID: {result.stdout.strip()})")
        else:
            print("🔴 DGM Process: NOT FOUND")
    except:
        print("❓ DGM Process: UNKNOWN")
    
    # Get run directories
    run_dirs = glob.glob("output_dgm/*/")
    if not run_dirs:
        print("❌ No DGM runs found")
        return
    
    latest_run = max(run_dirs, key=lambda x: os.path.getctime(x))
    run_name = os.path.basename(latest_run.rstrip('/'))
    print(f"📁 Current Run: {run_name}")
    
    # Count generations
    generations = glob.glob(f"{latest_run}/generation_*")
    generation_count = len(generations)
    print(f"🧬 Generations: {generation_count}/50 ({generation_count/50*100:.1f}%)")
    
    # Analyze patches
    patches = glob.glob(f"{latest_run}/*/model_patch.diff")
    patch_stats = []
    
    for patch in patches:
        try:
            with open(patch, 'r') as f:
                content = f.read()
            lines = len(content.splitlines())
            size = len(content)
            patch_stats.append({"file": patch, "lines": lines, "size": size})
        except:
            patch_stats.append({"file": patch, "lines": 0, "size": 0})
    
    total_patches = len(patch_stats)
    non_empty_patches = len([p for p in patch_stats if p['lines'] > 0])
    
    print(f"🔧 Patches: {non_empty_patches}/{total_patches} non-empty")
    
    if patch_stats:
        avg_lines = sum(p['lines'] for p in patch_stats) / total_patches
        max_lines = max(p['lines'] for p in patch_stats)
        total_size = sum(p['size'] for p in patch_stats)
        
        print(f"📊 Patch Stats: avg={avg_lines:.1f} lines, max={max_lines} lines")
        print(f"💾 Total Code: {total_size:,} bytes")
        
        # Show recent patches
        print("\n📝 Recent Patches:")
        recent_patches = sorted(patch_stats, key=lambda x: os.path.getctime(x['file']), reverse=True)[:5]
        
        for i, patch in enumerate(recent_patches):
            filename = os.path.basename(os.path.dirname(patch['file']))
            status = "✅" if patch['lines'] > 0 else "❌"
            print(f"   {status} {filename}: {patch['lines']} lines")
    
    # Check for evaluation results
    metadata_files = glob.glob(f"{latest_run}/*/metadata.json")
    if metadata_files:
        scores = []
        tasks = []
        
        for metadata_file in metadata_files:
            try:
                with open(metadata_file, 'r') as f:
                    data = json.load(f)
                score = data.get("score", 0)
                task = data.get("entry", "unknown")
                if isinstance(score, (int, float)):
                    scores.append(score)
                if task != "unknown":
                    tasks.append(task)
            except:
                continue
        
        if scores:
            avg_score = sum(scores) / len(scores)
            max_score = max(scores)
            print(f"\n📈 Evaluation: {len(scores)} completed, avg={avg_score:.3f}, max={max_score:.3f}")
        
        if tasks:
            unique_tasks = list(set(tasks))
            print(f"📋 Tasks: {', '.join(unique_tasks[:3])}")
            if len(unique_tasks) > 3:
                print(f"         ... and {len(unique_tasks) - 3} more")
    
    # Show latest activity
    print("\n🕐 Latest Activity:")
    try:
        latest_log = max(glob.glob("dgm_run*.log"), key=os.path.getctime)
        with open(latest_log, 'r') as f:
            lines = f.readlines()
        
        # Get last few lines
        for line in lines[-3:]:
            if line.strip():
                timestamp = line.split(' - ')[0] if ' - ' in line else ""
                message = line.split(' - ')[-1].strip() if ' - ' in line else line.strip()
                print(f"   {timestamp.split()[-1] if timestamp else ''}: {message[:60]}...")
    except:
        print("   No recent log activity found")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    get_dgm_stats()
