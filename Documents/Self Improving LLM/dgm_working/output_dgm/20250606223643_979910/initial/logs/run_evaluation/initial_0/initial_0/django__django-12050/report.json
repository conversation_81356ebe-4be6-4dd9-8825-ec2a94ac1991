{"django__django-12050": {"patch_is_None": false, "patch_exists": true, "patch_successfully_applied": true, "resolved": false, "tests_status": {"FAIL_TO_PASS": {"success": [], "failure": ["test_iterable_lookup_value (queries.test_query.TestQuery)"]}, "PASS_TO_PASS": {"success": [], "failure": ["test_clone_select_related (queries.test_query.TestQuery)", "test_complex_query (queries.test_query.TestQuery)", "test_foreign_key (queries.test_query.TestQuery)", "test_foreign_key_exclusive (queries.test_query.TestQuery)", "test_foreign_key_f (queries.test_query.TestQuery)", "test_multiple_fields (queries.test_query.TestQuery)", "test_negated_nullable (queries.test_query.TestQuery)", "test_simple_query (queries.test_query.TestQuery)", "test_simplecol_query (queries.test_query.TestQuery)", "test_transform (queries.test_query.TestQuery)"]}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}}}