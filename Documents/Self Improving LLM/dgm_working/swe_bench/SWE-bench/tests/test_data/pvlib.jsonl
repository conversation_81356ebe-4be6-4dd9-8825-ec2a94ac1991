{"url": "https://api.github.com/repos/pvlib/pvlib-python/pulls/1", "id": 29411065, "node_id": "MDExOlB1bGxSZXF1ZXN0Mjk0MTEwNjU=", "html_url": "https://github.com/pvlib/pvlib-python/pull/1", "diff_url": "https://github.com/pvlib/pvlib-python/pull/1.diff", "patch_url": "https://github.com/pvlib/pvlib-python/pull/1.patch", "issue_url": "https://api.github.com/repos/pvlib/pvlib-python/issues/1", "number": 1, "state": "closed", "locked": false, "title": "Update README.md", "user": {"login": "<PERSON><PERSON><PERSON>", "id": 4383303, "node_id": "MDQ6VXNlcjQzODMzMDM=", "avatar_url": "https://avatars.githubusercontent.com/u/4383303?v=4", "gravatar_id": "", "url": "https://api.github.com/users/wholmgren", "html_url": "https://github.com/wholm<PERSON>", "followers_url": "https://api.github.com/users/wholm<PERSON>/followers", "following_url": "https://api.github.com/users/wholm<PERSON>/following{/other_user}", "gists_url": "https://api.github.com/users/wholmgren/gists{/gist_id}", "starred_url": "https://api.github.com/users/who<PERSON><PERSON>/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/wholmgren/subscriptions", "organizations_url": "https://api.github.com/users/wholmgren/orgs", "repos_url": "https://api.github.com/users/wholmgren/repos", "events_url": "https://api.github.com/users/wholmgren/events{/privacy}", "received_events_url": "https://api.github.com/users/wholm<PERSON>/received_events", "type": "User", "site_admin": false}, "body": "Removed unneeded \\* in readme.\n", "created_at": "2015-02-17T01:01:06Z", "updated_at": "2015-03-12T07:08:37Z", "closed_at": "2015-02-17T01:02:03Z", "merged_at": "2015-02-17T01:02:03Z", "merge_commit_sha": "c8ea13cd3193897f760c9bbef6899a0cbcbd1a8d", "assignee": null, "assignees": [], "requested_reviewers": [], "requested_teams": [], "labels": [{"id": *********, "node_id": "MDU6TGFiZWwxODYwMTYxNDI=", "url": "https://api.github.com/repos/pvlib/pvlib-python/labels/documentation", "name": "documentation", "color": "bfe5bf", "default": true, "description": null}], "milestone": {"url": "https://api.github.com/repos/pvlib/pvlib-python/milestones/1", "html_url": "https://github.com/pvlib/pvlib-python/milestone/1", "labels_url": "https://api.github.com/repos/pvlib/pvlib-python/milestones/1/labels", "id": 1017829, "node_id": "MDk6TWlsZXN0b25lMTAxNzgyOQ==", "number": 1, "title": "0.1", "description": "First release on PyPi", "creator": {"login": "bmu", "id": 1622906, "node_id": "MDQ6VXNlcjE2MjI5MDY=", "avatar_url": "https://avatars.githubusercontent.com/u/1622906?v=4", "gravatar_id": "", "url": "https://api.github.com/users/bmu", "html_url": "https://github.com/bmu", "followers_url": "https://api.github.com/users/bmu/followers", "following_url": "https://api.github.com/users/bmu/following{/other_user}", "gists_url": "https://api.github.com/users/bmu/gists{/gist_id}", "starred_url": "https://api.github.com/users/bmu/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/bmu/subscriptions", "organizations_url": "https://api.github.com/users/bmu/orgs", "repos_url": "https://api.github.com/users/bmu/repos", "events_url": "https://api.github.com/users/bmu/events{/privacy}", "received_events_url": "https://api.github.com/users/bmu/received_events", "type": "User", "site_admin": false}, "open_issues": 0, "closed_issues": 32, "state": "closed", "created_at": "2015-03-12T06:35:09Z", "updated_at": "2023-05-25T15:38:31Z", "due_on": "2015-03-30T07:00:00Z", "closed_at": "2015-06-03T09:33:00Z"}, "draft": false, "commits_url": "https://api.github.com/repos/pvlib/pvlib-python/pulls/1/commits", "review_comments_url": "https://api.github.com/repos/pvlib/pvlib-python/pulls/1/comments", "review_comment_url": "https://api.github.com/repos/pvlib/pvlib-python/pulls/comments{/number}", "comments_url": "https://api.github.com/repos/pvlib/pvlib-python/issues/1/comments", "statuses_url": "https://api.github.com/repos/pvlib/pvlib-python/statuses/b6e190af35b8386093f6f02f4446cf6eacfc6b45", "head": {"label": "<PERSON><PERSON><PERSON>:master", "ref": "master", "sha": "b6e190af35b8386093f6f02f4446cf6eacfc6b45", "user": {"login": "<PERSON><PERSON><PERSON>", "id": 4383303, "node_id": "MDQ6VXNlcjQzODMzMDM=", "avatar_url": "https://avatars.githubusercontent.com/u/4383303?v=4", "gravatar_id": "", "url": "https://api.github.com/users/wholmgren", "html_url": "https://github.com/wholm<PERSON>", "followers_url": "https://api.github.com/users/wholm<PERSON>/followers", "following_url": "https://api.github.com/users/wholm<PERSON>/following{/other_user}", "gists_url": "https://api.github.com/users/wholmgren/gists{/gist_id}", "starred_url": "https://api.github.com/users/who<PERSON><PERSON>/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/wholmgren/subscriptions", "organizations_url": "https://api.github.com/users/wholmgren/orgs", "repos_url": "https://api.github.com/users/wholmgren/repos", "events_url": "https://api.github.com/users/wholmgren/events{/privacy}", "received_events_url": "https://api.github.com/users/wholm<PERSON>/received_events", "type": "User", "site_admin": false}, "repo": {"id": 30896535, "node_id": "MDEwOlJlcG9zaXRvcnkzMDg5NjUzNQ==", "name": "pvlib-python", "full_name": "wholmgren/pvlib-python", "private": false, "owner": {"login": "<PERSON><PERSON><PERSON>", "id": 4383303, "node_id": "MDQ6VXNlcjQzODMzMDM=", "avatar_url": "https://avatars.githubusercontent.com/u/4383303?v=4", "gravatar_id": "", "url": "https://api.github.com/users/wholmgren", "html_url": "https://github.com/wholm<PERSON>", "followers_url": "https://api.github.com/users/wholm<PERSON>/followers", "following_url": "https://api.github.com/users/wholm<PERSON>/following{/other_user}", "gists_url": "https://api.github.com/users/wholmgren/gists{/gist_id}", "starred_url": "https://api.github.com/users/who<PERSON><PERSON>/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/wholmgren/subscriptions", "organizations_url": "https://api.github.com/users/wholmgren/orgs", "repos_url": "https://api.github.com/users/wholmgren/repos", "events_url": "https://api.github.com/users/wholmgren/events{/privacy}", "received_events_url": "https://api.github.com/users/wholm<PERSON>/received_events", "type": "User", "site_admin": false}, "html_url": "https://github.com/wholmgren/pvlib-python", "description": "see https://github.com/pvlib/pvlib-python", "fork": true, "url": "https://api.github.com/repos/wholmgren/pvlib-python", "forks_url": "https://api.github.com/repos/wholmgren/pvlib-python/forks", "keys_url": "https://api.github.com/repos/wholm<PERSON>/pvlib-python/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/who<PERSON><PERSON>/pvlib-python/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/wholmgren/pvlib-python/teams", "hooks_url": "https://api.github.com/repos/wholmgren/pvlib-python/hooks", "issue_events_url": "https://api.github.com/repos/wholmgren/pvlib-python/issues/events{/number}", "events_url": "https://api.github.com/repos/wholmgren/pvlib-python/events", "assignees_url": "https://api.github.com/repos/wholmgren/pvlib-python/assignees{/user}", "branches_url": "https://api.github.com/repos/wholmgren/pvlib-python/branches{/branch}", "tags_url": "https://api.github.com/repos/wholmgren/pvlib-python/tags", "blobs_url": "https://api.github.com/repos/wholmgren/pvlib-python/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/wholmgren/pvlib-python/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/wholmgren/pvlib-python/git/refs{/sha}", "trees_url": "https://api.github.com/repos/wholmgren/pvlib-python/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/wholmgren/pvlib-python/statuses/{sha}", "languages_url": "https://api.github.com/repos/wholmgren/pvlib-python/languages", "stargazers_url": "https://api.github.com/repos/wholmgren/pvlib-python/stargazers", "contributors_url": "https://api.github.com/repos/wholm<PERSON>/pvlib-python/contributors", "subscribers_url": "https://api.github.com/repos/wholmgren/pvlib-python/subscribers", "subscription_url": "https://api.github.com/repos/wholmgren/pvlib-python/subscription", "commits_url": "https://api.github.com/repos/wholmgren/pvlib-python/commits{/sha}", "git_commits_url": "https://api.github.com/repos/wholmgren/pvlib-python/git/commits{/sha}", "comments_url": "https://api.github.com/repos/wholmgren/pvlib-python/comments{/number}", "issue_comment_url": "https://api.github.com/repos/wholm<PERSON>/pvlib-python/issues/comments{/number}", "contents_url": "https://api.github.com/repos/wholmgren/pvlib-python/contents/{+path}", "compare_url": "https://api.github.com/repos/wholmgren/pvlib-python/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/wholmgren/pvlib-python/merges", "archive_url": "https://api.github.com/repos/wholm<PERSON>/pvlib-python/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/wholmgren/pvlib-python/downloads", "issues_url": "https://api.github.com/repos/wholmgren/pvlib-python/issues{/number}", "pulls_url": "https://api.github.com/repos/wholmgren/pvlib-python/pulls{/number}", "milestones_url": "https://api.github.com/repos/wholmgren/pvlib-python/milestones{/number}", "notifications_url": "https://api.github.com/repos/wholmgren/pvlib-python/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/wholmgren/pvlib-python/labels{/name}", "releases_url": "https://api.github.com/repos/wholmgren/pvlib-python/releases{/id}", "deployments_url": "https://api.github.com/repos/wholmgren/pvlib-python/deployments", "created_at": "2015-02-17T00:55:18Z", "updated_at": "2022-01-21T20:25:16Z", "pushed_at": "2024-05-31T02:01:32Z", "git_url": "git://github.com/wholmgren/pvlib-python.git", "ssh_url": "**************:wholmgren/pvlib-python.git", "clone_url": "https://github.com/wholmgren/pvlib-python.git", "svn_url": "https://github.com/wholmgren/pvlib-python", "homepage": "", "size": 113209, "stargazers_count": 1, "watchers_count": 1, "language": "Python", "has_issues": false, "has_projects": true, "has_downloads": true, "has_wiki": true, "has_pages": false, "has_discussions": false, "forks_count": 0, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 0, "license": {"key": "bsd-3-clause", "name": "BSD 3-Clause \"New\" or \"Revised\" License", "spdx_id": "BSD-3-<PERSON><PERSON>", "url": "https://api.github.com/licenses/bsd-3-clause", "node_id": "MDc6TGljZW5zZTU="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": [], "visibility": "public", "forks": 0, "open_issues": 0, "watchers": 1, "default_branch": "master"}}, "base": {"label": "pvlib:master", "ref": "master", "sha": "e8dd1d9bdaff50319fde60397d704061290f19de", "user": {"login": "pvlib", "id": 11037261, "node_id": "MDEyOk9yZ2FuaXphdGlvbjExMDM3MjYx", "avatar_url": "https://avatars.githubusercontent.com/u/11037261?v=4", "gravatar_id": "", "url": "https://api.github.com/users/pvlib", "html_url": "https://github.com/pvlib", "followers_url": "https://api.github.com/users/pvlib/followers", "following_url": "https://api.github.com/users/pvlib/following{/other_user}", "gists_url": "https://api.github.com/users/pvlib/gists{/gist_id}", "starred_url": "https://api.github.com/users/pvlib/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/pvlib/subscriptions", "organizations_url": "https://api.github.com/users/pvlib/orgs", "repos_url": "https://api.github.com/users/pvlib/repos", "events_url": "https://api.github.com/users/pvlib/events{/privacy}", "received_events_url": "https://api.github.com/users/pvlib/received_events", "type": "Organization", "site_admin": false}, "repo": {"id": 30895522, "node_id": "MDEwOlJlcG9zaXRvcnkzMDg5NTUyMg==", "name": "pvlib-python", "full_name": "pvlib/pvlib-python", "private": false, "owner": {"login": "pvlib", "id": 11037261, "node_id": "MDEyOk9yZ2FuaXphdGlvbjExMDM3MjYx", "avatar_url": "https://avatars.githubusercontent.com/u/11037261?v=4", "gravatar_id": "", "url": "https://api.github.com/users/pvlib", "html_url": "https://github.com/pvlib", "followers_url": "https://api.github.com/users/pvlib/followers", "following_url": "https://api.github.com/users/pvlib/following{/other_user}", "gists_url": "https://api.github.com/users/pvlib/gists{/gist_id}", "starred_url": "https://api.github.com/users/pvlib/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/pvlib/subscriptions", "organizations_url": "https://api.github.com/users/pvlib/orgs", "repos_url": "https://api.github.com/users/pvlib/repos", "events_url": "https://api.github.com/users/pvlib/events{/privacy}", "received_events_url": "https://api.github.com/users/pvlib/received_events", "type": "Organization", "site_admin": false}, "html_url": "https://github.com/pvlib/pvlib-python", "description": "A set of documented functions for simulating the performance of photovoltaic energy systems.", "fork": false, "url": "https://api.github.com/repos/pvlib/pvlib-python", "forks_url": "https://api.github.com/repos/pvlib/pvlib-python/forks", "keys_url": "https://api.github.com/repos/pvlib/pvlib-python/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/pvlib/pvlib-python/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/pvlib/pvlib-python/teams", "hooks_url": "https://api.github.com/repos/pvlib/pvlib-python/hooks", "issue_events_url": "https://api.github.com/repos/pvlib/pvlib-python/issues/events{/number}", "events_url": "https://api.github.com/repos/pvlib/pvlib-python/events", "assignees_url": "https://api.github.com/repos/pvlib/pvlib-python/assignees{/user}", "branches_url": "https://api.github.com/repos/pvlib/pvlib-python/branches{/branch}", "tags_url": "https://api.github.com/repos/pvlib/pvlib-python/tags", "blobs_url": "https://api.github.com/repos/pvlib/pvlib-python/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/pvlib/pvlib-python/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/pvlib/pvlib-python/git/refs{/sha}", "trees_url": "https://api.github.com/repos/pvlib/pvlib-python/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/pvlib/pvlib-python/statuses/{sha}", "languages_url": "https://api.github.com/repos/pvlib/pvlib-python/languages", "stargazers_url": "https://api.github.com/repos/pvlib/pvlib-python/stargazers", "contributors_url": "https://api.github.com/repos/pvlib/pvlib-python/contributors", "subscribers_url": "https://api.github.com/repos/pvlib/pvlib-python/subscribers", "subscription_url": "https://api.github.com/repos/pvlib/pvlib-python/subscription", "commits_url": "https://api.github.com/repos/pvlib/pvlib-python/commits{/sha}", "git_commits_url": "https://api.github.com/repos/pvlib/pvlib-python/git/commits{/sha}", "comments_url": "https://api.github.com/repos/pvlib/pvlib-python/comments{/number}", "issue_comment_url": "https://api.github.com/repos/pvlib/pvlib-python/issues/comments{/number}", "contents_url": "https://api.github.com/repos/pvlib/pvlib-python/contents/{+path}", "compare_url": "https://api.github.com/repos/pvlib/pvlib-python/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/pvlib/pvlib-python/merges", "archive_url": "https://api.github.com/repos/pvlib/pvlib-python/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/pvlib/pvlib-python/downloads", "issues_url": "https://api.github.com/repos/pvlib/pvlib-python/issues{/number}", "pulls_url": "https://api.github.com/repos/pvlib/pvlib-python/pulls{/number}", "milestones_url": "https://api.github.com/repos/pvlib/pvlib-python/milestones{/number}", "notifications_url": "https://api.github.com/repos/pvlib/pvlib-python/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/pvlib/pvlib-python/labels{/name}", "releases_url": "https://api.github.com/repos/pvlib/pvlib-python/releases{/id}", "deployments_url": "https://api.github.com/repos/pvlib/pvlib-python/deployments", "created_at": "2015-02-17T00:21:33Z", "updated_at": "2024-07-03T19:58:46Z", "pushed_at": "2024-07-03T20:12:57Z", "git_url": "git://github.com/pvlib/pvlib-python.git", "ssh_url": "**************:pvlib/pvlib-python.git", "clone_url": "https://github.com/pvlib/pvlib-python.git", "svn_url": "https://github.com/pvlib/pvlib-python", "homepage": "https://pvlib-python.readthedocs.io", "size": 112782, "stargazers_count": 1120, "watchers_count": 1120, "language": "Python", "has_issues": true, "has_projects": false, "has_downloads": true, "has_wiki": true, "has_pages": false, "has_discussions": true, "forks_count": 949, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 229, "license": {"key": "bsd-3-clause", "name": "BSD 3-Clause \"New\" or \"Revised\" License", "spdx_id": "BSD-3-<PERSON><PERSON>", "url": "https://api.github.com/licenses/bsd-3-clause", "node_id": "MDc6TGljZW5zZTU="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": ["photovoltaic", "python", "renewable-energy", "renewables", "solar-energy"], "visibility": "public", "forks": 949, "open_issues": 229, "watchers": 1120, "default_branch": "main"}}, "_links": {"self": {"href": "https://api.github.com/repos/pvlib/pvlib-python/pulls/1"}, "html": {"href": "https://github.com/pvlib/pvlib-python/pull/1"}, "issue": {"href": "https://api.github.com/repos/pvlib/pvlib-python/issues/1"}, "comments": {"href": "https://api.github.com/repos/pvlib/pvlib-python/issues/1/comments"}, "review_comments": {"href": "https://api.github.com/repos/pvlib/pvlib-python/pulls/1/comments"}, "review_comment": {"href": "https://api.github.com/repos/pvlib/pvlib-python/pulls/comments{/number}"}, "commits": {"href": "https://api.github.com/repos/pvlib/pvlib-python/pulls/1/commits"}, "statuses": {"href": "https://api.github.com/repos/pvlib/pvlib-python/statuses/b6e190af35b8386093f6f02f4446cf6eacfc6b45"}}, "author_association": "MEMBER", "auto_merge": null, "active_lock_reason": null, "resolved_issues": []}