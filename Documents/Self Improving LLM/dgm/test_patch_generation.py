#!/usr/bin/env python3
"""
Test if DGM can now generate non-empty patches with the fixed tool usage.
"""

import os
import sys
import tempfile
import subprocess
from pathlib import Path

def test_patch_generation():
    """Test if the coding agent can generate a non-empty patch."""
    print("🧪 Testing patch generation with fixed tool usage...")
    
    try:
        from coding_agent import AgenticSystem
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Initialize git repo
            subprocess.run(['git', 'init'], cwd=temp_dir, check=True, capture_output=True)
            subprocess.run(['git', 'config', 'user.name', 'Test'], cwd=temp_dir, check=True, capture_output=True)
            subprocess.run(['git', 'config', 'user.email', '<EMAIL>'], cwd=temp_dir, check=True, capture_output=True)
            
            # Create a simple Python file with a bug
            test_file = Path(temp_dir) / "calculator.py"
            test_file.write_text("""def add(a, b):
    return a + b

def subtract(a, b):
    return a - b

def multiply(a, b):
    return a * b

def divide(a, b):
    # BUG: No check for division by zero
    return a / b

if __name__ == "__main__":
    print("Calculator ready!")
    print("5 + 3 =", add(5, 3))
    print("10 / 0 =", divide(10, 0))  # This will crash!
""")
            
            # Add and commit the file
            subprocess.run(['git', 'add', '.'], cwd=temp_dir, check=True, capture_output=True)
            result = subprocess.run(['git', 'commit', '-m', 'initial'], cwd=temp_dir, check=True, capture_output=True)
            
            # Get commit hash
            result = subprocess.run(['git', 'rev-parse', 'HEAD'], cwd=temp_dir, check=True, capture_output=True, text=True)
            commit_hash = result.stdout.strip()
            
            print(f"   Test directory: {temp_dir}")
            print(f"   Initial commit: {commit_hash}")
            print(f"   Test file content: {test_file.read_text()[:100]}...")
            
            # Create problem statement for self-improvement
            problem_statement = """The calculator.py file has a critical bug: it doesn't handle division by zero.
            
When someone calls divide(10, 0), the program crashes with a ZeroDivisionError.

Please fix this by:
1. Adding a check for division by zero in the divide() function
2. Return a meaningful error message or handle the case gracefully
3. Make sure the main section doesn't crash when testing division by zero

This is a self-improvement task to make the code more robust."""
            
            # Test AgenticSystem
            chat_history_file = os.path.join(temp_dir, "chat.md")
            agent = AgenticSystem(
                problem_statement=problem_statement,
                git_tempdir=temp_dir,
                base_commit=commit_hash,
                chat_history_file=chat_history_file,
                test_description="Run the calculator and make sure it doesn't crash on division by zero",
                self_improve=True
            )
            
            print("   Running AgenticSystem.forward()...")
            agent.forward()
            
            # Check if any changes were made
            result = subprocess.run(['git', 'diff', commit_hash], cwd=temp_dir, capture_output=True, text=True)
            patch_content = result.stdout
            
            print(f"   Generated patch length: {len(patch_content)} characters")
            
            if patch_content.strip():
                print("   ✅ Non-empty patch generated!")
                print("   Patch preview:")
                print("   " + "\n   ".join(patch_content.split('\n')[:10]))
                if len(patch_content.split('\n')) > 10:
                    print("   ...")
                
                # Check if the patch actually fixes the division by zero issue
                if 'zero' in patch_content.lower() or 'ZeroDivisionError' in patch_content:
                    print("   ✅ Patch appears to address the division by zero issue!")
                    return True
                else:
                    print("   ⚠️  Patch generated but may not address the specific issue.")
                    return True  # Still counts as success for patch generation
            else:
                print("   ❌ Empty patch generated.")
                return False
                
    except Exception as e:
        print(f"   ❌ Patch generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the patch generation test."""
    print("🧪 **PATCH GENERATION TEST**")
    print("=" * 50)
    
    # Check prerequisites
    if not os.getenv('OPENAI_API_KEY') and not os.getenv('ANTHROPIC_API_KEY'):
        print("❌ No API keys found. Please set OPENAI_API_KEY or ANTHROPIC_API_KEY.")
        return False
    
    # Run test
    success = test_patch_generation()
    
    if success:
        print("\n🎉 **SUCCESS!** DGM can now generate non-empty patches!")
        print("The tool usage fix has resolved the empty patch issue.")
        print("You can now run full DGM evolution experiments.")
    else:
        print("\n❌ **FAILED!** DGM is still generating empty patches.")
        print("Additional debugging may be needed.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
