# Darwin Gödel Machine - Working Version

This directory contains the **working, tested version** of the Darwin Gödel Machine (DGM) that has been fixed to work with local Ollama models.

## 🎯 What's Fixed

This version resolves the **empty patch problem** that was preventing the DGM from generating code changes:

- ✅ **Tool Usage Fixed**: qwen3:30b-a3b now correctly uses tools to modify files
- ✅ **Patch Generation**: The model generates non-empty patches with actual code changes
- ✅ **Self-Improvement**: The system can now iteratively improve its own code
- ✅ **Local Model Support**: Works with Ollama qwen3:30b-a3b (no external API required)

## 🚀 Quick Start

### Prerequisites
1. **Ollama** with qwen3:30b-a3b model installed
2. **Docker** for containerized evaluation
3. **Python 3.8+** with virtual environment

### Setup
```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Verify Ollama is running
ollama list | grep qwen3:30b-a3b
```

### Run DGM Evolution
```bash
# Start a DGM evolution run
python DGM_outer.py --max_generation 20 --selfimprove_size 2

# Monitor progress
tail -f output_dgm/*/dgm_outer.log
```

## 📁 Directory Structure

### Core Files
- `DGM_outer.py` - Main evolution loop
- `self_improve_step.py` - Single self-improvement step
- `coding_agent.py` - Agent that modifies code
- `llm.py` - LLM client interface
- `llm_withtools.py` - **FIXED** tool integration for qwen3:30b-a3b

### Supporting Directories
- `tools/` - Available tools (editor, bash)
- `prompts/` - System prompts for different tasks
- `utils/` - Utility functions
- `swe_bench/` - SWE-bench evaluation framework
- `initial/` - Initial evaluation results

## 🔧 Key Fixes Applied

### 1. Tool Call Parsing Fix
**Problem**: qwen3:30b-a3b was treated as OpenAI-compatible but outputs manual tool format.

**Solution**: Modified `check_for_tool_use()` in `llm_withtools.py` to properly parse `<tool_use>` tags.

### 2. Exception Handling Fix
**Problem**: Silent failures in tool execution were ignored.

**Solution**: Added proper error logging to debug tool execution issues.

## 🧪 Testing

The system has been tested with:
- ✅ Tool usage verification
- ✅ Patch generation testing  
- ✅ File modification confirmation
- ✅ End-to-end workflow validation

## 📊 Expected Behavior

### Before Fix
- Empty `model_patch.diff` files (0 lines)
- No actual code changes
- Silent tool execution failures

### After Fix
- Non-empty patches with actual code changes
- Successful tool execution with feedback
- Iterative self-improvement working

## 🎛️ Configuration

### Model Settings
- **Diagnosis Model**: qwen3:30b-a3b (local)
- **Coding Model**: qwen3:30b-a3b (local)
- **Tool Format**: Manual (`<tool_use>` tags)

### Evolution Parameters
- `--max_generation`: Number of evolution iterations (default: 80)
- `--selfimprove_size`: Attempts per generation (default: 2)
- `--selfimprove_workers`: Parallel workers (default: 2)

## 🚨 Important Notes

1. **Local Only**: This version uses qwen3:30b-a3b for everything (no external APIs)
2. **Self-Improvement**: The model improves its own code - this is the core concept
3. **Resource Usage**: Monitor CPU/memory usage during long runs
4. **Patience Required**: Each iteration can take several minutes

## 📈 Monitoring Progress

Check for successful evolution:
```bash
# Count non-empty patches
find output_dgm -name "model_patch.diff" -exec wc -l {} \; | grep -v "^0"

# View recent improvements
ls -la output_dgm/*/model_patch.diff
```

## 🎉 Success Indicators

- Non-zero line counts in `model_patch.diff` files
- Tool execution logs showing successful file modifications
- Gradual improvement in evaluation scores over generations

---

**This is the working version that successfully enables qwen3:30b-a3b to improve itself!** 🚀
