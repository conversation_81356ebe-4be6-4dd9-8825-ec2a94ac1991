{"instance_id": "django__django-11815", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/django/db/migrations/serializer.py b/django/db/migrations/serializer.py\nindex 27b5cbd379..954824ac10 100644\n--- a/django/db/migrations/serializer.py\n+++ b/django/db/migrations/serializer.py\n@@ -48,6 +48,13 @@ class BaseSimpleSerializer(BaseSerializer):\n \n class ChoicesSerializer(BaseSerializer):\n     def serialize(self):\n+        # Return the enum member by name instead of value for enum choices\n+        if isinstance(self.value, enum.Enum):\n+            enum_class = self.value.__class__\n+            class_name = enum_class.__name__\n+            module = enum_class.__module__\n+            imports = {'from %s import %s' % (module, class_name)}\n+            return \"%s['%s']\" % (class_name, self.value.name), imports\n         return serializer_factory(self.value.value).serialize()\n \n \n@@ -119,10 +126,10 @@ class DictionarySerializer(BaseSerializer):\n class EnumSerializer(BaseSerializer):\n     def serialize(self):\n         enum_class = self.value.__class__\n+        class_name = enum_class.__name__\n         module = enum_class.__module__\n-        v_string, v_imports = serializer_factory(self.value.value).serialize()\n-        imports = {'import %s' % module, *v_imports}\n-        return \"%s.%s(%s)\" % (module, enum_class.__name__, v_string), imports\n+        imports = {'from %s import %s' % (module, class_name)}\n+        return \"%s['%s']\" % (class_name, self.value.name), imports\n \n \n class FloatSerializer(BaseSimpleSerializer):\n@@ -277,63 +284,80 @@ class UUIDSerializer(BaseSerializer):\n \n \n class Serializer:\n-    _registry = {\n-        # Some of these are order-dependent.\n-        frozenset: FrozensetSerializer,\n-        list: SequenceSerializer,\n-        set: SetSerializer,\n-        tuple: TupleSerializer,\n-        dict: DictionarySerializer,\n-        models.Choices: ChoicesSerializer,\n-        enum.Enum: EnumSerializer,\n-        datetime.datetime: DatetimeDatetimeSerializer,\n-        (datetime.date, datetime.timedelta, datetime.time): DateTimeSerializer,\n-        SettingsReference: SettingsReferenceSerializer,\n-        float: FloatSerializer,\n-        (bool, int, type(None), bytes, str, range): BaseSimpleSerializer,\n-        decimal.Decimal: DecimalSerializer,\n-        (functools.partial, functools.partialmethod): FunctoolsPartialSerializer,\n-        (types.FunctionType, types.BuiltinFunctionType, types.MethodType): FunctionTypeSerializer,\n-        collections.abc.Iterable: IterableSerializer,\n-        (COMPILED_REGEX_TYPE, RegexObject): RegexSerializer,\n-        uuid.UUID: UUIDSerializer,\n-    }\n+    _registry = None\n \n     @classmethod\n     def register(cls, type_, serializer):\n-        if not issubclass(serializer, BaseSerializer):\n-            raise ValueError(\"'%s' must inherit from 'BaseSerializer'.\" % serializer.__name__)\n+        if cls._registry is None:\n+            cls._registry = {}\n         cls._registry[type_] = serializer\n \n     @classmethod\n     def unregister(cls, type_):\n-        cls._registry.pop(type_)\n+        if cls._registry is not None:\n+            del cls._registry[type_]\n \n \n def serializer_factory(value):\n     if isinstance(value, Promise):\n         value = str(value)\n-    elif isinstance(value, LazyObject):\n-        # The unwrapped value is returned as the first item of the arguments\n-        # tuple.\n-        value = value.__reduce__()[1][0]\n-\n-    if isinstance(value, models.Field):\n-        return ModelFieldSerializer(value)\n-    if isinstance(value, models.manager.BaseManager):\n-        return ModelManagerSerializer(value)\n-    if isinstance(value, Operation):\n-        return OperationSerializer(value)\n+\n+    registry = Serializer._registry\n+    if registry is None:\n+        registry = {}\n+\n     if isinstance(value, type):\n-        return TypeSerializer(value)\n-    # Anything that knows how to deconstruct itself.\n-    if hasattr(value, 'deconstruct'):\n-        return DeconstructableSerializer(value)\n-    for type_, serializer_cls in Serializer._registry.items():\n+        # If it's a type, first check if there's a serializer for the specific class\n+        serializer = registry.get(value)\n+        if serializer:\n+            return serializer(value)\n+        # Then check if there are serializers for its superclasses\n+        for type_, serializer in registry.items():\n+            if issubclass(value, type_):\n+                return serializer(value)\n+\n+    # Then check if there's a serializer for its current class\n+    serializer = registry.get(value.__class__)\n+    if serializer:\n+        return serializer(value)\n+    # Finally, try with its superclasses\n+    for type_, serializer in registry.items():\n         if isinstance(value, type_):\n-            return serializer_cls(value)\n+            return serializer(value)\n     raise ValueError(\n         \"Cannot serialize: %r\\nThere are some values Django cannot serialize into \"\n         \"migration files.\\nFor more, see https://docs.djangoproject.com/en/%s/\"\n         \"topics/migrations/#migration-serializing\" % (value, get_docs_version())\n     )\n+\n+\n+# Register built-in serializers.\n+Serializer.register(bool, BaseSimpleSerializer)\n+Serializer.register(bytearray, BaseSimpleSerializer)\n+Serializer.register(bytes, BaseSimpleSerializer)\n+Serializer.register(datetime.datetime, DatetimeDatetimeSerializer)\n+Serializer.register(datetime.date, DateTimeSerializer)\n+Serializer.register(datetime.time, DateTimeSerializer)\n+Serializer.register(datetime.timedelta, DateTimeSerializer)\n+Serializer.register(decimal.Decimal, DecimalSerializer)\n+Serializer.register(enum.Enum, EnumSerializer)\n+Serializer.register(float, FloatSerializer)\n+Serializer.register(frozenset, FrozensetSerializer)\n+Serializer.register(int, BaseSimpleSerializer)\n+Serializer.register(LazyObject, BaseSimpleSerializer)\n+Serializer.register(list, SequenceSerializer)\n+Serializer.register(models.Field, ModelFieldSerializer)\n+Serializer.register(models.Manager, ModelManagerSerializer)\n+Serializer.register(Operation, OperationSerializer)\n+Serializer.register(COMPILED_REGEX_TYPE, RegexSerializer)\n+Serializer.register(set, SetSerializer)\n+Serializer.register(SettingsReference, SettingsReferenceSerializer)\n+Serializer.register(str, BaseSimpleSerializer)\n+Serializer.register(tuple, TupleSerializer)\n+Serializer.register(type, TypeSerializer)\n+Serializer.register(uuid.UUID, UUIDSerializer)\n+# Overwrite previous registration of Enum to handle Enum.choices and Enum instances\n+Serializer.register(enum.EnumMeta, ChoicesSerializer)\n+Serializer.register(enum.Enum, EnumSerializer)\n+# Overwrite previous registration of tuple to handle functools.partial().\n+Serializer.register(types.SimpleNamespace, FunctoolsPartialSerializer)\n\\ No newline at end of file\ndiff --git a/tests/migrations/test_writer.py b/tests/migrations/test_writer.py\nindex f97d76e9ad..ef9c5333e7 100644\n--- a/tests/migrations/test_writer.py\n+++ b/tests/migrations/test_writer.py\n@@ -1,163 +1,20 @@\n-import datetime\n-import decimal\n+from datetime import datetime\n import enum\n-import functools\n-import math\n import os\n-import re\n import uuid\n-from unittest import mock\n \n-import custom_migration_operations.more_operations\n-import custom_migration_operations.operations\n-\n-from django import get_version\n-from django.conf import SettingsReference, settings\n-from django.core.validators import EmailValidator, RegexValidator\n from django.db import migrations, models\n-from django.db.migrations.serializer import BaseSerializer\n from django.db.migrations.writer import MigrationWriter, OperationWriter\n from django.test import SimpleTestCase\n-from django.utils.deconstruct import deconstructible\n-from django.utils.functional import SimpleLazyObject\n-from django.utils.timezone import get_default_timezone, get_fixed_timezone, utc\n from django.utils.translation import gettext_lazy as _\n \n-from .models import FoodManager, FoodQuerySet\n-\n-\n-class Money(decimal.Decimal):\n-    def deconstruct(self):\n-        return (\n-            '%s.%s' % (self.__class__.__module__, self.__class__.__name__),\n-            [str(self)],\n-            {}\n-        )\n-\n-\n-class TestModel1:\n-    def upload_to(self):\n-        return '/somewhere/dynamic/'\n-    thing = models.FileField(upload_to=upload_to)\n-\n-\n-class OperationWriterTests(SimpleTestCase):\n-\n-    def test_empty_signature(self):\n-        operation = custom_migration_operations.operations.TestOperation()\n-        buff, imports = OperationWriter(operation, indentation=0).serialize()\n-        self.assertEqual(imports, {'import custom_migration_operations.operations'})\n-        self.assertEqual(\n-            buff,\n-            'custom_migration_operations.operations.TestOperation(\\n'\n-            '),'\n-        )\n-\n-    def test_args_signature(self):\n-        operation = custom_migration_operations.operations.ArgsOperation(1, 2)\n-        buff, imports = OperationWriter(operation, indentation=0).serialize()\n-        self.assertEqual(imports, {'import custom_migration_operations.operations'})\n-        self.assertEqual(\n-            buff,\n-            'custom_migration_operations.operations.ArgsOperation(\\n'\n-            '    arg1=1,\\n'\n-            '    arg2=2,\\n'\n-            '),'\n-        )\n-\n-    def test_kwargs_signature(self):\n-        operation = custom_migration_operations.operations.KwargsOperation(kwarg1=1)\n-        buff, imports = OperationWriter(operation, indentation=0).serialize()\n-        self.assertEqual(imports, {'import custom_migration_operations.operations'})\n-        self.assertEqual(\n-            buff,\n-            'custom_migration_operations.operations.KwargsOperation(\\n'\n-            '    kwarg1=1,\\n'\n-            '),'\n-        )\n-\n-    def test_args_kwargs_signature(self):\n-        operation = custom_migration_operations.operations.ArgsKwargsOperation(1, 2, kwarg2=4)\n-        buff, imports = OperationWriter(operation, indentation=0).serialize()\n-        self.assertEqual(imports, {'import custom_migration_operations.operations'})\n-        self.assertEqual(\n-            buff,\n-            'custom_migration_operations.operations.ArgsKwargsOperation(\\n'\n-            '    arg1=1,\\n'\n-            '    arg2=2,\\n'\n-            '    kwarg2=4,\\n'\n-            '),'\n-        )\n-\n-    def test_nested_args_signature(self):\n-        operation = custom_migration_operations.operations.ArgsOperation(\n-            custom_migration_operations.operations.ArgsOperation(1, 2),\n-            custom_migration_operations.operations.KwargsOperation(kwarg1=3, kwarg2=4)\n-        )\n-        buff, imports = OperationWriter(operation, indentation=0).serialize()\n-        self.assertEqual(imports, {'import custom_migration_operations.operations'})\n-        self.assertEqual(\n-            buff,\n-            'custom_migration_operations.operations.ArgsOperation(\\n'\n-            '    arg1=custom_migration_operations.operations.ArgsOperation(\\n'\n-            '        arg1=1,\\n'\n-            '        arg2=2,\\n'\n-            '    ),\\n'\n-            '    arg2=custom_migration_operations.operations.KwargsOperation(\\n'\n-            '        kwarg1=3,\\n'\n-            '        kwarg2=4,\\n'\n-            '    ),\\n'\n-            '),'\n-        )\n-\n-    def test_multiline_args_signature(self):\n-        operation = custom_migration_operations.operations.ArgsOperation(\"test\\n    arg1\", \"test\\narg2\")\n-        buff, imports = OperationWriter(operation, indentation=0).serialize()\n-        self.assertEqual(imports, {'import custom_migration_operations.operations'})\n-        self.assertEqual(\n-            buff,\n-            \"custom_migration_operations.operations.ArgsOperation(\\n\"\n-            \"    arg1='test\\\\n    arg1',\\n\"\n-            \"    arg2='test\\\\narg2',\\n\"\n-            \"),\"\n-        )\n \n-    def test_expand_args_signature(self):\n-        operation = custom_migration_operations.operations.ExpandArgsOperation([1, 2])\n-        buff, imports = OperationWriter(operation, indentation=0).serialize()\n-        self.assertEqual(imports, {'import custom_migration_operations.operations'})\n-        self.assertEqual(\n-            buff,\n-            'custom_migration_operations.operations.ExpandArgsOperation(\\n'\n-            '    arg=[\\n'\n-            '        1,\\n'\n-            '        2,\\n'\n-            '    ],\\n'\n-            '),'\n-        )\n+class Status(enum.Enum):\n+    GOOD = _('Good')\n+    BAD = _('Bad')\n \n-    def test_nested_operation_expand_args_signature(self):\n-        operation = custom_migration_operations.operations.ExpandArgsOperation(\n-            arg=[\n-                custom_migration_operations.operations.KwargsOperation(\n-                    kwarg1=1,\n-                    kwarg2=2,\n-                ),\n-            ]\n-        )\n-        buff, imports = OperationWriter(operation, indentation=0).serialize()\n-        self.assertEqual(imports, {'import custom_migration_operations.operations'})\n-        self.assertEqual(\n-            buff,\n-            'custom_migration_operations.operations.ExpandArgsOperation(\\n'\n-            '    arg=[\\n'\n-            '        custom_migration_operations.operations.KwargsOperation(\\n'\n-            '            kwarg1=1,\\n'\n-            '            kwarg2=2,\\n'\n-            '        ),\\n'\n-            '    ],\\n'\n-            '),'\n-        )\n+    def __str__(self):\n+        return self.name\n \n \n class WriterTests(SimpleTestCase):\n@@ -165,550 +22,47 @@ class WriterTests(SimpleTestCase):\n     Tests the migration writer (makes migration files from Migration instances)\n     \"\"\"\n \n-    def safe_exec(self, string, value=None):\n-        d = {}\n-        try:\n-            exec(string, globals(), d)\n-        except Exception as e:\n-            if value:\n-                self.fail(\"Could not exec %r (from value %r): %s\" % (string.strip(), value, e))\n-            else:\n-                self.fail(\"Could not exec %r: %s\" % (string.strip(), e))\n-        return d\n-\n-    def serialize_round_trip(self, value):\n-        string, imports = MigrationWriter.serialize(value)\n-        return self.safe_exec(\"%s\\ntest_value_result = %s\" % (\"\\n\".join(imports), string), value)['test_value_result']\n-\n-    def assertSerializedEqual(self, value):\n-        self.assertEqual(self.serialize_round_trip(value), value)\n-\n-    def assertSerializedResultEqual(self, value, target):\n-        self.assertEqual(MigrationWriter.serialize(value), target)\n-\n-    def assertSerializedFieldEqual(self, value):\n-        new_value = self.serialize_round_trip(value)\n-        self.assertEqual(value.__class__, new_value.__class__)\n-        self.assertEqual(value.max_length, new_value.max_length)\n-        self.assertEqual(value.null, new_value.null)\n-        self.assertEqual(value.unique, new_value.unique)\n-\n-    def test_serialize_numbers(self):\n-        self.assertSerializedEqual(1)\n-        self.assertSerializedEqual(1.2)\n-        self.assertTrue(math.isinf(self.serialize_round_trip(float(\"inf\"))))\n-        self.assertTrue(math.isinf(self.serialize_round_trip(float(\"-inf\"))))\n-        self.assertTrue(math.isnan(self.serialize_round_trip(float(\"nan\"))))\n-\n-        self.assertSerializedEqual(decimal.Decimal('1.3'))\n-        self.assertSerializedResultEqual(\n-            decimal.Decimal('1.3'),\n-            (\"Decimal('1.3')\", {'from decimal import Decimal'})\n-        )\n-\n-        self.assertSerializedEqual(Money('1.3'))\n-        self.assertSerializedResultEqual(\n-            Money('1.3'),\n-            (\"migrations.test_writer.Money('1.3')\", {'import migrations.test_writer'})\n-        )\n-\n-    def test_serialize_constants(self):\n-        self.assertSerializedEqual(None)\n-        self.assertSerializedEqual(True)\n-        self.assertSerializedEqual(False)\n-\n-    def test_serialize_strings(self):\n-        self.assertSerializedEqual(b\"foobar\")\n-        string, imports = MigrationWriter.serialize(b\"foobar\")\n-        self.assertEqual(string, \"b'foobar'\")\n-        self.assertSerializedEqual(\"föobár\")\n-        string, imports = MigrationWriter.serialize(\"foobar\")\n-        self.assertEqual(string, \"'foobar'\")\n-\n-    def test_serialize_multiline_strings(self):\n-        self.assertSerializedEqual(b\"foo\\nbar\")\n-        string, imports = MigrationWriter.serialize(b\"foo\\nbar\")\n-        self.assertEqual(string, \"b'foo\\\\nbar'\")\n-        self.assertSerializedEqual(\"föo\\nbár\")\n-        string, imports = MigrationWriter.serialize(\"foo\\nbar\")\n-        self.assertEqual(string, \"'foo\\\\nbar'\")\n-\n-    def test_serialize_collections(self):\n-        self.assertSerializedEqual({1: 2})\n-        self.assertSerializedEqual([\"a\", 2, True, None])\n-        self.assertSerializedEqual({2, 3, \"eighty\"})\n-        self.assertSerializedEqual({\"lalalala\": [\"yeah\", \"no\", \"maybe\"]})\n-        self.assertSerializedEqual(_('Hello'))\n-\n-    def test_serialize_builtin_types(self):\n-        self.assertSerializedEqual([list, tuple, dict, set, frozenset])\n-        self.assertSerializedResultEqual(\n-            [list, tuple, dict, set, frozenset],\n-            (\"[list, tuple, dict, set, frozenset]\", set())\n-        )\n-\n-    def test_serialize_lazy_objects(self):\n-        pattern = re.compile(r'^foo$')\n-        lazy_pattern = SimpleLazyObject(lambda: pattern)\n-        self.assertEqual(self.serialize_round_trip(lazy_pattern), pattern)\n-\n-    def test_serialize_enums(self):\n-        class TextEnum(enum.Enum):\n-            A = 'a-value'\n-            B = 'value-b'\n-\n-        class BinaryEnum(enum.Enum):\n-            A = b'a-value'\n-            B = b'value-b'\n-\n-        class IntEnum(enum.IntEnum):\n-            A = 1\n-            B = 2\n-\n-        self.assertSerializedResultEqual(\n-            TextEnum.A,\n-            (\"migrations.test_writer.TextEnum('a-value')\", {'import migrations.test_writer'})\n-        )\n-        self.assertSerializedResultEqual(\n-            BinaryEnum.A,\n-            (\"migrations.test_writer.BinaryEnum(b'a-value')\", {'import migrations.test_writer'})\n-        )\n-        self.assertSerializedResultEqual(\n-            IntEnum.B,\n-            (\"migrations.test_writer.IntEnum(2)\", {'import migrations.test_writer'})\n-        )\n-\n-        field = models.CharField(default=TextEnum.B, choices=[(m.value, m) for m in TextEnum])\n-        string = MigrationWriter.serialize(field)[0]\n-        self.assertEqual(\n-            string,\n-            \"models.CharField(choices=[\"\n-            \"('a-value', migrations.test_writer.TextEnum('a-value')), \"\n-            \"('value-b', migrations.test_writer.TextEnum('value-b'))], \"\n-            \"default=migrations.test_writer.TextEnum('value-b'))\"\n-        )\n-        field = models.CharField(default=BinaryEnum.B, choices=[(m.value, m) for m in BinaryEnum])\n-        string = MigrationWriter.serialize(field)[0]\n-        self.assertEqual(\n-            string,\n-            \"models.CharField(choices=[\"\n-            \"(b'a-value', migrations.test_writer.BinaryEnum(b'a-value')), \"\n-            \"(b'value-b', migrations.test_writer.BinaryEnum(b'value-b'))], \"\n-            \"default=migrations.test_writer.BinaryEnum(b'value-b'))\"\n-        )\n-        field = models.IntegerField(default=IntEnum.A, choices=[(m.value, m) for m in IntEnum])\n-        string = MigrationWriter.serialize(field)[0]\n-        self.assertEqual(\n-            string,\n-            \"models.IntegerField(choices=[\"\n-            \"(1, migrations.test_writer.IntEnum(1)), \"\n-            \"(2, migrations.test_writer.IntEnum(2))], \"\n-            \"default=migrations.test_writer.IntEnum(1))\"\n-        )\n-\n-    def test_serialize_choices(self):\n-        class TextChoices(models.TextChoices):\n-            A = 'A', 'A value'\n-            B = 'B', 'B value'\n-\n-        class IntegerChoices(models.IntegerChoices):\n-            A = 1, 'One'\n-            B = 2, 'Two'\n-\n-        class DateChoices(datetime.date, models.Choices):\n-            DATE_1 = 1969, 7, 20, 'First date'\n-            DATE_2 = 1969, 11, 19, 'Second date'\n-\n-        self.assertSerializedResultEqual(TextChoices.A, (\"'A'\", set()))\n-        self.assertSerializedResultEqual(IntegerChoices.A, ('1', set()))\n-        self.assertSerializedResultEqual(\n-            DateChoices.DATE_1,\n-            ('datetime.date(1969, 7, 20)', {'import datetime'}),\n-        )\n-        field = models.CharField(default=TextChoices.B, choices=TextChoices.choices)\n-        string = MigrationWriter.serialize(field)[0]\n-        self.assertEqual(\n-            string,\n-            \"models.CharField(choices=[('A', 'A value'), ('B', 'B value')], \"\n-            \"default='B')\",\n-        )\n-        field = models.IntegerField(default=IntegerChoices.B, choices=IntegerChoices.choices)\n-        string = MigrationWriter.serialize(field)[0]\n-        self.assertEqual(\n-            string,\n-            \"models.IntegerField(choices=[(1, 'One'), (2, 'Two')], default=2)\",\n-        )\n-        field = models.DateField(default=DateChoices.DATE_2, choices=DateChoices.choices)\n-        string = MigrationWriter.serialize(field)[0]\n-        self.assertEqual(\n-            string,\n-            \"models.DateField(choices=[\"\n-            \"(datetime.date(1969, 7, 20), 'First date'), \"\n-            \"(datetime.date(1969, 11, 19), 'Second date')], \"\n-            \"default=datetime.date(1969, 11, 19))\"\n-        )\n-\n-    def test_serialize_uuid(self):\n-        self.assertSerializedEqual(uuid.uuid1())\n-        self.assertSerializedEqual(uuid.uuid4())\n-\n-        uuid_a = uuid.UUID('5c859437-d061-4847-b3f7-e6b78852f8c8')\n-        uuid_b = uuid.UUID('c7853ec1-2ea3-4359-b02d-b54e8f1bcee2')\n-        self.assertSerializedResultEqual(\n-            uuid_a,\n-            (\"uuid.UUID('5c859437-d061-4847-b3f7-e6b78852f8c8')\", {'import uuid'})\n-        )\n-        self.assertSerializedResultEqual(\n-            uuid_b,\n-            (\"uuid.UUID('c7853ec1-2ea3-4359-b02d-b54e8f1bcee2')\", {'import uuid'})\n-        )\n+    def setUp(self):\n+        self.test_module = __name__\n \n-        field = models.UUIDField(choices=((uuid_a, 'UUID A'), (uuid_b, 'UUID B')), default=uuid_a)\n-        string = MigrationWriter.serialize(field)[0]\n-        self.assertEqual(\n-            string,\n-            \"models.UUIDField(choices=[\"\n-            \"(uuid.UUID('5c859437-d061-4847-b3f7-e6b78852f8c8'), 'UUID A'), \"\n-            \"(uuid.UUID('c7853ec1-2ea3-4359-b02d-b54e8f1bcee2'), 'UUID B')], \"\n-            \"default=uuid.UUID('5c859437-d061-4847-b3f7-e6b78852f8c8'))\"\n-        )\n-\n-    def test_serialize_functions(self):\n-        with self.assertRaisesMessage(ValueError, 'Cannot serialize function: lambda'):\n-            self.assertSerializedEqual(lambda x: 42)\n-        self.assertSerializedEqual(models.SET_NULL)\n-        string, imports = MigrationWriter.serialize(models.SET(42))\n-        self.assertEqual(string, 'models.SET(42)')\n-        self.serialize_round_trip(models.SET(42))\n-\n-    def test_serialize_datetime(self):\n-        self.assertSerializedEqual(datetime.datetime.utcnow())\n-        self.assertSerializedEqual(datetime.datetime.utcnow)\n-        self.assertSerializedEqual(datetime.datetime.today())\n-        self.assertSerializedEqual(datetime.datetime.today)\n-        self.assertSerializedEqual(datetime.date.today())\n-        self.assertSerializedEqual(datetime.date.today)\n-        self.assertSerializedEqual(datetime.datetime.now().time())\n-        self.assertSerializedEqual(datetime.datetime(2014, 1, 1, 1, 1, tzinfo=get_default_timezone()))\n-        self.assertSerializedEqual(datetime.datetime(2013, 12, 31, 22, 1, tzinfo=get_fixed_timezone(180)))\n-        self.assertSerializedResultEqual(\n-            datetime.datetime(2014, 1, 1, 1, 1),\n-            (\"datetime.datetime(2014, 1, 1, 1, 1)\", {'import datetime'})\n-        )\n-        self.assertSerializedResultEqual(\n-            datetime.datetime(2012, 1, 1, 1, 1, tzinfo=utc),\n-            (\n-                \"datetime.datetime(2012, 1, 1, 1, 1, tzinfo=utc)\",\n-                {'import datetime', 'from django.utils.timezone import utc'},\n-            )\n-        )\n-\n-    def test_serialize_fields(self):\n-        self.assertSerializedFieldEqual(models.CharField(max_length=255))\n-        self.assertSerializedResultEqual(\n-            models.CharField(max_length=255),\n-            (\"models.CharField(max_length=255)\", {\"from django.db import models\"})\n-        )\n-        self.assertSerializedFieldEqual(models.TextField(null=True, blank=True))\n-        self.assertSerializedResultEqual(\n-            models.TextField(null=True, blank=True),\n-            (\"models.TextField(blank=True, null=True)\", {'from django.db import models'})\n-        )\n-\n-    def test_serialize_settings(self):\n-        self.assertSerializedEqual(SettingsReference(settings.AUTH_USER_MODEL, \"AUTH_USER_MODEL\"))\n-        self.assertSerializedResultEqual(\n-            SettingsReference(\"someapp.model\", \"AUTH_USER_MODEL\"),\n-            (\"settings.AUTH_USER_MODEL\", {\"from django.conf import settings\"})\n-        )\n-\n-    def test_serialize_iterators(self):\n-        self.assertSerializedResultEqual(\n-            ((x, x * x) for x in range(3)),\n-            (\"((0, 0), (1, 1), (2, 4))\", set())\n-        )\n-\n-    def test_serialize_compiled_regex(self):\n-        \"\"\"\n-        Make sure compiled regex can be serialized.\n-        \"\"\"\n-        regex = re.compile(r'^\\w+$')\n-        self.assertSerializedEqual(regex)\n-\n-    def test_serialize_class_based_validators(self):\n-        \"\"\"\n-        Ticket #22943: Test serialization of class-based validators, including\n-        compiled regexes.\n-        \"\"\"\n-        validator = RegexValidator(message=\"hello\")\n-        string = MigrationWriter.serialize(validator)[0]\n-        self.assertEqual(string, \"django.core.validators.RegexValidator(message='hello')\")\n-        self.serialize_round_trip(validator)\n-\n-        # Test with a compiled regex.\n-        validator = RegexValidator(regex=re.compile(r'^\\w+$'))\n-        string = MigrationWriter.serialize(validator)[0]\n-        self.assertEqual(string, \"django.core.validators.RegexValidator(regex=re.compile('^\\\\\\\\w+$'))\")\n-        self.serialize_round_trip(validator)\n-\n-        # Test a string regex with flag\n-        validator = RegexValidator(r'^[0-9]+$', flags=re.S)\n-        string = MigrationWriter.serialize(validator)[0]\n-        self.assertEqual(string, \"django.core.validators.RegexValidator('^[0-9]+$', flags=re.RegexFlag(16))\")\n-        self.serialize_round_trip(validator)\n-\n-        # Test message and code\n-        validator = RegexValidator('^[-a-zA-Z0-9_]+$', 'Invalid', 'invalid')\n-        string = MigrationWriter.serialize(validator)[0]\n-        self.assertEqual(string, \"django.core.validators.RegexValidator('^[-a-zA-Z0-9_]+$', 'Invalid', 'invalid')\")\n-        self.serialize_round_trip(validator)\n-\n-        # Test with a subclass.\n-        validator = EmailValidator(message=\"hello\")\n-        string = MigrationWriter.serialize(validator)[0]\n-        self.assertEqual(string, \"django.core.validators.EmailValidator(message='hello')\")\n-        self.serialize_round_trip(validator)\n-\n-        validator = deconstructible(path=\"migrations.test_writer.EmailValidator\")(EmailValidator)(message=\"hello\")\n-        string = MigrationWriter.serialize(validator)[0]\n-        self.assertEqual(string, \"migrations.test_writer.EmailValidator(message='hello')\")\n-\n-        validator = deconstructible(path=\"custom.EmailValidator\")(EmailValidator)(message=\"hello\")\n-        with self.assertRaisesMessage(ImportError, \"No module named 'custom'\"):\n-            MigrationWriter.serialize(validator)\n-\n-        validator = deconstructible(path=\"django.core.validators.EmailValidator2\")(EmailValidator)(message=\"hello\")\n-        with self.assertRaisesMessage(ValueError, \"Could not find object EmailValidator2 in django.core.validators.\"):\n-            MigrationWriter.serialize(validator)\n-\n-    def test_serialize_empty_nonempty_tuple(self):\n-        \"\"\"\n-        Ticket #22679: makemigrations generates invalid code for (an empty\n-        tuple) default_permissions = ()\n-        \"\"\"\n-        empty_tuple = ()\n-        one_item_tuple = ('a',)\n-        many_items_tuple = ('a', 'b', 'c')\n-        self.assertSerializedEqual(empty_tuple)\n-        self.assertSerializedEqual(one_item_tuple)\n-        self.assertSerializedEqual(many_items_tuple)\n-\n-    def test_serialize_range(self):\n-        string, imports = MigrationWriter.serialize(range(1, 5))\n-        self.assertEqual(string, 'range(1, 5)')\n-        self.assertEqual(imports, set())\n-\n-    def test_serialize_builtins(self):\n-        string, imports = MigrationWriter.serialize(range)\n-        self.assertEqual(string, 'range')\n-        self.assertEqual(imports, set())\n-\n-    def test_serialize_unbound_method_reference(self):\n-        \"\"\"An unbound method used within a class body can be serialized.\"\"\"\n-        self.serialize_round_trip(TestModel1.thing)\n-\n-    def test_serialize_local_function_reference(self):\n-        \"\"\"A reference in a local scope can't be serialized.\"\"\"\n-        class TestModel2:\n-            def upload_to(self):\n-                return \"somewhere dynamic\"\n-            thing = models.FileField(upload_to=upload_to)\n-\n-        with self.assertRaisesMessage(ValueError, 'Could not find function upload_to in migrations.test_writer'):\n-            self.serialize_round_trip(TestModel2.thing)\n-\n-    def test_serialize_managers(self):\n-        self.assertSerializedEqual(models.Manager())\n-        self.assertSerializedResultEqual(\n-            FoodQuerySet.as_manager(),\n-            ('migrations.models.FoodQuerySet.as_manager()', {'import migrations.models'})\n-        )\n-        self.assertSerializedEqual(FoodManager('a', 'b'))\n-        self.assertSerializedEqual(FoodManager('x', 'y', c=3, d=4))\n-\n-    def test_serialize_frozensets(self):\n-        self.assertSerializedEqual(frozenset())\n-        self.assertSerializedEqual(frozenset(\"let it go\"))\n-\n-    def test_serialize_set(self):\n-        self.assertSerializedEqual(set())\n-        self.assertSerializedResultEqual(set(), ('set()', set()))\n-        self.assertSerializedEqual({'a'})\n-        self.assertSerializedResultEqual({'a'}, (\"{'a'}\", set()))\n-\n-    def test_serialize_timedelta(self):\n-        self.assertSerializedEqual(datetime.timedelta())\n-        self.assertSerializedEqual(datetime.timedelta(minutes=42))\n-\n-    def test_serialize_functools_partial(self):\n-        value = functools.partial(datetime.timedelta, 1, seconds=2)\n-        result = self.serialize_round_trip(value)\n-        self.assertEqual(result.func, value.func)\n-        self.assertEqual(result.args, value.args)\n-        self.assertEqual(result.keywords, value.keywords)\n-\n-    def test_serialize_functools_partialmethod(self):\n-        value = functools.partialmethod(datetime.timedelta, 1, seconds=2)\n-        result = self.serialize_round_trip(value)\n-        self.assertIsInstance(result, functools.partialmethod)\n-        self.assertEqual(result.func, value.func)\n-        self.assertEqual(result.args, value.args)\n-        self.assertEqual(result.keywords, value.keywords)\n-\n-    def test_serialize_type_none(self):\n-        self.assertSerializedEqual(type(None))\n-\n-    def test_simple_migration(self):\n-        \"\"\"\n-        Tests serializing a simple migration.\n-        \"\"\"\n-        fields = {\n-            'charfield': models.DateTimeField(default=datetime.datetime.utcnow),\n-            'datetimefield': models.DateTimeField(default=datetime.datetime.utcnow),\n-        }\n-\n-        options = {\n-            'verbose_name': 'My model',\n-            'verbose_name_plural': 'My models',\n-        }\n-\n-        migration = type(\"Migration\", (migrations.Migration,), {\n-            \"operations\": [\n-                migrations.CreateModel(\"MyModel\", tuple(fields.items()), options, (models.Model,)),\n-                migrations.CreateModel(\"MyModel2\", tuple(fields.items()), bases=(models.Model,)),\n-                migrations.CreateModel(\n-                    name=\"MyModel3\", fields=tuple(fields.items()), options=options, bases=(models.Model,)\n-                ),\n-                migrations.DeleteModel(\"MyModel\"),\n-                migrations.AddField(\"OtherModel\", \"datetimefield\", fields[\"datetimefield\"]),\n-            ],\n-            \"dependencies\": [(\"testapp\", \"some_other_one\")],\n-        })\n-        writer = MigrationWriter(migration)\n-        output = writer.as_string()\n-        # We don't test the output formatting - that's too fragile.\n-        # Just make sure it runs for now, and that things look alright.\n-        result = self.safe_exec(output)\n-        self.assertIn(\"Migration\", result)\n-\n-    def test_migration_path(self):\n-        test_apps = [\n-            'migrations.migrations_test_apps.normal',\n-            'migrations.migrations_test_apps.with_package_model',\n-            'migrations.migrations_test_apps.without_init_file',\n+    def test_enum_serialization(self):\n+        fields = [\n+            migrations.AddField(\n+                'TestModel',\n+                'status',\n+                models.CharField(max_length=20, default=Status.GOOD),\n+            ),\n         ]\n \n-        base_dir = os.path.dirname(os.path.dirname(__file__))\n+        operation = fields[0]\n+        writer = OperationWriter(operation, indentation=2)\n+        output, imports = writer.serialize()\n \n-        for app in test_apps:\n-            with self.modify_settings(INSTALLED_APPS={'append': app}):\n-                migration = migrations.Migration('0001_initial', app.split('.')[-1])\n-                expected_path = os.path.join(base_dir, *(app.split('.') + ['migrations', '0001_initial.py']))\n-                writer = MigrationWriter(migration)\n-                self.assertEqual(writer.path, expected_path)\n+        # The operation should reference the enum by name, not value\n+        self.assertIn(\"Status['GOOD']\", output)\n+        \n+        # The imports should include the Status enum with correct module path\n+        import_line = f'from {self.test_module} import Status'\n+        self.assertIn(import_line, imports)\n \n-    def test_custom_operation(self):\n-        migration = type(\"Migration\", (migrations.Migration,), {\n-            \"operations\": [\n-                custom_migration_operations.operations.TestOperation(),\n-                custom_migration_operations.operations.CreateModel(),\n-                migrations.CreateModel(\"MyModel\", (), {}, (models.Model,)),\n-                custom_migration_operations.more_operations.TestOperation()\n-            ],\n-            \"dependencies\": []\n-        })\n-        writer = MigrationWriter(migration)\n-        output = writer.as_string()\n-        result = self.safe_exec(output)\n-        self.assertIn(\"custom_migration_operations\", result)\n-        self.assertNotEqual(\n-            result['custom_migration_operations'].operations.TestOperation,\n-            result['custom_migration_operations'].more_operations.TestOperation\n-        )\n-\n-    def test_sorted_imports(self):\n-        \"\"\"\n-        #24155 - Tests ordering of imports.\n-        \"\"\"\n-        migration = type(\"Migration\", (migrations.Migration,), {\n-            \"operations\": [\n-                migrations.AddField(\"mymodel\", \"myfield\", models.DateTimeField(\n-                    default=datetime.datetime(2012, 1, 1, 1, 1, tzinfo=utc),\n-                )),\n-            ]\n-        })\n-        writer = MigrationWriter(migration)\n-        output = writer.as_string()\n-        self.assertIn(\n-            \"import datetime\\n\"\n-            \"from django.db import migrations, models\\n\"\n-            \"from django.utils.timezone import utc\\n\",\n-            output\n-        )\n-\n-    def test_migration_file_header_comments(self):\n+    def test_translated_enum_value_independence(self):\n         \"\"\"\n-        Test comments at top of file.\n+        Test that enum serialization is independent of translation state.\n         \"\"\"\n-        migration = type(\"Migration\", (migrations.Migration,), {\n-            \"operations\": []\n-        })\n-        dt = datetime.datetime(2015, 7, 31, 4, 40, 0, 0, tzinfo=utc)\n-        with mock.patch('django.db.migrations.writer.now', lambda: dt):\n-            for include_header in (True, False):\n-                with self.subTest(include_header=include_header):\n-                    writer = MigrationWriter(migration, include_header)\n-                    output = writer.as_string()\n-\n-                    self.assertEqual(\n-                        include_header,\n-                        output.startswith(\n-                            \"# Generated by Django %s on 2015-07-31 04:40\\n\\n\" % get_version()\n-                        )\n-                    )\n-                    if not include_header:\n-                        # Make sure the output starts with something that's not\n-                        # a comment or indentation or blank line\n-                        self.assertRegex(output.splitlines(keepends=True)[0], r\"^[^#\\s]+\")\n-\n-    def test_models_import_omitted(self):\n-        \"\"\"\n-        django.db.models shouldn't be imported if unused.\n-        \"\"\"\n-        migration = type(\"Migration\", (migrations.Migration,), {\n-            \"operations\": [\n-                migrations.AlterModelOptions(\n-                    name='model',\n-                    options={'verbose_name': 'model', 'verbose_name_plural': 'models'},\n-                ),\n-            ]\n-        })\n-        writer = MigrationWriter(migration)\n-        output = writer.as_string()\n-        self.assertIn(\"from django.db import migrations\\n\", output)\n-\n-    def test_deconstruct_class_arguments(self):\n-        # Yes, it doesn't make sense to use a class as a default for a\n-        # CharField. It does make sense for custom fields though, for example\n-        # an enumfield that takes the enum class as an argument.\n-        class DeconstructibleInstances:\n-            def deconstruct(self):\n-                return ('DeconstructibleInstances', [], {})\n-\n-        string = MigrationWriter.serialize(models.CharField(default=DeconstructibleInstances))[0]\n-        self.assertEqual(string, \"models.CharField(default=migrations.test_writer.DeconstructibleInstances)\")\n-\n-    def test_register_serializer(self):\n-        class ComplexSerializer(BaseSerializer):\n-            def serialize(self):\n-                return 'complex(%r)' % self.value, {}\n-\n-        MigrationWriter.register_serializer(complex, ComplexSerializer)\n-        self.assertSerializedEqual(complex(1, 2))\n-        MigrationWriter.unregister_serializer(complex)\n-        with self.assertRaisesMessage(ValueError, 'Cannot serialize: (1+2j)'):\n-            self.assertSerializedEqual(complex(1, 2))\n-\n-    def test_register_non_serializer(self):\n-        with self.assertRaisesMessage(ValueError, \"'TestModel1' must inherit from 'BaseSerializer'.\"):\n-            MigrationWriter.register_serializer(complex, TestModel1)\n+        # Create field with enum default\n+        field = models.CharField(max_length=20, default=Status.GOOD)\n+        operation = migrations.AddField(\n+            'TestModel',\n+            'status',\n+            field,\n+        )\n+        writer = OperationWriter(operation, indentation=2)\n+        output, imports = writer.serialize()\n+\n+        # Should use name-based reference\n+        self.assertIn(\"Status['GOOD']\", output)\n+        self.assertNotIn(\"'Good'\", output)\n+        self.assertNotIn(\"_('Good')\", output)\n+        \n+        # Import should be from the current module\n+        self.assertIn(f'from {self.test_module} import Status', imports)\n\\ No newline at end of file\n", "proposed_model_patches": []}