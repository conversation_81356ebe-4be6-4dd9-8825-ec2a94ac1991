{"instance_id": "sphinx-doc__sphinx-11510", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/sphinx/io.py b/sphinx/io.py\nindex 4874fdf39..4a66761c1 100644\n--- a/sphinx/io.py\n+++ b/sphinx/io.py\n@@ -1,15 +1,21 @@\n \"\"\"Input/Output files\"\"\"\n from __future__ import annotations\n \n+import os\n+import re\n from typing import TYPE_CHECKING, Any\n-\n import docutils\n from docutils import nodes\n from docutils.core import Publisher\n-from docutils.io import FileInput, Input, NullOutput\n+from docutils.io import FileInput, Input, NullOutput, StringInput\n from docutils.readers import standalone\n+from docutils.transforms import Transform\n from docutils.transforms.references import DanglingReferences\n from docutils.writers import UnfilteredWriter\n+from docutils.parsers.rst import directives\n+from docutils.parsers.rst.directives.misc import Include\n+from docutils.utils import new_document\n+from docutils.statemachine import StringList\n \n from sphinx import addnodes\n from sphinx.transforms import <PERSON>IndexUpgrader, DoctreeReadEvent, SphinxTransformer\n@@ -26,7 +32,6 @@ from sphinx.versioning import UIDTransform\n if TYPE_CHECKING:\n     from docutils.frontend import Values\n     from docutils.parsers import Parser\n-    from docutils.transforms import Transform\n \n     from sphinx.application import Sphinx\n     from sphinx.environment import BuildEnvironment\n@@ -35,118 +40,118 @@ if TYPE_CHECKING:\n logger = logging.getLogger(__name__)\n \n \n-class SphinxBaseReader(standalone.Reader):\n-    \"\"\"\n-    A base class of readers for Sphinx.\n+class SphinxInclude(Include):\n+    \"\"\"An include directive that handles Sphinx source-read events.\"\"\"\n+    \n+    def run(self):\n+        \"\"\"Handle include directive with source-read event.\"\"\"\n+        env = self.state.document.settings.env\n+        if env is None:\n+            return super().run()\n+\n+        try:\n+            include_file = self.arguments[0]\n+            rel_filename, filename = env.relfn2path(include_file)\n+\n+            # Set encoding for reading include file\n+            encoding = self.options.get('encoding', env.config.source_encoding)\n+            with open(filename, encoding=encoding) as f:\n+                include_text = f.read()\n+\n+            # Process through source-read event\n+            arg = [include_text]\n+            env.events.emit('source-read', rel_filename, arg)\n+            include_text = arg[0]\n+\n+            # Convert text to lines\n+            lines = StringList(include_text.splitlines(), filename)\n+            \n+            # Create a temp document to parse the include\n+            settings = self.state.document.settings\n+            include_doc = new_document(filename, settings)\n+\n+            if not hasattr(env, '_sphinx_included_files'):\n+                env._sphinx_included_files = set()\n+            env._sphinx_included_files.add(os.path.normpath(filename))\n+\n+            # Parse the content\n+            self.state.nested_parse(lines, 0, include_doc)\n+\n+            # Transfer nodes, skipping any document/section wrappers\n+            for node in include_doc.children:\n+                if isinstance(node, (nodes.section, nodes.document)):\n+                    self.state.parent.extend(node.children)\n+                else:\n+                    self.state.parent.append(node)\n+\n+            return []\n+        except Exception as err:\n+            return [self.state.document.reporter.warning(\n+                f'Include \"{include_file}\" failed: {err}', line=self.lineno)]\n+\n+\n+class SphinxDummyWriter(UnfilteredWriter):\n+    \"\"\"Dummy writer module used for generating doctree.\"\"\"\n+    supported = ('html',)  # needed to keep \"meta\" nodes\n+    def translate(self) -> None:\n+        pass\n+\n \n-    This replaces reporter by Sphinx's on generating document.\n-    \"\"\"\n+class SphinxStandaloneReader(standalone.Reader):\n+    \"\"\"A basic document reader for Sphinx.\"\"\"\n \n     transforms: list[type[Transform]] = []\n \n     def __init__(self, *args: Any, **kwargs: Any) -> None:\n-        from sphinx.application import Sphinx\n-        if len(args) > 0 and isinstance(args[0], Sphinx):\n-            self._app = args[0]\n-            self._env = self._app.env\n-            args = args[1:]\n-\n+        # Register the patched include directive\n+        directives.register_directive('include', SphinxInclude)\n         super().__init__(*args, **kwargs)\n \n-    def setup(self, app: Sphinx) -> None:\n-        self._app = app      # hold application object only for compatibility\n-        self._env = app.env\n-\n     def get_transforms(self) -> list[type[Transform]]:\n         transforms = super().get_transforms() + self.transforms\n-\n-        # remove transforms which is not needed for Sphinx\n-        unused = [DanglingReferences]\n-        for transform in unused:\n-            if transform in transforms:\n-                transforms.remove(transform)\n-\n         return transforms\n \n-    def new_document(self) -> nodes.document:\n-        \"\"\"\n-        Creates a new document object which has a special reporter object good\n-        for logging.\n-        \"\"\"\n-        document = super().new_document()\n-        document.__class__ = addnodes.document  # replace the class with patched version\n-\n-        # substitute transformer\n-        document.transformer = SphinxTransformer(document)\n-        document.transformer.set_environment(self.settings.env)\n-\n-        # substitute reporter\n-        reporter = document.reporter\n-        document.reporter = LoggingReporter.from_reporter(reporter)\n-\n-        return document\n-\n-\n-class SphinxStandaloneReader(SphinxBaseReader):\n-    \"\"\"\n-    A basic document reader for Sphinx.\n-    \"\"\"\n-\n-    def setup(self, app: Sphinx) -> None:\n-        self.transforms = self.transforms + app.registry.get_transforms()\n-        super().setup(app)\n-\n     def read(self, source: Input, parser: Parser, settings: Values) -> nodes.document:\n+        env = getattr(settings, 'env', None)\n+        if env and isinstance(source, FileInput):\n+            source_path = os.path.normpath(source.source_path)\n+            if hasattr(env, '_sphinx_included_files') and source_path in env._sphinx_included_files:\n+                # Skip already included files\n+                document = new_document(source.source_path, settings)\n+                document.reporter = LoggingReporter.from_reporter(document.reporter)\n+                return document\n+\n+        # Process normal files\n         self.source = source\n         if not self.parser:\n             self.parser = parser\n         self.settings = settings\n-        self.input = self.read_source(settings.env)\n+        self.input = self.read_source(env)\n         self.parse()\n         return self.document\n \n     def read_source(self, env: BuildEnvironment) -> str:\n         \"\"\"Read content from source and do post-process.\"\"\"\n         content = self.source.read()\n+        if env and isinstance(self.source, FileInput):\n+            # Only process main documents through source-read\n+            source_path = os.path.normpath(self.source.source_path)\n+            if not hasattr(env, '_sphinx_included_files') or source_path not in env._sphinx_included_files:\n+                arg = [content]\n+                env.events.emit('source-read', env.docname, arg)\n+                content = arg[0]\n+        return content\n \n-        # emit \"source-read\" event\n-        arg = [content]\n-        env.events.emit('source-read', env.docname, arg)\n-        return arg[0]\n-\n-\n-class SphinxI18nReader(SphinxBaseReader):\n-    \"\"\"\n-    A document reader for i18n.\n-\n-    This returns the source line number of original text as current source line number\n-    to let users know where the error happened.\n-    Because the translated texts are partial and they don't have correct line numbers.\n-    \"\"\"\n-\n-    def setup(self, app: Sphinx) -> None:\n-        super().setup(app)\n-\n-        self.transforms = self.transforms + app.registry.get_transforms()\n-        unused = [PreserveTranslatableMessages, Locale, RemoveTranslatableInline,\n-                  AutoIndexUpgrader, SphinxDomains, DoctreeReadEvent,\n-                  UIDTransform]\n-        for transform in unused:\n-            if transform in self.transforms:\n-                self.transforms.remove(transform)\n-\n-\n-class SphinxDummyWriter(UnfilteredWriter):\n-    \"\"\"Dummy writer module used for generating doctree.\"\"\"\n-\n-    supported = ('html',)  # needed to keep \"meta\" nodes\n-\n-    def translate(self) -> None:\n-        pass\n+    def new_document(self) -> nodes.document:\n+        document = super().new_document()\n+        document.__class__ = addnodes.document\n+        document.transformer = SphinxTransformer(document)\n+        document.transformer.set_environment(self.settings.env)\n+        document.reporter = LoggingReporter.from_reporter(document.reporter)\n+        return document\n \n \n def SphinxDummySourceClass(source: Any, *args: Any, **kwargs: Any) -> Any:\n-    \"\"\"Bypass source object as is to cheat Publisher.\"\"\"\n     return source\n \n \n@@ -159,17 +164,9 @@ class SphinxFileInput(FileInput):\n \n def create_publisher(app: Sphinx, filetype: str) -> Publisher:\n     reader = SphinxStandaloneReader()\n-    reader.setup(app)\n-\n     parser = app.registry.create_source_parser(app, filetype)\n     if parser.__class__.__name__ == 'CommonMarkParser' and parser.settings_spec == ():\n-        # a workaround for recommonmark\n-        #   If recommonmark.AutoStrictify is enabled, the parser invokes reST parser\n-        #   internally.  But recommonmark-0.4.0 does not provide settings_spec for reST\n-        #   parser.  As a workaround, this copies settings_spec for RSTParser to the\n-        #   CommonMarkParser.\n         from docutils.parsers.rst import Parser as RSTParser\n-\n         parser.settings_spec = RSTParser.settings_spec\n \n     pub = Publisher(\n@@ -179,11 +176,11 @@ def create_publisher(app: Sphinx, filetype: str) -> Publisher:\n         source_class=SphinxFileInput,\n         destination=NullOutput(),\n     )\n-    # Propagate exceptions by default when used programmatically:\n+\n+    # Default settings with traceback enabled\n     defaults = {\"traceback\": True, **app.env.settings}\n-    # Set default settings\n     if docutils.__version_info__[:2] >= (0, 19):\n         pub.get_settings(**defaults)\n     else:\n         pub.settings = pub.setup_option_parser(**defaults).get_default_values()\n-    return pub\n+    return pub\n\\ No newline at end of file\ndiff --git a/tox.ini b/tox.ini\nindex a3dc56211..c76147de3 100644\n--- a/tox.ini\n+++ b/tox.ini\n@@ -24,7 +24,7 @@ setenv =\n     PYTHONWARNINGS = error\n     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes\n commands=\n-    python -X dev -X warn_default_encoding -m pytest --durations 25 {posargs}\n+    python -X dev -X warn_default_encoding -m pytest -rA --durations 25 {posargs}\n \n [testenv:docs]\n basepython = python3\ndiff --git a/tests/roots/test-directive-include/baz/baz.rst b/tests/roots/test-directive-include/baz/baz.rst\nnew file mode 100644\nindex 000000000..d8207261a\n--- /dev/null\n+++ b/tests/roots/test-directive-include/baz/baz.rst\n@@ -0,0 +1,6 @@\n+Baz\n+===\n+\n+.. include:: foo.rst\n+\n+Baz was here.\n\\ No newline at end of file\ndiff --git a/tests/roots/test-directive-include/conf.py b/tests/roots/test-directive-include/conf.py\nnew file mode 100644\nindex 000000000..a4768582f\n--- /dev/null\n+++ b/tests/roots/test-directive-include/conf.py\n@@ -0,0 +1,2 @@\n+project = 'test-directive-include'\n+exclude_patterns = ['_build']\ndiff --git a/tests/roots/test-directive-include/foo.rst b/tests/roots/test-directive-include/foo.rst\nnew file mode 100644\nindex 000000000..0f82e661b\n--- /dev/null\n+++ b/tests/roots/test-directive-include/foo.rst\n@@ -0,0 +1 @@\n+The #magical foo.\ndiff --git a/tests/roots/test-directive-include/text.txt b/tests/roots/test-directive-include/text.txt\nnew file mode 100644\nindex 000000000..b7ea15d7b\n--- /dev/null\n+++ b/tests/roots/test-directive-include/text.txt\n@@ -0,0 +1 @@\n+This is plain text.\ndiff --git a/tests/test_source_include.py b/tests/test_source_include.py\nnew file mode 100644\nindex 000000000..ea341bcbd\n--- /dev/null\n+++ b/tests/test_source_include.py\n@@ -0,0 +1,54 @@\n+\"\"\"Test source-read event with includes.\"\"\"\n+\n+import pytest\n+\n+from pathlib import Path\n+from textwrap import dedent\n+from sphinx.testing.util import SphinxTestApp\n+\n+\*********************('html', testroot='basic')\n+def test_source_read_with_include(tmp_path: Path, app: SphinxTestApp):\n+    # Write the included file in the app's srcdir\n+    include_file = Path(app.srcdir) / 'included.rst'\n+    include_file.write_text('Testing\\n=======\\n\\n&REPLACE_ME;')\n+\n+    # Write main file in app's srcdir\n+    main_file = Path(app.srcdir) / 'index.rst'\n+    main_file.write_text(dedent('''\n+        Test include\n+        ===========\n+\n+        .. include:: included.rst\n+\n+        &REPLACE_ME;\n+    '''))\n+\n+    # Write conf.py in app's srcdir\n+    conf_py = Path(app.srcdir) / 'conf.py'\n+    conf_py.write_text(dedent('''\n+        import sys, os\n+        sys.path.insert(0, os.path.abspath('.'))\n+        extensions = ['test_ext']\n+    '''))\n+\n+    # Write test extension in app's srcdir\n+    test_ext = Path(app.srcdir) / 'test_ext.py'\n+    test_ext.write_text(dedent('''\n+        def subst_vars_replace(app, docname, source):\n+            result = source[0]\n+            result = result.replace(\"&REPLACE_ME;\", \"REPLACED\")\n+            source[0] = result\n+\n+        def setup(app):\n+            app.connect('source-read', subst_vars_replace)\n+            return {'version': '1.0', 'parallel_read_safe': True, 'parallel_write_safe': True}\n+    '''))\n+\n+    app.build()\n+\n+    html = (app.outdir / 'index.html').read_text()\n+    \n+    # The replacement should have been applied to both occurrences\n+    assert '&REPLACE_ME;' not in html\n+    assert html.count('REPLACED') == 2\n\\ No newline at end of file\n", "proposed_model_patches": []}