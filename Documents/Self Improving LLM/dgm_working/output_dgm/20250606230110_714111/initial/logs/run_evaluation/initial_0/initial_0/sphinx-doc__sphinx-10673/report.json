{"sphinx-doc__sphinx-10673": {"patch_is_None": false, "patch_exists": true, "patch_successfully_applied": true, "resolved": false, "tests_status": {"FAIL_TO_PASS": {"success": [], "failure": ["tests/test_environment_toctree.py::test_toctree_index"]}, "PASS_TO_PASS": {"success": ["tests/test_environment_toctree.py::test_process_doc", "tests/test_environment_toctree.py::test_glob"], "failure": ["tests/test_environment_toctree.py::test_get_toc_for", "tests/test_environment_toctree.py::test_get_toc_for_only", "tests/test_environment_toctree.py::test_get_toc_for_tocdepth", "tests/test_environment_toctree.py::test_get_toctree_for", "tests/test_environment_toctree.py::test_get_toctree_for_collapse", "tests/test_environment_toctree.py::test_get_toctree_for_maxdepth", "tests/test_environment_toctree.py::test_get_toctree_for_includehidden"]}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}}}