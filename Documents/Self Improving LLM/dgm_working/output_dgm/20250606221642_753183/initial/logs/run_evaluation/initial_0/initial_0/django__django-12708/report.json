{"django__django-12708": {"patch_is_None": false, "patch_exists": true, "patch_successfully_applied": true, "resolved": false, "tests_status": {"FAIL_TO_PASS": {"success": [], "failure": ["test_alter_index_together_remove_with_unique_together (migrations.test_operations.OperationTests)"]}, "PASS_TO_PASS": {"success": [], "failure": ["test_references_model_mixin (migrations.test_operations.TestCreateModel)", "test_reference_field_by_through_fields (migrations.test_operations.FieldOperationTests)", "test_references_field_by_from_fields (migrations.test_operations.FieldOperationTests)", "test_references_field_by_name (migrations.test_operations.FieldOperationTests)", "test_references_field_by_remote_field_model (migrations.test_operations.FieldOperationTests)", "test_references_field_by_through (migrations.test_operations.FieldOperationTests)", "test_references_field_by_to_fields (migrations.test_operations.FieldOperationTests)", "test_references_model (migrations.test_operations.FieldOperationTests)", "test_add_field_ignore_swapped (migrations.test_operations.SwappableOperationTests)", "test_create_ignore_swapped (migrations.test_operations.SwappableOperationTests)", "test_delete_ignore_swapped (migrations.test_operations.SwappableOperationTests)", "test_indexes_ignore_swapped (migrations.test_operations.SwappableOperationTests)", "test_add_binaryfield (migrations.test_operations.OperationTests)", "test_add_charfield (migrations.test_operations.OperationTests)", "test_add_constraint (migrations.test_operations.OperationTests)", "test_add_constraint_combinable (migrations.test_operations.OperationTests)", "test_add_constraint_percent_escaping (migrations.test_operations.OperationTests)", "test_add_field (migrations.test_operations.OperationTests)", "test_add_field_m2m (migrations.test_operations.OperationTests)", "test_add_field_preserve_default (migrations.test_operations.OperationTests)", "test_add_index (migrations.test_operations.OperationTests)", "test_add_index_state_forwards (migrations.test_operations.OperationTests)", "test_add_or_constraint (migrations.test_operations.OperationTests)", "test_add_partial_unique_constraint (migrations.test_operations.OperationTests)", "test_add_textfield (migrations.test_operations.OperationTests)", "test_alter_field (migrations.test_operations.OperationTests)", "test_alter_field_m2m (migrations.test_operations.OperationTests)", "test_alter_field_pk (migrations.test_operations.OperationTests)", "test_alter_field_pk_fk (migrations.test_operations.OperationTests)", "test_alter_field_reloads_state_on_fk_target_changes (migrations.test_operations.OperationTests)", "test_alter_field_reloads_state_on_fk_with_to_field_related_name_target_type_change (migrations.test_operations.OperationTests)", "test_alter_field_reloads_state_on_fk_with_to_field_target_changes (migrations.test_operations.OperationTests)", "test_alter_field_reloads_state_on_fk_with_to_field_target_type_change (migrations.test_operations.OperationTests)", "test_alter_field_with_index (migrations.test_operations.OperationTests)", "test_alter_fk (migrations.test_operations.OperationTests)", "test_alter_fk_non_fk (migrations.test_operations.OperationTests)", "test_alter_index_together (migrations.test_operations.OperationTests)", "test_alter_index_together_remove (migrations.test_operations.OperationTests)", "test_alter_model_managers (migrations.test_operations.OperationTests)", "test_alter_model_managers_emptying (migrations.test_operations.OperationTests)", "test_alter_model_options (migrations.test_operations.OperationTests)", "test_alter_model_options_emptying (migrations.test_operations.OperationTests)", "test_alter_model_table (migrations.test_operations.OperationTests)", "test_alter_model_table_m2m (migrations.test_operations.OperationTests)", "test_alter_model_table_none (migrations.test_operations.OperationTests)", "test_alter_model_table_noop (migrations.test_operations.OperationTests)", "test_alter_order_with_respect_to (migrations.test_operations.OperationTests)", "test_alter_unique_together (migrations.test_operations.OperationTests)", "test_alter_unique_together_remove (migrations.test_operations.OperationTests)", "A field may be migrated from AutoField to BigAutoField.", "test_column_name_quoting (migrations.test_operations.OperationTests)", "test_create_model (migrations.test_operations.OperationTests)", "test_create_model_inheritance (migrations.test_operations.OperationTests)", "test_create_model_m2m (migrations.test_operations.OperationTests)", "test_create_model_managers (migrations.test_operations.OperationTests)", "test_create_model_with_constraint (migrations.test_operations.OperationTests)", "test_create_model_with_duplicate_base (migrations.test_operations.OperationTests)", "test_create_model_with_duplicate_field_name (migrations.test_operations.OperationTests)", "test_create_model_with_duplicate_manager_name (migrations.test_operations.OperationTests)", "test_create_model_with_partial_unique_constraint (migrations.test_operations.OperationTests)", "test_create_model_with_unique_after (migrations.test_operations.OperationTests)", "test_create_proxy_model (migrations.test_operations.OperationTests)", "test_create_unmanaged_model (migrations.test_operations.OperationTests)", "test_delete_model (migrations.test_operations.OperationTests)", "test_delete_mti_model (migrations.test_operations.OperationTests)", "test_delete_proxy_model (migrations.test_operations.OperationTests)", "test_model_with_bigautofield (migrations.test_operations.OperationTests)", "test_remove_constraint (migrations.test_operations.OperationTests)", "test_remove_field (migrations.test_operations.OperationTests)", "test_remove_field_m2m (migrations.test_operations.OperationTests)", "test_remove_field_m2m_with_through (migrations.test_operations.OperationTests)", "test_remove_fk (migrations.test_operations.OperationTests)", "test_remove_index (migrations.test_operations.OperationTests)", "test_remove_index_state_forwards (migrations.test_operations.OperationTests)", "test_remove_partial_unique_constraint (migrations.test_operations.OperationTests)", "test_rename_field (migrations.test_operations.OperationTests)", "test_rename_field_reloads_state_on_fk_target_changes (migrations.test_operations.OperationTests)", "RenameModel renames a many-to-many column after a RenameField.", "test_rename_m2m_target_model (migrations.test_operations.OperationTests)", "test_rename_m2m_through_model (migrations.test_operations.OperationTests)", "test_rename_missing_field (migrations.test_operations.OperationTests)", "test_rename_model (migrations.test_operations.OperationTests)", "test_rename_model_state_forwards (migrations.test_operations.OperationTests)", "test_rename_model_with_m2m (migrations.test_operations.OperationTests)", "test_rename_model_with_self_referential_fk (migrations.test_operations.OperationTests)", "test_rename_model_with_self_referential_m2m (migrations.test_operations.OperationTests)", "test_rename_model_with_superclass_fk (migrations.test_operations.OperationTests)", "test_rename_referenced_field_state_forward (migrations.test_operations.OperationTests)", "test_repoint_field_m2m (migrations.test_operations.OperationTests)", "test_run_python (migrations.test_operations.OperationTests)", "test_run_python_atomic (migrations.test_operations.OperationTests)", "test_run_python_noop (migrations.test_operations.OperationTests)", "test_run_python_related_assignment (migrations.test_operations.OperationTests)", "test_run_sql (migrations.test_operations.OperationTests)", "test_run_sql_noop (migrations.test_operations.OperationTests)", "test_run_sql_params (migrations.test_operations.OperationTests)", "test_run_sql_params_invalid (migrations.test_operations.OperationTests)", "test_separate_database_and_state (migrations.test_operations.OperationTests)", "test_separate_database_and_state2 (migrations.test_operations.OperationTests)", "A field may be migrated from SmallAutoField to AutoField.", "A field may be migrated from SmallAutoField to BigAutoField."]}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}}}