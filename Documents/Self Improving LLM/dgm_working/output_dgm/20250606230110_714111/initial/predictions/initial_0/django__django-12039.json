{"instance_id": "django__django-12039", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/django/db/backends/ddl_references.py b/django/db/backends/ddl_references.py\nindex 44e49806f8..cf7611e979 100644\n--- a/django/db/backends/ddl_references.py\n+++ b/django/db/backends/ddl_references.py\n@@ -112,12 +112,19 @@ class IndexColumns(Columns):\n         def col_str(column, idx):\n             # Index.__init__() guarantees that self.opclasses is the same\n             # length as self.columns.\n-            col = '{} {}'.format(self.quote_name(column), self.opclasses[idx])\n+            quoted_col = self.quote_name(column)\n+            parts = [quoted_col]\n+            \n+            if self.opclasses[idx]:\n+                parts.append(self.opclasses[idx])\n+            \n             try:\n-                col = '{} {}'.format(col, self.col_suffixes[idx])\n+                if self.col_suffixes[idx]:\n+                    parts.append(self.col_suffixes[idx].strip())\n             except IndexError:\n                 pass\n-            return col\n+            \n+            return ' '.join(parts)\n \n         return ', '.join(col_str(column, idx) for idx, column in enumerate(self.columns))\n \n@@ -191,4 +198,4 @@ class Statement(Reference):\n                 part.rename_column_references(table, old_column, new_column)\n \n     def __str__(self):\n-        return self.template % self.parts\n+        return self.template % self.parts\n\\ No newline at end of file\n", "proposed_model_patches": []}