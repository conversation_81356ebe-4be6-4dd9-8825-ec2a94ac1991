2025-03-14 14:12:21,451 - ThreadPoolExecutor-4_0 - INFO - No existing container with name sweb.eval.django__django-15930.20250314_141221_446860 found.
2025-03-14 14:12:21,453 - ThreadPoolExecutor-4_0 - INFO - Environment image sweb.env.x86_64.a33dddf55cdff5d8e23374:latest found for django__django-15930
Building instance image sweb.eval.x86_64.django__django-15930:latest for django__django-15930
2025-03-14 14:12:21,455 - ThreadPoolExecutor-4_0 - INFO - Image sweb.eval.x86_64.django__django-15930:latest already exists, skipping build.
2025-03-14 14:12:21,457 - ThreadPoolExecutor-4_0 - INFO - Creating container for django__django-15930...
2025-03-14 14:12:21,496 - Thread<PERSON>oolExecutor-4_0 - INFO - Container for django__django-15930 created: 21fd4ea897a81e3350c82fc7840980129b981680eb64bb69c6439e866f276ec3
2025-03-14 14:12:21,683 - ThreadPoolExecutor-4_0 - INFO - Copying coding_agent.py to container at /dgm/coding_agent.py
2025-03-14 14:12:21,685 - ThreadPoolExecutor-4_0 - INFO - Successfully copied coding_agent.py to container
2025-03-14 14:12:21,735 - ThreadPoolExecutor-4_0 - INFO - Copying requirements.txt to container at /dgm/requirements.txt
2025-03-14 14:12:21,737 - ThreadPoolExecutor-4_0 - INFO - Successfully copied requirements.txt to container
2025-03-14 14:12:21,789 - ThreadPoolExecutor-4_0 - INFO - Copying pytest.ini to container at /dgm/pytest.ini
2025-03-14 14:12:21,791 - ThreadPoolExecutor-4_0 - INFO - Successfully copied pytest.ini to container
2025-03-14 14:12:21,851 - ThreadPoolExecutor-4_0 - INFO - Copying tools to container at /dgm/tools
2025-03-14 14:12:21,853 - ThreadPoolExecutor-4_0 - INFO - Successfully copied tools to container
2025-03-14 14:12:21,919 - ThreadPoolExecutor-4_0 - INFO - Copying utils to container at /dgm/utils
2025-03-14 14:12:21,922 - ThreadPoolExecutor-4_0 - INFO - Successfully copied utils to container
2025-03-14 14:12:21,980 - ThreadPoolExecutor-4_0 - INFO - Copying tests to container at /dgm/tests
2025-03-14 14:12:21,982 - ThreadPoolExecutor-4_0 - INFO - Successfully copied tests to container
2025-03-14 14:12:22,041 - ThreadPoolExecutor-4_0 - INFO - Copying prompts to container at /dgm/prompts
2025-03-14 14:12:22,043 - ThreadPoolExecutor-4_0 - INFO - Successfully copied prompts to container
2025-03-14 14:12:22,094 - ThreadPoolExecutor-4_0 - INFO - Copying llm.py to container at /dgm/llm.py
2025-03-14 14:12:22,096 - ThreadPoolExecutor-4_0 - INFO - Successfully copied llm.py to container
2025-03-14 14:12:22,147 - ThreadPoolExecutor-4_0 - INFO - Copying llm_withtools.py to container at /dgm/llm_withtools.py
2025-03-14 14:12:22,149 - ThreadPoolExecutor-4_0 - INFO - Successfully copied llm_withtools.py to container
2025-03-14 14:12:22,150 - ThreadPoolExecutor-4_0 - INFO - Setting up environment
2025-03-14 14:12:22,212 - ThreadPoolExecutor-4_0 - INFO - Copying swe_bench/predictions/nerf_editwholefiles_0/django__django-15930_eval.sh to container at /eval.sh
2025-03-14 14:12:22,214 - ThreadPoolExecutor-4_0 - INFO - Successfully copied swe_bench/predictions/nerf_editwholefiles_0/django__django-15930_eval.sh to container
2025-03-14 14:12:27,237 - ThreadPoolExecutor-4_0 - INFO - Container output: + source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=2
++ CONDA_SHLVL=2
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_1=/opt/miniconda3
++ CONDA_PREFIX_1=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ cd /testbed
+ git config --global --add safe.directory /testbed
+ cd /testbed
+ git status
On branch main
nothing to commit, working tree clean
+ git show
commit 63884829acd207404f2a5c3cc1d6b4cd0a822b70
Author: Mariusz Felisiak <<EMAIL>>
Date:   Tue Aug 9 06:08:48 2022 +0200

    Fixed #33902 -- Fixed Meta.constraints validation crash with F() expressions.
    
    Thanks Adam Zahradník for the report.
    
    Bug in 667105877e6723c6985399803a364848891513cc.

diff --git a/django/db/models/constraints.py b/django/db/models/constraints.py
index 86f015465a..8cf1f0ff20 100644
--- a/django/db/models/constraints.py
+++ b/django/db/models/constraints.py
@@ -326,9 +326,12 @@ class UniqueConstraint(BaseConstraint):
             # Ignore constraints with excluded fields.
             if exclude:
                 for expression in self.expressions:
-                    for expr in expression.flatten():
-                        if isinstance(expr, F) and expr.name in exclude:
-                            return
+                    if hasattr(expression, "flatten"):
+                        for expr in expression.flatten():
+                            if isinstance(expr, F) and expr.name in exclude:
+                                return
+                    elif isinstance(expression, F) and expression.name in exclude:
+                        return
             replacement_map = instance._get_field_value_map(
                 meta=model._meta, exclude=exclude
             )
diff --git a/django/db/models/expressions.py b/django/db/models/expressions.py
index 5d23c1572f..822968ef56 100644
--- a/django/db/models/expressions.py
+++ b/django/db/models/expressions.py
@@ -393,9 +393,7 @@ class BaseExpression:
         clone = self.copy()
         clone.set_source_expressions(
             [
-                references_map.get(expr.name, expr)
-                if isinstance(expr, F)
-                else expr.replace_references(references_map)
+                expr.replace_references(references_map)
                 for expr in self.get_source_expressions()
             ]
         )
@@ -810,6 +808,9 @@ class F(Combinable):
     ):
         return query.resolve_ref(self.name, allow_joins, reuse, summarize)
 
+    def replace_references(self, references_map):
+        return references_map.get(self.name, self)
+
     def asc(self, **kwargs):
         return OrderBy(self, **kwargs)
 
diff --git a/docs/releases/4.1.1.txt b/docs/releases/4.1.1.txt
index 2e61e3877d..dbac1ff926 100644
--- a/docs/releases/4.1.1.txt
+++ b/docs/releases/4.1.1.txt
@@ -29,3 +29,6 @@ Bugfixes
 
 * Fixed a regression in Django 4.1 that caused a migration crash on SQLite
   3.35.5+ when removing an indexed field (:ticket:`33899`).
+
+* Fixed a bug in Django 4.1 that caused a crash of model validation on
+  ``UniqueConstraint()`` with field names in ``expressions`` (:ticket:`33902`).
diff --git a/tests/constraints/tests.py b/tests/constraints/tests.py
index 4032b418b4..d4054dfd77 100644
--- a/tests/constraints/tests.py
+++ b/tests/constraints/tests.py
@@ -685,6 +685,20 @@ class UniqueConstraintTests(TestCase):
             exclude={"color"},
         )
 
+    def test_validate_expression_str(self):
+        constraint = models.UniqueConstraint("name", name="name_uniq")
+        msg = "Constraint “name_uniq” is violated."
+        with self.assertRaisesMessage(ValidationError, msg):
+            constraint.validate(
+                UniqueConstraintProduct,
+                UniqueConstraintProduct(name=self.p1.name),
+            )
+        constraint.validate(
+            UniqueConstraintProduct,
+            UniqueConstraintProduct(name=self.p1.name),
+            exclude={"name"},
+        )
+
     def test_name(self):
         constraints = get_constraints(UniqueConstraintProduct._meta.db_table)
         expected_name = "name_color_uniq"
+ git diff 63884829acd207404f2a5c3cc1d6b4cd0a822b70
+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z x ']'
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1='(testbed) '
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=3
+++ CONDA_SHLVL=3
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=4
++ CONDA_SHLVL=4
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_3=/opt/miniconda3
++ CONDA_PREFIX_3=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ python -m pip install -e .
Obtaining file:///testbed
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Checking if build backend supports build_editable: started
  Checking if build backend supports build_editable: finished with status 'done'
  Getting requirements to build editable: started
  Getting requirements to build editable: finished with status 'done'
  Preparing editable metadata (pyproject.toml): started
  Preparing editable metadata (pyproject.toml): finished with status 'done'
Requirement already satisfied: asgiref>=3.5.2 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Django==4.2.dev20220809040848) (3.8.1)
Requirement already satisfied: sqlparse>=0.2.2 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Django==4.2.dev20220809040848) (0.5.3)
Requirement already satisfied: typing-extensions>=4 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from asgiref>=3.5.2->Django==4.2.dev20220809040848) (4.12.2)
Building wheels for collected packages: Django
  Building editable for Django (pyproject.toml): started
  Building editable for Django (pyproject.toml): finished with status 'done'
  Created wheel for Django: filename=django-4.2.dev20220809040848-0.editable-py3-none-any.whl size=27126 sha256=2bd3572587c30fa03bb62127832e3a21c07848877ba80da6d480ec058af37eba
  Stored in directory: /tmp/pip-ephem-wheel-cache-nfpqhujy/wheels/7d/66/67/70d1ee2124ccf21d601c352e25cdca10f611f7c8b3f9ffb9e4
Successfully built Django
Installing collected packages: Django
  Attempting uninstall: Django
    Found existing installation: Django 4.2.dev20220809040848
    Uninstalling Django-4.2.dev20220809040848:
      Successfully uninstalled Django-4.2.dev20220809040848
Successfully installed Django-4.2.dev20220809040848
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable.It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
+ git checkout 63884829acd207404f2a5c3cc1d6b4cd0a822b70 tests/expressions_case/tests.py
Updated 0 paths from 36bbc8a3fc
+ git apply -v -
Checking patch tests/expressions_case/tests.py...
Applied patch tests/expressions_case/tests.py cleanly.
+ ./tests/runtests.py --verbosity 2 --settings=test_sqlite --parallel 1 expressions_case.tests
Creating test database for alias 'default' ('file:memorydb_default?mode=memory&cache=shared')...
Testing against Django installed in '/testbed/django'
Importing application expressions_case
Found 89 test(s).
Skipping setup of unused database(s): other.
Operations to perform:
  Synchronize unmigrated apps: auth, contenttypes, expressions_case, messages, sessions, staticfiles
  Apply all migrations: admin, sites
Synchronizing apps without migrations:
  Creating tables...
    Creating table django_content_type
    Creating table auth_permission
    Creating table auth_group
    Creating table auth_user
    Creating table django_session
    Creating table expressions_case_casetestmodel
    Creating table expressions_case_o2ocasetestmodel
    Creating table expressions_case_fkcasetestmodel
    Creating table expressions_case_client
    Running deferred SQL...
Running migrations:
  Applying admin.0001_initial... OK
  Applying admin.0002_logentry_remove_auto_add... OK
  Applying admin.0003_logentry_add_action_flag_choices... OK
  Applying sites.0001_initial... OK
  Applying sites.0002_alter_domain_unique... OK
System check identified no issues (0 silenced).
test_conditional_aggregation_example (expressions_case.tests.CaseDocumentationExamples) ... ok
test_conditional_update_example (expressions_case.tests.CaseDocumentationExamples) ... ok
test_filter_example (expressions_case.tests.CaseDocumentationExamples) ... ok
test_hash (expressions_case.tests.CaseDocumentationExamples) ... ok
test_lookup_example (expressions_case.tests.CaseDocumentationExamples) ... ok
test_simple_example (expressions_case.tests.CaseDocumentationExamples) ... ok
test_aggregate (expressions_case.tests.CaseExpressionTests) ... ok
test_aggregate_with_expression_as_condition (expressions_case.tests.CaseExpressionTests) ... ok
test_aggregate_with_expression_as_value (expressions_case.tests.CaseExpressionTests) ... ok
test_aggregation_empty_cases (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_exclude (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_filter_decimal (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_values_not_in_order_by (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_with_aggregation_in_condition (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_with_aggregation_in_predicate (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_with_aggregation_in_value (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_with_annotation_in_condition (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_with_annotation_in_predicate (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_with_annotation_in_value (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_with_empty_when (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_with_expression_as_condition (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_with_expression_as_value (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_with_full_when (expressions_case.tests.CaseExpressionTests) ... ERROR
test_annotate_with_in_clause (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_with_join_in_condition (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_with_join_in_predicate (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_with_join_in_value (expressions_case.tests.CaseExpressionTests) ... ok
test_annotate_without_default (expressions_case.tests.CaseExpressionTests) ... ok
test_case_reuse (expressions_case.tests.CaseExpressionTests) ... ok
test_combined_expression (expressions_case.tests.CaseExpressionTests) ... ok
test_combined_q_object (expressions_case.tests.CaseExpressionTests) ... ok
test_condition_with_lookups (expressions_case.tests.CaseExpressionTests) ... ok
test_filter (expressions_case.tests.CaseExpressionTests) ... ok
test_filter_with_aggregation_in_condition (expressions_case.tests.CaseExpressionTests) ... ok
test_filter_with_aggregation_in_predicate (expressions_case.tests.CaseExpressionTests) ... ok
test_filter_with_aggregation_in_value (expressions_case.tests.CaseExpressionTests) ... ok
test_filter_with_annotation_in_condition (expressions_case.tests.CaseExpressionTests) ... ok
test_filter_with_annotation_in_predicate (expressions_case.tests.CaseExpressionTests) ... ok
test_filter_with_annotation_in_value (expressions_case.tests.CaseExpressionTests) ... ok
test_filter_with_expression_as_condition (expressions_case.tests.CaseExpressionTests) ... ok
test_filter_with_expression_as_value (expressions_case.tests.CaseExpressionTests) ... ok
test_filter_with_join_in_condition (expressions_case.tests.CaseExpressionTests) ... ok
test_filter_with_join_in_predicate (expressions_case.tests.CaseExpressionTests) ... ok
test_filter_with_join_in_value (expressions_case.tests.CaseExpressionTests) ... ok
test_filter_without_default (expressions_case.tests.CaseExpressionTests) ... ok
test_in_subquery (expressions_case.tests.CaseExpressionTests) ... ok
test_join_promotion (expressions_case.tests.CaseExpressionTests) ... ok
test_join_promotion_multiple_annotations (expressions_case.tests.CaseExpressionTests) ... ok
test_lookup_different_fields (expressions_case.tests.CaseExpressionTests) ... ok
test_lookup_in_condition (expressions_case.tests.CaseExpressionTests) ... ok
test_m2m_exclude (expressions_case.tests.CaseExpressionTests) ... ok
test_m2m_reuse (expressions_case.tests.CaseExpressionTests) ... ok
test_order_by_conditional_explicit (expressions_case.tests.CaseExpressionTests) ... ok
test_order_by_conditional_implicit (expressions_case.tests.CaseExpressionTests) ... ok
test_update (expressions_case.tests.CaseExpressionTests) ... ok
test_update_big_integer (expressions_case.tests.CaseExpressionTests) ... ok
test_update_binary (expressions_case.tests.CaseExpressionTests) ... ok
test_update_boolean (expressions_case.tests.CaseExpressionTests) ... ok
test_update_date (expressions_case.tests.CaseExpressionTests) ... ok
test_update_date_time (expressions_case.tests.CaseExpressionTests) ... ok
test_update_decimal (expressions_case.tests.CaseExpressionTests) ... ok
test_update_duration (expressions_case.tests.CaseExpressionTests) ... ok
test_update_email (expressions_case.tests.CaseExpressionTests) ... ok
test_update_file (expressions_case.tests.CaseExpressionTests) ... ok
test_update_file_path (expressions_case.tests.CaseExpressionTests) ... ok
test_update_fk (expressions_case.tests.CaseExpressionTests) ... ok
test_update_float (expressions_case.tests.CaseExpressionTests) ... ok
test_update_generic_ip_address (expressions_case.tests.CaseExpressionTests) ... ok
test_update_image (expressions_case.tests.CaseExpressionTests) ... ok
test_update_null_boolean (expressions_case.tests.CaseExpressionTests) ... ok
test_update_positive_big_integer (expressions_case.tests.CaseExpressionTests) ... ok
test_update_positive_integer (expressions_case.tests.CaseExpressionTests) ... ok
test_update_positive_small_integer (expressions_case.tests.CaseExpressionTests) ... ok
test_update_slug (expressions_case.tests.CaseExpressionTests) ... ok
test_update_small_integer (expressions_case.tests.CaseExpressionTests) ... ok
test_update_string (expressions_case.tests.CaseExpressionTests) ... ok
test_update_text (expressions_case.tests.CaseExpressionTests) ... ok
test_update_time (expressions_case.tests.CaseExpressionTests) ... ok
test_update_url (expressions_case.tests.CaseExpressionTests) ... ok
test_update_uuid (expressions_case.tests.CaseExpressionTests) ... ok
test_update_with_expression_as_condition (expressions_case.tests.CaseExpressionTests) ... ok
test_update_with_expression_as_value (expressions_case.tests.CaseExpressionTests) ... ok
test_update_with_join_in_condition_raise_field_error (expressions_case.tests.CaseExpressionTests) ... ok
test_update_with_join_in_predicate_raise_field_error (expressions_case.tests.CaseExpressionTests) ... ok
test_update_without_default (expressions_case.tests.CaseExpressionTests) ... ok
test_empty_q_object (expressions_case.tests.CaseWhenTests) ... ok
test_invalid_when_constructor_args (expressions_case.tests.CaseWhenTests) ... ok
test_only_when_arguments (expressions_case.tests.CaseWhenTests) ... ok

======================================================================
ERROR: test_annotate_with_full_when (expressions_case.tests.CaseExpressionTests)
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/testbed/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "/testbed/django/db/backends/sqlite3/base.py", line 369, in execute
    return Database.Cursor.execute(self, query, params)
sqlite3.OperationalError: near "THEN": syntax error

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/testbed/tests/expressions_case/tests.py", line 425, in test_annotate_with_full_when
    self.assertEqual(len(objects), CaseTestModel.objects.count())
  File "/testbed/django/db/models/query.py", line 376, in __len__
    self._fetch_all()
  File "/testbed/django/db/models/query.py", line 1876, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/testbed/django/db/models/query.py", line 87, in __iter__
    results = compiler.execute_sql(
  File "/testbed/django/db/models/sql/compiler.py", line 1396, in execute_sql
    cursor.execute(sql, params)
  File "/testbed/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "/testbed/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/testbed/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "/testbed/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/testbed/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "/testbed/django/db/backends/sqlite3/base.py", line 369, in execute
    return Database.Cursor.execute(self, query, params)
django.db.utils.OperationalError: near "THEN": syntax error

----------------------------------------------------------------------
Ran 89 tests in 0.124s

FAILED (errors=1)
Destroying test database for alias 'default' ('file:memorydb_default?mode=memory&cache=shared')...
+ git checkout 63884829acd207404f2a5c3cc1d6b4cd0a822b70 tests/expressions_case/tests.py
Updated 1 path from 36bbc8a3fc

2025-03-14 14:12:27,290 - ThreadPoolExecutor-4_0 - INFO - Container output: 
2025-03-14 14:12:27,290 - ThreadPoolExecutor-4_0 - INFO - Installing more requirements
2025-03-14 14:12:47,528 - ThreadPoolExecutor-4_0 - INFO - Container output: Collecting datasets (from -r /dgm/requirements.txt (line 1))
  Downloading datasets-3.3.2-py3-none-any.whl.metadata (19 kB)
Collecting anthropic (from -r /dgm/requirements.txt (line 2))
  Downloading anthropic-0.49.0-py3-none-any.whl.metadata (24 kB)
Collecting backoff (from -r /dgm/requirements.txt (line 4))
  Downloading backoff-2.2.1-py3-none-any.whl.metadata (14 kB)
Collecting botocore (from -r /dgm/requirements.txt (line 5))
  Downloading botocore-1.37.12-py3-none-any.whl.metadata (5.7 kB)
Collecting boto3 (from -r /dgm/requirements.txt (line 6))
  Downloading boto3-1.37.12-py3-none-any.whl.metadata (6.7 kB)
Collecting openai (from -r /dgm/requirements.txt (line 7))
  Downloading openai-1.66.3-py3-none-any.whl.metadata (25 kB)
Collecting beautifulsoup4 (from -r /dgm/requirements.txt (line 10))
  Downloading beautifulsoup4-4.13.3-py3-none-any.whl.metadata (3.8 kB)
Collecting chardet (from -r /dgm/requirements.txt (line 11))
  Downloading chardet-5.2.0-py3-none-any.whl.metadata (3.4 kB)
Collecting docker (from -r /dgm/requirements.txt (line 12))
  Downloading docker-7.1.0-py3-none-any.whl.metadata (3.8 kB)
Collecting ghapi (from -r /dgm/requirements.txt (line 13))
  Downloading ghapi-1.0.6-py3-none-any.whl.metadata (13 kB)
Collecting GitPython (from -r /dgm/requirements.txt (line 14))
  Downloading GitPython-3.1.44-py3-none-any.whl.metadata (13 kB)
Collecting pre-commit (from -r /dgm/requirements.txt (line 15))
  Downloading pre_commit-4.1.0-py2.py3-none-any.whl.metadata (1.3 kB)
Collecting python-dotenv (from -r /dgm/requirements.txt (line 16))
  Downloading python_dotenv-1.0.1-py3-none-any.whl.metadata (23 kB)
Collecting rich (from -r /dgm/requirements.txt (line 17))
  Downloading rich-13.9.4-py3-none-any.whl.metadata (18 kB)
Collecting unidiff (from -r /dgm/requirements.txt (line 18))
  Downloading unidiff-0.7.5-py2.py3-none-any.whl.metadata (4.6 kB)
Collecting pytest (from -r /dgm/requirements.txt (line 21))
  Downloading pytest-8.3.5-py3-none-any.whl.metadata (7.6 kB)
Collecting pytest-asyncio (from -r /dgm/requirements.txt (line 22))
  Downloading pytest_asyncio-0.25.3-py3-none-any.whl.metadata (3.9 kB)
Collecting filelock (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Collecting numpy>=1.17 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading numpy-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (62 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.0/62.0 kB 20.1 MB/s eta 0:00:00
Collecting pyarrow>=15.0.0 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading pyarrow-19.0.1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (3.3 kB)
Collecting dill<0.3.9,>=0.3.0 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading dill-0.3.8-py3-none-any.whl.metadata (10 kB)
Collecting pandas (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading pandas-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (89 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 89.9/89.9 kB 23.5 MB/s eta 0:00:00
Collecting requests>=2.32.2 (from datasets->-r /dgm/requirements.txt (line 1))
  Using cached requests-2.32.3-py3-none-any.whl.metadata (4.6 kB)
Collecting tqdm>=4.66.3 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 57.7/57.7 kB 11.6 MB/s eta 0:00:00
Collecting xxhash (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading xxhash-3.5.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (12 kB)
Collecting multiprocess<0.70.17 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading multiprocess-0.70.16-py311-none-any.whl.metadata (7.2 kB)
Collecting fsspec<=2024.12.0,>=2023.1.0 (from fsspec[http]<=2024.12.0,>=2023.1.0->datasets->-r /dgm/requirements.txt (line 1))
  Downloading fsspec-2024.12.0-py3-none-any.whl.metadata (11 kB)
Collecting aiohttp (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading aiohttp-3.11.13-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (7.7 kB)
Collecting huggingface-hub>=0.24.0 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading huggingface_hub-0.29.3-py3-none-any.whl.metadata (13 kB)
Requirement already satisfied: packaging in /opt/miniconda3/lib/python3.11/site-packages (from datasets->-r /dgm/requirements.txt (line 1)) (23.1)
Collecting pyyaml>=5.1 (from datasets->-r /dgm/requirements.txt (line 1))
  Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.1 kB)
Collecting anyio<5,>=3.5.0 (from anthropic->-r /dgm/requirements.txt (line 2))
  Downloading anyio-4.8.0-py3-none-any.whl.metadata (4.6 kB)
Requirement already satisfied: distro<2,>=1.7.0 in /opt/miniconda3/lib/python3.11/site-packages (from anthropic->-r /dgm/requirements.txt (line 2)) (1.8.0)
Collecting httpx<1,>=0.23.0 (from anthropic->-r /dgm/requirements.txt (line 2))
  Downloading httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Collecting jiter<1,>=0.4.0 (from anthropic->-r /dgm/requirements.txt (line 2))
  Downloading jiter-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.2 kB)
Collecting pydantic<3,>=1.9.0 (from anthropic->-r /dgm/requirements.txt (line 2))
  Downloading pydantic-2.10.6-py3-none-any.whl.metadata (30 kB)
Collecting sniffio (from anthropic->-r /dgm/requirements.txt (line 2))
  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting typing-extensions<5,>=4.10 (from anthropic->-r /dgm/requirements.txt (line 2))
  Using cached typing_extensions-4.12.2-py3-none-any.whl.metadata (3.0 kB)
Collecting jmespath<2.0.0,>=0.7.1 (from botocore->-r /dgm/requirements.txt (line 5))
  Downloading jmespath-1.0.1-py3-none-any.whl.metadata (7.6 kB)
Collecting python-dateutil<3.0.0,>=2.1 (from botocore->-r /dgm/requirements.txt (line 5))
  Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Requirement already satisfied: urllib3!=2.2.0,<3,>=1.25.4 in /opt/miniconda3/lib/python3.11/site-packages (from botocore->-r /dgm/requirements.txt (line 5)) (1.26.18)
Collecting s3transfer<0.12.0,>=0.11.0 (from boto3->-r /dgm/requirements.txt (line 6))
  Downloading s3transfer-0.11.4-py3-none-any.whl.metadata (1.7 kB)
Collecting soupsieve>1.2 (from beautifulsoup4->-r /dgm/requirements.txt (line 10))
  Downloading soupsieve-2.6-py3-none-any.whl.metadata (4.6 kB)
Collecting fastcore>=1.7.2 (from ghapi->-r /dgm/requirements.txt (line 13))
  Downloading fastcore-1.7.29-py3-none-any.whl.metadata (3.6 kB)
Collecting gitdb<5,>=4.0.1 (from GitPython->-r /dgm/requirements.txt (line 14))
  Downloading gitdb-4.0.12-py3-none-any.whl.metadata (1.2 kB)
Collecting cfgv>=2.0.0 (from pre-commit->-r /dgm/requirements.txt (line 15))
  Downloading cfgv-3.4.0-py2.py3-none-any.whl.metadata (8.5 kB)
Collecting identify>=1.0.0 (from pre-commit->-r /dgm/requirements.txt (line 15))
  Downloading identify-2.6.9-py2.py3-none-any.whl.metadata (4.4 kB)
Collecting nodeenv>=0.11.1 (from pre-commit->-r /dgm/requirements.txt (line 15))
  Downloading nodeenv-1.9.1-py2.py3-none-any.whl.metadata (21 kB)
Collecting virtualenv>=20.10.0 (from pre-commit->-r /dgm/requirements.txt (line 15))
  Downloading virtualenv-20.29.3-py3-none-any.whl.metadata (4.5 kB)
Collecting markdown-it-py>=2.2.0 (from rich->-r /dgm/requirements.txt (line 17))
  Downloading markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)
Collecting pygments<3.0.0,>=2.13.0 (from rich->-r /dgm/requirements.txt (line 17))
  Downloading pygments-2.19.1-py3-none-any.whl.metadata (2.5 kB)
Collecting iniconfig (from pytest->-r /dgm/requirements.txt (line 21))
  Downloading iniconfig-2.0.0-py3-none-any.whl.metadata (2.6 kB)
Collecting pluggy<2,>=1.5 (from pytest->-r /dgm/requirements.txt (line 21))
  Downloading pluggy-1.5.0-py3-none-any.whl.metadata (4.8 kB)
Requirement already satisfied: idna>=2.8 in /opt/miniconda3/lib/python3.11/site-packages (from anyio<5,>=3.5.0->anthropic->-r /dgm/requirements.txt (line 2)) (3.4)
Collecting aiohappyeyeballs>=2.3.0 (from aiohttp->datasets->-r /dgm/requirements.txt (line 1))
  Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl.metadata (5.9 kB)
Collecting aiosignal>=1.1.2 (from aiohttp->datasets->-r /dgm/requirements.txt (line 1))
  Using cached aiosignal-1.3.2-py2.py3-none-any.whl.metadata (3.8 kB)
Collecting attrs>=17.3.0 (from aiohttp->datasets->-r /dgm/requirements.txt (line 1))
  Downloading attrs-25.3.0-py3-none-any.whl.metadata (10 kB)
Collecting frozenlist>=1.1.1 (from aiohttp->datasets->-r /dgm/requirements.txt (line 1))
  Downloading frozenlist-1.5.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (13 kB)
Collecting multidict<7.0,>=4.5 (from aiohttp->datasets->-r /dgm/requirements.txt (line 1))
  Downloading multidict-6.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.0 kB)
Collecting propcache>=0.2.0 (from aiohttp->datasets->-r /dgm/requirements.txt (line 1))
  Downloading propcache-0.3.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (10 kB)
Collecting yarl<2.0,>=1.17.0 (from aiohttp->datasets->-r /dgm/requirements.txt (line 1))
  Downloading yarl-1.18.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (69 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 69.2/69.2 kB 10.6 MB/s eta 0:00:00
Collecting smmap<6,>=3.0.1 (from gitdb<5,>=4.0.1->GitPython->-r /dgm/requirements.txt (line 14))
  Downloading smmap-5.0.2-py3-none-any.whl.metadata (4.3 kB)
Requirement already satisfied: certifi in /opt/miniconda3/lib/python3.11/site-packages (from httpx<1,>=0.23.0->anthropic->-r /dgm/requirements.txt (line 2)) (2023.11.17)
Collecting httpcore==1.* (from httpx<1,>=0.23.0->anthropic->-r /dgm/requirements.txt (line 2))
  Downloading httpcore-1.0.7-py3-none-any.whl.metadata (21 kB)
Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->anthropic->-r /dgm/requirements.txt (line 2))
  Using cached h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)
Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich->-r /dgm/requirements.txt (line 17))
  Downloading mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)
Collecting annotated-types>=0.6.0 (from pydantic<3,>=1.9.0->anthropic->-r /dgm/requirements.txt (line 2))
  Downloading annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.27.2 (from pydantic<3,>=1.9.0->anthropic->-r /dgm/requirements.txt (line 2))
  Downloading pydantic_core-2.27.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.6 kB)
Collecting six>=1.5 (from python-dateutil<3.0.0,>=2.1->botocore->-r /dgm/requirements.txt (line 5))
  Downloading six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/lib/python3.11/site-packages (from requests>=2.32.2->datasets->-r /dgm/requirements.txt (line 1)) (2.0.4)
Collecting distlib<1,>=0.3.7 (from virtualenv>=20.10.0->pre-commit->-r /dgm/requirements.txt (line 15))
  Downloading distlib-0.3.9-py2.py3-none-any.whl.metadata (5.2 kB)
Requirement already satisfied: platformdirs<5,>=3.9.1 in /opt/miniconda3/lib/python3.11/site-packages (from virtualenv>=20.10.0->pre-commit->-r /dgm/requirements.txt (line 15)) (3.10.0)
Collecting pytz>=2020.1 (from pandas->datasets->-r /dgm/requirements.txt (line 1))
  Downloading pytz-2025.1-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.7 (from pandas->datasets->-r /dgm/requirements.txt (line 1))
  Downloading tzdata-2025.1-py2.py3-none-any.whl.metadata (1.4 kB)
Downloading datasets-3.3.2-py3-none-any.whl (485 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 485.4/485.4 kB 79.0 MB/s eta 0:00:00
Downloading anthropic-0.49.0-py3-none-any.whl (243 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 243.4/243.4 kB 64.1 MB/s eta 0:00:00
Downloading backoff-2.2.1-py3-none-any.whl (15 kB)
Downloading botocore-1.37.12-py3-none-any.whl (13.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.4/13.4 MB 182.5 MB/s eta 0:00:00
Downloading boto3-1.37.12-py3-none-any.whl (139 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 139.6/139.6 kB 41.3 MB/s eta 0:00:00
Downloading openai-1.66.3-py3-none-any.whl (567 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 567.4/567.4 kB 108.9 MB/s eta 0:00:00
Downloading beautifulsoup4-4.13.3-py3-none-any.whl (186 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 186.0/186.0 kB 52.3 MB/s eta 0:00:00
Downloading chardet-5.2.0-py3-none-any.whl (199 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 199.4/199.4 kB 52.1 MB/s eta 0:00:00
Downloading docker-7.1.0-py3-none-any.whl (147 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 147.8/147.8 kB 41.1 MB/s eta 0:00:00
Downloading ghapi-1.0.6-py3-none-any.whl (62 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.4/62.4 kB 20.2 MB/s eta 0:00:00
Downloading GitPython-3.1.44-py3-none-any.whl (207 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 207.6/207.6 kB 52.0 MB/s eta 0:00:00
Downloading pre_commit-4.1.0-py2.py3-none-any.whl (220 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 220.6/220.6 kB 51.5 MB/s eta 0:00:00
Downloading python_dotenv-1.0.1-py3-none-any.whl (19 kB)
Downloading rich-13.9.4-py3-none-any.whl (242 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 242.4/242.4 kB 63.2 MB/s eta 0:00:00
Downloading unidiff-0.7.5-py2.py3-none-any.whl (14 kB)
Downloading pytest-8.3.5-py3-none-any.whl (343 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 343.6/343.6 kB 64.8 MB/s eta 0:00:00
Downloading pytest_asyncio-0.25.3-py3-none-any.whl (19 kB)
Downloading anyio-4.8.0-py3-none-any.whl (96 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 96.0/96.0 kB 35.6 MB/s eta 0:00:00
Downloading cfgv-3.4.0-py2.py3-none-any.whl (7.2 kB)
Downloading dill-0.3.8-py3-none-any.whl (116 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 116.3/116.3 kB 9.7 MB/s eta 0:00:00
Downloading fastcore-1.7.29-py3-none-any.whl (84 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 84.2/84.2 kB 6.1 MB/s eta 0:00:00
Downloading fsspec-2024.12.0-py3-none-any.whl (183 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 183.9/183.9 kB 50.2 MB/s eta 0:00:00
Downloading aiohttp-3.11.13-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.7/1.7 MB 151.3 MB/s eta 0:00:00
Downloading gitdb-4.0.12-py3-none-any.whl (62 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.8/62.8 kB 19.3 MB/s eta 0:00:00
Downloading httpx-0.28.1-py3-none-any.whl (73 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 73.5/73.5 kB 18.9 MB/s eta 0:00:00
Downloading httpcore-1.0.7-py3-none-any.whl (78 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.6/78.6 kB 25.7 MB/s eta 0:00:00
Downloading huggingface_hub-0.29.3-py3-none-any.whl (468 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 469.0/469.0 kB 102.1 MB/s eta 0:00:00
Downloading identify-2.6.9-py2.py3-none-any.whl (99 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 99.1/99.1 kB 29.9 MB/s eta 0:00:00
Downloading jiter-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (351 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 351.8/351.8 kB 82.3 MB/s eta 0:00:00
Downloading jmespath-1.0.1-py3-none-any.whl (20 kB)
Downloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 87.5/87.5 kB 25.5 MB/s eta 0:00:00
Downloading multiprocess-0.70.16-py311-none-any.whl (143 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 143.5/143.5 kB 42.4 MB/s eta 0:00:00
Downloading nodeenv-1.9.1-py2.py3-none-any.whl (22 kB)
Downloading numpy-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (16.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 16.4/16.4 MB 31.9 MB/s eta 0:00:00
Downloading pluggy-1.5.0-py3-none-any.whl (20 kB)
Downloading pyarrow-19.0.1-cp311-cp311-manylinux_2_28_x86_64.whl (42.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 42.1/42.1 MB 95.5 MB/s eta 0:00:00
Downloading pydantic-2.10.6-py3-none-any.whl (431 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 431.7/431.7 kB 67.2 MB/s eta 0:00:00
Downloading pydantic_core-2.27.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 196.5 MB/s eta 0:00:00
Downloading pygments-2.19.1-py3-none-any.whl (1.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.2/1.2 MB 172.0 MB/s eta 0:00:00
Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 229.9/229.9 kB 60.7 MB/s eta 0:00:00
Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (762 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 763.0/763.0 kB 134.3 MB/s eta 0:00:00
Using cached requests-2.32.3-py3-none-any.whl (64 kB)
Downloading s3transfer-0.11.4-py3-none-any.whl (84 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 84.4/84.4 kB 28.6 MB/s eta 0:00:00
Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)
Downloading soupsieve-2.6-py3-none-any.whl (36 kB)
Downloading tqdm-4.67.1-py3-none-any.whl (78 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.5/78.5 kB 21.9 MB/s eta 0:00:00
Using cached typing_extensions-4.12.2-py3-none-any.whl (37 kB)
Downloading virtualenv-20.29.3-py3-none-any.whl (4.3 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 4.3/4.3 MB 204.6 MB/s eta 0:00:00
Downloading filelock-3.18.0-py3-none-any.whl (16 kB)
Downloading iniconfig-2.0.0-py3-none-any.whl (5.9 kB)
Downloading pandas-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (13.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.1/13.1 MB 197.4 MB/s eta 0:00:00
Downloading xxhash-3.5.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (194 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 194.8/194.8 kB 48.6 MB/s eta 0:00:00
Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
Using cached aiosignal-1.3.2-py2.py3-none-any.whl (7.6 kB)
Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
Downloading attrs-25.3.0-py3-none-any.whl (63 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 63.8/63.8 kB 22.3 MB/s eta 0:00:00
Downloading distlib-0.3.9-py2.py3-none-any.whl (468 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 469.0/469.0 kB 99.4 MB/s eta 0:00:00
Downloading frozenlist-1.5.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (274 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 274.9/274.9 kB 60.7 MB/s eta 0:00:00
Downloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)
Downloading multidict-6.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (129 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 129.0/129.0 kB 6.3 MB/s eta 0:00:00
Downloading propcache-0.3.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (231 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 231.3/231.3 kB 49.8 MB/s eta 0:00:00
Downloading pytz-2025.1-py2.py3-none-any.whl (507 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 507.9/507.9 kB 107.2 MB/s eta 0:00:00
Downloading six-1.17.0-py2.py3-none-any.whl (11 kB)
Downloading smmap-5.0.2-py3-none-any.whl (24 kB)
Downloading tzdata-2025.1-py2.py3-none-any.whl (346 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 346.8/346.8 kB 94.5 MB/s eta 0:00:00
Downloading yarl-1.18.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (344 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 344.1/344.1 kB 87.5 MB/s eta 0:00:00
Using cached h11-0.14.0-py3-none-any.whl (58 kB)
Installing collected packages: unidiff, pytz, distlib, xxhash, tzdata, typing-extensions, tqdm, soupsieve, sniffio, smmap, six, requests, pyyaml, python-dotenv, pygments, pyarrow, propcache, pluggy, numpy, nodeenv, multidict, mdurl, jmespath, jiter, iniconfig, identify, h11, fsspec, frozenlist, filelock, fastcore, dill, chardet, cfgv, backoff, attrs, annotated-types, aiohappyeyeballs, yarl, virtualenv, python-dateutil, pytest, pydantic-core, multiprocess, markdown-it-py, huggingface-hub, httpcore, gitdb, ghapi, docker, beautifulsoup4, anyio, aiosignal, rich, pytest-asyncio, pydantic, pre-commit, pandas, httpx, GitPython, botocore, aiohttp, s3transfer, openai, anthropic, datasets, boto3
  Attempting uninstall: tqdm
    Found existing installation: tqdm 4.65.0
    Uninstalling tqdm-4.65.0:
      Successfully uninstalled tqdm-4.65.0
  Attempting uninstall: requests
    Found existing installation: requests 2.31.0
    Uninstalling requests-2.31.0:
      Successfully uninstalled requests-2.31.0
  Attempting uninstall: pluggy
    Found existing installation: pluggy 1.0.0
    Uninstalling pluggy-1.0.0:
      Successfully uninstalled pluggy-1.0.0
Successfully installed GitPython-3.1.44 aiohappyeyeballs-2.6.1 aiohttp-3.11.13 aiosignal-1.3.2 annotated-types-0.7.0 anthropic-0.49.0 anyio-4.8.0 attrs-25.3.0 backoff-2.2.1 beautifulsoup4-4.13.3 boto3-1.37.12 botocore-1.37.12 cfgv-3.4.0 chardet-5.2.0 datasets-3.3.2 dill-0.3.8 distlib-0.3.9 docker-7.1.0 fastcore-1.7.29 filelock-3.18.0 frozenlist-1.5.0 fsspec-2024.12.0 ghapi-1.0.6 gitdb-4.0.12 h11-0.14.0 httpcore-1.0.7 httpx-0.28.1 huggingface-hub-0.29.3 identify-2.6.9 iniconfig-2.0.0 jiter-0.9.0 jmespath-1.0.1 markdown-it-py-3.0.0 mdurl-0.1.2 multidict-6.1.0 multiprocess-0.70.16 nodeenv-1.9.1 numpy-2.2.3 openai-1.66.3 pandas-2.2.3 pluggy-1.5.0 pre-commit-4.1.0 propcache-0.3.0 pyarrow-19.0.1 pydantic-2.10.6 pydantic-core-2.27.2 pygments-2.19.1 pytest-8.3.5 pytest-asyncio-0.25.3 python-dateutil-2.9.0.post0 python-dotenv-1.0.1 pytz-2025.1 pyyaml-6.0.2 requests-2.32.3 rich-13.9.4 s3transfer-0.11.4 six-1.17.0 smmap-5.0.2 sniffio-1.3.1 soupsieve-2.6 tqdm-4.67.1 typing-extensions-4.12.2 tzdata-2025.1 unidiff-0.7.5 virtualenv-20.29.3 xxhash-3.5.0 yarl-1.18.3
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv

2025-03-14 14:12:47,532 - ThreadPoolExecutor-4_0 - INFO - Running the agent
2025-03-14 14:14:07,941 - ThreadPoolExecutor-4_0 - INFO - Container output: Using Amazon Bedrock with model us.anthropic.claude-3-5-sonnet-20241022-v2:0.

2025-03-14 14:14:07,944 - ThreadPoolExecutor-4_0 - INFO - Copying output files back to host
2025-03-14 14:14:08,049 - ThreadPoolExecutor-4_0 - INFO - Copying from container /dgm/django__django-15930.md to local path swe_bench/predictions/nerf_editwholefiles_0/django__django-15930.md
2025-03-14 14:14:08,076 - ThreadPoolExecutor-4_0 - INFO - Successfully copied from container to swe_bench/predictions/nerf_editwholefiles_0/django__django-15930.md
2025-03-14 14:14:08,122 - ThreadPoolExecutor-4_0 - INFO - Getting model_patch
2025-03-14 14:14:08,166 - ThreadPoolExecutor-4_0 - INFO - Container output: diff --git a/django/db/models/expressions.py b/django/db/models/expressions.py
index 822968ef56..05059f577e 100644
--- a/django/db/models/expressions.py
+++ b/django/db/models/expressions.py
@@ -1299,6 +1299,9 @@ class When(Expression):
         template_params = extra_context
         sql_params = []
         condition_sql, condition_params = compiler.compile(self.condition)
+        # Handle the case of empty/negated empty IN clause
+        if not condition_sql.strip():
+            condition_sql = "TRUE"
         template_params["condition"] = condition_sql
         sql_params.extend(condition_params)
         result_sql, result_params = compiler.compile(self.result)
diff --git a/django/db/models/expressions.py.orig b/django/db/models/expressions.py.orig
new file mode 100644
index 0000000000..822968ef56
--- /dev/null
+++ b/django/db/models/expressions.py.orig
@@ -0,0 +1,1803 @@
+import copy
+import datetime
+import functools
+import inspect
+import warnings
+from collections import defaultdict
+from decimal import Decimal
+from uuid import UUID
+
+from django.core.exceptions import EmptyResultSet, FieldError
+from django.db import DatabaseError, NotSupportedError, connection
+from django.db.models import fields
+from django.db.models.constants import LOOKUP_SEP
+from django.db.models.query_utils import Q
+from django.utils.deconstruct import deconstructible
+from django.utils.deprecation import RemovedInDjango50Warning
+from django.utils.functional import cached_property
+from django.utils.hashable import make_hashable
+
+
+class SQLiteNumericMixin:
+    """
+    Some expressions with output_field=DecimalField() must be cast to
+    numeric to be properly filtered.
+    """
+
+    def as_sqlite(self, compiler, connection, **extra_context):
+        sql, params = self.as_sql(compiler, connection, **extra_context)
+        try:
+            if self.output_field.get_internal_type() == "DecimalField":
+                sql = "CAST(%s AS NUMERIC)" % sql
+        except FieldError:
+            pass
+        return sql, params
+
+
+class Combinable:
+    """
+    Provide the ability to combine one or two objects with
+    some connector. For example F('foo') + F('bar').
+    """
+
+    # Arithmetic connectors
+    ADD = "+"
+    SUB = "-"
+    MUL = "*"
+    DIV = "/"
+    POW = "^"
+    # The following is a quoted % operator - it is quoted because it can be
+    # used in strings that also have parameter substitution.
+    MOD = "%%"
+
+    # Bitwise operators - note that these are generated by .bitand()
+    # and .bitor(), the '&' and '|' are reserved for boolean operator
+    # usage.
+    BITAND = "&"
+    BITOR = "|"
+    BITLEFTSHIFT = "<<"
+    BITRIGHTSHIFT = ">>"
+    BITXOR = "#"
+
+    def _combine(self, other, connector, reversed):
+        if not hasattr(other, "resolve_expression"):
+            # everything must be resolvable to an expression
+            other = Value(other)
+
+        if reversed:
+            return CombinedExpression(other, connector, self)
+        return CombinedExpression(self, connector, other)
+
+    #############
+    # OPERATORS #
+    #############
+
+    def __neg__(self):
+        return self._combine(-1, self.MUL, False)
+
+    def __add__(self, other):
+        return self._combine(other, self.ADD, False)
+
+    def __sub__(self, other):
+        return self._combine(other, self.SUB, False)
+
+    def __mul__(self, other):
+        return self._combine(other, self.MUL, False)
+
+    def __truediv__(self, other):
+        return self._combine(other, self.DIV, False)
+
+    def __mod__(self, other):
+        return self._combine(other, self.MOD, False)
+
+    def __pow__(self, other):
+        return self._combine(other, self.POW, False)
+
+    def __and__(self, other):
+        if getattr(self, "conditional", False) and getattr(other, "conditional", False):
+            return Q(self) & Q(other)
+        raise NotImplementedError(
+            "Use .bitand(), .bitor(), and .bitxor() for bitwise logical operations."
+        )
+
+    def bitand(self, other):
+        return self._combine(other, self.BITAND, False)
+
+    def bitleftshift(self, other):
+        return self._combine(other, self.BITLEFTSHIFT, False)
+
+    def bitrightshift(self, other):
+        return self._combine(other, self.BITRIGHTSHIFT, False)
+
+    def __xor__(self, other):
+        if getattr(self, "conditional", False) and getattr(other, "conditional", False):
+            return Q(self) ^ Q(other)
+        raise NotImplementedError(
+            "Use .bitand(), .bitor(), and .bitxor() for bitwise logical operations."
+        )
+
+    def bitxor(self, other):
+        return self._combine(other, self.BITXOR, False)
+
+    def __or__(self, other):
+        if getattr(self, "conditional", False) and getattr(other, "conditional", False):
+            return Q(self) | Q(other)
+        raise NotImplementedError(
+            "Use .bitand(), .bitor(), and .bitxor() for bitwise logical operations."
+        )
+
+    def bitor(self, other):
+        return self._combine(other, self.BITOR, False)
+
+    def __radd__(self, other):
+        return self._combine(other, self.ADD, True)
+
+    def __rsub__(self, other):
+        return self._combine(other, self.SUB, True)
+
+    def __rmul__(self, other):
+        return self._combine(other, self.MUL, True)
+
+    def __rtruediv__(self, other):
+        return self._combine(other, self.DIV, True)
+
+    def __rmod__(self, other):
+        return self._combine(other, self.MOD, True)
+
+    def __rpow__(self, other):
+        return self._combine(other, self.POW, True)
+
+    def __rand__(self, other):
+        raise NotImplementedError(
+            "Use .bitand(), .bitor(), and .bitxor() for bitwise logical operations."
+        )
+
+    def __ror__(self, other):
+        raise NotImplementedError(
+            "Use .bitand(), .bitor(), and .bitxor() for bitwise logical operations."
+        )
+
+    def __rxor__(self, other):
+        raise NotImplementedError(
+            "Use .bitand(), .bitor(), and .bitxor() for bitwise logical operations."
+        )
+
+
+class BaseExpression:
+    """Base class for all query expressions."""
+
+    empty_result_set_value = NotImplemented
+    # aggregate specific fields
+    is_summary = False
+    _output_field_resolved_to_none = False
+    # Can the expression be used in a WHERE clause?
+    filterable = True
+    # Can the expression can be used as a source expression in Window?
+    window_compatible = False
+
+    def __init__(self, output_field=None):
+        if output_field is not None:
+            self.output_field = output_field
+
+    def __getstate__(self):
+        state = self.__dict__.copy()
+        state.pop("convert_value", None)
+        return state
+
+    def get_db_converters(self, connection):
+        return (
+            []
+            if self.convert_value is self._convert_value_noop
+            else [self.convert_value]
+        ) + self.output_field.get_db_converters(connection)
+
+    def get_source_expressions(self):
+        return []
+
+    def set_source_expressions(self, exprs):
+        assert not exprs
+
+    def _parse_expressions(self, *expressions):
+        return [
+            arg
+            if hasattr(arg, "resolve_expression")
+            else (F(arg) if isinstance(arg, str) else Value(arg))
+            for arg in expressions
+        ]
+
+    def as_sql(self, compiler, connection):
+        """
+        Responsible for returning a (sql, [params]) tuple to be included
+        in the current query.
+
+        Different backends can provide their own implementation, by
+        providing an `as_{vendor}` method and patching the Expression:
+
+        ```
+        def override_as_sql(self, compiler, connection):
+            # custom logic
+            return super().as_sql(compiler, connection)
+        setattr(Expression, 'as_' + connection.vendor, override_as_sql)
+        ```
+
+        Arguments:
+         * compiler: the query compiler responsible for generating the query.
+           Must have a compile method, returning a (sql, [params]) tuple.
+           Calling compiler(value) will return a quoted `value`.
+
+         * connection: the database connection used for the current query.
+
+        Return: (sql, params)
+          Where `sql` is a string containing ordered sql parameters to be
+          replaced with the elements of the list `params`.
+        """
+        raise NotImplementedError("Subclasses must implement as_sql()")
+
+    @cached_property
+    def contains_aggregate(self):
+        return any(
+            expr and expr.contains_aggregate for expr in self.get_source_expressions()
+        )
+
+    @cached_property
+    def contains_over_clause(self):
+        return any(
+            expr and expr.contains_over_clause for expr in self.get_source_expressions()
+        )
+
+    @cached_property
+    def contains_column_references(self):
+        return any(
+            expr and expr.contains_column_references
+            for expr in self.get_source_expressions()
+        )
+
+    def resolve_expression(
+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False
+    ):
+        """
+        Provide the chance to do any preprocessing or validation before being
+        added to the query.
+
+        Arguments:
+         * query: the backend query implementation
+         * allow_joins: boolean allowing or denying use of joins
+           in this query
+         * reuse: a set of reusable joins for multijoins
+         * summarize: a terminal aggregate clause
+         * for_save: whether this expression about to be used in a save or update
+
+        Return: an Expression to be added to the query.
+        """
+        c = self.copy()
+        c.is_summary = summarize
+        c.set_source_expressions(
+            [
+                expr.resolve_expression(query, allow_joins, reuse, summarize)
+                if expr
+                else None
+                for expr in c.get_source_expressions()
+            ]
+        )
+        return c
+
+    @property
+    def conditional(self):
+        return isinstance(self.output_field, fields.BooleanField)
+
+    @property
+    def field(self):
+        return self.output_field
+
+    @cached_property
+    def output_field(self):
+        """Return the output type of this expressions."""
+        output_field = self._resolve_output_field()
+        if output_field is None:
+            self._output_field_resolved_to_none = True
+            raise FieldError("Cannot resolve expression type, unknown output_field")
+        return output_field
+
+    @cached_property
+    def _output_field_or_none(self):
+        """
+        Return the output field of this expression, or None if
+        _resolve_output_field() didn't return an output type.
+        """
+        try:
+            return self.output_field
+        except FieldError:
+            if not self._output_field_resolved_to_none:
+                raise
+
+    def _resolve_output_field(self):
+        """
+        Attempt to infer the output type of the expression.
+
+        As a guess, if the output fields of all source fields match then simply
+        infer the same type here.
+
+        If a source's output field resolves to None, exclude it from this check.
+        If all sources are None, then an error is raised higher up the stack in
+        the output_field property.
+        """
+        # This guess is mostly a bad idea, but there is quite a lot of code
+        # (especially 3rd party Func subclasses) that depend on it, we'd need a
+        # deprecation path to fix it.
+        sources_iter = (
+            source for source in self.get_source_fields() if source is not None
+        )
+        for output_field in sources_iter:
+            for source in sources_iter:
+                if not isinstance(output_field, source.__class__):
+                    raise FieldError(
+                        "Expression contains mixed types: %s, %s. You must "
+                        "set output_field."
+                        % (
+                            output_field.__class__.__name__,
+                            source.__class__.__name__,
+                        )
+                    )
+            return output_field
+
+    @staticmethod
+    def _convert_value_noop(value, expression, connection):
+        return value
+
+    @cached_property
+    def convert_value(self):
+        """
+        Expressions provide their own converters because users have the option
+        of manually specifying the output_field which may be a different type
+        from the one the database returns.
+        """
+        field = self.output_field
+        internal_type = field.get_internal_type()
+        if internal_type == "FloatField":
+            return (
+                lambda value, expression, connection: None
+                if value is None
+                else float(value)
+            )
+        elif internal_type.endswith("IntegerField"):
+            return (
+                lambda value, expression, connection: None
+                if value is None
+                else int(value)
+            )
+        elif internal_type == "DecimalField":
+            return (
+                lambda value, expression, connection: None
+                if value is None
+                else Decimal(value)
+            )
+        return self._convert_value_noop
+
+    def get_lookup(self, lookup):
+        return self.output_field.get_lookup(lookup)
+
+    def get_transform(self, name):
+        return self.output_field.get_transform(name)
+
+    def relabeled_clone(self, change_map):
+        clone = self.copy()
+        clone.set_source_expressions(
+            [
+                e.relabeled_clone(change_map) if e is not None else None
+                for e in self.get_source_expressions()
+            ]
+        )
+        return clone
+
+    def replace_references(self, references_map):
+        clone = self.copy()
+        clone.set_source_expressions(
+            [
+                expr.replace_references(references_map)
+                for expr in self.get_source_expressions()
+            ]
+        )
+        return clone
+
+    def copy(self):
+        return copy.copy(self)
+
+    def prefix_references(self, prefix):
+        clone = self.copy()
+        clone.set_source_expressions(
+            [
+                F(f"{prefix}{expr.name}")
+                if isinstance(expr, F)
+                else expr.prefix_references(prefix)
+                for expr in self.get_source_expressions()
+            ]
+        )
+        return clone
+
+    def get_group_by_cols(self, alias=None):
+        if not self.contains_aggregate:
+            return [self]
+        cols = []
+        for source in self.get_source_expressions():
+            cols.extend(source.get_group_by_cols())
+        return cols
+
+    def get_source_fields(self):
+        """Return the underlying field types used by this aggregate."""
+        return [e._output_field_or_none for e in self.get_source_expressions()]
+
+    def asc(self, **kwargs):
+        return OrderBy(self, **kwargs)
+
+    def desc(self, **kwargs):
+        return OrderBy(self, descending=True, **kwargs)
+
+    def reverse_ordering(self):
+        return self
+
+    def flatten(self):
+        """
+        Recursively yield this expression and all subexpressions, in
+        depth-first order.
+        """
+        yield self
+        for expr in self.get_source_expressions():
+            if expr:
+                if hasattr(expr, "flatten"):
+                    yield from expr.flatten()
+                else:
+                    yield expr
+
+    def select_format(self, compiler, sql, params):
+        """
+        Custom format for select clauses. For example, EXISTS expressions need
+        to be wrapped in CASE WHEN on Oracle.
+        """
+        if hasattr(self.output_field, "select_format"):
+            return self.output_field.select_format(compiler, sql, params)
+        return sql, params
+
+
+@deconstructible
+class Expression(BaseExpression, Combinable):
+    """An expression that can be combined with other expressions."""
+
+    @cached_property
+    def identity(self):
+        constructor_signature = inspect.signature(self.__init__)
+        args, kwargs = self._constructor_args
+        signature = constructor_signature.bind_partial(*args, **kwargs)
+        signature.apply_defaults()
+        arguments = signature.arguments.items()
+        identity = [self.__class__]
+        for arg, value in arguments:
+            if isinstance(value, fields.Field):
+                if value.name and value.model:
+                    value = (value.model._meta.label, value.name)
+                else:
+                    value = type(value)
+            else:
+                value = make_hashable(value)
+            identity.append((arg, value))
+        return tuple(identity)
+
+    def __eq__(self, other):
+        if not isinstance(other, Expression):
+            return NotImplemented
+        return other.identity == self.identity
+
+    def __hash__(self):
+        return hash(self.identity)
+
+
+# Type inference for CombinedExpression.output_field.
+# Missing items will result in FieldError, by design.
+#
+# The current approach for NULL is based on lowest common denominator behavior
+# i.e. if one of the supported databases is raising an error (rather than
+# return NULL) for `val <op> NULL`, then Django raises FieldError.
+NoneType = type(None)
+
+_connector_combinations = [
+    # Numeric operations - operands of same type.
+    {
+        connector: [
+            (fields.IntegerField, fields.IntegerField, fields.IntegerField),
+            (fields.FloatField, fields.FloatField, fields.FloatField),
+            (fields.DecimalField, fields.DecimalField, fields.DecimalField),
+        ]
+        for connector in (
+            Combinable.ADD,
+            Combinable.SUB,
+            Combinable.MUL,
+            # Behavior for DIV with integer arguments follows Postgres/SQLite,
+            # not MySQL/Oracle.
+            Combinable.DIV,
+            Combinable.MOD,
+            Combinable.POW,
+        )
+    },
+    # Numeric operations - operands of different type.
+    {
+        connector: [
+            (fields.IntegerField, fields.DecimalField, fields.DecimalField),
+            (fields.DecimalField, fields.IntegerField, fields.DecimalField),
+            (fields.IntegerField, fields.FloatField, fields.FloatField),
+            (fields.FloatField, fields.IntegerField, fields.FloatField),
+        ]
+        for connector in (
+            Combinable.ADD,
+            Combinable.SUB,
+            Combinable.MUL,
+            Combinable.DIV,
+        )
+    },
+    # Bitwise operators.
+    {
+        connector: [
+            (fields.IntegerField, fields.IntegerField, fields.IntegerField),
+        ]
+        for connector in (
+            Combinable.BITAND,
+            Combinable.BITOR,
+            Combinable.BITLEFTSHIFT,
+            Combinable.BITRIGHTSHIFT,
+            Combinable.BITXOR,
+        )
+    },
+    # Numeric with NULL.
+    {
+        connector: [
+            (field_type, NoneType, field_type),
+            (NoneType, field_type, field_type),
+        ]
+        for connector in (
+            Combinable.ADD,
+            Combinable.SUB,
+            Combinable.MUL,
+            Combinable.DIV,
+            Combinable.MOD,
+            Combinable.POW,
+        )
+        for field_type in (fields.IntegerField, fields.DecimalField, fields.FloatField)
+    },
+    # Date/DateTimeField/DurationField/TimeField.
+    {
+        Combinable.ADD: [
+            # Date/DateTimeField.
+            (fields.DateField, fields.DurationField, fields.DateTimeField),
+            (fields.DateTimeField, fields.DurationField, fields.DateTimeField),
+            (fields.DurationField, fields.DateField, fields.DateTimeField),
+            (fields.DurationField, fields.DateTimeField, fields.DateTimeField),
+            # DurationField.
+            (fields.DurationField, fields.DurationField, fields.DurationField),
+            # TimeField.
+            (fields.TimeField, fields.DurationField, fields.TimeField),
+            (fields.DurationField, fields.TimeField, fields.TimeField),
+        ],
+    },
+    {
+        Combinable.SUB: [
+            # Date/DateTimeField.
+            (fields.DateField, fields.DurationField, fields.DateTimeField),
+            (fields.DateTimeField, fields.DurationField, fields.DateTimeField),
+            (fields.DateField, fields.DateField, fields.DurationField),
+            (fields.DateField, fields.DateTimeField, fields.DurationField),
+            (fields.DateTimeField, fields.DateField, fields.DurationField),
+            (fields.DateTimeField, fields.DateTimeField, fields.DurationField),
+            # DurationField.
+            (fields.DurationField, fields.DurationField, fields.DurationField),
+            # TimeField.
+            (fields.TimeField, fields.DurationField, fields.TimeField),
+            (fields.TimeField, fields.TimeField, fields.DurationField),
+        ],
+    },
+]
+
+_connector_combinators = defaultdict(list)
+
+
+def register_combinable_fields(lhs, connector, rhs, result):
+    """
+    Register combinable types:
+        lhs <connector> rhs -> result
+    e.g.
+        register_combinable_fields(
+            IntegerField, Combinable.ADD, FloatField, FloatField
+        )
+    """
+    _connector_combinators[connector].append((lhs, rhs, result))
+
+
+for d in _connector_combinations:
+    for connector, field_types in d.items():
+        for lhs, rhs, result in field_types:
+            register_combinable_fields(lhs, connector, rhs, result)
+
+
+@functools.lru_cache(maxsize=128)
+def _resolve_combined_type(connector, lhs_type, rhs_type):
+    combinators = _connector_combinators.get(connector, ())
+    for combinator_lhs_type, combinator_rhs_type, combined_type in combinators:
+        if issubclass(lhs_type, combinator_lhs_type) and issubclass(
+            rhs_type, combinator_rhs_type
+        ):
+            return combined_type
+
+
+class CombinedExpression(SQLiteNumericMixin, Expression):
+    def __init__(self, lhs, connector, rhs, output_field=None):
+        super().__init__(output_field=output_field)
+        self.connector = connector
+        self.lhs = lhs
+        self.rhs = rhs
+
+    def __repr__(self):
+        return "<{}: {}>".format(self.__class__.__name__, self)
+
+    def __str__(self):
+        return "{} {} {}".format(self.lhs, self.connector, self.rhs)
+
+    def get_source_expressions(self):
+        return [self.lhs, self.rhs]
+
+    def set_source_expressions(self, exprs):
+        self.lhs, self.rhs = exprs
+
+    def _resolve_output_field(self):
+        # We avoid using super() here for reasons given in
+        # Expression._resolve_output_field()
+        combined_type = _resolve_combined_type(
+            self.connector,
+            type(self.lhs._output_field_or_none),
+            type(self.rhs._output_field_or_none),
+        )
+        if combined_type is None:
+            raise FieldError(
+                f"Cannot infer type of {self.connector!r} expression involving these "
+                f"types: {self.lhs.output_field.__class__.__name__}, "
+                f"{self.rhs.output_field.__class__.__name__}. You must set "
+                f"output_field."
+            )
+        return combined_type()
+
+    def as_sql(self, compiler, connection):
+        expressions = []
+        expression_params = []
+        sql, params = compiler.compile(self.lhs)
+        expressions.append(sql)
+        expression_params.extend(params)
+        sql, params = compiler.compile(self.rhs)
+        expressions.append(sql)
+        expression_params.extend(params)
+        # order of precedence
+        expression_wrapper = "(%s)"
+        sql = connection.ops.combine_expression(self.connector, expressions)
+        return expression_wrapper % sql, expression_params
+
+    def resolve_expression(
+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False
+    ):
+        lhs = self.lhs.resolve_expression(
+            query, allow_joins, reuse, summarize, for_save
+        )
+        rhs = self.rhs.resolve_expression(
+            query, allow_joins, reuse, summarize, for_save
+        )
+        if not isinstance(self, (DurationExpression, TemporalSubtraction)):
+            try:
+                lhs_type = lhs.output_field.get_internal_type()
+            except (AttributeError, FieldError):
+                lhs_type = None
+            try:
+                rhs_type = rhs.output_field.get_internal_type()
+            except (AttributeError, FieldError):
+                rhs_type = None
+            if "DurationField" in {lhs_type, rhs_type} and lhs_type != rhs_type:
+                return DurationExpression(
+                    self.lhs, self.connector, self.rhs
+                ).resolve_expression(
+                    query,
+                    allow_joins,
+                    reuse,
+                    summarize,
+                    for_save,
+                )
+            datetime_fields = {"DateField", "DateTimeField", "TimeField"}
+            if (
+                self.connector == self.SUB
+                and lhs_type in datetime_fields
+                and lhs_type == rhs_type
+            ):
+                return TemporalSubtraction(self.lhs, self.rhs).resolve_expression(
+                    query,
+                    allow_joins,
+                    reuse,
+                    summarize,
+                    for_save,
+                )
+        c = self.copy()
+        c.is_summary = summarize
+        c.lhs = lhs
+        c.rhs = rhs
+        return c
+
+
+class DurationExpression(CombinedExpression):
+    def compile(self, side, compiler, connection):
+        try:
+            output = side.output_field
+        except FieldError:
+            pass
+        else:
+            if output.get_internal_type() == "DurationField":
+                sql, params = compiler.compile(side)
+                return connection.ops.format_for_duration_arithmetic(sql), params
+        return compiler.compile(side)
+
+    def as_sql(self, compiler, connection):
+        if connection.features.has_native_duration_field:
+            return super().as_sql(compiler, connection)
+        connection.ops.check_expression_support(self)
+        expressions = []
+        expression_params = []
+        sql, params = self.compile(self.lhs, compiler, connection)
+        expressions.append(sql)
+        expression_params.extend(params)
+        sql, params = self.compile(self.rhs, compiler, connection)
+        expressions.append(sql)
+        expression_params.extend(params)
+        # order of precedence
+        expression_wrapper = "(%s)"
+        sql = connection.ops.combine_duration_expression(self.connector, expressions)
+        return expression_wrapper % sql, expression_params
+
+    def as_sqlite(self, compiler, connection, **extra_context):
+        sql, params = self.as_sql(compiler, connection, **extra_context)
+        if self.connector in {Combinable.MUL, Combinable.DIV}:
+            try:
+                lhs_type = self.lhs.output_field.get_internal_type()
+                rhs_type = self.rhs.output_field.get_internal_type()
+            except (AttributeError, FieldError):
+                pass
+            else:
+                allowed_fields = {
+                    "DecimalField",
+                    "DurationField",
+                    "FloatField",
+                    "IntegerField",
+                }
+                if lhs_type not in allowed_fields or rhs_type not in allowed_fields:
+                    raise DatabaseError(
+                        f"Invalid arguments for operator {self.connector}."
+                    )
+        return sql, params
+
+
+class TemporalSubtraction(CombinedExpression):
+    output_field = fields.DurationField()
+
+    def __init__(self, lhs, rhs):
+        super().__init__(lhs, self.SUB, rhs)
+
+    def as_sql(self, compiler, connection):
+        connection.ops.check_expression_support(self)
+        lhs = compiler.compile(self.lhs)
+        rhs = compiler.compile(self.rhs)
+        return connection.ops.subtract_temporals(
+            self.lhs.output_field.get_internal_type(), lhs, rhs
+        )
+
+
+@deconstructible(path="django.db.models.F")
+class F(Combinable):
+    """An object capable of resolving references to existing query objects."""
+
+    def __init__(self, name):
+        """
+        Arguments:
+         * name: the name of the field this expression references
+        """
+        self.name = name
+
+    def __repr__(self):
+        return "{}({})".format(self.__class__.__name__, self.name)
+
+    def resolve_expression(
+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False
+    ):
+        return query.resolve_ref(self.name, allow_joins, reuse, summarize)
+
+    def replace_references(self, references_map):
+        return references_map.get(self.name, self)
+
+    def asc(self, **kwargs):
+        return OrderBy(self, **kwargs)
+
+    def desc(self, **kwargs):
+        return OrderBy(self, descending=True, **kwargs)
+
+    def __eq__(self, other):
+        return self.__class__ == other.__class__ and self.name == other.name
+
+    def __hash__(self):
+        return hash(self.name)
+
+
+class ResolvedOuterRef(F):
+    """
+    An object that contains a reference to an outer query.
+
+    In this case, the reference to the outer query has been resolved because
+    the inner query has been used as a subquery.
+    """
+
+    contains_aggregate = False
+
+    def as_sql(self, *args, **kwargs):
+        raise ValueError(
+            "This queryset contains a reference to an outer query and may "
+            "only be used in a subquery."
+        )
+
+    def resolve_expression(self, *args, **kwargs):
+        col = super().resolve_expression(*args, **kwargs)
+        # FIXME: Rename possibly_multivalued to multivalued and fix detection
+        # for non-multivalued JOINs (e.g. foreign key fields). This should take
+        # into account only many-to-many and one-to-many relationships.
+        col.possibly_multivalued = LOOKUP_SEP in self.name
+        return col
+
+    def relabeled_clone(self, relabels):
+        return self
+
+    def get_group_by_cols(self, alias=None):
+        return []
+
+
+class OuterRef(F):
+    contains_aggregate = False
+
+    def resolve_expression(self, *args, **kwargs):
+        if isinstance(self.name, self.__class__):
+            return self.name
+        return ResolvedOuterRef(self.name)
+
+    def relabeled_clone(self, relabels):
+        return self
+
+
+@deconstructible(path="django.db.models.Func")
+class Func(SQLiteNumericMixin, Expression):
+    """An SQL function call."""
+
+    function = None
+    template = "%(function)s(%(expressions)s)"
+    arg_joiner = ", "
+    arity = None  # The number of arguments the function accepts.
+
+    def __init__(self, *expressions, output_field=None, **extra):
+        if self.arity is not None and len(expressions) != self.arity:
+            raise TypeError(
+                "'%s' takes exactly %s %s (%s given)"
+                % (
+                    self.__class__.__name__,
+                    self.arity,
+                    "argument" if self.arity == 1 else "arguments",
+                    len(expressions),
+                )
+            )
+        super().__init__(output_field=output_field)
+        self.source_expressions = self._parse_expressions(*expressions)
+        self.extra = extra
+
+    def __repr__(self):
+        args = self.arg_joiner.join(str(arg) for arg in self.source_expressions)
+        extra = {**self.extra, **self._get_repr_options()}
+        if extra:
+            extra = ", ".join(
+                str(key) + "=" + str(val) for key, val in sorted(extra.items())
+            )
+            return "{}({}, {})".format(self.__class__.__name__, args, extra)
+        return "{}({})".format(self.__class__.__name__, args)
+
+    def _get_repr_options(self):
+        """Return a dict of extra __init__() options to include in the repr."""
+        return {}
+
+    def get_source_expressions(self):
+        return self.source_expressions
+
+    def set_source_expressions(self, exprs):
+        self.source_expressions = exprs
+
+    def resolve_expression(
+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False
+    ):
+        c = self.copy()
+        c.is_summary = summarize
+        for pos, arg in enumerate(c.source_expressions):
+            c.source_expressions[pos] = arg.resolve_expression(
+                query, allow_joins, reuse, summarize, for_save
+            )
+        return c
+
+    def as_sql(
+        self,
+        compiler,
+        connection,
+        function=None,
+        template=None,
+        arg_joiner=None,
+        **extra_context,
+    ):
+        connection.ops.check_expression_support(self)
+        sql_parts = []
+        params = []
+        for arg in self.source_expressions:
+            try:
+                arg_sql, arg_params = compiler.compile(arg)
+            except EmptyResultSet:
+                empty_result_set_value = getattr(
+                    arg, "empty_result_set_value", NotImplemented
+                )
+                if empty_result_set_value is NotImplemented:
+                    raise
+                arg_sql, arg_params = compiler.compile(Value(empty_result_set_value))
+            sql_parts.append(arg_sql)
+            params.extend(arg_params)
+        data = {**self.extra, **extra_context}
+        # Use the first supplied value in this order: the parameter to this
+        # method, a value supplied in __init__()'s **extra (the value in
+        # `data`), or the value defined on the class.
+        if function is not None:
+            data["function"] = function
+        else:
+            data.setdefault("function", self.function)
+        template = template or data.get("template", self.template)
+        arg_joiner = arg_joiner or data.get("arg_joiner", self.arg_joiner)
+        data["expressions"] = data["field"] = arg_joiner.join(sql_parts)
+        return template % data, params
+
+    def copy(self):
+        copy = super().copy()
+        copy.source_expressions = self.source_expressions[:]
+        copy.extra = self.extra.copy()
+        return copy
+
+
+@deconstructible(path="django.db.models.Value")
+class Value(SQLiteNumericMixin, Expression):
+    """Represent a wrapped value as a node within an expression."""
+
+    # Provide a default value for `for_save` in order to allow unresolved
+    # instances to be compiled until a decision is taken in #25425.
+    for_save = False
+
+    def __init__(self, value, output_field=None):
+        """
+        Arguments:
+         * value: the value this expression represents. The value will be
+           added into the sql parameter list and properly quoted.
+
+         * output_field: an instance of the model field type that this
+           expression will return, such as IntegerField() or CharField().
+        """
+        super().__init__(output_field=output_field)
+        self.value = value
+
+    def __repr__(self):
+        return f"{self.__class__.__name__}({self.value!r})"
+
+    def as_sql(self, compiler, connection):
+        connection.ops.check_expression_support(self)
+        val = self.value
+        output_field = self._output_field_or_none
+        if output_field is not None:
+            if self.for_save:
+                val = output_field.get_db_prep_save(val, connection=connection)
+            else:
+                val = output_field.get_db_prep_value(val, connection=connection)
+            if hasattr(output_field, "get_placeholder"):
+                return output_field.get_placeholder(val, compiler, connection), [val]
+        if val is None:
+            # cx_Oracle does not always convert None to the appropriate
+            # NULL type (like in case expressions using numbers), so we
+            # use a literal SQL NULL
+            return "NULL", []
+        return "%s", [val]
+
+    def resolve_expression(
+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False
+    ):
+        c = super().resolve_expression(query, allow_joins, reuse, summarize, for_save)
+        c.for_save = for_save
+        return c
+
+    def get_group_by_cols(self, alias=None):
+        return []
+
+    def _resolve_output_field(self):
+        if isinstance(self.value, str):
+            return fields.CharField()
+        if isinstance(self.value, bool):
+            return fields.BooleanField()
+        if isinstance(self.value, int):
+            return fields.IntegerField()
+        if isinstance(self.value, float):
+            return fields.FloatField()
+        if isinstance(self.value, datetime.datetime):
+            return fields.DateTimeField()
+        if isinstance(self.value, datetime.date):
+            return fields.DateField()
+        if isinstance(self.value, datetime.time):
+            return fields.TimeField()
+        if isinstance(self.value, datetime.timedelta):
+            return fields.DurationField()
+        if isinstance(self.value, Decimal):
+            return fields.DecimalField()
+        if isinstance(self.value, bytes):
+            return fields.BinaryField()
+        if isinstance(self.value, UUID):
+            return fields.UUIDField()
+
+    @property
+    def empty_result_set_value(self):
+        return self.value
+
+
+class RawSQL(Expression):
+    def __init__(self, sql, params, output_field=None):
+        if output_field is None:
+            output_field = fields.Field()
+        self.sql, self.params = sql, params
+        super().__init__(output_field=output_field)
+
+    def __repr__(self):
+        return "{}({}, {})".format(self.__class__.__name__, self.sql, self.params)
+
+    def as_sql(self, compiler, connection):
+        return "(%s)" % self.sql, self.params
+
+    def get_group_by_cols(self, alias=None):
+        return [self]
+
+    def resolve_expression(
+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False
+    ):
+        # Resolve parents fields used in raw SQL.
+        if query.model:
+            for parent in query.model._meta.get_parent_list():
+                for parent_field in parent._meta.local_fields:
+                    _, column_name = parent_field.get_attname_column()
+                    if column_name.lower() in self.sql.lower():
+                        query.resolve_ref(
+                            parent_field.name, allow_joins, reuse, summarize
+                        )
+                        break
+        return super().resolve_expression(
+            query, allow_joins, reuse, summarize, for_save
+        )
+
+
+class Star(Expression):
+    def __repr__(self):
+        return "'*'"
+
+    def as_sql(self, compiler, connection):
+        return "*", []
+
+
+class Col(Expression):
+
+    contains_column_references = True
+    possibly_multivalued = False
+
+    def __init__(self, alias, target, output_field=None):
+        if output_field is None:
+            output_field = target
+        super().__init__(output_field=output_field)
+        self.alias, self.target = alias, target
+
+    def __repr__(self):
+        alias, target = self.alias, self.target
+        identifiers = (alias, str(target)) if alias else (str(target),)
+        return "{}({})".format(self.__class__.__name__, ", ".join(identifiers))
+
+    def as_sql(self, compiler, connection):
+        alias, column = self.alias, self.target.column
+        identifiers = (alias, column) if alias else (column,)
+        sql = ".".join(map(compiler.quote_name_unless_alias, identifiers))
+        return sql, []
+
+    def relabeled_clone(self, relabels):
+        if self.alias is None:
+            return self
+        return self.__class__(
+            relabels.get(self.alias, self.alias), self.target, self.output_field
+        )
+
+    def get_group_by_cols(self, alias=None):
+        return [self]
+
+    def get_db_converters(self, connection):
+        if self.target == self.output_field:
+            return self.output_field.get_db_converters(connection)
+        return self.output_field.get_db_converters(
+            connection
+        ) + self.target.get_db_converters(connection)
+
+
+class Ref(Expression):
+    """
+    Reference to column alias of the query. For example, Ref('sum_cost') in
+    qs.annotate(sum_cost=Sum('cost')) query.
+    """
+
+    def __init__(self, refs, source):
+        super().__init__()
+        self.refs, self.source = refs, source
+
+    def __repr__(self):
+        return "{}({}, {})".format(self.__class__.__name__, self.refs, self.source)
+
+    def get_source_expressions(self):
+        return [self.source]
+
+    def set_source_expressions(self, exprs):
+        (self.source,) = exprs
+
+    def resolve_expression(
+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False
+    ):
+        # The sub-expression `source` has already been resolved, as this is
+        # just a reference to the name of `source`.
+        return self
+
+    def relabeled_clone(self, relabels):
+        return self
+
+    def as_sql(self, compiler, connection):
+        return connection.ops.quote_name(self.refs), []
+
+    def get_group_by_cols(self, alias=None):
+        return [self]
+
+
+class ExpressionList(Func):
+    """
+    An expression containing multiple expressions. Can be used to provide a
+    list of expressions as an argument to another expression, like a partition
+    clause.
+    """
+
+    template = "%(expressions)s"
+
+    def __init__(self, *expressions, **extra):
+        if not expressions:
+            raise ValueError(
+                "%s requires at least one expression." % self.__class__.__name__
+            )
+        super().__init__(*expressions, **extra)
+
+    def __str__(self):
+        return self.arg_joiner.join(str(arg) for arg in self.source_expressions)
+
+    def as_sqlite(self, compiler, connection, **extra_context):
+        # Casting to numeric is unnecessary.
+        return self.as_sql(compiler, connection, **extra_context)
+
+
+class OrderByList(Func):
+    template = "ORDER BY %(expressions)s"
+
+    def __init__(self, *expressions, **extra):
+        expressions = (
+            (
+                OrderBy(F(expr[1:]), descending=True)
+                if isinstance(expr, str) and expr[0] == "-"
+                else expr
+            )
+            for expr in expressions
+        )
+        super().__init__(*expressions, **extra)
+
+    def as_sql(self, *args, **kwargs):
+        if not self.source_expressions:
+            return "", ()
+        return super().as_sql(*args, **kwargs)
+
+
+@deconstructible(path="django.db.models.ExpressionWrapper")
+class ExpressionWrapper(SQLiteNumericMixin, Expression):
+    """
+    An expression that can wrap another expression so that it can provide
+    extra context to the inner expression, such as the output_field.
+    """
+
+    def __init__(self, expression, output_field):
+        super().__init__(output_field=output_field)
+        self.expression = expression
+
+    def set_source_expressions(self, exprs):
+        self.expression = exprs[0]
+
+    def get_source_expressions(self):
+        return [self.expression]
+
+    def get_group_by_cols(self, alias=None):
+        if isinstance(self.expression, Expression):
+            expression = self.expression.copy()
+            expression.output_field = self.output_field
+            return expression.get_group_by_cols(alias=alias)
+        # For non-expressions e.g. an SQL WHERE clause, the entire
+        # `expression` must be included in the GROUP BY clause.
+        return super().get_group_by_cols()
+
+    def as_sql(self, compiler, connection):
+        return compiler.compile(self.expression)
+
+    def __repr__(self):
+        return "{}({})".format(self.__class__.__name__, self.expression)
+
+
+@deconstructible(path="django.db.models.When")
+class When(Expression):
+    template = "WHEN %(condition)s THEN %(result)s"
+    # This isn't a complete conditional expression, must be used in Case().
+    conditional = False
+
+    def __init__(self, condition=None, then=None, **lookups):
+        if lookups:
+            if condition is None:
+                condition, lookups = Q(**lookups), None
+            elif getattr(condition, "conditional", False):
+                condition, lookups = Q(condition, **lookups), None
+        if condition is None or not getattr(condition, "conditional", False) or lookups:
+            raise TypeError(
+                "When() supports a Q object, a boolean expression, or lookups "
+                "as a condition."
+            )
+        if isinstance(condition, Q) and not condition:
+            raise ValueError("An empty Q() can't be used as a When() condition.")
+        super().__init__(output_field=None)
+        self.condition = condition
+        self.result = self._parse_expressions(then)[0]
+
+    def __str__(self):
+        return "WHEN %r THEN %r" % (self.condition, self.result)
+
+    def __repr__(self):
+        return "<%s: %s>" % (self.__class__.__name__, self)
+
+    def get_source_expressions(self):
+        return [self.condition, self.result]
+
+    def set_source_expressions(self, exprs):
+        self.condition, self.result = exprs
+
+    def get_source_fields(self):
+        # We're only interested in the fields of the result expressions.
+        return [self.result._output_field_or_none]
+
+    def resolve_expression(
+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False
+    ):
+        c = self.copy()
+        c.is_summary = summarize
+        if hasattr(c.condition, "resolve_expression"):
+            c.condition = c.condition.resolve_expression(
+                query, allow_joins, reuse, summarize, False
+            )
+        c.result = c.result.resolve_expression(
+            query, allow_joins, reuse, summarize, for_save
+        )
+        return c
+
+    def as_sql(self, compiler, connection, template=None, **extra_context):
+        connection.ops.check_expression_support(self)
+        template_params = extra_context
+        sql_params = []
+        condition_sql, condition_params = compiler.compile(self.condition)
+        template_params["condition"] = condition_sql
+        sql_params.extend(condition_params)
+        result_sql, result_params = compiler.compile(self.result)
+        template_params["result"] = result_sql
+        sql_params.extend(result_params)
+        template = template or self.template
+        return template % template_params, sql_params
+
+    def get_group_by_cols(self, alias=None):
+        # This is not a complete expression and cannot be used in GROUP BY.
+        cols = []
+        for source in self.get_source_expressions():
+            cols.extend(source.get_group_by_cols())
+        return cols
+
+
+@deconstructible(path="django.db.models.Case")
+class Case(SQLiteNumericMixin, Expression):
+    """
+    An SQL searched CASE expression:
+
+        CASE
+            WHEN n > 0
+                THEN 'positive'
+            WHEN n < 0
+                THEN 'negative'
+            ELSE 'zero'
+        END
+    """
+
+    template = "CASE %(cases)s ELSE %(default)s END"
+    case_joiner = " "
+
+    def __init__(self, *cases, default=None, output_field=None, **extra):
+        if not all(isinstance(case, When) for case in cases):
+            raise TypeError("Positional arguments must all be When objects.")
+        super().__init__(output_field)
+        self.cases = list(cases)
+        self.default = self._parse_expressions(default)[0]
+        self.extra = extra
+
+    def __str__(self):
+        return "CASE %s, ELSE %r" % (
+            ", ".join(str(c) for c in self.cases),
+            self.default,
+        )
+
+    def __repr__(self):
+        return "<%s: %s>" % (self.__class__.__name__, self)
+
+    def get_source_expressions(self):
+        return self.cases + [self.default]
+
+    def set_source_expressions(self, exprs):
+        *self.cases, self.default = exprs
+
+    def resolve_expression(
+        self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False
+    ):
+        c = self.copy()
+        c.is_summary = summarize
+        for pos, case in enumerate(c.cases):
+            c.cases[pos] = case.resolve_expression(
+                query, allow_joins, reuse, summarize, for_save
+            )
+        c.default = c.default.resolve_expression(
+            query, allow_joins, reuse, summarize, for_save
+        )
+        return c
+
+    def copy(self):
+        c = super().copy()
+        c.cases = c.cases[:]
+        return c
+
+    def as_sql(
+        self, compiler, connection, template=None, case_joiner=None, **extra_context
+    ):
+        connection.ops.check_expression_support(self)
+        if not self.cases:
+            return compiler.compile(self.default)
+        template_params = {**self.extra, **extra_context}
+        case_parts = []
+        sql_params = []
+        for case in self.cases:
+            try:
+                case_sql, case_params = compiler.compile(case)
+            except EmptyResultSet:
+                continue
+            case_parts.append(case_sql)
+            sql_params.extend(case_params)
+        default_sql, default_params = compiler.compile(self.default)
+        if not case_parts:
+            return default_sql, default_params
+        case_joiner = case_joiner or self.case_joiner
+        template_params["cases"] = case_joiner.join(case_parts)
+        template_params["default"] = default_sql
+        sql_params.extend(default_params)
+        template = template or template_params.get("template", self.template)
+        sql = template % template_params
+        if self._output_field_or_none is not None:
+            sql = connection.ops.unification_cast_sql(self.output_field) % sql
+        return sql, sql_params
+
+    def get_group_by_cols(self, alias=None):
+        if not self.cases:
+            return self.default.get_group_by_cols(alias)
+        return super().get_group_by_cols(alias)
+
+
+class Subquery(BaseExpression, Combinable):
+    """
+    An explicit subquery. It may contain OuterRef() references to the outer
+    query which will be resolved when it is applied to that query.
+    """
+
+    template = "(%(subquery)s)"
+    contains_aggregate = False
+    empty_result_set_value = None
+
+    def __init__(self, queryset, output_field=None, **extra):
+        # Allow the usage of both QuerySet and sql.Query objects.
+        self.query = getattr(queryset, "query", queryset).clone()
+        self.query.subquery = True
+        self.extra = extra
+        super().__init__(output_field)
+
+    def get_source_expressions(self):
+        return [self.query]
+
+    def set_source_expressions(self, exprs):
+        self.query = exprs[0]
+
+    def _resolve_output_field(self):
+        return self.query.output_field
+
+    def copy(self):
+        clone = super().copy()
+        clone.query = clone.query.clone()
+        return clone
+
+    @property
+    def external_aliases(self):
+        return self.query.external_aliases
+
+    def get_external_cols(self):
+        return self.query.get_external_cols()
+
+    def as_sql(self, compiler, connection, template=None, query=None, **extra_context):
+        connection.ops.check_expression_support(self)
+        template_params = {**self.extra, **extra_context}
+        query = query or self.query
+        subquery_sql, sql_params = query.as_sql(compiler, connection)
+        template_params["subquery"] = subquery_sql[1:-1]
+
+        template = template or template_params.get("template", self.template)
+        sql = template % template_params
+        return sql, sql_params
+
+    def get_group_by_cols(self, alias=None):
+        # If this expression is referenced by an alias for an explicit GROUP BY
+        # through values() a reference to this expression and not the
+        # underlying .query must be returned to ensure external column
+        # references are not grouped against as well.
+        if alias:
+            return [Ref(alias, self)]
+        return self.query.get_group_by_cols()
+
+
+class Exists(Subquery):
+    template = "EXISTS(%(subquery)s)"
+    output_field = fields.BooleanField()
+
+    def __init__(self, queryset, negated=False, **kwargs):
+        self.negated = negated
+        super().__init__(queryset, **kwargs)
+
+    def __invert__(self):
+        clone = self.copy()
+        clone.negated = not self.negated
+        return clone
+
+    def as_sql(self, compiler, connection, template=None, **extra_context):
+        query = self.query.exists(using=connection.alias)
+        try:
+            sql, params = super().as_sql(
+                compiler,
+                connection,
+                template=template,
+                query=query,
+                **extra_context,
+            )
+        except EmptyResultSet:
+            if self.negated:
+                features = compiler.connection.features
+                if not features.supports_boolean_expr_in_select_clause:
+                    return "1=1", ()
+                return compiler.compile(Value(True))
+            raise
+        if self.negated:
+            sql = "NOT {}".format(sql)
+        return sql, params
+
+    def select_format(self, compiler, sql, params):
+        # Wrap EXISTS() with a CASE WHEN expression if a database backend
+        # (e.g. Oracle) doesn't support boolean expression in SELECT or GROUP
+        # BY list.
+        if not compiler.connection.features.supports_boolean_expr_in_select_clause:
+            sql = "CASE WHEN {} THEN 1 ELSE 0 END".format(sql)
+        return sql, params
+
+
+@deconstructible(path="django.db.models.OrderBy")
+class OrderBy(Expression):
+    template = "%(expression)s %(ordering)s"
+    conditional = False
+
+    def __init__(self, expression, descending=False, nulls_first=None, nulls_last=None):
+        if nulls_first and nulls_last:
+            raise ValueError("nulls_first and nulls_last are mutually exclusive")
+        if nulls_first is False or nulls_last is False:
+            # When the deprecation ends, replace with:
+            # raise ValueError(
+            #     "nulls_first and nulls_last values must be True or None."
+            # )
+            warnings.warn(
+                "Passing nulls_first=False or nulls_last=False is deprecated, use None "
+                "instead.",
+                RemovedInDjango50Warning,
+                stacklevel=2,
+            )
+        self.nulls_first = nulls_first
+        self.nulls_last = nulls_last
+        self.descending = descending
+        if not hasattr(expression, "resolve_expression"):
+            raise ValueError("expression must be an expression type")
+        self.expression = expression
+
+    def __repr__(self):
+        return "{}({}, descending={})".format(
+            self.__class__.__name__, self.expression, self.descending
+        )
+
+    def set_source_expressions(self, exprs):
+        self.expression = exprs[0]
+
+    def get_source_expressions(self):
+        return [self.expression]
+
+    def as_sql(self, compiler, connection, template=None, **extra_context):
+        template = template or self.template
+        if connection.features.supports_order_by_nulls_modifier:
+            if self.nulls_last:
+                template = "%s NULLS LAST" % template
+            elif self.nulls_first:
+                template = "%s NULLS FIRST" % template
+        else:
+            if self.nulls_last and not (
+                self.descending and connection.features.order_by_nulls_first
+            ):
+                template = "%%(expression)s IS NULL, %s" % template
+            elif self.nulls_first and not (
+                not self.descending and connection.features.order_by_nulls_first
+            ):
+                template = "%%(expression)s IS NOT NULL, %s" % template
+        connection.ops.check_expression_support(self)
+        expression_sql, params = compiler.compile(self.expression)
+        placeholders = {
+            "expression": expression_sql,
+            "ordering": "DESC" if self.descending else "ASC",
+            **extra_context,
+        }
+        params *= template.count("%(expression)s")
+        return (template % placeholders).rstrip(), params
+
+    def as_oracle(self, compiler, connection):
+        # Oracle doesn't allow ORDER BY EXISTS() or filters unless it's wrapped
+        # in a CASE WHEN.
+        if connection.ops.conditional_expression_supported_in_where_clause(
+            self.expression
+        ):
+            copy = self.copy()
+            copy.expression = Case(
+                When(self.expression, then=True),
+                default=False,
+            )
+            return copy.as_sql(compiler, connection)
+        return self.as_sql(compiler, connection)
+
+    def get_group_by_cols(self, alias=None):
+        cols = []
+        for source in self.get_source_expressions():
+            cols.extend(source.get_group_by_cols())
+        return cols
+
+    def reverse_ordering(self):
+        self.descending = not self.descending
+        if self.nulls_first:
+            self.nulls_last = True
+            self.nulls_first = None
+        elif self.nulls_last:
+            self.nulls_first = True
+            self.nulls_last = None
+        return self
+
+    def asc(self):
+        self.descending = False
+
+    def desc(self):
+        self.descending = True
+
+
+class Window(SQLiteNumericMixin, Expression):
+    template = "%(expression)s OVER (%(window)s)"
+    # Although the main expression may either be an aggregate or an
+    # expression with an aggregate function, the GROUP BY that will
+    # be introduced in the query as a result is not desired.
+    contains_aggregate = False
+    contains_over_clause = True
+    filterable = False
+
+    def __init__(
+        self,
+        expression,
+        partition_by=None,
+        order_by=None,
+        frame=None,
+        output_field=None,
+    ):
+        self.partition_by = partition_by
+        self.order_by = order_by
+        self.frame = frame
+
+        if not getattr(expression, "window_compatible", False):
+            raise ValueError(
+                "Expression '%s' isn't compatible with OVER clauses."
+                % expression.__class__.__name__
+            )
+
+        if self.partition_by is not None:
+            if not isinstance(self.partition_by, (tuple, list)):
+                self.partition_by = (self.partition_by,)
+            self.partition_by = ExpressionList(*self.partition_by)
+
+        if self.order_by is not None:
+            if isinstance(self.order_by, (list, tuple)):
+                self.order_by = OrderByList(*self.order_by)
+            elif isinstance(self.order_by, (BaseExpression, str)):
+                self.order_by = OrderByList(self.order_by)
+            else:
+                raise ValueError(
+                    "Window.order_by must be either a string reference to a "
+                    "field, an expression, or a list or tuple of them."
+                )
+        super().__init__(output_field=output_field)
+        self.source_expression = self._parse_expressions(expression)[0]
+
+    def _resolve_output_field(self):
+        return self.source_expression.output_field
+
+    def get_source_expressions(self):
+        return [self.source_expression, self.partition_by, self.order_by, self.frame]
+
+    def set_source_expressions(self, exprs):
+        self.source_expression, self.partition_by, self.order_by, self.frame = exprs
+
+    def as_sql(self, compiler, connection, template=None):
+        connection.ops.check_expression_support(self)
+        if not connection.features.supports_over_clause:
+            raise NotSupportedError("This backend does not support window expressions.")
+        expr_sql, params = compiler.compile(self.source_expression)
+        window_sql, window_params = [], ()
+
+        if self.partition_by is not None:
+            sql_expr, sql_params = self.partition_by.as_sql(
+                compiler=compiler,
+                connection=connection,
+                template="PARTITION BY %(expressions)s",
+            )
+            window_sql.append(sql_expr)
+            window_params += tuple(sql_params)
+
+        if self.order_by is not None:
+            order_sql, order_params = compiler.compile(self.order_by)
+            window_sql.append(order_sql)
+            window_params += tuple(order_params)
+
+        if self.frame:
+            frame_sql, frame_params = compiler.compile(self.frame)
+            window_sql.append(frame_sql)
+            window_params += tuple(frame_params)
+
+        template = template or self.template
+
+        return (
+            template % {"expression": expr_sql, "window": " ".join(window_sql).strip()},
+            (*params, *window_params),
+        )
+
+    def as_sqlite(self, compiler, connection):
+        if isinstance(self.output_field, fields.DecimalField):
+            # Casting to numeric must be outside of the window expression.
+            copy = self.copy()
+            source_expressions = copy.get_source_expressions()
+            source_expressions[0].output_field = fields.FloatField()
+            copy.set_source_expressions(source_expressions)
+            return super(Window, copy).as_sqlite(compiler, connection)
+        return self.as_sql(compiler, connection)
+
+    def __str__(self):
+        return "{} OVER ({}{}{})".format(
+            str(self.source_expression),
+            "PARTITION BY " + str(self.partition_by) if self.partition_by else "",
+            str(self.order_by or ""),
+            str(self.frame or ""),
+        )
+
+    def __repr__(self):
+        return "<%s: %s>" % (self.__class__.__name__, self)
+
+    def get_group_by_cols(self, alias=None):
+        return []
+
+
+class WindowFrame(Expression):
+    """
+    Model the frame clause in window expressions. There are two types of frame
+    clauses which are subclasses, however, all processing and validation (by no
+    means intended to be complete) is done here. Thus, providing an end for a
+    frame is optional (the default is UNBOUNDED FOLLOWING, which is the last
+    row in the frame).
+    """
+
+    template = "%(frame_type)s BETWEEN %(start)s AND %(end)s"
+
+    def __init__(self, start=None, end=None):
+        self.start = Value(start)
+        self.end = Value(end)
+
+    def set_source_expressions(self, exprs):
+        self.start, self.end = exprs
+
+    def get_source_expressions(self):
+        return [self.start, self.end]
+
+    def as_sql(self, compiler, connection):
+        connection.ops.check_expression_support(self)
+        start, end = self.window_frame_start_end(
+            connection, self.start.value, self.end.value
+        )
+        return (
+            self.template
+            % {
+                "frame_type": self.frame_type,
+                "start": start,
+                "end": end,
+            },
+            [],
+        )
+
+    def __repr__(self):
+        return "<%s: %s>" % (self.__class__.__name__, self)
+
+    def get_group_by_cols(self, alias=None):
+        return []
+
+    def __str__(self):
+        if self.start.value is not None and self.start.value < 0:
+            start = "%d %s" % (abs(self.start.value), connection.ops.PRECEDING)
+        elif self.start.value is not None and self.start.value == 0:
+            start = connection.ops.CURRENT_ROW
+        else:
+            start = connection.ops.UNBOUNDED_PRECEDING
+
+        if self.end.value is not None and self.end.value > 0:
+            end = "%d %s" % (self.end.value, connection.ops.FOLLOWING)
+        elif self.end.value is not None and self.end.value == 0:
+            end = connection.ops.CURRENT_ROW
+        else:
+            end = connection.ops.UNBOUNDED_FOLLOWING
+        return self.template % {
+            "frame_type": self.frame_type,
+            "start": start,
+            "end": end,
+        }
+
+    def window_frame_start_end(self, connection, start, end):
+        raise NotImplementedError("Subclasses must implement window_frame_start_end().")
+
+
+class RowRange(WindowFrame):
+    frame_type = "ROWS"
+
+    def window_frame_start_end(self, connection, start, end):
+        return connection.ops.window_frame_rows_start_end(start, end)
+
+
+class ValueRange(WindowFrame):
+    frame_type = "RANGE"
+
+    def window_frame_start_end(self, connection, start, end):
+        return connection.ops.window_frame_range_start_end(start, end)
diff --git a/django/db/models/expressions_fix.patch b/django/db/models/expressions_fix.patch
new file mode 100644
index 0000000000..4cb922d3a7
--- /dev/null
+++ b/django/db/models/expressions_fix.patch
@@ -0,0 +1,13 @@
+diff --git a/django/db/models/expressions.py b/django/db/models/expressions.py
+--- a/django/db/models/expressions.py
++++ b/django/db/models/expressions.py
+@@ -1193,6 +1193,9 @@ class When(Expression):
+         template_params = extra_context
+         sql_params = []
+         condition_sql, condition_params = compiler.compile(self.condition)
++        # Handle the case of empty/negated empty IN clause
++        if not condition_sql.strip():
++            condition_sql = "TRUE"
+         template_params["condition"] = condition_sql
+         sql_params.extend(condition_params)
+         result_sql, result_params = compiler.compile(self.result)
\ No newline at end of file
diff --git a/tests/expressions_case/test_empty_in.py b/tests/expressions_case/test_empty_in.py
new file mode 100644
index 0000000000..33d50b9670
--- /dev/null
+++ b/tests/expressions_case/test_empty_in.py
@@ -0,0 +1,25 @@
+from django.db.models import BooleanField, Case, Q, Value, When
+from django.test import TestCase
+
+from .models import CaseTestModel
+
+class EmptyInNegationTests(TestCase):
+    @classmethod
+    def setUpTestData(cls):
+        CaseTestModel.objects.create(integer=1, integer2=1, string="1")
+        CaseTestModel.objects.create(integer=2, integer2=2, string="2")
+
+    def test_negated_empty_in_case(self):
+        """
+        Test that Case-When with ~Q(pk__in=[]) works correctly.
+        The expression should evaluate to True for all rows.
+        """
+        qs = CaseTestModel.objects.annotate(
+            test=Case(
+                When(~Q(pk__in=[]), then=Value(True)),
+                default=Value(False),
+                output_field=BooleanField(),
+            )
+        ).order_by('pk').values_list('pk', 'test')
+
+        self.assertQuerysetEqual(qs, [(1, True), (2, True)], transform=tuple)
\ No newline at end of file

2025-03-14 14:14:08,234 - ThreadPoolExecutor-4_0 - INFO - Attempting to stop container sweb.eval.django__django-15930.20250314_141221_446860...
2025-03-14 14:14:23,405 - ThreadPoolExecutor-4_0 - INFO - Attempting to remove container sweb.eval.django__django-15930.20250314_141221_446860...
2025-03-14 14:14:23,714 - ThreadPoolExecutor-4_0 - INFO - Container sweb.eval.django__django-15930.20250314_141221_446860 removed.
