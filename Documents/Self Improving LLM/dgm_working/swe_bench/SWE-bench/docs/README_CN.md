<p align="center">
  <a href="https://github.com/princeton-nlp/Llamao">
    <img src="assets/figures/swellama_banner.png" width="50%" alt="Kawi the SWE-Llama" />
  </a>
</p>

<div align="center">

 | [日本語](docs/README_JP.md) | [English](https://github.com/princeton-nlp/SWE-bench) | [中文简体](docs/README_CN.md) | [中文繁體](docs/README_TW.md) |

</div>


---
<p align="center">
您可以在我们的ICLR 2024的论文<a href="http://swe-bench.github.io/paper.pdf">《SWE-bench: Can Language Models Resolve Real-World GitHub Issues?》</a>中找到我们的代码和数据
    </br>
    </br>
    <a href="https://www.python.org/">
        <img alt="Build" src="https://img.shields.io/badge/Python-3.8+-1f425f.svg?color=purple">
    </a>
    <a href="https://copyright.princeton.edu/policy">
        <img alt="License" src="https://img.shields.io/badge/License-MIT-blue">
    </a>
    <a href="https://badge.fury.io/py/swebench">
        <img src="https://badge.fury.io/py/swebench.svg">
    </a>
</p>

请访问我们的[网站](http://swe-bench.github.io)查看公共排行榜，并查看[更改日志](https://github.com/princeton-nlp/SWE-bench/blob/master/CHANGELOG.md)以获取有关 SWE-bench 基准最新更新的信息。

## 👋 概述

SWE-bench是一个用于评估大型语言模型在真实世界软件问题上表现的基准测试,这些问题收集自GitHub。
给定一个*代码库*和一个*问题*,语言模型的任务是生成一个*补丁*来解决描述的问题。

<img src="../assets/figures/teaser.png">

要访问SWE-bench,复制并运行以下代码:
```python
from datasets import load_dataset
swebench = load_dataset('princeton-nlp/SWE-bench', split='test')
```

## 🚀 设置

SWE-bench使用Docker进行可重现的评估。
按照[Docker安装指南](https://docs.docker.com/engine/install/)中的说明在你的机器上安装Docker。
如果你在Linux上进行设置,我们建议你也查看[安装后步骤](https://docs.docker.com/engine/install/linux-postinstall/)。

最后,要从源代码构建SWE-bench,请按照以下步骤操作:
```bash
<NAME_EMAIL>:princeton-nlp/SWE-bench.git
cd SWE-bench
pip install -e .
```

通过运行以下命令测试你的安装:
```bash
python -m swebench.harness.run_evaluation \
    --predictions_path gold \
    --max_workers 1 \
    --instance_ids sympy__sympy-20590 \
    --run_id validate-gold
```

## 💽 使用

> [!警告]
> 在SWE-bench上运行快速评估可能会消耗大量资源
> 我们建议在一台具有至少120GB可用存储空间、16GB RAM和8个CPU核心的`x86_64`机器上运行评估工具。
> 你可能需要尝试调整`--max_workers`参数以找到适合你机器的最佳工作进程数,但我们建议使用少于`min(0.75 * os.cpu_count(), 24)`的数值。
>
> 如果使用docker desktop运行,请确保增加你的虚拟磁盘空间以有约120GB的可用空间,并根据上述建议为docker设置可用的CPU数量来设置max_workers。
>
> 对`arm64`机器的支持仍处于实验阶段。

使用以下命令通过评估工具在SWE-bench Lite上评估模型预测:
```bash
python -m swebench.harness.run_evaluation \
    --dataset_name princeton-nlp/SWE-bench_Lite \
    --predictions_path <预测结果路径> \
    --max_workers <工作进程数> \
    --run_id <运行ID>
    # 使用 --predictions_path 'gold' 来验证黄金补丁
    # 使用 --run_id 来命名评估运行
```

这个命令将在当前目录中生成docker构建日志(`logs/build_images`)和评估日志(`logs/run_evaluation`)。

最终的评估结果将存储在`evaluation_results`目录中。

要查看评估工具的完整参数列表,请运行:
```bash
python -m swebench.harness.run_evaluation --help
```

此外,SWE-Bench仓库还可以帮助你:
* 在我们预处理的数据集上训练你自己的模型
* 在现有模型上运行[推理](https://github.com/princeton-nlp/SWE-bench/blob/main/swebench/inference/README.md)(无论是你本地的模型如LLaMA,还是你通过API访问的模型如GPT-4)。推理步骤是指给定一个仓库和一个问题,让模型尝试生成修复方案。
* 在你自己的仓库上运行SWE-bench的[数据收集程序](https://github.com/princeton-nlp/SWE-bench/blob/main/swebench/collect/),以创建新的SWE-Bench任务。

## ⬇️ 下载

| 数据集 | 模型 |
| - | - |
| [🤗 SWE-bench](https://huggingface.co/datasets/princeton-nlp/SWE-bench) | [🦙 SWE-Llama 13b](https://huggingface.co/princeton-nlp/SWE-Llama-13b) |
| [🤗 "Oracle" 检索](https://huggingface.co/datasets/princeton-nlp/SWE-bench_oracle) | [🦙 SWE-Llama 13b (PEFT)](https://huggingface.co/princeton-nlp/SWE-Llama-13b-peft) |
| [🤗 BM25 检索 13K](https://huggingface.co/datasets/princeton-nlp/SWE-bench_bm25_13K) | [🦙 SWE-Llama 7b](https://huggingface.co/princeton-nlp/SWE-Llama-7b) |
| [🤗 BM25 检索 27K](https://huggingface.co/datasets/princeton-nlp/SWE-bench_bm25_27K) | [🦙 SWE-Llama 7b (PEFT)](https://huggingface.co/princeton-nlp/SWE-Llama-7b-peft) |
| [🤗 BM25 检索 40K](https://huggingface.co/datasets/princeton-nlp/SWE-bench_bm25_40K) | |
| [🤗 BM25 检索 50K (Llama tokens)](https://huggingface.co/datasets/princeton-nlp/SWE-bench_bm25_50k_llama)   | |

## 🍎 教程

我们还编写了以下博客文章,介绍如何使用SWE-bench的不同部分。
如果你想看到关于特定主题的文章,请通过issue告诉我们。
* [2023年11月1日] 为SWE-Bench收集评估任务 ([🔗](https://github.com/princeton-nlp/SWE-bench/blob/main/assets/collection.md))
* [2023年11月6日] 在SWE-bench上进行评估 ([🔗](https://github.com/princeton-nlp/SWE-bench/blob/main/assets/evaluation.md))

## 💫 贡献

我们很乐意听取更广泛的NLP、机器学习和软件工程研究社区的意见,我们欢迎任何贡献、拉取请求或问题！
要这样做,请提交新的拉取请求或问题,并相应地填写对应的模板。我们会尽快跟进！

联系人: [Carlos E. Jimenez](http://www.carlosejimenez.com/) 和 [John Yang](https://john-b-yang.github.io/) (邮箱: <EMAIL>, <EMAIL>)。

## ✍️ 引用

如果你觉得我们的工作有帮助,请使用以下引用。
```
@inproceedings{
    jimenez2024swebench,
    title={{SWE}-bench: Can Language Models Resolve Real-world Github Issues?},
    author={Carlos E Jimenez and John Yang and Alexander Wettig and Shunyu Yao and Kexin Pei and Ofir Press and Karthik R Narasimhan},
    booktitle={The Twelfth International Conference on Learning Representations},
    year={2024},
    url={https://openreview.net/forum?id=VTF8yNQM66}
}
```

## 🪪 许可证

MIT。查看`LICENSE.md`。