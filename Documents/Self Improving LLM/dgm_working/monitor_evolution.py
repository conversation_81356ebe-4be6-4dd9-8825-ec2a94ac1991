#!/usr/bin/env python3
"""
Real-time Darwin Gödel Machine Evolution Monitor
Shows detailed statistics and progress as the DGM evolves.
"""

import os
import time
import json
import glob
from pathlib import Path
from datetime import datetime
import subprocess

class DGMMonitor:
    def __init__(self):
        self.start_time = time.time()
        self.last_generation = -1
        self.patch_history = []
        
    def get_run_directories(self):
        """Get all DGM run directories."""
        return glob.glob("output_dgm/*/")
    
    def get_generation_directories(self, run_dir):
        """Get generation directories for a specific run."""
        return sorted(glob.glob(f"{run_dir}/generation_*"), 
                     key=lambda x: int(x.split('_')[-1]) if x.split('_')[-1].isdigit() else -1)
    
    def analyze_patch(self, patch_file):
        """Analyze a patch file and return statistics."""
        if not os.path.exists(patch_file):
            return {"lines": 0, "files": 0, "additions": 0, "deletions": 0}
        
        try:
            with open(patch_file, 'r') as f:
                content = f.read()
            
            lines = len(content.splitlines())
            files = content.count('diff --git')
            additions = content.count('\n+') - content.count('\n+++')
            deletions = content.count('\n-') - content.count('\n---')
            
            return {
                "lines": lines,
                "files": files,
                "additions": additions,
                "deletions": deletions,
                "size_bytes": len(content)
            }
        except Exception as e:
            return {"lines": 0, "files": 0, "additions": 0, "deletions": 0, "error": str(e)}
    
    def get_evaluation_results(self, run_dir):
        """Get evaluation results from metadata files."""
        results = {}
        metadata_files = glob.glob(f"{run_dir}/*/metadata.json")
        
        for metadata_file in metadata_files:
            try:
                with open(metadata_file, 'r') as f:
                    data = json.load(f)
                
                run_id = os.path.basename(os.path.dirname(metadata_file))
                results[run_id] = {
                    "score": data.get("score", 0),
                    "task": data.get("entry", "unknown"),
                    "parent": data.get("parent_commit", "unknown"),
                    "timestamp": data.get("timestamp", "unknown")
                }
            except Exception:
                continue
        
        return results
    
    def get_current_status(self):
        """Get current DGM status."""
        run_dirs = self.get_run_directories()
        if not run_dirs:
            return None
        
        # Get the most recent run
        latest_run = max(run_dirs, key=lambda x: os.path.getctime(x))
        
        # Get generations
        generations = self.get_generation_directories(latest_run)
        current_generation = len(generations)
        
        # Get evaluation results
        eval_results = self.get_evaluation_results(latest_run)
        
        # Analyze patches
        patch_stats = []
        for gen_dir in generations:
            patches = glob.glob(f"{gen_dir}/*/model_patch.diff")
            for patch in patches:
                stats = self.analyze_patch(patch)
                stats["generation"] = int(gen_dir.split('_')[-1])
                stats["run_id"] = os.path.basename(os.path.dirname(patch))
                patch_stats.append(stats)
        
        return {
            "run_dir": latest_run,
            "current_generation": current_generation,
            "total_attempts": len(eval_results),
            "patch_stats": patch_stats,
            "eval_results": eval_results,
            "generations": generations
        }
    
    def check_process_status(self):
        """Check if DGM process is still running."""
        try:
            result = subprocess.run(['pgrep', '-f', 'DGM_outer.py'], 
                                  capture_output=True, text=True)
            return len(result.stdout.strip()) > 0
        except:
            return False
    
    def format_time(self, seconds):
        """Format seconds into human readable time."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    
    def print_header(self):
        """Print the monitor header."""
        print("\n" + "="*80)
        print("🧬 DARWIN GÖDEL MACHINE - EVOLUTION MONITOR")
        print("="*80)
        print(f"🕐 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 Target: 50 generations of self-improvement")
        print(f"🤖 Model: qwen3:30b-a3b (local)")
        print("="*80)
    
    def print_status(self, status):
        """Print current status."""
        if not status:
            print("❌ No DGM runs found")
            return
        
        runtime = time.time() - self.start_time
        process_running = self.check_process_status()
        
        print(f"\n📊 **GENERATION {status['current_generation']}** | "
              f"Runtime: {self.format_time(runtime)} | "
              f"Process: {'🟢 RUNNING' if process_running else '🔴 STOPPED'}")
        print("-" * 80)
        
        # Generation progress
        print(f"🧬 Generations: {status['current_generation']}/50 "
              f"({status['current_generation']/50*100:.1f}%)")
        
        # Patch statistics
        if status['patch_stats']:
            total_patches = len(status['patch_stats'])
            non_empty_patches = len([p for p in status['patch_stats'] if p['lines'] > 0])
            avg_lines = sum(p['lines'] for p in status['patch_stats']) / total_patches if total_patches > 0 else 0
            
            print(f"🔧 Patches: {non_empty_patches}/{total_patches} non-empty "
                  f"(avg: {avg_lines:.1f} lines)")
            
            # Recent patches
            recent_patches = sorted(status['patch_stats'], 
                                  key=lambda x: x['generation'], reverse=True)[:3]
            
            print("📝 Recent patches:")
            for patch in recent_patches:
                if patch['lines'] > 0:
                    print(f"   Gen {patch['generation']}: {patch['lines']} lines, "
                          f"{patch['files']} files (+{patch['additions']}/-{patch['deletions']})")
                else:
                    print(f"   Gen {patch['generation']}: Empty patch")
        
        # Evaluation results
        if status['eval_results']:
            scores = [r['score'] for r in status['eval_results'].values() if isinstance(r['score'], (int, float))]
            if scores:
                avg_score = sum(scores) / len(scores)
                max_score = max(scores)
                print(f"📈 Scores: avg={avg_score:.3f}, max={max_score:.3f} ({len(scores)} evals)")
        
        # Latest activity
        latest_gen = status['current_generation'] - 1
        if latest_gen >= 0:
            print(f"🎯 Latest: Generation {latest_gen}")
            
            # Show tasks being worked on
            recent_results = {k: v for k, v in status['eval_results'].items() 
                            if 'generation' not in k or str(latest_gen) in k}
            if recent_results:
                tasks = set(r['task'] for r in recent_results.values())
                print(f"📋 Tasks: {', '.join(list(tasks)[:3])}")
    
    def run_monitor(self, refresh_interval=30):
        """Run the monitoring loop."""
        self.print_header()
        
        try:
            while True:
                status = self.get_current_status()
                self.print_status(status)
                
                # Check if we should continue monitoring
                if status and status['current_generation'] >= 50:
                    print("\n🎉 **EVOLUTION COMPLETE!** Reached 50 generations!")
                    break
                
                if not self.check_process_status():
                    print("\n⚠️  DGM process not detected. Monitoring will continue...")
                
                print(f"\n⏱️  Next update in {refresh_interval}s... (Ctrl+C to stop)")
                time.sleep(refresh_interval)
                
                # Clear screen for next update
                os.system('clear' if os.name == 'posix' else 'cls')
                self.print_header()
                
        except KeyboardInterrupt:
            print("\n\n🛑 Monitoring stopped by user")
        except Exception as e:
            print(f"\n❌ Monitor error: {e}")

def main():
    """Main function."""
    monitor = DGMMonitor()
    
    # Check if we're in the right directory
    if not os.path.exists("DGM_outer.py"):
        print("❌ Please run this script from the dgm_working directory")
        return
    
    print("🔍 Starting DGM Evolution Monitor...")
    monitor.run_monitor(refresh_interval=30)

if __name__ == "__main__":
    main()
