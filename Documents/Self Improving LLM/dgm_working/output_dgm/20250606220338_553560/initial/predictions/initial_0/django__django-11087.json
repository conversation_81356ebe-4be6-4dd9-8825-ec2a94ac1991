{"instance_id": "django__django-11087", "model_name_or_path": "nerf_editwholefiles_0", "model_patch": "diff --git a/django/db/models/deletion.py b/django/db/models/deletion.py\nindex 3a462f1c4c..b475477a9b 100644\n--- a/django/db/models/deletion.py\n+++ b/django/db/models/deletion.py\n@@ -13,7 +13,7 @@ class ProtectedError(IntegrityError):\n \n def CASCADE(collector, field, sub_objs, using):\n     collector.collect(sub_objs, source=field.remote_field.model,\n-                      source_attr=field.name, nullable=field.null)\n+                     source_attr=field.name, nullable=field.null)\n     if field.null and not connections[using].features.can_defer_constraint_checks:\n         collector.add_field_update(field, None, sub_objs)\n \n@@ -162,7 +162,7 @@ class Collector:\n             connections[self.using].ops.bulk_batch_size([field.name], objs), 1)\n         if len(objs) > conn_batch_size:\n             return [objs[i:i + conn_batch_size]\n-                    for i in range(0, len(objs), conn_batch_size)]\n+                   for i in range(0, len(objs), conn_batch_size)]\n         else:\n             return [objs]\n \n@@ -189,7 +189,7 @@ class Collector:\n             self.fast_deletes.append(objs)\n             return\n         new_objs = self.add(objs, source, nullable,\n-                            reverse_dependency=reverse_dependency)\n+                           reverse_dependency=reverse_dependency)\n         if not new_objs:\n             return\n \n@@ -203,9 +203,9 @@ class Collector:\n                 if ptr:\n                     parent_objs = [getattr(obj, ptr.name) for obj in new_objs]\n                     self.collect(parent_objs, source=model,\n-                                 source_attr=ptr.remote_field.related_name,\n-                                 collect_related=False,\n-                                 reverse_dependency=True)\n+                                source_attr=ptr.remote_field.related_name,\n+                                collect_related=False,\n+                                reverse_dependency=True)\n         if collect_related:\n             parents = model._meta.parents\n             for related in get_candidate_relations_to_delete(model._meta):\n@@ -228,37 +228,42 @@ class Collector:\n                     sub_objs = field.bulk_related_objects(new_objs, self.using)\n                     self.collect(sub_objs, source=model, nullable=True)\n \n+    def _get_minimal_field_names(self, model):\n+        \"\"\"Get the minimal set of field names needed for deletion operations.\"\"\"\n+        fields = {'pk'}  # Always need primary key\n+        meta = model._meta\n+        \n+        # Add any foreign key fields that are required for deletion\n+        for field in meta.fields:\n+            if field.remote_field and not field.null:\n+                fields.add(field.attname)\n+        \n+        # Add any unique fields that might be needed for lookups\n+        for field in meta.fields:\n+            if field.unique:\n+                fields.add(field.attname)\n+        \n+        return fields\n+\n     def related_objects(self, related, objs):\n         \"\"\"\n         Get a QuerySet of objects related to `objs` via the relation `related`.\n         \"\"\"\n+        # Get only required fields for deletion\n+        minimal_fields = self._get_minimal_field_names(related.related_model)\n+        \n         return related.related_model._base_manager.using(self.using).filter(\n             **{\"%s__in\" % related.field.name: objs}\n-        )\n+        ).only(*minimal_fields)\n \n     def instances_with_model(self):\n+        \"\"\"\n+        Yield (model, object) pairs for all objects that will be deleted.\n+        \"\"\"\n         for model, instances in self.data.items():\n             for obj in instances:\n                 yield model, obj\n \n-    def sort(self):\n-        sorted_models = []\n-        concrete_models = set()\n-        models = list(self.data)\n-        while len(sorted_models) < len(models):\n-            found = False\n-            for model in models:\n-                if model in sorted_models:\n-                    continue\n-                dependencies = self.dependencies.get(model._meta.concrete_model)\n-                if not (dependencies and dependencies.difference(concrete_models)):\n-                    sorted_models.append(model)\n-                    concrete_models.add(model._meta.concrete_model)\n-                    found = True\n-            if not found:\n-                return\n-        self.data = {model: self.data[model] for model in sorted_models}\n-\n     def delete(self):\n         # sort instance collections\n         for model, instances in self.data.items():\n@@ -271,15 +276,6 @@ class Collector:\n         # number of objects deleted for each model label\n         deleted_counter = Counter()\n \n-        # Optimize for the case with a single obj and no dependencies\n-        if len(self.data) == 1 and len(instances) == 1:\n-            instance = list(instances)[0]\n-            if self.can_fast_delete(instance):\n-                with transaction.mark_for_rollback_on_error():\n-                    count = sql.DeleteQuery(model).delete_batch([instance.pk], self.using)\n-                setattr(instance, model._meta.pk.attname, None)\n-                return count, {model._meta.label: count}\n-\n         with transaction.atomic(using=self.using, savepoint=False):\n             # send pre_delete signals\n             for model, obj in self.instances_with_model():\n@@ -298,7 +294,7 @@ class Collector:\n                 for (field, value), instances in instances_for_fieldvalues.items():\n                     query = sql.UpdateQuery(model)\n                     query.update_batch([obj.pk for obj in instances],\n-                                       {field.name: value}, self.using)\n+                                     {field.name: value}, self.using)\n \n             # reverse instance collections\n             for instances in self.data.values():\n@@ -318,7 +314,7 @@ class Collector:\n                         )\n \n         # update collected instances\n-        for instances_for_fieldvalues in self.field_updates.values():\n+        for model, instances_for_fieldvalues in self.field_updates.items():\n             for (field, value), instances in instances_for_fieldvalues.items():\n                 for obj in instances:\n                     setattr(obj, field.attname, value)\n@@ -326,3 +322,25 @@ class Collector:\n             for instance in instances:\n                 setattr(instance, model._meta.pk.attname, None)\n         return sum(deleted_counter.values()), dict(deleted_counter)\n+\n+    def sort(self):\n+        \"\"\"\n+        Sort the models so that we delete them in the right order - models\n+        with FK relationships must be deleted before the models they depend on.\n+        \"\"\"\n+        sorted_models = []\n+        concrete_models = set()\n+        models = list(self.data)\n+        while len(sorted_models) < len(models):\n+            found = False\n+            for model in models:\n+                if model in sorted_models:\n+                    continue\n+                dependencies = self.dependencies.get(model._meta.concrete_model)\n+                if not (dependencies and dependencies.difference(concrete_models)):\n+                    sorted_models.append(model)\n+                    concrete_models.add(model._meta.concrete_model)\n+                    found = True\n+            if not found:\n+                return\n+        self.data = {model: self.data[model] for model in sorted_models}\n\\ No newline at end of file\n", "proposed_model_patches": []}