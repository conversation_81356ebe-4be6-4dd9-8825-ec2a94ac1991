#!/usr/bin/env python3
"""
Darwin Gödel Machine Progress Analyzer
Analyzes the evolution progress and performance metrics across generations.
"""

import os
import json
import glob
from collections import defaultdict
from datetime import datetime
import re

def analyze_dgm_progress(run_dir):
    """Analyze DGM progress for a specific run."""
    
    print(f"🔬 Analyzing DGM Progress for: {os.path.basename(run_dir)}")
    print("=" * 60)
    
    # 1. Generation Timeline Analysis
    log_file = os.path.join(run_dir, "dgm_outer.log")
    generations = []
    
    if os.path.exists(log_file):
        with open(log_file, 'r') as f:
            for line in f:
                if "Self-improve entries for generation" in line:
                    # Extract generation number and timestamp
                    match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d+ .* generation (\d+):', line)
                    if match:
                        timestamp_str, gen_num = match.groups()
                        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                        
                        # Extract entries
                        entries_match = re.search(r'\[(.*)\]', line)
                        entries = []
                        if entries_match:
                            entries_str = entries_match.group(1)
                            # Parse entries like ('initial', 'solve_empty_patches'), ('initial', 'django__django-11815')
                            entry_matches = re.findall(r"\('([^']+)', '([^']+)'\)", entries_str)
                            entries = [entry[1] for entry in entry_matches]  # Just the problem names
                        
                        generations.append({
                            'generation': int(gen_num),
                            'timestamp': timestamp,
                            'entries': entries
                        })
    
    print(f"📊 **GENERATION PROGRESS**")
    print(f"   Total Generations: {len(generations)}")
    if generations:
        start_time = generations[0]['timestamp']
        end_time = generations[-1]['timestamp']
        duration = end_time - start_time
        print(f"   Duration: {duration}")
        print(f"   Average per generation: {duration / len(generations) if generations else 'N/A'}")
        print(f"   Latest generation: {generations[-1]['generation']}")
    
    # 2. Problem Type Distribution
    problem_types = defaultdict(int)
    solve_types = defaultdict(int)
    
    for gen in generations:
        for entry in gen['entries']:
            if entry.startswith('solve_'):
                solve_types[entry] += 1
            else:
                # Extract repository type (django, sphinx, etc.)
                repo_type = entry.split('__')[0] if '__' in entry else 'other'
                problem_types[repo_type] += 1
    
    print(f"\n🎯 **PROBLEM TYPE DISTRIBUTION**")
    print("   Self-Improvement Tasks:")
    for solve_type, count in sorted(solve_types.items()):
        print(f"     {solve_type}: {count} times")
    
    print("   Real-World Issues:")
    for prob_type, count in sorted(problem_types.items()):
        print(f"     {prob_type}: {count} issues")
    
    # 3. Performance Metrics Analysis
    print(f"\n📈 **PERFORMANCE METRICS**")
    
    # Find all metadata files
    metadata_files = glob.glob(os.path.join(run_dir, "**/metadata.json"), recursive=True)
    
    successful_runs = 0
    failed_runs = 0
    performance_data = []
    
    for metadata_file in metadata_files:
        try:
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
                
            run_id = metadata.get('run_id', 'unknown')
            entry = metadata.get('entry', 'unknown')
            
            # Check if there's performance data
            overall_perf = metadata.get('overall_performance', {})
            if overall_perf:
                performance_data.append({
                    'run_id': run_id,
                    'entry': entry,
                    'performance': overall_perf
                })
                successful_runs += 1
            else:
                failed_runs += 1
                
        except Exception as e:
            failed_runs += 1
    
    print(f"   Successful self-improvement attempts: {successful_runs}")
    print(f"   Failed attempts: {failed_runs}")
    print(f"   Success rate: {successful_runs/(successful_runs+failed_runs)*100:.1f}%" if (successful_runs+failed_runs) > 0 else "N/A")
    
    # 4. Test Results Analysis
    print(f"\n🧪 **TEST RESULTS ANALYSIS**")
    
    # Find evaluation reports
    report_files = glob.glob(os.path.join(run_dir, "**/report.json"), recursive=True)
    
    total_tests = 0
    passed_tests = 0
    resolved_issues = 0
    total_issues = 0
    
    for report_file in report_files:
        try:
            with open(report_file, 'r') as f:
                report = json.load(f)
            
            for issue_id, issue_data in report.items():
                total_issues += 1
                if issue_data.get('resolved', False):
                    resolved_issues += 1
                
                tests_status = issue_data.get('tests_status', {})
                
                # Count FAIL_TO_PASS tests (these should pass after fix)
                fail_to_pass = tests_status.get('FAIL_TO_PASS', {})
                if fail_to_pass:
                    success_count = len(fail_to_pass.get('success', []))
                    failure_count = len(fail_to_pass.get('failure', []))
                    total_tests += success_count + failure_count
                    passed_tests += success_count
                
                # Count PASS_TO_PASS tests (these should remain passing)
                pass_to_pass = tests_status.get('PASS_TO_PASS', {})
                if pass_to_pass:
                    success_count = len(pass_to_pass.get('success', []))
                    failure_count = len(pass_to_pass.get('failure', []))
                    total_tests += success_count + failure_count
                    passed_tests += success_count
                    
        except Exception as e:
            continue
    
    print(f"   Total issues evaluated: {total_issues}")
    print(f"   Issues resolved: {resolved_issues}")
    print(f"   Resolution rate: {resolved_issues/total_issues*100:.1f}%" if total_issues > 0 else "N/A")
    print(f"   Total tests run: {total_tests}")
    print(f"   Tests passed: {passed_tests}")
    print(f"   Test pass rate: {passed_tests/total_tests*100:.1f}%" if total_tests > 0 else "N/A")
    
    # 5. Evolution Trends
    print(f"\n📈 **EVOLUTION TRENDS**")
    
    # Analyze problem selection patterns
    recent_gens = generations[-5:] if len(generations) >= 5 else generations
    recent_problems = []
    for gen in recent_gens:
        recent_problems.extend(gen['entries'])
    
    print(f"   Recent focus areas (last {len(recent_gens)} generations):")
    recent_counts = defaultdict(int)
    for prob in recent_problems:
        if prob.startswith('solve_'):
            recent_counts[prob] += 1
        else:
            repo_type = prob.split('__')[0] if '__' in prob else 'other'
            recent_counts[repo_type] += 1
    
    for prob_type, count in sorted(recent_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"     {prob_type}: {count} times")
    
    return {
        'total_generations': len(generations),
        'successful_runs': successful_runs,
        'failed_runs': failed_runs,
        'resolved_issues': resolved_issues,
        'total_issues': total_issues,
        'test_pass_rate': passed_tests/total_tests*100 if total_tests > 0 else 0,
        'generations': generations
    }

def main():
    """Main function to analyze the latest DGM run."""
    
    # Find the most recent run
    output_dir = "output_dgm"
    if not os.path.exists(output_dir):
        print("❌ No output_dgm directory found. Run DGM first!")
        return
    
    run_dirs = [d for d in os.listdir(output_dir) if os.path.isdir(os.path.join(output_dir, d))]
    if not run_dirs:
        print("❌ No DGM runs found in output_dgm/")
        return
    
    # Sort by timestamp (assuming format YYYYMMDDHHMMSS_*)
    run_dirs.sort(reverse=True)
    latest_run = os.path.join(output_dir, run_dirs[0])
    
    print(f"🚀 **DARWIN GÖDEL MACHINE PROGRESS ANALYSIS**")
    print(f"📅 Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    results = analyze_dgm_progress(latest_run)
    
    print(f"\n🎯 **SUMMARY**")
    print(f"   🔄 Generations Completed: {results['total_generations']}")
    print(f"   ✅ Success Rate: {results['successful_runs']/(results['successful_runs']+results['failed_runs'])*100:.1f}%" if (results['successful_runs']+results['failed_runs']) > 0 else "N/A")
    print(f"   🎯 Issue Resolution Rate: {results['resolved_issues']}/{results['total_issues']} ({results['resolved_issues']/results['total_issues']*100:.1f}%)" if results['total_issues'] > 0 else "N/A")
    print(f"   🧪 Test Pass Rate: {results['test_pass_rate']:.1f}%")
    
    if results['total_generations'] >= 20:
        print(f"   🏆 **MILESTONE ACHIEVED: 20+ Generations!**")
    
    print(f"\n💡 **NEXT STEPS**")
    print(f"   • Continue evolution with: python DGM_outer.py --continue_from {run_dirs[0]}")
    print(f"   • Analyze specific generations: check output_dgm/{run_dirs[0]}/")
    print(f"   • Review patches: find output_dgm/{run_dirs[0]} -name '*.diff'")

if __name__ == "__main__":
    main()
