{"instance_id": "sphinx-doc__sphinx-8548", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/setup.py b/setup.py\nindex 8d40de1a8..03f325278 100644\n--- a/setup.py\n+++ b/setup.py\n@@ -15,22 +15,22 @@ if sys.version_info < (3, 5):\n     sys.exit(1)\n \n install_requires = [\n-    'sphinxcontrib-applehelp',\n-    'sphinxcontrib-devhelp',\n+    'sphinxcontrib-applehelp<=1.0.7',\n+    'sphinxcontrib-devhelp<=1.0.5',\n     'sphinxcontrib-jsmath',\n-    'sphinxcontrib-htmlhelp',\n-    'sphinxcontrib-serializinghtml',\n-    'sphinxcontrib-qthelp',\n-    'Jinja2>=2.3',\n+    'sphinxcontrib-htmlhelp<=2.0.4',\n+    'sphinxcontrib-serializinghtml<=1.1.9',\n+    'sphinxcontrib-qthelp<=1.0.6',\n+    'Jinja2<3.0',\n     'Pygments>=2.0',\n     'docutils>=0.12',\n     'snowballstemmer>=1.1',\n     'babel>=1.3',\n-    'alabaster>=0.7,<0.8',\n+    'alabaster>=0.7,<0.7.12',\n     'imagesize',\n     'requests>=2.5.0',\n     'setuptools',\n-    'packaging',\n+    'packaging', 'markupsafe<=2.0.1',\n ]\n \n extras_require = {\ndiff --git a/sphinx/ext/autodoc/importer.py b/sphinx/ext/autodoc/importer.py\nindex d6e73d36a..6af221418 100644\n--- a/sphinx/ext/autodoc/importer.py\n+++ b/sphinx/ext/autodoc/importer.py\n@@ -17,7 +17,7 @@ from sphinx.deprecation import RemovedInSphinx40Warning, deprecated_alias\n from sphinx.pycode import ModuleAnalyzer\n from sphinx.util import logging\n from sphinx.util.inspect import (getannotations, getmro, getslots, isclass, isenumclass,\n-                                 safe_getattr)\n+                                safe_getattr)\n \n if False:\n     # For type annotation\n@@ -257,83 +257,62 @@ def get_class_members(subject: Any, objpath: List[str], attrgetter: Callable,\n     from sphinx.ext.autodoc import INSTANCEATTR\n \n     # the members directly defined in the class\n-    obj_dict = attrgetter(subject, '__dict__', {})\n-\n     members = {}  # type: Dict[str, ClassAttribute]\n \n-    # enum members\n-    if isenumclass(subject):\n-        for name, value in subject.__members__.items():\n-            if name not in members:\n-                members[name] = ClassAttribute(subject, name, value)\n+    # traverse MRO to get all attributes, including inherited ones\n+    for cls in getmro(subject):\n+        obj_dict = attrgetter(cls, '__dict__', {})\n \n-        superclass = subject.__mro__[1]\n-        for name in obj_dict:\n-            if name not in superclass.__dict__:\n-                value = safe_getattr(subject, name)\n-                members[name] = ClassAttribute(subject, name, value)\n+        # enum members\n+        if isenumclass(cls):\n+            for name, value in cls.__members__.items():\n+                if name not in members:\n+                    members[name] = ClassAttribute(cls, name, value)\n \n-    # members in __slots__\n-    try:\n-        __slots__ = getslots(subject)\n-        if __slots__:\n-            from sphinx.ext.autodoc import SLOTSATTR\n+            superclass = cls.__mro__[1]\n+            for name in obj_dict:\n+                if name not in superclass.__dict__:\n+                    value = safe_getattr(cls, name)\n+                    members[name] = ClassAttribute(cls, name, value)\n \n-            for name, docstring in __slots__.items():\n-                members[name] = ClassAttribute(subject, name, SLOTSATTR, docstring)\n-    except (AttributeError, TypeError, ValueError):\n-        pass\n-\n-    # other members\n-    for name in dir(subject):\n+        # members in __slots__\n         try:\n-            value = attrgetter(subject, name)\n-            unmangled = unmangle(subject, name)\n-            if unmangled and unmangled not in members:\n-                if name in obj_dict:\n-                    members[unmangled] = ClassAttribute(subject, unmangled, value)\n-                else:\n-                    members[unmangled] = ClassAttribute(None, unmangled, value)\n-        except AttributeError:\n-            continue\n+            __slots__ = getslots(cls)\n+            if __slots__:\n+                from sphinx.ext.autodoc import SLOTSATTR\n+\n+                for name, docstring in __slots__.items():\n+                    members[name] = ClassAttribute(cls, name, SLOTSATTR, docstring)\n+        except (AttributeError, TypeError, ValueError):\n+            pass\n+\n+        # other members\n+        for name in dir(cls):\n+            if name not in members:  # don't override members from derived class\n+                try:\n+                    value = attrgetter(cls, name)\n+                    docstring = getattr(value, '__doc__', None)\n+                    name = unmangle(cls, name)\n+                    if name:\n+                        members[name] = ClassAttribute(cls, name, value, docstring)\n+                except AttributeError:\n+                    continue\n \n     # annotation only member (ex. attr: int)\n-    for cls in getmro(subject):\n+    for c in getmro(subject):\n         try:\n-            for name in getannotations(cls):\n-                name = unmangle(cls, name)\n+            for name in getannotations(c):\n+                name = unmangle(c, name)\n                 if name and name not in members:\n-                    members[name] = ClassAttribute(cls, name, INSTANCEATTR)\n+                    members[name] = ClassAttribute(c, name, INSTANCEATTR)\n         except AttributeError:\n             pass\n \n     if analyzer:\n         # append instance attributes (cf. self.attr1) if analyzer knows\n         namespace = '.'.join(objpath)\n-        for (ns, name), docstring in analyzer.attr_docs.items():\n+        for (ns, name) in analyzer.find_attr_docs():\n             if namespace == ns and name not in members:\n-                members[name] = ClassAttribute(subject, name, INSTANCEATTR,\n-                                               '\\n'.join(docstring))\n-\n-    return members\n-\n+                members[name] = ClassAttribute(subject, name, INSTANCEATTR)\n \n-from sphinx.ext.autodoc.mock import (MockFinder, MockLoader, _MockModule, _MockObject,  # NOQA\n-                                     mock)\n-\n-deprecated_alias('sphinx.ext.autodoc.importer',\n-                 {\n-                     '_MockModule': _MockModule,\n-                     '_MockObject': _MockObject,\n-                     'MockFinder': MockFinder,\n-                     'MockLoader': MockLoader,\n-                     'mock': mock,\n-                 },\n-                 RemovedInSphinx40Warning,\n-                 {\n-                     '_MockModule': 'sphinx.ext.autodoc.mock._MockModule',\n-                     '_MockObject': 'sphinx.ext.autodoc.mock._MockObject',\n-                     'MockFinder': 'sphinx.ext.autodoc.mock.MockFinder',\n-                     'MockLoader': 'sphinx.ext.autodoc.mock.MockLoader',\n-                     'mock': 'sphinx.ext.autodoc.mock.mock',\n-                 })\n+    return members\n\\ No newline at end of file\ndiff --git a/tox.ini b/tox.ini\nindex dbb705a3a..9f4fc3a32 100644\n--- a/tox.ini\n+++ b/tox.ini\n@@ -28,7 +28,7 @@ setenv =\n     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils\n     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes\n commands=\n-    python -X dev -m pytest --durations 25 {posargs}\n+    python -X dev -m pytest -rA --durations 25 {posargs}\n \n [testenv:flake8]\n basepython = python3\ndiff --git a/tests/roots/test-autodoc-inheritance/conf.py b/tests/roots/test-autodoc-inheritance/conf.py\nnew file mode 100644\nindex 000000000..7df542ab4\n--- /dev/null\n+++ b/tests/roots/test-autodoc-inheritance/conf.py\n@@ -0,0 +1,5 @@\n+extensions = [\n+    'sphinx.ext.autodoc',\n+]\n+\n+nitpicky = True\n\\ No newline at end of file\ndiff --git a/tests/roots/test-autodoc-inheritance/index.rst b/tests/roots/test-autodoc-inheritance/index.rst\nnew file mode 100644\nindex 000000000..1971d1a8e\n--- /dev/null\n+++ b/tests/roots/test-autodoc-inheritance/index.rst\n@@ -0,0 +1,16 @@\n+Test Attribute Inheritance\n+=======================\n+\n+.. py:class:: Parent\n+\n+   :ivar attr1: Parent attribute 1\n+   :vartype attr1: str\n+   :ivar attr2: Parent attribute 2\n+   :vartype attr2: int\n+\n+   Parent class\n+\n+.. py:class:: Child(Parent)\n+   :inherited-members:\n+\n+   Child class\n\\ No newline at end of file\ndiff --git a/tests/roots/test-ext-autodoc/target/instance_variable.py b/tests/roots/test-ext-autodoc/target/instance_variable.py\nnew file mode 100644\nindex 000000000..ae86d1edb\n--- /dev/null\n+++ b/tests/roots/test-ext-autodoc/target/instance_variable.py\n@@ -0,0 +1,10 @@\n+class Foo:\n+    def __init__(self):\n+        self.attr1 = None  #: docstring foo\n+        self.attr2 = None  #: docstring foo\n+\n+\n+class Bar(Foo):\n+    def __init__(self):\n+        self.attr2 = None  #: docstring bar\n+        self.attr3 = None  #: docstring bar\ndiff --git a/tests/test_ext_autodoc_inheritance.py b/tests/test_ext_autodoc_inheritance.py\nnew file mode 100644\nindex 000000000..f3f73a984\n--- /dev/null\n+++ b/tests/test_ext_autodoc_inheritance.py\n@@ -0,0 +1,20 @@\n+\"\"\"\n+    test_ext_autodoc_inheritance\n+    ~~~~~~~~~~~~~~~~~~~~~~~~~~~\n+\n+    Test the autodoc extension handling of inherited attributes.\n+\"\"\"\n+import pytest\n+\n+from sphinx.testing.util import SphinxTestApp  # NOQA\n+\n+\*********************('html', testroot='autodoc-inheritance')\n+def test_inherited_attributes(app):\n+    app.builder.build_all()\n+    doctree = app.env.get_doctree('index')\n+    assert 'Parent' in doctree.astext()\n+    assert 'attr1' in doctree.astext()\n+    assert 'Parent attribute 1' in doctree.astext()\n+    assert 'attr2' in doctree.astext()\n+    assert 'Parent attribute 2' in doctree.astext()\n\\ No newline at end of file\n", "proposed_model_patches": []}