#!/usr/bin/env python3
"""
Test script to verify the Ollama integration setup for DGM.
This script tests the connection to Ollama and LiteLLM proxy.
"""

import requests
import json
import sys
import time

def test_ollama_connection():
    """Test if Ollama server is running and has the required model."""
    print("Testing Ollama connection...")
    
    try:
        # Test Ollama server
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✓ Ollama server is running")
            
            # Check for qwen3:30b-a3b model
            models = response.json()
            model_names = [model['name'] for model in models.get('models', [])]
            
            if 'qwen3:30b-a3b' in model_names:
                print("✓ qwen3:30b-a3b model is available")
                return True
            else:
                print("⚠ qwen3:30b-a3b model not found")
                print("Available models:", model_names)
                print("Please run: ollama pull qwen3:30b-a3b")
                return False
        else:
            print(f"✗ Ollama server responded with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Cannot connect to Ollama server: {e}")
        print("Please start Ollama with: ollama serve")
        return False

def test_litellm_proxy():
    """Test if LiteLLM proxy is running."""
    print("\nTesting LiteLLM proxy...")
    
    try:
        # Test LiteLLM proxy health endpoint
        response = requests.get("http://localhost:4000/health", timeout=5)
        if response.status_code == 200:
            print("✓ LiteLLM proxy is running")
            return True
        else:
            print(f"⚠ LiteLLM proxy responded with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Cannot connect to LiteLLM proxy: {e}")
        print("Please start LiteLLM proxy with: ./start_litellm.sh")
        return False

def test_model_inference():
    """Test a simple inference through the LiteLLM proxy."""
    print("\nTesting model inference...")
    
    try:
        headers = {
            "Authorization": "Bearer sk-1234",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "qwen3:30b-a3b",
            "messages": [
                {"role": "user", "content": "Hello! Please respond with 'Hello from qwen3:30b-a3b'"}
            ],
            "max_tokens": 50
        }
        
        response = requests.post(
            "http://localhost:4000/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"✓ Model inference successful: {content}")
            return True
        else:
            print(f"✗ Model inference failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Model inference failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=== DGM Ollama Setup Test ===\n")
    
    tests_passed = 0
    total_tests = 3
    
    # Test Ollama connection
    if test_ollama_connection():
        tests_passed += 1
    
    # Test LiteLLM proxy
    if test_litellm_proxy():
        tests_passed += 1
    
    # Test model inference (only if previous tests passed)
    if tests_passed == 2:
        if test_model_inference():
            tests_passed += 1
    else:
        print("\nSkipping model inference test due to previous failures")
    
    # Summary
    print(f"\n=== Test Results ===")
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✓ All tests passed! Your setup is ready for DGM.")
        print("\nYou can now run: python DGM_outer.py")
        return 0
    else:
        print("✗ Some tests failed. Please check the setup.")
        print("\nSetup checklist:")
        print("1. Start Ollama: ollama serve")
        print("2. Pull model: ollama pull qwen3:30b-a3b")
        print("3. Start LiteLLM: ./start_litellm.sh")
        return 1

if __name__ == "__main__":
    sys.exit(main())
