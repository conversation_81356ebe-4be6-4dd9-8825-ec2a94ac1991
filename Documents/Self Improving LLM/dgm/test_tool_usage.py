#!/usr/bin/env python3
"""
Test tool usage with qwen3:30b-a3b to see if it can properly format tool calls.
"""

import os
import tempfile
import shutil
from pathlib import Path

def test_tool_usage():
    """Test if qwen3:30b-a3b can use tools correctly."""
    print("🧪 Testing tool usage with qwen3:30b-a3b...")
    
    try:
        from llm_withtools import chat_with_agent
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            test_file = Path(temp_dir) / "test.py"
            test_file.write_text("print('hello world')")
            
            # Test message that should trigger tool usage
            msg = f"""I need you to modify the file {test_file} to print 'hello universe' instead of 'hello world'.

Please use the edit tool to make this change. The file currently contains:
```python
print('hello world')
```

Change it to:
```python
print('hello universe')
```

Use the edit tool with the correct format."""
            
            print(f"   Test directory: {temp_dir}")
            print(f"   Test file: {test_file}")
            print(f"   Original content: {test_file.read_text()}")
            
            # Call the agent
            print("   Calling chat_with_agent...")
            msg_history = chat_with_agent(
                msg=msg,
                model='qwen3:30b-a3b',
                msg_history=[],
                logging=print
            )
            
            # Check if file was modified
            new_content = test_file.read_text()
            print(f"   Final content: {new_content}")
            
            if 'universe' in new_content:
                print("   ✅ Tool usage successful! File was modified correctly.")
                return True
            else:
                print("   ❌ Tool usage failed. File was not modified.")
                print(f"   Message history: {msg_history}")
                return False
                
    except Exception as e:
        print(f"   ❌ Tool usage test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_tool_call():
    """Test a simple tool call to see the model's output format."""
    print("🔧 Testing simple tool call format...")
    
    try:
        from llm import create_client, get_response_from_llm
        from prompts.tooluse_prompt import get_tooluse_prompt
        
        # Create client
        client, model = create_client('qwen3:30b-a3b')
        
        # Simple message asking to create a file
        msg = """Please create a file called 'hello.py' with the content 'print("Hello, World!")'.

Use the edit tool to create this file."""
        
        system_message = f'You are a coding agent.\n\n{get_tooluse_prompt()}'
        
        print("   System message preview:")
        print("   " + system_message[:200] + "...")
        print()
        print("   User message:")
        print("   " + msg)
        print()
        
        # Get response
        response, msg_history = get_response_from_llm(
            msg=msg,
            client=client,
            model=model,
            system_message=system_message,
            print_debug=True,
            msg_history=[],
        )
        
        print("   Model response:")
        print("   " + response)
        
        # Check if response contains tool_use tags
        if '<tool_use>' in response and '</tool_use>' in response:
            print("   ✅ Model used correct tool format!")
            return True
        else:
            print("   ❌ Model did not use correct tool format.")
            return False
            
    except Exception as e:
        print(f"   ❌ Simple tool call test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run tool usage tests."""
    print("🧪 **TOOL USAGE TESTING**")
    print("=" * 50)
    
    # Test 1: Simple tool call format
    test1_result = test_simple_tool_call()
    print()
    
    # Test 2: Full tool usage workflow
    test2_result = test_tool_usage()
    print()
    
    # Summary
    if test1_result and test2_result:
        print("🎉 All tool usage tests passed!")
        print("The qwen3:30b-a3b model can use tools correctly.")
        return True
    else:
        print("❌ Some tool usage tests failed.")
        print("The model may need better prompting or tool format adjustments.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
