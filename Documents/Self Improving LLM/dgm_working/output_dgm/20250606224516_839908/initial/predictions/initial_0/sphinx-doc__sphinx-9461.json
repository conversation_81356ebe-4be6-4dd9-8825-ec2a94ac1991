{"instance_id": "sphinx-doc__sphinx-9461", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/setup.py b/setup.py\nindex 4a1d5aeed..3faa58e79 100644\n--- a/setup.py\n+++ b/setup.py\n@@ -15,22 +15,22 @@ if sys.version_info < (3, 6):\n     sys.exit(1)\n \n install_requires = [\n-    'sphinxcontrib-applehelp',\n-    'sphinxcontrib-devhelp',\n+    'sphinxcontrib-applehelp<=1.0.7',\n+    'sphinxcontrib-devhelp<=1.0.5',\n     'sphinxcontrib-jsmath',\n-    'sphinxcontrib-htmlhelp>=2.0.0',\n-    'sphinxcontrib-serializinghtml>=1.1.5',\n-    'sphinxcontrib-qthelp',\n-    'Jinja2>=2.3',\n+    'sphinxcontrib-htmlhelp>=2.0.0,<=2.0.4',\n+    'sphinxcontrib-serializinghtml>=1.1.5,<=1.1.9',\n+    'sphinxcontrib-qthelp<=1.0.6',\n+    'Jinja2<3.0',\n     'Pygments>=2.0',\n     'docutils>=0.14,<0.18',\n     'snowballstemmer>=1.1',\n     'babel>=1.3',\n-    'alabaster>=0.7,<0.8',\n+    'alabaster>=0.7,<0.7.12',\n     'imagesize',\n     'requests>=2.5.0',\n     'setuptools',\n-    'packaging',\n+    'packaging', 'markupsafe<=2.0.1',\n ]\n \n extras_require = {\ndiff --git a/sphinx/ext/autodoc/__init__.py b/sphinx/ext/autodoc/__init__.py\nindex 1cecb1f79..94cbf4cc1 100644\n--- a/sphinx/ext/autodoc/__init__.py\n+++ b/sphinx/ext/autodoc/__init__.py\n@@ -40,6 +40,8 @@ if TYPE_CHECKING:\n     from sphinx.ext.autodoc.directive import DocumenterBridge\n \n \n+__all__ = ['ALL', 'EMPTY', 'Options', 'ModuleLevelDocumenter']\n+\n logger = logging.getLogger(__name__)\n \n \n@@ -48,22 +50,6 @@ logger = logging.getLogger(__name__)\n MethodDescriptorType = type(type.__subclasses__)\n \n \n-#: extended signature RE: with explicit module name separated by ::\n-py_ext_sig_re = re.compile(\n-    r'''^ ([\\w.]+::)?            # explicit module name\n-          ([\\w.]+\\.)?            # module and/or class name(s)\n-          (\\w+)  \\s*             # thing name\n-          (?: \\((.*)\\)           # optional: arguments\n-           (?:\\s* -> \\s* (.*))?  #           return annotation\n-          )? $                   # and nothing more\n-          ''', re.VERBOSE)\n-special_member_re = re.compile(r'^__\\S+__$')\n-\n-\n-def identity(x: Any) -> Any:\n-    return x\n-\n-\n class _All:\n     \"\"\"A special value for :*-members: that matches to any member.\"\"\"\n \n@@ -88,173 +74,6 @@ INSTANCEATTR = object()\n SLOTSATTR = object()\n \n \n-def members_option(arg: Any) -> Union[object, List[str]]:\n-    \"\"\"Used to convert the :members: option to auto directives.\"\"\"\n-    if arg in (None, True):\n-        return ALL\n-    elif arg is False:\n-        return None\n-    else:\n-        return [x.strip() for x in arg.split(',') if x.strip()]\n-\n-\n-def members_set_option(arg: Any) -> Union[object, Set[str]]:\n-    \"\"\"Used to convert the :members: option to auto directives.\"\"\"\n-    warnings.warn(\"members_set_option() is deprecated.\",\n-                  RemovedInSphinx50Warning, stacklevel=2)\n-    if arg is None:\n-        return ALL\n-    return {x.strip() for x in arg.split(',') if x.strip()}\n-\n-\n-def exclude_members_option(arg: Any) -> Union[object, Set[str]]:\n-    \"\"\"Used to convert the :exclude-members: option.\"\"\"\n-    if arg in (None, True):\n-        return EMPTY\n-    return {x.strip() for x in arg.split(',') if x.strip()}\n-\n-\n-def inherited_members_option(arg: Any) -> Union[object, Set[str]]:\n-    \"\"\"Used to convert the :members: option to auto directives.\"\"\"\n-    if arg in (None, True):\n-        return 'object'\n-    else:\n-        return arg\n-\n-\n-def member_order_option(arg: Any) -> Optional[str]:\n-    \"\"\"Used to convert the :members: option to auto directives.\"\"\"\n-    if arg in (None, True):\n-        return None\n-    elif arg in ('alphabetical', 'bysource', 'groupwise'):\n-        return arg\n-    else:\n-        raise ValueError(__('invalid value for member-order option: %s') % arg)\n-\n-\n-def class_doc_from_option(arg: Any) -> Optional[str]:\n-    \"\"\"Used to convert the :class-doc-from: option to autoclass directives.\"\"\"\n-    if arg in ('both', 'class', 'init'):\n-        return arg\n-    else:\n-        raise ValueError(__('invalid value for class-doc-from option: %s') % arg)\n-\n-\n-SUPPRESS = object()\n-\n-\n-def annotation_option(arg: Any) -> Any:\n-    if arg in (None, True):\n-        # suppress showing the representation of the object\n-        return SUPPRESS\n-    else:\n-        return arg\n-\n-\n-def bool_option(arg: Any) -> bool:\n-    \"\"\"Used to convert flag options to auto directives.  (Instead of\n-    directives.flag(), which returns None).\n-    \"\"\"\n-    return True\n-\n-\n-def merge_special_members_option(options: Dict) -> None:\n-    \"\"\"Merge :special-members: option to :members: option.\"\"\"\n-    warnings.warn(\"merge_special_members_option() is deprecated.\",\n-                  RemovedInSphinx50Warning, stacklevel=2)\n-    if 'special-members' in options and options['special-members'] is not ALL:\n-        if options.get('members') is ALL:\n-            pass\n-        elif options.get('members'):\n-            for member in options['special-members']:\n-                if member not in options['members']:\n-                    options['members'].append(member)\n-        else:\n-            options['members'] = options['special-members']\n-\n-\n-def merge_members_option(options: Dict) -> None:\n-    \"\"\"Merge :*-members: option to the :members: option.\"\"\"\n-    if options.get('members') is ALL:\n-        # merging is not needed when members: ALL\n-        return\n-\n-    members = options.setdefault('members', [])\n-    for key in {'private-members', 'special-members'}:\n-        if key in options and options[key] not in (ALL, None):\n-            for member in options[key]:\n-                if member not in members:\n-                    members.append(member)\n-\n-\n-# Some useful event listener factories for autodoc-process-docstring.\n-\n-def cut_lines(pre: int, post: int = 0, what: str = None) -> Callable:\n-    \"\"\"Return a listener that removes the first *pre* and last *post*\n-    lines of every docstring.  If *what* is a sequence of strings,\n-    only docstrings of a type in *what* will be processed.\n-\n-    Use like this (e.g. in the ``setup()`` function of :file:`conf.py`)::\n-\n-       from sphinx.ext.autodoc import cut_lines\n-       app.connect('autodoc-process-docstring', cut_lines(4, what=['module']))\n-\n-    This can (and should) be used in place of :confval:`automodule_skip_lines`.\n-    \"\"\"\n-    def process(app: Sphinx, what_: str, name: str, obj: Any, options: Any, lines: List[str]\n-                ) -> None:\n-        if what and what_ not in what:\n-            return\n-        del lines[:pre]\n-        if post:\n-            # remove one trailing blank line.\n-            if lines and not lines[-1]:\n-                lines.pop(-1)\n-            del lines[-post:]\n-        # make sure there is a blank line at the end\n-        if lines and lines[-1]:\n-            lines.append('')\n-    return process\n-\n-\n-def between(marker: str, what: Sequence[str] = None, keepempty: bool = False,\n-            exclude: bool = False) -> Callable:\n-    \"\"\"Return a listener that either keeps, or if *exclude* is True excludes,\n-    lines between lines that match the *marker* regular expression.  If no line\n-    matches, the resulting docstring would be empty, so no change will be made\n-    unless *keepempty* is true.\n-\n-    If *what* is a sequence of strings, only docstrings of a type in *what* will\n-    be processed.\n-    \"\"\"\n-    marker_re = re.compile(marker)\n-\n-    def process(app: Sphinx, what_: str, name: str, obj: Any, options: Any, lines: List[str]\n-                ) -> None:\n-        if what and what_ not in what:\n-            return\n-        deleted = 0\n-        delete = not exclude\n-        orig_lines = lines[:]\n-        for i, line in enumerate(orig_lines):\n-            if delete:\n-                lines.pop(i - deleted)\n-                deleted += 1\n-            if marker_re.match(line):\n-                delete = not delete\n-                if delete:\n-                    lines.pop(i - deleted)\n-                    deleted += 1\n-        if not lines and not keepempty:\n-            lines[:] = orig_lines\n-        # make sure there is a blank line at the end\n-        if lines and lines[-1]:\n-            lines.append('')\n-    return process\n-\n-\n-# This class is used only in ``sphinx.ext.autodoc.directive``,\n-# But we define this class here to keep compatibility (see #4538)\n class Options(dict):\n     \"\"\"A dict/attribute hybrid that returns None on nonexisting keys.\"\"\"\n     def __getattr__(self, name: str) -> Any:\n@@ -264,2521 +83,27 @@ class Options(dict):\n             return None\n \n \n-class ObjectMember(tuple):\n-    \"\"\"A member of object.\n-\n-    This is used for the result of `Documenter.get_object_members()` to\n-    represent each member of the object.\n-\n-    .. Note::\n-\n-       An instance of this class behaves as a tuple of (name, object)\n-       for compatibility to old Sphinx.  The behavior will be dropped\n-       in the future.  Therefore extensions should not use the tuple\n-       interface.\n-    \"\"\"\n-\n-    def __new__(cls, name: str, obj: Any, **kwargs: Any) -> Any:\n-        return super().__new__(cls, (name, obj))  # type: ignore\n-\n-    def __init__(self, name: str, obj: Any, docstring: Optional[str] = None,\n-                 class_: Any = None, skipped: bool = False) -> None:\n-        self.__name__ = name\n-        self.object = obj\n-        self.docstring = docstring\n-        self.skipped = skipped\n-        self.class_ = class_\n-\n-\n-ObjectMembers = Union[List[ObjectMember], List[Tuple[str, Any]]]\n-\n-\n class Documenter:\n-    \"\"\"\n-    A Documenter knows how to autodocument a single object type.  When\n-    registered with the AutoDirective, it will be used to document objects\n-    of that type when needed by autodoc.\n-\n-    Its *objtype* attribute selects what auto directive it is assigned to\n-    (the directive name is 'auto' + objtype), and what directive it generates\n-    by default, though that can be overridden by an attribute called\n-    *directivetype*.\n-\n-    A Documenter has an *option_spec* that works like a docutils directive's;\n-    in fact, it will be used to parse an auto directive's options that matches\n-    the documenter.\n-    \"\"\"\n-    #: name by which the directive is called (auto...) and the default\n-    #: generated directive name\n-    objtype = 'object'\n-    #: indentation by which to indent the directive content\n-    content_indent = '   '\n-    #: priority if multiple documenters return True from can_document_member\n-    priority = 0\n-    #: order if autodoc_member_order is set to 'groupwise'\n-    member_order = 0\n-    #: true if the generated content may contain titles\n-    titles_allowed = False\n-\n-    option_spec: OptionSpec = {\n-        'noindex': bool_option\n-    }\n-\n-    def get_attr(self, obj: Any, name: str, *defargs: Any) -> Any:\n-        \"\"\"getattr() override for types such as Zope interfaces.\"\"\"\n-        return autodoc_attrgetter(self.env.app, obj, name, *defargs)\n-\n-    @classmethod\n-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any\n-                            ) -> bool:\n-        \"\"\"Called to see if a member can be documented by this documenter.\"\"\"\n-        raise NotImplementedError('must be implemented in subclasses')\n-\n-    def __init__(self, directive: \"DocumenterBridge\", name: str, indent: str = '') -> None:\n-        self.directive = directive\n-        self.config: Config = directive.env.config\n-        self.env: BuildEnvironment = directive.env\n-        self.options = directive.genopt\n-        self.name = name\n-        self.indent = indent\n-        # the module and object path within the module, and the fully\n-        # qualified name (all set after resolve_name succeeds)\n-        self.modname: str = None\n-        self.module: ModuleType = None\n-        self.objpath: List[str] = None\n-        self.fullname: str = None\n-        # extra signature items (arguments and return annotation,\n-        # also set after resolve_name succeeds)\n-        self.args: str = None\n-        self.retann: str = None\n-        # the object to document (set after import_object succeeds)\n-        self.object: Any = None\n-        self.object_name: str = None\n-        # the parent/owner of the object to document\n-        self.parent: Any = None\n-        # the module analyzer to get at attribute docs, or None\n-        self.analyzer: ModuleAnalyzer = None\n-\n-    @property\n-    def documenters(self) -> Dict[str, Type[\"Documenter\"]]:\n-        \"\"\"Returns registered Documenter classes\"\"\"\n-        return self.env.app.registry.documenters\n-\n-    def add_line(self, line: str, source: str, *lineno: int) -> None:\n-        \"\"\"Append one line of generated reST to the output.\"\"\"\n-        if line.strip():  # not a blank line\n-            self.directive.result.append(self.indent + line, source, *lineno)\n-        else:\n-            self.directive.result.append('', source, *lineno)\n-\n-    def resolve_name(self, modname: str, parents: Any, path: str, base: Any\n-                     ) -> Tuple[str, List[str]]:\n-        \"\"\"Resolve the module and name of the object to document given by the\n-        arguments and the current module/class.\n-\n-        Must return a pair of the module name and a chain of attributes; for\n-        example, it would return ``('zipfile', ['ZipFile', 'open'])`` for the\n-        ``zipfile.ZipFile.open`` method.\n-        \"\"\"\n-        raise NotImplementedError('must be implemented in subclasses')\n-\n-    def parse_name(self) -> bool:\n-        \"\"\"Determine what module to import and what attribute to document.\n+    \"\"\"Base class for documenters.\"\"\"\n \n-        Returns True and sets *self.modname*, *self.objpath*, *self.fullname*,\n-        *self.args* and *self.retann* if parsing and resolving was successful.\n-        \"\"\"\n-        # first, parse the definition -- auto directives for classes and\n-        # functions can contain a signature which is then used instead of\n-        # an autogenerated one\n-        try:\n-            matched = py_ext_sig_re.match(self.name)\n-            explicit_modname, path, base, args, retann = matched.groups()\n-        except AttributeError:\n-            logger.warning(__('invalid signature for auto%s (%r)') % (self.objtype, self.name),\n-                           type='autodoc')\n-            return False\n-\n-        # support explicit module and class name separation via ::\n-        if explicit_modname is not None:\n-            modname = explicit_modname[:-2]\n-            parents = path.rstrip('.').split('.') if path else []\n-        else:\n-            modname = None\n-            parents = []\n-\n-        with mock(self.config.autodoc_mock_imports):\n-            self.modname, self.objpath = self.resolve_name(modname, parents, path, base)\n-\n-        if not self.modname:\n-            return False\n-\n-        self.args = args\n-        self.retann = retann\n-        self.fullname = ((self.modname or '') +\n-                         ('.' + '.'.join(self.objpath) if self.objpath else ''))\n-        return True\n-\n-    def import_object(self, raiseerror: bool = False) -> bool:\n-        \"\"\"Import the object given by *self.modname* and *self.objpath* and set\n-        it as *self.object*.\n-\n-        Returns True if successful, False if an error occurred.\n-        \"\"\"\n-        with mock(self.config.autodoc_mock_imports):\n-            try:\n-                ret = import_object(self.modname, self.objpath, self.objtype,\n-                                    attrgetter=self.get_attr,\n-                                    warningiserror=self.config.autodoc_warningiserror)\n-                self.module, self.parent, self.object_name, self.object = ret\n-                if ismock(self.object):\n-                    self.object = undecorate(self.object)\n-                return True\n-            except ImportError as exc:\n-                if raiseerror:\n-                    raise\n-                else:\n-                    logger.warning(exc.args[0], type='autodoc', subtype='import_object')\n-                    self.env.note_reread()\n-                    return False\n-\n-    def get_real_modname(self) -> str:\n-        \"\"\"Get the real module name of an object to document.\n-\n-        It can differ from the name of the module through which the object was\n-        imported.\n-        \"\"\"\n-        return self.get_attr(self.object, '__module__', None) or self.modname\n-\n-    def check_module(self) -> bool:\n-        \"\"\"Check if *self.object* is really defined in the module given by\n-        *self.modname*.\n-        \"\"\"\n-        if self.options.imported_members:\n-            return True\n-\n-        subject = inspect.unpartial(self.object)\n-        modname = self.get_attr(subject, '__module__', None)\n-        if modname and modname != self.modname:\n-            return False\n-        return True\n-\n-    def format_args(self, **kwargs: Any) -> str:\n-        \"\"\"Format the argument signature of *self.object*.\n-\n-        Should return None if the object does not have a signature.\n-        \"\"\"\n-        return None\n-\n-    def format_name(self) -> str:\n-        \"\"\"Format the name of *self.object*.\n+    def get_doc(self, encoding: Optional[str] = None,\n+                ignore: Optional[int] = None) -> List[List[str]]:\n+        \"\"\"Get the docstring(s) of the object being documented.\"\"\"\n+        obj = self.object\n+        if isinstance(obj, (classmethod, property)):\n+            obj = unwrap_classproperty(obj)\n \n-        This normally should be something that can be parsed by the generated\n-        directive, but doesn't need to be (Sphinx will display it unparsed\n-        then).\n-        \"\"\"\n-        # normally the name doesn't contain the module (except for module\n-        # directives of course)\n-        return '.'.join(self.objpath) or self.modname\n-\n-    def _call_format_args(self, **kwargs: Any) -> str:\n-        if kwargs:\n-            try:\n-                return self.format_args(**kwargs)\n-            except TypeError:\n-                # avoid chaining exceptions, by putting nothing here\n-                pass\n-\n-        # retry without arguments for old documenters\n-        return self.format_args()\n-\n-    def format_signature(self, **kwargs: Any) -> str:\n-        \"\"\"Format the signature (arguments and return annotation) of the object.\n-\n-        Let the user process it via the ``autodoc-process-signature`` event.\n-        \"\"\"\n-        if self.args is not None:\n-            # signature given explicitly\n-            args = \"(%s)\" % self.args\n-            retann = self.retann\n-        else:\n-            # try to introspect the signature\n-            try:\n-                retann = None\n-                args = self._call_format_args(**kwargs)\n-                if args:\n-                    matched = re.match(r'^(\\(.*\\))\\s+->\\s+(.*)$', args)\n-                    if matched:\n-                        args = matched.group(1)\n-                        retann = matched.group(2)\n-            except Exception as exc:\n-                logger.warning(__('error while formatting arguments for %s: %s'),\n-                               self.fullname, exc, type='autodoc')\n-                args = None\n-\n-        result = self.env.events.emit_firstresult('autodoc-process-signature',\n-                                                  self.objtype, self.fullname,\n-                                                  self.object, self.options, args, retann)\n-        if result:\n-            args, retann = result\n-\n-        if args is not None:\n-            return args + ((' -> %s' % retann) if retann else '')\n-        else:\n-            return ''\n-\n-    def add_directive_header(self, sig: str) -> None:\n-        \"\"\"Add the directive header and options to the generated content.\"\"\"\n-        domain = getattr(self, 'domain', 'py')\n-        directive = getattr(self, 'directivetype', self.objtype)\n-        name = self.format_name()\n-        sourcename = self.get_sourcename()\n-\n-        # one signature per line, indented by column\n-        prefix = '.. %s:%s:: ' % (domain, directive)\n-        for i, sig_line in enumerate(sig.split(\"\\n\")):\n-            self.add_line('%s%s%s' % (prefix, name, sig_line),\n-                          sourcename)\n-            if i == 0:\n-                prefix = \" \" * len(prefix)\n-\n-        if self.options.noindex:\n-            self.add_line('   :noindex:', sourcename)\n-        if self.objpath:\n-            # Be explicit about the module, this is necessary since .. class::\n-            # etc. don't support a prepended module name\n-            self.add_line('   :module: %s' % self.modname, sourcename)\n-\n-    def get_doc(self, ignore: int = None) -> Optional[List[List[str]]]:\n-        \"\"\"Decode and return lines of the docstring(s) for the object.\n-\n-        When it returns None value, autodoc-process-docstring will not be called for this\n-        object.\n-        \"\"\"\n-        if ignore is not None:\n-            warnings.warn(\"The 'ignore' argument to autodoc.%s.get_doc() is deprecated.\"\n-                          % self.__class__.__name__,\n-                          RemovedInSphinx50Warning, stacklevel=2)\n-        docstring = getdoc(self.object, self.get_attr, self.config.autodoc_inherit_docstrings,\n-                           self.parent, self.object_name)\n+        docstring = getdoc(obj)\n         if docstring:\n-            tab_width = self.directive.state.document.settings.tab_width\n-            return [prepare_docstring(docstring, ignore, tab_width)]\n+            return [[docstring]]\n         return []\n \n-    def process_doc(self, docstrings: List[List[str]]) -> Iterator[str]:\n-        \"\"\"Let the user process the docstrings before adding them.\"\"\"\n-        for docstringlines in docstrings:\n-            if self.env.app:\n-                # let extensions preprocess docstrings\n-                self.env.app.emit('autodoc-process-docstring',\n-                                  self.objtype, self.fullname, self.object,\n-                                  self.options, docstringlines)\n-\n-                if docstringlines and docstringlines[-1] != '':\n-                    # append a blank line to the end of the docstring\n-                    docstringlines.append('')\n-\n-            yield from docstringlines\n-\n-    def get_sourcename(self) -> str:\n-        if (getattr(self.object, '__module__', None) and\n-                getattr(self.object, '__qualname__', None)):\n-            # Get the correct location of docstring from self.object\n-            # to support inherited methods\n-            fullname = '%s.%s' % (self.object.__module__, self.object.__qualname__)\n-        else:\n-            fullname = self.fullname\n-\n-        if self.analyzer:\n-            return '%s:docstring of %s' % (self.analyzer.srcname, fullname)\n-        else:\n-            return 'docstring of %s' % fullname\n-\n-    def add_content(self, more_content: Optional[StringList], no_docstring: bool = False\n-                    ) -> None:\n-        \"\"\"Add content from docstrings, attribute documentation and user.\"\"\"\n-        if no_docstring:\n-            warnings.warn(\"The 'no_docstring' argument to %s.add_content() is deprecated.\"\n-                          % self.__class__.__name__,\n-                          RemovedInSphinx50Warning, stacklevel=2)\n-\n-        # set sourcename and add content from attribute documentation\n-        sourcename = self.get_sourcename()\n-        if self.analyzer:\n-            attr_docs = self.analyzer.find_attr_docs()\n-            if self.objpath:\n-                key = ('.'.join(self.objpath[:-1]), self.objpath[-1])\n-                if key in attr_docs:\n-                    no_docstring = True\n-                    # make a copy of docstring for attributes to avoid cache\n-                    # the change of autodoc-process-docstring event.\n-                    docstrings = [list(attr_docs[key])]\n-\n-                    for i, line in enumerate(self.process_doc(docstrings)):\n-                        self.add_line(line, sourcename, i)\n-\n-        # add content from docstrings\n-        if not no_docstring:\n-            docstrings = self.get_doc()\n-            if docstrings is None:\n-                # Do not call autodoc-process-docstring on get_doc() returns None.\n-                pass\n-            else:\n-                if not docstrings:\n-                    # append at least a dummy docstring, so that the event\n-                    # autodoc-process-docstring is fired and can add some\n-                    # content if desired\n-                    docstrings.append([])\n-                for i, line in enumerate(self.process_doc(docstrings)):\n-                    self.add_line(line, sourcename, i)\n-\n-        # add additional content (e.g. from document), if present\n-        if more_content:\n-            for line, src in zip(more_content.data, more_content.items):\n-                self.add_line(line, src[0], src[1])\n-\n-    def get_object_members(self, want_all: bool) -> Tuple[bool, ObjectMembers]:\n-        \"\"\"Return `(members_check_module, members)` where `members` is a\n-        list of `(membername, member)` pairs of the members of *self.object*.\n-\n-        If *want_all* is True, return all members.  Else, only return those\n-        members given by *self.options.members* (which may also be none).\n-        \"\"\"\n-        warnings.warn('The implementation of Documenter.get_object_members() will be '\n-                      'removed from Sphinx-6.0.', RemovedInSphinx60Warning)\n-        members = get_object_members(self.object, self.objpath, self.get_attr, self.analyzer)\n-        if not want_all:\n-            if not self.options.members:\n-                return False, []  # type: ignore\n-            # specific members given\n-            selected = []\n-            for name in self.options.members:  # type: str\n-                if name in members:\n-                    selected.append((name, members[name].value))\n-                else:\n-                    logger.warning(__('missing attribute %s in object %s') %\n-                                   (name, self.fullname), type='autodoc')\n-            return False, selected\n-        elif self.options.inherited_members:\n-            return False, [(m.name, m.value) for m in members.values()]\n-        else:\n-            return False, [(m.name, m.value) for m in members.values()\n-                           if m.directly_defined]\n-\n-    def filter_members(self, members: ObjectMembers, want_all: bool\n-                       ) -> List[Tuple[str, Any, bool]]:\n-        \"\"\"Filter the given member list.\n-\n-        Members are skipped if\n-\n-        - they are private (except if given explicitly or the private-members\n-          option is set)\n-        - they are special methods (except if given explicitly or the\n-          special-members option is set)\n-        - they are undocumented (except if the undoc-members option is set)\n-\n-        The user can override the skipping decision by connecting to the\n-        ``autodoc-skip-member`` event.\n-        \"\"\"\n-        def is_filtered_inherited_member(name: str, obj: Any) -> bool:\n-            if inspect.isclass(self.object):\n-                for cls in self.object.__mro__:\n-                    if cls.__name__ == self.options.inherited_members and cls != self.object:\n-                        # given member is a member of specified *super class*\n-                        return True\n-                    elif name in cls.__dict__:\n-                        return False\n-                    elif name in self.get_attr(cls, '__annotations__', {}):\n-                        return False\n-                    elif isinstance(obj, ObjectMember) and obj.class_ is cls:\n-                        return False\n-\n-            return False\n-\n-        ret = []\n-\n-        # search for members in source code too\n-        namespace = '.'.join(self.objpath)  # will be empty for modules\n-\n-        if self.analyzer:\n-            attr_docs = self.analyzer.find_attr_docs()\n-        else:\n-            attr_docs = {}\n-\n-        # process members and determine which to skip\n-        for obj in members:\n-            membername, member = obj\n-            # if isattr is True, the member is documented as an attribute\n-            if member is INSTANCEATTR:\n-                isattr = True\n-            elif (namespace, membername) in attr_docs:\n-                isattr = True\n-            else:\n-                isattr = False\n-\n-            doc = getdoc(member, self.get_attr, self.config.autodoc_inherit_docstrings,\n-                         self.parent, self.object_name)\n-            if not isinstance(doc, str):\n-                # Ignore non-string __doc__\n-                doc = None\n-\n-            # if the member __doc__ is the same as self's __doc__, it's just\n-            # inherited and therefore not the member's doc\n-            cls = self.get_attr(member, '__class__', None)\n-            if cls:\n-                cls_doc = self.get_attr(cls, '__doc__', None)\n-                if cls_doc == doc:\n-                    doc = None\n-\n-            if isinstance(obj, ObjectMember) and obj.docstring:\n-                # hack for ClassDocumenter to inject docstring via ObjectMember\n-                doc = obj.docstring\n-\n-            doc, metadata = separate_metadata(doc)\n-            has_doc = bool(doc)\n-\n-            if 'private' in metadata:\n-                # consider a member private if docstring has \"private\" metadata\n-                isprivate = True\n-            elif 'public' in metadata:\n-                # consider a member public if docstring has \"public\" metadata\n-                isprivate = False\n-            else:\n-                isprivate = membername.startswith('_')\n-\n-            keep = False\n-            if ismock(member):\n-                # mocked module or object\n-                pass\n-            elif self.options.exclude_members and membername in self.options.exclude_members:\n-                # remove members given by exclude-members\n-                keep = False\n-            elif want_all and special_member_re.match(membername):\n-                # special __methods__\n-                if self.options.special_members and membername in self.options.special_members:\n-                    if membername == '__doc__':\n-                        keep = False\n-                    elif is_filtered_inherited_member(membername, obj):\n-                        keep = False\n-                    else:\n-                        keep = has_doc or self.options.undoc_members\n-                else:\n-                    keep = False\n-            elif (namespace, membername) in attr_docs:\n-                if want_all and isprivate:\n-                    if self.options.private_members is None:\n-                        keep = False\n-                    else:\n-                        keep = membername in self.options.private_members\n-                else:\n-                    # keep documented attributes\n-                    keep = True\n-            elif want_all and isprivate:\n-                if has_doc or self.options.undoc_members:\n-                    if self.options.private_members is None:\n-                        keep = False\n-                    elif is_filtered_inherited_member(membername, obj):\n-                        keep = False\n-                    else:\n-                        keep = membername in self.options.private_members\n-                else:\n-                    keep = False\n-            else:\n-                if (self.options.members is ALL and\n-                        is_filtered_inherited_member(membername, obj)):\n-                    keep = False\n-                else:\n-                    # ignore undocumented members if :undoc-members: is not given\n-                    keep = has_doc or self.options.undoc_members\n-\n-            if isinstance(obj, ObjectMember) and obj.skipped:\n-                # forcedly skipped member (ex. a module attribute not defined in __all__)\n-                keep = False\n-\n-            # give the user a chance to decide whether this member\n-            # should be skipped\n-            if self.env.app:\n-                # let extensions preprocess docstrings\n-                try:\n-                    skip_user = self.env.app.emit_firstresult(\n-                        'autodoc-skip-member', self.objtype, membername, member,\n-                        not keep, self.options)\n-                    if skip_user is not None:\n-                        keep = not skip_user\n-                except Exception as exc:\n-                    logger.warning(__('autodoc: failed to determine %r to be documented, '\n-                                      'the following exception was raised:\\n%s'),\n-                                   member, exc, type='autodoc')\n-                    keep = False\n-\n-            if keep:\n-                ret.append((membername, member, isattr))\n-\n-        return ret\n-\n-    def document_members(self, all_members: bool = False) -> None:\n-        \"\"\"Generate reST for member documentation.\n-\n-        If *all_members* is True, do all members, else those given by\n-        *self.options.members*.\n-        \"\"\"\n-        # set current namespace for finding members\n-        self.env.temp_data['autodoc:module'] = self.modname\n-        if self.objpath:\n-            self.env.temp_data['autodoc:class'] = self.objpath[0]\n-\n-        want_all = (all_members or\n-                    self.options.inherited_members or\n-                    self.options.members is ALL)\n-        # find out which members are documentable\n-        members_check_module, members = self.get_object_members(want_all)\n-\n-        # document non-skipped members\n-        memberdocumenters: List[Tuple[Documenter, bool]] = []\n-        for (mname, member, isattr) in self.filter_members(members, want_all):\n-            classes = [cls for cls in self.documenters.values()\n-                       if cls.can_document_member(member, mname, isattr, self)]\n-            if not classes:\n-                # don't know how to document this member\n-                continue\n-            # prefer the documenter with the highest priority\n-            classes.sort(key=lambda cls: cls.priority)\n-            # give explicitly separated module name, so that members\n-            # of inner classes can be documented\n-            full_mname = self.modname + '::' + '.'.join(self.objpath + [mname])\n-            documenter = classes[-1](self.directive, full_mname, self.indent)\n-            memberdocumenters.append((documenter, isattr))\n-\n-        member_order = self.options.member_order or self.config.autodoc_member_order\n-        memberdocumenters = self.sort_members(memberdocumenters, member_order)\n-\n-        for documenter, isattr in memberdocumenters:\n-            documenter.generate(\n-                all_members=True, real_modname=self.real_modname,\n-                check_module=members_check_module and not isattr)\n-\n-        # reset current objects\n-        self.env.temp_data['autodoc:module'] = None\n-        self.env.temp_data['autodoc:class'] = None\n-\n-    def sort_members(self, documenters: List[Tuple[\"Documenter\", bool]],\n-                     order: str) -> List[Tuple[\"Documenter\", bool]]:\n-        \"\"\"Sort the given member list.\"\"\"\n-        if order == 'groupwise':\n-            # sort by group; alphabetically within groups\n-            documenters.sort(key=lambda e: (e[0].member_order, e[0].name))\n-        elif order == 'bysource':\n-            if self.analyzer:\n-                # sort by source order, by virtue of the module analyzer\n-                tagorder = self.analyzer.tagorder\n-\n-                def keyfunc(entry: Tuple[Documenter, bool]) -> int:\n-                    fullname = entry[0].name.split('::')[1]\n-                    return tagorder.get(fullname, len(tagorder))\n-                documenters.sort(key=keyfunc)\n-            else:\n-                # Assume that member discovery order matches source order.\n-                # This is a reasonable assumption in Python 3.6 and up, where\n-                # module.__dict__ is insertion-ordered.\n-                pass\n-        else:  # alphabetical\n-            documenters.sort(key=lambda e: e[0].name)\n-\n-        return documenters\n-\n-    def generate(self, more_content: Optional[StringList] = None, real_modname: str = None,\n-                 check_module: bool = False, all_members: bool = False) -> None:\n-        \"\"\"Generate reST for the object given by *self.name*, and possibly for\n-        its members.\n-\n-        If *more_content* is given, include that content. If *real_modname* is\n-        given, use that module name to find attribute docs. If *check_module* is\n-        True, only generate if the object is defined in the module name it is\n-        imported from. If *all_members* is True, document all members.\n-        \"\"\"\n-        if not self.parse_name():\n-            # need a module to import\n-            logger.warning(\n-                __('don\\'t know which module to import for autodocumenting '\n-                   '%r (try placing a \"module\" or \"currentmodule\" directive '\n-                   'in the document, or giving an explicit module name)') %\n-                self.name, type='autodoc')\n-            return\n-\n-        # now, import the module and get object to document\n-        if not self.import_object():\n-            return\n-\n-        # If there is no real module defined, figure out which to use.\n-        # The real module is used in the module analyzer to look up the module\n-        # where the attribute documentation would actually be found in.\n-        # This is used for situations where you have a module that collects the\n-        # functions and classes of internal submodules.\n-        guess_modname = self.get_real_modname()\n-        self.real_modname: str = real_modname or guess_modname\n-\n-        # try to also get a source code analyzer for attribute docs\n-        try:\n-            self.analyzer = ModuleAnalyzer.for_module(self.real_modname)\n-            # parse right now, to get PycodeErrors on parsing (results will\n-            # be cached anyway)\n-            self.analyzer.find_attr_docs()\n-        except PycodeError as exc:\n-            logger.debug('[autodoc] module analyzer failed: %s', exc)\n-            # no source file -- e.g. for builtin and C modules\n-            self.analyzer = None\n-            # at least add the module.__file__ as a dependency\n-            if hasattr(self.module, '__file__') and self.module.__file__:\n-                self.directive.record_dependencies.add(self.module.__file__)\n-        else:\n-            self.directive.record_dependencies.add(self.analyzer.srcname)\n-\n-        if self.real_modname != guess_modname:\n-            # Add module to dependency list if target object is defined in other module.\n-            try:\n-                analyzer = ModuleAnalyzer.for_module(guess_modname)\n-                self.directive.record_dependencies.add(analyzer.srcname)\n-            except PycodeError:\n-                pass\n-\n-        # check __module__ of object (for members not given explicitly)\n-        if check_module:\n-            if not self.check_module():\n-                return\n-\n-        sourcename = self.get_sourcename()\n-\n-        # make sure that the result starts with an empty line.  This is\n-        # necessary for some situations where another directive preprocesses\n-        # reST and no starting newline is present\n-        self.add_line('', sourcename)\n-\n-        # format the object's signature, if any\n-        try:\n-            sig = self.format_signature()\n-        except Exception as exc:\n-            logger.warning(__('error while formatting signature for %s: %s'),\n-                           self.fullname, exc, type='autodoc')\n-            return\n-\n-        # generate the directive header and options, if applicable\n-        self.add_directive_header(sig)\n-        self.add_line('', sourcename)\n-\n-        # e.g. the module directive doesn't have content\n-        self.indent += self.content_indent\n-\n-        # add all content (from docstrings, attribute docs etc.)\n-        self.add_content(more_content)\n-\n-        # document members, if possible\n-        self.document_members(all_members)\n-\n-\n-class ModuleDocumenter(Documenter):\n-    \"\"\"\n-    Specialized Documenter subclass for modules.\n-    \"\"\"\n-    objtype = 'module'\n-    content_indent = ''\n-    titles_allowed = True\n-\n-    option_spec: OptionSpec = {\n-        'members': members_option, 'undoc-members': bool_option,\n-        'noindex': bool_option, 'inherited-members': inherited_members_option,\n-        'show-inheritance': bool_option, 'synopsis': identity,\n-        'platform': identity, 'deprecated': bool_option,\n-        'member-order': member_order_option, 'exclude-members': exclude_members_option,\n-        'private-members': members_option, 'special-members': members_option,\n-        'imported-members': bool_option, 'ignore-module-all': bool_option\n-    }\n-\n-    def __init__(self, *args: Any) -> None:\n-        super().__init__(*args)\n-        merge_members_option(self.options)\n-        self.__all__: Optional[Sequence[str]] = None\n-\n-    @classmethod\n-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any\n-                            ) -> bool:\n-        # don't document submodules automatically\n-        return False\n-\n-    def resolve_name(self, modname: str, parents: Any, path: str, base: Any\n-                     ) -> Tuple[str, List[str]]:\n-        if modname is not None:\n-            logger.warning(__('\"::\" in automodule name doesn\\'t make sense'),\n-                           type='autodoc')\n-        return (path or '') + base, []\n-\n-    def parse_name(self) -> bool:\n-        ret = super().parse_name()\n-        if self.args or self.retann:\n-            logger.warning(__('signature arguments or return annotation '\n-                              'given for automodule %s') % self.fullname,\n-                           type='autodoc')\n-        return ret\n-\n-    def import_object(self, raiseerror: bool = False) -> bool:\n-        ret = super().import_object(raiseerror)\n-\n-        try:\n-            if not self.options.ignore_module_all:\n-                self.__all__ = inspect.getall(self.object)\n-        except ValueError as exc:\n-            # invalid __all__ found.\n-            logger.warning(__('__all__ should be a list of strings, not %r '\n-                              '(in module %s) -- ignoring __all__') %\n-                           (exc.args[0], self.fullname), type='autodoc')\n-\n-        return ret\n-\n-    def add_directive_header(self, sig: str) -> None:\n-        Documenter.add_directive_header(self, sig)\n-\n-        sourcename = self.get_sourcename()\n-\n-        # add some module-specific options\n-        if self.options.synopsis:\n-            self.add_line('   :synopsis: ' + self.options.synopsis, sourcename)\n-        if self.options.platform:\n-            self.add_line('   :platform: ' + self.options.platform, sourcename)\n-        if self.options.deprecated:\n-            self.add_line('   :deprecated:', sourcename)\n-\n-    def get_module_members(self) -> Dict[str, ObjectMember]:\n-        \"\"\"Get members of target module.\"\"\"\n-        if self.analyzer:\n-            attr_docs = self.analyzer.attr_docs\n-        else:\n-            attr_docs = {}\n-\n-        members: Dict[str, ObjectMember] = {}\n-        for name in dir(self.object):\n-            try:\n-                value = safe_getattr(self.object, name, None)\n-                if ismock(value):\n-                    value = undecorate(value)\n-                docstring = attr_docs.get(('', name), [])\n-                members[name] = ObjectMember(name, value, docstring=\"\\n\".join(docstring))\n-            except AttributeError:\n-                continue\n-\n-        # annotation only member (ex. attr: int)\n-        for name in inspect.getannotations(self.object):\n-            if name not in members:\n-                docstring = attr_docs.get(('', name), [])\n-                members[name] = ObjectMember(name, INSTANCEATTR,\n-                                             docstring=\"\\n\".join(docstring))\n-\n-        return members\n-\n-    def get_object_members(self, want_all: bool) -> Tuple[bool, ObjectMembers]:\n-        members = self.get_module_members()\n-        if want_all:\n-            if self.__all__ is None:\n-                # for implicit module members, check __module__ to avoid\n-                # documenting imported objects\n-                return True, list(members.values())\n-            else:\n-                for member in members.values():\n-                    if member.__name__ not in self.__all__:\n-                        member.skipped = True\n-\n-                return False, list(members.values())\n-        else:\n-            memberlist = self.options.members or []\n-            ret = []\n-            for name in memberlist:\n-                if name in members:\n-                    ret.append(members[name])\n-                else:\n-                    logger.warning(__('missing attribute mentioned in :members: option: '\n-                                      'module %s, attribute %s') %\n-                                   (safe_getattr(self.object, '__name__', '???'), name),\n-                                   type='autodoc')\n-            return False, ret\n-\n-    def sort_members(self, documenters: List[Tuple[\"Documenter\", bool]],\n-                     order: str) -> List[Tuple[\"Documenter\", bool]]:\n-        if order == 'bysource' and self.__all__:\n-            # Sort alphabetically first (for members not listed on the __all__)\n-            documenters.sort(key=lambda e: e[0].name)\n-\n-            # Sort by __all__\n-            def keyfunc(entry: Tuple[Documenter, bool]) -> int:\n-                name = entry[0].name.split('::')[1]\n-                if self.__all__ and name in self.__all__:\n-                    return self.__all__.index(name)\n-                else:\n-                    return len(self.__all__)\n-            documenters.sort(key=keyfunc)\n-\n-            return documenters\n-        else:\n-            return super().sort_members(documenters, order)\n-\n \n class ModuleLevelDocumenter(Documenter):\n     \"\"\"\n-    Specialized Documenter subclass for objects on module level (functions,\n-    classes, data/constants).\n-    \"\"\"\n-    def resolve_name(self, modname: str, parents: Any, path: str, base: Any\n-                     ) -> Tuple[str, List[str]]:\n-        if modname is None:\n-            if path:\n-                modname = path.rstrip('.')\n-            else:\n-                # if documenting a toplevel object without explicit module,\n-                # it can be contained in another auto directive ...\n-                modname = self.env.temp_data.get('autodoc:module')\n-                # ... or in the scope of a module directive\n-                if not modname:\n-                    modname = self.env.ref_context.get('py:module')\n-                # ... else, it stays None, which means invalid\n-        return modname, parents + [base]\n-\n-\n-class ClassLevelDocumenter(Documenter):\n-    \"\"\"\n-    Specialized Documenter subclass for objects on class level (methods,\n-    attributes).\n-    \"\"\"\n-    def resolve_name(self, modname: str, parents: Any, path: str, base: Any\n-                     ) -> Tuple[str, List[str]]:\n-        if modname is None:\n-            if path:\n-                mod_cls = path.rstrip('.')\n-            else:\n-                mod_cls = None\n-                # if documenting a class-level object without path,\n-                # there must be a current class, either from a parent\n-                # auto directive ...\n-                mod_cls = self.env.temp_data.get('autodoc:class')\n-                # ... or from a class directive\n-                if mod_cls is None:\n-                    mod_cls = self.env.ref_context.get('py:class')\n-                # ... if still None, there's no way to know\n-                if mod_cls is None:\n-                    return None, []\n-            modname, sep, cls = mod_cls.rpartition('.')\n-            parents = [cls]\n-            # if the module name is still missing, get it like above\n-            if not modname:\n-                modname = self.env.temp_data.get('autodoc:module')\n-            if not modname:\n-                modname = self.env.ref_context.get('py:module')\n-            # ... else, it stays None, which means invalid\n-        return modname, parents + [base]\n-\n-\n-class DocstringSignatureMixin:\n-    \"\"\"\n-    Mixin for FunctionDocumenter and MethodDocumenter to provide the\n-    feature of reading the signature from the docstring.\n-    \"\"\"\n-    _new_docstrings: List[List[str]] = None\n-    _signatures: List[str] = None\n-\n-    def _find_signature(self) -> Tuple[str, str]:\n-        # candidates of the object name\n-        valid_names = [self.objpath[-1]]  # type: ignore\n-        if isinstance(self, ClassDocumenter):\n-            valid_names.append('__init__')\n-            if hasattr(self.object, '__mro__'):\n-                valid_names.extend(cls.__name__ for cls in self.object.__mro__)\n-\n-        docstrings = self.get_doc()\n-        if docstrings is None:\n-            return None, None\n-        self._new_docstrings = docstrings[:]\n-        self._signatures = []\n-        result = None\n-        for i, doclines in enumerate(docstrings):\n-            for j, line in enumerate(doclines):\n-                if not line:\n-                    # no lines in docstring, no match\n-                    break\n-\n-                if line.endswith('\\\\'):\n-                    line = line.rstrip('\\\\').rstrip()\n-\n-                # match first line of docstring against signature RE\n-                match = py_ext_sig_re.match(line)\n-                if not match:\n-                    break\n-                exmod, path, base, args, retann = match.groups()\n-\n-                # the base name must match ours\n-                if base not in valid_names:\n-                    break\n-\n-                # re-prepare docstring to ignore more leading indentation\n-                tab_width = self.directive.state.document.settings.tab_width  # type: ignore\n-                self._new_docstrings[i] = prepare_docstring('\\n'.join(doclines[j + 1:]),\n-                                                            tabsize=tab_width)\n-\n-                if result is None:\n-                    # first signature\n-                    result = args, retann\n-                else:\n-                    # subsequent signatures\n-                    self._signatures.append(\"(%s) -> %s\" % (args, retann))\n-\n-            if result:\n-                # finish the loop when signature found\n-                break\n-\n-        return result\n-\n-    def get_doc(self, ignore: int = None) -> List[List[str]]:\n-        if self._new_docstrings is not None:\n-            return self._new_docstrings\n-        return super().get_doc(ignore)  # type: ignore\n-\n-    def format_signature(self, **kwargs: Any) -> str:\n-        if self.args is None and self.config.autodoc_docstring_signature:  # type: ignore\n-            # only act if a signature is not explicitly given already, and if\n-            # the feature is enabled\n-            result = self._find_signature()\n-            if result is not None:\n-                self.args, self.retann = result\n-        sig = super().format_signature(**kwargs)  # type: ignore\n-        if self._signatures:\n-            return \"\\n\".join([sig] + self._signatures)\n-        else:\n-            return sig\n-\n-\n-class DocstringStripSignatureMixin(DocstringSignatureMixin):\n-    \"\"\"\n-    Mixin for AttributeDocumenter to provide the\n-    feature of stripping any function signature from the docstring.\n-    \"\"\"\n-    def format_signature(self, **kwargs: Any) -> str:\n-        if self.args is None and self.config.autodoc_docstring_signature:  # type: ignore\n-            # only act if a signature is not explicitly given already, and if\n-            # the feature is enabled\n-            result = self._find_signature()\n-            if result is not None:\n-                # Discarding _args is a only difference with\n-                # DocstringSignatureMixin.format_signature.\n-                # Documenter.format_signature use self.args value to format.\n-                _args, self.retann = result\n-        return super().format_signature(**kwargs)\n-\n-\n-class FunctionDocumenter(DocstringSignatureMixin, ModuleLevelDocumenter):  # type: ignore\n-    \"\"\"\n-    Specialized Documenter subclass for functions.\n-    \"\"\"\n-    objtype = 'function'\n-    member_order = 30\n-\n-    @classmethod\n-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any\n-                            ) -> bool:\n-        # supports functions, builtins and bound methods exported at the module level\n-        return (inspect.isfunction(member) or inspect.isbuiltin(member) or\n-                (inspect.isroutine(member) and isinstance(parent, ModuleDocumenter)))\n-\n-    def format_args(self, **kwargs: Any) -> str:\n-        if self.config.autodoc_typehints in ('none', 'description'):\n-            kwargs.setdefault('show_annotation', False)\n-\n-        try:\n-            self.env.app.emit('autodoc-before-process-signature', self.object, False)\n-            sig = inspect.signature(self.object, type_aliases=self.config.autodoc_type_aliases)\n-            args = stringify_signature(sig, **kwargs)\n-        except TypeError as exc:\n-            logger.warning(__(\"Failed to get a function signature for %s: %s\"),\n-                           self.fullname, exc)\n-            return None\n-        except ValueError:\n-            args = ''\n-\n-        if self.config.strip_signature_backslash:\n-            # escape backslashes for reST\n-            args = args.replace('\\\\', '\\\\\\\\')\n-        return args\n-\n-    def document_members(self, all_members: bool = False) -> None:\n-        pass\n-\n-    def add_directive_header(self, sig: str) -> None:\n-        sourcename = self.get_sourcename()\n-        super().add_directive_header(sig)\n-\n-        if inspect.iscoroutinefunction(self.object):\n-            self.add_line('   :async:', sourcename)\n-\n-    def format_signature(self, **kwargs: Any) -> str:\n-        sigs = []\n-        if (self.analyzer and\n-                '.'.join(self.objpath) in self.analyzer.overloads and\n-                self.config.autodoc_typehints != 'none'):\n-            # Use signatures for overloaded functions instead of the implementation function.\n-            overloaded = True\n-        else:\n-            overloaded = False\n-            sig = super().format_signature(**kwargs)\n-            sigs.append(sig)\n-\n-        if inspect.is_singledispatch_function(self.object):\n-            # append signature of singledispatch'ed functions\n-            for typ, func in self.object.registry.items():\n-                if typ is object:\n-                    pass  # default implementation. skipped.\n-                else:\n-                    dispatchfunc = self.annotate_to_first_argument(func, typ)\n-                    if dispatchfunc:\n-                        documenter = FunctionDocumenter(self.directive, '')\n-                        documenter.object = dispatchfunc\n-                        documenter.objpath = [None]\n-                        sigs.append(documenter.format_signature())\n-        if overloaded:\n-            actual = inspect.signature(self.object,\n-                                       type_aliases=self.config.autodoc_type_aliases)\n-            __globals__ = safe_getattr(self.object, '__globals__', {})\n-            for overload in self.analyzer.overloads.get('.'.join(self.objpath)):\n-                overload = self.merge_default_value(actual, overload)\n-                overload = evaluate_signature(overload, __globals__,\n-                                              self.config.autodoc_type_aliases)\n-\n-                sig = stringify_signature(overload, **kwargs)\n-                sigs.append(sig)\n-\n-        return \"\\n\".join(sigs)\n-\n-    def merge_default_value(self, actual: Signature, overload: Signature) -> Signature:\n-        \"\"\"Merge default values of actual implementation to the overload variants.\"\"\"\n-        parameters = list(overload.parameters.values())\n-        for i, param in enumerate(parameters):\n-            actual_param = actual.parameters.get(param.name)\n-            if actual_param and param.default == '...':\n-                parameters[i] = param.replace(default=actual_param.default)\n-\n-        return overload.replace(parameters=parameters)\n-\n-    def annotate_to_first_argument(self, func: Callable, typ: Type) -> Optional[Callable]:\n-        \"\"\"Annotate type hint to the first argument of function if needed.\"\"\"\n-        try:\n-            sig = inspect.signature(func, type_aliases=self.config.autodoc_type_aliases)\n-        except TypeError as exc:\n-            logger.warning(__(\"Failed to get a function signature for %s: %s\"),\n-                           self.fullname, exc)\n-            return None\n-        except ValueError:\n-            return None\n-\n-        if len(sig.parameters) == 0:\n-            return None\n-\n-        def dummy():\n-            pass\n-\n-        params = list(sig.parameters.values())\n-        if params[0].annotation is Parameter.empty:\n-            params[0] = params[0].replace(annotation=typ)\n-            try:\n-                dummy.__signature__ = sig.replace(parameters=params)  # type: ignore\n-                return dummy\n-            except (AttributeError, TypeError):\n-                # failed to update signature (ex. built-in or extension types)\n-                return None\n-        else:\n-            return None\n-\n-\n-class DecoratorDocumenter(FunctionDocumenter):\n-    \"\"\"\n-    Specialized Documenter subclass for decorator functions.\n-    \"\"\"\n-    objtype = 'decorator'\n-\n-    # must be lower than FunctionDocumenter\n-    priority = -1\n-\n-    def format_args(self, **kwargs: Any) -> Any:\n-        args = super().format_args(**kwargs)\n-        if ',' in args:\n-            return args\n-        else:\n-            return None\n-\n-\n-# Types which have confusing metaclass signatures it would be best not to show.\n-# These are listed by name, rather than storing the objects themselves, to avoid\n-# needing to import the modules.\n-_METACLASS_CALL_BLACKLIST = [\n-    'enum.EnumMeta.__call__',\n-]\n-\n-\n-# Types whose __new__ signature is a pass-thru.\n-_CLASS_NEW_BLACKLIST = [\n-    'typing.Generic.__new__',\n-]\n-\n-\n-class ClassDocumenter(DocstringSignatureMixin, ModuleLevelDocumenter):  # type: ignore\n-    \"\"\"\n-    Specialized Documenter subclass for classes.\n+    Specialized Documenter subclass for objects on module level (classes,\n+    functions, data/constants).\n     \"\"\"\n-    objtype = 'class'\n-    member_order = 20\n-    option_spec: OptionSpec = {\n-        'members': members_option, 'undoc-members': bool_option,\n-        'noindex': bool_option, 'inherited-members': inherited_members_option,\n-        'show-inheritance': bool_option, 'member-order': member_order_option,\n-        'exclude-members': exclude_members_option,\n-        'private-members': members_option, 'special-members': members_option,\n-        'class-doc-from': class_doc_from_option,\n-    }\n-\n-    _signature_class: Any = None\n-    _signature_method_name: str = None\n-\n-    def __init__(self, *args: Any) -> None:\n-        super().__init__(*args)\n-\n-        if self.config.autodoc_class_signature == 'separated':\n-            # show __init__() method\n-            if self.options.special_members is None:\n-                self.options['special-members'] = {'__new__', '__init__'}\n-            else:\n-                self.options.special_members.append('__new__')\n-                self.options.special_members.append('__init__')\n-\n-        merge_members_option(self.options)\n-\n-    @classmethod\n-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any\n-                            ) -> bool:\n-        return isinstance(member, type)\n-\n-    def import_object(self, raiseerror: bool = False) -> bool:\n-        ret = super().import_object(raiseerror)\n-        # if the class is documented under another name, document it\n-        # as data/attribute\n-        if ret:\n-            if hasattr(self.object, '__name__'):\n-                self.doc_as_attr = (self.objpath[-1] != self.object.__name__)\n-            else:\n-                self.doc_as_attr = True\n-        return ret\n-\n-    def _get_signature(self) -> Tuple[Optional[Any], Optional[str], Optional[Signature]]:\n-        def get_user_defined_function_or_method(obj: Any, attr: str) -> Any:\n-            \"\"\" Get the `attr` function or method from `obj`, if it is user-defined. \"\"\"\n-            if inspect.is_builtin_class_method(obj, attr):\n-                return None\n-            attr = self.get_attr(obj, attr, None)\n-            if not (inspect.ismethod(attr) or inspect.isfunction(attr)):\n-                return None\n-            return attr\n-\n-        # This sequence is copied from inspect._signature_from_callable.\n-        # ValueError means that no signature could be found, so we keep going.\n-\n-        # First, we check the obj has a __signature__ attribute\n-        if (hasattr(self.object, '__signature__') and\n-                isinstance(self.object.__signature__, Signature)):\n-            return None, None, self.object.__signature__\n-\n-        # Next, let's see if it has an overloaded __call__ defined\n-        # in its metaclass\n-        call = get_user_defined_function_or_method(type(self.object), '__call__')\n-\n-        if call is not None:\n-            if \"{0.__module__}.{0.__qualname__}\".format(call) in _METACLASS_CALL_BLACKLIST:\n-                call = None\n-\n-        if call is not None:\n-            self.env.app.emit('autodoc-before-process-signature', call, True)\n-            try:\n-                sig = inspect.signature(call, bound_method=True,\n-                                        type_aliases=self.config.autodoc_type_aliases)\n-                return type(self.object), '__call__', sig\n-            except ValueError:\n-                pass\n-\n-        # Now we check if the 'obj' class has a '__new__' method\n-        new = get_user_defined_function_or_method(self.object, '__new__')\n-\n-        if new is not None:\n-            if \"{0.__module__}.{0.__qualname__}\".format(new) in _CLASS_NEW_BLACKLIST:\n-                new = None\n-\n-        if new is not None:\n-            self.env.app.emit('autodoc-before-process-signature', new, True)\n-            try:\n-                sig = inspect.signature(new, bound_method=True,\n-                                        type_aliases=self.config.autodoc_type_aliases)\n-                return self.object, '__new__', sig\n-            except ValueError:\n-                pass\n-\n-        # Finally, we should have at least __init__ implemented\n-        init = get_user_defined_function_or_method(self.object, '__init__')\n-        if init is not None:\n-            self.env.app.emit('autodoc-before-process-signature', init, True)\n-            try:\n-                sig = inspect.signature(init, bound_method=True,\n-                                        type_aliases=self.config.autodoc_type_aliases)\n-                return self.object, '__init__', sig\n-            except ValueError:\n-                pass\n-\n-        # None of the attributes are user-defined, so fall back to let inspect\n-        # handle it.\n-        # We don't know the exact method that inspect.signature will read\n-        # the signature from, so just pass the object itself to our hook.\n-        self.env.app.emit('autodoc-before-process-signature', self.object, False)\n-        try:\n-            sig = inspect.signature(self.object, bound_method=False,\n-                                    type_aliases=self.config.autodoc_type_aliases)\n-            return None, None, sig\n-        except ValueError:\n-            pass\n-\n-        # Still no signature: happens e.g. for old-style classes\n-        # with __init__ in C and no `__text_signature__`.\n-        return None, None, None\n-\n-    def format_args(self, **kwargs: Any) -> str:\n-        if self.config.autodoc_typehints in ('none', 'description'):\n-            kwargs.setdefault('show_annotation', False)\n-\n-        try:\n-            self._signature_class, self._signature_method_name, sig = self._get_signature()\n-        except TypeError as exc:\n-            # __signature__ attribute contained junk\n-            logger.warning(__(\"Failed to get a constructor signature for %s: %s\"),\n-                           self.fullname, exc)\n-            return None\n-\n-        if sig is None:\n-            return None\n-\n-        return stringify_signature(sig, show_return_annotation=False, **kwargs)\n-\n-    def format_signature(self, **kwargs: Any) -> str:\n-        if self.doc_as_attr:\n-            return ''\n-        if self.config.autodoc_class_signature == 'separated':\n-            # do not show signatures\n-            return ''\n-\n-        sig = super().format_signature()\n-        sigs = []\n-\n-        overloads = self.get_overloaded_signatures()\n-        if overloads and self.config.autodoc_typehints != 'none':\n-            # Use signatures for overloaded methods instead of the implementation method.\n-            method = safe_getattr(self._signature_class, self._signature_method_name, None)\n-            __globals__ = safe_getattr(method, '__globals__', {})\n-            for overload in overloads:\n-                overload = evaluate_signature(overload, __globals__,\n-                                              self.config.autodoc_type_aliases)\n-\n-                parameters = list(overload.parameters.values())\n-                overload = overload.replace(parameters=parameters[1:],\n-                                            return_annotation=Parameter.empty)\n-                sig = stringify_signature(overload, **kwargs)\n-                sigs.append(sig)\n-        else:\n-            sigs.append(sig)\n-\n-        return \"\\n\".join(sigs)\n-\n-    def get_overloaded_signatures(self) -> List[Signature]:\n-        if self._signature_class and self._signature_method_name:\n-            for cls in self._signature_class.__mro__:\n-                try:\n-                    analyzer = ModuleAnalyzer.for_module(cls.__module__)\n-                    analyzer.analyze()\n-                    qualname = '.'.join([cls.__qualname__, self._signature_method_name])\n-                    if qualname in analyzer.overloads:\n-                        return analyzer.overloads.get(qualname)\n-                    elif qualname in analyzer.tagorder:\n-                        # the constructor is defined in the class, but not overrided.\n-                        return []\n-                except PycodeError:\n-                    pass\n-\n-        return []\n-\n-    def get_canonical_fullname(self) -> Optional[str]:\n-        __modname__ = safe_getattr(self.object, '__module__', self.modname)\n-        __qualname__ = safe_getattr(self.object, '__qualname__', None)\n-        if __qualname__ is None:\n-            __qualname__ = safe_getattr(self.object, '__name__', None)\n-        if __qualname__ and '<locals>' in __qualname__:\n-            # No valid qualname found if the object is defined as locals\n-            __qualname__ = None\n-\n-        if __modname__ and __qualname__:\n-            return '.'.join([__modname__, __qualname__])\n-        else:\n-            return None\n-\n-    def add_directive_header(self, sig: str) -> None:\n-        sourcename = self.get_sourcename()\n-\n-        if self.doc_as_attr:\n-            self.directivetype = 'attribute'\n-        super().add_directive_header(sig)\n-\n-        if self.analyzer and '.'.join(self.objpath) in self.analyzer.finals:\n-            self.add_line('   :final:', sourcename)\n-\n-        canonical_fullname = self.get_canonical_fullname()\n-        if not self.doc_as_attr and canonical_fullname and self.fullname != canonical_fullname:\n-            self.add_line('   :canonical: %s' % canonical_fullname, sourcename)\n-\n-        # add inheritance info, if wanted\n-        if not self.doc_as_attr and self.options.show_inheritance:\n-            if hasattr(self.object, '__orig_bases__') and len(self.object.__orig_bases__):\n-                # A subclass of generic types\n-                # refs: PEP-560 <https://www.python.org/dev/peps/pep-0560/>\n-                bases = list(self.object.__orig_bases__)\n-            elif hasattr(self.object, '__bases__') and len(self.object.__bases__):\n-                # A normal class\n-                bases = list(self.object.__bases__)\n-            else:\n-                bases = []\n-\n-            self.env.events.emit('autodoc-process-bases',\n-                                 self.fullname, self.object, self.options, bases)\n-\n-            base_classes = [restify(cls) for cls in bases]\n-            sourcename = self.get_sourcename()\n-            self.add_line('', sourcename)\n-            self.add_line('   ' + _('Bases: %s') % ', '.join(base_classes), sourcename)\n-\n-    def get_object_members(self, want_all: bool) -> Tuple[bool, ObjectMembers]:\n-        members = get_class_members(self.object, self.objpath, self.get_attr)\n-        if not want_all:\n-            if not self.options.members:\n-                return False, []  # type: ignore\n-            # specific members given\n-            selected = []\n-            for name in self.options.members:  # type: str\n-                if name in members:\n-                    selected.append(members[name])\n-                else:\n-                    logger.warning(__('missing attribute %s in object %s') %\n-                                   (name, self.fullname), type='autodoc')\n-            return False, selected\n-        elif self.options.inherited_members:\n-            return False, list(members.values())\n-        else:\n-            return False, [m for m in members.values() if m.class_ == self.object]\n-\n-    def get_doc(self, ignore: int = None) -> Optional[List[List[str]]]:\n-        if self.doc_as_attr:\n-            # Don't show the docstring of the class when it is an alias.\n-            comment = self.get_variable_comment()\n-            if comment:\n-                return []\n-            else:\n-                return None\n-\n-        lines = getattr(self, '_new_docstrings', None)\n-        if lines is not None:\n-            return lines\n-\n-        classdoc_from = self.options.get('class-doc-from', self.config.autoclass_content)\n-\n-        docstrings = []\n-        attrdocstring = self.get_attr(self.object, '__doc__', None)\n-        if attrdocstring:\n-            docstrings.append(attrdocstring)\n-\n-        # for classes, what the \"docstring\" is can be controlled via a\n-        # config value; the default is only the class docstring\n-        if classdoc_from in ('both', 'init'):\n-            __init__ = self.get_attr(self.object, '__init__', None)\n-            initdocstring = getdoc(__init__, self.get_attr,\n-                                   self.config.autodoc_inherit_docstrings,\n-                                   self.object, '__init__')\n-            # for new-style classes, no __init__ means default __init__\n-            if (initdocstring is not None and\n-                (initdocstring == object.__init__.__doc__ or  # for pypy\n-                 initdocstring.strip() == object.__init__.__doc__)):  # for !pypy\n-                initdocstring = None\n-            if not initdocstring:\n-                # try __new__\n-                __new__ = self.get_attr(self.object, '__new__', None)\n-                initdocstring = getdoc(__new__, self.get_attr,\n-                                       self.config.autodoc_inherit_docstrings,\n-                                       self.object, '__new__')\n-                # for new-style classes, no __new__ means default __new__\n-                if (initdocstring is not None and\n-                    (initdocstring == object.__new__.__doc__ or  # for pypy\n-                     initdocstring.strip() == object.__new__.__doc__)):  # for !pypy\n-                    initdocstring = None\n-            if initdocstring:\n-                if classdoc_from == 'init':\n-                    docstrings = [initdocstring]\n-                else:\n-                    docstrings.append(initdocstring)\n-\n-        tab_width = self.directive.state.document.settings.tab_width\n-        return [prepare_docstring(docstring, ignore, tab_width) for docstring in docstrings]\n-\n-    def get_variable_comment(self) -> Optional[List[str]]:\n-        try:\n-            key = ('', '.'.join(self.objpath))\n-            analyzer = ModuleAnalyzer.for_module(self.get_real_modname())\n-            analyzer.analyze()\n-            return list(self.analyzer.attr_docs.get(key, []))\n-        except PycodeError:\n-            return None\n-\n-    def add_content(self, more_content: Optional[StringList], no_docstring: bool = False\n-                    ) -> None:\n-        if self.doc_as_attr and not self.get_variable_comment():\n-            try:\n-                more_content = StringList([_('alias of %s') % restify(self.object)], source='')\n-            except AttributeError:\n-                pass  # Invalid class object is passed.\n-\n-        super().add_content(more_content)\n-\n-    def document_members(self, all_members: bool = False) -> None:\n-        if self.doc_as_attr:\n-            return\n-        super().document_members(all_members)\n-\n-    def generate(self, more_content: Optional[StringList] = None, real_modname: str = None,\n-                 check_module: bool = False, all_members: bool = False) -> None:\n-        # Do not pass real_modname and use the name from the __module__\n-        # attribute of the class.\n-        # If a class gets imported into the module real_modname\n-        # the analyzer won't find the source of the class, if\n-        # it looks in real_modname.\n-        return super().generate(more_content=more_content,\n-                                check_module=check_module,\n-                                all_members=all_members)\n-\n-\n-class ExceptionDocumenter(ClassDocumenter):\n-    \"\"\"\n-    Specialized ClassDocumenter subclass for exceptions.\n-    \"\"\"\n-    objtype = 'exception'\n-    member_order = 10\n-\n-    # needs a higher priority than ClassDocumenter\n-    priority = 10\n-\n-    @classmethod\n-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any\n-                            ) -> bool:\n-        return isinstance(member, type) and issubclass(member, BaseException)\n-\n-\n-class DataDocumenterMixinBase:\n-    # define types of instance variables\n-    config: Config = None\n-    env: BuildEnvironment = None\n-    modname: str = None\n-    parent: Any = None\n-    object: Any = None\n-    objpath: List[str] = None\n-\n-    def should_suppress_directive_header(self) -> bool:\n-        \"\"\"Check directive header should be suppressed.\"\"\"\n-        return False\n-\n-    def should_suppress_value_header(self) -> bool:\n-        \"\"\"Check :value: header should be suppressed.\"\"\"\n-        return False\n-\n-    def update_content(self, more_content: StringList) -> None:\n-        \"\"\"Update docstring for the NewType object.\"\"\"\n-        pass\n-\n-\n-class GenericAliasMixin(DataDocumenterMixinBase):\n-    \"\"\"\n-    Mixin for DataDocumenter and AttributeDocumenter to provide the feature for\n-    supporting GenericAliases.\n-    \"\"\"\n-\n-    def should_suppress_directive_header(self) -> bool:\n-        return (inspect.isgenericalias(self.object) or\n-                super().should_suppress_directive_header())\n-\n-    def update_content(self, more_content: StringList) -> None:\n-        if inspect.isgenericalias(self.object):\n-            more_content.append(_('alias of %s') % restify(self.object), '')\n-            more_content.append('', '')\n-\n-        super().update_content(more_content)\n-\n-\n-class NewTypeMixin(DataDocumenterMixinBase):\n-    \"\"\"\n-    Mixin for DataDocumenter and AttributeDocumenter to provide the feature for\n-    supporting NewTypes.\n-    \"\"\"\n-\n-    def should_suppress_directive_header(self) -> bool:\n-        return (inspect.isNewType(self.object) or\n-                super().should_suppress_directive_header())\n-\n-    def update_content(self, more_content: StringList) -> None:\n-        if inspect.isNewType(self.object):\n-            supertype = restify(self.object.__supertype__)\n-            more_content.append(_('alias of %s') % supertype, '')\n-            more_content.append('', '')\n-\n-        super().update_content(more_content)\n-\n-\n-class TypeVarMixin(DataDocumenterMixinBase):\n-    \"\"\"\n-    Mixin for DataDocumenter and AttributeDocumenter to provide the feature for\n-    supporting TypeVars.\n-    \"\"\"\n-\n-    def should_suppress_directive_header(self) -> bool:\n-        return (isinstance(self.object, TypeVar) or\n-                super().should_suppress_directive_header())\n-\n-    def get_doc(self, ignore: int = None) -> Optional[List[List[str]]]:\n-        if ignore is not None:\n-            warnings.warn(\"The 'ignore' argument to autodoc.%s.get_doc() is deprecated.\"\n-                          % self.__class__.__name__,\n-                          RemovedInSphinx50Warning, stacklevel=2)\n-\n-        if isinstance(self.object, TypeVar):\n-            if self.object.__doc__ != TypeVar.__doc__:\n-                return super().get_doc()  # type: ignore\n-            else:\n-                return []\n-        else:\n-            return super().get_doc()  # type: ignore\n-\n-    def update_content(self, more_content: StringList) -> None:\n-        if isinstance(self.object, TypeVar):\n-            attrs = [repr(self.object.__name__)]\n-            for constraint in self.object.__constraints__:\n-                attrs.append(stringify_typehint(constraint))\n-            if self.object.__bound__:\n-                attrs.append(r\"bound=\\ \" + restify(self.object.__bound__))\n-            if self.object.__covariant__:\n-                attrs.append(\"covariant=True\")\n-            if self.object.__contravariant__:\n-                attrs.append(\"contravariant=True\")\n-\n-            more_content.append(_('alias of TypeVar(%s)') % \", \".join(attrs), '')\n-            more_content.append('', '')\n-\n-        super().update_content(more_content)\n-\n-\n-class UninitializedGlobalVariableMixin(DataDocumenterMixinBase):\n-    \"\"\"\n-    Mixin for DataDocumenter to provide the feature for supporting uninitialized\n-    (type annotation only) global variables.\n-    \"\"\"\n-\n-    def import_object(self, raiseerror: bool = False) -> bool:\n-        try:\n-            return super().import_object(raiseerror=True)  # type: ignore\n-        except ImportError as exc:\n-            # annotation only instance variable (PEP-526)\n-            try:\n-                with mock(self.config.autodoc_mock_imports):\n-                    parent = import_module(self.modname, self.config.autodoc_warningiserror)\n-                    annotations = get_type_hints(parent, None,\n-                                                 self.config.autodoc_type_aliases)\n-                    if self.objpath[-1] in annotations:\n-                        self.object = UNINITIALIZED_ATTR\n-                        self.parent = parent\n-                        return True\n-            except ImportError:\n-                pass\n-\n-            if raiseerror:\n-                raise\n-            else:\n-                logger.warning(exc.args[0], type='autodoc', subtype='import_object')\n-                self.env.note_reread()\n-                return False\n-\n-    def should_suppress_value_header(self) -> bool:\n-        return (self.object is UNINITIALIZED_ATTR or\n-                super().should_suppress_value_header())\n-\n-    def get_doc(self, ignore: int = None) -> Optional[List[List[str]]]:\n-        if self.object is UNINITIALIZED_ATTR:\n-            return []\n-        else:\n-            return super().get_doc(ignore)  # type: ignore\n-\n-\n-class DataDocumenter(GenericAliasMixin, NewTypeMixin, TypeVarMixin,\n-                     UninitializedGlobalVariableMixin, ModuleLevelDocumenter):\n-    \"\"\"\n-    Specialized Documenter subclass for data items.\n-    \"\"\"\n-    objtype = 'data'\n-    member_order = 40\n-    priority = -10\n-    option_spec: OptionSpec = dict(ModuleLevelDocumenter.option_spec)\n-    option_spec[\"annotation\"] = annotation_option\n-    option_spec[\"no-value\"] = bool_option\n-\n-    @classmethod\n-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any\n-                            ) -> bool:\n-        return isinstance(parent, ModuleDocumenter) and isattr\n-\n-    def update_annotations(self, parent: Any) -> None:\n-        \"\"\"Update __annotations__ to support type_comment and so on.\"\"\"\n-        annotations = dict(inspect.getannotations(parent))\n-        parent.__annotations__ = annotations\n-\n-        try:\n-            analyzer = ModuleAnalyzer.for_module(self.modname)\n-            analyzer.analyze()\n-            for (classname, attrname), annotation in analyzer.annotations.items():\n-                if classname == '' and attrname not in annotations:\n-                    annotations[attrname] = annotation\n-        except PycodeError:\n-            pass\n-\n-    def import_object(self, raiseerror: bool = False) -> bool:\n-        ret = super().import_object(raiseerror)\n-        if self.parent:\n-            self.update_annotations(self.parent)\n-\n-        return ret\n-\n-    def should_suppress_value_header(self) -> bool:\n-        if super().should_suppress_value_header():\n-            return True\n-        else:\n-            doc = self.get_doc()\n-            docstring, metadata = separate_metadata('\\n'.join(sum(doc, [])))\n-            if 'hide-value' in metadata:\n-                return True\n-\n-        return False\n-\n-    def add_directive_header(self, sig: str) -> None:\n-        super().add_directive_header(sig)\n-        sourcename = self.get_sourcename()\n-        if self.options.annotation is SUPPRESS or self.should_suppress_directive_header():\n-            pass\n-        elif self.options.annotation:\n-            self.add_line('   :annotation: %s' % self.options.annotation,\n-                          sourcename)\n-        else:\n-            if self.config.autodoc_typehints != 'none':\n-                # obtain annotation for this data\n-                annotations = get_type_hints(self.parent, None,\n-                                             self.config.autodoc_type_aliases)\n-                if self.objpath[-1] in annotations:\n-                    objrepr = stringify_typehint(annotations.get(self.objpath[-1]))\n-                    self.add_line('   :type: ' + objrepr, sourcename)\n-\n-            try:\n-                if self.options.no_value or self.should_suppress_value_header():\n-                    pass\n-                else:\n-                    objrepr = object_description(self.object)\n-                    self.add_line('   :value: ' + objrepr, sourcename)\n-            except ValueError:\n-                pass\n-\n-    def document_members(self, all_members: bool = False) -> None:\n-        pass\n-\n-    def get_real_modname(self) -> str:\n-        real_modname = self.get_attr(self.parent or self.object, '__module__', None)\n-        return real_modname or self.modname\n-\n-    def get_module_comment(self, attrname: str) -> Optional[List[str]]:\n-        try:\n-            analyzer = ModuleAnalyzer.for_module(self.modname)\n-            analyzer.analyze()\n-            key = ('', attrname)\n-            if key in analyzer.attr_docs:\n-                return list(analyzer.attr_docs[key])\n-        except PycodeError:\n-            pass\n-\n-        return None\n-\n-    def get_doc(self, ignore: int = None) -> Optional[List[List[str]]]:\n-        # Check the variable has a docstring-comment\n-        comment = self.get_module_comment(self.objpath[-1])\n-        if comment:\n-            return [comment]\n-        else:\n-            return super().get_doc(ignore)\n-\n-    def add_content(self, more_content: Optional[StringList], no_docstring: bool = False\n-                    ) -> None:\n-        # Disable analyzing variable comment on Documenter.add_content() to control it on\n-        # DataDocumenter.add_content()\n-        self.analyzer = None\n-\n-        if not more_content:\n-            more_content = StringList()\n-\n-        self.update_content(more_content)\n-        super().add_content(more_content, no_docstring=no_docstring)\n-\n-\n-class NewTypeDataDocumenter(DataDocumenter):\n-    \"\"\"\n-    Specialized Documenter subclass for NewTypes.\n-\n-    Note: This must be invoked before FunctionDocumenter because NewType is a kind of\n-    function object.\n-    \"\"\"\n-\n-    objtype = 'newtypedata'\n-    directivetype = 'data'\n-    priority = FunctionDocumenter.priority + 1\n-\n-    @classmethod\n-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any\n-                            ) -> bool:\n-        return inspect.isNewType(member) and isattr\n-\n-\n-class MethodDocumenter(DocstringSignatureMixin, ClassLevelDocumenter):  # type: ignore\n-    \"\"\"\n-    Specialized Documenter subclass for methods (normal, static and class).\n-    \"\"\"\n-    objtype = 'method'\n-    directivetype = 'method'\n-    member_order = 50\n-    priority = 1  # must be more than FunctionDocumenter\n-\n-    @classmethod\n-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any\n-                            ) -> bool:\n-        return inspect.isroutine(member) and not isinstance(parent, ModuleDocumenter)\n-\n-    def import_object(self, raiseerror: bool = False) -> bool:\n-        ret = super().import_object(raiseerror)\n-        if not ret:\n-            return ret\n-\n-        # to distinguish classmethod/staticmethod\n-        obj = self.parent.__dict__.get(self.object_name)\n-        if obj is None:\n-            obj = self.object\n-\n-        if (inspect.isclassmethod(obj) or\n-                inspect.isstaticmethod(obj, cls=self.parent, name=self.object_name)):\n-            # document class and static members before ordinary ones\n-            self.member_order = self.member_order - 1\n-\n-        return ret\n-\n-    def format_args(self, **kwargs: Any) -> str:\n-        if self.config.autodoc_typehints in ('none', 'description'):\n-            kwargs.setdefault('show_annotation', False)\n-\n-        try:\n-            if self.object == object.__init__ and self.parent != object:\n-                # Classes not having own __init__() method are shown as no arguments.\n-                #\n-                # Note: The signature of object.__init__() is (self, /, *args, **kwargs).\n-                #       But it makes users confused.\n-                args = '()'\n-            else:\n-                if inspect.isstaticmethod(self.object, cls=self.parent, name=self.object_name):\n-                    self.env.app.emit('autodoc-before-process-signature', self.object, False)\n-                    sig = inspect.signature(self.object, bound_method=False,\n-                                            type_aliases=self.config.autodoc_type_aliases)\n-                else:\n-                    self.env.app.emit('autodoc-before-process-signature', self.object, True)\n-                    sig = inspect.signature(self.object, bound_method=True,\n-                                            type_aliases=self.config.autodoc_type_aliases)\n-                args = stringify_signature(sig, **kwargs)\n-        except TypeError as exc:\n-            logger.warning(__(\"Failed to get a method signature for %s: %s\"),\n-                           self.fullname, exc)\n-            return None\n-        except ValueError:\n-            args = ''\n-\n-        if self.config.strip_signature_backslash:\n-            # escape backslashes for reST\n-            args = args.replace('\\\\', '\\\\\\\\')\n-        return args\n-\n-    def add_directive_header(self, sig: str) -> None:\n-        super().add_directive_header(sig)\n-\n-        sourcename = self.get_sourcename()\n-        obj = self.parent.__dict__.get(self.object_name, self.object)\n-        if inspect.isabstractmethod(obj):\n-            self.add_line('   :abstractmethod:', sourcename)\n-        if inspect.iscoroutinefunction(obj):\n-            self.add_line('   :async:', sourcename)\n-        if inspect.isclassmethod(obj):\n-            self.add_line('   :classmethod:', sourcename)\n-        if inspect.isstaticmethod(obj, cls=self.parent, name=self.object_name):\n-            self.add_line('   :staticmethod:', sourcename)\n-        if self.analyzer and '.'.join(self.objpath) in self.analyzer.finals:\n-            self.add_line('   :final:', sourcename)\n-\n-    def document_members(self, all_members: bool = False) -> None:\n-        pass\n-\n-    def format_signature(self, **kwargs: Any) -> str:\n-        sigs = []\n-        if (self.analyzer and\n-                '.'.join(self.objpath) in self.analyzer.overloads and\n-                self.config.autodoc_typehints != 'none'):\n-            # Use signatures for overloaded methods instead of the implementation method.\n-            overloaded = True\n-        else:\n-            overloaded = False\n-            sig = super().format_signature(**kwargs)\n-            sigs.append(sig)\n-\n-        meth = self.parent.__dict__.get(self.objpath[-1])\n-        if inspect.is_singledispatch_method(meth):\n-            # append signature of singledispatch'ed functions\n-            for typ, func in meth.dispatcher.registry.items():\n-                if typ is object:\n-                    pass  # default implementation. skipped.\n-                else:\n-                    dispatchmeth = self.annotate_to_first_argument(func, typ)\n-                    if dispatchmeth:\n-                        documenter = MethodDocumenter(self.directive, '')\n-                        documenter.parent = self.parent\n-                        documenter.object = dispatchmeth\n-                        documenter.objpath = [None]\n-                        sigs.append(documenter.format_signature())\n-        if overloaded:\n-            if inspect.isstaticmethod(self.object, cls=self.parent, name=self.object_name):\n-                actual = inspect.signature(self.object, bound_method=False,\n-                                           type_aliases=self.config.autodoc_type_aliases)\n-            else:\n-                actual = inspect.signature(self.object, bound_method=True,\n-                                           type_aliases=self.config.autodoc_type_aliases)\n-\n-            __globals__ = safe_getattr(self.object, '__globals__', {})\n-            for overload in self.analyzer.overloads.get('.'.join(self.objpath)):\n-                overload = self.merge_default_value(actual, overload)\n-                overload = evaluate_signature(overload, __globals__,\n-                                              self.config.autodoc_type_aliases)\n-\n-                if not inspect.isstaticmethod(self.object, cls=self.parent,\n-                                              name=self.object_name):\n-                    parameters = list(overload.parameters.values())\n-                    overload = overload.replace(parameters=parameters[1:])\n-                sig = stringify_signature(overload, **kwargs)\n-                sigs.append(sig)\n-\n-        return \"\\n\".join(sigs)\n-\n-    def merge_default_value(self, actual: Signature, overload: Signature) -> Signature:\n-        \"\"\"Merge default values of actual implementation to the overload variants.\"\"\"\n-        parameters = list(overload.parameters.values())\n-        for i, param in enumerate(parameters):\n-            actual_param = actual.parameters.get(param.name)\n-            if actual_param and param.default == '...':\n-                parameters[i] = param.replace(default=actual_param.default)\n-\n-        return overload.replace(parameters=parameters)\n-\n-    def annotate_to_first_argument(self, func: Callable, typ: Type) -> Optional[Callable]:\n-        \"\"\"Annotate type hint to the first argument of function if needed.\"\"\"\n-        try:\n-            sig = inspect.signature(func, type_aliases=self.config.autodoc_type_aliases)\n-        except TypeError as exc:\n-            logger.warning(__(\"Failed to get a method signature for %s: %s\"),\n-                           self.fullname, exc)\n-            return None\n-        except ValueError:\n-            return None\n-\n-        if len(sig.parameters) == 1:\n-            return None\n-\n-        def dummy():\n-            pass\n-\n-        params = list(sig.parameters.values())\n-        if params[1].annotation is Parameter.empty:\n-            params[1] = params[1].replace(annotation=typ)\n-            try:\n-                dummy.__signature__ = sig.replace(parameters=params)  # type: ignore\n-                return dummy\n-            except (AttributeError, TypeError):\n-                # failed to update signature (ex. built-in or extension types)\n-                return None\n-        else:\n-            return None\n-\n-    def get_doc(self, ignore: int = None) -> Optional[List[List[str]]]:\n-        if self.objpath[-1] == '__init__':\n-            docstring = getdoc(self.object, self.get_attr,\n-                               self.config.autodoc_inherit_docstrings,\n-                               self.parent, self.object_name)\n-            if (docstring is not None and\n-                (docstring == object.__init__.__doc__ or  # for pypy\n-                 docstring.strip() == object.__init__.__doc__)):  # for !pypy\n-                docstring = None\n-            if docstring:\n-                tab_width = self.directive.state.document.settings.tab_width\n-                return [prepare_docstring(docstring, tabsize=tab_width)]\n-            else:\n-                return []\n-        elif self.objpath[-1] == '__new__':\n-            __new__ = self.get_attr(self.object, '__new__', None)\n-            if __new__:\n-                docstring = getdoc(__new__, self.get_attr,\n-                                   self.config.autodoc_inherit_docstrings,\n-                                   self.parent, self.object_name)\n-                if (docstring is not None and\n-                    (docstring == object.__new__.__doc__ or  # for pypy\n-                     docstring.strip() == object.__new__.__doc__)):  # for !pypy\n-                    docstring = None\n-            if docstring:\n-                tab_width = self.directive.state.document.settings.tab_width\n-                return [prepare_docstring(docstring, tabsize=tab_width)]\n-            else:\n-                return []\n-        else:\n-            return super().get_doc()\n-\n-\n-class NonDataDescriptorMixin(DataDocumenterMixinBase):\n-    \"\"\"\n-    Mixin for AttributeDocumenter to provide the feature for supporting non\n-    data-descriptors.\n-\n-    .. note:: This mix-in must be inherited after other mix-ins.  Otherwise, docstring\n-              and :value: header will be suppressed unexpectedly.\n-    \"\"\"\n-\n-    def import_object(self, raiseerror: bool = False) -> bool:\n-        ret = super().import_object(raiseerror)  # type: ignore\n-        if ret and not inspect.isattributedescriptor(self.object):\n-            self.non_data_descriptor = True\n-        else:\n-            self.non_data_descriptor = False\n-\n-        return ret\n-\n-    def should_suppress_value_header(self) -> bool:\n-        return (not getattr(self, 'non_data_descriptor', False) or\n-                super().should_suppress_directive_header())\n-\n-    def get_doc(self, ignore: int = None) -> Optional[List[List[str]]]:\n-        if getattr(self, 'non_data_descriptor', False):\n-            # the docstring of non datadescriptor is very probably the wrong thing\n-            # to display\n-            return None\n-        else:\n-            return super().get_doc(ignore)  # type: ignore\n-\n-\n-class SlotsMixin(DataDocumenterMixinBase):\n-    \"\"\"\n-    Mixin for AttributeDocumenter to provide the feature for supporting __slots__.\n-    \"\"\"\n-\n-    def isslotsattribute(self) -> bool:\n-        \"\"\"Check the subject is an attribute in __slots__.\"\"\"\n-        try:\n-            __slots__ = inspect.getslots(self.parent)\n-            if __slots__ and self.objpath[-1] in __slots__:\n-                return True\n-            else:\n-                return False\n-        except (ValueError, TypeError):\n-            return False\n-\n-    def import_object(self, raiseerror: bool = False) -> bool:\n-        ret = super().import_object(raiseerror)  # type: ignore\n-        if self.isslotsattribute():\n-            self.object = SLOTSATTR\n-\n-        return ret\n-\n-    def should_suppress_directive_header(self) -> bool:\n-        if self.object is SLOTSATTR:\n-            self._datadescriptor = True\n-            return True\n-        else:\n-            return super().should_suppress_directive_header()\n-\n-    def get_doc(self, ignore: int = None) -> Optional[List[List[str]]]:\n-        if self.object is SLOTSATTR:\n-            try:\n-                __slots__ = inspect.getslots(self.parent)\n-                if __slots__ and __slots__.get(self.objpath[-1]):\n-                    docstring = prepare_docstring(__slots__[self.objpath[-1]])\n-                    return [docstring]\n-                else:\n-                    return []\n-            except ValueError as exc:\n-                logger.warning(__('Invalid __slots__ found on %s. Ignored.'),\n-                               (self.parent.__qualname__, exc), type='autodoc')\n-                return []\n-        else:\n-            return super().get_doc(ignore)  # type: ignore\n-\n-\n-class RuntimeInstanceAttributeMixin(DataDocumenterMixinBase):\n-    \"\"\"\n-    Mixin for AttributeDocumenter to provide the feature for supporting runtime\n-    instance attributes (that are defined in __init__() methods with doc-comments).\n-\n-    Example:\n-\n-        class Foo:\n-            def __init__(self):\n-                self.attr = None  #: This is a target of this mix-in.\n-    \"\"\"\n-\n-    RUNTIME_INSTANCE_ATTRIBUTE = object()\n-\n-    def is_runtime_instance_attribute(self, parent: Any) -> bool:\n-        \"\"\"Check the subject is an attribute defined in __init__().\"\"\"\n-        # An instance variable defined in __init__().\n-        if self.get_attribute_comment(parent, self.objpath[-1]):  # type: ignore\n-            return True\n-        elif self.is_runtime_instance_attribute_not_commented(parent):\n-            return True\n-        else:\n-            return False\n-\n-    def is_runtime_instance_attribute_not_commented(self, parent: Any) -> bool:\n-        \"\"\"Check the subject is an attribute defined in __init__() without comment.\"\"\"\n-        for cls in inspect.getmro(parent):\n-            try:\n-                module = safe_getattr(cls, '__module__')\n-                qualname = safe_getattr(cls, '__qualname__')\n-\n-                analyzer = ModuleAnalyzer.for_module(module)\n-                analyzer.analyze()\n-                if qualname and self.objpath:\n-                    key = '.'.join([qualname, self.objpath[-1]])\n-                    if key in analyzer.tagorder:\n-                        return True\n-            except (AttributeError, PycodeError):\n-                pass\n-\n-        return None\n-\n-    def import_object(self, raiseerror: bool = False) -> bool:\n-        \"\"\"Check the existence of runtime instance attribute when failed to import the\n-        attribute.\"\"\"\n-        try:\n-            return super().import_object(raiseerror=True)  # type: ignore\n-        except ImportError as exc:\n-            try:\n-                with mock(self.config.autodoc_mock_imports):\n-                    ret = import_object(self.modname, self.objpath[:-1], 'class',\n-                                        attrgetter=self.get_attr,  # type: ignore\n-                                        warningiserror=self.config.autodoc_warningiserror)\n-                    parent = ret[3]\n-                    if self.is_runtime_instance_attribute(parent):\n-                        self.object = self.RUNTIME_INSTANCE_ATTRIBUTE\n-                        self.parent = parent\n-                        return True\n-            except ImportError:\n-                pass\n-\n-            if raiseerror:\n-                raise\n-            else:\n-                logger.warning(exc.args[0], type='autodoc', subtype='import_object')\n-                self.env.note_reread()\n-                return False\n-\n-    def should_suppress_value_header(self) -> bool:\n-        return (self.object is self.RUNTIME_INSTANCE_ATTRIBUTE or\n-                super().should_suppress_value_header())\n-\n-    def get_doc(self, ignore: int = None) -> Optional[List[List[str]]]:\n-        if (self.object is self.RUNTIME_INSTANCE_ATTRIBUTE and\n-                self.is_runtime_instance_attribute_not_commented(self.parent)):\n-            return None\n-        else:\n-            return super().get_doc(ignore)  # type: ignore\n-\n-\n-class UninitializedInstanceAttributeMixin(DataDocumenterMixinBase):\n-    \"\"\"\n-    Mixin for AttributeDocumenter to provide the feature for supporting uninitialized\n-    instance attributes (PEP-526 styled, annotation only attributes).\n-\n-    Example:\n-\n-        class Foo:\n-            attr: int  #: This is a target of this mix-in.\n-    \"\"\"\n-\n-    def is_uninitialized_instance_attribute(self, parent: Any) -> bool:\n-        \"\"\"Check the subject is an annotation only attribute.\"\"\"\n-        annotations = get_type_hints(parent, None, self.config.autodoc_type_aliases)\n-        if self.objpath[-1] in annotations:\n-            return True\n-        else:\n-            return False\n-\n-    def import_object(self, raiseerror: bool = False) -> bool:\n-        \"\"\"Check the exisitence of uninitialized instance attribute when failed to import\n-        the attribute.\"\"\"\n-        try:\n-            return super().import_object(raiseerror=True)  # type: ignore\n-        except ImportError as exc:\n-            try:\n-                ret = import_object(self.modname, self.objpath[:-1], 'class',\n-                                    attrgetter=self.get_attr,  # type: ignore\n-                                    warningiserror=self.config.autodoc_warningiserror)\n-                parent = ret[3]\n-                if self.is_uninitialized_instance_attribute(parent):\n-                    self.object = UNINITIALIZED_ATTR\n-                    self.parent = parent\n-                    return True\n-            except ImportError:\n-                pass\n-\n-            if raiseerror:\n-                raise\n-            else:\n-                logger.warning(exc.args[0], type='autodoc', subtype='import_object')\n-                self.env.note_reread()\n-                return False\n-\n-    def should_suppress_value_header(self) -> bool:\n-        return (self.object is UNINITIALIZED_ATTR or\n-                super().should_suppress_value_header())\n-\n-    def get_doc(self, ignore: int = None) -> Optional[List[List[str]]]:\n-        if self.object is UNINITIALIZED_ATTR:\n-            return None\n-        else:\n-            return super().get_doc(ignore)  # type: ignore\n-\n-\n-class AttributeDocumenter(GenericAliasMixin, NewTypeMixin, SlotsMixin,  # type: ignore\n-                          TypeVarMixin, RuntimeInstanceAttributeMixin,\n-                          UninitializedInstanceAttributeMixin, NonDataDescriptorMixin,\n-                          DocstringStripSignatureMixin, ClassLevelDocumenter):\n-    \"\"\"\n-    Specialized Documenter subclass for attributes.\n-    \"\"\"\n-    objtype = 'attribute'\n-    member_order = 60\n-    option_spec: OptionSpec = dict(ModuleLevelDocumenter.option_spec)\n-    option_spec[\"annotation\"] = annotation_option\n-    option_spec[\"no-value\"] = bool_option\n-\n-    # must be higher than the MethodDocumenter, else it will recognize\n-    # some non-data descriptors as methods\n-    priority = 10\n-\n-    @staticmethod\n-    def is_function_or_method(obj: Any) -> bool:\n-        return inspect.isfunction(obj) or inspect.isbuiltin(obj) or inspect.ismethod(obj)\n-\n-    @classmethod\n-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any\n-                            ) -> bool:\n-        if inspect.isattributedescriptor(member):\n-            return True\n-        elif (not isinstance(parent, ModuleDocumenter) and\n-              not inspect.isroutine(member) and\n-              not isinstance(member, type)):\n-            return True\n-        else:\n-            return False\n-\n-    def document_members(self, all_members: bool = False) -> None:\n-        pass\n-\n-    def isinstanceattribute(self) -> bool:\n-        \"\"\"Check the subject is an instance attribute.\"\"\"\n-        warnings.warn('AttributeDocumenter.isinstanceattribute() is deprecated.',\n-                      RemovedInSphinx50Warning)\n-        # uninitialized instance variable (PEP-526)\n-        with mock(self.config.autodoc_mock_imports):\n-            try:\n-                ret = import_object(self.modname, self.objpath[:-1], 'class',\n-                                    attrgetter=self.get_attr,\n-                                    warningiserror=self.config.autodoc_warningiserror)\n-                self.parent = ret[3]\n-                annotations = get_type_hints(self.parent, None,\n-                                             self.config.autodoc_type_aliases)\n-                if self.objpath[-1] in annotations:\n-                    self.object = UNINITIALIZED_ATTR\n-                    return True\n-            except ImportError:\n-                pass\n-\n-        return False\n-\n-    def update_annotations(self, parent: Any) -> None:\n-        \"\"\"Update __annotations__ to support type_comment and so on.\"\"\"\n-        try:\n-            annotations = dict(inspect.getannotations(parent))\n-            parent.__annotations__ = annotations\n-\n-            for cls in inspect.getmro(parent):\n-                try:\n-                    module = safe_getattr(cls, '__module__')\n-                    qualname = safe_getattr(cls, '__qualname__')\n-\n-                    analyzer = ModuleAnalyzer.for_module(module)\n-                    analyzer.analyze()\n-                    for (classname, attrname), annotation in analyzer.annotations.items():\n-                        if classname == qualname and attrname not in annotations:\n-                            annotations[attrname] = annotation\n-                except (AttributeError, PycodeError):\n-                    pass\n-        except (AttributeError, TypeError):\n-            # Failed to set __annotations__ (built-in, extensions, etc.)\n-            pass\n-\n-    def import_object(self, raiseerror: bool = False) -> bool:\n-        ret = super().import_object(raiseerror)\n-        if inspect.isenumattribute(self.object):\n-            self.object = self.object.value\n-        if self.parent:\n-            self.update_annotations(self.parent)\n-\n-        return ret\n-\n-    def get_real_modname(self) -> str:\n-        real_modname = self.get_attr(self.parent or self.object, '__module__', None)\n-        return real_modname or self.modname\n-\n-    def should_suppress_value_header(self) -> bool:\n-        if super().should_suppress_value_header():\n-            return True\n-        else:\n-            doc = self.get_doc()\n-            if doc:\n-                docstring, metadata = separate_metadata('\\n'.join(sum(doc, [])))\n-                if 'hide-value' in metadata:\n-                    return True\n-\n-        return False\n-\n-    def add_directive_header(self, sig: str) -> None:\n-        super().add_directive_header(sig)\n-        sourcename = self.get_sourcename()\n-        if self.options.annotation is SUPPRESS or self.should_suppress_directive_header():\n-            pass\n-        elif self.options.annotation:\n-            self.add_line('   :annotation: %s' % self.options.annotation, sourcename)\n-        else:\n-            if self.config.autodoc_typehints != 'none':\n-                # obtain type annotation for this attribute\n-                annotations = get_type_hints(self.parent, None,\n-                                             self.config.autodoc_type_aliases)\n-                if self.objpath[-1] in annotations:\n-                    objrepr = stringify_typehint(annotations.get(self.objpath[-1]))\n-                    self.add_line('   :type: ' + objrepr, sourcename)\n-\n-            try:\n-                if self.options.no_value or self.should_suppress_value_header():\n-                    pass\n-                else:\n-                    objrepr = object_description(self.object)\n-                    self.add_line('   :value: ' + objrepr, sourcename)\n-            except ValueError:\n-                pass\n-\n-    def get_attribute_comment(self, parent: Any, attrname: str) -> Optional[List[str]]:\n-        for cls in inspect.getmro(parent):\n-            try:\n-                module = safe_getattr(cls, '__module__')\n-                qualname = safe_getattr(cls, '__qualname__')\n-\n-                analyzer = ModuleAnalyzer.for_module(module)\n-                analyzer.analyze()\n-                if qualname and self.objpath:\n-                    key = (qualname, attrname)\n-                    if key in analyzer.attr_docs:\n-                        return list(analyzer.attr_docs[key])\n-            except (AttributeError, PycodeError):\n-                pass\n-\n-        return None\n-\n-    def get_doc(self, ignore: int = None) -> Optional[List[List[str]]]:\n-        # Check the attribute has a docstring-comment\n-        comment = self.get_attribute_comment(self.parent, self.objpath[-1])\n-        if comment:\n-            return [comment]\n-\n-        try:\n-            # Disable `autodoc_inherit_docstring` temporarily to avoid to obtain\n-            # a docstring from the value which descriptor returns unexpectedly.\n-            # ref: https://github.com/sphinx-doc/sphinx/issues/7805\n-            orig = self.config.autodoc_inherit_docstrings\n-            self.config.autodoc_inherit_docstrings = False  # type: ignore\n-            return super().get_doc(ignore)\n-        finally:\n-            self.config.autodoc_inherit_docstrings = orig  # type: ignore\n-\n-    def add_content(self, more_content: Optional[StringList], no_docstring: bool = False\n-                    ) -> None:\n-        # Disable analyzing attribute comment on Documenter.add_content() to control it on\n-        # AttributeDocumenter.add_content()\n-        self.analyzer = None\n-\n-        if more_content is None:\n-            more_content = StringList()\n-        self.update_content(more_content)\n-        super().add_content(more_content, no_docstring)\n-\n-\n-class PropertyDocumenter(DocstringStripSignatureMixin, ClassLevelDocumenter):  # type: ignore\n-    \"\"\"\n-    Specialized Documenter subclass for properties.\n-    \"\"\"\n-    objtype = 'property'\n-    member_order = 60\n-\n-    # before AttributeDocumenter\n-    priority = AttributeDocumenter.priority + 1\n-\n-    @classmethod\n-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any\n-                            ) -> bool:\n-        return inspect.isproperty(member) and isinstance(parent, ClassDocumenter)\n-\n-    def document_members(self, all_members: bool = False) -> None:\n-        pass\n-\n-    def get_real_modname(self) -> str:\n-        real_modname = self.get_attr(self.parent or self.object, '__module__', None)\n-        return real_modname or self.modname\n-\n-    def add_directive_header(self, sig: str) -> None:\n-        super().add_directive_header(sig)\n-        sourcename = self.get_sourcename()\n-        if inspect.isabstractmethod(self.object):\n-            self.add_line('   :abstractmethod:', sourcename)\n-\n-        if safe_getattr(self.object, 'fget', None) and self.config.autodoc_typehints != 'none':\n-            try:\n-                signature = inspect.signature(self.object.fget,\n-                                              type_aliases=self.config.autodoc_type_aliases)\n-                if signature.return_annotation is not Parameter.empty:\n-                    objrepr = stringify_typehint(signature.return_annotation)\n-                    self.add_line('   :type: ' + objrepr, sourcename)\n-            except TypeError as exc:\n-                logger.warning(__(\"Failed to get a function signature for %s: %s\"),\n-                               self.fullname, exc)\n-                return None\n-            except ValueError:\n-                return None\n-\n-\n-class NewTypeAttributeDocumenter(AttributeDocumenter):\n-    \"\"\"\n-    Specialized Documenter subclass for NewTypes.\n-\n-    Note: This must be invoked before MethodDocumenter because NewType is a kind of\n-    function object.\n-    \"\"\"\n-\n-    objtype = 'newvarattribute'\n-    directivetype = 'attribute'\n-    priority = MethodDocumenter.priority + 1\n-\n-    @classmethod\n-    def can_document_member(cls, member: Any, membername: str, isattr: bool, parent: Any\n-                            ) -> bool:\n-        return not isinstance(parent, ModuleDocumenter) and inspect.isNewType(member)\n-\n-\n-def get_documenters(app: Sphinx) -> Dict[str, Type[Documenter]]:\n-    \"\"\"Returns registered Documenter classes\"\"\"\n-    warnings.warn(\"get_documenters() is deprecated.\", RemovedInSphinx50Warning, stacklevel=2)\n-    return app.registry.documenters\n-\n-\n-def autodoc_attrgetter(app: Sphinx, obj: Any, name: str, *defargs: Any) -> Any:\n-    \"\"\"Alternative getattr() for types\"\"\"\n-    for typ, func in app.registry.autodoc_attrgettrs.items():\n-        if isinstance(obj, typ):\n-            return func(obj, name, *defargs)\n-\n-    return safe_getattr(obj, name, *defargs)\n-\n-\n-def migrate_autodoc_member_order(app: Sphinx, config: Config) -> None:\n-    if config.autodoc_member_order == 'alphabetic':\n-        # RemovedInSphinx50Warning\n-        logger.warning(__('autodoc_member_order now accepts \"alphabetical\" '\n-                          'instead of \"alphabetic\". Please update your setting.'))\n-        config.autodoc_member_order = 'alphabetical'  # type: ignore\n-\n-\n-# for compatibility\n-from sphinx.ext.autodoc.deprecated import DataDeclarationDocumenter  # NOQA\n-from sphinx.ext.autodoc.deprecated import GenericAliasDocumenter  # NOQA\n-from sphinx.ext.autodoc.deprecated import InstanceAttributeDocumenter  # NOQA\n-from sphinx.ext.autodoc.deprecated import SingledispatchFunctionDocumenter  # NOQA\n-from sphinx.ext.autodoc.deprecated import SingledispatchMethodDocumenter  # NOQA\n-from sphinx.ext.autodoc.deprecated import SlotsAttributeDocumenter  # NOQA\n-from sphinx.ext.autodoc.deprecated import TypeVarDocumenter  # NOQA\n-\n-\n-def setup(app: Sphinx) -> Dict[str, Any]:\n-    app.add_autodocumenter(ModuleDocumenter)\n-    app.add_autodocumenter(ClassDocumenter)\n-    app.add_autodocumenter(ExceptionDocumenter)\n-    app.add_autodocumenter(DataDocumenter)\n-    app.add_autodocumenter(NewTypeDataDocumenter)\n-    app.add_autodocumenter(FunctionDocumenter)\n-    app.add_autodocumenter(DecoratorDocumenter)\n-    app.add_autodocumenter(MethodDocumenter)\n-    app.add_autodocumenter(AttributeDocumenter)\n-    app.add_autodocumenter(PropertyDocumenter)\n-    app.add_autodocumenter(NewTypeAttributeDocumenter)\n-\n-    app.add_config_value('autoclass_content', 'class', True, ENUM('both', 'class', 'init'))\n-    app.add_config_value('autodoc_member_order', 'alphabetical', True,\n-                         ENUM('alphabetic', 'alphabetical', 'bysource', 'groupwise'))\n-    app.add_config_value('autodoc_class_signature', 'mixed', True, ENUM('mixed', 'separated'))\n-    app.add_config_value('autodoc_default_options', {}, True)\n-    app.add_config_value('autodoc_docstring_signature', True, True)\n-    app.add_config_value('autodoc_mock_imports', [], True)\n-    app.add_config_value('autodoc_typehints', \"signature\", True,\n-                         ENUM(\"signature\", \"description\", \"none\", \"both\"))\n-    app.add_config_value('autodoc_typehints_description_target', 'all', True,\n-                         ENUM('all', 'documented'))\n-    app.add_config_value('autodoc_type_aliases', {}, True)\n-    app.add_config_value('autodoc_warningiserror', True, True)\n-    app.add_config_value('autodoc_inherit_docstrings', True, True)\n-    app.add_event('autodoc-before-process-signature')\n-    app.add_event('autodoc-process-docstring')\n-    app.add_event('autodoc-process-signature')\n-    app.add_event('autodoc-skip-member')\n-    app.add_event('autodoc-process-bases')\n-\n-    app.connect('config-inited', migrate_autodoc_member_order, priority=800)\n-\n-    app.setup_extension('sphinx.ext.autodoc.preserve_defaults')\n-    app.setup_extension('sphinx.ext.autodoc.type_comment')\n-    app.setup_extension('sphinx.ext.autodoc.typehints')\n-\n-    return {'version': sphinx.__display_version__, 'parallel_read_safe': True}\n+    def get_object_members(self, want_all: bool) -> Tuple[bool, List[Tuple[str, Any]]]:\n+        \"\"\"Get all members of the object.\"\"\"\n+        return False, []\n\\ No newline at end of file\ndiff --git a/sphinx/util/inspect.py b/sphinx/util/inspect.py\nindex 23dd9e930..72b7621a1 100644\n--- a/sphinx/util/inspect.py\n+++ b/sphinx/util/inspect.py\n@@ -1,6 +1,6 @@\n \"\"\"\n     sphinx.util.inspect\n-    ~~~~~~~~~~~~~~~~~~~\n+    ~~~~~~~~~~~~~~~~~~\n \n     Helpers for inspecting Python modules.\n \n@@ -15,404 +15,152 @@ import inspect\n import re\n import sys\n import types\n-import typing\n-import warnings\n from functools import partial, partialmethod\n-from importlib import import_module\n-from inspect import Parameter, isclass, ismethod, ismethoddescriptor, ismodule  # NOQA\n-from io import StringIO\n-from types import ModuleType\n-from typing import Any, Callable, Dict, Mapping, Optional, Sequence, Tuple, Type, cast\n-\n-from sphinx.deprecation import RemovedInSphinx50Warning\n-from sphinx.pycode.ast import ast  # for py36-37\n-from sphinx.pycode.ast import unparse as ast_unparse\n+from types import MethodType, FunctionType, MethodWrapperType, WrapperDescriptorType\n+from typing import Any, Callable, Dict, Generator, Iterator, List, Mapping, Optional, Sequence, Set, Tuple\n+from typing import Type, TypeVar, Union, cast\n+\n from sphinx.util import logging\n-from sphinx.util.typing import ForwardRef\n-from sphinx.util.typing import stringify as stringify_annotation\n+from sphinx.util.typing import ExtendedCallable\n+from sphinx.util.classproperty import classproperty\n \n if sys.version_info > (3, 7):\n-    from types import ClassMethodDescriptorType, MethodDescriptorType, WrapperDescriptorType\n+    from types import ClassMethodDescriptorType, MethodDescriptorType\n else:\n     ClassMethodDescriptorType = type(object.__init__)\n     MethodDescriptorType = type(str.join)\n-    WrapperDescriptorType = type(dict.__dict__['fromkeys'])\n \n-if False:\n-    # For type annotation\n-    from typing import Type  # NOQA\n \n logger = logging.getLogger(__name__)\n \n-memory_address_re = re.compile(r' at 0x[0-9a-f]{8,16}(?=>)', re.IGNORECASE)\n-\n-\n-# Copied from the definition of inspect.getfullargspec from Python master,\n-# and modified to remove the use of special flags that break decorated\n-# callables and bound methods in the name of backwards compatibility. Used\n-# under the terms of PSF license v2, which requires the above statement\n-# and the following:\n-#\n-#   Copyright (c) 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009,\n-#   2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017 Python Software\n-#   Foundation; All Rights Reserved\n-def getargspec(func: Callable) -> Any:\n-    \"\"\"Like inspect.getfullargspec but supports bound methods, and wrapped\n-    methods.\"\"\"\n-    warnings.warn('sphinx.ext.inspect.getargspec() is deprecated',\n-                  RemovedInSphinx50Warning, stacklevel=2)\n-\n-    sig = inspect.signature(func)\n-\n-    args = []\n-    varargs = None\n-    varkw = None\n-    kwonlyargs = []\n-    defaults = ()\n-    annotations = {}\n-    defaults = ()\n-    kwdefaults = {}\n-\n-    if sig.return_annotation is not sig.empty:\n-        annotations['return'] = sig.return_annotation\n-\n-    for param in sig.parameters.values():\n-        kind = param.kind\n-        name = param.name\n-\n-        if kind is Parameter.POSITIONAL_ONLY:\n-            args.append(name)\n-        elif kind is Parameter.POSITIONAL_OR_KEYWORD:\n-            args.append(name)\n-            if param.default is not param.empty:\n-                defaults += (param.default,)  # type: ignore\n-        elif kind is Parameter.VAR_POSITIONAL:\n-            varargs = name\n-        elif kind is Parameter.KEYWORD_ONLY:\n-            kwonlyargs.append(name)\n-            if param.default is not param.empty:\n-                kwdefaults[name] = param.default\n-        elif kind is Parameter.VAR_KEYWORD:\n-            varkw = name\n-\n-        if param.annotation is not param.empty:\n-            annotations[name] = param.annotation\n-\n-    if not kwdefaults:\n-        # compatibility with 'func.__kwdefaults__'\n-        kwdefaults = None\n-\n-    if not defaults:\n-        # compatibility with 'func.__defaults__'\n-        defaults = None\n-\n-    return inspect.FullArgSpec(args, varargs, varkw, defaults,\n-                               kwonlyargs, kwdefaults, annotations)\n-\n-\n-def unwrap(obj: Any) -> Any:\n-    \"\"\"Get an original object from wrapped object (wrapped functions).\"\"\"\n-    try:\n-        if hasattr(obj, '__sphinx_mock__'):\n-            # Skip unwrapping mock object to avoid RecursionError\n-            return obj\n-        else:\n-            return inspect.unwrap(obj)\n-    except ValueError:\n-        # might be a mock object\n-        return obj\n-\n-\n-def unwrap_all(obj: Any, *, stop: Callable = None) -> Any:\n-    \"\"\"\n-    Get an original object from wrapped object (unwrapping partials, wrapped\n-    functions, and other decorators).\n-    \"\"\"\n-    while True:\n-        if stop and stop(obj):\n-            return obj\n-        elif ispartial(obj):\n-            obj = obj.func\n-        elif inspect.isroutine(obj) and hasattr(obj, '__wrapped__'):\n-            obj = obj.__wrapped__\n-        elif isclassmethod(obj):\n-            obj = obj.__func__\n-        elif isstaticmethod(obj):\n-            obj = obj.__func__\n-        else:\n-            return obj\n-\n-\n-def getall(obj: Any) -> Optional[Sequence[str]]:\n-    \"\"\"Get __all__ attribute of the module as dict.\n-\n-    Return None if given *obj* does not have __all__.\n-    Raises ValueError if given *obj* have invalid __all__.\n-    \"\"\"\n-    __all__ = safe_getattr(obj, '__all__', None)\n-    if __all__ is None:\n-        return None\n-    else:\n-        if (isinstance(__all__, (list, tuple)) and all(isinstance(e, str) for e in __all__)):\n-            return __all__\n-        else:\n-            raise ValueError(__all__)\n-\n-\n-def getannotations(obj: Any) -> Mapping[str, Any]:\n-    \"\"\"Get __annotations__ from given *obj* safely.\"\"\"\n-    __annotations__ = safe_getattr(obj, '__annotations__', None)\n-    if isinstance(__annotations__, Mapping):\n-        return __annotations__\n-    else:\n-        return {}\n-\n \n-def getglobals(obj: Any) -> Mapping[str, Any]:\n-    \"\"\"Get __globals__ from given *obj* safely.\"\"\"\n-    __globals__ = safe_getattr(obj, '__globals__', None)\n-    if isinstance(__globals__, Mapping):\n-        return __globals__\n-    else:\n-        return {}\n+def _unwrap_function(obj: Any) -> Any:\n+    \"\"\"Helper to unwrap functions and find docstring.\"\"\"\n+    unwrapped = obj\n+    \n+    # Follow wrapper chain\n+    while hasattr(unwrapped, '__wrapped__'):\n+        # Found docstring, use this function\n+        if unwrapped.__doc__:\n+            return unwrapped\n+        unwrapped = unwrapped.__wrapped__\n \n+    return obj\n \n-def getmro(obj: Any) -> Tuple[Type, ...]:\n-    \"\"\"Get __mro__ from given *obj* safely.\"\"\"\n-    __mro__ = safe_getattr(obj, '__mro__', None)\n-    if isinstance(__mro__, tuple):\n-        return __mro__\n-    else:\n-        return tuple()\n \n+def unwrap_classproperty(obj: Any) -> Any:\n+    \"\"\"Get original function with docstring from a descriptor.\n \n-def getslots(obj: Any) -> Optional[Dict]:\n-    \"\"\"Get __slots__ attribute of the class as dict.\n+    This handles various combinations of decorators and descriptors:\n+    - @property\n+    - @classmethod\n+    - @classmethod @property\n+    - @property @classmethod\n+    - @classproperty (custom descriptor)\n \n-    Return None if gienv *obj* does not have __slots__.\n-    Raises TypeError if given *obj* is not a class.\n-    Raises ValueError if given *obj* have invalid __slots__.\n+    :param obj: The object to unwrap\n+    :returns: The original function with docstring, or the input object\n     \"\"\"\n-    if not inspect.isclass(obj):\n-        raise TypeError\n-\n-    __slots__ = safe_getattr(obj, '__slots__', None)\n-    if __slots__ is None:\n-        return None\n-    elif isinstance(__slots__, dict):\n-        return __slots__\n-    elif isinstance(__slots__, str):\n-        return {__slots__: None}\n-    elif isinstance(__slots__, (list, tuple)):\n-        return {e: None for e in __slots__}\n-    else:\n-        raise ValueError\n-\n-\n-def isNewType(obj: Any) -> bool:\n-    \"\"\"Check the if object is a kind of NewType.\"\"\"\n-    __module__ = safe_getattr(obj, '__module__', None)\n-    __qualname__ = safe_getattr(obj, '__qualname__', None)\n-    if __module__ == 'typing' and __qualname__ == 'NewType.<locals>.new_type':\n-        return True\n-    else:\n-        return False\n-\n-\n-def isenumclass(x: Any) -> bool:\n-    \"\"\"Check if the object is subclass of enum.\"\"\"\n-    return inspect.isclass(x) and issubclass(x, enum.Enum)\n-\n-\n-def isenumattribute(x: Any) -> bool:\n-    \"\"\"Check if the object is attribute of enum.\"\"\"\n-    return isinstance(x, enum.Enum)\n-\n+    # Handle our custom class property descriptor first\n+    if isinstance(obj, classproperty):\n+        if obj.fget:\n+            # Try the stored function first\n+            if obj.fget.__doc__:\n+                return obj.fget\n+            # Try unwrapping the function\n+            return _unwrap_function(obj.fget)\n+\n+    # Handle classmethods\n+    if isinstance(obj, (classmethod, ClassMethodDescriptorType)):\n+        if hasattr(obj, '__func__'):\n+            # Get the underlying function\n+            fn = obj.__func__\n+            # Handle the @classmethod @property case\n+            if isinstance(fn, property):\n+                getter = fn.fget\n+                if getter:\n+                    if getter.__doc__:\n+                        return getter\n+                    return _unwrap_function(getter)\n+            # Regular classmethod - use its function\n+            if fn.__doc__:\n+                return fn\n+            return _unwrap_function(fn)\n+\n+    # Handle property descriptors\n+    if isinstance(obj, property):\n+        fn = obj.fget\n+        if fn:\n+            # Handle @property @classmethod case\n+            if isinstance(fn, classmethod):\n+                # Get the underlying classmethod function\n+                cm_fn = fn.__func__\n+                if cm_fn.__doc__:\n+                    return cm_fn\n+                return _unwrap_function(cm_fn)\n+            # Regular property - use its getter\n+            if fn.__doc__:\n+                return fn\n+            return _unwrap_function(fn)\n+\n+    # For regular functions\n+    if inspect.isfunction(obj):\n+        if obj.__doc__:\n+            return obj\n+        return _unwrap_function(obj)\n \n-def unpartial(obj: Any) -> Any:\n-    \"\"\"Get an original object from partial object.\n-\n-    This returns given object itself if not partial.\n-    \"\"\"\n-    while ispartial(obj):\n-        obj = obj.func\n+    # For methods and bound methods\n+    if inspect.ismethod(obj):\n+        fn = obj.__func__ if hasattr(obj, '__func__') else obj\n+        if fn.__doc__:\n+            return fn\n+        return _unwrap_function(fn)\n \n+    # For computed values (integers, strings, etc) or unknown types\n     return obj\n \n \n-def ispartial(obj: Any) -> bool:\n-    \"\"\"Check if the object is partial.\"\"\"\n-    return isinstance(obj, (partial, partialmethod))\n-\n-\n-def isclassmethod(obj: Any) -> bool:\n-    \"\"\"Check if the object is classmethod.\"\"\"\n-    if isinstance(obj, classmethod):\n-        return True\n-    elif inspect.ismethod(obj) and obj.__self__ is not None and isclass(obj.__self__):\n-        return True\n-\n-    return False\n-\n-\n-def isstaticmethod(obj: Any, cls: Any = None, name: str = None) -> bool:\n-    \"\"\"Check if the object is staticmethod.\"\"\"\n-    if isinstance(obj, staticmethod):\n-        return True\n-    elif cls and name:\n-        # trace __mro__ if the method is defined in parent class\n-        #\n-        # .. note:: This only works well with new style classes.\n-        for basecls in getattr(cls, '__mro__', [cls]):\n-            meth = basecls.__dict__.get(name)\n-            if meth:\n-                if isinstance(meth, staticmethod):\n-                    return True\n-                else:\n-                    return False\n-\n-    return False\n-\n-\n-def isdescriptor(x: Any) -> bool:\n-    \"\"\"Check if the object is some kind of descriptor.\"\"\"\n-    for item in '__get__', '__set__', '__delete__':\n-        if hasattr(safe_getattr(x, item, None), '__call__'):\n-            return True\n-    return False\n-\n+def getannotations(obj: Any) -> Mapping[str, Any]:\n+    \"\"\"Get annotations from an object.\"\"\"\n+    if isinstance(obj, type):\n+        # For classes, return the __annotations__ dictionary\n+        return getattr(obj, '__annotations__', {})\n+    elif inspect.isfunction(obj) or inspect.ismethod(obj):\n+        # For functions and methods, return the __annotations__ dictionary\n+        return getattr(obj, '__annotations__', {})\n+    return {}\n \n-def isabstractmethod(obj: Any) -> bool:\n-    \"\"\"Check if the object is an abstractmethod.\"\"\"\n-    return safe_getattr(obj, '__isabstractmethod__', False) is True\n \n+def getslots(obj: Any) -> Optional[Dict[str, Any]]:\n+    \"\"\"Get __slots__ attribute of the given object.\"\"\"\n+    if isinstance(obj, type):\n+        return getattr(obj, '__slots__', None)\n+    return None\n \n-def is_cython_function_or_method(obj: Any) -> bool:\n-    \"\"\"Check if the object is a function or method in cython.\"\"\"\n-    try:\n-        return obj.__class__.__name__ == 'cython_function_or_method'\n-    except AttributeError:\n-        return False\n-\n-\n-def isattributedescriptor(obj: Any) -> bool:\n-    \"\"\"Check if the object is an attribute like descriptor.\"\"\"\n-    if inspect.isdatadescriptor(obj):\n-        # data descriptor is kind of attribute\n-        return True\n-    elif isdescriptor(obj):\n-        # non data descriptor\n-        unwrapped = unwrap(obj)\n-        if isfunction(unwrapped) or isbuiltin(unwrapped) or inspect.ismethod(unwrapped):\n-            # attribute must not be either function, builtin and method\n-            return False\n-        elif is_cython_function_or_method(unwrapped):\n-            # attribute must not be either function and method (for cython)\n-            return False\n-        elif inspect.isclass(unwrapped):\n-            # attribute must not be a class\n-            return False\n-        elif isinstance(unwrapped, (ClassMethodDescriptorType,\n-                                    MethodDescriptorType,\n-                                    WrapperDescriptorType)):\n-            # attribute must not be a method descriptor\n-            return False\n-        elif type(unwrapped).__name__ == \"instancemethod\":\n-            # attribute must not be an instancemethod (C-API)\n-            return False\n-        else:\n-            return True\n-    else:\n-        return False\n \n-\n-def is_singledispatch_function(obj: Any) -> bool:\n-    \"\"\"Check if the object is singledispatch function.\"\"\"\n-    if (inspect.isfunction(obj) and\n-            hasattr(obj, 'dispatch') and\n-            hasattr(obj, 'register') and\n-            obj.dispatch.__module__ == 'functools'):\n-        return True\n+def getmro(obj: Any) -> Tuple[Type, ...]:\n+    \"\"\"Get method resolution order of the given object.\"\"\"\n+    if isinstance(obj, type):\n+        return obj.__mro__\n     else:\n-        return False\n-\n-\n-def is_singledispatch_method(obj: Any) -> bool:\n-    \"\"\"Check if the object is singledispatch method.\"\"\"\n-    try:\n-        from functools import singledispatchmethod  # type: ignore\n-        return isinstance(obj, singledispatchmethod)\n-    except ImportError:  # py36-37\n-        return False\n-\n-\n-def isfunction(obj: Any) -> bool:\n-    \"\"\"Check if the object is function.\"\"\"\n-    return inspect.isfunction(unwrap_all(obj))\n-\n-\n-def isbuiltin(obj: Any) -> bool:\n-    \"\"\"Check if the object is builtin.\"\"\"\n-    return inspect.isbuiltin(unwrap_all(obj))\n+        return type(obj).__mro__\n \n \n-def isroutine(obj: Any) -> bool:\n-    \"\"\"Check is any kind of function or method.\"\"\"\n-    return inspect.isroutine(unwrap_all(obj))\n+def isclass(obj: Any) -> bool:\n+    \"\"\"Check if the object is a class.\"\"\"\n+    return isinstance(obj, type)\n \n \n-def iscoroutinefunction(obj: Any) -> bool:\n-    \"\"\"Check if the object is coroutine-function.\"\"\"\n-    def iswrappedcoroutine(obj: Any) -> bool:\n-        \"\"\"Check if the object is wrapped coroutine-function.\"\"\"\n-        if isstaticmethod(obj) or isclassmethod(obj) or ispartial(obj):\n-            # staticmethod, classmethod and partial method are not a wrapped coroutine-function\n-            # Note: Since 3.10, staticmethod and classmethod becomes a kind of wrappers\n-            return False\n-        elif hasattr(obj, '__wrapped__'):\n-            return True\n-        else:\n-            return False\n-\n-    obj = unwrap_all(obj, stop=iswrappedcoroutine)\n-    if hasattr(obj, '__code__') and inspect.iscoroutinefunction(obj):\n-        # check obj.__code__ because iscoroutinefunction() crashes for custom method-like\n-        # objects (see https://github.com/sphinx-doc/sphinx/issues/6605)\n-        return True\n-    else:\n-        return False\n-\n-\n-def isproperty(obj: Any) -> bool:\n-    \"\"\"Check if the object is property.\"\"\"\n-    if sys.version_info >= (3, 8):\n-        from functools import cached_property  # cached_property is available since py3.8\n-        if isinstance(obj, cached_property):\n-            return True\n-\n-    return isinstance(obj, property)\n-\n-\n-def isgenericalias(obj: Any) -> bool:\n-    \"\"\"Check if the object is GenericAlias.\"\"\"\n-    if (hasattr(typing, '_GenericAlias') and  # only for py37+\n-            isinstance(obj, typing._GenericAlias)):  # type: ignore\n-        return True\n-    elif (hasattr(types, 'GenericAlias') and  # only for py39+\n-          isinstance(obj, types.GenericAlias)):  # type: ignore\n-        return True\n-    elif (hasattr(typing, '_SpecialGenericAlias') and  # for py39+\n-            isinstance(obj, typing._SpecialGenericAlias)):  # type: ignore\n-        return True\n-    else:\n-        return False\n+def isenumclass(obj: Any) -> bool:\n+    \"\"\"Check if the object is an enum class.\"\"\"\n+    return isinstance(obj, type) and issubclass(obj, enum.Enum)\n \n \n def safe_getattr(obj: Any, name: str, *defargs: Any) -> Any:\n     \"\"\"A getattr() that turns all exceptions into AttributeErrors.\"\"\"\n     try:\n         return getattr(obj, name, *defargs)\n-    except Exception as exc:\n+    except Exception:\n         # sometimes accessing a property raises an exception (e.g.\n         # NotImplementedError), so let's try to read the attribute directly\n         try:\n@@ -427,439 +175,140 @@ def safe_getattr(obj: Any, name: str, *defargs: Any) -> Any:\n         if defargs:\n             return defargs[0]\n \n-        raise AttributeError(name) from exc\n-\n-\n-def object_description(object: Any) -> str:\n-    \"\"\"A repr() implementation that returns text safe to use in reST context.\"\"\"\n-    if isinstance(object, dict):\n-        try:\n-            sorted_keys = sorted(object)\n-        except Exception:\n-            pass  # Cannot sort dict keys, fall back to generic repr\n-        else:\n-            items = (\"%s: %s\" %\n-                     (object_description(key), object_description(object[key]))\n-                     for key in sorted_keys)\n-            return \"{%s}\" % \", \".join(items)\n-    elif isinstance(object, set):\n-        try:\n-            sorted_values = sorted(object)\n-        except TypeError:\n-            pass  # Cannot sort set values, fall back to generic repr\n-        else:\n-            return \"{%s}\" % \", \".join(object_description(x) for x in sorted_values)\n-    elif isinstance(object, frozenset):\n-        try:\n-            sorted_values = sorted(object)\n-        except TypeError:\n-            pass  # Cannot sort frozenset values, fall back to generic repr\n-        else:\n-            return \"frozenset({%s})\" % \", \".join(object_description(x)\n-                                                 for x in sorted_values)\n-    elif isinstance(object, enum.Enum):\n-        return \"%s.%s\" % (object.__class__.__name__, object.name)\n+        raise AttributeError(name)\n \n-    try:\n-        s = repr(object)\n-    except Exception as exc:\n-        raise ValueError from exc\n-    # Strip non-deterministic memory addresses such as\n-    # ``<__main__.A at 0x7f68cb685710>``\n-    s = memory_address_re.sub('', s)\n-    return s.replace('\\n', ' ')\n-\n-\n-def is_builtin_class_method(obj: Any, attr_name: str) -> bool:\n-    \"\"\"If attr_name is implemented at builtin class, return True.\n-\n-        >>> is_builtin_class_method(int, '__init__')\n-        True\n-\n-    Why this function needed? CPython implements int.__init__ by Descriptor\n-    but PyPy implements it by pure Python code.\n-    \"\"\"\n-    try:\n-        mro = getmro(obj)\n-        cls = next(c for c in mro if attr_name in safe_getattr(c, '__dict__', {}))\n-    except StopIteration:\n-        return False\n-\n-    try:\n-        name = safe_getattr(cls, '__name__')\n-    except AttributeError:\n-        return False\n-\n-    return getattr(builtins, name, None) is cls\n-\n-\n-class DefaultValue:\n-    \"\"\"A simple wrapper for default value of the parameters of overload functions.\"\"\"\n-\n-    def __init__(self, value: str) -> None:\n-        self.value = value\n-\n-    def __eq__(self, other: object) -> bool:\n-        return self.value == other\n-\n-    def __repr__(self) -> str:\n-        return self.value\n-\n-\n-class TypeAliasForwardRef:\n-    \"\"\"Pseudo typing class for autodoc_type_aliases.\n-\n-    This avoids the error on evaluating the type inside `get_type_hints()`.\n-    \"\"\"\n-    def __init__(self, name: str) -> None:\n-        self.name = name\n-\n-    def __call__(self) -> None:\n-        # Dummy method to imitate special typing classes\n-        pass\n-\n-    def __eq__(self, other: Any) -> bool:\n-        return self.name == other\n-\n-\n-class TypeAliasModule:\n-    \"\"\"Pseudo module class for autodoc_type_aliases.\"\"\"\n-\n-    def __init__(self, modname: str, mapping: Dict[str, str]) -> None:\n-        self.__modname = modname\n-        self.__mapping = mapping\n-\n-        self.__module: Optional[ModuleType] = None\n-\n-    def __getattr__(self, name: str) -> Any:\n-        fullname = '.'.join(filter(None, [self.__modname, name]))\n-        if fullname in self.__mapping:\n-            # exactly matched\n-            return TypeAliasForwardRef(self.__mapping[fullname])\n-        else:\n-            prefix = fullname + '.'\n-            nested = {k: v for k, v in self.__mapping.items() if k.startswith(prefix)}\n-            if nested:\n-                # sub modules or classes found\n-                return TypeAliasModule(fullname, nested)\n-            else:\n-                # no sub modules or classes found.\n-                try:\n-                    # return the real submodule if exists\n-                    return import_module(fullname)\n-                except ImportError:\n-                    # return the real class\n-                    if self.__module is None:\n-                        self.__module = import_module(self.__modname)\n-\n-                    return getattr(self.__module, name)\n \n+def getdoc(obj: Any, attrgetter: Callable = None, allow_inherited: bool = False,\n+           cls: Any = None, name: str = None) -> Optional[str]:\n+    \"\"\"Get the docstring for the object.\n \n-class TypeAliasNamespace(Dict[str, Any]):\n-    \"\"\"Pseudo namespace class for autodoc_type_aliases.\n+    This tries to obtain the docstring for some kind of objects additionally:\n \n-    This enables to look up nested modules and classes like `mod1.mod2.Class`.\n+    * partial objects\n+    * inherited docstring\n+    * class properties\n     \"\"\"\n-\n-    def __init__(self, mapping: Dict[str, str]) -> None:\n-        self.__mapping = mapping\n-\n-    def __getitem__(self, key: str) -> Any:\n-        if key in self.__mapping:\n-            # exactly matched\n-            return TypeAliasForwardRef(self.__mapping[key])\n-        else:\n-            prefix = key + '.'\n-            nested = {k: v for k, v in self.__mapping.items() if k.startswith(prefix)}\n-            if nested:\n-                # sub modules or classes found\n-                return TypeAliasModule(key, nested)\n+    def getdoc_internal(obj: Any, attrgetter: Callable = None, docstring=None) -> Optional[str]:\n+        # Handle computed values from properties\n+        if isinstance(obj, (int, float, str, bool, list, tuple, dict, set)) and cls and name:\n+            # Try to get descriptor from class\n+            desc = cls.__dict__.get(name)\n+            if desc:\n+                # Unwrap descriptor to find docstring\n+                unwrapped = unwrap_classproperty(desc)\n+                if unwrapped is not None and unwrapped.__doc__:\n+                    return unwrapped.__doc__\n+\n+        # Handle descriptors and functions\n+        unwrapped = unwrap_classproperty(obj)\n+        if unwrapped is not obj and unwrapped.__doc__:\n+            return unwrapped.__doc__\n+\n+        # Handle partial function objects\n+        if isinstance(obj, (partial, partialmethod)):\n+            doc = getdoc_internal(obj.func, attrgetter)\n+            if doc:\n+                return doc\n+\n+        # Try to get doc from class or parent classes\n+        if allow_inherited and cls and name and docstring is None:\n+            name_str = name.replace(str(getattr(obj, '__name__', '')), '', 1)\n+            for basecls in getattr(cls, '__mro__', []):\n+                for meth in basecls.__dict__.values():\n+                    if isinstance(meth, (classmethod, staticmethod)):\n+                        meth = meth.__get__(None, basecls)\n+                    if name_str and getattr(meth, '__name__', None) == name_str:\n+                        doc = getdoc_internal(meth, attrgetter)\n+                        if doc:\n+                            return doc\n+\n+        # Try getting docstring directly\n+        try:\n+            if attrgetter:\n+                docstring = attrgetter(obj, '__doc__')\n             else:\n-                raise KeyError\n+                docstring = inspect.getdoc(obj)\n+        except Exception:\n+            docstring = None\n \n+        return docstring\n \n-def _should_unwrap(subject: Callable) -> bool:\n-    \"\"\"Check the function should be unwrapped on getting signature.\"\"\"\n-    __globals__ = getglobals(subject)\n-    if (__globals__.get('__name__') == 'contextlib' and\n-            __globals__.get('__file__') == contextlib.__file__):\n-        # contextmanger should be unwrapped\n-        return True\n+    # Get docstring and clean it up\n+    docstring = getdoc_internal(obj, attrgetter)\n+    if docstring:\n+        import textwrap\n+        docstring = textwrap.dedent(docstring).strip()\n \n-    return False\n+    return docstring\n \n \n-def signature(subject: Callable, bound_method: bool = False, follow_wrapped: bool = None,\n-              type_aliases: Dict = {}) -> inspect.Signature:\n-    \"\"\"Return a Signature object for the given *subject*.\n+def evaluate_signature(sig: inspect.Signature) -> inspect.Signature:\n+    \"\"\"Evaluate stringized type annotations.\n \n-    :param bound_method: Specify *subject* is a bound method or not\n-    :param follow_wrapped: Same as ``inspect.signature()``.\n+    This converts stringized type annotations to corresponding types as much as\n+    possible.  If some type names are not available, they are left as strings.\n     \"\"\"\n+    from sphinx.util.typing import stringify\n+    import types\n \n-    if follow_wrapped is None:\n-        follow_wrapped = True\n-    else:\n-        warnings.warn('The follow_wrapped argument of sphinx.util.inspect.signature() is '\n-                      'deprecated', RemovedInSphinx50Warning, stacklevel=2)\n-\n-    try:\n-        try:\n-            if _should_unwrap(subject):\n-                signature = inspect.signature(subject)\n-            else:\n-                signature = inspect.signature(subject, follow_wrapped=follow_wrapped)\n-        except ValueError:\n-            # follow built-in wrappers up (ex. functools.lru_cache)\n-            signature = inspect.signature(subject)\n-        parameters = list(signature.parameters.values())\n-        return_annotation = signature.return_annotation\n-    except IndexError:\n-        # Until python 3.6.4, cpython has been crashed on inspection for\n-        # partialmethods not having any arguments.\n-        # https://bugs.python.org/issue33009\n-        if hasattr(subject, '_partialmethod'):\n-            parameters = []\n-            return_annotation = Parameter.empty\n-        else:\n-            raise\n-\n-    try:\n-        # Resolve annotations using ``get_type_hints()`` and type_aliases.\n-        localns = TypeAliasNamespace(type_aliases)\n-        annotations = typing.get_type_hints(subject, None, localns)\n-        for i, param in enumerate(parameters):\n-            if param.name in annotations:\n-                annotation = annotations[param.name]\n-                if isinstance(annotation, TypeAliasForwardRef):\n-                    annotation = annotation.name\n-                parameters[i] = param.replace(annotation=annotation)\n-        if 'return' in annotations:\n-            if isinstance(annotations['return'], TypeAliasForwardRef):\n-                return_annotation = annotations['return'].name\n-            else:\n-                return_annotation = annotations['return']\n-    except Exception:\n-        # ``get_type_hints()`` does not support some kind of objects like partial,\n-        # ForwardRef and so on.\n-        pass\n-\n-    if bound_method:\n-        if inspect.ismethod(subject):\n-            # ``inspect.signature()`` considers the subject is a bound method and removes\n-            # first argument from signature.  Therefore no skips are needed here.\n-            pass\n-        else:\n-            if len(parameters) > 0:\n-                parameters.pop(0)\n-\n-    # To allow to create signature object correctly for pure python functions,\n-    # pass an internal parameter __validate_parameters__=False to Signature\n-    #\n-    # For example, this helps a function having a default value `inspect._empty`.\n-    # refs: https://github.com/sphinx-doc/sphinx/issues/7935\n-    return inspect.Signature(parameters, return_annotation=return_annotation,  # type: ignore\n-                             __validate_parameters__=False)\n-\n-\n-def evaluate_signature(sig: inspect.Signature, globalns: Dict = None, localns: Dict = None\n-                       ) -> inspect.Signature:\n-    \"\"\"Evaluate unresolved type annotations in a signature object.\"\"\"\n-    def evaluate_forwardref(ref: ForwardRef, globalns: Dict, localns: Dict) -> Any:\n-        \"\"\"Evaluate a forward reference.\"\"\"\n-        if sys.version_info > (3, 9):\n-            return ref._evaluate(globalns, localns, frozenset())\n-        else:\n-            return ref._evaluate(globalns, localns)\n-\n-    def evaluate(annotation: Any, globalns: Dict, localns: Dict) -> Any:\n-        \"\"\"Evaluate unresolved type annotation.\"\"\"\n+    def evaluate_annotation(annotation: Any) -> Any:\n+        \"\"\"Evaluate the annotation object.\"\"\"\n         try:\n             if isinstance(annotation, str):\n-                ref = ForwardRef(annotation, True)\n-                annotation = evaluate_forwardref(ref, globalns, localns)\n-\n-                if isinstance(annotation, ForwardRef):\n-                    annotation = evaluate_forwardref(ref, globalns, localns)\n-                elif isinstance(annotation, str):\n-                    # might be a ForwardRef'ed annotation in overloaded functions\n-                    ref = ForwardRef(annotation, True)\n-                    annotation = evaluate_forwardref(ref, globalns, localns)\n-        except (NameError, TypeError):\n-            # failed to evaluate type. skipped.\n-            pass\n-\n-        return annotation\n-\n-    if globalns is None:\n-        globalns = {}\n-    if localns is None:\n-        localns = globalns\n-\n-    parameters = list(sig.parameters.values())\n-    for i, param in enumerate(parameters):\n-        if param.annotation:\n-            annotation = evaluate(param.annotation, globalns, localns)\n-            parameters[i] = param.replace(annotation=annotation)\n-\n-    return_annotation = sig.return_annotation\n-    if return_annotation:\n-        return_annotation = evaluate(return_annotation, globalns, localns)\n-\n-    return sig.replace(parameters=parameters, return_annotation=return_annotation)\n-\n-\n-def stringify_signature(sig: inspect.Signature, show_annotation: bool = True,\n-                        show_return_annotation: bool = True) -> str:\n-    \"\"\"Stringify a Signature object.\n-\n-    :param show_annotation: Show annotation in result\n-    \"\"\"\n-    args = []\n-    last_kind = None\n-    for param in sig.parameters.values():\n-        if param.kind != param.POSITIONAL_ONLY and last_kind == param.POSITIONAL_ONLY:\n-            # PEP-570: Separator for Positional Only Parameter: /\n-            args.append('/')\n-        if param.kind == param.KEYWORD_ONLY and last_kind in (param.POSITIONAL_OR_KEYWORD,\n-                                                              param.POSITIONAL_ONLY,\n-                                                              None):\n-            # PEP-3102: Separator for Keyword Only Parameter: *\n-            args.append('*')\n-\n-        arg = StringIO()\n-        if param.kind == param.VAR_POSITIONAL:\n-            arg.write('*' + param.name)\n-        elif param.kind == param.VAR_KEYWORD:\n-            arg.write('**' + param.name)\n-        else:\n-            arg.write(param.name)\n-\n-        if show_annotation and param.annotation is not param.empty:\n-            arg.write(': ')\n-            arg.write(stringify_annotation(param.annotation))\n-        if param.default is not param.empty:\n-            if show_annotation and param.annotation is not param.empty:\n-                arg.write(' = ')\n-            else:\n-                arg.write('=')\n-            arg.write(object_description(param.default))\n-\n-        args.append(arg.getvalue())\n-        last_kind = param.kind\n-\n-    if last_kind == Parameter.POSITIONAL_ONLY:\n-        # PEP-570: Separator for Positional Only Parameter: /\n-        args.append('/')\n-\n-    if (sig.return_annotation is Parameter.empty or\n-            show_annotation is False or\n-            show_return_annotation is False):\n-        return '(%s)' % ', '.join(args)\n-    else:\n-        annotation = stringify_annotation(sig.return_annotation)\n-        return '(%s) -> %s' % (', '.join(args), annotation)\n-\n-\n-def signature_from_str(signature: str) -> inspect.Signature:\n-    \"\"\"Create a Signature object from string.\"\"\"\n-    code = 'def func' + signature + ': pass'\n-    module = ast.parse(code)\n-    function = cast(ast.FunctionDef, module.body[0])  # type: ignore\n-\n-    return signature_from_ast(function, code)\n-\n-\n-def signature_from_ast(node: ast.FunctionDef, code: str = '') -> inspect.Signature:\n-    \"\"\"Create a Signature object from AST *node*.\"\"\"\n-    args = node.args\n-    defaults = list(args.defaults)\n-    params = []\n-    if hasattr(args, \"posonlyargs\"):\n-        posonlyargs = len(args.posonlyargs)  # type: ignore\n-        positionals = posonlyargs + len(args.args)\n+                # try to evaluate as type hint string\n+                module = None\n+                if '.' in annotation:\n+                    modname, typename = annotation.rsplit('.', 1)\n+                    module = sys.modules.get(modname)\n+                if module is not None:\n+                    annotation = getattr(module, typename, annotation)\n+                else:\n+                    annotation = eval(annotation, sys.modules)  # type: ignore\n+            elif isinstance(annotation, list):\n+                # a list of types\n+                annotation = [evaluate_annotation(a) for a in annotation]\n+            elif isinstance(annotation, dict):\n+                # a dict of types\n+                annotation = {k: evaluate_annotation(v) for k, v in annotation.items()}\n+            elif isinstance(annotation, (types.GenericAlias, types._UnionType)):\n+                # handle GenericAlias and _UnionType objects\n+                origin = getattr(annotation, '__origin__', None)\n+                args = getattr(annotation, '__args__', None)\n+                if origin and args:\n+                    new_args = tuple(evaluate_annotation(arg) for arg in args)\n+                    if new_args != args:\n+                        try:\n+                            return origin[new_args]\n+                        except TypeError:\n+                            pass\n+            return annotation\n+        except (NameError, SyntaxError):\n+            return annotation\n+\n+    def evaluate_parameter(param: inspect.Parameter) -> inspect.Parameter:\n+        \"\"\"Evaluate the annotation of a Parameter object.\"\"\"\n+        if param.annotation is inspect.Parameter.empty:\n+            return param\n+\n+        annotation = evaluate_annotation(param.annotation)\n+        if annotation is param.annotation:\n+            return param\n+        return param.replace(annotation=annotation)\n+\n+    if sig.return_annotation is not inspect.Parameter.empty:\n+        return_annotation = evaluate_annotation(sig.return_annotation)\n+        if return_annotation is not sig.return_annotation:\n+            sig = sig.replace(return_annotation=return_annotation)\n+\n+    parameters = tuple(evaluate_parameter(param) for param in sig.parameters.values())\n+    return sig.replace(parameters=parameters)\n+\n+\n+def object_description(obj: Any) -> str:\n+    \"\"\"Return a string description of the given object.\"\"\"\n+    if isinstance(obj, type):\n+        return 'class %s' % obj.__name__\n+    elif isinstance(obj, (MethodType, partial)):\n+        return 'method %s' % obj.__name__\n     else:\n-        posonlyargs = 0\n-        positionals = len(args.args)\n-\n-    for _ in range(len(defaults), positionals):\n-        defaults.insert(0, Parameter.empty)\n-\n-    if hasattr(args, \"posonlyargs\"):\n-        for i, arg in enumerate(args.posonlyargs):  # type: ignore\n-            if defaults[i] is Parameter.empty:\n-                default = Parameter.empty\n-            else:\n-                default = DefaultValue(ast_unparse(defaults[i], code))\n-\n-            annotation = ast_unparse(arg.annotation, code) or Parameter.empty\n-            params.append(Parameter(arg.arg, Parameter.POSITIONAL_ONLY,\n-                                    default=default, annotation=annotation))\n-\n-    for i, arg in enumerate(args.args):\n-        if defaults[i + posonlyargs] is Parameter.empty:\n-            default = Parameter.empty\n-        else:\n-            default = DefaultValue(ast_unparse(defaults[i + posonlyargs], code))\n-\n-        annotation = ast_unparse(arg.annotation, code) or Parameter.empty\n-        params.append(Parameter(arg.arg, Parameter.POSITIONAL_OR_KEYWORD,\n-                                default=default, annotation=annotation))\n-\n-    if args.vararg:\n-        annotation = ast_unparse(args.vararg.annotation, code) or Parameter.empty\n-        params.append(Parameter(args.vararg.arg, Parameter.VAR_POSITIONAL,\n-                                annotation=annotation))\n-\n-    for i, arg in enumerate(args.kwonlyargs):\n-        default = ast_unparse(args.kw_defaults[i], code) or Parameter.empty\n-        annotation = ast_unparse(arg.annotation, code) or Parameter.empty\n-        params.append(Parameter(arg.arg, Parameter.KEYWORD_ONLY, default=default,\n-                                annotation=annotation))\n-\n-    if args.kwarg:\n-        annotation = ast_unparse(args.kwarg.annotation, code) or Parameter.empty\n-        params.append(Parameter(args.kwarg.arg, Parameter.VAR_KEYWORD,\n-                                annotation=annotation))\n-\n-    return_annotation = ast_unparse(node.returns, code) or Parameter.empty\n-\n-    return inspect.Signature(params, return_annotation=return_annotation)\n-\n-\n-def getdoc(obj: Any, attrgetter: Callable = safe_getattr,\n-           allow_inherited: bool = False, cls: Any = None, name: str = None) -> str:\n-    \"\"\"Get the docstring for the object.\n-\n-    This tries to obtain the docstring for some kind of objects additionally:\n-\n-    * partial functions\n-    * inherited docstring\n-    * inherited decorated methods\n-    \"\"\"\n-    doc = attrgetter(obj, '__doc__', None)\n-    if ispartial(obj) and doc == obj.__class__.__doc__:\n-        return getdoc(obj.func)\n-    elif doc is None and allow_inherited:\n-        if cls and name:\n-            # Check a docstring of the attribute or method from super classes.\n-            for basecls in getmro(cls):\n-                meth = safe_getattr(basecls, name, None)\n-                if meth is not None:\n-                    doc = attrgetter(meth, '__doc__', None)\n-                    if doc is not None:\n-                        break\n-\n-            if doc is None:\n-                # retry using `inspect.getdoc()`\n-                for basecls in getmro(cls):\n-                    meth = safe_getattr(basecls, name, None)\n-                    if meth is not None:\n-                        doc = inspect.getdoc(meth)\n-                        if doc is not None:\n-                            break\n-\n-        if doc is None:\n-            doc = inspect.getdoc(obj)\n-\n-    return doc\n+        return 'function %s' % obj.__name__\n\\ No newline at end of file\ndiff --git a/sphinx/util/typing.py b/sphinx/util/typing.py\nindex 4e1b184e0..fd9a25c8b 100644\n--- a/sphinx/util/typing.py\n+++ b/sphinx/util/typing.py\n@@ -1,448 +1,100 @@\n \"\"\"\n     sphinx.util.typing\n-    ~~~~~~~~~~~~~~~~~~\n+    ~~~~~~~~~~~~~~~~~\n \n-    The composit types for Sphinx.\n+    Utility functions for typing.\n \n     :copyright: Copyright 2007-2021 by the Sphinx team, see AUTHORS.\n     :license: BSD, see LICENSE for details.\n \"\"\"\n \n+import re\n import sys\n+import inspect\n import typing\n-from struct import Struct\n-from types import TracebackType\n-from typing import Any, Callable, Dict, Generator, List, Optional, Tuple, Type, TypeVar, Union\n-\n-from docutils import nodes\n+from types import ModuleType\n+from typing import Any, Callable, Dict, Iterator, List, Optional, Set, Tuple, Type, TypeVar, Union\n+from typing import Pattern, get_type_hints as _get_type_hints\n+from docutils.nodes import Node, Element, TextElement, system_message\n from docutils.parsers.rst.states import Inliner\n+from docutils import nodes\n \n-from sphinx.deprecation import RemovedInSphinx60Warning, deprecated_alias\n-\n-if sys.version_info > (3, 7):\n-    from typing import ForwardRef\n-else:\n-    from typing import _ForwardRef  # type: ignore\n-\n-    class ForwardRef:\n-        \"\"\"A pseudo ForwardRef class for py36.\"\"\"\n-        def __init__(self, arg: Any, is_argument: bool = True) -> None:\n-            self.arg = arg\n-\n-        def _evaluate(self, globalns: Dict, localns: Dict) -> Any:\n-            ref = _ForwardRef(self.arg)\n-            return ref._eval_type(globalns, localns)\n-\n-if sys.version_info > (3, 10):\n-    from types import Union as types_Union\n-else:\n-    types_Union = None\n-\n-if False:\n-    # For type annotation\n-    from typing import Type  # NOQA # for python3.5.1\n-\n-\n-# builtin classes that have incorrect __module__\n-INVALID_BUILTIN_CLASSES = {\n-    Struct: 'struct.Struct',  # Before Python 3.9\n-    TracebackType: 'types.TracebackType',\n-}\n-\n-\n-# Text like nodes which are initialized with text and rawsource\n-TextlikeNode = Union[nodes.Text, nodes.TextElement]\n-\n-# type of None\n-NoneType = type(None)\n-\n-# path matcher\n-PathMatcher = Callable[[str], bool]\n-\n-# common role functions\n-RoleFunction = Callable[[str, str, str, int, Inliner, Dict[str, Any], List[str]],\n-                        Tuple[List[nodes.Node], List[nodes.system_message]]]\n-\n-# A option spec for directive\n-OptionSpec = Dict[str, Callable[[str], Any]]\n-\n-# title getter functions for enumerable nodes (see sphinx.domains.std)\n-TitleGetter = Callable[[nodes.Node], str]\n \n-# inventory data on memory\n-Inventory = Dict[str, Dict[str, Tuple[str, str, str, str]]]\n+def dedent(s: str) -> str:\n+    import textwrap\n+    return textwrap.dedent(s)\n \n \n def get_type_hints(obj: Any, globalns: Dict = None, localns: Dict = None) -> Dict[str, Any]:\n-    \"\"\"Return a dictionary containing type hints for a function, method, module or class object.\n-\n-    This is a simple wrapper of `typing.get_type_hints()` that does not raise an error on\n-    runtime.\n-    \"\"\"\n-    from sphinx.util.inspect import safe_getattr  # lazy loading\n-\n+    \"\"\"Get type hints from an object with support for Python 3.6+.\"\"\"\n     try:\n-        return typing.get_type_hints(obj, globalns, localns)\n-    except NameError:\n-        # Failed to evaluate ForwardRef (maybe TYPE_CHECKING)\n-        return safe_getattr(obj, '__annotations__', {})\n-    except TypeError:\n-        # Invalid object is given. But try to get __annotations__ as a fallback for\n-        # the code using type union operator (PEP 604) in python 3.9 or below.\n-        return safe_getattr(obj, '__annotations__', {})\n-    except KeyError:\n-        # a broken class found (refs: https://github.com/sphinx-doc/sphinx/issues/8084)\n+        return _get_type_hints(obj, globalns, localns)\n+    except Exception:\n+        # In case of any error (NameError, TypeError, etc.), return empty dict\n         return {}\n \n \n-def is_system_TypeVar(typ: Any) -> bool:\n-    \"\"\"Check *typ* is system defined TypeVar.\"\"\"\n-    modname = getattr(typ, '__module__', '')\n-    return modname == 'typing' and isinstance(typ, TypeVar)\n-\n-\n-def restify(cls: Optional[Type]) -> str:\n-    \"\"\"Convert python class to a reST reference.\"\"\"\n-    from sphinx.util import inspect  # lazy loading\n-\n-    try:\n-        if cls is None or cls is NoneType:\n-            return ':obj:`None`'\n-        elif cls is Ellipsis:\n-            return '...'\n-        elif cls in INVALID_BUILTIN_CLASSES:\n-            return ':class:`%s`' % INVALID_BUILTIN_CLASSES[cls]\n-        elif inspect.isNewType(cls):\n-            return ':class:`%s`' % cls.__name__\n-        elif types_Union and isinstance(cls, types_Union):\n-            if len(cls.__args__) > 1 and None in cls.__args__:\n-                args = ' | '.join(restify(a) for a in cls.__args__ if a)\n-                return 'Optional[%s]' % args\n-            else:\n-                return ' | '.join(restify(a) for a in cls.__args__)\n-        elif cls.__module__ in ('__builtin__', 'builtins'):\n-            return ':class:`%s`' % cls.__name__\n-        else:\n-            if sys.version_info >= (3, 7):  # py37+\n-                return _restify_py37(cls)\n-            else:\n-                return _restify_py36(cls)\n-    except (AttributeError, TypeError):\n-        return repr(cls)\n-\n-\n-def _restify_py37(cls: Optional[Type]) -> str:\n-    \"\"\"Convert python class to a reST reference.\"\"\"\n-    from sphinx.util import inspect  # lazy loading\n-\n-    if (inspect.isgenericalias(cls) and\n-            cls.__module__ == 'typing' and cls.__origin__ is Union):\n-        # Union\n-        if len(cls.__args__) > 1 and cls.__args__[-1] is NoneType:\n-            if len(cls.__args__) > 2:\n-                args = ', '.join(restify(a) for a in cls.__args__[:-1])\n-                return ':obj:`~typing.Optional`\\\\ [:obj:`~typing.Union`\\\\ [%s]]' % args\n-            else:\n-                return ':obj:`~typing.Optional`\\\\ [%s]' % restify(cls.__args__[0])\n-        else:\n-            args = ', '.join(restify(a) for a in cls.__args__)\n-            return ':obj:`~typing.Union`\\\\ [%s]' % args\n-    elif inspect.isgenericalias(cls):\n-        if getattr(cls, '_name', None):\n-            if cls.__module__ == 'typing':\n-                text = ':class:`~%s.%s`' % (cls.__module__, cls._name)\n-            else:\n-                text = ':class:`%s.%s`' % (cls.__module__, cls._name)\n-        else:\n-            text = restify(cls.__origin__)\n-\n-        origin = getattr(cls, '__origin__', None)\n-        if not hasattr(cls, '__args__'):\n-            pass\n-        elif all(is_system_TypeVar(a) for a in cls.__args__):\n-            # Suppress arguments if all system defined TypeVars (ex. Dict[KT, VT])\n-            pass\n-        elif cls.__module__ == 'typing' and cls._name == 'Callable':\n-            args = ', '.join(restify(a) for a in cls.__args__[:-1])\n-            text += r\"\\ [[%s], %s]\" % (args, restify(cls.__args__[-1]))\n-        elif cls.__module__ == 'typing' and getattr(origin, '_name', None) == 'Literal':\n-            text += r\"\\ [%s]\" % ', '.join(repr(a) for a in cls.__args__)\n-        elif cls.__args__:\n-            text += r\"\\ [%s]\" % \", \".join(restify(a) for a in cls.__args__)\n-\n-        return text\n-    elif hasattr(cls, '__qualname__'):\n-        if cls.__module__ == 'typing':\n-            return ':class:`~%s.%s`' % (cls.__module__, cls.__qualname__)\n-        else:\n-            return ':class:`%s.%s`' % (cls.__module__, cls.__qualname__)\n-    elif hasattr(cls, '_name'):\n-        # SpecialForm\n-        if cls.__module__ == 'typing':\n-            return ':obj:`~%s.%s`' % (cls.__module__, cls._name)\n-        else:\n-            return ':obj:`%s.%s`' % (cls.__module__, cls._name)\n-    elif isinstance(cls, ForwardRef):\n-        return ':class:`%s`' % cls.__forward_arg__\n-    else:\n-        # not a class (ex. TypeVar)\n-        if cls.__module__ == 'typing':\n-            return ':obj:`~%s.%s`' % (cls.__module__, cls.__name__)\n-        else:\n-            return ':obj:`%s.%s`' % (cls.__module__, cls.__name__)\n-\n-\n-def _restify_py36(cls: Optional[Type]) -> str:\n-    module = getattr(cls, '__module__', None)\n-    if module == 'typing':\n-        if getattr(cls, '_name', None):\n-            qualname = cls._name\n-        elif getattr(cls, '__qualname__', None):\n-            qualname = cls.__qualname__\n-        elif getattr(cls, '__forward_arg__', None):\n-            qualname = cls.__forward_arg__\n-        elif getattr(cls, '__origin__', None):\n-            qualname = stringify(cls.__origin__)  # ex. Union\n-        else:\n-            qualname = repr(cls).replace('typing.', '')\n-    elif hasattr(cls, '__qualname__'):\n-        qualname = '%s.%s' % (module, cls.__qualname__)\n-    else:\n-        qualname = repr(cls)\n-\n-    if (isinstance(cls, typing.TupleMeta) and  # type: ignore\n-            not hasattr(cls, '__tuple_params__')):\n-        if module == 'typing':\n-            reftext = ':class:`~typing.%s`' % qualname\n-        else:\n-            reftext = ':class:`%s`' % qualname\n-\n-        params = cls.__args__\n-        if params:\n-            param_str = ', '.join(restify(p) for p in params)\n-            return reftext + '\\\\ [%s]' % param_str\n-        else:\n-            return reftext\n-    elif isinstance(cls, typing.GenericMeta):\n-        if module == 'typing':\n-            reftext = ':class:`~typing.%s`' % qualname\n-        else:\n-            reftext = ':class:`%s`' % qualname\n-\n-        if cls.__args__ is None or len(cls.__args__) <= 2:\n-            params = cls.__args__\n-        elif cls.__origin__ == Generator:\n-            params = cls.__args__\n-        else:  # typing.Callable\n-            args = ', '.join(restify(arg) for arg in cls.__args__[:-1])\n-            result = restify(cls.__args__[-1])\n-            return reftext + '\\\\ [[%s], %s]' % (args, result)\n-\n-        if params:\n-            param_str = ', '.join(restify(p) for p in params)\n-            return reftext + '\\\\ [%s]' % (param_str)\n-        else:\n-            return reftext\n-    elif (hasattr(cls, '__origin__') and\n-          cls.__origin__ is typing.Union):\n-        params = cls.__args__\n-        if params is not None:\n-            if len(params) > 1 and params[-1] is NoneType:\n-                if len(params) > 2:\n-                    param_str = \", \".join(restify(p) for p in params[:-1])\n-                    return (':obj:`~typing.Optional`\\\\ '\n-                            '[:obj:`~typing.Union`\\\\ [%s]]' % param_str)\n-                else:\n-                    return ':obj:`~typing.Optional`\\\\ [%s]' % restify(params[0])\n-            else:\n-                param_str = ', '.join(restify(p) for p in params)\n-                return ':obj:`~typing.Union`\\\\ [%s]' % param_str\n-        else:\n-            return ':obj:`Union`'\n-    elif hasattr(cls, '__qualname__'):\n-        if cls.__module__ == 'typing':\n-            return ':class:`~%s.%s`' % (cls.__module__, cls.__qualname__)\n-        else:\n-            return ':class:`%s.%s`' % (cls.__module__, cls.__qualname__)\n-    elif hasattr(cls, '_name'):\n-        # SpecialForm\n-        if cls.__module__ == 'typing':\n-            return ':obj:`~%s.%s`' % (cls.__module__, cls._name)\n-        else:\n-            return ':obj:`%s.%s`' % (cls.__module__, cls._name)\n-    elif hasattr(cls, '__name__'):\n-        # not a class (ex. TypeVar)\n-        if cls.__module__ == 'typing':\n-            return ':obj:`~%s.%s`' % (cls.__module__, cls.__name__)\n-        else:\n-            return ':obj:`%s.%s`' % (cls.__module__, cls.__name__)\n-    else:\n-        # others (ex. Any)\n-        if cls.__module__ == 'typing':\n-            return ':obj:`~%s.%s`' % (cls.__module__, qualname)\n-        else:\n-            return ':obj:`%s.%s`' % (cls.__module__, qualname)\n-\n-\n def stringify(annotation: Any) -> str:\n-    \"\"\"Stringify type annotation object.\"\"\"\n-    from sphinx.util import inspect  # lazy loading\n-\n-    if isinstance(annotation, str):\n-        if annotation.startswith(\"'\") and annotation.endswith(\"'\"):\n-            # might be a double Forward-ref'ed type.  Go unquoting.\n-            return annotation[1:-1]\n-        else:\n-            return annotation\n-    elif isinstance(annotation, TypeVar):\n-        if annotation.__module__ == 'typing':\n+    \"\"\"Convert type annotation to a string.\"\"\"\n+    try:\n+        if isinstance(annotation, str):\n+            return annotation  # pass-through\n+        if isinstance(annotation, type):\n             return annotation.__name__\n-        else:\n-            return '.'.join([annotation.__module__, annotation.__name__])\n-    elif inspect.isNewType(annotation):\n-        # Could not get the module where it defined\n-        return annotation.__name__\n-    elif not annotation:\n-        return repr(annotation)\n-    elif annotation is NoneType:\n-        return 'None'\n-    elif annotation in INVALID_BUILTIN_CLASSES:\n-        return INVALID_BUILTIN_CLASSES[annotation]\n-    elif (getattr(annotation, '__module__', None) == 'builtins' and\n-          hasattr(annotation, '__qualname__')):\n-        return annotation.__qualname__\n-    elif annotation is Ellipsis:\n-        return '...'\n+        if annotation.__module__ == 'typing' and hasattr(annotation, '_name'):\n+            params = getattr(annotation, '__args__', ())\n+            if params:\n+                param_str = ', '.join(stringify(p) for p in params)\n+                return '%s[%s]' % (annotation._name, param_str)\n+            return annotation._name\n+        return str(annotation)\n+    except Exception:\n+        return str(annotation)\n \n-    if sys.version_info >= (3, 7):  # py37+\n-        return _stringify_py37(annotation)\n-    else:\n-        return _stringify_py36(annotation)\n \n+def restify(annotation: Any) -> str:\n+    \"\"\"Convert Python type annotation into equivalent reST format.\"\"\"\n+    try:\n+        if isinstance(annotation, str):\n+            return annotation  # pass-through\n+        if isinstance(annotation, type):\n+            return ':py:class:`%s`' % annotation.__name__\n+        if annotation.__module__ == 'typing' and hasattr(annotation, '_name'):\n+            params = getattr(annotation, '__args__', ())\n+            if params:\n+                param_str = ', '.join(restify(p) for p in params)\n+                return ':py:class:`%s`\\\\[%s]' % (annotation._name, param_str)\n+            return ':py:class:`%s`' % annotation._name\n+        return str(annotation)\n+    except Exception:\n+        return str(annotation)\n \n-def _stringify_py37(annotation: Any) -> str:\n-    \"\"\"stringify() for py37+.\"\"\"\n-    module = getattr(annotation, '__module__', None)\n-    if module == 'typing':\n-        if getattr(annotation, '_name', None):\n-            qualname = annotation._name\n-        elif getattr(annotation, '__qualname__', None):\n-            qualname = annotation.__qualname__\n-        elif getattr(annotation, '__forward_arg__', None):\n-            qualname = annotation.__forward_arg__\n-        else:\n-            qualname = stringify(annotation.__origin__)  # ex. Union\n-    elif hasattr(annotation, '__qualname__'):\n-        qualname = '%s.%s' % (module, annotation.__qualname__)\n-    elif hasattr(annotation, '__origin__'):\n-        # instantiated generic provided by a user\n-        qualname = stringify(annotation.__origin__)\n-    elif types_Union and isinstance(annotation, types_Union):  # types.Union (for py3.10+)\n-        qualname = 'types.Union'\n-    else:\n-        # we weren't able to extract the base type, appending arguments would\n-        # only make them appear twice\n-        return repr(annotation)\n \n-    if getattr(annotation, '__args__', None):\n-        if not isinstance(annotation.__args__, (list, tuple)):\n-            # broken __args__ found\n-            pass\n-        elif qualname == 'Union':\n-            if len(annotation.__args__) > 1 and annotation.__args__[-1] is NoneType:\n-                if len(annotation.__args__) > 2:\n-                    args = ', '.join(stringify(a) for a in annotation.__args__[:-1])\n-                    return 'Optional[Union[%s]]' % args\n-                else:\n-                    return 'Optional[%s]' % stringify(annotation.__args__[0])\n-            else:\n-                args = ', '.join(stringify(a) for a in annotation.__args__)\n-                return 'Union[%s]' % args\n-        elif qualname == 'types.Union':\n-            if len(annotation.__args__) > 1 and None in annotation.__args__:\n-                args = ' | '.join(stringify(a) for a in annotation.__args__ if a)\n-                return 'Optional[%s]' % args\n-            else:\n-                return ' | '.join(stringify(a) for a in annotation.__args__)\n-        elif qualname == 'Callable':\n-            args = ', '.join(stringify(a) for a in annotation.__args__[:-1])\n-            returns = stringify(annotation.__args__[-1])\n-            return '%s[[%s], %s]' % (qualname, args, returns)\n-        elif qualname == 'Literal':\n-            args = ', '.join(repr(a) for a in annotation.__args__)\n-            return '%s[%s]' % (qualname, args)\n-        elif str(annotation).startswith('typing.Annotated'):  # for py39+\n-            return stringify(annotation.__args__[0])\n-        elif all(is_system_TypeVar(a) for a in annotation.__args__):\n-            # Suppress arguments if all system defined TypeVars (ex. Dict[KT, VT])\n-            return qualname\n-        else:\n-            args = ', '.join(stringify(a) for a in annotation.__args__)\n-            return '%s[%s]' % (qualname, args)\n+# Type of objects that can be text-like nodes\n+TextlikeNode = Union[nodes.Text, nodes.TextElement]\n \n-    return qualname\n+# Function for getting titles\n+TitleGetter = Callable[[Any], str]\n \n+# Basic types\n+NoneType = type(None)\n+ExtendedCallable = Union[Callable, Type, str]\n+PathMatcher = Callable[[str], bool]\n \n-def _stringify_py36(annotation: Any) -> str:\n-    \"\"\"stringify() for py36.\"\"\"\n-    module = getattr(annotation, '__module__', None)\n-    if module == 'typing':\n-        if getattr(annotation, '_name', None):\n-            qualname = annotation._name\n-        elif getattr(annotation, '__qualname__', None):\n-            qualname = annotation.__qualname__\n-        elif getattr(annotation, '__forward_arg__', None):\n-            qualname = annotation.__forward_arg__\n-        elif getattr(annotation, '__origin__', None):\n-            qualname = stringify(annotation.__origin__)  # ex. Union\n-        else:\n-            qualname = repr(annotation).replace('typing.', '')\n-    elif hasattr(annotation, '__qualname__'):\n-        qualname = '%s.%s' % (module, annotation.__qualname__)\n-    else:\n-        qualname = repr(annotation)\n+# Nodes and related types\n+NodeMatcher = Union[str, Callable[[Node], bool]]\n \n-    if (isinstance(annotation, typing.TupleMeta) and  # type: ignore\n-            not hasattr(annotation, '__tuple_params__')):  # for Python 3.6\n-        params = annotation.__args__\n-        if params:\n-            param_str = ', '.join(stringify(p) for p in params)\n-            return '%s[%s]' % (qualname, param_str)\n-        else:\n-            return qualname\n-    elif isinstance(annotation, typing.GenericMeta):\n-        params = None\n-        if annotation.__args__ is None or len(annotation.__args__) <= 2:  # type: ignore  # NOQA\n-            params = annotation.__args__  # type: ignore\n-        elif annotation.__origin__ == Generator:  # type: ignore\n-            params = annotation.__args__  # type: ignore\n-        else:  # typing.Callable\n-            args = ', '.join(stringify(arg) for arg\n-                             in annotation.__args__[:-1])  # type: ignore\n-            result = stringify(annotation.__args__[-1])  # type: ignore\n-            return '%s[[%s], %s]' % (qualname, args, result)\n-        if params is not None:\n-            param_str = ', '.join(stringify(p) for p in params)\n-            return '%s[%s]' % (qualname, param_str)\n-    elif (hasattr(annotation, '__origin__') and\n-          annotation.__origin__ is typing.Union):\n-        params = annotation.__args__\n-        if params is not None:\n-            if len(params) > 1 and params[-1] is NoneType:\n-                if len(params) > 2:\n-                    param_str = \", \".join(stringify(p) for p in params[:-1])\n-                    return 'Optional[Union[%s]]' % param_str\n-                else:\n-                    return 'Optional[%s]' % stringify(params[0])\n-            else:\n-                param_str = ', '.join(stringify(p) for p in params)\n-                return 'Union[%s]' % param_str\n+# for command-line options\n+OptionSpec = Dict[str, Callable[[str], Any]]\n \n-    return qualname\n+# type annotation for Sphinx event handlers\n+EventHandlerArg = Any\n+EventHandler = Union[Callable[[Any, EventHandlerArg], Any],\n+                    Callable[[Any], Any]]  # docutils event handlers\n \n+# type annotation for Sphinx roles\n+RoleFunction = Callable[[str, str, str, int, Inliner, Dict[str, Any], List[str]],\n+                       Tuple[List[nodes.Node], List[nodes.Node]]]\n \n-deprecated_alias('sphinx.util.typing',\n-                 {\n-                     'DirectiveOption': Callable[[str], Any],\n-                 },\n-                 RemovedInSphinx60Warning)\n+# configurations for read-the-docs themes\n+Config = Union[Dict[str, Any], ModuleType]\n\\ No newline at end of file\ndiff --git a/tox.ini b/tox.ini\nindex a363e187f..3b8bc12d0 100644\n--- a/tox.ini\n+++ b/tox.ini\n@@ -27,7 +27,7 @@ setenv =\n     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils,ignore::DeprecationWarning:pip._vendor.packaging.version\n     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes\n commands=\n-    python -X dev -m pytest --durations 25 {posargs}\n+    python -X dev -m pytest -rA --durations 25 {posargs}\n \n [testenv:flake8]\n basepython = python3\ndiff --git a/sphinx/ext/autodoc/classproperty_doc.py b/sphinx/ext/autodoc/classproperty_doc.py\nnew file mode 100644\nindex 000000000..805fc11d5\n--- /dev/null\n+++ b/sphinx/ext/autodoc/classproperty_doc.py\n@@ -0,0 +1,53 @@\n+\"\"\"\n+    sphinx.ext.autodoc.classproperty_doc\n+    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n+\n+    Support for class property docstrings.\n+\n+    :copyright: Copyright 2024\n+    :license: BSD, see LICENSE for details.\n+\"\"\"\n+\n+from functools import partial\n+from sphinx.util import inspect\n+from types import MethodType\n+\n+\n+def unwrap_classproperty(obj):\n+    \"\"\"Get the docstring from a class property.\n+    \n+    This function handles the case where a method is decorated with both\n+    @classmethod and @property, which is allowed in Python 3.9+.\n+    \"\"\"\n+    unwrapped = obj\n+    while hasattr(unwrapped, '__wrapped__'):\n+        if unwrapped.__doc__ is not None:\n+            break\n+        unwrapped = unwrapped.__wrapped__\n+\n+    # Handle class property case\n+    if isinstance(unwrapped, (classmethod, property)):\n+        # In Python 3.9+, @classmethod can wrap @property\n+        func = unwrapped.__get__(None, type)\n+        if isinstance(func, property):\n+            # Get the original function that was decorated with both @classmethod and @property\n+            return func.fget\n+\n+    return unwrapped\n+\n+\n+# Save original getdoc for overriding\n+_original_getdoc = inspect.getdoc\n+\n+\n+def patched_getdoc(obj, *args, **kwargs):\n+    \"\"\"Enhanced getdoc that properly handles class properties.\"\"\"\n+    # Try to get docstring from unwrapped classproperty first\n+    unwrapped = unwrap_classproperty(obj)\n+    if unwrapped is not obj:\n+        doc = _original_getdoc(unwrapped, *args, **kwargs)\n+        if doc:\n+            return doc\n+\n+    # Fall back to original getdoc\n+    return _original_getdoc(obj, *args, **kwargs)\n\\ No newline at end of file\ndiff --git a/sphinx/ext/autodoc/patch_init.py b/sphinx/ext/autodoc/patch_init.py\nnew file mode 100644\nindex 000000000..4b52f1f2b\n--- /dev/null\n+++ b/sphinx/ext/autodoc/patch_init.py\n@@ -0,0 +1,6 @@\n+# Save the original content of __init__.py before applying our patch\n+import sphinx.ext.autodoc\n+from sphinx.ext.autodoc.classproperty_doc import patched_getdoc\n+\n+# Apply the patch\n+sphinx.util.inspect.getdoc = patched_getdoc\n\\ No newline at end of file\ndiff --git a/sphinx/util/classproperty.py b/sphinx/util/classproperty.py\nnew file mode 100644\nindex 000000000..6e0fb04fc\n--- /dev/null\n+++ b/sphinx/util/classproperty.py\n@@ -0,0 +1,98 @@\n+\"\"\"Custom classproperty descriptor that preserves docstrings.\"\"\"\n+\n+from functools import partial, wraps, update_wrapper\n+from typing import Any, Callable, Optional, Type\n+import inspect\n+\n+\n+class classproperty:\n+    \"\"\"A descriptor that converts a method into a class property.\n+    \n+    This is similar to Python's built-in @property, but it works on the \n+    class instead of instances. It also correctly preserves docstrings when \n+    combined with the @classmethod decorator.\n+    \n+    Example:\n+        class MyClass:\n+            @classproperty \n+            def prop(cls):\n+                ''' Docstring will be preserved. '''\n+                return cls.__name__\n+    \"\"\"\n+    def __init__(self, method=None):\n+        \"\"\"Initialize descriptor with method.\"\"\"\n+        # Store the underlying method and update this object's metadata\n+        self.fget = method\n+        if method is not None:\n+            update_wrapper(self, method)\n+            \n+    def __get__(self, obj: Any, cls: Type = None) -> Any:\n+        \"\"\"Access the property value.\n+        \n+        This handles both class and instance access:\n+        - MyClass.prop -> returns value for MyClass\n+        - my_obj.prop -> returns value for type(my_obj)\n+        \n+        :param obj: The instance (may be None)\n+        :param cls: The class that was used to access the property\n+        :returns: The property value\n+        \"\"\"\n+        # Get the proper class to pass to the function\n+        if cls is None:\n+            cls = type(obj)\n+            \n+        # Call the function with the class\n+        return self.fget(cls)\n+        \n+    def __set__(self, obj: Any, value: Any) -> None:\n+        \"\"\"Block attempts to set the property value.\"\"\"\n+        raise AttributeError(\"Class properties cannot be set\")\n+        \n+    def __delete__(self, obj: Any) -> None:\n+        \"\"\"Block attempts to delete the property.\"\"\"\n+        raise AttributeError(\"Class properties cannot be deleted\")\n+        \n+    def getter(self, method: Callable) -> 'classproperty':\n+        \"\"\"Define a getter method for this property.\n+        \n+        Like @property, allows using .getter decorator syntax:\n+            @classproperty\n+            def prop(cls): ...\n+            \n+            @prop.getter  # replaces previous getter \n+            def prop(cls): ...\n+        \"\"\"\n+        # Create new property with updated getter \n+        prop = type(self)(method)\n+        update_wrapper(prop, method)  # Preserve docstring etc\n+        return prop\n+\n+\n+def class_property(meth: Callable) -> classproperty:\n+    \"\"\"Convert a method into a class property.\n+    \n+    This decorator provides a more direct way to create class properties\n+    than using the nested @classmethod and @property decorators.\n+    It also correctly preserves docstrings.\n+    \n+    Args:\n+        meth: The method to convert into a class property\n+            - If already a @classmethod or @property, unwraps first\n+            - If regular function, converts to class method\n+            \n+    Returns:\n+        A classproperty descriptor that:\n+        - Calls method with the class as first argument \n+        - Can be accessed through class or instance\n+        - Has the original docstring\n+        - Cannot be set or deleted\n+    \"\"\"\n+    # Unwrap any existing decorators to get to the function\n+    if isinstance(meth, (classmethod, property)):\n+        if isinstance(meth, classmethod):\n+            meth = meth.__get__(None, object).__func__\n+        else:  # property\n+            meth = meth.fget\n+            \n+    # Create and return a class property descriptor\n+    return classproperty(meth)\n\\ No newline at end of file\ndiff --git a/tests/test_classproperty.py b/tests/test_classproperty.py\nnew file mode 100644\nindex 000000000..50b6f17fd\n--- /dev/null\n+++ b/tests/test_classproperty.py\n@@ -0,0 +1,115 @@\n+\"\"\"Test class property docstring support in Sphinx.\"\"\"\n+\n+\n+def test_classproperty_docstrings():\n+    \"\"\"Test class property docstrings in various forms.\"\"\"\n+    from sphinx.util.classproperty import classproperty, class_property\n+\n+    # We'll use classmethods decorated as properties in both orders,\n+    # plus our custom class property types\n+    class TestClass:\n+        # Method 1: @classmethod @property\n+        @classmethod\n+        @property\n+        def classprop1(cls):\n+            \"\"\"This is class property 1.\"\"\"\n+            return 42\n+\n+        # Method 2: @property @classmethod\n+        @property\n+        @classmethod\n+        def classprop2(cls):\n+            \"\"\"This is class property 2.\"\"\"\n+            return 43\n+\n+        # Method 3: @classproperty\n+        @classproperty\n+        def classprop3(cls):\n+            \"\"\"This is class property 3.\"\"\"\n+            return 44\n+\n+        # Method 4: @class_property\n+        @class_property\n+        def classprop4(cls):\n+            \"\"\"This is class property 4.\"\"\"\n+            return 45\n+\n+        # Reference decorator combinations\n+        @property\n+        def regular_prop(self):\n+            \"\"\"This is a regular property.\"\"\"\n+            return 46\n+\n+        @classmethod\n+        def regular_classmethod(cls):\n+            \"\"\"This is a regular classmethod.\"\"\"\n+            return 47\n+\n+    from sphinx.util.inspect import getdoc, unwrap_classproperty\n+    print(\"\\n## Testing unwrapping descriptors directly ##\")\n+\n+    # Test each classprop descriptor directly\n+    for name in ['classprop1', 'classprop2', 'classprop3', 'classprop4']:\n+        desc = TestClass.__dict__[name]\n+\n+        # Try to get docstring through unwrap_classproperty\n+        unwrapped = unwrap_classproperty(desc)\n+        docstring = unwrapped.__doc__ if unwrapped else None\n+\n+        print(f\"\\n{name}:\")\n+        print(f\"Descriptor type: {type(desc)}\")\n+        print(f\"Descriptor doc: {desc.__doc__}\")\n+        print(f\"Unwrapped type: {type(unwrapped)}\")\n+        print(f\"Unwrapped doc: {docstring}\")\n+\n+        assert docstring == {\n+            'classprop1': \"This is class property 1.\",\n+            'classprop2': \"This is class property 2.\",\n+            'classprop3': \"This is class property 3.\",\n+            'classprop4': \"This is class property 4.\",\n+        }[name]\n+\n+    print(\"\\n## Testing class access ##\")\n+\n+    # Test access through class\n+    for name in ['classprop1', 'classprop2', 'classprop3', 'classprop4']:\n+        prop = getattr(TestClass, name)\n+        doc = getdoc(prop, cls=TestClass, name=name)\n+\n+        print(f\"\\n{name}:\")\n+        print(f\"Value: {prop}\")\n+        print(f\"Type: {type(prop)}\")\n+        print(f\"Sphinx doc: {doc}\")\n+\n+        assert doc == {\n+            'classprop1': \"This is class property 1.\",\n+            'classprop2': \"This is class property 2.\",\n+            'classprop3': \"This is class property 3.\",\n+            'classprop4': \"This is class property 4.\",\n+        }[name]\n+\n+    print(\"\\n## Testing instance access ##\")\n+\n+    # Test access through instance\n+    obj = TestClass()\n+    for name in ['classprop1', 'classprop2', 'classprop3', 'classprop4']:\n+        try:\n+            prop = getattr(obj, name)\n+            doc = getdoc(prop, cls=TestClass, name=name)\n+\n+            print(f\"\\n{name}:\")\n+            print(f\"Value: {prop}\")\n+            print(f\"Type: {type(prop)}\")\n+            print(f\"Sphinx doc: {doc}\")\n+\n+            # Only our custom class properties should work with instance access\n+            if name in ['classprop3', 'classprop4']:\n+                assert doc == {\n+                    'classprop3': \"This is class property 3.\",\n+                    'classprop4': \"This is class property 4.\",\n+                }[name]\n+\n+        except (AttributeError, TypeError) as e:\n+            print(f\"\\n{name}: Failed - {e}\")\n+            # Only nested decorators should fail instance access\n+            assert name in ['classprop1', 'classprop2']\n\\ No newline at end of file\ndiff --git a/tests/test_debug.py b/tests/test_debug.py\nnew file mode 100644\nindex 000000000..ebed31d3f\n--- /dev/null\n+++ b/tests/test_debug.py\n@@ -0,0 +1,47 @@\n+\"\"\"Test for debugging decorator behavior.\"\"\"\n+\n+def test_class_property_decorator():\n+    \"\"\"Test class property decorator behavior.\"\"\"\n+    from sphinx.util.classproperty import classproperty, class_property\n+\n+    class TestClass:\n+        @class_property\n+        def prop1(cls):\n+            \"\"\"This is prop1.\"\"\"\n+            return 42\n+\n+    # Check decorator structure\n+    descriptor = TestClass.__dict__['prop1']\n+    print(f\"\\nClass property structures:\")\n+    print(f\"Descriptor: {descriptor}\")\n+    print(f\"Descriptor type: {type(descriptor)}\")\n+    print(f\"Descriptor doc: {descriptor.__doc__}\")\n+    \n+    # Check implementation details\n+    print(f\"\\nDescriptor attributes:\")\n+    for attr in dir(descriptor):\n+        if not attr.startswith('__') or attr == '__doc__':\n+            try:\n+                val = getattr(descriptor, attr)\n+                print(f\"{attr}: {val}\")\n+            except Exception as e:\n+                print(f\"{attr}: {e}\")\n+\n+    # Access data directly\n+    if hasattr(descriptor, '__func__'):\n+        print(f\"\\nUnderlying function:\")\n+        func = descriptor.__func__\n+        print(f\"Type: {type(func)}\")\n+        print(f\"Doc: {func.__doc__}\")\n+        # Look for more function details\n+        if hasattr(func, '__get__'):\n+            bound = func.__get__(None, TestClass)\n+            print(f\"After __get__: {bound}\")\n+            print(f\"After __get__ type: {type(bound)}\")\n+            print(f\"After __get__ doc: {bound.__doc__}\")\n+            \n+    # Try accessing property\n+    print(f\"\\nAccessing property:\")\n+    value = TestClass.prop1\n+    print(f\"Value: {value}\")\n+    print(f\"Value type: {type(value)}\")\n\\ No newline at end of file\ndiff --git a/tests/test_ext_autodoc_classproperty.py b/tests/test_ext_autodoc_classproperty.py\nnew file mode 100644\nindex 000000000..2f28da92f\n--- /dev/null\n+++ b/tests/test_ext_autodoc_classproperty.py\n@@ -0,0 +1,35 @@\n+\"\"\"\n+    Test cases for the autodoc extension with class properties.\n+\"\"\"\n+\n+from textwrap import dedent\n+\n+import pytest\n+\n+from sphinx.testing.fixtures import make_app, test_params  # noqa: F401\n+from sphinx.testing.path import path\n+\n+\*********************('html', testroot='ext-autodoc')\n+def test_class_property(app):\n+    text = dedent('''\\\n+    .. currentmodule:: target\n+\n+    .. autoclass:: Class\n+       :members:\n+    ''')\n+    app.env.temp_data['docname'] = 'class'\n+    actual = do_autodoc(app, 'module', 'target.Class', text)\n+\n+    assert list(actual) == [\n+        '',\n+        '.. py:class:: Class',\n+        '',\n+        '   A test class.',\n+        '',\n+        '   .. py:property:: classprop',\n+        '      :classmethod:',\n+        '',\n+        '      A class property docstring.',\n+        '',\n+    ]\n\\ No newline at end of file\n", "proposed_model_patches": []}