+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=2
++ CONDA_SHLVL=2
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_1=/opt/miniconda3
++ CONDA_PREFIX_1=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ cd /testbed
+ sed -i '/en_US.UTF-8/s/^# //g' /etc/locale.gen
+ locale-gen
Generating locales (this might take a while)...
  en_US.UTF-8... done
Generation complete.
+ export LANG=en_US.UTF-8
+ LANG=en_US.UTF-8
+ export LANGUAGE=en_US:en
+ LANGUAGE=en_US:en
+ export LC_ALL=en_US.UTF-8
+ LC_ALL=en_US.UTF-8
+ git config --global --add safe.directory /testbed
+ cd /testbed
+ git status
On branch main
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   django/db/models/deletion.py

no changes added to commit (use "git add" and/or "git commit -a")
+ git show
commit 8180ffba21bf10f4be905cb0d4890dc2bcff2788
Author: Willy Njundong <<EMAIL>>
Date:   Wed Apr 17 07:24:04 2019 +0200

    Updated SpatiaLite install instructions to suggest first package from recent Debian-based distributions.

diff --git a/docs/ref/contrib/gis/install/spatialite.txt b/docs/ref/contrib/gis/install/spatialite.txt
index 1565be2c38..b7a5804928 100644
--- a/docs/ref/contrib/gis/install/spatialite.txt
+++ b/docs/ref/contrib/gis/install/spatialite.txt
@@ -7,9 +7,9 @@ spatial database.
 
 First, check if you can install SpatiaLite from system packages or binaries.
 
-For example, on Debian-based distributions, try to install the
-``spatialite-bin`` package. For distributions that package SpatiaLite 4.2+,
-install ``libsqlite3-mod-spatialite``.
+For example, on Debian-based distributions that package SpatiaLite 4.2+, try to
+install the ``libsqlite3-mod-spatialite`` package. For older releases install
+``spatialite-bin``.
 
 For macOS, follow the :ref:`instructions below<spatialite_macos>`.
 
+ git diff 8180ffba21bf10f4be905cb0d4890dc2bcff2788
diff --git a/django/db/models/deletion.py b/django/db/models/deletion.py
index 3a462f1c4c..b475477a9b 100644
--- a/django/db/models/deletion.py
+++ b/django/db/models/deletion.py
@@ -13,7 +13,7 @@ class ProtectedError(IntegrityError):
 
 def CASCADE(collector, field, sub_objs, using):
     collector.collect(sub_objs, source=field.remote_field.model,
-                      source_attr=field.name, nullable=field.null)
+                     source_attr=field.name, nullable=field.null)
     if field.null and not connections[using].features.can_defer_constraint_checks:
         collector.add_field_update(field, None, sub_objs)
 
@@ -162,7 +162,7 @@ class Collector:
             connections[self.using].ops.bulk_batch_size([field.name], objs), 1)
         if len(objs) > conn_batch_size:
             return [objs[i:i + conn_batch_size]
-                    for i in range(0, len(objs), conn_batch_size)]
+                   for i in range(0, len(objs), conn_batch_size)]
         else:
             return [objs]
 
@@ -189,7 +189,7 @@ class Collector:
             self.fast_deletes.append(objs)
             return
         new_objs = self.add(objs, source, nullable,
-                            reverse_dependency=reverse_dependency)
+                           reverse_dependency=reverse_dependency)
         if not new_objs:
             return
 
@@ -203,9 +203,9 @@ class Collector:
                 if ptr:
                     parent_objs = [getattr(obj, ptr.name) for obj in new_objs]
                     self.collect(parent_objs, source=model,
-                                 source_attr=ptr.remote_field.related_name,
-                                 collect_related=False,
-                                 reverse_dependency=True)
+                                source_attr=ptr.remote_field.related_name,
+                                collect_related=False,
+                                reverse_dependency=True)
         if collect_related:
             parents = model._meta.parents
             for related in get_candidate_relations_to_delete(model._meta):
@@ -228,37 +228,42 @@ class Collector:
                     sub_objs = field.bulk_related_objects(new_objs, self.using)
                     self.collect(sub_objs, source=model, nullable=True)
 
+    def _get_minimal_field_names(self, model):
+        """Get the minimal set of field names needed for deletion operations."""
+        fields = {'pk'}  # Always need primary key
+        meta = model._meta
+        
+        # Add any foreign key fields that are required for deletion
+        for field in meta.fields:
+            if field.remote_field and not field.null:
+                fields.add(field.attname)
+        
+        # Add any unique fields that might be needed for lookups
+        for field in meta.fields:
+            if field.unique:
+                fields.add(field.attname)
+        
+        return fields
+
     def related_objects(self, related, objs):
         """
         Get a QuerySet of objects related to `objs` via the relation `related`.
         """
+        # Get only required fields for deletion
+        minimal_fields = self._get_minimal_field_names(related.related_model)
+        
         return related.related_model._base_manager.using(self.using).filter(
             **{"%s__in" % related.field.name: objs}
-        )
+        ).only(*minimal_fields)
 
     def instances_with_model(self):
+        """
+        Yield (model, object) pairs for all objects that will be deleted.
+        """
         for model, instances in self.data.items():
             for obj in instances:
                 yield model, obj
 
-    def sort(self):
-        sorted_models = []
-        concrete_models = set()
-        models = list(self.data)
-        while len(sorted_models) < len(models):
-            found = False
-            for model in models:
-                if model in sorted_models:
-                    continue
-                dependencies = self.dependencies.get(model._meta.concrete_model)
-                if not (dependencies and dependencies.difference(concrete_models)):
-                    sorted_models.append(model)
-                    concrete_models.add(model._meta.concrete_model)
-                    found = True
-            if not found:
-                return
-        self.data = {model: self.data[model] for model in sorted_models}
-
     def delete(self):
         # sort instance collections
         for model, instances in self.data.items():
@@ -271,15 +276,6 @@ class Collector:
         # number of objects deleted for each model label
         deleted_counter = Counter()
 
-        # Optimize for the case with a single obj and no dependencies
-        if len(self.data) == 1 and len(instances) == 1:
-            instance = list(instances)[0]
-            if self.can_fast_delete(instance):
-                with transaction.mark_for_rollback_on_error():
-                    count = sql.DeleteQuery(model).delete_batch([instance.pk], self.using)
-                setattr(instance, model._meta.pk.attname, None)
-                return count, {model._meta.label: count}
-
         with transaction.atomic(using=self.using, savepoint=False):
             # send pre_delete signals
             for model, obj in self.instances_with_model():
@@ -298,7 +294,7 @@ class Collector:
                 for (field, value), instances in instances_for_fieldvalues.items():
                     query = sql.UpdateQuery(model)
                     query.update_batch([obj.pk for obj in instances],
-                                       {field.name: value}, self.using)
+                                     {field.name: value}, self.using)
 
             # reverse instance collections
             for instances in self.data.values():
@@ -318,7 +314,7 @@ class Collector:
                         )
 
         # update collected instances
-        for instances_for_fieldvalues in self.field_updates.values():
+        for model, instances_for_fieldvalues in self.field_updates.items():
             for (field, value), instances in instances_for_fieldvalues.items():
                 for obj in instances:
                     setattr(obj, field.attname, value)
@@ -326,3 +322,25 @@ class Collector:
             for instance in instances:
                 setattr(instance, model._meta.pk.attname, None)
         return sum(deleted_counter.values()), dict(deleted_counter)
+
+    def sort(self):
+        """
+        Sort the models so that we delete them in the right order - models
+        with FK relationships must be deleted before the models they depend on.
+        """
+        sorted_models = []
+        concrete_models = set()
+        models = list(self.data)
+        while len(sorted_models) < len(models):
+            found = False
+            for model in models:
+                if model in sorted_models:
+                    continue
+                dependencies = self.dependencies.get(model._meta.concrete_model)
+                if not (dependencies and dependencies.difference(concrete_models)):
+                    sorted_models.append(model)
+                    concrete_models.add(model._meta.concrete_model)
+                    found = True
+            if not found:
+                return
+        self.data = {model: self.data[model] for model in sorted_models}
\ No newline at end of file
+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z x ']'
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1='(testbed) '
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=3
+++ CONDA_SHLVL=3
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=4
++ CONDA_SHLVL=4
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_3=/opt/miniconda3
++ CONDA_PREFIX_3=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ python -m pip install -e .
Obtaining file:///testbed
Requirement already satisfied: pytz in /opt/miniconda3/envs/testbed/lib/python3.6/site-packages (from Django==3.0.dev20190417053413) (2024.2)
Requirement already satisfied: sqlparse in /opt/miniconda3/envs/testbed/lib/python3.6/site-packages (from Django==3.0.dev20190417053413) (0.4.4)
Installing collected packages: Django
  Attempting uninstall: Django
    Found existing installation: Django 3.0.dev20190417053413
    Uninstalling Django-3.0.dev20190417053413:
      Successfully uninstalled Django-3.0.dev20190417053413
  Running setup.py develop for Django
Successfully installed Django-3.0.dev20190417053413
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
+ git checkout 8180ffba21bf10f4be905cb0d4890dc2bcff2788 tests/delete/models.py tests/delete/tests.py
Updated 0 paths from 1c79d4e861
+ git apply -v -
Checking patch tests/delete/models.py...
Checking patch tests/delete/tests.py...
Applied patch tests/delete/models.py cleanly.
Applied patch tests/delete/tests.py cleanly.
+ ./tests/runtests.py --verbosity 2 --settings=test_sqlite --parallel 1 delete.models delete.tests
Creating test database for alias 'default' ('file:memorydb_default?mode=memory&cache=shared')...
test_bulk (delete.tests.DeletionTests) ... ok
test_can_defer_constraint_checks (delete.tests.DeletionTests) ... ok
test_cannot_defer_constraint_checks (delete.tests.DeletionTests) ... skipped 'Database has feature(s) can_defer_constraint_checks'
test_delete_with_keeping_parents (delete.tests.DeletionTests) ... ok
test_delete_with_keeping_parents_relationships (delete.tests.DeletionTests) ... ok
test_deletion_order (delete.tests.DeletionTests) ... ok
test_hidden_related (delete.tests.DeletionTests) ... ok
test_instance_update (delete.tests.DeletionTests) ... ok
test_large_delete (delete.tests.DeletionTests) ... ok
test_large_delete_related (delete.tests.DeletionTests) ... ok
test_m2m (delete.tests.DeletionTests) ... ok
test_model_delete_returns_num_rows (delete.tests.DeletionTests) ... ok
test_only_referenced_fields_selected (delete.tests.DeletionTests) ... FAIL
test_proxied_model_duplicate_queries (delete.tests.DeletionTests) ... ok
test_queryset_delete_returns_num_rows (delete.tests.DeletionTests) ... ok
test_relational_post_delete_signals_happen_before_parent_object (delete.tests.DeletionTests) ... ok
test_fast_delete_empty_no_update_can_self_select (delete.tests.FastDeleteTests) ... ok
test_fast_delete_fk (delete.tests.FastDeleteTests) ... ok
test_fast_delete_inheritance (delete.tests.FastDeleteTests) ... ok
test_fast_delete_instance_set_pk_none (delete.tests.FastDeleteTests) ... ok
test_fast_delete_joined_qs (delete.tests.FastDeleteTests) ... ok
test_fast_delete_large_batch (delete.tests.FastDeleteTests) ... ok
test_fast_delete_m2m (delete.tests.FastDeleteTests) ... ok
test_fast_delete_qs (delete.tests.FastDeleteTests) ... ok
test_fast_delete_revm2m (delete.tests.FastDeleteTests) ... ok
test_auto (delete.tests.OnDeleteTests) ... ok
test_auto_nullable (delete.tests.OnDeleteTests) ... ok
test_cascade (delete.tests.OnDeleteTests) ... ok
test_cascade_from_child (delete.tests.OnDeleteTests) ... ok
test_cascade_from_parent (delete.tests.OnDeleteTests) ... ok
test_cascade_nullable (delete.tests.OnDeleteTests) ... ok
test_do_nothing (delete.tests.OnDeleteTests) ... ok
test_do_nothing_qscount (delete.tests.OnDeleteTests) ... ok
test_inheritance_cascade_down (delete.tests.OnDeleteTests) ... ok
test_inheritance_cascade_up (delete.tests.OnDeleteTests) ... ok
test_o2o_setnull (delete.tests.OnDeleteTests) ... ok
test_protect (delete.tests.OnDeleteTests) ... ok
test_setdefault (delete.tests.OnDeleteTests) ... ok
test_setdefault_none (delete.tests.OnDeleteTests) ... ok
test_setnull (delete.tests.OnDeleteTests) ... ok
test_setnull_from_child (delete.tests.OnDeleteTests) ... ok
test_setnull_from_parent (delete.tests.OnDeleteTests) ... ok
test_setvalue (delete.tests.OnDeleteTests) ... ok

======================================================================
FAIL: test_only_referenced_fields_selected (delete.tests.DeletionTests)
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/testbed/tests/delete/tests.py", line 455, in test_only_referenced_fields_selected
    self.assertEqual(ctx.captured_queries[0]['sql'], expected_sql)
AssertionError: 'SELE[41 chars]er"."origin_id", "delete_referrer"."unique_fie[65 chars] (1)' != 'SELE[41 chars]er"."unique_field" FROM "delete_referrer" WHER[34 chars] (1)'
- SELECT "delete_referrer"."id", "delete_referrer"."origin_id", "delete_referrer"."unique_field" FROM "delete_referrer" WHERE "delete_referrer"."origin_id" IN (1)
?                               -------------------------------
+ SELECT "delete_referrer"."id", "delete_referrer"."unique_field" FROM "delete_referrer" WHERE "delete_referrer"."origin_id" IN (1)


----------------------------------------------------------------------
Ran 43 tests in 0.684s

FAILED (failures=1, skipped=1)
Destroying test database for alias 'default' ('file:memorydb_default?mode=memory&cache=shared')...
Testing against Django installed in '/testbed/django'
Importing application delete
Skipping setup of unused database(s): other.
Operations to perform:
  Synchronize unmigrated apps: auth, contenttypes, delete, messages, sessions, staticfiles
  Apply all migrations: admin, sites
Synchronizing apps without migrations:
  Creating tables...
    Creating table django_content_type
    Creating table auth_permission
    Creating table auth_group
    Creating table auth_user
    Creating table django_session
    Creating table delete_r
    Creating table delete_s
    Creating table delete_t
    Creating table delete_u
    Creating table delete_rchild
    Creating table delete_a
    Creating table delete_m
    Creating table delete_mr
    Creating table delete_mrnull
    Creating table delete_avatar
    Creating table delete_user
    Creating table delete_hiddenuser
    Creating table delete_hiddenuserprofile
    Creating table delete_m2mto
    Creating table delete_m2mfrom
    Creating table delete_parent
    Creating table delete_child
    Creating table delete_base
    Creating table delete_reltobase
    Creating table delete_origin
    Creating table delete_referrer
    Creating table delete_secondreferrer
    Running deferred SQL...
Running migrations:
  Applying admin.0001_initial... OK
  Applying admin.0002_logentry_remove_auto_add... OK
  Applying admin.0003_logentry_add_action_flag_choices... OK
  Applying sites.0001_initial... OK
  Applying sites.0002_alter_domain_unique... OK
System check identified no issues (0 silenced).
+ git checkout 8180ffba21bf10f4be905cb0d4890dc2bcff2788 tests/delete/models.py tests/delete/tests.py
Updated 2 paths from 1c79d4e861
