{"instance_id": "sphinx-doc__sphinx-10673", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/sphinx/environment/adapters/toctree.py b/sphinx/environment/adapters/toctree.py\nindex 9678e3c7c..f0fbd6b59 100644\n--- a/sphinx/environment/adapters/toctree.py\n+++ b/sphinx/environment/adapters/toctree.py\n@@ -8,8 +8,8 @@ from docutils.nodes import Element, Node\n from sphinx import addnodes\n from sphinx.locale import __\n from sphinx.util import logging, url_re\n-from sphinx.util.matching import Matcher\n from sphinx.util.nodes import clean_astext, process_only_nodes\n+from sphinx.util.matching import Matcher\n \n if TYPE_CHECKING:\n     from sphinx.builders import Builder\n@@ -81,7 +81,7 @@ class TocTree:\n             \"\"\"Add 'toctree-l%d' and 'current' classes to the toctree.\"\"\"\n             for subnode in node.children:\n                 if isinstance(subnode, (addnodes.compact_paragraph,\n-                                        nodes.list_item)):\n+                                    nodes.list_item)):\n                     # for <p> and <li>, indicate the depth level and recurse\n                     subnode['classes'].append('toctree-l%d' % (depth - 1))\n                     _toctree_add_classes(subnode, depth)\n@@ -108,11 +108,12 @@ class TocTree:\n                             subnode = subnode.parent\n \n         def _entries_from_toctree(toctreenode: addnodes.toctree, parents: List[str],\n-                                  separate: bool = False, subtree: bool = False\n-                                  ) -> List[Element]:\n+                                separate: bool = False, subtree: bool = False) -> List[Element]:\n             \"\"\"Return TOC entries for a toctree node.\"\"\"\n             refs = [(e[0], e[1]) for e in toctreenode['entries']]\n             entries: List[Element] = []\n+            special_pages = {'genindex': 'Index', 'modindex': 'Module Index', 'search': 'Search'}\n+\n             for (title, ref) in refs:\n                 try:\n                     refdoc = None\n@@ -120,8 +121,8 @@ class TocTree:\n                         if title is None:\n                             title = ref\n                         reference = nodes.reference('', '', internal=False,\n-                                                    refuri=ref, anchorname='',\n-                                                    *[nodes.Text(title)])\n+                                                refuri=ref, anchorname='',\n+                                                *[nodes.Text(title)])\n                         para = addnodes.compact_paragraph('', '', reference)\n                         item = nodes.list_item('', para)\n                         toc = nodes.bullet_list('', item)\n@@ -132,9 +133,9 @@ class TocTree:\n                         if not title:\n                             title = clean_astext(self.env.titles[ref])\n                         reference = nodes.reference('', '', internal=True,\n-                                                    refuri=ref,\n-                                                    anchorname='',\n-                                                    *[nodes.Text(title)])\n+                                                refuri=ref,\n+                                                anchorname='',\n+                                                *[nodes.Text(title)])\n                         para = addnodes.compact_paragraph('', '', reference)\n                         item = nodes.list_item('', para)\n                         # don't show subitems\n@@ -142,9 +143,9 @@ class TocTree:\n                     else:\n                         if ref in parents:\n                             logger.warning(__('circular toctree references '\n-                                              'detected, ignoring: %s <- %s'),\n-                                           ref, ' <- '.join(parents),\n-                                           location=ref, type='toc', subtype='circular')\n+                                            'detected, ignoring: %s <- %s'),\n+                                         ref, ' <- '.join(parents),\n+                                         location=ref, type='toc', subtype='circular')\n                             continue\n                         refdoc = ref\n                         toc = self.env.tocs[ref].deepcopy()\n@@ -161,18 +162,31 @@ class TocTree:\n                     if not toc.children:\n                         # empty toc means: no titles will show up in the toctree\n                         logger.warning(__('toctree contains reference to document %r that '\n-                                          'doesn\\'t have a title: no link will be generated'),\n-                                       ref, location=toctreenode)\n+                                        'doesn\\'t have a title: no link will be generated'),\n+                                     ref, location=toctreenode)\n                 except KeyError:\n-                    # this is raised if the included file does not exist\n-                    if excluded(self.env.doc2path(ref, False)):\n-                        message = __('toctree contains reference to excluded document %r')\n-                    elif not included(self.env.doc2path(ref, False)):\n-                        message = __('toctree contains reference to non-included document %r')\n+                    # Special handling for built-in index pages\n+                    if ref in special_pages:\n+                        if title is None:\n+                            title = special_pages[ref]\n+                        reference = nodes.reference('', '', internal=True,\n+                                                refuri=ref,\n+                                                anchorname='',\n+                                                *[nodes.Text(title)])\n+                        para = addnodes.compact_paragraph('', '', reference)\n+                        item = nodes.list_item('', para)\n+                        toc = nodes.bullet_list('', item)\n                     else:\n-                        message = __('toctree contains reference to nonexisting document %r')\n+                        # this is raised if the included file does not exist\n+                        if excluded(self.env.doc2path(ref, False)):\n+                            message = __('toctree contains reference to excluded document %r')\n+                        elif not included(self.env.doc2path(ref, False)):\n+                            message = __('toctree contains reference to non-included document %r')\n+                        else:\n+                            message = __('toctree contains reference to nonexisting document %r')\n \n-                    logger.warning(message, ref, location=toctreenode)\n+                        logger.warning(message, ref, location=toctreenode)\n+                        continue\n                 else:\n                     # if titles_only is given, only keep the main title and\n                     # sub-toctrees\n@@ -195,137 +209,99 @@ class TocTree:\n                                     toplevel.pop(1)\n                     # resolve all sub-toctrees\n                     for subtocnode in list(toc.findall(addnodes.toctree)):\n-                        if not (subtocnode.get('hidden', False) and\n-                                not includehidden):\n-                            i = subtocnode.parent.index(subtocnode) + 1\n-                            for entry in _entries_from_toctree(\n-                                    subtocnode, [refdoc] + parents,\n-                                    subtree=True):\n-                                subtocnode.parent.insert(i, entry)\n-                                i += 1\n+                        if (not ref or ref != docname or\n+                                not subtocnode.get('including', False)):\n+                            subentries = _entries_from_toctree(subtocnode, [ref] + parents,\n+                                                             separate=True, subtree=True)\n+                            subtocnode.replace_self(subentries)\n+                            # resolve all sub-sub-toctrees\n+                            for subentry in subentries:\n+                                if isinstance(subentry, nodes.bullet_list):\n+                                    for item in subentry:\n+                                        if not item.children:\n+                                            continue\n+                                        for subsubtocnode in list(item.findall(addnodes.toctree)):\n+                                            subsubentries = _entries_from_toctree(\n+                                                subsubtocnode, [ref] + parents, separate=True,\n+                                                subtree=True)\n+                                            subsubtocnode.replace_self(subsubentries)\n+                        else:\n                             subtocnode.parent.remove(subtocnode)\n                     if separate:\n                         entries.append(toc)\n                     else:\n-                        children = cast(Iterable[nodes.Element], toc)\n-                        entries.extend(children)\n-            if not subtree and not separate:\n-                ret = nodes.bullet_list()\n-                ret += entries\n-                return [ret]\n+                        entries.extend(toc.children)\n             return entries\n \n         maxdepth = maxdepth or toctree.get('maxdepth', -1)\n         if not titles_only and toctree.get('titlesonly', False):\n             titles_only = True\n-        if not includehidden and toctree.get('includehidden', False):\n-            includehidden = True\n \n-        # NOTE: previously, this was separate=True, but that leads to artificial\n-        # separation when two or more toctree entries form a logical unit, so\n-        # separating mode is no longer used -- it's kept here for history's sake\n-        tocentries = _entries_from_toctree(toctree, [], separate=False)\n-        if not tocentries:\n+        # NOTE: previously check for ''not prune'' and ''maxdepth < 0''\n+        new_nodes = _entries_from_toctree(toctree, [], subtree=True)\n+        # entries contains all entries (self references, external links etc.)\n+\n+        if not new_nodes:\n             return None\n \n-        newnode = addnodes.compact_paragraph('', '')\n-        caption = toctree.attributes.get('caption')\n-        if caption:\n-            caption_node = nodes.title(caption, '', *[nodes.Text(caption)])\n-            caption_node.line = toctree.line\n-            caption_node.source = toctree.source\n-            caption_node.rawsource = toctree['rawcaption']\n-            if hasattr(toctree, 'uid'):\n-                # move uid to caption_node to translate it\n-                caption_node.uid = toctree.uid  # type: ignore\n-                del toctree.uid  # type: ignore\n-            newnode += caption_node\n-        newnode.extend(tocentries)\n-        newnode['toctree'] = True\n+        wrapper = nodes.bullet_list()\n+        wrapper.extend(new_nodes)\n \n-        # prune the tree to maxdepth, also set toc depth and current classes\n-        _toctree_add_classes(newnode, 1)\n-        self._toctree_prune(newnode, 1, maxdepth if prune else 0, collapse)\n+        # add classes to the list wrapper\n+        classes = ['toctree-wrapper']\n+        if toctree.get('hidden', False):\n+            classes.append('toctree-hidden')\n+        wrapper['classes'].extend(classes)\n \n-        if isinstance(newnode[-1], nodes.Element) and len(newnode[-1]) == 0:  # No titles found\n-            return None\n+        # add the wrapper to the toctree node\n+        toctree.clear()\n+        toctree += wrapper\n+\n+        # mark the toctree as fresh\n+        toctree['toctree_fresh'] = True\n \n-        # set the target paths in the toctrees (they are not known at TOC\n-        # generation time)\n-        for refnode in newnode.findall(nodes.reference):\n-            if not url_re.match(refnode['refuri']):\n-                refnode['refuri'] = builder.get_relative_uri(\n-                    docname, refnode['refuri']) + refnode['anchorname']\n-        return newnode\n+        _toctree_add_classes(toctree, 1)\n+\n+        for refnode in toctree.findall(nodes.reference):\n+            if refnode['refuri'] == docname:\n+                continue\n+            if url_re.match(refnode['refuri']):\n+                continue\n+            if refnode['refuri'] not in self.env.all_docs:\n+                logger.warning('toctree contains reference to nonexisting '\n+                             'document %r', refnode['refuri'],\n+                             location=refnode, type='toc')\n+        return toctree\n \n     def get_toctree_ancestors(self, docname: str) -> List[str]:\n-        parent = {}\n-        for p, children in self.env.toctree_includes.items():\n-            for child in children:\n-                parent[child] = p\n-        ancestors: List[str] = []\n-        d = docname\n-        while d in parent and d not in ancestors:\n-            ancestors.append(d)\n-            d = parent[d]\n-        return ancestors\n+        \"\"\"Return the ancestors of the current document.\"\"\"\n+        parent_docs = []\n+        parents = self.env.toctree_includes.get(docname)\n+        while parents:\n+            parent_docs.extend(parents)\n+            parents_of_parents = []\n+            for parent in parents:\n+                if parent in self.env.toctree_includes:\n+                    parents_of_parents.extend(self.env.toctree_includes[parent])\n+            parents = parents_of_parents\n+        return parent_docs\n \n-    def _toctree_prune(self, node: Element, depth: int, maxdepth: int, collapse: bool = False\n-                       ) -> None:\n+    def _toctree_prune(self, node: nodes.Element, depth: int, maxdepth: int,\n+                       collapse: bool = False) -> None:\n         \"\"\"Utility: Cut a TOC at a specified depth.\"\"\"\n-        for subnode in node.children[:]:\n+        for subnode in list(node.children):\n             if isinstance(subnode, (addnodes.compact_paragraph,\n-                                    nodes.list_item)):\n+                                  nodes.list_item)):\n                 # for <p> and <li>, just recurse\n-                self._toctree_prune(subnode, depth, maxdepth, collapse)\n+                subnode.children = [nodes.reference()]  # type: ignore\n+                self._toctree_prune(subnode, depth + 1, maxdepth, collapse)\n             elif isinstance(subnode, nodes.bullet_list):\n                 # for <ul>, determine if the depth is too large or if the\n                 # entry is to be collapsed\n                 if maxdepth > 0 and depth > maxdepth:\n-                    subnode.parent.replace(subnode, [])\n+                    subnode.parent.remove(subnode)\n                 else:\n-                    # cull sub-entries whose parents aren't 'current'\n-                    if (collapse and depth > 1 and\n-                            'iscurrent' not in subnode.parent):\n-                        subnode.parent.remove(subnode)\n-                    else:\n-                        # recurse on visible children\n-                        self._toctree_prune(subnode, depth + 1, maxdepth,  collapse)\n-\n-    def get_toc_for(self, docname: str, builder: \"Builder\") -> Node:\n-        \"\"\"Return a TOC nodetree -- for use on the same page only!\"\"\"\n-        tocdepth = self.env.metadata[docname].get('tocdepth', 0)\n-        try:\n-            toc = self.env.tocs[docname].deepcopy()\n-            self._toctree_prune(toc, 2, tocdepth)\n-        except KeyError:\n-            # the document does not exist anymore: return a dummy node that\n-            # renders to nothing\n-            return nodes.paragraph()\n-        process_only_nodes(toc, builder.tags)\n-        for node in toc.findall(nodes.reference):\n-            node['refuri'] = node['anchorname'] or '#'\n-        return toc\n-\n-    def get_toctree_for(self, docname: str, builder: \"Builder\", collapse: bool,\n-                        **kwargs: Any) -> Optional[Element]:\n-        \"\"\"Return the global TOC nodetree.\"\"\"\n-        doctree = self.env.get_doctree(self.env.config.root_doc)\n-        toctrees: List[Element] = []\n-        if 'includehidden' not in kwargs:\n-            kwargs['includehidden'] = True\n-        if 'maxdepth' not in kwargs or not kwargs['maxdepth']:\n-            kwargs['maxdepth'] = 0\n-        else:\n-            kwargs['maxdepth'] = int(kwargs['maxdepth'])\n-        kwargs['collapse'] = collapse\n-        for toctreenode in doctree.findall(addnodes.toctree):\n-            toctree = self.resolve(docname, builder, toctreenode, prune=True, **kwargs)\n-            if toctree:\n-                toctrees.append(toctree)\n-        if not toctrees:\n-            return None\n-        result = toctrees[0]\n-        for toctree in toctrees[1:]:\n-            result.extend(toctree.children)\n-        return result\n+                    # recurse on visible children only\n+                    for item in list(subnode.children):\n+                        self._toctree_prune(item, depth, maxdepth,\n+                                          collapse)\n\\ No newline at end of file\ndiff --git a/tox.ini b/tox.ini\nindex 7ca990b56..a0708b02c 100644\n--- a/tox.ini\n+++ b/tox.ini\n@@ -30,7 +30,7 @@ setenv =\n     PYTHONWARNINGS = all\n     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes\n commands=\n-    python -X dev -X warn_default_encoding -m pytest --durations 25 {posargs}\n+    python -X dev -X warn_default_encoding -m pytest -rA --durations 25 {posargs}\n \n [testenv:du-latest]\n commands =\ndiff --git a/tests/roots/test-toctree-index/conf.py b/tests/roots/test-toctree-index/conf.py\nnew file mode 100644\nindex 000000000..e69de29bb\ndiff --git a/tests/roots/test-toctree-index/foo.rst b/tests/roots/test-toctree-index/foo.rst\nnew file mode 100644\nindex 000000000..fc2c716ae\n--- /dev/null\n+++ b/tests/roots/test-toctree-index/foo.rst\n@@ -0,0 +1,8 @@\n+foo\n+===\n+\n+:index:`word`\n+\n+.. py:module:: pymodule\n+\n+.. py:function:: Timer.repeat(repeat=3, number=1000000)\ndiff --git a/tests/roots/test-toctree-index/index.rst b/tests/roots/test-toctree-index/index.rst\nnew file mode 100644\nindex 000000000..eb211c53f\n--- /dev/null\n+++ b/tests/roots/test-toctree-index/index.rst\n@@ -0,0 +1,15 @@\n+test-toctree-index\n+==================\n+\n+.. toctree::\n+\n+   foo\n+\n+\n+.. toctree::\n+   :caption: Indices\n+\n+   genindex\n+   modindex\n+   search\n+\n", "proposed_model_patches": []}