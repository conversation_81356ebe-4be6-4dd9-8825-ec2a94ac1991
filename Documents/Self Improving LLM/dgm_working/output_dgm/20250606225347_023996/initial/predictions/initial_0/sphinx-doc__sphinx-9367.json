{"instance_id": "sphinx-doc__sphinx-9367", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/setup.py b/setup.py\nindex 206ff4ad4..4bb5faeb1 100644\n--- a/setup.py\n+++ b/setup.py\n@@ -15,22 +15,22 @@ if sys.version_info < (3, 6):\n     sys.exit(1)\n \n install_requires = [\n-    'sphinxcontrib-applehelp',\n-    'sphinxcontrib-devhelp',\n+    'sphinxcontrib-applehelp<=1.0.7',\n+    'sphinxcontrib-devhelp<=1.0.5',\n     'sphinxcontrib-jsmath',\n-    'sphinxcontrib-htmlhelp',\n-    'sphinxcontrib-serializinghtml',\n-    'sphinxcontrib-qthelp',\n-    'Jinja2>=2.3',\n+    'sphinxcontrib-htmlhelp<=2.0.4',\n+    'sphinxcontrib-serializinghtml<=1.1.9',\n+    'sphinxcontrib-qthelp<=1.0.6',\n+    'Jinja2<3.0',\n     'Pygments>=2.0',\n     'docutils>=0.14,<0.18',\n     'snowballstemmer>=1.1',\n     'babel>=1.3',\n-    'alabaster>=0.7,<0.8',\n+    'alabaster>=0.7,<0.7.12',\n     'imagesize',\n     'requests>=2.5.0',\n     'setuptools',\n-    'packaging',\n+    'packaging', 'markupsafe<=2.0.1',\n ]\n \n extras_require = {\ndiff --git a/sphinx/pycode/ast.py b/sphinx/pycode/ast.py\nindex f541ec0a9..8c4ad98e8 100644\n--- a/sphinx/pycode/ast.py\n+++ b/sphinx/pycode/ast.py\n@@ -213,10 +213,12 @@ class _UnparseVisitor(ast.NodeVisitor):\n         return \"%s %s\" % (self.visit(node.op), self.visit(node.operand))\n \n     def visit_Tuple(self, node: ast.Tuple) -> str:\n-        if node.elts:\n-            return \"(\" + \", \".join(self.visit(e) for e in node.elts) + \")\"\n-        else:\n+        if not node.elts:\n             return \"()\"\n+        items = [self.visit(e) for e in node.elts]\n+        if len(items) == 1:\n+            return \"(%s,)\" % items[0]\n+        return \"(\" + \", \".join(items) + \")\"\n \n     if sys.version_info < (3, 8):\n         # these ast nodes were deprecated in python 3.8\n@@ -236,4 +238,4 @@ class _UnparseVisitor(ast.NodeVisitor):\n             return repr(node.s)\n \n     def generic_visit(self, node):\n-        raise NotImplementedError('Unable to parse %s object' % type(node).__name__)\n+        raise NotImplementedError('Unable to parse %s object' % type(node).__name__)\n\\ No newline at end of file\ndiff --git a/tests/test_pycode_ast.py b/tests/test_pycode_ast.py\nindex e80062351..17cba981a 100644\n--- a/tests/test_pycode_ast.py\n+++ b/tests/test_pycode_ast.py\n@@ -53,8 +53,9 @@ from sphinx.pycode import ast\n     (\"+ a\", \"+ a\"),                             # UAdd\n     (\"- 1\", \"- 1\"),                             # UnaryOp\n     (\"- a\", \"- a\"),                             # USub\n-    (\"(1, 2, 3)\", \"(1, 2, 3)\"),                   # Tuple\n+    (\"(1, 2, 3)\", \"(1, 2, 3)\"),                # Tuple\n     (\"()\", \"()\"),                               # Tuple (empty)\n+    (\"(1,)\", \"(1,)\"),                           # Tuple (single element)\n ])\n def test_unparse(source, expected):\n     module = ast.parse(source)\n@@ -74,4 +75,4 @@ def test_unparse_None():\n ])\n def test_unparse_py38(source, expected):\n     module = ast.parse(source)\n-    assert ast.unparse(module.body[0].value, source) == expected\n+    assert ast.unparse(module.body[0].value, source) == expected\n\\ No newline at end of file\ndiff --git a/tox.ini b/tox.ini\nindex a363e187f..3b8bc12d0 100644\n--- a/tox.ini\n+++ b/tox.ini\n@@ -27,7 +27,7 @@ setenv =\n     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils,ignore::DeprecationWarning:pip._vendor.packaging.version\n     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes\n commands=\n-    python -X dev -m pytest --durations 25 {posargs}\n+    python -X dev -m pytest -rA --durations 25 {posargs}\n \n [testenv:flake8]\n basepython = python3\n", "proposed_model_patches": []}