{"django__django-13346": {"patch_is_None": false, "patch_exists": true, "patch_successfully_applied": true, "resolved": false, "tests_status": {"FAIL_TO_PASS": {"success": [], "failure": ["test_key_in (model_fields.test_jsonfield.TestQuerying)", "test_key_iregex (model_fields.test_jsonfield.TestQuerying)"]}, "PASS_TO_PASS": {"success": [], "failure": ["test_formfield (model_fields.test_jsonfield.TestFormField)", "test_formfield_custom_encoder_decoder (model_fields.test_jsonfield.TestFormField)", "test_custom_encoder (model_fields.test_jsonfield.TestValidation)", "test_invalid_decoder (model_fields.test_jsonfield.TestValidation)", "test_invalid_encoder (model_fields.test_jsonfield.TestValidation)", "test_validation_error (model_fields.test_jsonfield.TestValidation)", "test_deconstruct (model_fields.test_jsonfield.TestMethods)", "test_deconstruct_custom_encoder_decoder (model_fields.test_jsonfield.TestMethods)", "test_get_transforms (model_fields.test_jsonfield.TestMethods)", "test_key_transform_text_lookup_mixin_non_key_transform (model_fields.test_jsonfield.TestMethods)", "test_custom_encoder_decoder (model_fields.test_jsonfield.JSONFieldTests)", "test_db_check_constraints (model_fields.test_jsonfield.JSONFieldTests)", "test_invalid_value (model_fields.test_jsonfield.JSONFieldTests)", "test_dumping (model_fields.test_jsonfield.TestSerialization)", "test_loading (model_fields.test_jsonfield.TestSerialization)", "test_xml_serialization (model_fields.test_jsonfield.TestSerialization)", "test_dict (model_fields.test_jsonfield.TestSaveLoad)", "test_json_null_different_from_sql_null (model_fields.test_jsonfield.TestSaveLoad)", "test_list (model_fields.test_jsonfield.TestSaveLoad)", "test_null (model_fields.test_jsonfield.TestSaveLoad)", "test_primitives (model_fields.test_jsonfield.TestSaveLoad)", "test_realistic_object (model_fields.test_jsonfield.TestSaveLoad)", "test_contained_by_unsupported (model_fields.test_jsonfield.TestQuerying)", "test_contains_unsupported (model_fields.test_jsonfield.TestQuerying)", "test_deep_lookup_array (model_fields.test_jsonfield.TestQuerying)", "test_deep_lookup_mixed (model_fields.test_jsonfield.TestQuerying)", "test_deep_lookup_objs (model_fields.test_jsonfield.TestQuerying)", "test_deep_lookup_transform (model_fields.test_jsonfield.TestQuerying)", "test_deep_values (model_fields.test_jsonfield.TestQuerying)", "test_exact (model_fields.test_jsonfield.TestQuerying)", "test_exact_complex (model_fields.test_jsonfield.TestQuerying)", "test_has_any_keys (model_fields.test_jsonfield.TestQuerying)", "test_has_key (model_fields.test_jsonfield.TestQuerying)", "test_has_key_deep (model_fields.test_jsonfield.TestQuerying)", "test_has_key_list (model_fields.test_jsonfield.TestQuerying)", "test_has_key_null_value (model_fields.test_jsonfield.TestQuerying)", "test_has_keys (model_fields.test_jsonfield.TestQuerying)", "test_isnull (model_fields.test_jsonfield.TestQuerying)", "test_isnull_key (model_fields.test_jsonfield.TestQuerying)", "test_isnull_key_or_none (model_fields.test_jsonfield.TestQuerying)", "test_key_endswith (model_fields.test_jsonfield.TestQuerying)", "test_key_escape (model_fields.test_jsonfield.TestQuerying)", "test_key_icontains (model_fields.test_jsonfield.TestQuerying)", "test_key_iendswith (model_fields.test_jsonfield.TestQuerying)", "test_key_iexact (model_fields.test_jsonfield.TestQuerying)", "test_key_istartswith (model_fields.test_jsonfield.TestQuerying)", "test_key_regex (model_fields.test_jsonfield.TestQuerying)", "test_key_sql_injection_escape (model_fields.test_jsonfield.TestQuerying)", "test_key_startswith (model_fields.test_jsonfield.TestQuerying)", "test_key_transform_expression (model_fields.test_jsonfield.TestQuerying)", "test_key_transform_raw_expression (model_fields.test_jsonfield.TestQuerying)", "test_lookups_with_key_transform (model_fields.test_jsonfield.TestQuerying)", "test_nested_key_transform_expression (model_fields.test_jsonfield.TestQuerying)", "test_nested_key_transform_raw_expression (model_fields.test_jsonfield.TestQuerying)", "test_none_key (model_fields.test_jsonfield.TestQuerying)", "test_none_key_and_exact_lookup (model_fields.test_jsonfield.TestQuerying)", "test_none_key_exclude (model_fields.test_jsonfield.TestQuerying)", "test_obj_subquery_lookup (model_fields.test_jsonfield.TestQuerying)", "test_ordering_by_transform (model_fields.test_jsonfield.TestQuerying)", "test_ordering_grouping_by_count (model_fields.test_jsonfield.TestQuerying)", "test_ordering_grouping_by_key_transform (model_fields.test_jsonfield.TestQuerying)", "test_shallow_list_lookup (model_fields.test_jsonfield.TestQuerying)", "test_shallow_lookup_obj_target (model_fields.test_jsonfield.TestQuerying)", "test_shallow_obj_lookup (model_fields.test_jsonfield.TestQuerying)", "test_usage_in_subquery (model_fields.test_jsonfield.TestQuerying)"]}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}}}