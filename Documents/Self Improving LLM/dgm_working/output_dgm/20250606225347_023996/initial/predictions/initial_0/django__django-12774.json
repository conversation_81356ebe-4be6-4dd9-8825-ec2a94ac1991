{"instance_id": "django__django-12774", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/django/db/models/query.py b/django/db/models/query.py\nindex d9c9b0db04..180f4a41fc 100644\n--- a/django/db/models/query.py\n+++ b/django/db/models/query.py\n@@ -12,17 +12,18 @@ from itertools import chain\n from django.conf import settings\n from django.core import exceptions\n from django.db import (\n-    DJANGO_VERSION_PICKLE_KEY, IntegrityError, NotSupportedError, connections,\n-    router, transaction,\n+    DJANGO_VERSION_PICKLE_KEY, IntegrityError, connections, router,\n+    transaction,\n )\n-from django.db.models import <PERSON>Field, DateField, DateTimeField, sql\n+from django.db.models import DateField, DateTimeField, sql\n from django.db.models.constants import LOOKUP_SEP\n from django.db.models.deletion import Collector\n from django.db.models.expressions import Case, Expression, F, Value, When\n+from django.db.models.fields import AutoField\n from django.db.models.functions import Cast, Trunc\n-from django.db.models.query_utils import FilteredRelation, Q\n+from django.db.models.query_utils import FilteredRelation, InvalidQuery, Q\n from django.db.models.sql.constants import CURSOR, GET_ITERATOR_CHUNK_SIZE\n-from django.db.models.utils import resolve_callables\n+from django.db.utils import NotSupportedError\n from django.utils import timezone\n from django.utils.functional import cached_property, partition\n from django.utils.version import get_version\n@@ -33,6 +34,9 @@ MAX_GET_RESULTS = 21\n # The maximum number of items to display in a QuerySet.__repr__\n REPR_OUTPUT_SIZE = 20\n \n+# Pull into this namespace for backwards compatibility.\n+EmptyResultSet = sql.EmptyResultSet\n+\n \n class BaseIterable:\n     def __init__(self, queryset, chunked_fetch=False, chunk_size=GET_ITERATOR_CHUNK_SIZE):\n@@ -188,7 +192,7 @@ class QuerySet:\n         self.model = model\n         self._db = using\n         self._hints = hints or {}\n-        self._query = query or sql.Query(self.model)\n+        self.query = query or sql.Query(self.model)\n         self._result_cache = None\n         self._sticky_filter = False\n         self._for_write = False\n@@ -197,20 +201,6 @@ class QuerySet:\n         self._known_related_objects = {}  # {rel_field: {pk: rel_obj}}\n         self._iterable_class = ModelIterable\n         self._fields = None\n-        self._defer_next_filter = False\n-        self._deferred_filter = None\n-\n-    @property\n-    def query(self):\n-        if self._deferred_filter:\n-            negate, args, kwargs = self._deferred_filter\n-            self._filter_or_exclude_inplace(negate, *args, **kwargs)\n-            self._deferred_filter = None\n-        return self._query\n-\n-    @query.setter\n-    def query(self, value):\n-        self._query = value\n \n     def as_manager(cls):\n         # Address the circular dependency between `Queryset` and `Manager`.\n@@ -323,9 +313,6 @@ class QuerySet:\n         qs._fetch_all()\n         return qs._result_cache[0]\n \n-    def __class_getitem__(cls, *args, **kwargs):\n-        return cls\n-\n     def __and__(self, other):\n         self._merge_sanity_check(other)\n         if isinstance(other, EmptyQuerySet):\n@@ -593,8 +580,8 @@ class QuerySet:\n                 obj, created = self._create_object_from_params(kwargs, params, lock=True)\n                 if created:\n                     return obj, created\n-            for k, v in resolve_callables(defaults):\n-                setattr(obj, k, v)\n+            for k, v in defaults.items():\n+                setattr(obj, k, v() if callable(v) else v)\n             obj.save(using=self.db)\n         return obj, False\n \n@@ -605,16 +592,16 @@ class QuerySet:\n         \"\"\"\n         try:\n             with transaction.atomic(using=self.db):\n-                params = dict(resolve_callables(params))\n+                params = {k: v() if callable(v) else v for k, v in params.items()}\n                 obj = self.create(**params)\n             return obj, True\n-        except IntegrityError:\n+        except IntegrityError as e:\n             try:\n                 qs = self.select_for_update() if lock else self\n                 return qs.get(**lookup), False\n             except self.model.DoesNotExist:\n                 pass\n-            raise\n+            raise e\n \n     def _extract_model_params(self, defaults, **kwargs):\n         \"\"\"\n@@ -712,7 +699,6 @@ class QuerySet:\n \n     def delete(self):\n         \"\"\"Delete the records in the current QuerySet.\"\"\"\n-        self._not_support_combined_queries('delete')\n         assert not self.query.is_sliced, \\\n             \"Cannot use 'limit' or 'offset' with delete.\"\n \n@@ -747,13 +733,7 @@ class QuerySet:\n         Delete objects found from the given queryset in single direct SQL\n         query. No signals are sent and there is no protection for cascades.\n         \"\"\"\n-        query = self.query.clone()\n-        query.__class__ = sql.DeleteQuery\n-        cursor = query.get_compiler(using).execute_sql(CURSOR)\n-        if cursor:\n-            with cursor:\n-                return cursor.rowcount\n-        return 0\n+        return sql.DeleteQuery(self.model).delete_qs(self, using)\n     _raw_delete.alters_data = True\n \n     def update(self, **kwargs):\n@@ -761,7 +741,6 @@ class QuerySet:\n         Update all elements in the current QuerySet, setting all the given\n         fields to the appropriate values.\n         \"\"\"\n-        self._not_support_combined_queries('update')\n         assert not self.query.is_sliced, \\\n             \"Cannot update a query once a slice has been taken.\"\n         self._for_write = True\n@@ -878,7 +857,7 @@ class QuerySet:\n             'datefield', flat=True\n         ).distinct().filter(plain_field__isnull=False).order_by(('-' if order == 'DESC' else '') + 'datefield')\n \n-    def datetimes(self, field_name, kind, order='ASC', tzinfo=None, is_dst=None):\n+    def datetimes(self, field_name, kind, order='ASC', tzinfo=None):\n         \"\"\"\n         Return a list of datetime objects representing all available\n         datetimes for the given field_name, scoped to 'kind'.\n@@ -893,13 +872,7 @@ class QuerySet:\n         else:\n             tzinfo = None\n         return self.annotate(\n-            datetimefield=Trunc(\n-                field_name,\n-                kind,\n-                output_field=DateTimeField(),\n-                tzinfo=tzinfo,\n-                is_dst=is_dst,\n-            ),\n+            datetimefield=Trunc(field_name, kind, output_field=DateTimeField(), tzinfo=tzinfo),\n             plain_field=F(field_name)\n         ).values_list(\n             'datetimefield', flat=True\n@@ -944,18 +917,11 @@ class QuerySet:\n                 \"Cannot filter a query once a slice has been taken.\"\n \n         clone = self._chain()\n-        if self._defer_next_filter:\n-            self._defer_next_filter = False\n-            clone._deferred_filter = negate, args, kwargs\n-        else:\n-            clone._filter_or_exclude_inplace(negate, *args, **kwargs)\n-        return clone\n-\n-    def _filter_or_exclude_inplace(self, negate, *args, **kwargs):\n         if negate:\n-            self._query.add_q(~Q(*args, **kwargs))\n+            clone.query.add_q(~Q(*args, **kwargs))\n         else:\n-            self._query.add_q(Q(*args, **kwargs))\n+            clone.query.add_q(Q(*args, **kwargs))\n+        return clone\n \n     def complex_filter(self, filter_obj):\n         \"\"\"\n@@ -972,7 +938,7 @@ class QuerySet:\n             clone.query.add_q(filter_obj)\n             return clone\n         else:\n-            return self._filter_or_exclude(False, **filter_obj)\n+            return self._filter_or_exclude(None, **filter_obj)\n \n     def _combinator_query(self, combinator, *other_qs, all=False):\n         # Clone the query to inherit the select list and everything\n@@ -1246,17 +1212,20 @@ class QuerySet:\n         if ignore_conflicts and not connections[self.db].features.supports_ignore_conflicts:\n             raise NotSupportedError('This database backend does not support ignoring conflicts.')\n         ops = connections[self.db].ops\n-        max_batch_size = max(ops.bulk_batch_size(fields, objs), 1)\n-        batch_size = min(batch_size, max_batch_size) if batch_size else max_batch_size\n+        batch_size = (batch_size or max(ops.bulk_batch_size(fields, objs), 1))\n         inserted_rows = []\n         bulk_return = connections[self.db].features.can_return_rows_from_bulk_insert\n         for item in [objs[i:i + batch_size] for i in range(0, len(objs), batch_size)]:\n             if bulk_return and not ignore_conflicts:\n-                inserted_rows.extend(self._insert(\n+                inserted_columns = self._insert(\n                     item, fields=fields, using=self.db,\n                     returning_fields=self.model._meta.db_returning_fields,\n                     ignore_conflicts=ignore_conflicts,\n-                ))\n+                )\n+                if isinstance(inserted_columns, list):\n+                    inserted_rows.extend(inserted_columns)\n+                else:\n+                    inserted_rows.append(inserted_columns)\n             else:\n                 self._insert(item, fields=fields, using=self.db, ignore_conflicts=ignore_conflicts)\n         return inserted_rows\n@@ -1464,9 +1433,7 @@ class RawQuerySet:\n         try:\n             model_init_names, model_init_pos, annotation_fields = self.resolve_model_init_order()\n             if self.model._meta.pk.attname not in model_init_names:\n-                raise exceptions.FieldDoesNotExist(\n-                    'Raw query must include the primary key'\n-                )\n+                raise InvalidQuery('Raw query must include the primary key')\n             model_cls = self.model\n             fields = [self.model_fields.get(c) for c in self.columns]\n             converters = compiler.get_converters([\n@@ -1542,16 +1509,8 @@ class Prefetch:\n         self.prefetch_through = lookup\n         # `prefetch_to` is the path to the attribute that stores the result.\n         self.prefetch_to = lookup\n-        if queryset is not None and (\n-            isinstance(queryset, RawQuerySet) or (\n-                hasattr(queryset, '_iterable_class') and\n-                not issubclass(queryset._iterable_class, ModelIterable)\n-            )\n-        ):\n-            raise ValueError(\n-                'Prefetch querysets cannot use raw(), values(), and '\n-                'values_list().'\n-            )\n+        if queryset is not None and not issubclass(queryset._iterable_class, ModelIterable):\n+            raise ValueError('Prefetch querysets cannot use values().')\n         if to_attr:\n             self.prefetch_to = LOOKUP_SEP.join(lookup.split(LOOKUP_SEP)[:-1] + [to_attr])\n \n@@ -1587,9 +1546,7 @@ class Prefetch:\n         return None\n \n     def __eq__(self, other):\n-        if not isinstance(other, Prefetch):\n-            return NotImplemented\n-        return self.prefetch_to == other.prefetch_to\n+        return isinstance(other, Prefetch) and self.prefetch_to == other.prefetch_to\n \n     def __hash__(self):\n         return hash((self.__class__, self.prefetch_to))\ndiff --git a/bulk_patch.py b/bulk_patch.py\nnew file mode 100644\nindex 0000000000..62364a7205\n--- /dev/null\n+++ b/bulk_patch.py\n@@ -0,0 +1,41 @@\n+import fileinput\n+import sys\n+\n+def change_in_bulk():\n+    inside_in_bulk = False\n+    skip_lines = 0\n+    new_lines = '''        assert not self.query.is_sliced, \\\\\n+            \"Cannot use 'limit' or 'offset' with in_bulk\"\n+\n+        opts = self.model._meta\n+        field = opts.get_field(field_name)\n+\n+        # Check if the field is unique either through unique=True or UniqueConstraint\n+        is_unique = (\n+            field.unique or \n+            any(\n+                isinstance(constraint, sql.UniqueConstraint) and \n+                len(constraint.fields) == 1 and\n+                constraint.fields[0] == field_name\n+                for constraint in opts.constraints\n+            )\n+        )\n+\n+        if field_name != 'pk' and not is_unique:\n+            raise ValueError(\"in_bulk()'s field_name must be a unique field but %r isn't.\" % field_name)'''.split('\\n')\n+\n+    with fileinput.FileInput('/testbed/django/db/models/query.py', inplace=True) as f:\n+        for line in f:\n+            if 'def in_bulk(' in line:\n+                inside_in_bulk = True\n+                print(line, end='')\n+            elif inside_in_bulk and any(x in line for x in [\"is_sliced\", \"field =\", \"if field_name != 'pk'\"]):\n+                if \"is_sliced\" in line:\n+                    print('\\n'.join(new_lines))\n+                    inside_in_bulk = False\n+                continue\n+            else:\n+                print(line, end='')\n+\n+if __name__ == '__main__':\n+    change_in_bulk()\n\\ No newline at end of file\ndiff --git a/django/db/models/query.py.bak b/django/db/models/query.py.bak\nnew file mode 100644\nindex 0000000000..180f4a41fc\n--- /dev/null\n+++ b/django/db/models/query.py.bak\n@@ -0,0 +1,1923 @@\n+\"\"\"\n+The main QuerySet implementation. This provides the public API for the ORM.\n+\"\"\"\n+\n+import copy\n+import operator\n+import warnings\n+from collections import namedtuple\n+from functools import lru_cache\n+from itertools import chain\n+\n+from django.conf import settings\n+from django.core import exceptions\n+from django.db import (\n+    DJANGO_VERSION_PICKLE_KEY, IntegrityError, connections, router,\n+    transaction,\n+)\n+from django.db.models import DateField, DateTimeField, sql\n+from django.db.models.constants import LOOKUP_SEP\n+from django.db.models.deletion import Collector\n+from django.db.models.expressions import Case, Expression, F, Value, When\n+from django.db.models.fields import AutoField\n+from django.db.models.functions import Cast, Trunc\n+from django.db.models.query_utils import FilteredRelation, InvalidQuery, Q\n+from django.db.models.sql.constants import CURSOR, GET_ITERATOR_CHUNK_SIZE\n+from django.db.utils import NotSupportedError\n+from django.utils import timezone\n+from django.utils.functional import cached_property, partition\n+from django.utils.version import get_version\n+\n+# The maximum number of results to fetch in a get() query.\n+MAX_GET_RESULTS = 21\n+\n+# The maximum number of items to display in a QuerySet.__repr__\n+REPR_OUTPUT_SIZE = 20\n+\n+# Pull into this namespace for backwards compatibility.\n+EmptyResultSet = sql.EmptyResultSet\n+\n+\n+class BaseIterable:\n+    def __init__(self, queryset, chunked_fetch=False, chunk_size=GET_ITERATOR_CHUNK_SIZE):\n+        self.queryset = queryset\n+        self.chunked_fetch = chunked_fetch\n+        self.chunk_size = chunk_size\n+\n+\n+class ModelIterable(BaseIterable):\n+    \"\"\"Iterable that yields a model instance for each row.\"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        db = queryset.db\n+        compiler = queryset.query.get_compiler(using=db)\n+        # Execute the query. This will also fill compiler.select, klass_info,\n+        # and annotations.\n+        results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)\n+        select, klass_info, annotation_col_map = (compiler.select, compiler.klass_info,\n+                                                  compiler.annotation_col_map)\n+        model_cls = klass_info['model']\n+        select_fields = klass_info['select_fields']\n+        model_fields_start, model_fields_end = select_fields[0], select_fields[-1] + 1\n+        init_list = [f[0].target.attname\n+                     for f in select[model_fields_start:model_fields_end]]\n+        related_populators = get_related_populators(klass_info, select, db)\n+        known_related_objects = [\n+            (field, related_objs, operator.attrgetter(*[\n+                field.attname\n+                if from_field == 'self' else\n+                queryset.model._meta.get_field(from_field).attname\n+                for from_field in field.from_fields\n+            ])) for field, related_objs in queryset._known_related_objects.items()\n+        ]\n+        for row in compiler.results_iter(results):\n+            obj = model_cls.from_db(db, init_list, row[model_fields_start:model_fields_end])\n+            for rel_populator in related_populators:\n+                rel_populator.populate(row, obj)\n+            if annotation_col_map:\n+                for attr_name, col_pos in annotation_col_map.items():\n+                    setattr(obj, attr_name, row[col_pos])\n+\n+            # Add the known related objects to the model.\n+            for field, rel_objs, rel_getter in known_related_objects:\n+                # Avoid overwriting objects loaded by, e.g., select_related().\n+                if field.is_cached(obj):\n+                    continue\n+                rel_obj_id = rel_getter(obj)\n+                try:\n+                    rel_obj = rel_objs[rel_obj_id]\n+                except KeyError:\n+                    pass  # May happen in qs1 | qs2 scenarios.\n+                else:\n+                    setattr(obj, field.name, rel_obj)\n+\n+            yield obj\n+\n+\n+class ValuesIterable(BaseIterable):\n+    \"\"\"\n+    Iterable returned by QuerySet.values() that yields a dict for each row.\n+    \"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        query = queryset.query\n+        compiler = query.get_compiler(queryset.db)\n+\n+        # extra(select=...) cols are always at the start of the row.\n+        names = [\n+            *query.extra_select,\n+            *query.values_select,\n+            *query.annotation_select,\n+        ]\n+        indexes = range(len(names))\n+        for row in compiler.results_iter(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size):\n+            yield {names[i]: row[i] for i in indexes}\n+\n+\n+class ValuesListIterable(BaseIterable):\n+    \"\"\"\n+    Iterable returned by QuerySet.values_list(flat=False) that yields a tuple\n+    for each row.\n+    \"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        query = queryset.query\n+        compiler = query.get_compiler(queryset.db)\n+\n+        if queryset._fields:\n+            # extra(select=...) cols are always at the start of the row.\n+            names = [\n+                *query.extra_select,\n+                *query.values_select,\n+                *query.annotation_select,\n+            ]\n+            fields = [*queryset._fields, *(f for f in query.annotation_select if f not in queryset._fields)]\n+            if fields != names:\n+                # Reorder according to fields.\n+                index_map = {name: idx for idx, name in enumerate(names)}\n+                rowfactory = operator.itemgetter(*[index_map[f] for f in fields])\n+                return map(\n+                    rowfactory,\n+                    compiler.results_iter(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)\n+                )\n+        return compiler.results_iter(tuple_expected=True, chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)\n+\n+\n+class NamedValuesListIterable(ValuesListIterable):\n+    \"\"\"\n+    Iterable returned by QuerySet.values_list(named=True) that yields a\n+    namedtuple for each row.\n+    \"\"\"\n+\n+    @staticmethod\n+    @lru_cache()\n+    def create_namedtuple_class(*names):\n+        # Cache namedtuple() with @lru_cache() since it's too slow to be\n+        # called for every QuerySet evaluation.\n+        return namedtuple('Row', names)\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        if queryset._fields:\n+            names = queryset._fields\n+        else:\n+            query = queryset.query\n+            names = [*query.extra_select, *query.values_select, *query.annotation_select]\n+        tuple_class = self.create_namedtuple_class(*names)\n+        new = tuple.__new__\n+        for row in super().__iter__():\n+            yield new(tuple_class, row)\n+\n+\n+class FlatValuesListIterable(BaseIterable):\n+    \"\"\"\n+    Iterable returned by QuerySet.values_list(flat=True) that yields single\n+    values.\n+    \"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        compiler = queryset.query.get_compiler(queryset.db)\n+        for row in compiler.results_iter(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size):\n+            yield row[0]\n+\n+\n+class QuerySet:\n+    \"\"\"Represent a lazy database lookup for a set of objects.\"\"\"\n+\n+    def __init__(self, model=None, query=None, using=None, hints=None):\n+        self.model = model\n+        self._db = using\n+        self._hints = hints or {}\n+        self.query = query or sql.Query(self.model)\n+        self._result_cache = None\n+        self._sticky_filter = False\n+        self._for_write = False\n+        self._prefetch_related_lookups = ()\n+        self._prefetch_done = False\n+        self._known_related_objects = {}  # {rel_field: {pk: rel_obj}}\n+        self._iterable_class = ModelIterable\n+        self._fields = None\n+\n+    def as_manager(cls):\n+        # Address the circular dependency between `Queryset` and `Manager`.\n+        from django.db.models.manager import Manager\n+        manager = Manager.from_queryset(cls)()\n+        manager._built_with_as_manager = True\n+        return manager\n+    as_manager.queryset_only = True\n+    as_manager = classmethod(as_manager)\n+\n+    ########################\n+    # PYTHON MAGIC METHODS #\n+    ########################\n+\n+    def __deepcopy__(self, memo):\n+        \"\"\"Don't populate the QuerySet's cache.\"\"\"\n+        obj = self.__class__()\n+        for k, v in self.__dict__.items():\n+            if k == '_result_cache':\n+                obj.__dict__[k] = None\n+            else:\n+                obj.__dict__[k] = copy.deepcopy(v, memo)\n+        return obj\n+\n+    def __getstate__(self):\n+        # Force the cache to be fully populated.\n+        self._fetch_all()\n+        return {**self.__dict__, DJANGO_VERSION_PICKLE_KEY: get_version()}\n+\n+    def __setstate__(self, state):\n+        msg = None\n+        pickled_version = state.get(DJANGO_VERSION_PICKLE_KEY)\n+        if pickled_version:\n+            current_version = get_version()\n+            if current_version != pickled_version:\n+                msg = (\n+                    \"Pickled queryset instance's Django version %s does not \"\n+                    \"match the current version %s.\" % (pickled_version, current_version)\n+                )\n+        else:\n+            msg = \"Pickled queryset instance's Django version is not specified.\"\n+\n+        if msg:\n+            warnings.warn(msg, RuntimeWarning, stacklevel=2)\n+\n+        self.__dict__.update(state)\n+\n+    def __repr__(self):\n+        data = list(self[:REPR_OUTPUT_SIZE + 1])\n+        if len(data) > REPR_OUTPUT_SIZE:\n+            data[-1] = \"...(remaining elements truncated)...\"\n+        return '<%s %r>' % (self.__class__.__name__, data)\n+\n+    def __len__(self):\n+        self._fetch_all()\n+        return len(self._result_cache)\n+\n+    def __iter__(self):\n+        \"\"\"\n+        The queryset iterator protocol uses three nested iterators in the\n+        default case:\n+            1. sql.compiler.execute_sql()\n+               - Returns 100 rows at time (constants.GET_ITERATOR_CHUNK_SIZE)\n+                 using cursor.fetchmany(). This part is responsible for\n+                 doing some column masking, and returning the rows in chunks.\n+            2. sql.compiler.results_iter()\n+               - Returns one row at time. At this point the rows are still just\n+                 tuples. In some cases the return values are converted to\n+                 Python values at this location.\n+            3. self.iterator()\n+               - Responsible for turning the rows into model objects.\n+        \"\"\"\n+        self._fetch_all()\n+        return iter(self._result_cache)\n+\n+    def __bool__(self):\n+        self._fetch_all()\n+        return bool(self._result_cache)\n+\n+    def __getitem__(self, k):\n+        \"\"\"Retrieve an item or slice from the set of results.\"\"\"\n+        if not isinstance(k, (int, slice)):\n+            raise TypeError(\n+                'QuerySet indices must be integers or slices, not %s.'\n+                % type(k).__name__\n+            )\n+        assert ((not isinstance(k, slice) and (k >= 0)) or\n+                (isinstance(k, slice) and (k.start is None or k.start >= 0) and\n+                 (k.stop is None or k.stop >= 0))), \\\n+            \"Negative indexing is not supported.\"\n+\n+        if self._result_cache is not None:\n+            return self._result_cache[k]\n+\n+        if isinstance(k, slice):\n+            qs = self._chain()\n+            if k.start is not None:\n+                start = int(k.start)\n+            else:\n+                start = None\n+            if k.stop is not None:\n+                stop = int(k.stop)\n+            else:\n+                stop = None\n+            qs.query.set_limits(start, stop)\n+            return list(qs)[::k.step] if k.step else qs\n+\n+        qs = self._chain()\n+        qs.query.set_limits(k, k + 1)\n+        qs._fetch_all()\n+        return qs._result_cache[0]\n+\n+    def __and__(self, other):\n+        self._merge_sanity_check(other)\n+        if isinstance(other, EmptyQuerySet):\n+            return other\n+        if isinstance(self, EmptyQuerySet):\n+            return self\n+        combined = self._chain()\n+        combined._merge_known_related_objects(other)\n+        combined.query.combine(other.query, sql.AND)\n+        return combined\n+\n+    def __or__(self, other):\n+        self._merge_sanity_check(other)\n+        if isinstance(self, EmptyQuerySet):\n+            return other\n+        if isinstance(other, EmptyQuerySet):\n+            return self\n+        query = self if self.query.can_filter() else self.model._base_manager.filter(pk__in=self.values('pk'))\n+        combined = query._chain()\n+        combined._merge_known_related_objects(other)\n+        if not other.query.can_filter():\n+            other = other.model._base_manager.filter(pk__in=other.values('pk'))\n+        combined.query.combine(other.query, sql.OR)\n+        return combined\n+\n+    ####################################\n+    # METHODS THAT DO DATABASE QUERIES #\n+    ####################################\n+\n+    def _iterator(self, use_chunked_fetch, chunk_size):\n+        yield from self._iterable_class(self, chunked_fetch=use_chunked_fetch, chunk_size=chunk_size)\n+\n+    def iterator(self, chunk_size=2000):\n+        \"\"\"\n+        An iterator over the results from applying this QuerySet to the\n+        database.\n+        \"\"\"\n+        if chunk_size <= 0:\n+            raise ValueError('Chunk size must be strictly positive.')\n+        use_chunked_fetch = not connections[self.db].settings_dict.get('DISABLE_SERVER_SIDE_CURSORS')\n+        return self._iterator(use_chunked_fetch, chunk_size)\n+\n+    def aggregate(self, *args, **kwargs):\n+        \"\"\"\n+        Return a dictionary containing the calculations (aggregation)\n+        over the current queryset.\n+\n+        If args is present the expression is passed as a kwarg using\n+        the Aggregate object's default alias.\n+        \"\"\"\n+        if self.query.distinct_fields:\n+            raise NotImplementedError(\"aggregate() + distinct(fields) not implemented.\")\n+        self._validate_values_are_expressions((*args, *kwargs.values()), method_name='aggregate')\n+        for arg in args:\n+            # The default_alias property raises TypeError if default_alias\n+            # can't be set automatically or AttributeError if it isn't an\n+            # attribute.\n+            try:\n+                arg.default_alias\n+            except (AttributeError, TypeError):\n+                raise TypeError(\"Complex aggregates require an alias\")\n+            kwargs[arg.default_alias] = arg\n+\n+        query = self.query.chain()\n+        for (alias, aggregate_expr) in kwargs.items():\n+            query.add_annotation(aggregate_expr, alias, is_summary=True)\n+            if not query.annotations[alias].contains_aggregate:\n+                raise TypeError(\"%s is not an aggregate expression\" % alias)\n+        return query.get_aggregation(self.db, kwargs)\n+\n+    def count(self):\n+        \"\"\"\n+        Perform a SELECT COUNT() and return the number of records as an\n+        integer.\n+\n+        If the QuerySet is already fully cached, return the length of the\n+        cached results set to avoid multiple SELECT COUNT(*) calls.\n+        \"\"\"\n+        if self._result_cache is not None:\n+            return len(self._result_cache)\n+\n+        return self.query.get_count(using=self.db)\n+\n+    def get(self, *args, **kwargs):\n+        \"\"\"\n+        Perform the query and return a single object matching the given\n+        keyword arguments.\n+        \"\"\"\n+        clone = self._chain() if self.query.combinator else self.filter(*args, **kwargs)\n+        if self.query.can_filter() and not self.query.distinct_fields:\n+            clone = clone.order_by()\n+        limit = None\n+        if not clone.query.select_for_update or connections[clone.db].features.supports_select_for_update_with_limit:\n+            limit = MAX_GET_RESULTS\n+            clone.query.set_limits(high=limit)\n+        num = len(clone)\n+        if num == 1:\n+            return clone._result_cache[0]\n+        if not num:\n+            raise self.model.DoesNotExist(\n+                \"%s matching query does not exist.\" %\n+                self.model._meta.object_name\n+            )\n+        raise self.model.MultipleObjectsReturned(\n+            'get() returned more than one %s -- it returned %s!' % (\n+                self.model._meta.object_name,\n+                num if not limit or num < limit else 'more than %s' % (limit - 1),\n+            )\n+        )\n+\n+    def create(self, **kwargs):\n+        \"\"\"\n+        Create a new object with the given kwargs, saving it to the database\n+        and returning the created object.\n+        \"\"\"\n+        obj = self.model(**kwargs)\n+        self._for_write = True\n+        obj.save(force_insert=True, using=self.db)\n+        return obj\n+\n+    def _populate_pk_values(self, objs):\n+        for obj in objs:\n+            if obj.pk is None:\n+                obj.pk = obj._meta.pk.get_pk_value_on_save(obj)\n+\n+    def bulk_create(self, objs, batch_size=None, ignore_conflicts=False):\n+        \"\"\"\n+        Insert each of the instances into the database. Do *not* call\n+        save() on each of the instances, do not send any pre/post_save\n+        signals, and do not set the primary key attribute if it is an\n+        autoincrement field (except if features.can_return_rows_from_bulk_insert=True).\n+        Multi-table models are not supported.\n+        \"\"\"\n+        # When you bulk insert you don't get the primary keys back (if it's an\n+        # autoincrement, except if can_return_rows_from_bulk_insert=True), so\n+        # you can't insert into the child tables which references this. There\n+        # are two workarounds:\n+        # 1) This could be implemented if you didn't have an autoincrement pk\n+        # 2) You could do it by doing O(n) normal inserts into the parent\n+        #    tables to get the primary keys back and then doing a single bulk\n+        #    insert into the childmost table.\n+        # We currently set the primary keys on the objects when using\n+        # PostgreSQL via the RETURNING ID clause. It should be possible for\n+        # Oracle as well, but the semantics for extracting the primary keys is\n+        # trickier so it's not done yet.\n+        assert batch_size is None or batch_size > 0\n+        # Check that the parents share the same concrete model with the our\n+        # model to detect the inheritance pattern ConcreteGrandParent ->\n+        # MultiTableParent -> ProxyChild. Simply checking self.model._meta.proxy\n+        # would not identify that case as involving multiple tables.\n+        for parent in self.model._meta.get_parent_list():\n+            if parent._meta.concrete_model is not self.model._meta.concrete_model:\n+                raise ValueError(\"Can't bulk create a multi-table inherited model\")\n+        if not objs:\n+            return objs\n+        self._for_write = True\n+        connection = connections[self.db]\n+        opts = self.model._meta\n+        fields = opts.concrete_fields\n+        objs = list(objs)\n+        self._populate_pk_values(objs)\n+        with transaction.atomic(using=self.db, savepoint=False):\n+            objs_with_pk, objs_without_pk = partition(lambda o: o.pk is None, objs)\n+            if objs_with_pk:\n+                returned_columns = self._batched_insert(\n+                    objs_with_pk, fields, batch_size, ignore_conflicts=ignore_conflicts,\n+                )\n+                for obj_with_pk, results in zip(objs_with_pk, returned_columns):\n+                    for result, field in zip(results, opts.db_returning_fields):\n+                        if field != opts.pk:\n+                            setattr(obj_with_pk, field.attname, result)\n+                for obj_with_pk in objs_with_pk:\n+                    obj_with_pk._state.adding = False\n+                    obj_with_pk._state.db = self.db\n+            if objs_without_pk:\n+                fields = [f for f in fields if not isinstance(f, AutoField)]\n+                returned_columns = self._batched_insert(\n+                    objs_without_pk, fields, batch_size, ignore_conflicts=ignore_conflicts,\n+                )\n+                if connection.features.can_return_rows_from_bulk_insert and not ignore_conflicts:\n+                    assert len(returned_columns) == len(objs_without_pk)\n+                for obj_without_pk, results in zip(objs_without_pk, returned_columns):\n+                    for result, field in zip(results, opts.db_returning_fields):\n+                        setattr(obj_without_pk, field.attname, result)\n+                    obj_without_pk._state.adding = False\n+                    obj_without_pk._state.db = self.db\n+\n+        return objs\n+\n+    def bulk_update(self, objs, fields, batch_size=None):\n+        \"\"\"\n+        Update the given fields in each of the given objects in the database.\n+        \"\"\"\n+        if batch_size is not None and batch_size < 0:\n+            raise ValueError('Batch size must be a positive integer.')\n+        if not fields:\n+            raise ValueError('Field names must be given to bulk_update().')\n+        objs = tuple(objs)\n+        if any(obj.pk is None for obj in objs):\n+            raise ValueError('All bulk_update() objects must have a primary key set.')\n+        fields = [self.model._meta.get_field(name) for name in fields]\n+        if any(not f.concrete or f.many_to_many for f in fields):\n+            raise ValueError('bulk_update() can only be used with concrete fields.')\n+        if any(f.primary_key for f in fields):\n+            raise ValueError('bulk_update() cannot be used with primary key fields.')\n+        if not objs:\n+            return\n+        # PK is used twice in the resulting update query, once in the filter\n+        # and once in the WHEN. Each field will also have one CAST.\n+        max_batch_size = connections[self.db].ops.bulk_batch_size(['pk', 'pk'] + fields, objs)\n+        batch_size = min(batch_size, max_batch_size) if batch_size else max_batch_size\n+        requires_casting = connections[self.db].features.requires_casted_case_in_updates\n+        batches = (objs[i:i + batch_size] for i in range(0, len(objs), batch_size))\n+        updates = []\n+        for batch_objs in batches:\n+            update_kwargs = {}\n+            for field in fields:\n+                when_statements = []\n+                for obj in batch_objs:\n+                    attr = getattr(obj, field.attname)\n+                    if not isinstance(attr, Expression):\n+                        attr = Value(attr, output_field=field)\n+                    when_statements.append(When(pk=obj.pk, then=attr))\n+                case_statement = Case(*when_statements, output_field=field)\n+                if requires_casting:\n+                    case_statement = Cast(case_statement, output_field=field)\n+                update_kwargs[field.attname] = case_statement\n+            updates.append(([obj.pk for obj in batch_objs], update_kwargs))\n+        with transaction.atomic(using=self.db, savepoint=False):\n+            for pks, update_kwargs in updates:\n+                self.filter(pk__in=pks).update(**update_kwargs)\n+    bulk_update.alters_data = True\n+\n+    def get_or_create(self, defaults=None, **kwargs):\n+        \"\"\"\n+        Look up an object with the given kwargs, creating one if necessary.\n+        Return a tuple of (object, created), where created is a boolean\n+        specifying whether an object was created.\n+        \"\"\"\n+        # The get() needs to be targeted at the write database in order\n+        # to avoid potential transaction consistency problems.\n+        self._for_write = True\n+        try:\n+            return self.get(**kwargs), False\n+        except self.model.DoesNotExist:\n+            params = self._extract_model_params(defaults, **kwargs)\n+            return self._create_object_from_params(kwargs, params)\n+\n+    def update_or_create(self, defaults=None, **kwargs):\n+        \"\"\"\n+        Look up an object with the given kwargs, updating one with defaults\n+        if it exists, otherwise create a new one.\n+        Return a tuple (object, created), where created is a boolean\n+        specifying whether an object was created.\n+        \"\"\"\n+        defaults = defaults or {}\n+        self._for_write = True\n+        with transaction.atomic(using=self.db):\n+            try:\n+                obj = self.select_for_update().get(**kwargs)\n+            except self.model.DoesNotExist:\n+                params = self._extract_model_params(defaults, **kwargs)\n+                # Lock the row so that a concurrent update is blocked until\n+                # after update_or_create() has performed its save.\n+                obj, created = self._create_object_from_params(kwargs, params, lock=True)\n+                if created:\n+                    return obj, created\n+            for k, v in defaults.items():\n+                setattr(obj, k, v() if callable(v) else v)\n+            obj.save(using=self.db)\n+        return obj, False\n+\n+    def _create_object_from_params(self, lookup, params, lock=False):\n+        \"\"\"\n+        Try to create an object using passed params. Used by get_or_create()\n+        and update_or_create().\n+        \"\"\"\n+        try:\n+            with transaction.atomic(using=self.db):\n+                params = {k: v() if callable(v) else v for k, v in params.items()}\n+                obj = self.create(**params)\n+            return obj, True\n+        except IntegrityError as e:\n+            try:\n+                qs = self.select_for_update() if lock else self\n+                return qs.get(**lookup), False\n+            except self.model.DoesNotExist:\n+                pass\n+            raise e\n+\n+    def _extract_model_params(self, defaults, **kwargs):\n+        \"\"\"\n+        Prepare `params` for creating a model instance based on the given\n+        kwargs; for use by get_or_create() and update_or_create().\n+        \"\"\"\n+        defaults = defaults or {}\n+        params = {k: v for k, v in kwargs.items() if LOOKUP_SEP not in k}\n+        params.update(defaults)\n+        property_names = self.model._meta._property_names\n+        invalid_params = []\n+        for param in params:\n+            try:\n+                self.model._meta.get_field(param)\n+            except exceptions.FieldDoesNotExist:\n+                # It's okay to use a model's property if it has a setter.\n+                if not (param in property_names and getattr(self.model, param).fset):\n+                    invalid_params.append(param)\n+        if invalid_params:\n+            raise exceptions.FieldError(\n+                \"Invalid field name(s) for model %s: '%s'.\" % (\n+                    self.model._meta.object_name,\n+                    \"', '\".join(sorted(invalid_params)),\n+                ))\n+        return params\n+\n+    def _earliest(self, *fields):\n+        \"\"\"\n+        Return the earliest object according to fields (if given) or by the\n+        model's Meta.get_latest_by.\n+        \"\"\"\n+        if fields:\n+            order_by = fields\n+        else:\n+            order_by = getattr(self.model._meta, 'get_latest_by')\n+            if order_by and not isinstance(order_by, (tuple, list)):\n+                order_by = (order_by,)\n+        if order_by is None:\n+            raise ValueError(\n+                \"earliest() and latest() require either fields as positional \"\n+                \"arguments or 'get_latest_by' in the model's Meta.\"\n+            )\n+\n+        assert not self.query.is_sliced, \\\n+            \"Cannot change a query once a slice has been taken.\"\n+        obj = self._chain()\n+        obj.query.set_limits(high=1)\n+        obj.query.clear_ordering(force_empty=True)\n+        obj.query.add_ordering(*order_by)\n+        return obj.get()\n+\n+    def earliest(self, *fields):\n+        return self._earliest(*fields)\n+\n+    def latest(self, *fields):\n+        return self.reverse()._earliest(*fields)\n+\n+    def first(self):\n+        \"\"\"Return the first object of a query or None if no match is found.\"\"\"\n+        for obj in (self if self.ordered else self.order_by('pk'))[:1]:\n+            return obj\n+\n+    def last(self):\n+        \"\"\"Return the last object of a query or None if no match is found.\"\"\"\n+        for obj in (self.reverse() if self.ordered else self.order_by('-pk'))[:1]:\n+            return obj\n+\n+    def in_bulk(self, id_list=None, *, field_name='pk'):\n+        \"\"\"\n+        Return a dictionary mapping each of the given IDs to the object with\n+        that ID. If `id_list` isn't provided, evaluate the entire QuerySet.\n+        \"\"\"\n+        assert not self.query.is_sliced, \\\n+            \"Cannot use 'limit' or 'offset' with in_bulk\"\n+        if field_name != 'pk' and not self.model._meta.get_field(field_name).unique:\n+            raise ValueError(\"in_bulk()'s field_name must be a unique field but %r isn't.\" % field_name)\n+        if id_list is not None:\n+            if not id_list:\n+                return {}\n+            filter_key = '{}__in'.format(field_name)\n+            batch_size = connections[self.db].features.max_query_params\n+            id_list = tuple(id_list)\n+            # If the database has a limit on the number of query parameters\n+            # (e.g. SQLite), retrieve objects in batches if necessary.\n+            if batch_size and batch_size < len(id_list):\n+                qs = ()\n+                for offset in range(0, len(id_list), batch_size):\n+                    batch = id_list[offset:offset + batch_size]\n+                    qs += tuple(self.filter(**{filter_key: batch}).order_by())\n+            else:\n+                qs = self.filter(**{filter_key: id_list}).order_by()\n+        else:\n+            qs = self._chain()\n+        return {getattr(obj, field_name): obj for obj in qs}\n+\n+    def delete(self):\n+        \"\"\"Delete the records in the current QuerySet.\"\"\"\n+        assert not self.query.is_sliced, \\\n+            \"Cannot use 'limit' or 'offset' with delete.\"\n+\n+        if self._fields is not None:\n+            raise TypeError(\"Cannot call delete() after .values() or .values_list()\")\n+\n+        del_query = self._chain()\n+\n+        # The delete is actually 2 queries - one to find related objects,\n+        # and one to delete. Make sure that the discovery of related\n+        # objects is performed on the same database as the deletion.\n+        del_query._for_write = True\n+\n+        # Disable non-supported fields.\n+        del_query.query.select_for_update = False\n+        del_query.query.select_related = False\n+        del_query.query.clear_ordering(force_empty=True)\n+\n+        collector = Collector(using=del_query.db)\n+        collector.collect(del_query)\n+        deleted, _rows_count = collector.delete()\n+\n+        # Clear the result cache, in case this QuerySet gets reused.\n+        self._result_cache = None\n+        return deleted, _rows_count\n+\n+    delete.alters_data = True\n+    delete.queryset_only = True\n+\n+    def _raw_delete(self, using):\n+        \"\"\"\n+        Delete objects found from the given queryset in single direct SQL\n+        query. No signals are sent and there is no protection for cascades.\n+        \"\"\"\n+        return sql.DeleteQuery(self.model).delete_qs(self, using)\n+    _raw_delete.alters_data = True\n+\n+    def update(self, **kwargs):\n+        \"\"\"\n+        Update all elements in the current QuerySet, setting all the given\n+        fields to the appropriate values.\n+        \"\"\"\n+        assert not self.query.is_sliced, \\\n+            \"Cannot update a query once a slice has been taken.\"\n+        self._for_write = True\n+        query = self.query.chain(sql.UpdateQuery)\n+        query.add_update_values(kwargs)\n+        # Clear any annotations so that they won't be present in subqueries.\n+        query.annotations = {}\n+        with transaction.mark_for_rollback_on_error(using=self.db):\n+            rows = query.get_compiler(self.db).execute_sql(CURSOR)\n+        self._result_cache = None\n+        return rows\n+    update.alters_data = True\n+\n+    def _update(self, values):\n+        \"\"\"\n+        A version of update() that accepts field objects instead of field names.\n+        Used primarily for model saving and not intended for use by general\n+        code (it requires too much poking around at model internals to be\n+        useful at that level).\n+        \"\"\"\n+        assert not self.query.is_sliced, \\\n+            \"Cannot update a query once a slice has been taken.\"\n+        query = self.query.chain(sql.UpdateQuery)\n+        query.add_update_fields(values)\n+        # Clear any annotations so that they won't be present in subqueries.\n+        query.annotations = {}\n+        self._result_cache = None\n+        return query.get_compiler(self.db).execute_sql(CURSOR)\n+    _update.alters_data = True\n+    _update.queryset_only = False\n+\n+    def exists(self):\n+        if self._result_cache is None:\n+            return self.query.has_results(using=self.db)\n+        return bool(self._result_cache)\n+\n+    def _prefetch_related_objects(self):\n+        # This method can only be called once the result cache has been filled.\n+        prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)\n+        self._prefetch_done = True\n+\n+    def explain(self, *, format=None, **options):\n+        return self.query.explain(using=self.db, format=format, **options)\n+\n+    ##################################################\n+    # PUBLIC METHODS THAT RETURN A QUERYSET SUBCLASS #\n+    ##################################################\n+\n+    def raw(self, raw_query, params=None, translations=None, using=None):\n+        if using is None:\n+            using = self.db\n+        qs = RawQuerySet(raw_query, model=self.model, params=params, translations=translations, using=using)\n+        qs._prefetch_related_lookups = self._prefetch_related_lookups[:]\n+        return qs\n+\n+    def _values(self, *fields, **expressions):\n+        clone = self._chain()\n+        if expressions:\n+            clone = clone.annotate(**expressions)\n+        clone._fields = fields\n+        clone.query.set_values(fields)\n+        return clone\n+\n+    def values(self, *fields, **expressions):\n+        fields += tuple(expressions)\n+        clone = self._values(*fields, **expressions)\n+        clone._iterable_class = ValuesIterable\n+        return clone\n+\n+    def values_list(self, *fields, flat=False, named=False):\n+        if flat and named:\n+            raise TypeError(\"'flat' and 'named' can't be used together.\")\n+        if flat and len(fields) > 1:\n+            raise TypeError(\"'flat' is not valid when values_list is called with more than one field.\")\n+\n+        field_names = {f for f in fields if not hasattr(f, 'resolve_expression')}\n+        _fields = []\n+        expressions = {}\n+        counter = 1\n+        for field in fields:\n+            if hasattr(field, 'resolve_expression'):\n+                field_id_prefix = getattr(field, 'default_alias', field.__class__.__name__.lower())\n+                while True:\n+                    field_id = field_id_prefix + str(counter)\n+                    counter += 1\n+                    if field_id not in field_names:\n+                        break\n+                expressions[field_id] = field\n+                _fields.append(field_id)\n+            else:\n+                _fields.append(field)\n+\n+        clone = self._values(*_fields, **expressions)\n+        clone._iterable_class = (\n+            NamedValuesListIterable if named\n+            else FlatValuesListIterable if flat\n+            else ValuesListIterable\n+        )\n+        return clone\n+\n+    def dates(self, field_name, kind, order='ASC'):\n+        \"\"\"\n+        Return a list of date objects representing all available dates for\n+        the given field_name, scoped to 'kind'.\n+        \"\"\"\n+        assert kind in ('year', 'month', 'week', 'day'), \\\n+            \"'kind' must be one of 'year', 'month', 'week', or 'day'.\"\n+        assert order in ('ASC', 'DESC'), \\\n+            \"'order' must be either 'ASC' or 'DESC'.\"\n+        return self.annotate(\n+            datefield=Trunc(field_name, kind, output_field=DateField()),\n+            plain_field=F(field_name)\n+        ).values_list(\n+            'datefield', flat=True\n+        ).distinct().filter(plain_field__isnull=False).order_by(('-' if order == 'DESC' else '') + 'datefield')\n+\n+    def datetimes(self, field_name, kind, order='ASC', tzinfo=None):\n+        \"\"\"\n+        Return a list of datetime objects representing all available\n+        datetimes for the given field_name, scoped to 'kind'.\n+        \"\"\"\n+        assert kind in ('year', 'month', 'week', 'day', 'hour', 'minute', 'second'), \\\n+            \"'kind' must be one of 'year', 'month', 'week', 'day', 'hour', 'minute', or 'second'.\"\n+        assert order in ('ASC', 'DESC'), \\\n+            \"'order' must be either 'ASC' or 'DESC'.\"\n+        if settings.USE_TZ:\n+            if tzinfo is None:\n+                tzinfo = timezone.get_current_timezone()\n+        else:\n+            tzinfo = None\n+        return self.annotate(\n+            datetimefield=Trunc(field_name, kind, output_field=DateTimeField(), tzinfo=tzinfo),\n+            plain_field=F(field_name)\n+        ).values_list(\n+            'datetimefield', flat=True\n+        ).distinct().filter(plain_field__isnull=False).order_by(('-' if order == 'DESC' else '') + 'datetimefield')\n+\n+    def none(self):\n+        \"\"\"Return an empty QuerySet.\"\"\"\n+        clone = self._chain()\n+        clone.query.set_empty()\n+        return clone\n+\n+    ##################################################################\n+    # PUBLIC METHODS THAT ALTER ATTRIBUTES AND RETURN A NEW QUERYSET #\n+    ##################################################################\n+\n+    def all(self):\n+        \"\"\"\n+        Return a new QuerySet that is a copy of the current one. This allows a\n+        QuerySet to proxy for a model manager in some cases.\n+        \"\"\"\n+        return self._chain()\n+\n+    def filter(self, *args, **kwargs):\n+        \"\"\"\n+        Return a new QuerySet instance with the args ANDed to the existing\n+        set.\n+        \"\"\"\n+        self._not_support_combined_queries('filter')\n+        return self._filter_or_exclude(False, *args, **kwargs)\n+\n+    def exclude(self, *args, **kwargs):\n+        \"\"\"\n+        Return a new QuerySet instance with NOT (args) ANDed to the existing\n+        set.\n+        \"\"\"\n+        self._not_support_combined_queries('exclude')\n+        return self._filter_or_exclude(True, *args, **kwargs)\n+\n+    def _filter_or_exclude(self, negate, *args, **kwargs):\n+        if args or kwargs:\n+            assert not self.query.is_sliced, \\\n+                \"Cannot filter a query once a slice has been taken.\"\n+\n+        clone = self._chain()\n+        if negate:\n+            clone.query.add_q(~Q(*args, **kwargs))\n+        else:\n+            clone.query.add_q(Q(*args, **kwargs))\n+        return clone\n+\n+    def complex_filter(self, filter_obj):\n+        \"\"\"\n+        Return a new QuerySet instance with filter_obj added to the filters.\n+\n+        filter_obj can be a Q object or a dictionary of keyword lookup\n+        arguments.\n+\n+        This exists to support framework features such as 'limit_choices_to',\n+        and usually it will be more natural to use other methods.\n+        \"\"\"\n+        if isinstance(filter_obj, Q):\n+            clone = self._chain()\n+            clone.query.add_q(filter_obj)\n+            return clone\n+        else:\n+            return self._filter_or_exclude(None, **filter_obj)\n+\n+    def _combinator_query(self, combinator, *other_qs, all=False):\n+        # Clone the query to inherit the select list and everything\n+        clone = self._chain()\n+        # Clear limits and ordering so they can be reapplied\n+        clone.query.clear_ordering(True)\n+        clone.query.clear_limits()\n+        clone.query.combined_queries = (self.query,) + tuple(qs.query for qs in other_qs)\n+        clone.query.combinator = combinator\n+        clone.query.combinator_all = all\n+        return clone\n+\n+    def union(self, *other_qs, all=False):\n+        # If the query is an EmptyQuerySet, combine all nonempty querysets.\n+        if isinstance(self, EmptyQuerySet):\n+            qs = [q for q in other_qs if not isinstance(q, EmptyQuerySet)]\n+            return qs[0]._combinator_query('union', *qs[1:], all=all) if qs else self\n+        return self._combinator_query('union', *other_qs, all=all)\n+\n+    def intersection(self, *other_qs):\n+        # If any query is an EmptyQuerySet, return it.\n+        if isinstance(self, EmptyQuerySet):\n+            return self\n+        for other in other_qs:\n+            if isinstance(other, EmptyQuerySet):\n+                return other\n+        return self._combinator_query('intersection', *other_qs)\n+\n+    def difference(self, *other_qs):\n+        # If the query is an EmptyQuerySet, return it.\n+        if isinstance(self, EmptyQuerySet):\n+            return self\n+        return self._combinator_query('difference', *other_qs)\n+\n+    def select_for_update(self, nowait=False, skip_locked=False, of=()):\n+        \"\"\"\n+        Return a new QuerySet instance that will select objects with a\n+        FOR UPDATE lock.\n+        \"\"\"\n+        if nowait and skip_locked:\n+            raise ValueError('The nowait option cannot be used with skip_locked.')\n+        obj = self._chain()\n+        obj._for_write = True\n+        obj.query.select_for_update = True\n+        obj.query.select_for_update_nowait = nowait\n+        obj.query.select_for_update_skip_locked = skip_locked\n+        obj.query.select_for_update_of = of\n+        return obj\n+\n+    def select_related(self, *fields):\n+        \"\"\"\n+        Return a new QuerySet instance that will select related objects.\n+\n+        If fields are specified, they must be ForeignKey fields and only those\n+        related objects are included in the selection.\n+\n+        If select_related(None) is called, clear the list.\n+        \"\"\"\n+        self._not_support_combined_queries('select_related')\n+        if self._fields is not None:\n+            raise TypeError(\"Cannot call select_related() after .values() or .values_list()\")\n+\n+        obj = self._chain()\n+        if fields == (None,):\n+            obj.query.select_related = False\n+        elif fields:\n+            obj.query.add_select_related(fields)\n+        else:\n+            obj.query.select_related = True\n+        return obj\n+\n+    def prefetch_related(self, *lookups):\n+        \"\"\"\n+        Return a new QuerySet instance that will prefetch the specified\n+        Many-To-One and Many-To-Many related objects when the QuerySet is\n+        evaluated.\n+\n+        When prefetch_related() is called more than once, append to the list of\n+        prefetch lookups. If prefetch_related(None) is called, clear the list.\n+        \"\"\"\n+        self._not_support_combined_queries('prefetch_related')\n+        clone = self._chain()\n+        if lookups == (None,):\n+            clone._prefetch_related_lookups = ()\n+        else:\n+            for lookup in lookups:\n+                if isinstance(lookup, Prefetch):\n+                    lookup = lookup.prefetch_to\n+                lookup = lookup.split(LOOKUP_SEP, 1)[0]\n+                if lookup in self.query._filtered_relations:\n+                    raise ValueError('prefetch_related() is not supported with FilteredRelation.')\n+            clone._prefetch_related_lookups = clone._prefetch_related_lookups + lookups\n+        return clone\n+\n+    def annotate(self, *args, **kwargs):\n+        \"\"\"\n+        Return a query set in which the returned objects have been annotated\n+        with extra data or aggregations.\n+        \"\"\"\n+        self._not_support_combined_queries('annotate')\n+        self._validate_values_are_expressions(args + tuple(kwargs.values()), method_name='annotate')\n+        annotations = {}\n+        for arg in args:\n+            # The default_alias property may raise a TypeError.\n+            try:\n+                if arg.default_alias in kwargs:\n+                    raise ValueError(\"The named annotation '%s' conflicts with the \"\n+                                     \"default name for another annotation.\"\n+                                     % arg.default_alias)\n+            except TypeError:\n+                raise TypeError(\"Complex annotations require an alias\")\n+            annotations[arg.default_alias] = arg\n+        annotations.update(kwargs)\n+\n+        clone = self._chain()\n+        names = self._fields\n+        if names is None:\n+            names = set(chain.from_iterable(\n+                (field.name, field.attname) if hasattr(field, 'attname') else (field.name,)\n+                for field in self.model._meta.get_fields()\n+            ))\n+\n+        for alias, annotation in annotations.items():\n+            if alias in names:\n+                raise ValueError(\"The annotation '%s' conflicts with a field on \"\n+                                 \"the model.\" % alias)\n+            if isinstance(annotation, FilteredRelation):\n+                clone.query.add_filtered_relation(annotation, alias)\n+            else:\n+                clone.query.add_annotation(annotation, alias, is_summary=False)\n+\n+        for alias, annotation in clone.query.annotations.items():\n+            if alias in annotations and annotation.contains_aggregate:\n+                if clone._fields is None:\n+                    clone.query.group_by = True\n+                else:\n+                    clone.query.set_group_by()\n+                break\n+\n+        return clone\n+\n+    def order_by(self, *field_names):\n+        \"\"\"Return a new QuerySet instance with the ordering changed.\"\"\"\n+        assert not self.query.is_sliced, \\\n+            \"Cannot reorder a query once a slice has been taken.\"\n+        obj = self._chain()\n+        obj.query.clear_ordering(force_empty=False)\n+        obj.query.add_ordering(*field_names)\n+        return obj\n+\n+    def distinct(self, *field_names):\n+        \"\"\"\n+        Return a new QuerySet instance that will select only distinct results.\n+        \"\"\"\n+        assert not self.query.is_sliced, \\\n+            \"Cannot create distinct fields once a slice has been taken.\"\n+        obj = self._chain()\n+        obj.query.add_distinct_fields(*field_names)\n+        return obj\n+\n+    def extra(self, select=None, where=None, params=None, tables=None,\n+              order_by=None, select_params=None):\n+        \"\"\"Add extra SQL fragments to the query.\"\"\"\n+        self._not_support_combined_queries('extra')\n+        assert not self.query.is_sliced, \\\n+            \"Cannot change a query once a slice has been taken\"\n+        clone = self._chain()\n+        clone.query.add_extra(select, select_params, where, params, tables, order_by)\n+        return clone\n+\n+    def reverse(self):\n+        \"\"\"Reverse the ordering of the QuerySet.\"\"\"\n+        if self.query.is_sliced:\n+            raise TypeError('Cannot reverse a query once a slice has been taken.')\n+        clone = self._chain()\n+        clone.query.standard_ordering = not clone.query.standard_ordering\n+        return clone\n+\n+    def defer(self, *fields):\n+        \"\"\"\n+        Defer the loading of data for certain fields until they are accessed.\n+        Add the set of deferred fields to any existing set of deferred fields.\n+        The only exception to this is if None is passed in as the only\n+        parameter, in which case removal all deferrals.\n+        \"\"\"\n+        self._not_support_combined_queries('defer')\n+        if self._fields is not None:\n+            raise TypeError(\"Cannot call defer() after .values() or .values_list()\")\n+        clone = self._chain()\n+        if fields == (None,):\n+            clone.query.clear_deferred_loading()\n+        else:\n+            clone.query.add_deferred_loading(fields)\n+        return clone\n+\n+    def only(self, *fields):\n+        \"\"\"\n+        Essentially, the opposite of defer(). Only the fields passed into this\n+        method and that are not already specified as deferred are loaded\n+        immediately when the queryset is evaluated.\n+        \"\"\"\n+        self._not_support_combined_queries('only')\n+        if self._fields is not None:\n+            raise TypeError(\"Cannot call only() after .values() or .values_list()\")\n+        if fields == (None,):\n+            # Can only pass None to defer(), not only(), as the rest option.\n+            # That won't stop people trying to do this, so let's be explicit.\n+            raise TypeError(\"Cannot pass None as an argument to only().\")\n+        for field in fields:\n+            field = field.split(LOOKUP_SEP, 1)[0]\n+            if field in self.query._filtered_relations:\n+                raise ValueError('only() is not supported with FilteredRelation.')\n+        clone = self._chain()\n+        clone.query.add_immediate_loading(fields)\n+        return clone\n+\n+    def using(self, alias):\n+        \"\"\"Select which database this QuerySet should execute against.\"\"\"\n+        clone = self._chain()\n+        clone._db = alias\n+        return clone\n+\n+    ###################################\n+    # PUBLIC INTROSPECTION ATTRIBUTES #\n+    ###################################\n+\n+    @property\n+    def ordered(self):\n+        \"\"\"\n+        Return True if the QuerySet is ordered -- i.e. has an order_by()\n+        clause or a default ordering on the model (or is empty).\n+        \"\"\"\n+        if isinstance(self, EmptyQuerySet):\n+            return True\n+        if self.query.extra_order_by or self.query.order_by:\n+            return True\n+        elif self.query.default_ordering and self.query.get_meta().ordering:\n+            return True\n+        else:\n+            return False\n+\n+    @property\n+    def db(self):\n+        \"\"\"Return the database used if this query is executed now.\"\"\"\n+        if self._for_write:\n+            return self._db or router.db_for_write(self.model, **self._hints)\n+        return self._db or router.db_for_read(self.model, **self._hints)\n+\n+    ###################\n+    # PRIVATE METHODS #\n+    ###################\n+\n+    def _insert(self, objs, fields, returning_fields=None, raw=False, using=None, ignore_conflicts=False):\n+        \"\"\"\n+        Insert a new record for the given model. This provides an interface to\n+        the InsertQuery class and is how Model.save() is implemented.\n+        \"\"\"\n+        self._for_write = True\n+        if using is None:\n+            using = self.db\n+        query = sql.InsertQuery(self.model, ignore_conflicts=ignore_conflicts)\n+        query.insert_values(fields, objs, raw=raw)\n+        return query.get_compiler(using=using).execute_sql(returning_fields)\n+    _insert.alters_data = True\n+    _insert.queryset_only = False\n+\n+    def _batched_insert(self, objs, fields, batch_size, ignore_conflicts=False):\n+        \"\"\"\n+        Helper method for bulk_create() to insert objs one batch at a time.\n+        \"\"\"\n+        if ignore_conflicts and not connections[self.db].features.supports_ignore_conflicts:\n+            raise NotSupportedError('This database backend does not support ignoring conflicts.')\n+        ops = connections[self.db].ops\n+        batch_size = (batch_size or max(ops.bulk_batch_size(fields, objs), 1))\n+        inserted_rows = []\n+        bulk_return = connections[self.db].features.can_return_rows_from_bulk_insert\n+        for item in [objs[i:i + batch_size] for i in range(0, len(objs), batch_size)]:\n+            if bulk_return and not ignore_conflicts:\n+                inserted_columns = self._insert(\n+                    item, fields=fields, using=self.db,\n+                    returning_fields=self.model._meta.db_returning_fields,\n+                    ignore_conflicts=ignore_conflicts,\n+                )\n+                if isinstance(inserted_columns, list):\n+                    inserted_rows.extend(inserted_columns)\n+                else:\n+                    inserted_rows.append(inserted_columns)\n+            else:\n+                self._insert(item, fields=fields, using=self.db, ignore_conflicts=ignore_conflicts)\n+        return inserted_rows\n+\n+    def _chain(self, **kwargs):\n+        \"\"\"\n+        Return a copy of the current QuerySet that's ready for another\n+        operation.\n+        \"\"\"\n+        obj = self._clone()\n+        if obj._sticky_filter:\n+            obj.query.filter_is_sticky = True\n+            obj._sticky_filter = False\n+        obj.__dict__.update(kwargs)\n+        return obj\n+\n+    def _clone(self):\n+        \"\"\"\n+        Return a copy of the current QuerySet. A lightweight alternative\n+        to deepcopy().\n+        \"\"\"\n+        c = self.__class__(model=self.model, query=self.query.chain(), using=self._db, hints=self._hints)\n+        c._sticky_filter = self._sticky_filter\n+        c._for_write = self._for_write\n+        c._prefetch_related_lookups = self._prefetch_related_lookups[:]\n+        c._known_related_objects = self._known_related_objects\n+        c._iterable_class = self._iterable_class\n+        c._fields = self._fields\n+        return c\n+\n+    def _fetch_all(self):\n+        if self._result_cache is None:\n+            self._result_cache = list(self._iterable_class(self))\n+        if self._prefetch_related_lookups and not self._prefetch_done:\n+            self._prefetch_related_objects()\n+\n+    def _next_is_sticky(self):\n+        \"\"\"\n+        Indicate that the next filter call and the one following that should\n+        be treated as a single filter. This is only important when it comes to\n+        determining when to reuse tables for many-to-many filters. Required so\n+        that we can filter naturally on the results of related managers.\n+\n+        This doesn't return a clone of the current QuerySet (it returns\n+        \"self\"). The method is only used internally and should be immediately\n+        followed by a filter() that does create a clone.\n+        \"\"\"\n+        self._sticky_filter = True\n+        return self\n+\n+    def _merge_sanity_check(self, other):\n+        \"\"\"Check that two QuerySet classes may be merged.\"\"\"\n+        if self._fields is not None and (\n+                set(self.query.values_select) != set(other.query.values_select) or\n+                set(self.query.extra_select) != set(other.query.extra_select) or\n+                set(self.query.annotation_select) != set(other.query.annotation_select)):\n+            raise TypeError(\n+                \"Merging '%s' classes must involve the same values in each case.\"\n+                % self.__class__.__name__\n+            )\n+\n+    def _merge_known_related_objects(self, other):\n+        \"\"\"\n+        Keep track of all known related objects from either QuerySet instance.\n+        \"\"\"\n+        for field, objects in other._known_related_objects.items():\n+            self._known_related_objects.setdefault(field, {}).update(objects)\n+\n+    def resolve_expression(self, *args, **kwargs):\n+        if self._fields and len(self._fields) > 1:\n+            # values() queryset can only be used as nested queries\n+            # if they are set up to select only a single field.\n+            raise TypeError('Cannot use multi-field values as a filter value.')\n+        query = self.query.resolve_expression(*args, **kwargs)\n+        query._db = self._db\n+        return query\n+    resolve_expression.queryset_only = True\n+\n+    def _add_hints(self, **hints):\n+        \"\"\"\n+        Update hinting information for use by routers. Add new key/values or\n+        overwrite existing key/values.\n+        \"\"\"\n+        self._hints.update(hints)\n+\n+    def _has_filters(self):\n+        \"\"\"\n+        Check if this QuerySet has any filtering going on. This isn't\n+        equivalent with checking if all objects are present in results, for\n+        example, qs[1:]._has_filters() -> False.\n+        \"\"\"\n+        return self.query.has_filters()\n+\n+    @staticmethod\n+    def _validate_values_are_expressions(values, method_name):\n+        invalid_args = sorted(str(arg) for arg in values if not hasattr(arg, 'resolve_expression'))\n+        if invalid_args:\n+            raise TypeError(\n+                'QuerySet.%s() received non-expression(s): %s.' % (\n+                    method_name,\n+                    ', '.join(invalid_args),\n+                )\n+            )\n+\n+    def _not_support_combined_queries(self, operation_name):\n+        if self.query.combinator:\n+            raise NotSupportedError(\n+                'Calling QuerySet.%s() after %s() is not supported.'\n+                % (operation_name, self.query.combinator)\n+            )\n+\n+\n+class InstanceCheckMeta(type):\n+    def __instancecheck__(self, instance):\n+        return isinstance(instance, QuerySet) and instance.query.is_empty()\n+\n+\n+class EmptyQuerySet(metaclass=InstanceCheckMeta):\n+    \"\"\"\n+    Marker class to checking if a queryset is empty by .none():\n+        isinstance(qs.none(), EmptyQuerySet) -> True\n+    \"\"\"\n+\n+    def __init__(self, *args, **kwargs):\n+        raise TypeError(\"EmptyQuerySet can't be instantiated\")\n+\n+\n+class RawQuerySet:\n+    \"\"\"\n+    Provide an iterator which converts the results of raw SQL queries into\n+    annotated model instances.\n+    \"\"\"\n+    def __init__(self, raw_query, model=None, query=None, params=None,\n+                 translations=None, using=None, hints=None):\n+        self.raw_query = raw_query\n+        self.model = model\n+        self._db = using\n+        self._hints = hints or {}\n+        self.query = query or sql.RawQuery(sql=raw_query, using=self.db, params=params)\n+        self.params = params or ()\n+        self.translations = translations or {}\n+        self._result_cache = None\n+        self._prefetch_related_lookups = ()\n+        self._prefetch_done = False\n+\n+    def resolve_model_init_order(self):\n+        \"\"\"Resolve the init field names and value positions.\"\"\"\n+        converter = connections[self.db].introspection.identifier_converter\n+        model_init_fields = [f for f in self.model._meta.fields if converter(f.column) in self.columns]\n+        annotation_fields = [(column, pos) for pos, column in enumerate(self.columns)\n+                             if column not in self.model_fields]\n+        model_init_order = [self.columns.index(converter(f.column)) for f in model_init_fields]\n+        model_init_names = [f.attname for f in model_init_fields]\n+        return model_init_names, model_init_order, annotation_fields\n+\n+    def prefetch_related(self, *lookups):\n+        \"\"\"Same as QuerySet.prefetch_related()\"\"\"\n+        clone = self._clone()\n+        if lookups == (None,):\n+            clone._prefetch_related_lookups = ()\n+        else:\n+            clone._prefetch_related_lookups = clone._prefetch_related_lookups + lookups\n+        return clone\n+\n+    def _prefetch_related_objects(self):\n+        prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)\n+        self._prefetch_done = True\n+\n+    def _clone(self):\n+        \"\"\"Same as QuerySet._clone()\"\"\"\n+        c = self.__class__(\n+            self.raw_query, model=self.model, query=self.query, params=self.params,\n+            translations=self.translations, using=self._db, hints=self._hints\n+        )\n+        c._prefetch_related_lookups = self._prefetch_related_lookups[:]\n+        return c\n+\n+    def _fetch_all(self):\n+        if self._result_cache is None:\n+            self._result_cache = list(self.iterator())\n+        if self._prefetch_related_lookups and not self._prefetch_done:\n+            self._prefetch_related_objects()\n+\n+    def __len__(self):\n+        self._fetch_all()\n+        return len(self._result_cache)\n+\n+    def __bool__(self):\n+        self._fetch_all()\n+        return bool(self._result_cache)\n+\n+    def __iter__(self):\n+        self._fetch_all()\n+        return iter(self._result_cache)\n+\n+    def iterator(self):\n+        # Cache some things for performance reasons outside the loop.\n+        db = self.db\n+        compiler = connections[db].ops.compiler('SQLCompiler')(\n+            self.query, connections[db], db\n+        )\n+\n+        query = iter(self.query)\n+\n+        try:\n+            model_init_names, model_init_pos, annotation_fields = self.resolve_model_init_order()\n+            if self.model._meta.pk.attname not in model_init_names:\n+                raise InvalidQuery('Raw query must include the primary key')\n+            model_cls = self.model\n+            fields = [self.model_fields.get(c) for c in self.columns]\n+            converters = compiler.get_converters([\n+                f.get_col(f.model._meta.db_table) if f else None for f in fields\n+            ])\n+            if converters:\n+                query = compiler.apply_converters(query, converters)\n+            for values in query:\n+                # Associate fields to values\n+                model_init_values = [values[pos] for pos in model_init_pos]\n+                instance = model_cls.from_db(db, model_init_names, model_init_values)\n+                if annotation_fields:\n+                    for column, pos in annotation_fields:\n+                        setattr(instance, column, values[pos])\n+                yield instance\n+        finally:\n+            # Done iterating the Query. If it has its own cursor, close it.\n+            if hasattr(self.query, 'cursor') and self.query.cursor:\n+                self.query.cursor.close()\n+\n+    def __repr__(self):\n+        return \"<%s: %s>\" % (self.__class__.__name__, self.query)\n+\n+    def __getitem__(self, k):\n+        return list(self)[k]\n+\n+    @property\n+    def db(self):\n+        \"\"\"Return the database used if this query is executed now.\"\"\"\n+        return self._db or router.db_for_read(self.model, **self._hints)\n+\n+    def using(self, alias):\n+        \"\"\"Select the database this RawQuerySet should execute against.\"\"\"\n+        return RawQuerySet(\n+            self.raw_query, model=self.model,\n+            query=self.query.chain(using=alias),\n+            params=self.params, translations=self.translations,\n+            using=alias,\n+        )\n+\n+    @cached_property\n+    def columns(self):\n+        \"\"\"\n+        A list of model field names in the order they'll appear in the\n+        query results.\n+        \"\"\"\n+        columns = self.query.get_columns()\n+        # Adjust any column names which don't match field names\n+        for (query_name, model_name) in self.translations.items():\n+            # Ignore translations for nonexistent column names\n+            try:\n+                index = columns.index(query_name)\n+            except ValueError:\n+                pass\n+            else:\n+                columns[index] = model_name\n+        return columns\n+\n+    @cached_property\n+    def model_fields(self):\n+        \"\"\"A dict mapping column names to model field names.\"\"\"\n+        converter = connections[self.db].introspection.identifier_converter\n+        model_fields = {}\n+        for field in self.model._meta.fields:\n+            name, column = field.get_attname_column()\n+            model_fields[converter(column)] = field\n+        return model_fields\n+\n+\n+class Prefetch:\n+    def __init__(self, lookup, queryset=None, to_attr=None):\n+        # `prefetch_through` is the path we traverse to perform the prefetch.\n+        self.prefetch_through = lookup\n+        # `prefetch_to` is the path to the attribute that stores the result.\n+        self.prefetch_to = lookup\n+        if queryset is not None and not issubclass(queryset._iterable_class, ModelIterable):\n+            raise ValueError('Prefetch querysets cannot use values().')\n+        if to_attr:\n+            self.prefetch_to = LOOKUP_SEP.join(lookup.split(LOOKUP_SEP)[:-1] + [to_attr])\n+\n+        self.queryset = queryset\n+        self.to_attr = to_attr\n+\n+    def __getstate__(self):\n+        obj_dict = self.__dict__.copy()\n+        if self.queryset is not None:\n+            # Prevent the QuerySet from being evaluated\n+            obj_dict['queryset'] = self.queryset._chain(\n+                _result_cache=[],\n+                _prefetch_done=True,\n+            )\n+        return obj_dict\n+\n+    def add_prefix(self, prefix):\n+        self.prefetch_through = prefix + LOOKUP_SEP + self.prefetch_through\n+        self.prefetch_to = prefix + LOOKUP_SEP + self.prefetch_to\n+\n+    def get_current_prefetch_to(self, level):\n+        return LOOKUP_SEP.join(self.prefetch_to.split(LOOKUP_SEP)[:level + 1])\n+\n+    def get_current_to_attr(self, level):\n+        parts = self.prefetch_to.split(LOOKUP_SEP)\n+        to_attr = parts[level]\n+        as_attr = self.to_attr and level == len(parts) - 1\n+        return to_attr, as_attr\n+\n+    def get_current_queryset(self, level):\n+        if self.get_current_prefetch_to(level) == self.prefetch_to:\n+            return self.queryset\n+        return None\n+\n+    def __eq__(self, other):\n+        return isinstance(other, Prefetch) and self.prefetch_to == other.prefetch_to\n+\n+    def __hash__(self):\n+        return hash((self.__class__, self.prefetch_to))\n+\n+\n+def normalize_prefetch_lookups(lookups, prefix=None):\n+    \"\"\"Normalize lookups into Prefetch objects.\"\"\"\n+    ret = []\n+    for lookup in lookups:\n+        if not isinstance(lookup, Prefetch):\n+            lookup = Prefetch(lookup)\n+        if prefix:\n+            lookup.add_prefix(prefix)\n+        ret.append(lookup)\n+    return ret\n+\n+\n+def prefetch_related_objects(model_instances, *related_lookups):\n+    \"\"\"\n+    Populate prefetched object caches for a list of model instances based on\n+    the lookups/Prefetch instances given.\n+    \"\"\"\n+    if not model_instances:\n+        return  # nothing to do\n+\n+    # We need to be able to dynamically add to the list of prefetch_related\n+    # lookups that we look up (see below).  So we need some book keeping to\n+    # ensure we don't do duplicate work.\n+    done_queries = {}    # dictionary of things like 'foo__bar': [results]\n+\n+    auto_lookups = set()  # we add to this as we go through.\n+    followed_descriptors = set()  # recursion protection\n+\n+    all_lookups = normalize_prefetch_lookups(reversed(related_lookups))\n+    while all_lookups:\n+        lookup = all_lookups.pop()\n+        if lookup.prefetch_to in done_queries:\n+            if lookup.queryset is not None:\n+                raise ValueError(\"'%s' lookup was already seen with a different queryset. \"\n+                                 \"You may need to adjust the ordering of your lookups.\" % lookup.prefetch_to)\n+\n+            continue\n+\n+        # Top level, the list of objects to decorate is the result cache\n+        # from the primary QuerySet. It won't be for deeper levels.\n+        obj_list = model_instances\n+\n+        through_attrs = lookup.prefetch_through.split(LOOKUP_SEP)\n+        for level, through_attr in enumerate(through_attrs):\n+            # Prepare main instances\n+            if not obj_list:\n+                break\n+\n+            prefetch_to = lookup.get_current_prefetch_to(level)\n+            if prefetch_to in done_queries:\n+                # Skip any prefetching, and any object preparation\n+                obj_list = done_queries[prefetch_to]\n+                continue\n+\n+            # Prepare objects:\n+            good_objects = True\n+            for obj in obj_list:\n+                # Since prefetching can re-use instances, it is possible to have\n+                # the same instance multiple times in obj_list, so obj might\n+                # already be prepared.\n+                if not hasattr(obj, '_prefetched_objects_cache'):\n+                    try:\n+                        obj._prefetched_objects_cache = {}\n+                    except (AttributeError, TypeError):\n+                        # Must be an immutable object from\n+                        # values_list(flat=True), for example (TypeError) or\n+                        # a QuerySet subclass that isn't returning Model\n+                        # instances (AttributeError), either in Django or a 3rd\n+                        # party. prefetch_related() doesn't make sense, so quit.\n+                        good_objects = False\n+                        break\n+            if not good_objects:\n+                break\n+\n+            # Descend down tree\n+\n+            # We assume that objects retrieved are homogeneous (which is the premise\n+            # of prefetch_related), so what applies to first object applies to all.\n+            first_obj = obj_list[0]\n+            to_attr = lookup.get_current_to_attr(level)[0]\n+            prefetcher, descriptor, attr_found, is_fetched = get_prefetcher(first_obj, through_attr, to_attr)\n+\n+            if not attr_found:\n+                raise AttributeError(\"Cannot find '%s' on %s object, '%s' is an invalid \"\n+                                     \"parameter to prefetch_related()\" %\n+                                     (through_attr, first_obj.__class__.__name__, lookup.prefetch_through))\n+\n+            if level == len(through_attrs) - 1 and prefetcher is None:\n+                # Last one, this *must* resolve to something that supports\n+                # prefetching, otherwise there is no point adding it and the\n+                # developer asking for it has made a mistake.\n+                raise ValueError(\"'%s' does not resolve to an item that supports \"\n+                                 \"prefetching - this is an invalid parameter to \"\n+                                 \"prefetch_related().\" % lookup.prefetch_through)\n+\n+            if prefetcher is not None and not is_fetched:\n+                obj_list, additional_lookups = prefetch_one_level(obj_list, prefetcher, lookup, level)\n+                # We need to ensure we don't keep adding lookups from the\n+                # same relationships to stop infinite recursion. So, if we\n+                # are already on an automatically added lookup, don't add\n+                # the new lookups from relationships we've seen already.\n+                if not (prefetch_to in done_queries and lookup in auto_lookups and descriptor in followed_descriptors):\n+                    done_queries[prefetch_to] = obj_list\n+                    new_lookups = normalize_prefetch_lookups(reversed(additional_lookups), prefetch_to)\n+                    auto_lookups.update(new_lookups)\n+                    all_lookups.extend(new_lookups)\n+                followed_descriptors.add(descriptor)\n+            else:\n+                # Either a singly related object that has already been fetched\n+                # (e.g. via select_related), or hopefully some other property\n+                # that doesn't support prefetching but needs to be traversed.\n+\n+                # We replace the current list of parent objects with the list\n+                # of related objects, filtering out empty or missing values so\n+                # that we can continue with nullable or reverse relations.\n+                new_obj_list = []\n+                for obj in obj_list:\n+                    if through_attr in getattr(obj, '_prefetched_objects_cache', ()):\n+                        # If related objects have been prefetched, use the\n+                        # cache rather than the object's through_attr.\n+                        new_obj = list(obj._prefetched_objects_cache.get(through_attr))\n+                    else:\n+                        try:\n+                            new_obj = getattr(obj, through_attr)\n+                        except exceptions.ObjectDoesNotExist:\n+                            continue\n+                    if new_obj is None:\n+                        continue\n+                    # We special-case `list` rather than something more generic\n+                    # like `Iterable` because we don't want to accidentally match\n+                    # user models that define __iter__.\n+                    if isinstance(new_obj, list):\n+                        new_obj_list.extend(new_obj)\n+                    else:\n+                        new_obj_list.append(new_obj)\n+                obj_list = new_obj_list\n+\n+\n+def get_prefetcher(instance, through_attr, to_attr):\n+    \"\"\"\n+    For the attribute 'through_attr' on the given instance, find\n+    an object that has a get_prefetch_queryset().\n+    Return a 4 tuple containing:\n+    (the object with get_prefetch_queryset (or None),\n+     the descriptor object representing this relationship (or None),\n+     a boolean that is False if the attribute was not found at all,\n+     a boolean that is True if the attribute has already been fetched)\n+    \"\"\"\n+    prefetcher = None\n+    is_fetched = False\n+\n+    # For singly related objects, we have to avoid getting the attribute\n+    # from the object, as this will trigger the query. So we first try\n+    # on the class, in order to get the descriptor object.\n+    rel_obj_descriptor = getattr(instance.__class__, through_attr, None)\n+    if rel_obj_descriptor is None:\n+        attr_found = hasattr(instance, through_attr)\n+    else:\n+        attr_found = True\n+        if rel_obj_descriptor:\n+            # singly related object, descriptor object has the\n+            # get_prefetch_queryset() method.\n+            if hasattr(rel_obj_descriptor, 'get_prefetch_queryset'):\n+                prefetcher = rel_obj_descriptor\n+                if rel_obj_descriptor.is_cached(instance):\n+                    is_fetched = True\n+            else:\n+                # descriptor doesn't support prefetching, so we go ahead and get\n+                # the attribute on the instance rather than the class to\n+                # support many related managers\n+                rel_obj = getattr(instance, through_attr)\n+                if hasattr(rel_obj, 'get_prefetch_queryset'):\n+                    prefetcher = rel_obj\n+                if through_attr != to_attr:\n+                    # Special case cached_property instances because hasattr\n+                    # triggers attribute computation and assignment.\n+                    if isinstance(getattr(instance.__class__, to_attr, None), cached_property):\n+                        is_fetched = to_attr in instance.__dict__\n+                    else:\n+                        is_fetched = hasattr(instance, to_attr)\n+                else:\n+                    is_fetched = through_attr in instance._prefetched_objects_cache\n+    return prefetcher, rel_obj_descriptor, attr_found, is_fetched\n+\n+\n+def prefetch_one_level(instances, prefetcher, lookup, level):\n+    \"\"\"\n+    Helper function for prefetch_related_objects().\n+\n+    Run prefetches on all instances using the prefetcher object,\n+    assigning results to relevant caches in instance.\n+\n+    Return the prefetched objects along with any additional prefetches that\n+    must be done due to prefetch_related lookups found from default managers.\n+    \"\"\"\n+    # prefetcher must have a method get_prefetch_queryset() which takes a list\n+    # of instances, and returns a tuple:\n+\n+    # (queryset of instances of self.model that are related to passed in instances,\n+    #  callable that gets value to be matched for returned instances,\n+    #  callable that gets value to be matched for passed in instances,\n+    #  boolean that is True for singly related objects,\n+    #  cache or field name to assign to,\n+    #  boolean that is True when the previous argument is a cache name vs a field name).\n+\n+    # The 'values to be matched' must be hashable as they will be used\n+    # in a dictionary.\n+\n+    rel_qs, rel_obj_attr, instance_attr, single, cache_name, is_descriptor = (\n+        prefetcher.get_prefetch_queryset(instances, lookup.get_current_queryset(level)))\n+    # We have to handle the possibility that the QuerySet we just got back\n+    # contains some prefetch_related lookups. We don't want to trigger the\n+    # prefetch_related functionality by evaluating the query. Rather, we need\n+    # to merge in the prefetch_related lookups.\n+    # Copy the lookups in case it is a Prefetch object which could be reused\n+    # later (happens in nested prefetch_related).\n+    additional_lookups = [\n+        copy.copy(additional_lookup) for additional_lookup\n+        in getattr(rel_qs, '_prefetch_related_lookups', ())\n+    ]\n+    if additional_lookups:\n+        # Don't need to clone because the manager should have given us a fresh\n+        # instance, so we access an internal instead of using public interface\n+        # for performance reasons.\n+        rel_qs._prefetch_related_lookups = ()\n+\n+    all_related_objects = list(rel_qs)\n+\n+    rel_obj_cache = {}\n+    for rel_obj in all_related_objects:\n+        rel_attr_val = rel_obj_attr(rel_obj)\n+        rel_obj_cache.setdefault(rel_attr_val, []).append(rel_obj)\n+\n+    to_attr, as_attr = lookup.get_current_to_attr(level)\n+    # Make sure `to_attr` does not conflict with a field.\n+    if as_attr and instances:\n+        # We assume that objects retrieved are homogeneous (which is the premise\n+        # of prefetch_related), so what applies to first object applies to all.\n+        model = instances[0].__class__\n+        try:\n+            model._meta.get_field(to_attr)\n+        except exceptions.FieldDoesNotExist:\n+            pass\n+        else:\n+            msg = 'to_attr={} conflicts with a field on the {} model.'\n+            raise ValueError(msg.format(to_attr, model.__name__))\n+\n+    # Whether or not we're prefetching the last part of the lookup.\n+    leaf = len(lookup.prefetch_through.split(LOOKUP_SEP)) - 1 == level\n+\n+    for obj in instances:\n+        instance_attr_val = instance_attr(obj)\n+        vals = rel_obj_cache.get(instance_attr_val, [])\n+\n+        if single:\n+            val = vals[0] if vals else None\n+            if as_attr:\n+                # A to_attr has been given for the prefetch.\n+                setattr(obj, to_attr, val)\n+            elif is_descriptor:\n+                # cache_name points to a field name in obj.\n+                # This field is a descriptor for a related object.\n+                setattr(obj, cache_name, val)\n+            else:\n+                # No to_attr has been given for this prefetch operation and the\n+                # cache_name does not point to a descriptor. Store the value of\n+                # the field in the object's field cache.\n+                obj._state.fields_cache[cache_name] = val\n+        else:\n+            if as_attr:\n+                setattr(obj, to_attr, vals)\n+            else:\n+                manager = getattr(obj, to_attr)\n+                if leaf and lookup.queryset is not None:\n+                    qs = manager._apply_rel_filters(lookup.queryset)\n+                else:\n+                    qs = manager.get_queryset()\n+                qs._result_cache = vals\n+                # We don't want the individual qs doing prefetch_related now,\n+                # since we have merged this into the current work.\n+                qs._prefetch_done = True\n+                obj._prefetched_objects_cache[cache_name] = qs\n+    return all_related_objects, additional_lookups\n+\n+\n+class RelatedPopulator:\n+    \"\"\"\n+    RelatedPopulator is used for select_related() object instantiation.\n+\n+    The idea is that each select_related() model will be populated by a\n+    different RelatedPopulator instance. The RelatedPopulator instances get\n+    klass_info and select (computed in SQLCompiler) plus the used db as\n+    input for initialization. That data is used to compute which columns\n+    to use, how to instantiate the model, and how to populate the links\n+    between the objects.\n+\n+    The actual creation of the objects is done in populate() method. This\n+    method gets row and from_obj as input and populates the select_related()\n+    model instance.\n+    \"\"\"\n+    def __init__(self, klass_info, select, db):\n+        self.db = db\n+        # Pre-compute needed attributes. The attributes are:\n+        #  - model_cls: the possibly deferred model class to instantiate\n+        #  - either:\n+        #    - cols_start, cols_end: usually the columns in the row are\n+        #      in the same order model_cls.__init__ expects them, so we\n+        #      can instantiate by model_cls(*row[cols_start:cols_end])\n+        #    - reorder_for_init: When select_related descends to a child\n+        #      class, then we want to reuse the already selected parent\n+        #      data. However, in this case the parent data isn't necessarily\n+        #      in the same order that Model.__init__ expects it to be, so\n+        #      we have to reorder the parent data. The reorder_for_init\n+        #      attribute contains a function used to reorder the field data\n+        #      in the order __init__ expects it.\n+        #  - pk_idx: the index of the primary key field in the reordered\n+        #    model data. Used to check if a related object exists at all.\n+        #  - init_list: the field attnames fetched from the database. For\n+        #    deferred models this isn't the same as all attnames of the\n+        #    model's fields.\n+        #  - related_populators: a list of RelatedPopulator instances if\n+        #    select_related() descends to related models from this model.\n+        #  - local_setter, remote_setter: Methods to set cached values on\n+        #    the object being populated and on the remote object. Usually\n+        #    these are Field.set_cached_value() methods.\n+        select_fields = klass_info['select_fields']\n+        from_parent = klass_info['from_parent']\n+        if not from_parent:\n+            self.cols_start = select_fields[0]\n+            self.cols_end = select_fields[-1] + 1\n+            self.init_list = [\n+                f[0].target.attname for f in select[self.cols_start:self.cols_end]\n+            ]\n+            self.reorder_for_init = None\n+        else:\n+            attname_indexes = {select[idx][0].target.attname: idx for idx in select_fields}\n+            model_init_attnames = (f.attname for f in klass_info['model']._meta.concrete_fields)\n+            self.init_list = [attname for attname in model_init_attnames if attname in attname_indexes]\n+            self.reorder_for_init = operator.itemgetter(*[attname_indexes[attname] for attname in self.init_list])\n+\n+        self.model_cls = klass_info['model']\n+        self.pk_idx = self.init_list.index(self.model_cls._meta.pk.attname)\n+        self.related_populators = get_related_populators(klass_info, select, self.db)\n+        self.local_setter = klass_info['local_setter']\n+        self.remote_setter = klass_info['remote_setter']\n+\n+    def populate(self, row, from_obj):\n+        if self.reorder_for_init:\n+            obj_data = self.reorder_for_init(row)\n+        else:\n+            obj_data = row[self.cols_start:self.cols_end]\n+        if obj_data[self.pk_idx] is None:\n+            obj = None\n+        else:\n+            obj = self.model_cls.from_db(self.db, self.init_list, obj_data)\n+            for rel_iter in self.related_populators:\n+                rel_iter.populate(row, obj)\n+        self.local_setter(from_obj, obj)\n+        if obj is not None:\n+            self.remote_setter(obj, from_obj)\n+\n+\n+def get_related_populators(klass_info, select, db):\n+    iterators = []\n+    related_klass_infos = klass_info.get('related_klass_infos', [])\n+    for rel_klass_info in related_klass_infos:\n+        rel_cls = RelatedPopulator(rel_klass_info, select, db)\n+        iterators.append(rel_cls)\n+    return iterators\ndiff --git a/django/db/models/query.py.new b/django/db/models/query.py.new\nnew file mode 100644\nindex 0000000000..3ac962d3e9\n--- /dev/null\n+++ b/django/db/models/query.py.new\n@@ -0,0 +1,177 @@\n+\"\"\"\n+The main QuerySet implementation. This provides the public API for the ORM.\n+\"\"\"\n+\n+import copy\n+import operator\n+import warnings\n+from collections import namedtuple\n+from functools import lru_cache\n+from itertools import chain\n+\n+from django.conf import settings\n+from django.core import exceptions\n+from django.db import (\n+    DJANGO_VERSION_PICKLE_KEY, IntegrityError, NotSupportedError, connections,\n+    router, transaction,\n+)\n+from django.db.models import AutoField, DateField, DateTimeField, sql\n+from django.db.models.constants import LOOKUP_SEP\n+from django.db.models.deletion import Collector\n+from django.db.models.expressions import Case, Expression, F, Value, When\n+from django.db.models.functions import Cast, Trunc\n+from django.db.models.query_utils import FilteredRelation, Q\n+from django.db.models.sql.constants import CURSOR, GET_ITERATOR_CHUNK_SIZE\n+from django.db.models.utils import resolve_callables\n+from django.utils import timezone\n+from django.utils.functional import cached_property, partition\n+from django.utils.version import get_version\n+\n+# The maximum number of results to fetch in a get() query.\n+MAX_GET_RESULTS = 21\n+\n+# The maximum number of items to display in a QuerySet.__repr__\n+REPR_OUTPUT_SIZE = 20\n+\n+\n+class BaseIterable:\n+    def __init__(self, queryset, chunked_fetch=False, chunk_size=GET_ITERATOR_CHUNK_SIZE):\n+        self.queryset = queryset\n+        self.chunked_fetch = chunked_fetch\n+        self.chunk_size = chunk_size\n+\n+\n+class ModelIterable(BaseIterable):\n+    \"\"\"Iterable that yields a model instance for each row.\"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        db = queryset.db\n+        compiler = queryset.query.get_compiler(using=db)\n+        # Execute the query. This will also fill compiler.select, klass_info,\n+        # and annotations.\n+        results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)\n+        select, klass_info, annotation_col_map = (compiler.select, compiler.klass_info,\n+                                                  compiler.annotation_col_map)\n+        model_cls = klass_info['model']\n+        select_fields = klass_info['select_fields']\n+        model_fields_start, model_fields_end = select_fields[0], select_fields[-1] + 1\n+        init_list = [f[0].target.attname\n+                     for f in select[model_fields_start:model_fields_end]]\n+        model_init_args = [\n+            f[0].target.attname\n+            for f in select[model_fields_start:model_fields_end]\n+        ]\n+        for row in compiler.results_iter(results):\n+            obj = model_cls.from_db(db, model_init_args, row[model_fields_start:model_fields_end])\n+            for annotation in annotation_col_map:\n+                setattr(obj, annotation, row[annotation_col_map[annotation]])\n+            yield obj\n+\n+\n+class ValuesIterable(BaseIterable):\n+    \"\"\"\n+    Iterable returned by QuerySet.values() that yields a dict for each row.\n+    \"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        query = queryset.query\n+        compiler = query.get_compiler(queryset.db)\n+        field_names = list(query.values_select)\n+        extra_names = list(query.extra_select)\n+        annotation_names = list(query.annotation_select)\n+        names = extra_names + field_names + annotation_names\n+        for row in compiler.results_iter(chunked_fetch=self.chunked_fetch):\n+            yield {name: val for name, val in zip(names, row)}\n+\n+\n+class QuerySet:\n+    \"\"\"\n+    Represents a lazy database lookup for a set of objects.\n+    \"\"\"\n+    def __init__(self, model=None, query=None, using=None, hints=None):\n+        self.model = model\n+        self._db = using\n+        self._hints = hints or {}\n+        self._query = query or sql.Query(self.model)\n+        self._result_cache = None\n+        self._sticky_filter = False\n+        self._for_write = False\n+        self._prefetch_related_lookups = ()\n+        self._prefetch_done = False\n+        self._known_related_objects = {}  # {rel_field: {pk: rel_obj}}\n+        self._iterable_class = ModelIterable\n+        self._fields = None\n+        self._deferred_filter = None\n+\n+    @property\n+    def query(self):\n+        if self._deferred_filter:\n+            negate, args, kwargs = self._deferred_filter\n+            self._filter_or_exclude_inplace(negate, *args, **kwargs)\n+            self._deferred_filter = None\n+        return self._query\n+\n+    @query.setter\n+    def query(self, value):\n+        self._query = value\n+\n+    @property\n+    def db(self):\n+        \"Return the database that will be used if this query is executed now\"\n+        if self._for_write:\n+            return self._db or router.db_for_write(self.model, **self._hints)\n+        return self._db or router.db_for_read(self.model, **self._hints)\n+        \n+    def in_bulk(self, id_list=None, *, field_name='pk'):\n+        \"\"\"\n+        Return a dictionary mapping each of the given IDs to the object with\n+        that ID. If `id_list` isn't provided, evaluate the entire QuerySet.\n+        \"\"\"\n+        assert not self.query.is_sliced, \\\n+            \"Cannot use 'limit' or 'offset' with in_bulk\"\n+        \n+        opts = self.model._meta\n+        field = opts.get_field(field_name)\n+\n+        # Check if the field is unique either through unique=True or UniqueConstraint\n+        is_unique = (\n+            field.unique or \n+            any(\n+                isinstance(constraint, sql.UniqueConstraint) and \n+                len(constraint.fields) == 1 and\n+                constraint.fields[0] == field_name\n+                for constraint in opts.constraints\n+            )\n+        )\n+\n+        if field_name != 'pk' and not is_unique:\n+            raise ValueError(\"in_bulk()'s field_name must be a unique field but %r isn't.\" % field_name)\n+\n+        if id_list is not None:\n+            if not id_list:\n+                return {}\n+            filter_key = '{}__in'.format(field_name)\n+            batch_size = connections[self.db].features.max_query_params\n+            id_list = tuple(id_list)\n+            # If the database has a limit on the number of query parameters\n+            # split the values into batches and merge the results together.\n+            if batch_size and batch_size < len(id_list):\n+                qs = []\n+                for offset in range(0, len(id_list), batch_size):\n+                    batch = id_list[offset:offset + batch_size]\n+                    qs.extend(self.filter(**{filter_key: batch}).order_by())\n+                qs = {getattr(obj, field_name): obj for obj in qs}\n+            else:\n+                qs = {getattr(obj, field_name): obj for obj in self.filter(**{filter_key: id_list}).order_by()}\n+        else:\n+            qs = {getattr(obj, field_name): obj for obj in self.order_by()}\n+        return qs\n+\n+    def count(self):\n+        \"\"\"\n+        Performs a SELECT COUNT() and returns the number of records as an\n+        integer.\n+        \"\"\"\n+        return self.query.get_count(using=self.db)\n\\ No newline at end of file\ndiff --git a/django/db/models/query.py.orig b/django/db/models/query.py.orig\nnew file mode 100644\nindex 0000000000..175073b961\n--- /dev/null\n+++ b/django/db/models/query.py.orig\n@@ -0,0 +1,2685 @@\n+\"\"\"\n+The main QuerySet implementation. This provides the public API for the ORM.\n+\"\"\"\n+\n+import copy\n+import operator\n+import warnings\n+from itertools import chain, islice\n+\n+from asgiref.sync import sync_to_async\n+\n+import django\n+from django.conf import settings\n+from django.core import exceptions\n+from django.db import (\n+    DJANGO_VERSION_PICKLE_KEY,\n+    IntegrityError,\n+    NotSupportedError,\n+    connections,\n+    router,\n+    transaction,\n+)\n+from django.db.models import AutoField, DateField, DateTimeField, Field, sql\n+from django.db.models.constants import LOOKUP_SEP, OnConflict\n+from django.db.models.deletion import Collector\n+from django.db.models.expressions import Case, F, Value, When\n+from django.db.models.functions import Cast, Trunc\n+from django.db.models.query_utils import FilteredRelation, Q\n+from django.db.models.sql.constants import GET_ITERATOR_CHUNK_SIZE, ROW_COUNT\n+from django.db.models.utils import (\n+    AltersData,\n+    create_namedtuple_class,\n+    resolve_callables,\n+)\n+from django.utils import timezone\n+from django.utils.functional import cached_property, partition\n+\n+# The maximum number of results to fetch in a get() query.\n+MAX_GET_RESULTS = 21\n+\n+# The maximum number of items to display in a QuerySet.__repr__\n+REPR_OUTPUT_SIZE = 20\n+\n+\n+class BaseIterable:\n+    def __init__(\n+        self, queryset, chunked_fetch=False, chunk_size=GET_ITERATOR_CHUNK_SIZE\n+    ):\n+        self.queryset = queryset\n+        self.chunked_fetch = chunked_fetch\n+        self.chunk_size = chunk_size\n+\n+    async def _async_generator(self):\n+        # Generators don't actually start running until the first time you call\n+        # next() on them, so make the generator object in the async thread and\n+        # then repeatedly dispatch to it in a sync thread.\n+        sync_generator = self.__iter__()\n+\n+        def next_slice(gen):\n+            return list(islice(gen, self.chunk_size))\n+\n+        while True:\n+            chunk = await sync_to_async(next_slice)(sync_generator)\n+            for item in chunk:\n+                yield item\n+            if len(chunk) < self.chunk_size:\n+                break\n+\n+    # __aiter__() is a *synchronous* method that has to then return an\n+    # *asynchronous* iterator/generator. Thus, nest an async generator inside\n+    # it.\n+    # This is a generic iterable converter for now, and is going to suffer a\n+    # performance penalty on large sets of items due to the cost of crossing\n+    # over the sync barrier for each chunk. Custom __aiter__() methods should\n+    # be added to each Iterable subclass, but that needs some work in the\n+    # Compiler first.\n+    def __aiter__(self):\n+        return self._async_generator()\n+\n+\n+class ModelIterable(BaseIterable):\n+    \"\"\"Iterable that yields a model instance for each row.\"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        db = queryset.db\n+        compiler = queryset.query.get_compiler(using=db)\n+        # Execute the query. This will also fill compiler.select, klass_info,\n+        # and annotations.\n+        results = compiler.execute_sql(\n+            chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size\n+        )\n+        select, klass_info, annotation_col_map = (\n+            compiler.select,\n+            compiler.klass_info,\n+            compiler.annotation_col_map,\n+        )\n+        model_cls = klass_info[\"model\"]\n+        select_fields = klass_info[\"select_fields\"]\n+        model_fields_start, model_fields_end = select_fields[0], select_fields[-1] + 1\n+        init_list = [\n+            f[0].target.attname for f in select[model_fields_start:model_fields_end]\n+        ]\n+        related_populators = get_related_populators(klass_info, select, db)\n+        known_related_objects = [\n+            (\n+                field,\n+                related_objs,\n+                operator.attrgetter(\n+                    *[\n+                        (\n+                            field.attname\n+                            if from_field == \"self\"\n+                            else queryset.model._meta.get_field(from_field).attname\n+                        )\n+                        for from_field in field.from_fields\n+                    ]\n+                ),\n+            )\n+            for field, related_objs in queryset._known_related_objects.items()\n+        ]\n+        for row in compiler.results_iter(results):\n+            obj = model_cls.from_db(\n+                db, init_list, row[model_fields_start:model_fields_end]\n+            )\n+            for rel_populator in related_populators:\n+                rel_populator.populate(row, obj)\n+            if annotation_col_map:\n+                for attr_name, col_pos in annotation_col_map.items():\n+                    setattr(obj, attr_name, row[col_pos])\n+\n+            # Add the known related objects to the model.\n+            for field, rel_objs, rel_getter in known_related_objects:\n+                # Avoid overwriting objects loaded by, e.g., select_related().\n+                if field.is_cached(obj):\n+                    continue\n+                rel_obj_id = rel_getter(obj)\n+                try:\n+                    rel_obj = rel_objs[rel_obj_id]\n+                except KeyError:\n+                    pass  # May happen in qs1 | qs2 scenarios.\n+                else:\n+                    setattr(obj, field.name, rel_obj)\n+\n+            yield obj\n+\n+\n+class RawModelIterable(BaseIterable):\n+    \"\"\"\n+    Iterable that yields a model instance for each row from a raw queryset.\n+    \"\"\"\n+\n+    def __iter__(self):\n+        # Cache some things for performance reasons outside the loop.\n+        db = self.queryset.db\n+        query = self.queryset.query\n+        connection = connections[db]\n+        compiler = connection.ops.compiler(\"SQLCompiler\")(query, connection, db)\n+        query_iterator = iter(query)\n+\n+        try:\n+            (\n+                model_init_names,\n+                model_init_pos,\n+                annotation_fields,\n+            ) = self.queryset.resolve_model_init_order()\n+            model_cls = self.queryset.model\n+            if model_cls._meta.pk.attname not in model_init_names:\n+                raise exceptions.FieldDoesNotExist(\n+                    \"Raw query must include the primary key\"\n+                )\n+            fields = [self.queryset.model_fields.get(c) for c in self.queryset.columns]\n+            cols = [f.get_col(f.model._meta.db_table) if f else None for f in fields]\n+            converters = compiler.get_converters(cols)\n+            if converters:\n+                query_iterator = compiler.apply_converters(query_iterator, converters)\n+            if compiler.has_composite_fields(cols):\n+                query_iterator = compiler.composite_fields_to_tuples(\n+                    query_iterator, cols\n+                )\n+            for values in query_iterator:\n+                # Associate fields to values\n+                model_init_values = [values[pos] for pos in model_init_pos]\n+                instance = model_cls.from_db(db, model_init_names, model_init_values)\n+                if annotation_fields:\n+                    for column, pos in annotation_fields:\n+                        setattr(instance, column, values[pos])\n+                yield instance\n+        finally:\n+            # Done iterating the Query. If it has its own cursor, close it.\n+            if hasattr(query, \"cursor\") and query.cursor:\n+                query.cursor.close()\n+\n+\n+class ValuesIterable(BaseIterable):\n+    \"\"\"\n+    Iterable returned by QuerySet.values() that yields a dict for each row.\n+    \"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        query = queryset.query\n+        compiler = query.get_compiler(queryset.db)\n+\n+        if query.selected:\n+            names = list(query.selected)\n+        else:\n+            # extra(select=...) cols are always at the start of the row.\n+            names = [\n+                *query.extra_select,\n+                *query.values_select,\n+                *query.annotation_select,\n+            ]\n+        indexes = range(len(names))\n+        for row in compiler.results_iter(\n+            chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size\n+        ):\n+            yield {names[i]: row[i] for i in indexes}\n+\n+\n+class ValuesListIterable(BaseIterable):\n+    \"\"\"\n+    Iterable returned by QuerySet.values_list(flat=False) that yields a tuple\n+    for each row.\n+    \"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        query = queryset.query\n+        compiler = query.get_compiler(queryset.db)\n+        return compiler.results_iter(\n+            tuple_expected=True,\n+            chunked_fetch=self.chunked_fetch,\n+            chunk_size=self.chunk_size,\n+        )\n+\n+\n+class NamedValuesListIterable(ValuesListIterable):\n+    \"\"\"\n+    Iterable returned by QuerySet.values_list(named=True) that yields a\n+    namedtuple for each row.\n+    \"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        if queryset._fields:\n+            names = queryset._fields\n+        else:\n+            query = queryset.query\n+            names = [\n+                *query.extra_select,\n+                *query.values_select,\n+                *query.annotation_select,\n+            ]\n+        tuple_class = create_namedtuple_class(*names)\n+        new = tuple.__new__\n+        for row in super().__iter__():\n+            yield new(tuple_class, row)\n+\n+\n+class FlatValuesListIterable(BaseIterable):\n+    \"\"\"\n+    Iterable returned by QuerySet.values_list(flat=True) that yields single\n+    values.\n+    \"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        compiler = queryset.query.get_compiler(queryset.db)\n+        for row in compiler.results_iter(\n+            chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size\n+        ):\n+            yield row[0]\n+\n+\n+class QuerySet(AltersData):\n+    \"\"\"Represent a lazy database lookup for a set of objects.\"\"\"\n+\n+    def __init__(self, model=None, query=None, using=None, hints=None):\n+        self.model = model\n+        self._db = using\n+        self._hints = hints or {}\n+        self._query = query or sql.Query(self.model)\n+        self._result_cache = None\n+        self._sticky_filter = False\n+        self._for_write = False\n+        self._prefetch_related_lookups = ()\n+        self._prefetch_done = False\n+        self._known_related_objects = {}  # {rel_field: {pk: rel_obj}}\n+        self._iterable_class = ModelIterable\n+        self._fields = None\n+        self._defer_next_filter = False\n+        self._deferred_filter = None\n+\n+    @property\n+    def query(self):\n+        if self._deferred_filter:\n+            negate, args, kwargs = self._deferred_filter\n+            self._filter_or_exclude_inplace(negate, args, kwargs)\n+            self._deferred_filter = None\n+        return self._query\n+\n+    @query.setter\n+    def query(self, value):\n+        if value.values_select:\n+            self._iterable_class = ValuesIterable\n+        self._query = value\n+\n+    def as_manager(cls):\n+        # Address the circular dependency between `Queryset` and `Manager`.\n+        from django.db.models.manager import Manager\n+\n+        manager = Manager.from_queryset(cls)()\n+        manager._built_with_as_manager = True\n+        return manager\n+\n+    as_manager.queryset_only = True\n+    as_manager = classmethod(as_manager)\n+\n+    ########################\n+    # PYTHON MAGIC METHODS #\n+    ########################\n+\n+    def __deepcopy__(self, memo):\n+        \"\"\"Don't populate the QuerySet's cache.\"\"\"\n+        obj = self.__class__()\n+        for k, v in self.__dict__.items():\n+            if k == \"_result_cache\":\n+                obj.__dict__[k] = None\n+            else:\n+                obj.__dict__[k] = copy.deepcopy(v, memo)\n+        return obj\n+\n+    def __getstate__(self):\n+        # Force the cache to be fully populated.\n+        self._fetch_all()\n+        return {**self.__dict__, DJANGO_VERSION_PICKLE_KEY: django.__version__}\n+\n+    def __setstate__(self, state):\n+        pickled_version = state.get(DJANGO_VERSION_PICKLE_KEY)\n+        if pickled_version:\n+            if pickled_version != django.__version__:\n+                warnings.warn(\n+                    \"Pickled queryset instance's Django version %s does not \"\n+                    \"match the current version %s.\"\n+                    % (pickled_version, django.__version__),\n+                    RuntimeWarning,\n+                    stacklevel=2,\n+                )\n+        else:\n+            warnings.warn(\n+                \"Pickled queryset instance's Django version is not specified.\",\n+                RuntimeWarning,\n+                stacklevel=2,\n+            )\n+        self.__dict__.update(state)\n+\n+    def __repr__(self):\n+        data = list(self[: REPR_OUTPUT_SIZE + 1])\n+        if len(data) > REPR_OUTPUT_SIZE:\n+            data[-1] = \"...(remaining elements truncated)...\"\n+        return \"<%s %r>\" % (self.__class__.__name__, data)\n+\n+    def __len__(self):\n+        self._fetch_all()\n+        return len(self._result_cache)\n+\n+    def __iter__(self):\n+        \"\"\"\n+        The queryset iterator protocol uses three nested iterators in the\n+        default case:\n+            1. sql.compiler.execute_sql()\n+               - Returns 100 rows at time (constants.GET_ITERATOR_CHUNK_SIZE)\n+                 using cursor.fetchmany(). This part is responsible for\n+                 doing some column masking, and returning the rows in chunks.\n+            2. sql.compiler.results_iter()\n+               - Returns one row at time. At this point the rows are still just\n+                 tuples. In some cases the return values are converted to\n+                 Python values at this location.\n+            3. self.iterator()\n+               - Responsible for turning the rows into model objects.\n+        \"\"\"\n+        self._fetch_all()\n+        return iter(self._result_cache)\n+\n+    def __aiter__(self):\n+        # Remember, __aiter__ itself is synchronous, it's the thing it returns\n+        # that is async!\n+        async def generator():\n+            await sync_to_async(self._fetch_all)()\n+            for item in self._result_cache:\n+                yield item\n+\n+        return generator()\n+\n+    def __bool__(self):\n+        self._fetch_all()\n+        return bool(self._result_cache)\n+\n+    def __getitem__(self, k):\n+        \"\"\"Retrieve an item or slice from the set of results.\"\"\"\n+        if not isinstance(k, (int, slice)):\n+            raise TypeError(\n+                \"QuerySet indices must be integers or slices, not %s.\"\n+                % type(k).__name__\n+            )\n+        if (isinstance(k, int) and k < 0) or (\n+            isinstance(k, slice)\n+            and (\n+                (k.start is not None and k.start < 0)\n+                or (k.stop is not None and k.stop < 0)\n+            )\n+        ):\n+            raise ValueError(\"Negative indexing is not supported.\")\n+\n+        if self._result_cache is not None:\n+            return self._result_cache[k]\n+\n+        if isinstance(k, slice):\n+            qs = self._chain()\n+            if k.start is not None:\n+                start = int(k.start)\n+            else:\n+                start = None\n+            if k.stop is not None:\n+                stop = int(k.stop)\n+            else:\n+                stop = None\n+            qs.query.set_limits(start, stop)\n+            return list(qs)[:: k.step] if k.step else qs\n+\n+        qs = self._chain()\n+        qs.query.set_limits(k, k + 1)\n+        qs._fetch_all()\n+        return qs._result_cache[0]\n+\n+    def __class_getitem__(cls, *args, **kwargs):\n+        return cls\n+\n+    def __and__(self, other):\n+        self._check_operator_queryset(other, \"&\")\n+        self._merge_sanity_check(other)\n+        if isinstance(other, EmptyQuerySet):\n+            return other\n+        if isinstance(self, EmptyQuerySet):\n+            return self\n+        combined = self._chain()\n+        combined._merge_known_related_objects(other)\n+        combined.query.combine(other.query, sql.AND)\n+        return combined\n+\n+    def __or__(self, other):\n+        self._check_operator_queryset(other, \"|\")\n+        self._merge_sanity_check(other)\n+        if isinstance(self, EmptyQuerySet):\n+            return other\n+        if isinstance(other, EmptyQuerySet):\n+            return self\n+        query = (\n+            self\n+            if self.query.can_filter()\n+            else self.model._base_manager.filter(pk__in=self.values(\"pk\"))\n+        )\n+        combined = query._chain()\n+        combined._merge_known_related_objects(other)\n+        if not other.query.can_filter():\n+            other = other.model._base_manager.filter(pk__in=other.values(\"pk\"))\n+        combined.query.combine(other.query, sql.OR)\n+        return combined\n+\n+    def __xor__(self, other):\n+        self._check_operator_queryset(other, \"^\")\n+        self._merge_sanity_check(other)\n+        if isinstance(self, EmptyQuerySet):\n+            return other\n+        if isinstance(other, EmptyQuerySet):\n+            return self\n+        query = (\n+            self\n+            if self.query.can_filter()\n+            else self.model._base_manager.filter(pk__in=self.values(\"pk\"))\n+        )\n+        combined = query._chain()\n+        combined._merge_known_related_objects(other)\n+        if not other.query.can_filter():\n+            other = other.model._base_manager.filter(pk__in=other.values(\"pk\"))\n+        combined.query.combine(other.query, sql.XOR)\n+        return combined\n+\n+    ####################################\n+    # METHODS THAT DO DATABASE QUERIES #\n+    ####################################\n+\n+    def _iterator(self, use_chunked_fetch, chunk_size):\n+        iterable = self._iterable_class(\n+            self,\n+            chunked_fetch=use_chunked_fetch,\n+            chunk_size=chunk_size or 2000,\n+        )\n+        if not self._prefetch_related_lookups or chunk_size is None:\n+            yield from iterable\n+            return\n+\n+        iterator = iter(iterable)\n+        while results := list(islice(iterator, chunk_size)):\n+            prefetch_related_objects(results, *self._prefetch_related_lookups)\n+            yield from results\n+\n+    def iterator(self, chunk_size=None):\n+        \"\"\"\n+        An iterator over the results from applying this QuerySet to the\n+        database. chunk_size must be provided for QuerySets that prefetch\n+        related objects. Otherwise, a default chunk_size of 2000 is supplied.\n+        \"\"\"\n+        if chunk_size is None:\n+            if self._prefetch_related_lookups:\n+                raise ValueError(\n+                    \"chunk_size must be provided when using QuerySet.iterator() after \"\n+                    \"prefetch_related().\"\n+                )\n+        elif chunk_size <= 0:\n+            raise ValueError(\"Chunk size must be strictly positive.\")\n+        use_chunked_fetch = not connections[self.db].settings_dict.get(\n+            \"DISABLE_SERVER_SIDE_CURSORS\"\n+        )\n+        return self._iterator(use_chunked_fetch, chunk_size)\n+\n+    async def aiterator(self, chunk_size=2000):\n+        \"\"\"\n+        An asynchronous iterator over the results from applying this QuerySet\n+        to the database.\n+        \"\"\"\n+        if chunk_size <= 0:\n+            raise ValueError(\"Chunk size must be strictly positive.\")\n+        use_chunked_fetch = not connections[self.db].settings_dict.get(\n+            \"DISABLE_SERVER_SIDE_CURSORS\"\n+        )\n+        iterable = self._iterable_class(\n+            self, chunked_fetch=use_chunked_fetch, chunk_size=chunk_size\n+        )\n+        if self._prefetch_related_lookups:\n+            results = []\n+\n+            async for item in iterable:\n+                results.append(item)\n+                if len(results) >= chunk_size:\n+                    await aprefetch_related_objects(\n+                        results, *self._prefetch_related_lookups\n+                    )\n+                    for result in results:\n+                        yield result\n+                    results.clear()\n+\n+            if results:\n+                await aprefetch_related_objects(\n+                    results, *self._prefetch_related_lookups\n+                )\n+                for result in results:\n+                    yield result\n+        else:\n+            async for item in iterable:\n+                yield item\n+\n+    def aggregate(self, *args, **kwargs):\n+        \"\"\"\n+        Return a dictionary containing the calculations (aggregation)\n+        over the current queryset.\n+\n+        If args is present the expression is passed as a kwarg using\n+        the Aggregate object's default alias.\n+        \"\"\"\n+        if self.query.distinct_fields:\n+            raise NotImplementedError(\"aggregate() + distinct(fields) not implemented.\")\n+        self._validate_values_are_expressions(\n+            (*args, *kwargs.values()), method_name=\"aggregate\"\n+        )\n+        for arg in args:\n+            # The default_alias property raises TypeError if default_alias\n+            # can't be set automatically or AttributeError if it isn't an\n+            # attribute.\n+            try:\n+                arg.default_alias\n+            except (AttributeError, TypeError):\n+                raise TypeError(\"Complex aggregates require an alias\")\n+            kwargs[arg.default_alias] = arg\n+\n+        return self.query.chain().get_aggregation(self.db, kwargs)\n+\n+    async def aaggregate(self, *args, **kwargs):\n+        return await sync_to_async(self.aggregate)(*args, **kwargs)\n+\n+    def count(self):\n+        \"\"\"\n+        Perform a SELECT COUNT() and return the number of records as an\n+        integer.\n+\n+        If the QuerySet is already fully cached, return the length of the\n+        cached results set to avoid multiple SELECT COUNT(*) calls.\n+        \"\"\"\n+        if self._result_cache is not None:\n+            return len(self._result_cache)\n+\n+        return self.query.get_count(using=self.db)\n+\n+    async def acount(self):\n+        return await sync_to_async(self.count)()\n+\n+    def get(self, *args, **kwargs):\n+        \"\"\"\n+        Perform the query and return a single object matching the given\n+        keyword arguments.\n+        \"\"\"\n+        if self.query.combinator and (args or kwargs):\n+            raise NotSupportedError(\n+                \"Calling QuerySet.get(...) with filters after %s() is not \"\n+                \"supported.\" % self.query.combinator\n+            )\n+        clone = self._chain() if self.query.combinator else self.filter(*args, **kwargs)\n+        if self.query.can_filter() and not self.query.distinct_fields:\n+            clone = clone.order_by()\n+        limit = None\n+        if (\n+            not clone.query.select_for_update\n+            or connections[clone.db].features.supports_select_for_update_with_limit\n+        ):\n+            limit = MAX_GET_RESULTS\n+            clone.query.set_limits(high=limit)\n+        num = len(clone)\n+        if num == 1:\n+            return clone._result_cache[0]\n+        if not num:\n+            raise self.model.DoesNotExist(\n+                \"%s matching query does not exist.\" % self.model._meta.object_name\n+            )\n+        raise self.model.MultipleObjectsReturned(\n+            \"get() returned more than one %s -- it returned %s!\"\n+            % (\n+                self.model._meta.object_name,\n+                num if not limit or num < limit else \"more than %s\" % (limit - 1),\n+            )\n+        )\n+\n+    async def aget(self, *args, **kwargs):\n+        return await sync_to_async(self.get)(*args, **kwargs)\n+\n+    def create(self, **kwargs):\n+        \"\"\"\n+        Create a new object with the given kwargs, saving it to the database\n+        and returning the created object.\n+        \"\"\"\n+        reverse_one_to_one_fields = frozenset(kwargs).intersection(\n+            self.model._meta._reverse_one_to_one_field_names\n+        )\n+        if reverse_one_to_one_fields:\n+            raise ValueError(\n+                \"The following fields do not exist in this model: %s\"\n+                % \", \".join(reverse_one_to_one_fields)\n+            )\n+\n+        obj = self.model(**kwargs)\n+        self._for_write = True\n+        obj.save(force_insert=True, using=self.db)\n+        return obj\n+\n+    create.alters_data = True\n+\n+    async def acreate(self, **kwargs):\n+        return await sync_to_async(self.create)(**kwargs)\n+\n+    acreate.alters_data = True\n+\n+    def _prepare_for_bulk_create(self, objs):\n+        for obj in objs:\n+            if not obj._is_pk_set():\n+                # Populate new PK values.\n+                obj.pk = obj._meta.pk.get_pk_value_on_save(obj)\n+            obj._prepare_related_fields_for_save(operation_name=\"bulk_create\")\n+\n+    def _check_bulk_create_options(\n+        self, ignore_conflicts, update_conflicts, update_fields, unique_fields\n+    ):\n+        if ignore_conflicts and update_conflicts:\n+            raise ValueError(\n+                \"ignore_conflicts and update_conflicts are mutually exclusive.\"\n+            )\n+        db_features = connections[self.db].features\n+        if ignore_conflicts:\n+            if not db_features.supports_ignore_conflicts:\n+                raise NotSupportedError(\n+                    \"This database backend does not support ignoring conflicts.\"\n+                )\n+            return OnConflict.IGNORE\n+        elif update_conflicts:\n+            if not db_features.supports_update_conflicts:\n+                raise NotSupportedError(\n+                    \"This database backend does not support updating conflicts.\"\n+                )\n+            if not update_fields:\n+                raise ValueError(\n+                    \"Fields that will be updated when a row insertion fails \"\n+                    \"on conflicts must be provided.\"\n+                )\n+            if unique_fields and not db_features.supports_update_conflicts_with_target:\n+                raise NotSupportedError(\n+                    \"This database backend does not support updating \"\n+                    \"conflicts with specifying unique fields that can trigger \"\n+                    \"the upsert.\"\n+                )\n+            if not unique_fields and db_features.supports_update_conflicts_with_target:\n+                raise ValueError(\n+                    \"Unique fields that can trigger the upsert must be provided.\"\n+                )\n+            # Updating primary keys and non-concrete fields is forbidden.\n+            if any(not f.concrete or f.many_to_many for f in update_fields):\n+                raise ValueError(\n+                    \"bulk_create() can only be used with concrete fields in \"\n+                    \"update_fields.\"\n+                )\n+            if any(f in self.model._meta.pk_fields for f in update_fields):\n+                raise ValueError(\n+                    \"bulk_create() cannot be used with primary keys in \"\n+                    \"update_fields.\"\n+                )\n+            if unique_fields:\n+                if any(not f.concrete or f.many_to_many for f in unique_fields):\n+                    raise ValueError(\n+                        \"bulk_create() can only be used with concrete fields \"\n+                        \"in unique_fields.\"\n+                    )\n+            return OnConflict.UPDATE\n+        return None\n+\n+    def bulk_create(\n+        self,\n+        objs,\n+        batch_size=None,\n+        ignore_conflicts=False,\n+        update_conflicts=False,\n+        update_fields=None,\n+        unique_fields=None,\n+    ):\n+        \"\"\"\n+        Insert each of the instances into the database. Do *not* call\n+        save() on each of the instances, do not send any pre/post_save\n+        signals, and do not set the primary key attribute if it is an\n+        autoincrement field (except if features.can_return_rows_from_bulk_insert=True).\n+        Multi-table models are not supported.\n+        \"\"\"\n+        # When you bulk insert you don't get the primary keys back (if it's an\n+        # autoincrement, except if can_return_rows_from_bulk_insert=True), so\n+        # you can't insert into the child tables which references this. There\n+        # are two workarounds:\n+        # 1) This could be implemented if you didn't have an autoincrement pk\n+        # 2) You could do it by doing O(n) normal inserts into the parent\n+        #    tables to get the primary keys back and then doing a single bulk\n+        #    insert into the childmost table.\n+        # We currently set the primary keys on the objects when using\n+        # PostgreSQL via the RETURNING ID clause. It should be possible for\n+        # Oracle as well, but the semantics for extracting the primary keys is\n+        # trickier so it's not done yet.\n+        if batch_size is not None and batch_size <= 0:\n+            raise ValueError(\"Batch size must be a positive integer.\")\n+        # Check that the parents share the same concrete model with the our\n+        # model to detect the inheritance pattern ConcreteGrandParent ->\n+        # MultiTableParent -> ProxyChild. Simply checking self.model._meta.proxy\n+        # would not identify that case as involving multiple tables.\n+        for parent in self.model._meta.all_parents:\n+            if parent._meta.concrete_model is not self.model._meta.concrete_model:\n+                raise ValueError(\"Can't bulk create a multi-table inherited model\")\n+        if not objs:\n+            return objs\n+        opts = self.model._meta\n+        if unique_fields:\n+            # Primary key is allowed in unique_fields.\n+            unique_fields = [\n+                self.model._meta.get_field(opts.pk.name if name == \"pk\" else name)\n+                for name in unique_fields\n+            ]\n+        if update_fields:\n+            update_fields = [self.model._meta.get_field(name) for name in update_fields]\n+        on_conflict = self._check_bulk_create_options(\n+            ignore_conflicts,\n+            update_conflicts,\n+            update_fields,\n+            unique_fields,\n+        )\n+        self._for_write = True\n+        fields = [f for f in opts.concrete_fields if not f.generated]\n+        objs = list(objs)\n+        self._prepare_for_bulk_create(objs)\n+        with transaction.atomic(using=self.db, savepoint=False):\n+            objs_without_pk, objs_with_pk = partition(lambda o: o._is_pk_set(), objs)\n+            if objs_with_pk:\n+                returned_columns = self._batched_insert(\n+                    objs_with_pk,\n+                    fields,\n+                    batch_size,\n+                    on_conflict=on_conflict,\n+                    update_fields=update_fields,\n+                    unique_fields=unique_fields,\n+                )\n+                for obj_with_pk, results in zip(objs_with_pk, returned_columns):\n+                    for result, field in zip(results, opts.db_returning_fields):\n+                        if field != opts.pk:\n+                            setattr(obj_with_pk, field.attname, result)\n+                for obj_with_pk in objs_with_pk:\n+                    obj_with_pk._state.adding = False\n+                    obj_with_pk._state.db = self.db\n+            if objs_without_pk:\n+                fields = [f for f in fields if not isinstance(f, AutoField)]\n+                returned_columns = self._batched_insert(\n+                    objs_without_pk,\n+                    fields,\n+                    batch_size,\n+                    on_conflict=on_conflict,\n+                    update_fields=update_fields,\n+                    unique_fields=unique_fields,\n+                )\n+                connection = connections[self.db]\n+                if (\n+                    connection.features.can_return_rows_from_bulk_insert\n+                    and on_conflict is None\n+                ):\n+                    assert len(returned_columns) == len(objs_without_pk)\n+                for obj_without_pk, results in zip(objs_without_pk, returned_columns):\n+                    for result, field in zip(results, opts.db_returning_fields):\n+                        setattr(obj_without_pk, field.attname, result)\n+                    obj_without_pk._state.adding = False\n+                    obj_without_pk._state.db = self.db\n+\n+        return objs\n+\n+    bulk_create.alters_data = True\n+\n+    async def abulk_create(\n+        self,\n+        objs,\n+        batch_size=None,\n+        ignore_conflicts=False,\n+        update_conflicts=False,\n+        update_fields=None,\n+        unique_fields=None,\n+    ):\n+        return await sync_to_async(self.bulk_create)(\n+            objs=objs,\n+            batch_size=batch_size,\n+            ignore_conflicts=ignore_conflicts,\n+            update_conflicts=update_conflicts,\n+            update_fields=update_fields,\n+            unique_fields=unique_fields,\n+        )\n+\n+    abulk_create.alters_data = True\n+\n+    def bulk_update(self, objs, fields, batch_size=None):\n+        \"\"\"\n+        Update the given fields in each of the given objects in the database.\n+        \"\"\"\n+        if batch_size is not None and batch_size <= 0:\n+            raise ValueError(\"Batch size must be a positive integer.\")\n+        if not fields:\n+            raise ValueError(\"Field names must be given to bulk_update().\")\n+        objs = tuple(objs)\n+        if not all(obj._is_pk_set() for obj in objs):\n+            raise ValueError(\"All bulk_update() objects must have a primary key set.\")\n+        opts = self.model._meta\n+        fields = [opts.get_field(name) for name in fields]\n+        if any(not f.concrete or f.many_to_many for f in fields):\n+            raise ValueError(\"bulk_update() can only be used with concrete fields.\")\n+        all_pk_fields = set(opts.pk_fields)\n+        for parent in opts.all_parents:\n+            all_pk_fields.update(parent._meta.pk_fields)\n+        if any(f in all_pk_fields for f in fields):\n+            raise ValueError(\"bulk_update() cannot be used with primary key fields.\")\n+        if not objs:\n+            return 0\n+        for obj in objs:\n+            obj._prepare_related_fields_for_save(\n+                operation_name=\"bulk_update\", fields=fields\n+            )\n+        # PK is used twice in the resulting update query, once in the filter\n+        # and once in the WHEN. Each field will also have one CAST.\n+        self._for_write = True\n+        connection = connections[self.db]\n+        max_batch_size = connection.ops.bulk_batch_size(\n+            [opts.pk, opts.pk] + fields, objs\n+        )\n+        batch_size = min(batch_size, max_batch_size) if batch_size else max_batch_size\n+        requires_casting = connection.features.requires_casted_case_in_updates\n+        batches = (objs[i : i + batch_size] for i in range(0, len(objs), batch_size))\n+        updates = []\n+        for batch_objs in batches:\n+            update_kwargs = {}\n+            for field in fields:\n+                when_statements = []\n+                for obj in batch_objs:\n+                    attr = getattr(obj, field.attname)\n+                    if not hasattr(attr, \"resolve_expression\"):\n+                        attr = Value(attr, output_field=field)\n+                    when_statements.append(When(pk=obj.pk, then=attr))\n+                case_statement = Case(*when_statements, output_field=field)\n+                if requires_casting:\n+                    case_statement = Cast(case_statement, output_field=field)\n+                update_kwargs[field.attname] = case_statement\n+            updates.append(([obj.pk for obj in batch_objs], update_kwargs))\n+        rows_updated = 0\n+        queryset = self.using(self.db)\n+        with transaction.atomic(using=self.db, savepoint=False):\n+            for pks, update_kwargs in updates:\n+                rows_updated += queryset.filter(pk__in=pks).update(**update_kwargs)\n+        return rows_updated\n+\n+    bulk_update.alters_data = True\n+\n+    async def abulk_update(self, objs, fields, batch_size=None):\n+        return await sync_to_async(self.bulk_update)(\n+            objs=objs,\n+            fields=fields,\n+            batch_size=batch_size,\n+        )\n+\n+    abulk_update.alters_data = True\n+\n+    def get_or_create(self, defaults=None, **kwargs):\n+        \"\"\"\n+        Look up an object with the given kwargs, creating one if necessary.\n+        Return a tuple of (object, created), where created is a boolean\n+        specifying whether an object was created.\n+        \"\"\"\n+        # The get() needs to be targeted at the write database in order\n+        # to avoid potential transaction consistency problems.\n+        self._for_write = True\n+        try:\n+            return self.get(**kwargs), False\n+        except self.model.DoesNotExist:\n+            params = self._extract_model_params(defaults, **kwargs)\n+            # Try to create an object using passed params.\n+            try:\n+                with transaction.atomic(using=self.db):\n+                    params = dict(resolve_callables(params))\n+                    return self.create(**params), True\n+            except IntegrityError:\n+                try:\n+                    return self.get(**kwargs), False\n+                except self.model.DoesNotExist:\n+                    pass\n+                raise\n+\n+    get_or_create.alters_data = True\n+\n+    async def aget_or_create(self, defaults=None, **kwargs):\n+        return await sync_to_async(self.get_or_create)(\n+            defaults=defaults,\n+            **kwargs,\n+        )\n+\n+    aget_or_create.alters_data = True\n+\n+    def update_or_create(self, defaults=None, create_defaults=None, **kwargs):\n+        \"\"\"\n+        Look up an object with the given kwargs, updating one with defaults\n+        if it exists, otherwise create a new one. Optionally, an object can\n+        be created with different values than defaults by using\n+        create_defaults.\n+        Return a tuple (object, created), where created is a boolean\n+        specifying whether an object was created.\n+        \"\"\"\n+        update_defaults = defaults or {}\n+        if create_defaults is None:\n+            create_defaults = update_defaults\n+\n+        self._for_write = True\n+        with transaction.atomic(using=self.db):\n+            # Lock the row so that a concurrent update is blocked until\n+            # update_or_create() has performed its save.\n+            obj, created = self.select_for_update().get_or_create(\n+                create_defaults, **kwargs\n+            )\n+            if created:\n+                return obj, created\n+            for k, v in resolve_callables(update_defaults):\n+                setattr(obj, k, v)\n+\n+            update_fields = set(update_defaults)\n+            concrete_field_names = self.model._meta._non_pk_concrete_field_names\n+            # update_fields does not support non-concrete fields.\n+            if concrete_field_names.issuperset(update_fields):\n+                # Add fields which are set on pre_save(), e.g. auto_now fields.\n+                # This is to maintain backward compatibility as these fields\n+                # are not updated unless explicitly specified in the\n+                # update_fields list.\n+                pk_fields = self.model._meta.pk_fields\n+                for field in self.model._meta.local_concrete_fields:\n+                    if not (\n+                        field in pk_fields or field.__class__.pre_save is Field.pre_save\n+                    ):\n+                        update_fields.add(field.name)\n+                        if field.name != field.attname:\n+                            update_fields.add(field.attname)\n+                obj.save(using=self.db, update_fields=update_fields)\n+            else:\n+                obj.save(using=self.db)\n+        return obj, False\n+\n+    update_or_create.alters_data = True\n+\n+    async def aupdate_or_create(self, defaults=None, create_defaults=None, **kwargs):\n+        return await sync_to_async(self.update_or_create)(\n+            defaults=defaults,\n+            create_defaults=create_defaults,\n+            **kwargs,\n+        )\n+\n+    aupdate_or_create.alters_data = True\n+\n+    def _extract_model_params(self, defaults, **kwargs):\n+        \"\"\"\n+        Prepare `params` for creating a model instance based on the given\n+        kwargs; for use by get_or_create().\n+        \"\"\"\n+        defaults = defaults or {}\n+        params = {k: v for k, v in kwargs.items() if LOOKUP_SEP not in k}\n+        params.update(defaults)\n+        property_names = self.model._meta._property_names\n+        invalid_params = []\n+        for param in params:\n+            try:\n+                self.model._meta.get_field(param)\n+            except exceptions.FieldDoesNotExist:\n+                # It's okay to use a model's property if it has a setter.\n+                if not (param in property_names and getattr(self.model, param).fset):\n+                    invalid_params.append(param)\n+        if invalid_params:\n+            raise exceptions.FieldError(\n+                \"Invalid field name(s) for model %s: '%s'.\"\n+                % (\n+                    self.model._meta.object_name,\n+                    \"', '\".join(sorted(invalid_params)),\n+                )\n+            )\n+        return params\n+\n+    def _earliest(self, *fields):\n+        \"\"\"\n+        Return the earliest object according to fields (if given) or by the\n+        model's Meta.get_latest_by.\n+        \"\"\"\n+        if fields:\n+            order_by = fields\n+        else:\n+            order_by = getattr(self.model._meta, \"get_latest_by\")\n+            if order_by and not isinstance(order_by, (tuple, list)):\n+                order_by = (order_by,)\n+        if order_by is None:\n+            raise ValueError(\n+                \"earliest() and latest() require either fields as positional \"\n+                \"arguments or 'get_latest_by' in the model's Meta.\"\n+            )\n+        obj = self._chain()\n+        obj.query.set_limits(high=1)\n+        obj.query.clear_ordering(force=True)\n+        obj.query.add_ordering(*order_by)\n+        return obj.get()\n+\n+    def earliest(self, *fields):\n+        if self.query.is_sliced:\n+            raise TypeError(\"Cannot change a query once a slice has been taken.\")\n+        return self._earliest(*fields)\n+\n+    async def aearliest(self, *fields):\n+        return await sync_to_async(self.earliest)(*fields)\n+\n+    def latest(self, *fields):\n+        \"\"\"\n+        Return the latest object according to fields (if given) or by the\n+        model's Meta.get_latest_by.\n+        \"\"\"\n+        if self.query.is_sliced:\n+            raise TypeError(\"Cannot change a query once a slice has been taken.\")\n+        return self.reverse()._earliest(*fields)\n+\n+    async def alatest(self, *fields):\n+        return await sync_to_async(self.latest)(*fields)\n+\n+    def first(self):\n+        \"\"\"Return the first object of a query or None if no match is found.\"\"\"\n+        if self.ordered:\n+            queryset = self\n+        else:\n+            self._check_ordering_first_last_queryset_aggregation(method=\"first\")\n+            queryset = self.order_by(\"pk\")\n+        for obj in queryset[:1]:\n+            return obj\n+\n+    async def afirst(self):\n+        return await sync_to_async(self.first)()\n+\n+    def last(self):\n+        \"\"\"Return the last object of a query or None if no match is found.\"\"\"\n+        if self.ordered:\n+            queryset = self.reverse()\n+        else:\n+            self._check_ordering_first_last_queryset_aggregation(method=\"last\")\n+            queryset = self.order_by(\"-pk\")\n+        for obj in queryset[:1]:\n+            return obj\n+\n+    async def alast(self):\n+        return await sync_to_async(self.last)()\n+\n+    def in_bulk(self, id_list=None, *, field_name=\"pk\"):\n+        \"\"\"\n+        Return a dictionary mapping each of the given IDs to the object with\n+        that ID. If `id_list` isn't provided, evaluate the entire QuerySet.\n+        \"\"\"\n+        if self.query.is_sliced:\n+            raise TypeError(\"Cannot use 'limit' or 'offset' with in_bulk().\")\n+        if not issubclass(self._iterable_class, ModelIterable):\n+            raise TypeError(\"in_bulk() cannot be used with values() or values_list().\")\n+        opts = self.model._meta\n+        unique_fields = [\n+            constraint.fields[0]\n+            for constraint in opts.total_unique_constraints\n+            if len(constraint.fields) == 1\n+        ]\n+        if (\n+            field_name != \"pk\"\n+            and not opts.get_field(field_name).unique\n+            and field_name not in unique_fields\n+            and self.query.distinct_fields != (field_name,)\n+        ):\n+            raise ValueError(\n+                \"in_bulk()'s field_name must be a unique field but %r isn't.\"\n+                % field_name\n+            )\n+        if id_list is not None:\n+            if not id_list:\n+                return {}\n+            filter_key = \"{}__in\".format(field_name)\n+            batch_size = connections[self.db].features.max_query_params\n+            id_list = tuple(id_list)\n+            # If the database has a limit on the number of query parameters\n+            # (e.g. SQLite), retrieve objects in batches if necessary.\n+            if batch_size and batch_size < len(id_list):\n+                qs = ()\n+                for offset in range(0, len(id_list), batch_size):\n+                    batch = id_list[offset : offset + batch_size]\n+                    qs += tuple(self.filter(**{filter_key: batch}))\n+            else:\n+                qs = self.filter(**{filter_key: id_list})\n+        else:\n+            qs = self._chain()\n+        return {getattr(obj, field_name): obj for obj in qs}\n+\n+    async def ain_bulk(self, id_list=None, *, field_name=\"pk\"):\n+        return await sync_to_async(self.in_bulk)(\n+            id_list=id_list,\n+            field_name=field_name,\n+        )\n+\n+    def delete(self):\n+        \"\"\"Delete the records in the current QuerySet.\"\"\"\n+        self._not_support_combined_queries(\"delete\")\n+        if self.query.is_sliced:\n+            raise TypeError(\"Cannot use 'limit' or 'offset' with delete().\")\n+        if self.query.distinct_fields:\n+            raise TypeError(\"Cannot call delete() after .distinct(*fields).\")\n+        if self._fields is not None:\n+            raise TypeError(\"Cannot call delete() after .values() or .values_list()\")\n+\n+        del_query = self._chain()\n+\n+        # The delete is actually 2 queries - one to find related objects,\n+        # and one to delete. Make sure that the discovery of related\n+        # objects is performed on the same database as the deletion.\n+        del_query._for_write = True\n+\n+        # Disable non-supported fields.\n+        del_query.query.select_for_update = False\n+        del_query.query.select_related = False\n+        del_query.query.clear_ordering(force=True)\n+\n+        collector = Collector(using=del_query.db, origin=self)\n+        collector.collect(del_query)\n+        num_deleted, num_deleted_per_model = collector.delete()\n+\n+        # Clear the result cache, in case this QuerySet gets reused.\n+        self._result_cache = None\n+        return num_deleted, num_deleted_per_model\n+\n+    delete.alters_data = True\n+    delete.queryset_only = True\n+\n+    async def adelete(self):\n+        return await sync_to_async(self.delete)()\n+\n+    adelete.alters_data = True\n+    adelete.queryset_only = True\n+\n+    def _raw_delete(self, using):\n+        \"\"\"\n+        Delete objects found from the given queryset in single direct SQL\n+        query. No signals are sent and there is no protection for cascades.\n+        \"\"\"\n+        query = self.query.clone()\n+        query.__class__ = sql.DeleteQuery\n+        return query.get_compiler(using).execute_sql(ROW_COUNT)\n+\n+    _raw_delete.alters_data = True\n+\n+    def update(self, **kwargs):\n+        \"\"\"\n+        Update all elements in the current QuerySet, setting all the given\n+        fields to the appropriate values.\n+        \"\"\"\n+        self._not_support_combined_queries(\"update\")\n+        if self.query.is_sliced:\n+            raise TypeError(\"Cannot update a query once a slice has been taken.\")\n+        self._for_write = True\n+        query = self.query.chain(sql.UpdateQuery)\n+        query.add_update_values(kwargs)\n+\n+        # Inline annotations in order_by(), if possible.\n+        new_order_by = []\n+        for col in query.order_by:\n+            alias = col\n+            descending = False\n+            if isinstance(alias, str) and alias.startswith(\"-\"):\n+                alias = alias.removeprefix(\"-\")\n+                descending = True\n+            if annotation := query.annotations.get(alias):\n+                if getattr(annotation, \"contains_aggregate\", False):\n+                    raise exceptions.FieldError(\n+                        f\"Cannot update when ordering by an aggregate: {annotation}\"\n+                    )\n+                if descending:\n+                    annotation = annotation.desc()\n+                new_order_by.append(annotation)\n+            else:\n+                new_order_by.append(col)\n+        query.order_by = tuple(new_order_by)\n+\n+        # Clear any annotations so that they won't be present in subqueries.\n+        query.annotations = {}\n+        with transaction.mark_for_rollback_on_error(using=self.db):\n+            rows = query.get_compiler(self.db).execute_sql(ROW_COUNT)\n+        self._result_cache = None\n+        return rows\n+\n+    update.alters_data = True\n+\n+    async def aupdate(self, **kwargs):\n+        return await sync_to_async(self.update)(**kwargs)\n+\n+    aupdate.alters_data = True\n+\n+    def _update(self, values):\n+        \"\"\"\n+        A version of update() that accepts field objects instead of field names.\n+        Used primarily for model saving and not intended for use by general\n+        code (it requires too much poking around at model internals to be\n+        useful at that level).\n+        \"\"\"\n+        if self.query.is_sliced:\n+            raise TypeError(\"Cannot update a query once a slice has been taken.\")\n+        query = self.query.chain(sql.UpdateQuery)\n+        query.add_update_fields(values)\n+        # Clear any annotations so that they won't be present in subqueries.\n+        query.annotations = {}\n+        self._result_cache = None\n+        return query.get_compiler(self.db).execute_sql(ROW_COUNT)\n+\n+    _update.alters_data = True\n+    _update.queryset_only = False\n+\n+    def exists(self):\n+        \"\"\"\n+        Return True if the QuerySet would have any results, False otherwise.\n+        \"\"\"\n+        if self._result_cache is None:\n+            return self.query.has_results(using=self.db)\n+        return bool(self._result_cache)\n+\n+    async def aexists(self):\n+        return await sync_to_async(self.exists)()\n+\n+    def contains(self, obj):\n+        \"\"\"\n+        Return True if the QuerySet contains the provided obj,\n+        False otherwise.\n+        \"\"\"\n+        self._not_support_combined_queries(\"contains\")\n+        if self._fields is not None:\n+            raise TypeError(\n+                \"Cannot call QuerySet.contains() after .values() or .values_list().\"\n+            )\n+        try:\n+            if obj._meta.concrete_model != self.model._meta.concrete_model:\n+                return False\n+        except AttributeError:\n+            raise TypeError(\"'obj' must be a model instance.\")\n+        if not obj._is_pk_set():\n+            raise ValueError(\"QuerySet.contains() cannot be used on unsaved objects.\")\n+        if self._result_cache is not None:\n+            return obj in self._result_cache\n+        return self.filter(pk=obj.pk).exists()\n+\n+    async def acontains(self, obj):\n+        return await sync_to_async(self.contains)(obj=obj)\n+\n+    def _prefetch_related_objects(self):\n+        # This method can only be called once the result cache has been filled.\n+        prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)\n+        self._prefetch_done = True\n+\n+    def explain(self, *, format=None, **options):\n+        \"\"\"\n+        Runs an EXPLAIN on the SQL query this QuerySet would perform, and\n+        returns the results.\n+        \"\"\"\n+        return self.query.explain(using=self.db, format=format, **options)\n+\n+    async def aexplain(self, *, format=None, **options):\n+        return await sync_to_async(self.explain)(format=format, **options)\n+\n+    ##################################################\n+    # PUBLIC METHODS THAT RETURN A QUERYSET SUBCLASS #\n+    ##################################################\n+\n+    def raw(self, raw_query, params=(), translations=None, using=None):\n+        if using is None:\n+            using = self.db\n+        qs = RawQuerySet(\n+            raw_query,\n+            model=self.model,\n+            params=params,\n+            translations=translations,\n+            using=using,\n+        )\n+        qs._prefetch_related_lookups = self._prefetch_related_lookups[:]\n+        return qs\n+\n+    def _values(self, *fields, **expressions):\n+        clone = self._chain()\n+        if expressions:\n+            clone = clone.annotate(**expressions)\n+        clone._fields = fields\n+        clone.query.set_values(fields)\n+        return clone\n+\n+    def values(self, *fields, **expressions):\n+        fields += tuple(expressions)\n+        clone = self._values(*fields, **expressions)\n+        clone._iterable_class = ValuesIterable\n+        return clone\n+\n+    def values_list(self, *fields, flat=False, named=False):\n+        if flat and named:\n+            raise TypeError(\"'flat' and 'named' can't be used together.\")\n+        if flat and len(fields) > 1:\n+            raise TypeError(\n+                \"'flat' is not valid when values_list is called with more than one \"\n+                \"field.\"\n+            )\n+\n+        field_names = {f for f in fields if not hasattr(f, \"resolve_expression\")}\n+        _fields = []\n+        expressions = {}\n+        counter = 1\n+        for field in fields:\n+            if hasattr(field, \"resolve_expression\"):\n+                field_id_prefix = getattr(\n+                    field, \"default_alias\", field.__class__.__name__.lower()\n+                )\n+                while True:\n+                    field_id = field_id_prefix + str(counter)\n+                    counter += 1\n+                    if field_id not in field_names:\n+                        break\n+                expressions[field_id] = field\n+                _fields.append(field_id)\n+            else:\n+                _fields.append(field)\n+\n+        clone = self._values(*_fields, **expressions)\n+        clone._iterable_class = (\n+            NamedValuesListIterable\n+            if named\n+            else FlatValuesListIterable if flat else ValuesListIterable\n+        )\n+        return clone\n+\n+    def dates(self, field_name, kind, order=\"ASC\"):\n+        \"\"\"\n+        Return a list of date objects representing all available dates for\n+        the given field_name, scoped to 'kind'.\n+        \"\"\"\n+        if kind not in (\"year\", \"month\", \"week\", \"day\"):\n+            raise ValueError(\"'kind' must be one of 'year', 'month', 'week', or 'day'.\")\n+        if order not in (\"ASC\", \"DESC\"):\n+            raise ValueError(\"'order' must be either 'ASC' or 'DESC'.\")\n+        return (\n+            self.annotate(\n+                datefield=Trunc(field_name, kind, output_field=DateField()),\n+                plain_field=F(field_name),\n+            )\n+            .values_list(\"datefield\", flat=True)\n+            .distinct()\n+            .filter(plain_field__isnull=False)\n+            .order_by((\"-\" if order == \"DESC\" else \"\") + \"datefield\")\n+        )\n+\n+    def datetimes(self, field_name, kind, order=\"ASC\", tzinfo=None):\n+        \"\"\"\n+        Return a list of datetime objects representing all available\n+        datetimes for the given field_name, scoped to 'kind'.\n+        \"\"\"\n+        if kind not in (\"year\", \"month\", \"week\", \"day\", \"hour\", \"minute\", \"second\"):\n+            raise ValueError(\n+                \"'kind' must be one of 'year', 'month', 'week', 'day', \"\n+                \"'hour', 'minute', or 'second'.\"\n+            )\n+        if order not in (\"ASC\", \"DESC\"):\n+            raise ValueError(\"'order' must be either 'ASC' or 'DESC'.\")\n+        if settings.USE_TZ:\n+            if tzinfo is None:\n+                tzinfo = timezone.get_current_timezone()\n+        else:\n+            tzinfo = None\n+        return (\n+            self.annotate(\n+                datetimefield=Trunc(\n+                    field_name,\n+                    kind,\n+                    output_field=DateTimeField(),\n+                    tzinfo=tzinfo,\n+                ),\n+                plain_field=F(field_name),\n+            )\n+            .values_list(\"datetimefield\", flat=True)\n+            .distinct()\n+            .filter(plain_field__isnull=False)\n+            .order_by((\"-\" if order == \"DESC\" else \"\") + \"datetimefield\")\n+        )\n+\n+    def none(self):\n+        \"\"\"Return an empty QuerySet.\"\"\"\n+        clone = self._chain()\n+        clone.query.set_empty()\n+        return clone\n+\n+    ##################################################################\n+    # PUBLIC METHODS THAT ALTER ATTRIBUTES AND RETURN A NEW QUERYSET #\n+    ##################################################################\n+\n+    def all(self):\n+        \"\"\"\n+        Return a new QuerySet that is a copy of the current one. This allows a\n+        QuerySet to proxy for a model manager in some cases.\n+        \"\"\"\n+        return self._chain()\n+\n+    def filter(self, *args, **kwargs):\n+        \"\"\"\n+        Return a new QuerySet instance with the args ANDed to the existing\n+        set.\n+        \"\"\"\n+        self._not_support_combined_queries(\"filter\")\n+        return self._filter_or_exclude(False, args, kwargs)\n+\n+    def exclude(self, *args, **kwargs):\n+        \"\"\"\n+        Return a new QuerySet instance with NOT (args) ANDed to the existing\n+        set.\n+        \"\"\"\n+        self._not_support_combined_queries(\"exclude\")\n+        return self._filter_or_exclude(True, args, kwargs)\n+\n+    def _filter_or_exclude(self, negate, args, kwargs):\n+        if (args or kwargs) and self.query.is_sliced:\n+            raise TypeError(\"Cannot filter a query once a slice has been taken.\")\n+        clone = self._chain()\n+        if self._defer_next_filter:\n+            self._defer_next_filter = False\n+            clone._deferred_filter = negate, args, kwargs\n+        else:\n+            clone._filter_or_exclude_inplace(negate, args, kwargs)\n+        return clone\n+\n+    def _filter_or_exclude_inplace(self, negate, args, kwargs):\n+        if negate:\n+            self._query.add_q(~Q(*args, **kwargs))\n+        else:\n+            self._query.add_q(Q(*args, **kwargs))\n+\n+    def complex_filter(self, filter_obj):\n+        \"\"\"\n+        Return a new QuerySet instance with filter_obj added to the filters.\n+\n+        filter_obj can be a Q object or a dictionary of keyword lookup\n+        arguments.\n+\n+        This exists to support framework features such as 'limit_choices_to',\n+        and usually it will be more natural to use other methods.\n+        \"\"\"\n+        if isinstance(filter_obj, Q):\n+            clone = self._chain()\n+            clone.query.add_q(filter_obj)\n+            return clone\n+        else:\n+            return self._filter_or_exclude(False, args=(), kwargs=filter_obj)\n+\n+    def _combinator_query(self, combinator, *other_qs, all=False):\n+        # Clone the query to inherit the select list and everything\n+        clone = self._chain()\n+        # Clear limits and ordering so they can be reapplied\n+        clone.query.clear_ordering(force=True)\n+        clone.query.clear_limits()\n+        clone.query.combined_queries = (self.query,) + tuple(\n+            qs.query for qs in other_qs\n+        )\n+        clone.query.combinator = combinator\n+        clone.query.combinator_all = all\n+        return clone\n+\n+    def union(self, *other_qs, all=False):\n+        # If the query is an EmptyQuerySet, combine all nonempty querysets.\n+        if isinstance(self, EmptyQuerySet):\n+            qs = [q for q in other_qs if not isinstance(q, EmptyQuerySet)]\n+            if not qs:\n+                return self\n+            if len(qs) == 1:\n+                return qs[0]\n+            return qs[0]._combinator_query(\"union\", *qs[1:], all=all)\n+        return self._combinator_query(\"union\", *other_qs, all=all)\n+\n+    def intersection(self, *other_qs):\n+        # If any query is an EmptyQuerySet, return it.\n+        if isinstance(self, EmptyQuerySet):\n+            return self\n+        for other in other_qs:\n+            if isinstance(other, EmptyQuerySet):\n+                return other\n+        return self._combinator_query(\"intersection\", *other_qs)\n+\n+    def difference(self, *other_qs):\n+        # If the query is an EmptyQuerySet, return it.\n+        if isinstance(self, EmptyQuerySet):\n+            return self\n+        return self._combinator_query(\"difference\", *other_qs)\n+\n+    def select_for_update(self, nowait=False, skip_locked=False, of=(), no_key=False):\n+        \"\"\"\n+        Return a new QuerySet instance that will select objects with a\n+        FOR UPDATE lock.\n+        \"\"\"\n+        if nowait and skip_locked:\n+            raise ValueError(\"The nowait option cannot be used with skip_locked.\")\n+        obj = self._chain()\n+        obj._for_write = True\n+        obj.query.select_for_update = True\n+        obj.query.select_for_update_nowait = nowait\n+        obj.query.select_for_update_skip_locked = skip_locked\n+        obj.query.select_for_update_of = of\n+        obj.query.select_for_no_key_update = no_key\n+        return obj\n+\n+    def select_related(self, *fields):\n+        \"\"\"\n+        Return a new QuerySet instance that will select related objects.\n+\n+        If fields are specified, they must be ForeignKey fields and only those\n+        related objects are included in the selection.\n+\n+        If select_related(None) is called, clear the list.\n+        \"\"\"\n+        self._not_support_combined_queries(\"select_related\")\n+        if self._fields is not None:\n+            raise TypeError(\n+                \"Cannot call select_related() after .values() or .values_list()\"\n+            )\n+\n+        obj = self._chain()\n+        if fields == (None,):\n+            obj.query.select_related = False\n+        elif fields:\n+            obj.query.add_select_related(fields)\n+        else:\n+            obj.query.select_related = True\n+        return obj\n+\n+    def prefetch_related(self, *lookups):\n+        \"\"\"\n+        Return a new QuerySet instance that will prefetch the specified\n+        Many-To-One and Many-To-Many related objects when the QuerySet is\n+        evaluated.\n+\n+        When prefetch_related() is called more than once, append to the list of\n+        prefetch lookups. If prefetch_related(None) is called, clear the list.\n+        \"\"\"\n+        self._not_support_combined_queries(\"prefetch_related\")\n+        clone = self._chain()\n+        if lookups == (None,):\n+            clone._prefetch_related_lookups = ()\n+        else:\n+            for lookup in lookups:\n+                if isinstance(lookup, Prefetch):\n+                    lookup = lookup.prefetch_to\n+                lookup = lookup.split(LOOKUP_SEP, 1)[0]\n+                if lookup in self.query._filtered_relations:\n+                    raise ValueError(\n+                        \"prefetch_related() is not supported with FilteredRelation.\"\n+                    )\n+            clone._prefetch_related_lookups = clone._prefetch_related_lookups + lookups\n+        return clone\n+\n+    def annotate(self, *args, **kwargs):\n+        \"\"\"\n+        Return a query set in which the returned objects have been annotated\n+        with extra data or aggregations.\n+        \"\"\"\n+        self._not_support_combined_queries(\"annotate\")\n+        return self._annotate(args, kwargs, select=True)\n+\n+    def alias(self, *args, **kwargs):\n+        \"\"\"\n+        Return a query set with added aliases for extra data or aggregations.\n+        \"\"\"\n+        self._not_support_combined_queries(\"alias\")\n+        return self._annotate(args, kwargs, select=False)\n+\n+    def _annotate(self, args, kwargs, select=True):\n+        self._validate_values_are_expressions(\n+            args + tuple(kwargs.values()), method_name=\"annotate\"\n+        )\n+        annotations = {}\n+        for arg in args:\n+            # The default_alias property raises TypeError if default_alias\n+            # can't be set automatically or AttributeError if it isn't an\n+            # attribute.\n+            try:\n+                if arg.default_alias in kwargs:\n+                    raise ValueError(\n+                        \"The named annotation '%s' conflicts with the \"\n+                        \"default name for another annotation.\" % arg.default_alias\n+                    )\n+            except (TypeError, AttributeError):\n+                raise TypeError(\"Complex annotations require an alias\")\n+            annotations[arg.default_alias] = arg\n+        annotations.update(kwargs)\n+\n+        clone = self._chain()\n+        names = self._fields\n+        if names is None:\n+            names = set(\n+                chain.from_iterable(\n+                    (\n+                        (field.name, field.attname)\n+                        if hasattr(field, \"attname\")\n+                        else (field.name,)\n+                    )\n+                    for field in self.model._meta.get_fields()\n+                )\n+            )\n+\n+        for alias, annotation in annotations.items():\n+            if alias in names:\n+                raise ValueError(\n+                    \"The annotation '%s' conflicts with a field on \"\n+                    \"the model.\" % alias\n+                )\n+            if isinstance(annotation, FilteredRelation):\n+                clone.query.add_filtered_relation(annotation, alias)\n+            else:\n+                clone.query.add_annotation(\n+                    annotation,\n+                    alias,\n+                    select=select,\n+                )\n+        for alias, annotation in clone.query.annotations.items():\n+            if alias in annotations and annotation.contains_aggregate:\n+                if clone._fields is None:\n+                    clone.query.group_by = True\n+                else:\n+                    clone.query.set_group_by()\n+                break\n+\n+        return clone\n+\n+    def order_by(self, *field_names):\n+        \"\"\"Return a new QuerySet instance with the ordering changed.\"\"\"\n+        if self.query.is_sliced:\n+            raise TypeError(\"Cannot reorder a query once a slice has been taken.\")\n+        obj = self._chain()\n+        obj.query.clear_ordering(force=True, clear_default=False)\n+        obj.query.add_ordering(*field_names)\n+        return obj\n+\n+    def distinct(self, *field_names):\n+        \"\"\"\n+        Return a new QuerySet instance that will select only distinct results.\n+        \"\"\"\n+        self._not_support_combined_queries(\"distinct\")\n+        if self.query.is_sliced:\n+            raise TypeError(\n+                \"Cannot create distinct fields once a slice has been taken.\"\n+            )\n+        obj = self._chain()\n+        obj.query.add_distinct_fields(*field_names)\n+        return obj\n+\n+    def extra(\n+        self,\n+        select=None,\n+        where=None,\n+        params=None,\n+        tables=None,\n+        order_by=None,\n+        select_params=None,\n+    ):\n+        \"\"\"Add extra SQL fragments to the query.\"\"\"\n+        self._not_support_combined_queries(\"extra\")\n+        if self.query.is_sliced:\n+            raise TypeError(\"Cannot change a query once a slice has been taken.\")\n+        clone = self._chain()\n+        clone.query.add_extra(select, select_params, where, params, tables, order_by)\n+        return clone\n+\n+    def reverse(self):\n+        \"\"\"Reverse the ordering of the QuerySet.\"\"\"\n+        if self.query.is_sliced:\n+            raise TypeError(\"Cannot reverse a query once a slice has been taken.\")\n+        clone = self._chain()\n+        clone.query.standard_ordering = not clone.query.standard_ordering\n+        return clone\n+\n+    def defer(self, *fields):\n+        \"\"\"\n+        Defer the loading of data for certain fields until they are accessed.\n+        Add the set of deferred fields to any existing set of deferred fields.\n+        The only exception to this is if None is passed in as the only\n+        parameter, in which case remove all deferrals.\n+        \"\"\"\n+        self._not_support_combined_queries(\"defer\")\n+        if self._fields is not None:\n+            raise TypeError(\"Cannot call defer() after .values() or .values_list()\")\n+        clone = self._chain()\n+        if fields == (None,):\n+            clone.query.clear_deferred_loading()\n+        else:\n+            clone.query.add_deferred_loading(fields)\n+        return clone\n+\n+    def only(self, *fields):\n+        \"\"\"\n+        Essentially, the opposite of defer(). Only the fields passed into this\n+        method and that are not already specified as deferred are loaded\n+        immediately when the queryset is evaluated.\n+        \"\"\"\n+        self._not_support_combined_queries(\"only\")\n+        if self._fields is not None:\n+            raise TypeError(\"Cannot call only() after .values() or .values_list()\")\n+        if fields == (None,):\n+            # Can only pass None to defer(), not only(), as the rest option.\n+            # That won't stop people trying to do this, so let's be explicit.\n+            raise TypeError(\"Cannot pass None as an argument to only().\")\n+        for field in fields:\n+            field = field.split(LOOKUP_SEP, 1)[0]\n+            if field in self.query._filtered_relations:\n+                raise ValueError(\"only() is not supported with FilteredRelation.\")\n+        clone = self._chain()\n+        clone.query.add_immediate_loading(fields)\n+        return clone\n+\n+    def using(self, alias):\n+        \"\"\"Select which database this QuerySet should execute against.\"\"\"\n+        clone = self._chain()\n+        clone._db = alias\n+        return clone\n+\n+    ###################################\n+    # PUBLIC INTROSPECTION ATTRIBUTES #\n+    ###################################\n+\n+    @property\n+    def ordered(self):\n+        \"\"\"\n+        Return True if the QuerySet is ordered -- i.e. has an order_by()\n+        clause or a default ordering on the model (or is empty).\n+        \"\"\"\n+        if isinstance(self, EmptyQuerySet):\n+            return True\n+        if self.query.extra_order_by or self.query.order_by:\n+            return True\n+        elif (\n+            self.query.default_ordering\n+            and self.query.get_meta().ordering\n+            and\n+            # A default ordering doesn't affect GROUP BY queries.\n+            not self.query.group_by\n+        ):\n+            return True\n+        else:\n+            return False\n+\n+    @property\n+    def db(self):\n+        \"\"\"Return the database used if this query is executed now.\"\"\"\n+        if self._for_write:\n+            return self._db or router.db_for_write(self.model, **self._hints)\n+        return self._db or router.db_for_read(self.model, **self._hints)\n+\n+    ###################\n+    # PRIVATE METHODS #\n+    ###################\n+\n+    def _insert(\n+        self,\n+        objs,\n+        fields,\n+        returning_fields=None,\n+        raw=False,\n+        using=None,\n+        on_conflict=None,\n+        update_fields=None,\n+        unique_fields=None,\n+    ):\n+        \"\"\"\n+        Insert a new record for the given model. This provides an interface to\n+        the InsertQuery class and is how Model.save() is implemented.\n+        \"\"\"\n+        self._for_write = True\n+        if using is None:\n+            using = self.db\n+        query = sql.InsertQuery(\n+            self.model,\n+            on_conflict=on_conflict,\n+            update_fields=update_fields,\n+            unique_fields=unique_fields,\n+        )\n+        query.insert_values(fields, objs, raw=raw)\n+        return query.get_compiler(using=using).execute_sql(returning_fields)\n+\n+    _insert.alters_data = True\n+    _insert.queryset_only = False\n+\n+    def _batched_insert(\n+        self,\n+        objs,\n+        fields,\n+        batch_size,\n+        on_conflict=None,\n+        update_fields=None,\n+        unique_fields=None,\n+    ):\n+        \"\"\"\n+        Helper method for bulk_create() to insert objs one batch at a time.\n+        \"\"\"\n+        connection = connections[self.db]\n+        ops = connection.ops\n+        max_batch_size = max(ops.bulk_batch_size(fields, objs), 1)\n+        batch_size = min(batch_size, max_batch_size) if batch_size else max_batch_size\n+        inserted_rows = []\n+        bulk_return = connection.features.can_return_rows_from_bulk_insert\n+        for item in [objs[i : i + batch_size] for i in range(0, len(objs), batch_size)]:\n+            if bulk_return and (\n+                on_conflict is None or on_conflict == OnConflict.UPDATE\n+            ):\n+                inserted_rows.extend(\n+                    self._insert(\n+                        item,\n+                        fields=fields,\n+                        using=self.db,\n+                        on_conflict=on_conflict,\n+                        update_fields=update_fields,\n+                        unique_fields=unique_fields,\n+                        returning_fields=self.model._meta.db_returning_fields,\n+                    )\n+                )\n+            else:\n+                self._insert(\n+                    item,\n+                    fields=fields,\n+                    using=self.db,\n+                    on_conflict=on_conflict,\n+                    update_fields=update_fields,\n+                    unique_fields=unique_fields,\n+                )\n+        return inserted_rows\n+\n+    def _chain(self):\n+        \"\"\"\n+        Return a copy of the current QuerySet that's ready for another\n+        operation.\n+        \"\"\"\n+        obj = self._clone()\n+        if obj._sticky_filter:\n+            obj.query.filter_is_sticky = True\n+            obj._sticky_filter = False\n+        return obj\n+\n+    def _clone(self):\n+        \"\"\"\n+        Return a copy of the current QuerySet. A lightweight alternative\n+        to deepcopy().\n+        \"\"\"\n+        c = self.__class__(\n+            model=self.model,\n+            query=self.query.chain(),\n+            using=self._db,\n+            hints=self._hints,\n+        )\n+        c._sticky_filter = self._sticky_filter\n+        c._for_write = self._for_write\n+        c._prefetch_related_lookups = self._prefetch_related_lookups[:]\n+        c._known_related_objects = self._known_related_objects\n+        c._iterable_class = self._iterable_class\n+        c._fields = self._fields\n+        return c\n+\n+    def _fetch_all(self):\n+        if self._result_cache is None:\n+            self._result_cache = list(self._iterable_class(self))\n+        if self._prefetch_related_lookups and not self._prefetch_done:\n+            self._prefetch_related_objects()\n+\n+    def _next_is_sticky(self):\n+        \"\"\"\n+        Indicate that the next filter call and the one following that should\n+        be treated as a single filter. This is only important when it comes to\n+        determining when to reuse tables for many-to-many filters. Required so\n+        that we can filter naturally on the results of related managers.\n+\n+        This doesn't return a clone of the current QuerySet (it returns\n+        \"self\"). The method is only used internally and should be immediately\n+        followed by a filter() that does create a clone.\n+        \"\"\"\n+        self._sticky_filter = True\n+        return self\n+\n+    def _merge_sanity_check(self, other):\n+        \"\"\"Check that two QuerySet classes may be merged.\"\"\"\n+        if self._fields is not None and (\n+            set(self.query.values_select) != set(other.query.values_select)\n+            or set(self.query.extra_select) != set(other.query.extra_select)\n+            or set(self.query.annotation_select) != set(other.query.annotation_select)\n+        ):\n+            raise TypeError(\n+                \"Merging '%s' classes must involve the same values in each case.\"\n+                % self.__class__.__name__\n+            )\n+\n+    def _merge_known_related_objects(self, other):\n+        \"\"\"\n+        Keep track of all known related objects from either QuerySet instance.\n+        \"\"\"\n+        for field, objects in other._known_related_objects.items():\n+            self._known_related_objects.setdefault(field, {}).update(objects)\n+\n+    def resolve_expression(self, *args, **kwargs):\n+        query = self.query.resolve_expression(*args, **kwargs)\n+        query._db = self._db\n+        return query\n+\n+    resolve_expression.queryset_only = True\n+\n+    def _add_hints(self, **hints):\n+        \"\"\"\n+        Update hinting information for use by routers. Add new key/values or\n+        overwrite existing key/values.\n+        \"\"\"\n+        self._hints.update(hints)\n+\n+    def _has_filters(self):\n+        \"\"\"\n+        Check if this QuerySet has any filtering going on. This isn't\n+        equivalent with checking if all objects are present in results, for\n+        example, qs[1:]._has_filters() -> False.\n+        \"\"\"\n+        return self.query.has_filters()\n+\n+    @staticmethod\n+    def _validate_values_are_expressions(values, method_name):\n+        invalid_args = sorted(\n+            str(arg) for arg in values if not hasattr(arg, \"resolve_expression\")\n+        )\n+        if invalid_args:\n+            raise TypeError(\n+                \"QuerySet.%s() received non-expression(s): %s.\"\n+                % (\n+                    method_name,\n+                    \", \".join(invalid_args),\n+                )\n+            )\n+\n+    def _not_support_combined_queries(self, operation_name):\n+        if self.query.combinator:\n+            raise NotSupportedError(\n+                \"Calling QuerySet.%s() after %s() is not supported.\"\n+                % (operation_name, self.query.combinator)\n+            )\n+\n+    def _check_operator_queryset(self, other, operator_):\n+        if self.query.combinator or other.query.combinator:\n+            raise TypeError(f\"Cannot use {operator_} operator with combined queryset.\")\n+\n+    def _check_ordering_first_last_queryset_aggregation(self, method):\n+        if isinstance(self.query.group_by, tuple) and not any(\n+            col.output_field is self.model._meta.pk for col in self.query.group_by\n+        ):\n+            raise TypeError(\n+                f\"Cannot use QuerySet.{method}() on an unordered queryset performing \"\n+                f\"aggregation. Add an ordering with order_by().\"\n+            )\n+\n+\n+class InstanceCheckMeta(type):\n+    def __instancecheck__(self, instance):\n+        return isinstance(instance, QuerySet) and instance.query.is_empty()\n+\n+\n+class EmptyQuerySet(metaclass=InstanceCheckMeta):\n+    \"\"\"\n+    Marker class to checking if a queryset is empty by .none():\n+        isinstance(qs.none(), EmptyQuerySet) -> True\n+    \"\"\"\n+\n+    def __init__(self, *args, **kwargs):\n+        raise TypeError(\"EmptyQuerySet can't be instantiated\")\n+\n+\n+class RawQuerySet:\n+    \"\"\"\n+    Provide an iterator which converts the results of raw SQL queries into\n+    annotated model instances.\n+    \"\"\"\n+\n+    def __init__(\n+        self,\n+        raw_query,\n+        model=None,\n+        query=None,\n+        params=(),\n+        translations=None,\n+        using=None,\n+        hints=None,\n+    ):\n+        self.raw_query = raw_query\n+        self.model = model\n+        self._db = using\n+        self._hints = hints or {}\n+        self.query = query or sql.RawQuery(sql=raw_query, using=self.db, params=params)\n+        self.params = params\n+        self.translations = translations or {}\n+        self._result_cache = None\n+        self._prefetch_related_lookups = ()\n+        self._prefetch_done = False\n+\n+    def resolve_model_init_order(self):\n+        \"\"\"Resolve the init field names and value positions.\"\"\"\n+        converter = connections[self.db].introspection.identifier_converter\n+        model_init_fields = [\n+            f for f in self.model._meta.fields if converter(f.column) in self.columns\n+        ]\n+        annotation_fields = [\n+            (column, pos)\n+            for pos, column in enumerate(self.columns)\n+            if column not in self.model_fields\n+        ]\n+        model_init_order = [\n+            self.columns.index(converter(f.column)) for f in model_init_fields\n+        ]\n+        model_init_names = [f.attname for f in model_init_fields]\n+        return model_init_names, model_init_order, annotation_fields\n+\n+    def prefetch_related(self, *lookups):\n+        \"\"\"Same as QuerySet.prefetch_related()\"\"\"\n+        clone = self._clone()\n+        if lookups == (None,):\n+            clone._prefetch_related_lookups = ()\n+        else:\n+            clone._prefetch_related_lookups = clone._prefetch_related_lookups + lookups\n+        return clone\n+\n+    def _prefetch_related_objects(self):\n+        prefetch_related_objects(self._result_cache, *self._prefetch_related_lookups)\n+        self._prefetch_done = True\n+\n+    def _clone(self):\n+        \"\"\"Same as QuerySet._clone()\"\"\"\n+        c = self.__class__(\n+            self.raw_query,\n+            model=self.model,\n+            query=self.query,\n+            params=self.params,\n+            translations=self.translations,\n+            using=self._db,\n+            hints=self._hints,\n+        )\n+        c._prefetch_related_lookups = self._prefetch_related_lookups[:]\n+        return c\n+\n+    def _fetch_all(self):\n+        if self._result_cache is None:\n+            self._result_cache = list(self.iterator())\n+        if self._prefetch_related_lookups and not self._prefetch_done:\n+            self._prefetch_related_objects()\n+\n+    def __len__(self):\n+        self._fetch_all()\n+        return len(self._result_cache)\n+\n+    def __bool__(self):\n+        self._fetch_all()\n+        return bool(self._result_cache)\n+\n+    def __iter__(self):\n+        self._fetch_all()\n+        return iter(self._result_cache)\n+\n+    def __aiter__(self):\n+        # Remember, __aiter__ itself is synchronous, it's the thing it returns\n+        # that is async!\n+        async def generator():\n+            await sync_to_async(self._fetch_all)()\n+            for item in self._result_cache:\n+                yield item\n+\n+        return generator()\n+\n+    def iterator(self):\n+        yield from RawModelIterable(self)\n+\n+    def __repr__(self):\n+        return \"<%s: %s>\" % (self.__class__.__name__, self.query)\n+\n+    def __getitem__(self, k):\n+        return list(self)[k]\n+\n+    @property\n+    def db(self):\n+        \"\"\"Return the database used if this query is executed now.\"\"\"\n+        return self._db or router.db_for_read(self.model, **self._hints)\n+\n+    def using(self, alias):\n+        \"\"\"Select the database this RawQuerySet should execute against.\"\"\"\n+        return RawQuerySet(\n+            self.raw_query,\n+            model=self.model,\n+            query=self.query.chain(using=alias),\n+            params=self.params,\n+            translations=self.translations,\n+            using=alias,\n+        )\n+\n+    @cached_property\n+    def columns(self):\n+        \"\"\"\n+        A list of model field names in the order they'll appear in the\n+        query results.\n+        \"\"\"\n+        columns = self.query.get_columns()\n+        # Adjust any column names which don't match field names\n+        for query_name, model_name in self.translations.items():\n+            # Ignore translations for nonexistent column names\n+            try:\n+                index = columns.index(query_name)\n+            except ValueError:\n+                pass\n+            else:\n+                columns[index] = model_name\n+        return columns\n+\n+    @cached_property\n+    def model_fields(self):\n+        \"\"\"A dict mapping column names to model field names.\"\"\"\n+        converter = connections[self.db].introspection.identifier_converter\n+        model_fields = {}\n+        for field in self.model._meta.fields:\n+            model_fields[converter(field.column)] = field\n+        return model_fields\n+\n+\n+class Prefetch:\n+    def __init__(self, lookup, queryset=None, to_attr=None):\n+        # `prefetch_through` is the path we traverse to perform the prefetch.\n+        self.prefetch_through = lookup\n+        # `prefetch_to` is the path to the attribute that stores the result.\n+        self.prefetch_to = lookup\n+        if queryset is not None and (\n+            isinstance(queryset, RawQuerySet)\n+            or (\n+                hasattr(queryset, \"_iterable_class\")\n+                and not issubclass(queryset._iterable_class, ModelIterable)\n+            )\n+        ):\n+            raise ValueError(\n+                \"Prefetch querysets cannot use raw(), values(), and values_list().\"\n+            )\n+        if to_attr:\n+            self.prefetch_to = LOOKUP_SEP.join(\n+                lookup.split(LOOKUP_SEP)[:-1] + [to_attr]\n+            )\n+\n+        self.queryset = queryset\n+        self.to_attr = to_attr\n+\n+    def __getstate__(self):\n+        obj_dict = self.__dict__.copy()\n+        if self.queryset is not None:\n+            queryset = self.queryset._chain()\n+            # Prevent the QuerySet from being evaluated\n+            queryset._result_cache = []\n+            queryset._prefetch_done = True\n+            obj_dict[\"queryset\"] = queryset\n+        return obj_dict\n+\n+    def add_prefix(self, prefix):\n+        self.prefetch_through = prefix + LOOKUP_SEP + self.prefetch_through\n+        self.prefetch_to = prefix + LOOKUP_SEP + self.prefetch_to\n+\n+    def get_current_prefetch_to(self, level):\n+        return LOOKUP_SEP.join(self.prefetch_to.split(LOOKUP_SEP)[: level + 1])\n+\n+    def get_current_to_attr(self, level):\n+        parts = self.prefetch_to.split(LOOKUP_SEP)\n+        to_attr = parts[level]\n+        as_attr = self.to_attr and level == len(parts) - 1\n+        return to_attr, as_attr\n+\n+    def get_current_querysets(self, level):\n+        if (\n+            self.get_current_prefetch_to(level) == self.prefetch_to\n+            and self.queryset is not None\n+        ):\n+            return [self.queryset]\n+        return None\n+\n+    def __eq__(self, other):\n+        if not isinstance(other, Prefetch):\n+            return NotImplemented\n+        return self.prefetch_to == other.prefetch_to\n+\n+    def __hash__(self):\n+        return hash((self.__class__, self.prefetch_to))\n+\n+\n+def normalize_prefetch_lookups(lookups, prefix=None):\n+    \"\"\"Normalize lookups into Prefetch objects.\"\"\"\n+    ret = []\n+    for lookup in lookups:\n+        if not isinstance(lookup, Prefetch):\n+            lookup = Prefetch(lookup)\n+        if prefix:\n+            lookup.add_prefix(prefix)\n+        ret.append(lookup)\n+    return ret\n+\n+\n+def prefetch_related_objects(model_instances, *related_lookups):\n+    \"\"\"\n+    Populate prefetched object caches for a list of model instances based on\n+    the lookups/Prefetch instances given.\n+    \"\"\"\n+    if not model_instances:\n+        return  # nothing to do\n+\n+    # We need to be able to dynamically add to the list of prefetch_related\n+    # lookups that we look up (see below).  So we need some book keeping to\n+    # ensure we don't do duplicate work.\n+    done_queries = {}  # dictionary of things like 'foo__bar': [results]\n+\n+    auto_lookups = set()  # we add to this as we go through.\n+    followed_descriptors = set()  # recursion protection\n+\n+    all_lookups = normalize_prefetch_lookups(reversed(related_lookups))\n+    while all_lookups:\n+        lookup = all_lookups.pop()\n+        if lookup.prefetch_to in done_queries:\n+            if lookup.queryset is not None:\n+                raise ValueError(\n+                    \"'%s' lookup was already seen with a different queryset. \"\n+                    \"You may need to adjust the ordering of your lookups.\"\n+                    % lookup.prefetch_to\n+                )\n+\n+            continue\n+\n+        # Top level, the list of objects to decorate is the result cache\n+        # from the primary QuerySet. It won't be for deeper levels.\n+        obj_list = model_instances\n+\n+        through_attrs = lookup.prefetch_through.split(LOOKUP_SEP)\n+        for level, through_attr in enumerate(through_attrs):\n+            # Prepare main instances\n+            if not obj_list:\n+                break\n+\n+            prefetch_to = lookup.get_current_prefetch_to(level)\n+            if prefetch_to in done_queries:\n+                # Skip any prefetching, and any object preparation\n+                obj_list = done_queries[prefetch_to]\n+                continue\n+\n+            # Prepare objects:\n+            good_objects = True\n+            for obj in obj_list:\n+                # Since prefetching can re-use instances, it is possible to have\n+                # the same instance multiple times in obj_list, so obj might\n+                # already be prepared.\n+                if not hasattr(obj, \"_prefetched_objects_cache\"):\n+                    try:\n+                        obj._prefetched_objects_cache = {}\n+                    except (AttributeError, TypeError):\n+                        # Must be an immutable object from\n+                        # values_list(flat=True), for example (TypeError) or\n+                        # a QuerySet subclass that isn't returning Model\n+                        # instances (AttributeError), either in Django or a 3rd\n+                        # party. prefetch_related() doesn't make sense, so quit.\n+                        good_objects = False\n+                        break\n+            if not good_objects:\n+                break\n+\n+            # Descend down tree\n+\n+            # We assume that objects retrieved are homogeneous (which is the premise\n+            # of prefetch_related), so what applies to first object applies to all.\n+            first_obj = obj_list[0]\n+            to_attr = lookup.get_current_to_attr(level)[0]\n+            prefetcher, descriptor, attr_found, is_fetched = get_prefetcher(\n+                first_obj, through_attr, to_attr\n+            )\n+\n+            if not attr_found:\n+                raise AttributeError(\n+                    \"Cannot find '%s' on %s object, '%s' is an invalid \"\n+                    \"parameter to prefetch_related()\"\n+                    % (\n+                        through_attr,\n+                        first_obj.__class__.__name__,\n+                        lookup.prefetch_through,\n+                    )\n+                )\n+\n+            if level == len(through_attrs) - 1 and prefetcher is None:\n+                # Last one, this *must* resolve to something that supports\n+                # prefetching, otherwise there is no point adding it and the\n+                # developer asking for it has made a mistake.\n+                raise ValueError(\n+                    \"'%s' does not resolve to an item that supports \"\n+                    \"prefetching - this is an invalid parameter to \"\n+                    \"prefetch_related().\" % lookup.prefetch_through\n+                )\n+\n+            obj_to_fetch = None\n+            if prefetcher is not None:\n+                obj_to_fetch = [obj for obj in obj_list if not is_fetched(obj)]\n+\n+            if obj_to_fetch:\n+                obj_list, additional_lookups = prefetch_one_level(\n+                    obj_to_fetch,\n+                    prefetcher,\n+                    lookup,\n+                    level,\n+                )\n+                # We need to ensure we don't keep adding lookups from the\n+                # same relationships to stop infinite recursion. So, if we\n+                # are already on an automatically added lookup, don't add\n+                # the new lookups from relationships we've seen already.\n+                if not (\n+                    prefetch_to in done_queries\n+                    and lookup in auto_lookups\n+                    and descriptor in followed_descriptors\n+                ):\n+                    done_queries[prefetch_to] = obj_list\n+                    new_lookups = normalize_prefetch_lookups(\n+                        reversed(additional_lookups), prefetch_to\n+                    )\n+                    auto_lookups.update(new_lookups)\n+                    all_lookups.extend(new_lookups)\n+                followed_descriptors.add(descriptor)\n+            else:\n+                # Either a singly related object that has already been fetched\n+                # (e.g. via select_related), or hopefully some other property\n+                # that doesn't support prefetching but needs to be traversed.\n+\n+                # We replace the current list of parent objects with the list\n+                # of related objects, filtering out empty or missing values so\n+                # that we can continue with nullable or reverse relations.\n+                new_obj_list = []\n+                for obj in obj_list:\n+                    if through_attr in getattr(obj, \"_prefetched_objects_cache\", ()):\n+                        # If related objects have been prefetched, use the\n+                        # cache rather than the object's through_attr.\n+                        new_obj = list(obj._prefetched_objects_cache.get(through_attr))\n+                    else:\n+                        try:\n+                            new_obj = getattr(obj, through_attr)\n+                        except exceptions.ObjectDoesNotExist:\n+                            continue\n+                    if new_obj is None:\n+                        continue\n+                    # We special-case `list` rather than something more generic\n+                    # like `Iterable` because we don't want to accidentally match\n+                    # user models that define __iter__.\n+                    if isinstance(new_obj, list):\n+                        new_obj_list.extend(new_obj)\n+                    else:\n+                        new_obj_list.append(new_obj)\n+                obj_list = new_obj_list\n+\n+\n+async def aprefetch_related_objects(model_instances, *related_lookups):\n+    \"\"\"See prefetch_related_objects().\"\"\"\n+    return await sync_to_async(prefetch_related_objects)(\n+        model_instances, *related_lookups\n+    )\n+\n+\n+def get_prefetcher(instance, through_attr, to_attr):\n+    \"\"\"\n+    For the attribute 'through_attr' on the given instance, find\n+    an object that has a get_prefetch_querysets().\n+    Return a 4 tuple containing:\n+    (the object with get_prefetch_querysets (or None),\n+     the descriptor object representing this relationship (or None),\n+     a boolean that is False if the attribute was not found at all,\n+     a function that takes an instance and returns a boolean that is True if\n+     the attribute has already been fetched for that instance)\n+    \"\"\"\n+\n+    def is_to_attr_fetched(model, to_attr):\n+        # Special case cached_property instances because hasattr() triggers\n+        # attribute computation and assignment.\n+        if isinstance(getattr(model, to_attr, None), cached_property):\n+\n+            def has_cached_property(instance):\n+                return to_attr in instance.__dict__\n+\n+            return has_cached_property\n+\n+        def has_to_attr_attribute(instance):\n+            return hasattr(instance, to_attr)\n+\n+        return has_to_attr_attribute\n+\n+    prefetcher = None\n+    is_fetched = is_to_attr_fetched(instance.__class__, to_attr)\n+\n+    # For singly related objects, we have to avoid getting the attribute\n+    # from the object, as this will trigger the query. So we first try\n+    # on the class, in order to get the descriptor object.\n+    rel_obj_descriptor = getattr(instance.__class__, through_attr, None)\n+    if rel_obj_descriptor is None:\n+        attr_found = hasattr(instance, through_attr)\n+    else:\n+        attr_found = True\n+        if rel_obj_descriptor:\n+            # singly related object, descriptor object has the\n+            # get_prefetch_querysets() method.\n+            if hasattr(rel_obj_descriptor, \"get_prefetch_querysets\"):\n+                prefetcher = rel_obj_descriptor\n+                # If to_attr is set, check if the value has already been set,\n+                # which is done with has_to_attr_attribute(). Do not use the\n+                # method from the descriptor, as the cache_name it defines\n+                # checks the field name, not the to_attr value.\n+                if through_attr == to_attr:\n+                    is_fetched = rel_obj_descriptor.is_cached\n+            else:\n+                # descriptor doesn't support prefetching, so we go ahead and get\n+                # the attribute on the instance rather than the class to\n+                # support many related managers\n+                rel_obj = getattr(instance, through_attr)\n+                if hasattr(rel_obj, \"get_prefetch_querysets\"):\n+                    prefetcher = rel_obj\n+                if through_attr == to_attr:\n+\n+                    def in_prefetched_cache(instance):\n+                        return through_attr in instance._prefetched_objects_cache\n+\n+                    is_fetched = in_prefetched_cache\n+    return prefetcher, rel_obj_descriptor, attr_found, is_fetched\n+\n+\n+def prefetch_one_level(instances, prefetcher, lookup, level):\n+    \"\"\"\n+    Helper function for prefetch_related_objects().\n+\n+    Run prefetches on all instances using the prefetcher object,\n+    assigning results to relevant caches in instance.\n+\n+    Return the prefetched objects along with any additional prefetches that\n+    must be done due to prefetch_related lookups found from default managers.\n+    \"\"\"\n+    # prefetcher must have a method get_prefetch_querysets() which takes a list\n+    # of instances, and returns a tuple:\n+\n+    # (queryset of instances of self.model that are related to passed in instances,\n+    #  callable that gets value to be matched for returned instances,\n+    #  callable that gets value to be matched for passed in instances,\n+    #  boolean that is True for singly related objects,\n+    #  cache or field name to assign to,\n+    #  boolean that is True when the previous argument is a cache name vs a field name).\n+\n+    # The 'values to be matched' must be hashable as they will be used\n+    # in a dictionary.\n+    (\n+        rel_qs,\n+        rel_obj_attr,\n+        instance_attr,\n+        single,\n+        cache_name,\n+        is_descriptor,\n+    ) = prefetcher.get_prefetch_querysets(\n+        instances, lookup.get_current_querysets(level)\n+    )\n+    # We have to handle the possibility that the QuerySet we just got back\n+    # contains some prefetch_related lookups. We don't want to trigger the\n+    # prefetch_related functionality by evaluating the query. Rather, we need\n+    # to merge in the prefetch_related lookups.\n+    # Copy the lookups in case it is a Prefetch object which could be reused\n+    # later (happens in nested prefetch_related).\n+    additional_lookups = [\n+        copy.copy(additional_lookup)\n+        for additional_lookup in getattr(rel_qs, \"_prefetch_related_lookups\", ())\n+    ]\n+    if additional_lookups:\n+        # Don't need to clone because the manager should have given us a fresh\n+        # instance, so we access an internal instead of using public interface\n+        # for performance reasons.\n+        rel_qs._prefetch_related_lookups = ()\n+\n+    all_related_objects = list(rel_qs)\n+\n+    rel_obj_cache = {}\n+    for rel_obj in all_related_objects:\n+        rel_attr_val = rel_obj_attr(rel_obj)\n+        rel_obj_cache.setdefault(rel_attr_val, []).append(rel_obj)\n+\n+    to_attr, as_attr = lookup.get_current_to_attr(level)\n+    # Make sure `to_attr` does not conflict with a field.\n+    if as_attr and instances:\n+        # We assume that objects retrieved are homogeneous (which is the premise\n+        # of prefetch_related), so what applies to first object applies to all.\n+        model = instances[0].__class__\n+        try:\n+            model._meta.get_field(to_attr)\n+        except exceptions.FieldDoesNotExist:\n+            pass\n+        else:\n+            msg = \"to_attr={} conflicts with a field on the {} model.\"\n+            raise ValueError(msg.format(to_attr, model.__name__))\n+\n+    # Whether or not we're prefetching the last part of the lookup.\n+    leaf = len(lookup.prefetch_through.split(LOOKUP_SEP)) - 1 == level\n+\n+    for obj in instances:\n+        instance_attr_val = instance_attr(obj)\n+        vals = rel_obj_cache.get(instance_attr_val, [])\n+\n+        if single:\n+            val = vals[0] if vals else None\n+            if as_attr:\n+                # A to_attr has been given for the prefetch.\n+                setattr(obj, to_attr, val)\n+            elif is_descriptor:\n+                # cache_name points to a field name in obj.\n+                # This field is a descriptor for a related object.\n+                setattr(obj, cache_name, val)\n+            else:\n+                # No to_attr has been given for this prefetch operation and the\n+                # cache_name does not point to a descriptor. Store the value of\n+                # the field in the object's field cache.\n+                obj._state.fields_cache[cache_name] = val\n+        else:\n+            if as_attr:\n+                setattr(obj, to_attr, vals)\n+            else:\n+                manager = getattr(obj, to_attr)\n+                if leaf and lookup.queryset is not None:\n+                    qs = manager._apply_rel_filters(lookup.queryset)\n+                else:\n+                    qs = manager.get_queryset()\n+                qs._result_cache = vals\n+                # We don't want the individual qs doing prefetch_related now,\n+                # since we have merged this into the current work.\n+                qs._prefetch_done = True\n+                obj._prefetched_objects_cache[cache_name] = qs\n+    return all_related_objects, additional_lookups\n+\n+\n+class RelatedPopulator:\n+    \"\"\"\n+    RelatedPopulator is used for select_related() object instantiation.\n+\n+    The idea is that each select_related() model will be populated by a\n+    different RelatedPopulator instance. The RelatedPopulator instances get\n+    klass_info and select (computed in SQLCompiler) plus the used db as\n+    input for initialization. That data is used to compute which columns\n+    to use, how to instantiate the model, and how to populate the links\n+    between the objects.\n+\n+    The actual creation of the objects is done in populate() method. This\n+    method gets row and from_obj as input and populates the select_related()\n+    model instance.\n+    \"\"\"\n+\n+    def __init__(self, klass_info, select, db):\n+        self.db = db\n+        # Pre-compute needed attributes. The attributes are:\n+        #  - model_cls: the possibly deferred model class to instantiate\n+        #  - either:\n+        #    - cols_start, cols_end: usually the columns in the row are\n+        #      in the same order model_cls.__init__ expects them, so we\n+        #      can instantiate by model_cls(*row[cols_start:cols_end])\n+        #    - reorder_for_init: When select_related descends to a child\n+        #      class, then we want to reuse the already selected parent\n+        #      data. However, in this case the parent data isn't necessarily\n+        #      in the same order that Model.__init__ expects it to be, so\n+        #      we have to reorder the parent data. The reorder_for_init\n+        #      attribute contains a function used to reorder the field data\n+        #      in the order __init__ expects it.\n+        #  - pk_idx: the index of the primary key field in the reordered\n+        #    model data. Used to check if a related object exists at all.\n+        #  - init_list: the field attnames fetched from the database. For\n+        #    deferred models this isn't the same as all attnames of the\n+        #    model's fields.\n+        #  - related_populators: a list of RelatedPopulator instances if\n+        #    select_related() descends to related models from this model.\n+        #  - local_setter, remote_setter: Methods to set cached values on\n+        #    the object being populated and on the remote object. Usually\n+        #    these are Field.set_cached_value() methods.\n+        select_fields = klass_info[\"select_fields\"]\n+        from_parent = klass_info[\"from_parent\"]\n+        if not from_parent:\n+            self.cols_start = select_fields[0]\n+            self.cols_end = select_fields[-1] + 1\n+            self.init_list = [\n+                f[0].target.attname for f in select[self.cols_start : self.cols_end]\n+            ]\n+            self.reorder_for_init = None\n+        else:\n+            attname_indexes = {\n+                select[idx][0].target.attname: idx for idx in select_fields\n+            }\n+            model_init_attnames = (\n+                f.attname for f in klass_info[\"model\"]._meta.concrete_fields\n+            )\n+            self.init_list = [\n+                attname for attname in model_init_attnames if attname in attname_indexes\n+            ]\n+            self.reorder_for_init = operator.itemgetter(\n+                *[attname_indexes[attname] for attname in self.init_list]\n+            )\n+\n+        self.model_cls = klass_info[\"model\"]\n+        self.pk_idx = self.init_list.index(self.model_cls._meta.pk.attname)\n+        self.related_populators = get_related_populators(klass_info, select, self.db)\n+        self.local_setter = klass_info[\"local_setter\"]\n+        self.remote_setter = klass_info[\"remote_setter\"]\n+\n+    def populate(self, row, from_obj):\n+        if self.reorder_for_init:\n+            obj_data = self.reorder_for_init(row)\n+        else:\n+            obj_data = row[self.cols_start : self.cols_end]\n+        if obj_data[self.pk_idx] is None:\n+            obj = None\n+        else:\n+            obj = self.model_cls.from_db(self.db, self.init_list, obj_data)\n+            for rel_iter in self.related_populators:\n+                rel_iter.populate(row, obj)\n+        self.local_setter(from_obj, obj)\n+        if obj is not None:\n+            self.remote_setter(obj, from_obj)\n+\n+\n+def get_related_populators(klass_info, select, db):\n+    iterators = []\n+    related_klass_infos = klass_info.get(\"related_klass_infos\", [])\n+    for rel_klass_info in related_klass_infos:\n+        rel_cls = RelatedPopulator(rel_klass_info, select, db)\n+        iterators.append(rel_cls)\n+    return iterators\nerror: Could not access 'django_clean/null'\nerror: Could not access 'django_tmp/null'\ndiff --git a/part1.txt b/part1.txt\nnew file mode 100644\nindex 0000000000..9c9c82d083\n--- /dev/null\n+++ b/part1.txt\n@@ -0,0 +1,672 @@\n+\"\"\"\n+The main QuerySet implementation. This provides the public API for the ORM.\n+\"\"\"\n+\n+import copy\n+import operator\n+import warnings\n+from collections import namedtuple\n+from functools import lru_cache\n+from itertools import chain\n+\n+from django.conf import settings\n+from django.core import exceptions\n+from django.db import (\n+    DJANGO_VERSION_PICKLE_KEY, IntegrityError, connections, router,\n+    transaction,\n+)\n+from django.db.models import DateField, DateTimeField, sql\n+from django.db.models.constants import LOOKUP_SEP\n+from django.db.models.deletion import Collector\n+from django.db.models.expressions import Case, Expression, F, Value, When\n+from django.db.models.fields import AutoField\n+from django.db.models.functions import Cast, Trunc\n+from django.db.models.query_utils import FilteredRelation, InvalidQuery, Q\n+from django.db.models.sql.constants import CURSOR, GET_ITERATOR_CHUNK_SIZE\n+from django.db.utils import NotSupportedError\n+from django.utils import timezone\n+from django.utils.functional import cached_property, partition\n+from django.utils.version import get_version\n+\n+# The maximum number of results to fetch in a get() query.\n+MAX_GET_RESULTS = 21\n+\n+# The maximum number of items to display in a QuerySet.__repr__\n+REPR_OUTPUT_SIZE = 20\n+\n+# Pull into this namespace for backwards compatibility.\n+EmptyResultSet = sql.EmptyResultSet\n+\n+\n+class BaseIterable:\n+    def __init__(self, queryset, chunked_fetch=False, chunk_size=GET_ITERATOR_CHUNK_SIZE):\n+        self.queryset = queryset\n+        self.chunked_fetch = chunked_fetch\n+        self.chunk_size = chunk_size\n+\n+\n+class ModelIterable(BaseIterable):\n+    \"\"\"Iterable that yields a model instance for each row.\"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        db = queryset.db\n+        compiler = queryset.query.get_compiler(using=db)\n+        # Execute the query. This will also fill compiler.select, klass_info,\n+        # and annotations.\n+        results = compiler.execute_sql(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)\n+        select, klass_info, annotation_col_map = (compiler.select, compiler.klass_info,\n+                                                  compiler.annotation_col_map)\n+        model_cls = klass_info['model']\n+        select_fields = klass_info['select_fields']\n+        model_fields_start, model_fields_end = select_fields[0], select_fields[-1] + 1\n+        init_list = [f[0].target.attname\n+                     for f in select[model_fields_start:model_fields_end]]\n+        related_populators = get_related_populators(klass_info, select, db)\n+        known_related_objects = [\n+            (field, related_objs, operator.attrgetter(*[\n+                field.attname\n+                if from_field == 'self' else\n+                queryset.model._meta.get_field(from_field).attname\n+                for from_field in field.from_fields\n+            ])) for field, related_objs in queryset._known_related_objects.items()\n+        ]\n+        for row in compiler.results_iter(results):\n+            obj = model_cls.from_db(db, init_list, row[model_fields_start:model_fields_end])\n+            for rel_populator in related_populators:\n+                rel_populator.populate(row, obj)\n+            if annotation_col_map:\n+                for attr_name, col_pos in annotation_col_map.items():\n+                    setattr(obj, attr_name, row[col_pos])\n+\n+            # Add the known related objects to the model.\n+            for field, rel_objs, rel_getter in known_related_objects:\n+                # Avoid overwriting objects loaded by, e.g., select_related().\n+                if field.is_cached(obj):\n+                    continue\n+                rel_obj_id = rel_getter(obj)\n+                try:\n+                    rel_obj = rel_objs[rel_obj_id]\n+                except KeyError:\n+                    pass  # May happen in qs1 | qs2 scenarios.\n+                else:\n+                    setattr(obj, field.name, rel_obj)\n+\n+            yield obj\n+\n+\n+class ValuesIterable(BaseIterable):\n+    \"\"\"\n+    Iterable returned by QuerySet.values() that yields a dict for each row.\n+    \"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        query = queryset.query\n+        compiler = query.get_compiler(queryset.db)\n+\n+        # extra(select=...) cols are always at the start of the row.\n+        names = [\n+            *query.extra_select,\n+            *query.values_select,\n+            *query.annotation_select,\n+        ]\n+        indexes = range(len(names))\n+        for row in compiler.results_iter(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size):\n+            yield {names[i]: row[i] for i in indexes}\n+\n+\n+class ValuesListIterable(BaseIterable):\n+    \"\"\"\n+    Iterable returned by QuerySet.values_list(flat=False) that yields a tuple\n+    for each row.\n+    \"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        query = queryset.query\n+        compiler = query.get_compiler(queryset.db)\n+\n+        if queryset._fields:\n+            # extra(select=...) cols are always at the start of the row.\n+            names = [\n+                *query.extra_select,\n+                *query.values_select,\n+                *query.annotation_select,\n+            ]\n+            fields = [*queryset._fields, *(f for f in query.annotation_select if f not in queryset._fields)]\n+            if fields != names:\n+                # Reorder according to fields.\n+                index_map = {name: idx for idx, name in enumerate(names)}\n+                rowfactory = operator.itemgetter(*[index_map[f] for f in fields])\n+                return map(\n+                    rowfactory,\n+                    compiler.results_iter(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)\n+                )\n+        return compiler.results_iter(tuple_expected=True, chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size)\n+\n+\n+class NamedValuesListIterable(ValuesListIterable):\n+    \"\"\"\n+    Iterable returned by QuerySet.values_list(named=True) that yields a\n+    namedtuple for each row.\n+    \"\"\"\n+\n+    @staticmethod\n+    @lru_cache()\n+    def create_namedtuple_class(*names):\n+        # Cache namedtuple() with @lru_cache() since it's too slow to be\n+        # called for every QuerySet evaluation.\n+        return namedtuple('Row', names)\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        if queryset._fields:\n+            names = queryset._fields\n+        else:\n+            query = queryset.query\n+            names = [*query.extra_select, *query.values_select, *query.annotation_select]\n+        tuple_class = self.create_namedtuple_class(*names)\n+        new = tuple.__new__\n+        for row in super().__iter__():\n+            yield new(tuple_class, row)\n+\n+\n+class FlatValuesListIterable(BaseIterable):\n+    \"\"\"\n+    Iterable returned by QuerySet.values_list(flat=True) that yields single\n+    values.\n+    \"\"\"\n+\n+    def __iter__(self):\n+        queryset = self.queryset\n+        compiler = queryset.query.get_compiler(queryset.db)\n+        for row in compiler.results_iter(chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size):\n+            yield row[0]\n+\n+\n+class QuerySet:\n+    \"\"\"Represent a lazy database lookup for a set of objects.\"\"\"\n+\n+    def __init__(self, model=None, query=None, using=None, hints=None):\n+        self.model = model\n+        self._db = using\n+        self._hints = hints or {}\n+        self.query = query or sql.Query(self.model)\n+        self._result_cache = None\n+        self._sticky_filter = False\n+        self._for_write = False\n+        self._prefetch_related_lookups = ()\n+        self._prefetch_done = False\n+        self._known_related_objects = {}  # {rel_field: {pk: rel_obj}}\n+        self._iterable_class = ModelIterable\n+        self._fields = None\n+\n+    def as_manager(cls):\n+        # Address the circular dependency between `Queryset` and `Manager`.\n+        from django.db.models.manager import Manager\n+        manager = Manager.from_queryset(cls)()\n+        manager._built_with_as_manager = True\n+        return manager\n+    as_manager.queryset_only = True\n+    as_manager = classmethod(as_manager)\n+\n+    ########################\n+    # PYTHON MAGIC METHODS #\n+    ########################\n+\n+    def __deepcopy__(self, memo):\n+        \"\"\"Don't populate the QuerySet's cache.\"\"\"\n+        obj = self.__class__()\n+        for k, v in self.__dict__.items():\n+            if k == '_result_cache':\n+                obj.__dict__[k] = None\n+            else:\n+                obj.__dict__[k] = copy.deepcopy(v, memo)\n+        return obj\n+\n+    def __getstate__(self):\n+        # Force the cache to be fully populated.\n+        self._fetch_all()\n+        return {**self.__dict__, DJANGO_VERSION_PICKLE_KEY: get_version()}\n+\n+    def __setstate__(self, state):\n+        msg = None\n+        pickled_version = state.get(DJANGO_VERSION_PICKLE_KEY)\n+        if pickled_version:\n+            current_version = get_version()\n+            if current_version != pickled_version:\n+                msg = (\n+                    \"Pickled queryset instance's Django version %s does not \"\n+                    \"match the current version %s.\" % (pickled_version, current_version)\n+                )\n+        else:\n+            msg = \"Pickled queryset instance's Django version is not specified.\"\n+\n+        if msg:\n+            warnings.warn(msg, RuntimeWarning, stacklevel=2)\n+\n+        self.__dict__.update(state)\n+\n+    def __repr__(self):\n+        data = list(self[:REPR_OUTPUT_SIZE + 1])\n+        if len(data) > REPR_OUTPUT_SIZE:\n+            data[-1] = \"...(remaining elements truncated)...\"\n+        return '<%s %r>' % (self.__class__.__name__, data)\n+\n+    def __len__(self):\n+        self._fetch_all()\n+        return len(self._result_cache)\n+\n+    def __iter__(self):\n+        \"\"\"\n+        The queryset iterator protocol uses three nested iterators in the\n+        default case:\n+            1. sql.compiler.execute_sql()\n+               - Returns 100 rows at time (constants.GET_ITERATOR_CHUNK_SIZE)\n+                 using cursor.fetchmany(). This part is responsible for\n+                 doing some column masking, and returning the rows in chunks.\n+            2. sql.compiler.results_iter()\n+               - Returns one row at time. At this point the rows are still just\n+                 tuples. In some cases the return values are converted to\n+                 Python values at this location.\n+            3. self.iterator()\n+               - Responsible for turning the rows into model objects.\n+        \"\"\"\n+        self._fetch_all()\n+        return iter(self._result_cache)\n+\n+    def __bool__(self):\n+        self._fetch_all()\n+        return bool(self._result_cache)\n+\n+    def __getitem__(self, k):\n+        \"\"\"Retrieve an item or slice from the set of results.\"\"\"\n+        if not isinstance(k, (int, slice)):\n+            raise TypeError(\n+                'QuerySet indices must be integers or slices, not %s.'\n+                % type(k).__name__\n+            )\n+        assert ((not isinstance(k, slice) and (k >= 0)) or\n+                (isinstance(k, slice) and (k.start is None or k.start >= 0) and\n+                 (k.stop is None or k.stop >= 0))), \\\n+            \"Negative indexing is not supported.\"\n+\n+        if self._result_cache is not None:\n+            return self._result_cache[k]\n+\n+        if isinstance(k, slice):\n+            qs = self._chain()\n+            if k.start is not None:\n+                start = int(k.start)\n+            else:\n+                start = None\n+            if k.stop is not None:\n+                stop = int(k.stop)\n+            else:\n+                stop = None\n+            qs.query.set_limits(start, stop)\n+            return list(qs)[::k.step] if k.step else qs\n+\n+        qs = self._chain()\n+        qs.query.set_limits(k, k + 1)\n+        qs._fetch_all()\n+        return qs._result_cache[0]\n+\n+    def __and__(self, other):\n+        self._merge_sanity_check(other)\n+        if isinstance(other, EmptyQuerySet):\n+            return other\n+        if isinstance(self, EmptyQuerySet):\n+            return self\n+        combined = self._chain()\n+        combined._merge_known_related_objects(other)\n+        combined.query.combine(other.query, sql.AND)\n+        return combined\n+\n+    def __or__(self, other):\n+        self._merge_sanity_check(other)\n+        if isinstance(self, EmptyQuerySet):\n+            return other\n+        if isinstance(other, EmptyQuerySet):\n+            return self\n+        query = self if self.query.can_filter() else self.model._base_manager.filter(pk__in=self.values('pk'))\n+        combined = query._chain()\n+        combined._merge_known_related_objects(other)\n+        if not other.query.can_filter():\n+            other = other.model._base_manager.filter(pk__in=other.values('pk'))\n+        combined.query.combine(other.query, sql.OR)\n+        return combined\n+\n+    ####################################\n+    # METHODS THAT DO DATABASE QUERIES #\n+    ####################################\n+\n+    def _iterator(self, use_chunked_fetch, chunk_size):\n+        yield from self._iterable_class(self, chunked_fetch=use_chunked_fetch, chunk_size=chunk_size)\n+\n+    def iterator(self, chunk_size=2000):\n+        \"\"\"\n+        An iterator over the results from applying this QuerySet to the\n+        database.\n+        \"\"\"\n+        if chunk_size <= 0:\n+            raise ValueError('Chunk size must be strictly positive.')\n+        use_chunked_fetch = not connections[self.db].settings_dict.get('DISABLE_SERVER_SIDE_CURSORS')\n+        return self._iterator(use_chunked_fetch, chunk_size)\n+\n+    def aggregate(self, *args, **kwargs):\n+        \"\"\"\n+        Return a dictionary containing the calculations (aggregation)\n+        over the current queryset.\n+\n+        If args is present the expression is passed as a kwarg using\n+        the Aggregate object's default alias.\n+        \"\"\"\n+        if self.query.distinct_fields:\n+            raise NotImplementedError(\"aggregate() + distinct(fields) not implemented.\")\n+        self._validate_values_are_expressions((*args, *kwargs.values()), method_name='aggregate')\n+        for arg in args:\n+            # The default_alias property raises TypeError if default_alias\n+            # can't be set automatically or AttributeError if it isn't an\n+            # attribute.\n+            try:\n+                arg.default_alias\n+            except (AttributeError, TypeError):\n+                raise TypeError(\"Complex aggregates require an alias\")\n+            kwargs[arg.default_alias] = arg\n+\n+        query = self.query.chain()\n+        for (alias, aggregate_expr) in kwargs.items():\n+            query.add_annotation(aggregate_expr, alias, is_summary=True)\n+            if not query.annotations[alias].contains_aggregate:\n+                raise TypeError(\"%s is not an aggregate expression\" % alias)\n+        return query.get_aggregation(self.db, kwargs)\n+\n+    def count(self):\n+        \"\"\"\n+        Perform a SELECT COUNT() and return the number of records as an\n+        integer.\n+\n+        If the QuerySet is already fully cached, return the length of the\n+        cached results set to avoid multiple SELECT COUNT(*) calls.\n+        \"\"\"\n+        if self._result_cache is not None:\n+            return len(self._result_cache)\n+\n+        return self.query.get_count(using=self.db)\n+\n+    def get(self, *args, **kwargs):\n+        \"\"\"\n+        Perform the query and return a single object matching the given\n+        keyword arguments.\n+        \"\"\"\n+        clone = self._chain() if self.query.combinator else self.filter(*args, **kwargs)\n+        if self.query.can_filter() and not self.query.distinct_fields:\n+            clone = clone.order_by()\n+        limit = None\n+        if not clone.query.select_for_update or connections[clone.db].features.supports_select_for_update_with_limit:\n+            limit = MAX_GET_RESULTS\n+            clone.query.set_limits(high=limit)\n+        num = len(clone)\n+        if num == 1:\n+            return clone._result_cache[0]\n+        if not num:\n+            raise self.model.DoesNotExist(\n+                \"%s matching query does not exist.\" %\n+                self.model._meta.object_name\n+            )\n+        raise self.model.MultipleObjectsReturned(\n+            'get() returned more than one %s -- it returned %s!' % (\n+                self.model._meta.object_name,\n+                num if not limit or num < limit else 'more than %s' % (limit - 1),\n+            )\n+        )\n+\n+    def create(self, **kwargs):\n+        \"\"\"\n+        Create a new object with the given kwargs, saving it to the database\n+        and returning the created object.\n+        \"\"\"\n+        obj = self.model(**kwargs)\n+        self._for_write = True\n+        obj.save(force_insert=True, using=self.db)\n+        return obj\n+\n+    def _populate_pk_values(self, objs):\n+        for obj in objs:\n+            if obj.pk is None:\n+                obj.pk = obj._meta.pk.get_pk_value_on_save(obj)\n+\n+    def bulk_create(self, objs, batch_size=None, ignore_conflicts=False):\n+        \"\"\"\n+        Insert each of the instances into the database. Do *not* call\n+        save() on each of the instances, do not send any pre/post_save\n+        signals, and do not set the primary key attribute if it is an\n+        autoincrement field (except if features.can_return_rows_from_bulk_insert=True).\n+        Multi-table models are not supported.\n+        \"\"\"\n+        # When you bulk insert you don't get the primary keys back (if it's an\n+        # autoincrement, except if can_return_rows_from_bulk_insert=True), so\n+        # you can't insert into the child tables which references this. There\n+        # are two workarounds:\n+        # 1) This could be implemented if you didn't have an autoincrement pk\n+        # 2) You could do it by doing O(n) normal inserts into the parent\n+        #    tables to get the primary keys back and then doing a single bulk\n+        #    insert into the childmost table.\n+        # We currently set the primary keys on the objects when using\n+        # PostgreSQL via the RETURNING ID clause. It should be possible for\n+        # Oracle as well, but the semantics for extracting the primary keys is\n+        # trickier so it's not done yet.\n+        assert batch_size is None or batch_size > 0\n+        # Check that the parents share the same concrete model with the our\n+        # model to detect the inheritance pattern ConcreteGrandParent ->\n+        # MultiTableParent -> ProxyChild. Simply checking self.model._meta.proxy\n+        # would not identify that case as involving multiple tables.\n+        for parent in self.model._meta.get_parent_list():\n+            if parent._meta.concrete_model is not self.model._meta.concrete_model:\n+                raise ValueError(\"Can't bulk create a multi-table inherited model\")\n+        if not objs:\n+            return objs\n+        self._for_write = True\n+        connection = connections[self.db]\n+        opts = self.model._meta\n+        fields = opts.concrete_fields\n+        objs = list(objs)\n+        self._populate_pk_values(objs)\n+        with transaction.atomic(using=self.db, savepoint=False):\n+            objs_with_pk, objs_without_pk = partition(lambda o: o.pk is None, objs)\n+            if objs_with_pk:\n+                returned_columns = self._batched_insert(\n+                    objs_with_pk, fields, batch_size, ignore_conflicts=ignore_conflicts,\n+                )\n+                for obj_with_pk, results in zip(objs_with_pk, returned_columns):\n+                    for result, field in zip(results, opts.db_returning_fields):\n+                        if field != opts.pk:\n+                            setattr(obj_with_pk, field.attname, result)\n+                for obj_with_pk in objs_with_pk:\n+                    obj_with_pk._state.adding = False\n+                    obj_with_pk._state.db = self.db\n+            if objs_without_pk:\n+                fields = [f for f in fields if not isinstance(f, AutoField)]\n+                returned_columns = self._batched_insert(\n+                    objs_without_pk, fields, batch_size, ignore_conflicts=ignore_conflicts,\n+                )\n+                if connection.features.can_return_rows_from_bulk_insert and not ignore_conflicts:\n+                    assert len(returned_columns) == len(objs_without_pk)\n+                for obj_without_pk, results in zip(objs_without_pk, returned_columns):\n+                    for result, field in zip(results, opts.db_returning_fields):\n+                        setattr(obj_without_pk, field.attname, result)\n+                    obj_without_pk._state.adding = False\n+                    obj_without_pk._state.db = self.db\n+\n+        return objs\n+\n+    def bulk_update(self, objs, fields, batch_size=None):\n+        \"\"\"\n+        Update the given fields in each of the given objects in the database.\n+        \"\"\"\n+        if batch_size is not None and batch_size < 0:\n+            raise ValueError('Batch size must be a positive integer.')\n+        if not fields:\n+            raise ValueError('Field names must be given to bulk_update().')\n+        objs = tuple(objs)\n+        if any(obj.pk is None for obj in objs):\n+            raise ValueError('All bulk_update() objects must have a primary key set.')\n+        fields = [self.model._meta.get_field(name) for name in fields]\n+        if any(not f.concrete or f.many_to_many for f in fields):\n+            raise ValueError('bulk_update() can only be used with concrete fields.')\n+        if any(f.primary_key for f in fields):\n+            raise ValueError('bulk_update() cannot be used with primary key fields.')\n+        if not objs:\n+            return\n+        # PK is used twice in the resulting update query, once in the filter\n+        # and once in the WHEN. Each field will also have one CAST.\n+        max_batch_size = connections[self.db].ops.bulk_batch_size(['pk', 'pk'] + fields, objs)\n+        batch_size = min(batch_size, max_batch_size) if batch_size else max_batch_size\n+        requires_casting = connections[self.db].features.requires_casted_case_in_updates\n+        batches = (objs[i:i + batch_size] for i in range(0, len(objs), batch_size))\n+        updates = []\n+        for batch_objs in batches:\n+            update_kwargs = {}\n+            for field in fields:\n+                when_statements = []\n+                for obj in batch_objs:\n+                    attr = getattr(obj, field.attname)\n+                    if not isinstance(attr, Expression):\n+                        attr = Value(attr, output_field=field)\n+                    when_statements.append(When(pk=obj.pk, then=attr))\n+                case_statement = Case(*when_statements, output_field=field)\n+                if requires_casting:\n+                    case_statement = Cast(case_statement, output_field=field)\n+                update_kwargs[field.attname] = case_statement\n+            updates.append(([obj.pk for obj in batch_objs], update_kwargs))\n+        with transaction.atomic(using=self.db, savepoint=False):\n+            for pks, update_kwargs in updates:\n+                self.filter(pk__in=pks).update(**update_kwargs)\n+    bulk_update.alters_data = True\n+\n+    def get_or_create(self, defaults=None, **kwargs):\n+        \"\"\"\n+        Look up an object with the given kwargs, creating one if necessary.\n+        Return a tuple of (object, created), where created is a boolean\n+        specifying whether an object was created.\n+        \"\"\"\n+        # The get() needs to be targeted at the write database in order\n+        # to avoid potential transaction consistency problems.\n+        self._for_write = True\n+        try:\n+            return self.get(**kwargs), False\n+        except self.model.DoesNotExist:\n+            params = self._extract_model_params(defaults, **kwargs)\n+            return self._create_object_from_params(kwargs, params)\n+\n+    def update_or_create(self, defaults=None, **kwargs):\n+        \"\"\"\n+        Look up an object with the given kwargs, updating one with defaults\n+        if it exists, otherwise create a new one.\n+        Return a tuple (object, created), where created is a boolean\n+        specifying whether an object was created.\n+        \"\"\"\n+        defaults = defaults or {}\n+        self._for_write = True\n+        with transaction.atomic(using=self.db):\n+            try:\n+                obj = self.select_for_update().get(**kwargs)\n+            except self.model.DoesNotExist:\n+                params = self._extract_model_params(defaults, **kwargs)\n+                # Lock the row so that a concurrent update is blocked until\n+                # after update_or_create() has performed its save.\n+                obj, created = self._create_object_from_params(kwargs, params, lock=True)\n+                if created:\n+                    return obj, created\n+            for k, v in defaults.items():\n+                setattr(obj, k, v() if callable(v) else v)\n+            obj.save(using=self.db)\n+        return obj, False\n+\n+    def _create_object_from_params(self, lookup, params, lock=False):\n+        \"\"\"\n+        Try to create an object using passed params. Used by get_or_create()\n+        and update_or_create().\n+        \"\"\"\n+        try:\n+            with transaction.atomic(using=self.db):\n+                params = {k: v() if callable(v) else v for k, v in params.items()}\n+                obj = self.create(**params)\n+            return obj, True\n+        except IntegrityError as e:\n+            try:\n+                qs = self.select_for_update() if lock else self\n+                return qs.get(**lookup), False\n+            except self.model.DoesNotExist:\n+                pass\n+            raise e\n+\n+    def _extract_model_params(self, defaults, **kwargs):\n+        \"\"\"\n+        Prepare `params` for creating a model instance based on the given\n+        kwargs; for use by get_or_create() and update_or_create().\n+        \"\"\"\n+        defaults = defaults or {}\n+        params = {k: v for k, v in kwargs.items() if LOOKUP_SEP not in k}\n+        params.update(defaults)\n+        property_names = self.model._meta._property_names\n+        invalid_params = []\n+        for param in params:\n+            try:\n+                self.model._meta.get_field(param)\n+            except exceptions.FieldDoesNotExist:\n+                # It's okay to use a model's property if it has a setter.\n+                if not (param in property_names and getattr(self.model, param).fset):\n+                    invalid_params.append(param)\n+        if invalid_params:\n+            raise exceptions.FieldError(\n+                \"Invalid field name(s) for model %s: '%s'.\" % (\n+                    self.model._meta.object_name,\n+                    \"', '\".join(sorted(invalid_params)),\n+                ))\n+        return params\n+\n+    def _earliest(self, *fields):\n+        \"\"\"\n+        Return the earliest object according to fields (if given) or by the\n+        model's Meta.get_latest_by.\n+        \"\"\"\n+        if fields:\n+            order_by = fields\n+        else:\n+            order_by = getattr(self.model._meta, 'get_latest_by')\n+            if order_by and not isinstance(order_by, (tuple, list)):\n+                order_by = (order_by,)\n+        if order_by is None:\n+            raise ValueError(\n+                \"earliest() and latest() require either fields as positional \"\n+                \"arguments or 'get_latest_by' in the model's Meta.\"\n+            )\n+\n+        assert not self.query.is_sliced, \\\n+            \"Cannot change a query once a slice has been taken.\"\n+        obj = self._chain()\n+        obj.query.set_limits(high=1)\n+        obj.query.clear_ordering(force_empty=True)\n+        obj.query.add_ordering(*order_by)\n+        return obj.get()\n+\n+    def earliest(self, *fields):\n+        return self._earliest(*fields)\n+\n+    def latest(self, *fields):\n+        return self.reverse()._earliest(*fields)\n+\n+    def first(self):\n+        \"\"\"Return the first object of a query or None if no match is found.\"\"\"\n+        for obj in (self if self.ordered else self.order_by('pk'))[:1]:\n+            return obj\n+\n+    def last(self):\n+        \"\"\"Return the last object of a query or None if no match is found.\"\"\"\n+        for obj in (self.reverse() if self.ordered else self.order_by('-pk'))[:1]:\n+            return obj\n+\n+    def in_bulk(self, id_list=None, *, field_name='pk'):\ndiff --git a/part2.txt b/part2.txt\nnew file mode 100644\nindex 0000000000..6506f4e294\n--- /dev/null\n+++ b/part2.txt\n@@ -0,0 +1,44 @@\n+    def in_bulk(self, id_list=None, *, field_name='pk'):\n+        \"\"\"\n+        Return a dictionary mapping each of the given IDs to the object with\n+        that ID. If `id_list` isn't provided, evaluate the entire QuerySet.\n+        \"\"\"\n+        assert not self.query.is_sliced, \\\n+            \"Cannot use 'limit' or 'offset' with in_bulk\"\n+        \n+        opts = self.model._meta\n+        field = opts.get_field(field_name)\n+\n+        # Check if the field is unique either through unique=True or UniqueConstraint\n+        is_unique = (\n+            field.unique or \n+            any(\n+                isinstance(constraint, sql.UniqueConstraint) and \n+                len(constraint.fields) == 1 and\n+                constraint.fields[0] == field_name\n+                for constraint in opts.constraints\n+            )\n+        )\n+\n+        if field_name != 'pk' and not is_unique:\n+            raise ValueError(\"in_bulk()'s field_name must be a unique field but %r isn't.\" % field_name)\n+\n+        if id_list is not None:\n+            if not id_list:\n+                return {}\n+            filter_key = '{}__in'.format(field_name)\n+            batch_size = connections[self.db].features.max_query_params\n+            id_list = tuple(id_list)\n+            # If the database has a limit on the number of query parameters\n+            # split the values into batches and merge the results together.\n+            if batch_size and batch_size < len(id_list):\n+                qs = []\n+                for offset in range(0, len(id_list), batch_size):\n+                    batch = id_list[offset:offset + batch_size]\n+                    qs.extend(self.filter(**{filter_key: batch}).order_by())\n+                qs = {getattr(obj, field_name): obj for obj in qs}\n+            else:\n+                qs = {getattr(obj, field_name): obj for obj in self.filter(**{filter_key: id_list}).order_by()}\n+        else:\n+            qs = {getattr(obj, field_name): obj for obj in self.order_by()}\n+        return qs\n\\ No newline at end of file\ndiff --git a/patch.py b/patch.py\nnew file mode 100644\nindex 0000000000..829acd07c3\n--- /dev/null\n+++ b/patch.py\n@@ -0,0 +1,45 @@\n+import re\n+\n+with open('/testbed/django/db/models/query.py', 'r') as f:\n+    content = f.read()\n+\n+old_method = '''    def in_bulk(self, id_list=None, *, field_name='pk'):\n+        \"\"\"\n+        Return a dictionary mapping each of the given IDs to the object with\n+        that ID. If `id_list` isn't provided, evaluate the entire QuerySet.\n+        \"\"\"\n+        assert not self.query.is_sliced, \\\\\n+            \"Cannot use 'limit' or 'offset' with in_bulk\"\n+        field = self.model._meta.get_field(field_name)\n+        if field_name != 'pk' and not field.unique:\n+            raise ValueError(\"in_bulk()'s field_name must be a unique field but %r isn't.\" % field_name)'''\n+\n+new_method = '''    def in_bulk(self, id_list=None, *, field_name='pk'):\n+        \"\"\"\n+        Return a dictionary mapping each of the given IDs to the object with\n+        that ID. If `id_list` isn't provided, evaluate the entire QuerySet.\n+        \"\"\"\n+        assert not self.query.is_sliced, \\\\\n+            \"Cannot use 'limit' or 'offset' with in_bulk\"\n+        \n+        opts = self.model._meta\n+        field = opts.get_field(field_name)\n+\n+        # Check if the field is unique either through unique=True or UniqueConstraint\n+        is_unique = (\n+            field.unique or \n+            any(\n+                isinstance(constraint, sql.UniqueConstraint) and \n+                len(constraint.fields) == 1 and\n+                constraint.fields[0] == field_name\n+                for constraint in opts.constraints\n+            )\n+        )\n+\n+        if field_name != 'pk' and not is_unique:\n+            raise ValueError(\"in_bulk()'s field_name must be a unique field but %r isn't.\" % field_name)'''\n+\n+content = content.replace(old_method, new_method)\n+\n+with open('/testbed/django/db/models/query.py', 'w') as f:\n+    f.write(content)\n\\ No newline at end of file\ndiff --git a/patch.txt b/patch.txt\nnew file mode 100644\nindex 0000000000..ac5f714a93\n--- /dev/null\n+++ b/patch.txt\n@@ -0,0 +1,25 @@\n+--- django/db/models/query.py.orig\t2019-12-17 14:00:00.000000000 +0000\n++++ django/db/models/query.py\t2023-07-08 14:00:00.000000000 +0000\n+@@ -685,10 +685,19 @@\n+         \"\"\"\n+         assert not self.query.is_sliced, \\\n+             \"Cannot use 'limit' or 'offset' with in_bulk\"\n+-        field = self.model._meta.get_field(field_name)\n+-        if field_name != 'pk' and not field.unique:\n++        \n++        opts = self.model._meta\n++        field = opts.get_field(field_name)\n++\n++        # Check if the field is unique either through unique=True or UniqueConstraint\n++        is_unique = (\n++            field.unique or \n++            any(\n++                isinstance(constraint, sql.UniqueConstraint) and \n++                len(constraint.fields) == 1 and\n++                constraint.fields[0] == field_name\n++                for constraint in opts.constraints\n++            )\n++        )\n++\n++        if field_name != 'pk' and not is_unique:\n+             raise ValueError(\"in_bulk()'s field_name must be a unique field but %r isn't.\" % field_name)\n\\ No newline at end of file\ndiff --git a/tests/queries/test_unique_constraint.py b/tests/queries/test_unique_constraint.py\nnew file mode 100644\nindex 0000000000..e66270ef7b\n--- /dev/null\n+++ b/tests/queries/test_unique_constraint.py\n@@ -0,0 +1,36 @@\n+from django.db import models\n+from django.test import TestCase\n+\n+\n+class ArticleWithConstraint(models.Model):\n+    slug = models.CharField(max_length=255)\n+\n+    class Meta:\n+        app_label = 'queries'\n+        constraints = [\n+            models.UniqueConstraint(fields=[\"slug\"], name=\"article_slug_unq\")\n+        ]\n+\n+\n+class InBulkUniqueConstraintTests(TestCase):\n+    @classmethod\n+    def setUpTestData(cls):\n+        cls.articles = [\n+            ArticleWithConstraint.objects.create(slug=\"first-article\"),\n+            ArticleWithConstraint.objects.create(slug=\"second-article\"),\n+            ArticleWithConstraint.objects.create(slug=\"third-article\"),\n+        ]\n+\n+    def test_in_bulk_with_unique_constraint(self):\n+        \"\"\"Test in_bulk() using a field with UniqueConstraint.\"\"\"\n+        slugs = [\"first-article\", \"second-article\"]\n+        articles = ArticleWithConstraint.objects.in_bulk(slugs, field_name=\"slug\")\n+        self.assertEqual(len(articles), 2)\n+        self.assertEqual(\n+            articles[\"first-article\"].slug,\n+            \"first-article\",\n+        )\n+        self.assertEqual(\n+            articles[\"second-article\"].slug,\n+            \"second-article\",\n+        )\n\\ No newline at end of file\n", "proposed_model_patches": []}