#!/usr/bin/env python3
"""
Simple test to verify the tool parameter fix.
"""

import tempfile
import subprocess
from pathlib import Path

def test_simple_fix():
    """Test if we can get the model to use the correct parameter name."""
    print("🧪 Testing simple tool parameter fix...")
    
    try:
        from llm_withtools import chat_with_agent
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            test_file = Path(temp_dir) / "test.py"
            test_file.write_text("print('hello world')")
            
            # Very explicit message about parameter names
            msg = f"""I need you to modify the file {test_file} to print 'hello universe' instead of 'hello world'.

IMPORTANT: When using the editor tool, use these exact parameter names:
- command: "edit"
- path: "{test_file}"
- file_text: "print('hello universe')"

Do NOT use 'content' as a parameter name. Use 'file_text' instead.

Please use the editor tool now."""
            
            print(f"   Test file: {test_file}")
            print(f"   Original content: {test_file.read_text()}")
            
            # Call the agent with explicit instructions
            msg_history = chat_with_agent(
                msg=msg,
                model='qwen3:30b-a3b',
                msg_history=[],
                logging=print
            )
            
            # Check if file was modified
            new_content = test_file.read_text()
            print(f"   Final content: {new_content}")
            
            if 'universe' in new_content:
                print("   ✅ Tool parameter fix successful!")
                return True
            else:
                print("   ❌ Tool parameter fix failed.")
                return False
                
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the simple fix test."""
    print("🧪 **SIMPLE TOOL PARAMETER FIX TEST**")
    print("=" * 50)
    
    success = test_simple_fix()
    
    if success:
        print("\n🎉 **SUCCESS!** Tool parameter issue is resolved!")
        print("The model can now use tools with correct parameter names.")
    else:
        print("\n❌ **FAILED!** Tool parameter issue persists.")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
