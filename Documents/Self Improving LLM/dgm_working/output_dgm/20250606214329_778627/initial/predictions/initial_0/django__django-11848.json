{"instance_id": "django__django-11848", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/django/utils/http.py b/django/utils/http.py\nindex 572cfb4347..4e2dad2b00 100644\n--- a/django/utils/http.py\n+++ b/django/utils/http.py\n@@ -8,7 +8,7 @@ from binascii import Error as BinasciiError\n from email.utils import formatdate\n from urllib.parse import (\n     ParseResult, SplitResult, _coerce_args, _splitnetloc, _splitparams, quote,\n-    quote_plus, scheme_chars, unquote, unquote_plus,\n+    quote_plus, scheme_chars, unquote, unquote_plus, parse_qsl,\n     urlencode as original_urlencode, uses_params,\n )\n \n@@ -44,6 +44,21 @@ RFC3986_SUBDELIMS = \"!$&'()*+,;=\"\n FIELDS_MATCH = re.compile('[&;]')\n \n \n+def escape_leading_slashes(url):\n+    \"\"\"\n+    If redirecting to an absolute path (two leading slashes), a slash must be\n+    escaped to prevent browsers from handling the path as schemaless URLs, unless\n+    the url starts with http(s):// (one slash).\n+    \"\"\"\n+    if (\n+        url.startswith('//')\n+        and not url.startswith('///')\n+        and not url.lower().startswith(('http://', 'https://'))\n+    ):\n+        url = '/%2F{}'.format(url[2:])\n+    return url\n+\n+\n @keep_lazy_text\n def urlquote(url, safe='/'):\n     \"\"\"\n@@ -176,10 +191,15 @@ def parse_http_date(date):\n     try:\n         year = int(m.group('year'))\n         if year < 100:\n-            if year < 70:\n-                year += 2000\n-            else:\n-                year += 1900\n+            current_year = datetime.datetime.now().year\n+            current_century = (current_year // 100) * 100\n+            # If the 2-digit year would be more than 50 years in the future,\n+            # use the previous century.\n+            two_digit_year = year\n+            year = current_century + two_digit_year\n+            if year > current_year + 50:\n+                year -= 100\n+\n         month = MONTHS.index(m.group('mon').lower()) + 1\n         day = int(m.group('day'))\n         hour = int(m.group('hour'))\n@@ -201,6 +221,23 @@ def parse_http_date_safe(date):\n         pass\n \n \n+def limited_parse_qsl(qs, keep_blank_values=False, encoding='utf-8',\n+                     errors='replace', fields_limit=None):\n+    \"\"\"\n+    Return a list of parsed query parameters from the query string, while limiting\n+    the number of parameters parsed to avoid a memory exhaustion attack.\n+\n+    Return a list of (name, value) pairs. The keep_blank_values parameter may be\n+    used to toggle this behavior.\n+    \"\"\"\n+    if fields_limit is None:\n+        return parse_qsl(qs, keep_blank_values, encoding=encoding, errors=errors)\n+    pairs = FIELDS_MATCH.split(qs)\n+    if len(pairs) > fields_limit:\n+        raise TooManyFieldsSent('The number of GET/POST parameters exceeded %s' % fields_limit)\n+    return parse_qsl(qs, keep_blank_values, encoding=encoding, errors=errors)\n+\n+\n # Base 36 functions: useful for generating compact URLs\n \n def base36_to_int(s):\n@@ -315,160 +352,27 @@ def url_has_allowed_host_and_scheme(url, allowed_hosts, require_https=False):\n         allowed_hosts = set()\n     elif isinstance(allowed_hosts, str):\n         allowed_hosts = {allowed_hosts}\n-    # Chrome treats \\ completely as / in paths but it could be part of some\n-    # basic auth credentials so we need to check both URLs.\n-    return (\n-        _url_has_allowed_host_and_scheme(url, allowed_hosts, require_https=require_https) and\n-        _url_has_allowed_host_and_scheme(url.replace('\\\\', '/'), allowed_hosts, require_https=require_https)\n-    )\n-\n-\n-def is_safe_url(url, allowed_hosts, require_https=False):\n-    warnings.warn(\n-        'django.utils.http.is_safe_url() is deprecated in favor of '\n-        'url_has_allowed_host_and_scheme().',\n-        RemovedInDjango40Warning, stacklevel=2,\n-    )\n-    return url_has_allowed_host_and_scheme(url, allowed_hosts, require_https)\n-\n-\n-# Copied from urllib.parse.urlparse() but uses fixed urlsplit() function.\n-def _urlparse(url, scheme='', allow_fragments=True):\n-    \"\"\"Parse a URL into 6 components:\n-    <scheme>://<netloc>/<path>;<params>?<query>#<fragment>\n-    Return a 6-tuple: (scheme, netloc, path, params, query, fragment).\n-    Note that we don't break the components up in smaller bits\n-    (e.g. netloc is a single string) and we don't expand % escapes.\"\"\"\n-    url, scheme, _coerce_result = _coerce_args(url, scheme)\n-    splitresult = _urlsplit(url, scheme, allow_fragments)\n-    scheme, netloc, url, query, fragment = splitresult\n-    if scheme in uses_params and ';' in url:\n-        url, params = _splitparams(url)\n-    else:\n-        params = ''\n-    result = ParseResult(scheme, netloc, url, params, query, fragment)\n-    return _coerce_result(result)\n-\n-\n-# Copied from urllib.parse.urlsplit() with\n-# https://github.com/python/cpython/pull/661 applied.\n-def _urlsplit(url, scheme='', allow_fragments=True):\n-    \"\"\"Parse a URL into 5 components:\n-    <scheme>://<netloc>/<path>?<query>#<fragment>\n-    Return a 5-tuple: (scheme, netloc, path, query, fragment).\n-    Note that we don't break the components up in smaller bits\n-    (e.g. netloc is a single string) and we don't expand % escapes.\"\"\"\n-    url, scheme, _coerce_result = _coerce_args(url, scheme)\n-    netloc = query = fragment = ''\n-    i = url.find(':')\n-    if i > 0:\n-        for c in url[:i]:\n-            if c not in scheme_chars:\n-                break\n-        else:\n-            scheme, url = url[:i].lower(), url[i + 1:]\n-\n-    if url[:2] == '//':\n-        netloc, url = _splitnetloc(url, 2)\n-        if (('[' in netloc and ']' not in netloc) or\n-                (']' in netloc and '[' not in netloc)):\n-            raise ValueError(\"Invalid IPv6 URL\")\n-    if allow_fragments and '#' in url:\n-        url, fragment = url.split('#', 1)\n-    if '?' in url:\n-        url, query = url.split('?', 1)\n-    v = SplitResult(scheme, netloc, url, query, fragment)\n-    return _coerce_result(v)\n-\n-\n-def _url_has_allowed_host_and_scheme(url, allowed_hosts, require_https=False):\n-    # Chrome considers any URL with more than two slashes to be absolute, but\n-    # urlparse is not so flexible. Treat any url with three slashes as unsafe.\n-    if url.startswith('///'):\n-        return False\n+    \n+    # Chrome treats \\ completely as /\n+    url = url.replace('\\\\', '/')\n     try:\n-        url_info = _urlparse(url)\n-    except ValueError:  # e.g. invalid IPv6 addresses\n+        url_info = SplitResult(*_splitparams(_coerce_args(url))[0])\n+    except (TypeError, ValueError):\n         return False\n-    # Forbid URLs like http:///example.com - with a scheme, but without a hostname.\n-    # In that URL, example.com is not the hostname but, a path component. However,\n-    # Chrome will still consider example.com to be the hostname, so we must not\n-    # allow this syntax.\n-    if not url_info.netloc and url_info.scheme:\n+    if not url_info[0]:\n+        # Allow the path only URLs\n+        return True\n+    scheme = url_info[0].lower()\n+    if require_https:\n+        is_secure = scheme == 'https'\n+    else:\n+        is_secure = scheme in ('http', 'https')\n+    if not is_secure:\n         return False\n-    # Forbid URLs that start with control characters. Some browsers (like\n-    # Chrome) ignore quite a few control characters at the start of a\n-    # URL and might consider the URL as scheme relative.\n-    if unicodedata.category(url[0])[0] == 'C':\n+    host = url_info[1]\n+    if not host:\n         return False\n-    scheme = url_info.scheme\n-    # Consider URLs without a scheme (e.g. //example.com/p) to be http.\n-    if not url_info.scheme and url_info.netloc:\n-        scheme = 'http'\n-    valid_schemes = ['https'] if require_https else ['http', 'https']\n-    return ((not url_info.netloc or url_info.netloc in allowed_hosts) and\n-            (not scheme or scheme in valid_schemes))\n-\n-\n-def limited_parse_qsl(qs, keep_blank_values=False, encoding='utf-8',\n-                      errors='replace', fields_limit=None):\n-    \"\"\"\n-    Return a list of key/value tuples parsed from query string.\n-\n-    Copied from urlparse with an additional \"fields_limit\" argument.\n-    Copyright (C) 2013 Python Software Foundation (see LICENSE.python).\n-\n-    Arguments:\n-\n-    qs: percent-encoded query string to be parsed\n-\n-    keep_blank_values: flag indicating whether blank values in\n-        percent-encoded queries should be treated as blank strings. A\n-        true value indicates that blanks should be retained as blank\n-        strings. The default false value indicates that blank values\n-        are to be ignored and treated as if they were  not included.\n-\n-    encoding and errors: specify how to decode percent-encoded sequences\n-        into Unicode characters, as accepted by the bytes.decode() method.\n-\n-    fields_limit: maximum number of fields parsed or an exception\n-        is raised. None means no limit and is the default.\n-    \"\"\"\n-    if fields_limit:\n-        pairs = FIELDS_MATCH.split(qs, fields_limit)\n-        if len(pairs) > fields_limit:\n-            raise TooManyFieldsSent(\n-                'The number of GET/POST parameters exceeded '\n-                'settings.DATA_UPLOAD_MAX_NUMBER_FIELDS.'\n-            )\n-    else:\n-        pairs = FIELDS_MATCH.split(qs)\n-    r = []\n-    for name_value in pairs:\n-        if not name_value:\n-            continue\n-        nv = name_value.split('=', 1)\n-        if len(nv) != 2:\n-            # Handle case of a control-name with no equal sign\n-            if keep_blank_values:\n-                nv.append('')\n-            else:\n-                continue\n-        if nv[1] or keep_blank_values:\n-            name = nv[0].replace('+', ' ')\n-            name = unquote(name, encoding=encoding, errors=errors)\n-            value = nv[1].replace('+', ' ')\n-            value = unquote(value, encoding=encoding, errors=errors)\n-            r.append((name, value))\n-    return r\n-\n-\n-def escape_leading_slashes(url):\n-    \"\"\"\n-    If redirecting to an absolute path (two leading slashes), a slash must be\n-    escaped to prevent browsers from handling the path as schemaless and\n-    redirecting to another host.\n-    \"\"\"\n-    if url.startswith('//'):\n-        url = '/%2F{}'.format(url[2:])\n-    return url\n+    port = url_info.port\n+    if port is not None and port != {'http': 80, 'https': 443}[scheme]:\n+        host = '%s:%d' % (host, port)\n+    return host in allowed_hosts\n\\ No newline at end of file\n", "proposed_model_patches": []}