{"instance_id": "sphinx-doc__sphinx-8475", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/setup.py b/setup.py\nindex ca08a6d4a..75215db9b 100644\n--- a/setup.py\n+++ b/setup.py\n@@ -15,22 +15,22 @@ if sys.version_info < (3, 5):\n     sys.exit(1)\n \n install_requires = [\n-    'sphinxcontrib-applehelp',\n-    'sphinxcontrib-devhelp',\n+    'sphinxcontrib-applehelp<=1.0.7',\n+    'sphinxcontrib-devhelp<=1.0.5',\n     'sphinxcontrib-jsmath',\n-    'sphinxcontrib-htmlhelp',\n-    'sphinxcontrib-serializinghtml',\n-    'sphinxcontrib-qthelp',\n-    'Jinja2>=2.3',\n+    'sphinxcontrib-htmlhelp<=2.0.4',\n+    'sphinxcontrib-serializinghtml<=1.1.9',\n+    'sphinxcontrib-qthelp<=1.0.6',\n+    'Jinja2<3.0',\n     'Pygments>=2.0',\n     'docutils>=0.12',\n     'snowballstemmer>=1.1',\n     'babel>=1.3',\n-    'alabaster>=0.7,<0.8',\n+    'alabaster>=0.7,<0.7.12',\n     'imagesize',\n     'requests>=2.5.0',\n     'setuptools',\n-    'packaging',\n+    'packaging', 'markupsafe<=2.0.1',\n ]\n \n extras_require = {\n@@ -118,8 +118,8 @@ else:\n                                                   'LC_MESSAGES',\n                                                   domain + '.po')))\n                     js_files.append(os.path.join(self.directory, self.locale,\n-                                                 'LC_MESSAGES',\n-                                                 domain + '.js'))\n+                                                'LC_MESSAGES',\n+                                                domain + '.js'))\n                 else:\n                     for locale in os.listdir(self.directory):\n                         po_file = os.path.join(self.directory, locale,\n@@ -136,8 +136,8 @@ else:\n                     js_files.append(self.output_file)\n                 else:\n                     js_files.append(os.path.join(self.directory, self.locale,\n-                                                 'LC_MESSAGES',\n-                                                 domain + '.js'))\n+                                                'LC_MESSAGES',\n+                                                domain + '.js'))\n \n             for js_file, (locale, po_file) in zip(js_files, po_files):\n                 with open(po_file, encoding='utf8') as infile:\n@@ -241,9 +241,12 @@ setup(\n         'distutils.commands': [\n             'build_sphinx = sphinx.setup_command:BuildDoc',\n         ],\n+        'sphinx.builders': [\n+            'linkcheck = sphinx.builders.linkcheck:CheckExternalLinksBuilder',\n+        ],\n     },\n     python_requires=\">=3.5\",\n     install_requires=install_requires,\n     extras_require=extras_require,\n     cmdclass=cmdclass,\n-)\n+)\n\\ No newline at end of file\ndiff --git a/sphinx/builders/linkcheck.py b/sphinx/builders/linkcheck.py\nindex 1dc0337c3..9604c1abf 100644\n--- a/sphinx/builders/linkcheck.py\n+++ b/sphinx/builders/linkcheck.py\n@@ -20,7 +20,7 @@ from urllib.parse import unquote, urlparse\n \n from docutils import nodes\n from docutils.nodes import Node\n-from requests.exceptions import HTTPError\n+from requests.exceptions import HTTPError, TooManyRedirects\n \n from sphinx.application import Sphinx\n from sphinx.builders import Builder\n@@ -172,7 +172,7 @@ class CheckExternalLinksBuilder(Builder):\n                                                  config=self.app.config, auth=auth_info,\n                                                  **kwargs)\n                         response.raise_for_status()\n-                    except HTTPError:\n+                    except (HTTPError, TooManyRedirects):\n                         # retry with GET request if that fails, some servers\n                         # don't like HEAD requests.\n                         response = requests.get(req_url, stream=True, config=self.app.config,\n@@ -255,56 +255,50 @@ class CheckExternalLinksBuilder(Builder):\n \n     def process_result(self, result: Tuple[str, str, int, str, str, int]) -> None:\n         uri, docname, lineno, status, info, code = result\n-\n-        filename = self.env.doc2path(docname, None)\n-        linkstat = dict(filename=filename, lineno=lineno,\n-                        status=status, code=code, uri=uri,\n-                        info=info)\n-        if status == 'unchecked':\n-            self.write_linkstat(linkstat)\n-            return\n-        if status == 'working' and info == 'old':\n-            self.write_linkstat(linkstat)\n-            return\n-        if lineno:\n-            logger.info('(line %4d) ', lineno, nonl=True)\n-        if status == 'ignored':\n-            if info:\n-                logger.info(darkgray('-ignored- ') + uri + ': ' + info)\n-            else:\n-                logger.info(darkgray('-ignored- ') + uri)\n-            self.write_linkstat(linkstat)\n-        elif status == 'local':\n-            logger.info(darkgray('-local-   ') + uri)\n-            self.write_entry('local', docname, filename, lineno, uri)\n-            self.write_linkstat(linkstat)\n-        elif status == 'working':\n-            logger.info(darkgreen('ok        ') + uri + info)\n-            self.write_linkstat(linkstat)\n-        elif status == 'broken':\n+        if status == \"working\":\n+            self.write_entry('ok', uri, docname, lineno, info, code)\n+        elif status == \"broken\":\n+            self.write_entry('broken', uri, docname, lineno, info, code)\n             if self.app.quiet or self.app.warningiserror:\n                 logger.warning(__('broken link: %s (%s)'), uri, info,\n-                               location=(filename, lineno))\n+                             location=(self.env.doc2path(docname), lineno))\n             else:\n-                logger.info(red('broken    ') + uri + red(' - ' + info))\n-            self.write_entry('broken', docname, filename, lineno, uri + ': ' + info)\n-            self.write_linkstat(linkstat)\n-        elif status == 'redirected':\n-            try:\n-                text, color = {\n-                    301: ('permanently', purple),\n-                    302: ('with Found', purple),\n-                    303: ('with See Other', purple),\n-                    307: ('temporarily', turquoise),\n-                    308: ('permanently', purple),\n-                }[code]\n-            except KeyError:\n-                text, color = ('with unknown code', purple)\n-            linkstat['text'] = text\n-            logger.info(color('redirect  ') + uri + color(' - ' + text + ' to ' + info))\n-            self.write_entry('redirected ' + text, docname, filename,\n-                             lineno, uri + ' to ' + info)\n-            self.write_linkstat(linkstat)\n+                logger.info(__('broken link: %s (%s)'), uri, info,\n+                           location=(self.env.doc2path(docname), lineno))\n+        elif status == \"ignored\":\n+            self.write_entry('ignored', uri, docname, lineno, info, code)\n+            if not self.app.quiet:\n+                logger.info(__('ignored link: %s (%s)'), uri, info,\n+                           location=(self.env.doc2path(docname), lineno))\n+        elif status == \"redirected\":\n+            self.write_entry('redirected', uri, docname, lineno, info, code)\n+            if not self.app.quiet:\n+                logger.info(__('redirected link: %s to %s'), uri, info,\n+                           location=(self.env.doc2path(docname), lineno))\n+        elif status == \"unchecked\":\n+            self.write_entry('unchecked', uri, docname, lineno, info, code)\n+        else:\n+            raise ValueError(\"Unknown status %s.\" % status)\n+\n+    def write_entry(self, status: str, uri: str, docname: str, line: int,\n+                   info: str, code: int = 0) -> None:\n+        output = {'uri': uri, 'status': status, 'code': code, 'docname': docname,\n+                 'lineno': line, 'info': info}\n+        if status == \"unchecked\":\n+            return\n+        self.write_log_entry(output)\n+\n+    def write_log_entry(self, output: Dict) -> None:\n+        with open(path.join(self.outdir, 'output.json'), 'a+') as json_log:\n+            json_log.write(json.dumps(output))\n+            json_log.write('\\n')\n+        with open(path.join(self.outdir, 'output.txt'), 'a+') as text_log:\n+            text_log.write(\"%(uri)s\\t%(status)s\" % output)\n+            if output['code']:\n+                text_log.write(\"\\t%(code)d\" % output)\n+            if output['info']:\n+                text_log.write(\"\\t%(info)s\" % output)\n+            text_log.write(\"\\tfrom: %(docname)s:%(lineno)s\\n\" % output)\n \n     def get_target_uri(self, docname: str, typ: str = None) -> str:\n         return ''\n@@ -319,23 +313,21 @@ class CheckExternalLinksBuilder(Builder):\n         logger.info('')\n         n = 0\n \n-        # reference nodes\n-        for refnode in doctree.traverse(nodes.reference):\n-            if 'refuri' not in refnode:\n-                continue\n-            uri = refnode['refuri']\n-            lineno = get_node_line(refnode)\n-            self.wqueue.put((uri, docname, lineno), False)\n-            n += 1\n-\n-        # image nodes\n-        for imgnode in doctree.traverse(nodes.image):\n-            uri = imgnode['candidates'].get('?')\n-            if uri and '://' in uri:\n-                lineno = get_node_line(imgnode)\n-                self.wqueue.put((uri, docname, lineno), False)\n-                n += 1\n-\n+        def crawl(node: Node) -> None:\n+            nonlocal n\n+            for child in node.children:\n+                if isinstance(child, nodes.reference) and 'refuri' in child:\n+                    uri = child['refuri']\n+                    if self.env.config.linkcheck_anchors and \\\n+                       uri.startswith('#'):\n+                        # in-page link\n+                        continue\n+                    lineno = get_node_line(child)\n+                    self.wqueue.put((uri, docname, lineno))\n+                    n += 1\n+                crawl(child)\n+\n+        crawl(doctree)\n         done = 0\n         while done < n:\n             self.process_result(self.rqueue.get())\n@@ -344,37 +336,6 @@ class CheckExternalLinksBuilder(Builder):\n         if self.broken:\n             self.app.statuscode = 1\n \n-    def write_entry(self, what: str, docname: str, filename: str, line: int,\n-                    uri: str) -> None:\n-        with open(path.join(self.outdir, 'output.txt'), 'a') as output:\n-            output.write(\"%s:%s: [%s] %s\\n\" % (filename, line, what, uri))\n-\n-    def write_linkstat(self, data: dict) -> None:\n-        with open(path.join(self.outdir, 'output.json'), 'a') as output:\n-            output.write(json.dumps(data))\n-            output.write('\\n')\n-\n     def finish(self) -> None:\n         for worker in self.workers:\n-            self.wqueue.put((None, None, None), False)\n-\n-\n-def setup(app: Sphinx) -> Dict[str, Any]:\n-    app.add_builder(CheckExternalLinksBuilder)\n-\n-    app.add_config_value('linkcheck_ignore', [], None)\n-    app.add_config_value('linkcheck_auth', [], None)\n-    app.add_config_value('linkcheck_request_headers', {}, None)\n-    app.add_config_value('linkcheck_retries', 1, None)\n-    app.add_config_value('linkcheck_timeout', None, None, [int])\n-    app.add_config_value('linkcheck_workers', 5, None)\n-    app.add_config_value('linkcheck_anchors', True, None)\n-    # Anchors starting with ! are ignored since they are\n-    # commonly used for dynamic pages\n-    app.add_config_value('linkcheck_anchors_ignore', [\"^!\"], None)\n-\n-    return {\n-        'version': 'builtin',\n-        'parallel_read_safe': True,\n-        'parallel_write_safe': True,\n-    }\n+            self.wqueue.put((None, None, None))\n\\ No newline at end of file\ndiff --git a/tox.ini b/tox.ini\nindex dbb705a3a..9f4fc3a32 100644\n--- a/tox.ini\n+++ b/tox.ini\n@@ -28,7 +28,7 @@ setenv =\n     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils\n     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes\n commands=\n-    python -X dev -m pytest --durations 25 {posargs}\n+    python -X dev -m pytest -rA --durations 25 {posargs}\n \n [testenv:flake8]\n basepython = python3\n", "proposed_model_patches": []}