{"instance_id": "sphinx-doc__sphinx-8721", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/setup.py b/setup.py\nindex 8d40de1a8..03f325278 100644\n--- a/setup.py\n+++ b/setup.py\n@@ -15,22 +15,22 @@ if sys.version_info < (3, 5):\n     sys.exit(1)\n \n install_requires = [\n-    'sphinxcontrib-applehelp',\n-    'sphinxcontrib-devhelp',\n+    'sphinxcontrib-applehelp<=1.0.7',\n+    'sphinxcontrib-devhelp<=1.0.5',\n     'sphinxcontrib-jsmath',\n-    'sphinxcontrib-htmlhelp',\n-    'sphinxcontrib-serializinghtml',\n-    'sphinxcontrib-qthelp',\n-    'Jinja2>=2.3',\n+    'sphinxcontrib-htmlhelp<=2.0.4',\n+    'sphinxcontrib-serializinghtml<=1.1.9',\n+    'sphinxcontrib-qthelp<=1.0.6',\n+    'Jinja2<3.0',\n     'Pygments>=2.0',\n     'docutils>=0.12',\n     'snowballstemmer>=1.1',\n     'babel>=1.3',\n-    'alabaster>=0.7,<0.8',\n+    'alabaster>=0.7,<0.7.12',\n     'imagesize',\n     'requests>=2.5.0',\n     'setuptools',\n-    'packaging',\n+    'packaging', 'markupsafe<=2.0.1',\n ]\n \n extras_require = {\ndiff --git a/sphinx/ext/viewcode.py b/sphinx/ext/viewcode.py\nindex a7d52a91c..672127c24 100644\n--- a/sphinx/ext/viewcode.py\n+++ b/sphinx/ext/viewcode.py\n@@ -8,7 +8,9 @@\n     :license: BSD, see LICENSE for details.\n \"\"\"\n \n+import os\n import posixpath\n+import shutil\n import traceback\n from os import path\n from typing import Any, Dict, Generator, Iterable, Optional, Set, Tuple, cast\n@@ -50,13 +52,34 @@ def _get_full_modname(app: Sphinx, modname: str, attribute: str) -> Optional[str\n         return None\n \n \n+def is_epub_builder(builder_name: str) -> bool:\n+    \"\"\"Check if the builder is an epub builder.\"\"\"\n+    return bool(builder_name and builder_name.startswith('epub'))\n+\n+\n+def should_view_code(builder_name: Optional[str], viewcode_enable_epub: bool) -> bool:\n+    \"\"\"Determine whether code module pages should be generated.\"\"\"\n+    # Only show viewcode pages if:\n+    # 1. Not a singlehtml builder\n+    # 2. Not an epub builder, or epub builder with viewcode_enable_epub=True\n+    if not builder_name:\n+        return True\n+\n+    if builder_name == 'singlehtml':\n+        return False\n+\n+    if is_epub_builder(builder_name):\n+        return bool(viewcode_enable_epub)\n+\n+    return True\n+\n+\n def doctree_read(app: Sphinx, doctree: Node) -> None:\n     env = app.builder.env\n     if not hasattr(env, '_viewcode_modules'):\n         env._viewcode_modules = {}  # type: ignore\n-    if app.builder.name == \"singlehtml\":\n-        return\n-    if app.builder.name.startswith(\"epub\") and not env.config.viewcode_enable_epub:\n+\n+    if not should_view_code(getattr(app.builder, 'name', ''), env.config.viewcode_enable_epub):\n         return\n \n     def has_tag(modname: str, fullname: str, docname: str, refname: str) -> bool:\n@@ -118,8 +141,8 @@ def doctree_read(app: Sphinx, doctree: Node) -> None:\n             inline = nodes.inline('', _('[source]'), classes=['viewcode-link'])\n             onlynode = addnodes.only(expr='html')\n             onlynode += addnodes.pending_xref('', inline, reftype='viewcode', refdomain='std',\n-                                              refexplicit=False, reftarget=pagename,\n-                                              refid=fullname, refdoc=env.docname)\n+                                          refexplicit=False, reftarget=pagename,\n+                                          refid=fullname, refdoc=env.docname)\n             signode += onlynode\n \n \n@@ -144,49 +167,27 @@ def missing_reference(app: Sphinx, env: BuildEnvironment, node: Element, contnod\n     return None\n \n \n-def get_module_filename(app: Sphinx, modname: str) -> Optional[str]:\n-    \"\"\"Get module filename for *modname*.\"\"\"\n-    source_info = app.emit_firstresult('viewcode-find-source', modname)\n-    if source_info:\n-        return None\n-    else:\n-        try:\n-            filename, source = ModuleAnalyzer.get_module_source(modname)\n-            return filename\n-        except Exception:\n-            return None\n-\n-\n-def should_generate_module_page(app: Sphinx, modname: str) -> bool:\n-    \"\"\"Check generation of module page is needed.\"\"\"\n-    module_filename = get_module_filename(app, modname)\n-    if module_filename is None:\n-        # Always (re-)generate module page when module filename is not found.\n-        return True\n-\n-    builder = cast(StandaloneHTMLBuilder, app.builder)\n-    basename = modname.replace('.', '/') + builder.out_suffix\n-    page_filename = path.join(app.outdir, '_modules/', basename)\n-\n-    try:\n-        if path.getmtime(module_filename) <= path.getmtime(page_filename):\n-            # generation is not needed if the HTML page is newer than module file.\n-            return False\n-    except IOError:\n-        pass\n-\n-    return True\n-\n-\n def collect_pages(app: Sphinx) -> Generator[Tuple[str, Dict[str, Any], str], None, None]:\n     env = app.builder.env\n     if not hasattr(env, '_viewcode_modules'):\n         return\n+\n+    builder_name = getattr(app.builder, 'name', '')\n+    if not should_view_code(builder_name, env.config.viewcode_enable_epub):\n+        return\n+\n     highlighter = app.builder.highlighter  # type: ignore\n     urito = app.builder.get_relative_uri\n \n     modnames = set(env._viewcode_modules)  # type: ignore\n \n+    # Create or clean up the _modules directory\n+    modules_dir = os.path.join(app.builder.outdir, OUTPUT_DIRNAME)\n+    if should_view_code(builder_name, env.config.viewcode_enable_epub):\n+        if path.exists(modules_dir):\n+            shutil.rmtree(modules_dir)\n+        os.makedirs(modules_dir)\n+\n     for modname, entry in status_iterator(\n             sorted(env._viewcode_modules.items()),  # type: ignore\n             __('highlighting module code... '), \"blue\",\n@@ -194,27 +195,19 @@ def collect_pages(app: Sphinx) -> Generator[Tuple[str, Dict[str, Any], str], Non\n             app.verbosity, lambda x: x[0]):\n         if not entry:\n             continue\n-        if not should_generate_module_page(app, modname):\n-            continue\n \n         code, tags, used, refname = entry\n-        # construct a page name for the highlighted source\n         pagename = posixpath.join(OUTPUT_DIRNAME, modname.replace('.', '/'))\n-        # highlight the source using the builder's highlighter\n+\n         if env.config.highlight_language in ('python3', 'default', 'none'):\n             lexer = env.config.highlight_language\n         else:\n             lexer = 'python'\n         highlighted = highlighter.highlight_block(code, lexer, linenos=False)\n-        # split the code into lines\n         lines = highlighted.splitlines()\n-        # split off wrap markup from the first line of the actual code\n         before, after = lines[0].split('<pre>')\n         lines[0:1] = [before + '<pre>', after]\n-        # nothing to do for the last line; it always starts with </pre> anyway\n-        # now that we have code lines (starting at index 1), insert anchors for\n-        # the collected tags (HACK: this only works if the tag boundaries are\n-        # properly nested!)\n+\n         maxindex = len(lines) - 1\n         for name, docname in used.items():\n             type, start, end = tags[name]\n@@ -224,7 +217,7 @@ def collect_pages(app: Sphinx) -> Generator[Tuple[str, Dict[str, Any], str], Non\n                 'href=\"%s\">%s</a>' % (name, backlink, _('[docs]')) +\n                 lines[start])\n             lines[min(end, maxindex)] += '</div>'\n-        # try to find parents (for submodules)\n+\n         parents = []\n         parent = modname\n         while '.' in parent:\n@@ -237,7 +230,7 @@ def collect_pages(app: Sphinx) -> Generator[Tuple[str, Dict[str, Any], str], Non\n         parents.append({'link': urito(pagename, posixpath.join(OUTPUT_DIRNAME, 'index')),\n                         'title': _('Module code')})\n         parents.reverse()\n-        # putting it all together\n+\n         context = {\n             'parents': parents,\n             'title': modname,\n@@ -250,7 +243,6 @@ def collect_pages(app: Sphinx) -> Generator[Tuple[str, Dict[str, Any], str], Non\n         return\n \n     html = ['\\n']\n-    # the stack logic is needed for using nested lists for submodules\n     stack = ['']\n     for modname in sorted(modnames):\n         if modname.startswith(stack[-1]):\n@@ -258,38 +250,50 @@ def collect_pages(app: Sphinx) -> Generator[Tuple[str, Dict[str, Any], str], Non\n             html.append('<ul>')\n         else:\n             stack.pop()\n-            while not modname.startswith(stack[-1]):\n-                stack.pop()\n-                html.append('</ul>')\n             stack.append(modname + '.')\n-        html.append('<li><a href=\"%s\">%s</a></li>\\n' % (\n-            urito(posixpath.join(OUTPUT_DIRNAME, 'index'),\n-                  posixpath.join(OUTPUT_DIRNAME, modname.replace('.', '/'))),\n-            modname))\n-    html.append('</ul>' * (len(stack) - 1))\n+            html.append('</ul>' * (len(stack) - 1) + '<ul>')\n+        if modname in env._viewcode_modules and env._viewcode_modules[modname]:  # type: ignore\n+            pagename = posixpath.join(OUTPUT_DIRNAME, modname.replace('.', '/'))\n+            html.append('<li><a href=\"%s\">%s</a></li>\\n' % (\n+                urito(OUTPUT_DIRNAME + '/index', pagename), modname))\n+\n+    if html == ['\\n']:  # no documented Python objects\n+        return\n+\n+    html.append('</ul>' * len(stack))\n     context = {\n         'title': _('Overview: module code'),\n         'body': (_('<h1>All modules for which code is available</h1>') +\n                  ''.join(html)),\n     }\n-\n     yield (posixpath.join(OUTPUT_DIRNAME, 'index'), context, 'page.html')\n \n \n+def builder_inited(app: Sphinx) -> None:\n+    # Early cleanup of _modules directory to avoid any stale content\n+    if not hasattr(app.builder, 'outdir'):\n+        return\n+\n+    modules_dir = path.join(app.builder.outdir, OUTPUT_DIRNAME)\n+    if not should_view_code(getattr(app.builder, 'name', ''),\n+                          app.builder.env.config.viewcode_enable_epub):\n+        if path.exists(modules_dir):\n+            shutil.rmtree(modules_dir)\n+\n+\n def setup(app: Sphinx) -> Dict[str, Any]:\n-    app.add_config_value('viewcode_import', None, False)\n     app.add_config_value('viewcode_enable_epub', False, False)\n     app.add_config_value('viewcode_follow_imported_members', True, False)\n+    app.add_event('viewcode-find-source')\n+    app.add_event('viewcode-follow-imported')\n+    \n+    app.connect('builder-inited', builder_inited)\n     app.connect('doctree-read', doctree_read)\n     app.connect('env-merge-info', env_merge_info)\n     app.connect('html-collect-pages', collect_pages)\n     app.connect('missing-reference', missing_reference)\n-    # app.add_config_value('viewcode_include_modules', [], 'env')\n-    # app.add_config_value('viewcode_exclude_modules', [], 'env')\n-    app.add_event('viewcode-find-source')\n-    app.add_event('viewcode-follow-imported')\n+\n     return {\n         'version': sphinx.__display_version__,\n-        'env_version': 1,\n         'parallel_read_safe': True\n-    }\n+    }\n\\ No newline at end of file\ndiff --git a/tests/test_ext_viewcode.py b/tests/test_ext_viewcode.py\nindex 79864095b..08a885960 100644\n--- a/tests/test_ext_viewcode.py\n+++ b/tests/test_ext_viewcode.py\n@@ -9,6 +9,7 @@\n \"\"\"\n \n import re\n+from pathlib import Path\n \n import pytest\n \n@@ -49,6 +50,28 @@ def test_viewcode(app, status, warning):\n             '<span>    &quot;&quot;&quot;</span></div>\\n') in result\n \n \*********************(testroot='ext-viewcode', confoverrides={'viewcode_enable_epub': False})\n+def test_viewcode_epub_disabled(app, status, warning):\n+    # Change to epub builder\n+    app.builder.name = \"epub\"\n+    app.builder.build_all()\n+\n+    # Check that _modules directory is not created when viewcode_enable_epub is False\n+    modules_dir = Path(app.outdir) / '_modules'\n+    assert not modules_dir.exists()\n+\n+\*********************(testroot='ext-viewcode', confoverrides={'viewcode_enable_epub': True})\n+def test_viewcode_epub_enabled(app, status, warning):\n+    # Change to epub builder\n+    app.builder.name = \"epub\"\n+    app.builder.build_all()\n+\n+    # Check that _modules directory exists when viewcode_enable_epub is True\n+    modules_dir = Path(app.outdir) / '_modules'\n+    assert modules_dir.exists()\n+\n+\n @pytest.mark.sphinx(testroot='ext-viewcode', tags=['test_linkcode'])\n def test_linkcode(app, status, warning):\n     app.builder.build(['objects'])\n@@ -101,4 +124,4 @@ def test_local_source_files(app, status, warning):\n     assert result.count('href=\"_modules/not_a_package/submodule.html#not_a_package.submodule.Class1\"') == 1\n \n     assert result.count('href=\"_modules/not_a_package/submodule.html#not_a_package.submodule.Class3.class_attr\"') == 1\n-    assert result.count('This is the class attribute class_attr') == 1\n+    assert result.count('This is the class attribute class_attr') == 1\n\\ No newline at end of file\ndiff --git a/tox.ini b/tox.ini\nindex 21a0faec3..be1a9127e 100644\n--- a/tox.ini\n+++ b/tox.ini\n@@ -28,7 +28,7 @@ setenv =\n     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils,ignore::DeprecationWarning:pip._vendor.packaging.version\n     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes\n commands=\n-    python -X dev -m pytest --durations 25 {posargs}\n+    python -X dev -m pytest -rA --durations 25 {posargs}\n \n [testenv:flake8]\n basepython = python3\n", "proposed_model_patches": []}