{"instance_id": "sphinx-doc__sphinx-7757", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/setup.py b/setup.py\nindex 5e822fe9b..77b63df38 100644\n--- a/setup.py\n+++ b/setup.py\n@@ -15,22 +15,22 @@ if sys.version_info < (3, 5):\n     sys.exit(1)\n \n install_requires = [\n-    'sphinxcontrib-applehelp',\n-    'sphinxcontrib-devhelp',\n+    'sphinxcontrib-applehelp<=1.0.7',\n+    'sphinxcontrib-devhelp<=1.0.5',\n     'sphinxcontrib-jsmath',\n-    'sphinxcontrib-htmlhelp',\n-    'sphinxcontrib-serializinghtml',\n-    'sphinxcontrib-qthelp',\n-    'Jinja2>=2.3',\n+    'sphinxcontrib-htmlhelp<=2.0.4',\n+    'sphinxcontrib-serializinghtml<=1.1.9',\n+    'sphinxcontrib-qthelp<=1.0.6',\n+    'Jinja2<3.0',\n     'Pygments>=2.0',\n     'docutils>=0.12',\n     'snowballstemmer>=1.1',\n     'babel>=1.3',\n-    'alabaster>=0.7,<0.8',\n+    'alabaster>=0.7,<0.7.12',\n     'imagesize',\n     'requests>=2.5.0',\n     'setuptools',\n-    'packaging',\n+    'packaging', 'markupsafe<=2.0.1',\n ]\n \n extras_require = {\ndiff --git a/tests/test_domain_py.py b/tests/test_domain_py.py\nindex 98b295b99..0bb54be48 100644\n--- a/tests/test_domain_py.py\n+++ b/tests/test_domain_py.py\n@@ -58,13 +58,26 @@ def test_function_signatures():\n     assert rv == '(a=[], [b=None])'\n \n \n+def test_function_pos_only_signatures():\n+    # Test positional-only parameters with default values\n+    rv = parse('func(a, b=0, /, c=1)')\n+    assert rv == '(a, b=0, /, c=1)'\n+\n+    rv = parse('func(a=1, /, c=3)')\n+    assert rv == '(a=1, /, c=3)'\n+\n+    # Test with string default values to ensure proper quotes\n+    rv = parse('func(a=\"x\", /, b=\"y\")')\n+    assert rv == '(a=\"x\", /, b=\"y\")'\n+\n+\n @pytest.mark.sphinx('dummy', testroot='domain-py')\n def test_domain_py_xrefs(app, status, warning):\n     \"\"\"Domain objects have correct prefixes when looking up xrefs\"\"\"\n     app.builder.build_all()\n \n     def assert_refnode(node, module_name, class_name, target, reftype=None,\n-                       domain='py'):\n+                      domain='py'):\n         attributes = {\n             'refdomain': domain,\n             'reftarget': target,\n@@ -88,7 +101,7 @@ def test_domain_py_xrefs(app, status, warning):\n     assert_refnode(refnodes[6], None, 'NestedParentA', 'NestedChildA', 'class')\n     assert_refnode(refnodes[7], None, 'NestedParentA.NestedChildA', 'subchild_2', 'meth')\n     assert_refnode(refnodes[8], None, 'NestedParentA.NestedChildA',\n-                   'NestedParentA.child_1', 'meth')\n+                  'NestedParentA.child_1', 'meth')\n     assert_refnode(refnodes[9], None, 'NestedParentA', 'NestedChildA.subchild_1', 'meth')\n     assert_refnode(refnodes[10], None, 'NestedParentB', 'child_1', 'meth')\n     assert_refnode(refnodes[11], None, 'NestedParentB', 'NestedParentB', 'class')\n@@ -98,23 +111,23 @@ def test_domain_py_xrefs(app, status, warning):\n     doctree = app.env.get_doctree('module')\n     refnodes = list(doctree.traverse(pending_xref))\n     assert_refnode(refnodes[0], 'module_a.submodule', None,\n-                   'ModTopLevel', 'class')\n+                  'ModTopLevel', 'class')\n     assert_refnode(refnodes[1], 'module_a.submodule', 'ModTopLevel',\n-                   'mod_child_1', 'meth')\n+                  'mod_child_1', 'meth')\n     assert_refnode(refnodes[2], 'module_a.submodule', 'ModTopLevel',\n-                   'ModTopLevel.mod_child_1', 'meth')\n+                  'ModTopLevel.mod_child_1', 'meth')\n     assert_refnode(refnodes[3], 'module_a.submodule', 'ModTopLevel',\n-                   'mod_child_2', 'meth')\n+                  'mod_child_2', 'meth')\n     assert_refnode(refnodes[4], 'module_a.submodule', 'ModTopLevel',\n-                   'module_a.submodule.ModTopLevel.mod_child_1', 'meth')\n+                  'module_a.submodule.ModTopLevel.mod_child_1', 'meth')\n     assert_refnode(refnodes[5], 'module_a.submodule', 'ModTopLevel',\n-                   'prop', 'attr')\n+                  'prop', 'attr')\n     assert_refnode(refnodes[6], 'module_a.submodule', 'ModTopLevel',\n-                   'prop', 'meth')\n+                  'prop', 'meth')\n     assert_refnode(refnodes[7], 'module_b.submodule', None,\n-                   'ModTopLevel', 'class')\n+                  'ModTopLevel', 'class')\n     assert_refnode(refnodes[8], 'module_b.submodule', 'ModTopLevel',\n-                   'ModNoModule', 'class')\n+                  'ModNoModule', 'class')\n     assert_refnode(refnodes[9], False, False, 'int', 'class')\n     assert_refnode(refnodes[10], False, False, 'tuple', 'class')\n     assert_refnode(refnodes[11], False, False, 'str', 'class')\n@@ -230,569 +243,7 @@ def test_get_full_qualified_name():\n     node = nodes.reference(reftarget='func', **kwargs)\n     assert domain.get_full_qualified_name(node) == 'Class.func'\n \n-    # with both py:module and py:class context\n+    # with both py:module and py:class\n     kwargs = {'py:module': 'module1', 'py:class': 'Class'}\n     node = nodes.reference(reftarget='func', **kwargs)\n-    assert domain.get_full_qualified_name(node) == 'module1.Class.func'\n-\n-\n-def test_parse_annotation():\n-    doctree = _parse_annotation(\"int\")\n-    assert_node(doctree, ([pending_xref, \"int\"],))\n-    assert_node(doctree[0], pending_xref, refdomain=\"py\", reftype=\"class\", reftarget=\"int\")\n-\n-    doctree = _parse_annotation(\"List[int]\")\n-    assert_node(doctree, ([pending_xref, \"List\"],\n-                          [desc_sig_punctuation, \"[\"],\n-                          [pending_xref, \"int\"],\n-                          [desc_sig_punctuation, \"]\"]))\n-\n-    doctree = _parse_annotation(\"Tuple[int, int]\")\n-    assert_node(doctree, ([pending_xref, \"Tuple\"],\n-                          [desc_sig_punctuation, \"[\"],\n-                          [pending_xref, \"int\"],\n-                          [desc_sig_punctuation, \", \"],\n-                          [pending_xref, \"int\"],\n-                          [desc_sig_punctuation, \"]\"]))\n-\n-    doctree = _parse_annotation(\"Tuple[()]\")\n-    assert_node(doctree, ([pending_xref, \"Tuple\"],\n-                          [desc_sig_punctuation, \"[\"],\n-                          [desc_sig_punctuation, \"(\"],\n-                          [desc_sig_punctuation, \")\"],\n-                          [desc_sig_punctuation, \"]\"]))\n-\n-    doctree = _parse_annotation(\"Callable[[int, int], int]\")\n-    assert_node(doctree, ([pending_xref, \"Callable\"],\n-                          [desc_sig_punctuation, \"[\"],\n-                          [desc_sig_punctuation, \"[\"],\n-                          [pending_xref, \"int\"],\n-                          [desc_sig_punctuation, \", \"],\n-                          [pending_xref, \"int\"],\n-                          [desc_sig_punctuation, \"]\"],\n-                          [desc_sig_punctuation, \", \"],\n-                          [pending_xref, \"int\"],\n-                          [desc_sig_punctuation, \"]\"]))\n-\n-    # None type makes an object-reference (not a class reference)\n-    doctree = _parse_annotation(\"None\")\n-    assert_node(doctree, ([pending_xref, \"None\"],))\n-    assert_node(doctree[0], pending_xref, refdomain=\"py\", reftype=\"obj\", reftarget=\"None\")\n-\n-\n-\n-def test_pyfunction_signature(app):\n-    text = \".. py:function:: hello(name: str) -> str\"\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_name, \"hello\"],\n-                                                    desc_parameterlist,\n-                                                    [desc_returns, pending_xref, \"str\"])],\n-                                  desc_content)]))\n-    assert_node(doctree[1], addnodes.desc, desctype=\"function\",\n-                domain=\"py\", objtype=\"function\", noindex=False)\n-    assert_node(doctree[1][0][1],\n-                [desc_parameterlist, desc_parameter, ([desc_sig_name, \"name\"],\n-                                                      [desc_sig_punctuation, \":\"],\n-                                                      \" \",\n-                                                      [nodes.inline, pending_xref, \"str\"])])\n-\n-\n-def test_pyfunction_signature_full(app):\n-    text = (\".. py:function:: hello(a: str, b = 1, *args: str, \"\n-            \"c: bool = True, **kwargs: str) -> str\")\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_name, \"hello\"],\n-                                                    desc_parameterlist,\n-                                                    [desc_returns, pending_xref, \"str\"])],\n-                                  desc_content)]))\n-    assert_node(doctree[1], addnodes.desc, desctype=\"function\",\n-                domain=\"py\", objtype=\"function\", noindex=False)\n-    assert_node(doctree[1][0][1],\n-                [desc_parameterlist, ([desc_parameter, ([desc_sig_name, \"a\"],\n-                                                        [desc_sig_punctuation, \":\"],\n-                                                        \" \",\n-                                                        [desc_sig_name, pending_xref, \"str\"])],\n-                                      [desc_parameter, ([desc_sig_name, \"b\"],\n-                                                        [desc_sig_operator, \"=\"],\n-                                                        [nodes.inline, \"1\"])],\n-                                      [desc_parameter, ([desc_sig_operator, \"*\"],\n-                                                        [desc_sig_name, \"args\"],\n-                                                        [desc_sig_punctuation, \":\"],\n-                                                        \" \",\n-                                                        [desc_sig_name, pending_xref, \"str\"])],\n-                                      [desc_parameter, ([desc_sig_name, \"c\"],\n-                                                        [desc_sig_punctuation, \":\"],\n-                                                        \" \",\n-                                                        [desc_sig_name, pending_xref, \"bool\"],\n-                                                        \" \",\n-                                                        [desc_sig_operator, \"=\"],\n-                                                        \" \",\n-                                                        [nodes.inline, \"True\"])],\n-                                      [desc_parameter, ([desc_sig_operator, \"**\"],\n-                                                        [desc_sig_name, \"kwargs\"],\n-                                                        [desc_sig_punctuation, \":\"],\n-                                                        \" \",\n-                                                        [desc_sig_name, pending_xref, \"str\"])])])\n-\n-\*********************(sys.version_info < (3, 8), reason='python 3.8+ is required.')\n-def test_pyfunction_signature_full_py38(app):\n-    # case: separator at head\n-    text = \".. py:function:: hello(*, a)\"\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree[1][0][1],\n-                [desc_parameterlist, ([desc_parameter, nodes.inline, \"*\"],\n-                                      [desc_parameter, desc_sig_name, \"a\"])])\n-\n-    # case: separator in the middle\n-    text = \".. py:function:: hello(a, /, b, *, c)\"\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree[1][0][1],\n-                [desc_parameterlist, ([desc_parameter, desc_sig_name, \"a\"],\n-                                      [desc_parameter, desc_sig_operator, \"/\"],\n-                                      [desc_parameter, desc_sig_name, \"b\"],\n-                                      [desc_parameter, desc_sig_operator, \"*\"],\n-                                      [desc_parameter, desc_sig_name, \"c\"])])\n-\n-    # case: separator in the middle (2)\n-    text = \".. py:function:: hello(a, /, *, b)\"\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree[1][0][1],\n-                [desc_parameterlist, ([desc_parameter, desc_sig_name, \"a\"],\n-                                      [desc_parameter, desc_sig_operator, \"/\"],\n-                                      [desc_parameter, desc_sig_operator, \"*\"],\n-                                      [desc_parameter, desc_sig_name, \"b\"])])\n-\n-    # case: separator at tail\n-    text = \".. py:function:: hello(a, /)\"\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree[1][0][1],\n-                [desc_parameterlist, ([desc_parameter, desc_sig_name, \"a\"],\n-                                      [desc_parameter, desc_sig_operator, \"/\"])])\n-\n-\n-def test_optional_pyfunction_signature(app):\n-    text = \".. py:function:: compile(source [, filename [, symbol]]) -> ast object\"\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_name, \"compile\"],\n-                                                    desc_parameterlist,\n-                                                    [desc_returns, pending_xref, \"ast object\"])],\n-                                  desc_content)]))\n-    assert_node(doctree[1], addnodes.desc, desctype=\"function\",\n-                domain=\"py\", objtype=\"function\", noindex=False)\n-    assert_node(doctree[1][0][1],\n-                ([desc_parameter, \"source\"],\n-                 [desc_optional, ([desc_parameter, \"filename\"],\n-                                  [desc_optional, desc_parameter, \"symbol\"])]))\n-\n-\n-def test_pyexception_signature(app):\n-    text = \".. py:exception:: exceptions.IOError\"\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_annotation, \"exception \"],\n-                                                    [desc_addname, \"exceptions.\"],\n-                                                    [desc_name, \"IOError\"])],\n-                                  desc_content)]))\n-    assert_node(doctree[1], desc, desctype=\"exception\",\n-                domain=\"py\", objtype=\"exception\", noindex=False)\n-\n-\n-def test_exceptions_module_is_ignored(app):\n-    text = (\".. py:exception:: IOError\\n\"\n-            \"   :module: exceptions\\n\")\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_annotation, \"exception \"],\n-                                                    [desc_name, \"IOError\"])],\n-                                  desc_content)]))\n-    assert_node(doctree[1], desc, desctype=\"exception\",\n-                domain=\"py\", objtype=\"exception\", noindex=False)\n-\n-\n-def test_pydata_signature(app):\n-    text = (\".. py:data:: version\\n\"\n-            \"   :type: int\\n\"\n-            \"   :value: 1\\n\")\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_name, \"version\"],\n-                                                    [desc_annotation, (\": \",\n-                                                                       [pending_xref, \"int\"])],\n-                                                    [desc_annotation, \" = 1\"])],\n-                                  desc_content)]))\n-    assert_node(doctree[1], addnodes.desc, desctype=\"data\",\n-                domain=\"py\", objtype=\"data\", noindex=False)\n-\n-\n-def test_pydata_signature_old(app):\n-    text = (\".. py:data:: version\\n\"\n-            \"   :annotation: = 1\\n\")\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_name, \"version\"],\n-                                                    [desc_annotation, \" = 1\"])],\n-                                  desc_content)]))\n-    assert_node(doctree[1], addnodes.desc, desctype=\"data\",\n-                domain=\"py\", objtype=\"data\", noindex=False)\n-\n-\n-def test_pyobject_prefix(app):\n-    text = (\".. py:class:: Foo\\n\"\n-            \"\\n\"\n-            \"   .. py:method:: Foo.say\\n\"\n-            \"   .. py:method:: FooBar.say\")\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_annotation, \"class \"],\n-                                                    [desc_name, \"Foo\"])],\n-                                  [desc_content, (addnodes.index,\n-                                                  desc,\n-                                                  addnodes.index,\n-                                                  desc)])]))\n-    assert doctree[1][1][1].astext().strip() == 'say()'           # prefix is stripped\n-    assert doctree[1][1][3].astext().strip() == 'FooBar.say()'    # not stripped\n-\n-\n-def test_pydata(app):\n-    text = \".. py:data:: var\\n\"\n-    domain = app.env.get_domain('py')\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, desc_name, \"var\"],\n-                                  [desc_content, ()])]))\n-    assert 'var' in domain.objects\n-    assert domain.objects['var'] == ('index', 'var', 'data')\n-\n-\n-def test_pyfunction(app):\n-    text = (\".. py:function:: func1\\n\"\n-            \".. py:module:: example\\n\"\n-            \".. py:function:: func2\\n\"\n-            \"   :async:\\n\")\n-    domain = app.env.get_domain('py')\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_name, \"func1\"],\n-                                                    [desc_parameterlist, ()])],\n-                                  [desc_content, ()])],\n-                          nodes.target,\n-                          addnodes.index,\n-                          addnodes.index,\n-                          [desc, ([desc_signature, ([desc_annotation, \"async \"],\n-                                                    [desc_addname, \"example.\"],\n-                                                    [desc_name, \"func2\"],\n-                                                    [desc_parameterlist, ()])],\n-                                  [desc_content, ()])]))\n-    assert_node(doctree[0], addnodes.index,\n-                entries=[('pair', 'built-in function; func1()', 'func1', '', None)])\n-    assert_node(doctree[3], addnodes.index,\n-                entries=[('pair', 'module; example', 'module-example', '', None)])\n-    assert_node(doctree[4], addnodes.index,\n-                entries=[('single', 'func2() (in module example)', 'example.func2', '', None)])\n-\n-    assert 'func1' in domain.objects\n-    assert domain.objects['func1'] == ('index', 'func1', 'function')\n-    assert 'example.func2' in domain.objects\n-    assert domain.objects['example.func2'] == ('index', 'example.func2', 'function')\n-\n-\n-def test_pyclass_options(app):\n-    text = (\".. py:class:: Class1\\n\"\n-            \".. py:class:: Class2\\n\"\n-            \"   :final:\\n\")\n-    domain = app.env.get_domain('py')\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_annotation, \"class \"],\n-                                                    [desc_name, \"Class1\"])],\n-                                  [desc_content, ()])],\n-                          addnodes.index,\n-                          [desc, ([desc_signature, ([desc_annotation, \"final class \"],\n-                                                    [desc_name, \"Class2\"])],\n-                                  [desc_content, ()])]))\n-\n-    # class\n-    assert_node(doctree[0], addnodes.index,\n-                entries=[('single', 'Class1 (built-in class)', 'Class1', '', None)])\n-    assert 'Class1' in domain.objects\n-    assert domain.objects['Class1'] == ('index', 'Class1', 'class')\n-\n-    # :final:\n-    assert_node(doctree[2], addnodes.index,\n-                entries=[('single', 'Class2 (built-in class)', 'Class2', '', None)])\n-    assert 'Class2' in domain.objects\n-    assert domain.objects['Class2'] == ('index', 'Class2', 'class')\n-\n-\n-def test_pymethod_options(app):\n-    text = (\".. py:class:: Class\\n\"\n-            \"\\n\"\n-            \"   .. py:method:: meth1\\n\"\n-            \"   .. py:method:: meth2\\n\"\n-            \"      :classmethod:\\n\"\n-            \"   .. py:method:: meth3\\n\"\n-            \"      :staticmethod:\\n\"\n-            \"   .. py:method:: meth4\\n\"\n-            \"      :async:\\n\"\n-            \"   .. py:method:: meth5\\n\"\n-            \"      :property:\\n\"\n-            \"   .. py:method:: meth6\\n\"\n-            \"      :abstractmethod:\\n\"\n-            \"   .. py:method:: meth7\\n\"\n-            \"      :final:\\n\")\n-    domain = app.env.get_domain('py')\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_annotation, \"class \"],\n-                                                    [desc_name, \"Class\"])],\n-                                  [desc_content, (addnodes.index,\n-                                                  desc,\n-                                                  addnodes.index,\n-                                                  desc,\n-                                                  addnodes.index,\n-                                                  desc,\n-                                                  addnodes.index,\n-                                                  desc,\n-                                                  addnodes.index,\n-                                                  desc,\n-                                                  addnodes.index,\n-                                                  desc,\n-                                                  addnodes.index,\n-                                                  desc)])]))\n-\n-    # method\n-    assert_node(doctree[1][1][0], addnodes.index,\n-                entries=[('single', 'meth1() (Class method)', 'Class.meth1', '', None)])\n-    assert_node(doctree[1][1][1], ([desc_signature, ([desc_name, \"meth1\"],\n-                                                     [desc_parameterlist, ()])],\n-                                   [desc_content, ()]))\n-    assert 'Class.meth1' in domain.objects\n-    assert domain.objects['Class.meth1'] == ('index', 'Class.meth1', 'method')\n-\n-    # :classmethod:\n-    assert_node(doctree[1][1][2], addnodes.index,\n-                entries=[('single', 'meth2() (Class class method)', 'Class.meth2', '', None)])\n-    assert_node(doctree[1][1][3], ([desc_signature, ([desc_annotation, \"classmethod \"],\n-                                                     [desc_name, \"meth2\"],\n-                                                     [desc_parameterlist, ()])],\n-                                   [desc_content, ()]))\n-    assert 'Class.meth2' in domain.objects\n-    assert domain.objects['Class.meth2'] == ('index', 'Class.meth2', 'method')\n-\n-    # :staticmethod:\n-    assert_node(doctree[1][1][4], addnodes.index,\n-                entries=[('single', 'meth3() (Class static method)', 'Class.meth3', '', None)])\n-    assert_node(doctree[1][1][5], ([desc_signature, ([desc_annotation, \"static \"],\n-                                                     [desc_name, \"meth3\"],\n-                                                     [desc_parameterlist, ()])],\n-                                   [desc_content, ()]))\n-    assert 'Class.meth3' in domain.objects\n-    assert domain.objects['Class.meth3'] == ('index', 'Class.meth3', 'method')\n-\n-    # :async:\n-    assert_node(doctree[1][1][6], addnodes.index,\n-                entries=[('single', 'meth4() (Class method)', 'Class.meth4', '', None)])\n-    assert_node(doctree[1][1][7], ([desc_signature, ([desc_annotation, \"async \"],\n-                                                     [desc_name, \"meth4\"],\n-                                                     [desc_parameterlist, ()])],\n-                                   [desc_content, ()]))\n-    assert 'Class.meth4' in domain.objects\n-    assert domain.objects['Class.meth4'] == ('index', 'Class.meth4', 'method')\n-\n-    # :property:\n-    assert_node(doctree[1][1][8], addnodes.index,\n-                entries=[('single', 'meth5() (Class property)', 'Class.meth5', '', None)])\n-    assert_node(doctree[1][1][9], ([desc_signature, ([desc_annotation, \"property \"],\n-                                                     [desc_name, \"meth5\"])],\n-                                   [desc_content, ()]))\n-    assert 'Class.meth5' in domain.objects\n-    assert domain.objects['Class.meth5'] == ('index', 'Class.meth5', 'method')\n-\n-    # :abstractmethod:\n-    assert_node(doctree[1][1][10], addnodes.index,\n-                entries=[('single', 'meth6() (Class method)', 'Class.meth6', '', None)])\n-    assert_node(doctree[1][1][11], ([desc_signature, ([desc_annotation, \"abstract \"],\n-                                                      [desc_name, \"meth6\"],\n-                                                      [desc_parameterlist, ()])],\n-                                    [desc_content, ()]))\n-    assert 'Class.meth6' in domain.objects\n-    assert domain.objects['Class.meth6'] == ('index', 'Class.meth6', 'method')\n-\n-    # :final:\n-    assert_node(doctree[1][1][12], addnodes.index,\n-                entries=[('single', 'meth7() (Class method)', 'Class.meth7', '', None)])\n-    assert_node(doctree[1][1][13], ([desc_signature, ([desc_annotation, \"final \"],\n-                                                      [desc_name, \"meth7\"],\n-                                                      [desc_parameterlist, ()])],\n-                                    [desc_content, ()]))\n-    assert 'Class.meth7' in domain.objects\n-    assert domain.objects['Class.meth7'] == ('index', 'Class.meth7', 'method')\n-\n-\n-def test_pyclassmethod(app):\n-    text = (\".. py:class:: Class\\n\"\n-            \"\\n\"\n-            \"   .. py:classmethod:: meth\\n\")\n-    domain = app.env.get_domain('py')\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_annotation, \"class \"],\n-                                                    [desc_name, \"Class\"])],\n-                                  [desc_content, (addnodes.index,\n-                                                  desc)])]))\n-    assert_node(doctree[1][1][0], addnodes.index,\n-                entries=[('single', 'meth() (Class class method)', 'Class.meth', '', None)])\n-    assert_node(doctree[1][1][1], ([desc_signature, ([desc_annotation, \"classmethod \"],\n-                                                     [desc_name, \"meth\"],\n-                                                     [desc_parameterlist, ()])],\n-                                   [desc_content, ()]))\n-    assert 'Class.meth' in domain.objects\n-    assert domain.objects['Class.meth'] == ('index', 'Class.meth', 'method')\n-\n-\n-def test_pystaticmethod(app):\n-    text = (\".. py:class:: Class\\n\"\n-            \"\\n\"\n-            \"   .. py:staticmethod:: meth\\n\")\n-    domain = app.env.get_domain('py')\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_annotation, \"class \"],\n-                                                    [desc_name, \"Class\"])],\n-                                  [desc_content, (addnodes.index,\n-                                                  desc)])]))\n-    assert_node(doctree[1][1][0], addnodes.index,\n-                entries=[('single', 'meth() (Class static method)', 'Class.meth', '', None)])\n-    assert_node(doctree[1][1][1], ([desc_signature, ([desc_annotation, \"static \"],\n-                                                     [desc_name, \"meth\"],\n-                                                     [desc_parameterlist, ()])],\n-                                   [desc_content, ()]))\n-    assert 'Class.meth' in domain.objects\n-    assert domain.objects['Class.meth'] == ('index', 'Class.meth', 'method')\n-\n-\n-def test_pyattribute(app):\n-    text = (\".. py:class:: Class\\n\"\n-            \"\\n\"\n-            \"   .. py:attribute:: attr\\n\"\n-            \"      :type: str\\n\"\n-            \"      :value: ''\\n\")\n-    domain = app.env.get_domain('py')\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_annotation, \"class \"],\n-                                                    [desc_name, \"Class\"])],\n-                                  [desc_content, (addnodes.index,\n-                                                  desc)])]))\n-    assert_node(doctree[1][1][0], addnodes.index,\n-                entries=[('single', 'attr (Class attribute)', 'Class.attr', '', None)])\n-    assert_node(doctree[1][1][1], ([desc_signature, ([desc_name, \"attr\"],\n-                                                     [desc_annotation, (\": \",\n-                                                                        [pending_xref, \"str\"])],\n-                                                     [desc_annotation, \" = ''\"])],\n-                                   [desc_content, ()]))\n-    assert 'Class.attr' in domain.objects\n-    assert domain.objects['Class.attr'] == ('index', 'Class.attr', 'attribute')\n-\n-\n-def test_pydecorator_signature(app):\n-    text = \".. py:decorator:: deco\"\n-    domain = app.env.get_domain('py')\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_addname, \"@\"],\n-                                                    [desc_name, \"deco\"])],\n-                                  desc_content)]))\n-    assert_node(doctree[1], addnodes.desc, desctype=\"function\",\n-                domain=\"py\", objtype=\"function\", noindex=False)\n-\n-    assert 'deco' in domain.objects\n-    assert domain.objects['deco'] == ('index', 'deco', 'function')\n-\n-\n-def test_pydecoratormethod_signature(app):\n-    text = \".. py:decoratormethod:: deco\"\n-    domain = app.env.get_domain('py')\n-    doctree = restructuredtext.parse(app, text)\n-    assert_node(doctree, (addnodes.index,\n-                          [desc, ([desc_signature, ([desc_addname, \"@\"],\n-                                                    [desc_name, \"deco\"])],\n-                                  desc_content)]))\n-    assert_node(doctree[1], addnodes.desc, desctype=\"method\",\n-                domain=\"py\", objtype=\"method\", noindex=False)\n-\n-    assert 'deco' in domain.objects\n-    assert domain.objects['deco'] == ('index', 'deco', 'method')\n-\n-\*********************(freshenv=True)\n-def test_module_index(app):\n-    text = (\".. py:module:: docutils\\n\"\n-            \".. py:module:: sphinx\\n\"\n-            \".. py:module:: sphinx.config\\n\"\n-            \".. py:module:: sphinx.builders\\n\"\n-            \".. py:module:: sphinx.builders.html\\n\"\n-            \".. py:module:: sphinx_intl\\n\")\n-    restructuredtext.parse(app, text)\n-    index = PythonModuleIndex(app.env.get_domain('py'))\n-    assert index.generate() == (\n-        [('d', [IndexEntry('docutils', 0, 'index', 'module-docutils', '', '', '')]),\n-         ('s', [IndexEntry('sphinx', 1, 'index', 'module-sphinx', '', '', ''),\n-                IndexEntry('sphinx.builders', 2, 'index', 'module-sphinx.builders', '', '', ''),  # NOQA\n-                IndexEntry('sphinx.builders.html', 2, 'index', 'module-sphinx.builders.html', '', '', ''),  # NOQA\n-                IndexEntry('sphinx.config', 2, 'index', 'module-sphinx.config', '', '', ''),\n-                IndexEntry('sphinx_intl', 0, 'index', 'module-sphinx_intl', '', '', '')])],\n-        False\n-    )\n-\n-\*********************(freshenv=True)\n-def test_module_index_submodule(app):\n-    text = \".. py:module:: sphinx.config\\n\"\n-    restructuredtext.parse(app, text)\n-    index = PythonModuleIndex(app.env.get_domain('py'))\n-    assert index.generate() == (\n-        [('s', [IndexEntry('sphinx', 1, '', '', '', '', ''),\n-                IndexEntry('sphinx.config', 2, 'index', 'module-sphinx.config', '', '', '')])],\n-        False\n-    )\n-\n-\*********************(freshenv=True)\n-def test_module_index_not_collapsed(app):\n-    text = (\".. py:module:: docutils\\n\"\n-            \".. py:module:: sphinx\\n\")\n-    restructuredtext.parse(app, text)\n-    index = PythonModuleIndex(app.env.get_domain('py'))\n-    assert index.generate() == (\n-        [('d', [IndexEntry('docutils', 0, 'index', 'module-docutils', '', '', '')]),\n-         ('s', [IndexEntry('sphinx', 0, 'index', 'module-sphinx', '', '', '')])],\n-        True\n-    )\n-\n-\*********************(freshenv=True, confoverrides={'modindex_common_prefix': ['sphinx.']})\n-def test_modindex_common_prefix(app):\n-    text = (\".. py:module:: docutils\\n\"\n-            \".. py:module:: sphinx\\n\"\n-            \".. py:module:: sphinx.config\\n\"\n-            \".. py:module:: sphinx.builders\\n\"\n-            \".. py:module:: sphinx.builders.html\\n\"\n-            \".. py:module:: sphinx_intl\\n\")\n-    restructuredtext.parse(app, text)\n-    index = PythonModuleIndex(app.env.get_domain('py'))\n-    assert index.generate() == (\n-        [('b', [IndexEntry('sphinx.builders', 1, 'index', 'module-sphinx.builders', '', '', ''),  # NOQA\n-                IndexEntry('sphinx.builders.html', 2, 'index', 'module-sphinx.builders.html', '', '', '')]),  # NOQA\n-         ('c', [IndexEntry('sphinx.config', 0, 'index', 'module-sphinx.config', '', '', '')]),\n-         ('d', [IndexEntry('docutils', 0, 'index', 'module-docutils', '', '', '')]),\n-         ('s', [IndexEntry('sphinx', 0, 'index', 'module-sphinx', '', '', ''),\n-                IndexEntry('sphinx_intl', 0, 'index', 'module-sphinx_intl', '', '', '')])],\n-        True\n-    )\n-\n-\n+    assert domain.get_full_qualified_name(node) == 'module1.Class.func'\n\\ No newline at end of file\ndiff --git a/tox.ini b/tox.ini\nindex d9f040544..bf39854b6 100644\n--- a/tox.ini\n+++ b/tox.ini\n@@ -28,7 +28,7 @@ extras =\n setenv =\n     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils\n commands=\n-    pytest --durations 25 {posargs}\n+    pytest -rA --durations 25 {posargs}\n \n [testenv:flake8]\n basepython = python3\ndiff --git a/sphinx/domains/_arglist.py b/sphinx/domains/_arglist.py\nnew file mode 100644\nindex 000000000..920d312c8\n--- /dev/null\n+++ b/sphinx/domains/_arglist.py\n@@ -0,0 +1,48 @@\n+# Test changes\n+def _parse_arglist(arglist: str) -> addnodes.desc_parameterlist:\n+    \"\"\"Parse a list of arguments using AST parser\"\"\"\n+    params = addnodes.desc_parameterlist(arglist)\n+    sig = signature_from_str('(%s)' % arglist)\n+    last_kind = None\n+    for param in sig.parameters.values():\n+        if param.kind != param.POSITIONAL_ONLY and last_kind == param.POSITIONAL_ONLY:\n+            # PEP-570: Separator for Positional Only Parameter: /\n+            params += addnodes.desc_parameter('', '', addnodes.desc_sig_operator('', '/'))\n+        if param.kind == param.KEYWORD_ONLY and last_kind in (param.POSITIONAL_OR_KEYWORD,\n+                                                              param.POSITIONAL_ONLY,\n+                                                              None):\n+            # PEP-3102: Separator for Keyword Only Parameter: *\n+            params += addnodes.desc_parameter('', '', addnodes.desc_sig_operator('', '*'))\n+\n+        node = addnodes.desc_parameter()\n+        if param.kind == param.VAR_POSITIONAL:\n+            node += addnodes.desc_sig_operator('', '*')\n+            node += addnodes.desc_sig_name('', param.name)\n+        elif param.kind == param.VAR_KEYWORD:\n+            node += addnodes.desc_sig_operator('', '**')\n+            node += addnodes.desc_sig_name('', param.name)\n+        else:\n+            node += addnodes.desc_sig_name('', param.name)\n+\n+        if param.annotation is not param.empty:\n+            children = _parse_annotation(param.annotation)\n+            node += addnodes.desc_sig_punctuation('', ':')\n+            node += nodes.Text(' ')\n+            node += addnodes.desc_sig_name('', '', *children)  # type: ignore\n+        if param.default is not param.empty:\n+            node += addnodes.desc_sig_operator('', '=')\n+            node += nodes.Text(' ')  # Add space after =\n+            default_value = str(param.default)\n+            if isinstance(param.default, str):\n+                default_value = repr(param.default)\n+            node += nodes.inline('', default_value, classes=['default_value'],\n+                             support_smartquotes=False)\n+\n+        params += node\n+        last_kind = param.kind\n+\n+    if last_kind == Parameter.POSITIONAL_ONLY:\n+        # PEP-570: Separator for Positional Only Parameter: /\n+        params += addnodes.desc_parameter('', '', addnodes.desc_sig_operator('', '/'))\n+\n+    return params\n\\ No newline at end of file\ndiff --git a/tests/roots/test-domain-py/pos_only_params.rst b/tests/roots/test-domain-py/pos_only_params.rst\nnew file mode 100644\nindex 000000000..9df61c22b\n--- /dev/null\n+++ b/tests/roots/test-domain-py/pos_only_params.rst\n@@ -0,0 +1,6 @@\n+Test document\n+============\n+\n+.. py:function:: foo(a, b=0, /, c=1)\n+   \n+   Test function with positional-only parameters.\n\\ No newline at end of file\ndiff --git a/tests/roots/test-domain-py/test-pos-only-default/conf.py b/tests/roots/test-domain-py/test-pos-only-default/conf.py\nnew file mode 100644\nindex 000000000..e59383f41\n--- /dev/null\n+++ b/tests/roots/test-domain-py/test-pos-only-default/conf.py\n@@ -0,0 +1,2 @@\n+project = 'test-pos-only-default'\n+extensions = ['sphinx.ext.autodoc']\n\\ No newline at end of file\ndiff --git a/tests/roots/test-domain-py/test-pos-only-default/index.rst b/tests/roots/test-domain-py/test-pos-only-default/index.rst\nnew file mode 100644\nindex 000000000..f6665c837\n--- /dev/null\n+++ b/tests/roots/test-domain-py/test-pos-only-default/index.rst\n@@ -0,0 +1,6 @@\n+Test document\n+============\n+\n+.. py:function:: foo(a, b=0, /, c=1)\n+\n+   Test function with positional-only default parameters.\n\\ No newline at end of file\n", "proposed_model_patches": []}