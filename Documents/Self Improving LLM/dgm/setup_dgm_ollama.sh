#!/bin/bash

# Setup script for Darwin Gödel Machine with local Ollama integration
# This script sets up the DGM project to work with your local qwen3:30b-a3b model

echo "=== Darwin <PERSON>del Machine - Ollama Setup ==="
echo ""

# Check if we're in the right directory
if [ ! -f "DGM_outer.py" ]; then
    echo "Error: Please run this script from the dgm directory"
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

# Check if Ollama is running
echo "Checking Ollama server..."
if curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "✓ Ollama server is running"
    
    # Check if qwen3:30b-a3b model is available
    if curl -s http://localhost:11434/api/tags | grep -q "qwen3:30b-a3b"; then
        echo "✓ qwen3:30b-a3b model is available"
    else
        echo "⚠ qwen3:30b-a3b model not found"
        echo "Please run: ollama pull qwen3:30b-a3b"
        echo "This may take some time as it's a large model..."
    fi
else
    echo "⚠ Ollama server is not running on localhost:11434"
    echo "Please start Ollama with: ollama serve"
    echo "Then pull the model with: ollama pull qwen3:30b-a3b"
fi

echo ""
echo "=== Setup Complete ==="
echo ""
echo "To start the DGM system:"
echo "1. Make sure Ollama is running: ollama serve"
echo "2. Make sure you have the model: ollama pull qwen3:30b-a3b"
echo "3. Start LiteLLM proxy: ./start_litellm.sh"
echo "4. In another terminal, run DGM: python DGM_outer.py"
echo ""
echo "The system is now configured to use your local qwen3:30b-a3b model!"
