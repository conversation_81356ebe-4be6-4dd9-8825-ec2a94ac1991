+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=2
++ CONDA_SHLVL=2
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_1=/opt/miniconda3
++ CONDA_PREFIX_1=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ cd /testbed
+ git config --global --add safe.directory /testbed
+ cd /testbed
+ git status
On branch master
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   sphinx/domains/python.py
	modified:   tox.ini

Untracked files:
  (use "git add <file>..." to include in what will be committed)
	setup.py.orig

no changes added to commit (use "git add" and/or "git commit -a")
+ git show
commit 8ec06e9a1bd862cd713b9db748e039ccc7b3e15b
Merge: 830b3fbe2 6a0dae08d
Author: Takeshi KOMIYA <<EMAIL>>
Date:   Sun May 30 00:39:41 2021 +0900

    Merge pull request #9277 from greyli/fix-intl-docs
    
    Fix typo in doc/intl.rst

+ git diff 8ec06e9a1bd862cd713b9db748e039ccc7b3e15b
diff --git a/sphinx/domains/python.py b/sphinx/domains/python.py
index 5035ce2ab..cd65bf019 100644
--- a/sphinx/domains/python.py
+++ b/sphinx/domains/python.py
@@ -14,6 +14,7 @@ import re
 import sys
 import typing
 import warnings
+import enum
 from inspect import Parameter
 from typing import Any, Dict, Iterable, Iterator, List, NamedTuple, Optional, Tuple, Type, cast
 
@@ -43,6 +44,21 @@ from sphinx.util.typing import OptionSpec, TextlikeNode
 logger = logging.getLogger(__name__)
 
 
+def format_default_value(value: str) -> str:
+    """Format default value for parameter, handling enum values specially."""
+    if value.startswith('<') and value.endswith('>') and '.' in value:
+        # This looks like an Enum value string representation
+        # Try to extract just the enum member name
+        try:
+            enum_parts = value.strip('<>').split('.')
+            if len(enum_parts) >= 2:
+                # Return just EnumClass.MEMBER_NAME
+                return '.'.join(enum_parts[:-1]) + '.' + enum_parts[-1].split(':')[0].strip()
+        except Exception:
+            pass
+    return value
+
+
 # REs for Python signatures
 py_sig_re = re.compile(
     r'''^ ([\w.]*\.)?            # class name(s)
@@ -218,8 +234,9 @@ def _parse_arglist(arglist: str, env: BuildEnvironment = None) -> addnodes.desc_
                 node += nodes.Text(' ')
             else:
                 node += addnodes.desc_sig_operator('', '=')
-            node += nodes.inline('', param.default, classes=['default_value'],
-                                 support_smartquotes=False)
+            formatted_default = format_default_value(param.default)
+            node += nodes.inline('', formatted_default, classes=['default_value'],
+                               support_smartquotes=False)
 
         params += node
         last_kind = param.kind
@@ -228,1168 +245,4 @@ def _parse_arglist(arglist: str, env: BuildEnvironment = None) -> addnodes.desc_
         # PEP-570: Separator for Positional Only Parameter: /
         params += addnodes.desc_parameter('', '', addnodes.desc_sig_operator('', '/'))
 
-    return params
-
-
-def _pseudo_parse_arglist(signode: desc_signature, arglist: str) -> None:
-    """"Parse" a list of arguments separated by commas.
-
-    Arguments can have "optional" annotations given by enclosing them in
-    brackets.  Currently, this will split at any comma, even if it's inside a
-    string literal (e.g. default argument value).
-    """
-    paramlist = addnodes.desc_parameterlist()
-    stack: List[Element] = [paramlist]
-    try:
-        for argument in arglist.split(','):
-            argument = argument.strip()
-            ends_open = ends_close = 0
-            while argument.startswith('['):
-                stack.append(addnodes.desc_optional())
-                stack[-2] += stack[-1]
-                argument = argument[1:].strip()
-            while argument.startswith(']'):
-                stack.pop()
-                argument = argument[1:].strip()
-            while argument.endswith(']') and not argument.endswith('[]'):
-                ends_close += 1
-                argument = argument[:-1].strip()
-            while argument.endswith('['):
-                ends_open += 1
-                argument = argument[:-1].strip()
-            if argument:
-                stack[-1] += addnodes.desc_parameter(argument, argument)
-            while ends_open:
-                stack.append(addnodes.desc_optional())
-                stack[-2] += stack[-1]
-                ends_open -= 1
-            while ends_close:
-                stack.pop()
-                ends_close -= 1
-        if len(stack) != 1:
-            raise IndexError
-    except IndexError:
-        # if there are too few or too many elements on the stack, just give up
-        # and treat the whole argument list as one argument, discarding the
-        # already partially populated paramlist node
-        paramlist = addnodes.desc_parameterlist()
-        paramlist += addnodes.desc_parameter(arglist, arglist)
-        signode += paramlist
-    else:
-        signode += paramlist
-
-
-# This override allows our inline type specifiers to behave like :class: link
-# when it comes to handling "." and "~" prefixes.
-class PyXrefMixin:
-    def make_xref(self, rolename: str, domain: str, target: str,
-                  innernode: Type[TextlikeNode] = nodes.emphasis,
-                  contnode: Node = None, env: BuildEnvironment = None) -> Node:
-        result = super().make_xref(rolename, domain, target,  # type: ignore
-                                   innernode, contnode, env)
-        result['refspecific'] = True
-        result['py:module'] = env.ref_context.get('py:module')
-        result['py:class'] = env.ref_context.get('py:class')
-        if target.startswith(('.', '~')):
-            prefix, result['reftarget'] = target[0], target[1:]
-            if prefix == '.':
-                text = target[1:]
-            elif prefix == '~':
-                text = target.split('.')[-1]
-            for node in result.traverse(nodes.Text):
-                node.parent[node.parent.index(node)] = nodes.Text(text)
-                break
-        return result
-
-    def make_xrefs(self, rolename: str, domain: str, target: str,
-                   innernode: Type[TextlikeNode] = nodes.emphasis,
-                   contnode: Node = None, env: BuildEnvironment = None) -> List[Node]:
-        delims = r'(\s*[\[\]\(\),](?:\s*or\s)?\s*|\s+or\s+|\s*\|\s*|\.\.\.)'
-        delims_re = re.compile(delims)
-        sub_targets = re.split(delims, target)
-
-        split_contnode = bool(contnode and contnode.astext() == target)
-
-        results = []
-        for sub_target in filter(None, sub_targets):
-            if split_contnode:
-                contnode = nodes.Text(sub_target)
-
-            if delims_re.match(sub_target):
-                results.append(contnode or innernode(sub_target, sub_target))
-            else:
-                results.append(self.make_xref(rolename, domain, sub_target,
-                                              innernode, contnode, env))
-
-        return results
-
-
-class PyField(PyXrefMixin, Field):
-    def make_xref(self, rolename: str, domain: str, target: str,
-                  innernode: Type[TextlikeNode] = nodes.emphasis,
-                  contnode: Node = None, env: BuildEnvironment = None) -> Node:
-        if rolename == 'class' and target == 'None':
-            # None is not a type, so use obj role instead.
-            rolename = 'obj'
-
-        return super().make_xref(rolename, domain, target, innernode, contnode, env)
-
-
-class PyGroupedField(PyXrefMixin, GroupedField):
-    pass
-
-
-class PyTypedField(PyXrefMixin, TypedField):
-    def make_xref(self, rolename: str, domain: str, target: str,
-                  innernode: Type[TextlikeNode] = nodes.emphasis,
-                  contnode: Node = None, env: BuildEnvironment = None) -> Node:
-        if rolename == 'class' and target == 'None':
-            # None is not a type, so use obj role instead.
-            rolename = 'obj'
-
-        return super().make_xref(rolename, domain, target, innernode, contnode, env)
-
-
-class PyObject(ObjectDescription[Tuple[str, str]]):
-    """
-    Description of a general Python object.
-
-    :cvar allow_nesting: Class is an object that allows for nested namespaces
-    :vartype allow_nesting: bool
-    """
-    option_spec: OptionSpec = {
-        'noindex': directives.flag,
-        'noindexentry': directives.flag,
-        'module': directives.unchanged,
-        'canonical': directives.unchanged,
-        'annotation': directives.unchanged,
-    }
-
-    doc_field_types = [
-        PyTypedField('parameter', label=_('Parameters'),
-                     names=('param', 'parameter', 'arg', 'argument',
-                            'keyword', 'kwarg', 'kwparam'),
-                     typerolename='class', typenames=('paramtype', 'type'),
-                     can_collapse=True),
-        PyTypedField('variable', label=_('Variables'),
-                     names=('var', 'ivar', 'cvar'),
-                     typerolename='class', typenames=('vartype',),
-                     can_collapse=True),
-        PyGroupedField('exceptions', label=_('Raises'), rolename='exc',
-                       names=('raises', 'raise', 'exception', 'except'),
-                       can_collapse=True),
-        Field('returnvalue', label=_('Returns'), has_arg=False,
-              names=('returns', 'return')),
-        PyField('returntype', label=_('Return type'), has_arg=False,
-                names=('rtype',), bodyrolename='class'),
-    ]
-
-    allow_nesting = False
-
-    def get_signature_prefix(self, sig: str) -> str:
-        """May return a prefix to put before the object name in the
-        signature.
-        """
-        return ''
-
-    def needs_arglist(self) -> bool:
-        """May return true if an empty argument list is to be generated even if
-        the document contains none.
-        """
-        return False
-
-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:
-        """Transform a Python signature into RST nodes.
-
-        Return (fully qualified name of the thing, classname if any).
-
-        If inside a class, the current class name is handled intelligently:
-        * it is stripped from the displayed name if present
-        * it is added to the full name (return value) if not present
-        """
-        m = py_sig_re.match(sig)
-        if m is None:
-            raise ValueError
-        prefix, name, arglist, retann = m.groups()
-
-        # determine module and class name (if applicable), as well as full name
-        modname = self.options.get('module', self.env.ref_context.get('py:module'))
-        classname = self.env.ref_context.get('py:class')
-        if classname:
-            add_module = False
-            if prefix and (prefix == classname or
-                           prefix.startswith(classname + ".")):
-                fullname = prefix + name
-                # class name is given again in the signature
-                prefix = prefix[len(classname):].lstrip('.')
-            elif prefix:
-                # class name is given in the signature, but different
-                # (shouldn't happen)
-                fullname = classname + '.' + prefix + name
-            else:
-                # class name is not given in the signature
-                fullname = classname + '.' + name
-        else:
-            add_module = True
-            if prefix:
-                classname = prefix.rstrip('.')
-                fullname = prefix + name
-            else:
-                classname = ''
-                fullname = name
-
-        signode['module'] = modname
-        signode['class'] = classname
-        signode['fullname'] = fullname
-
-        sig_prefix = self.get_signature_prefix(sig)
-        if sig_prefix:
-            signode += addnodes.desc_annotation(sig_prefix, sig_prefix)
-
-        if prefix:
-            signode += addnodes.desc_addname(prefix, prefix)
-        elif add_module and self.env.config.add_module_names:
-            if modname and modname != 'exceptions':
-                # exceptions are a special case, since they are documented in the
-                # 'exceptions' module.
-                nodetext = modname + '.'
-                signode += addnodes.desc_addname(nodetext, nodetext)
-
-        signode += addnodes.desc_name(name, name)
-        if arglist:
-            try:
-                signode += _parse_arglist(arglist, self.env)
-            except SyntaxError:
-                # fallback to parse arglist original parser.
-                # it supports to represent optional arguments (ex. "func(foo [, bar])")
-                _pseudo_parse_arglist(signode, arglist)
-            except NotImplementedError as exc:
-                logger.warning("could not parse arglist (%r): %s", arglist, exc,
-                               location=signode)
-                _pseudo_parse_arglist(signode, arglist)
-        else:
-            if self.needs_arglist():
-                # for callables, add an empty parameter list
-                signode += addnodes.desc_parameterlist()
-
-        if retann:
-            children = _parse_annotation(retann, self.env)
-            signode += addnodes.desc_returns(retann, '', *children)
-
-        anno = self.options.get('annotation')
-        if anno:
-            signode += addnodes.desc_annotation(' ' + anno, ' ' + anno)
-
-        return fullname, prefix
-
-    def get_index_text(self, modname: str, name: Tuple[str, str]) -> str:
-        """Return the text for the index entry of the object."""
-        raise NotImplementedError('must be implemented in subclasses')
-
-    def add_target_and_index(self, name_cls: Tuple[str, str], sig: str,
-                             signode: desc_signature) -> None:
-        modname = self.options.get('module', self.env.ref_context.get('py:module'))
-        fullname = (modname + '.' if modname else '') + name_cls[0]
-        node_id = make_id(self.env, self.state.document, '', fullname)
-        signode['ids'].append(node_id)
-
-        # Assign old styled node_id(fullname) not to break old hyperlinks (if possible)
-        # Note: Will removed in Sphinx-5.0  (RemovedInSphinx50Warning)
-        if node_id != fullname and fullname not in self.state.document.ids:
-            signode['ids'].append(fullname)
-
-        self.state.document.note_explicit_target(signode)
-
-        domain = cast(PythonDomain, self.env.get_domain('py'))
-        domain.note_object(fullname, self.objtype, node_id, location=signode)
-
-        canonical_name = self.options.get('canonical')
-        if canonical_name:
-            domain.note_object(canonical_name, self.objtype, node_id, aliased=True,
-                               location=signode)
-
-        if 'noindexentry' not in self.options:
-            indextext = self.get_index_text(modname, name_cls)
-            if indextext:
-                self.indexnode['entries'].append(('single', indextext, node_id, '', None))
-
-    def before_content(self) -> None:
-        """Handle object nesting before content
-
-        :py:class:`PyObject` represents Python language constructs. For
-        constructs that are nestable, such as a Python classes, this method will
-        build up a stack of the nesting hierarchy so that it can be later
-        de-nested correctly, in :py:meth:`after_content`.
-
-        For constructs that aren't nestable, the stack is bypassed, and instead
-        only the most recent object is tracked. This object prefix name will be
-        removed with :py:meth:`after_content`.
-        """
-        prefix = None
-        if self.names:
-            # fullname and name_prefix come from the `handle_signature` method.
-            # fullname represents the full object name that is constructed using
-            # object nesting and explicit prefixes. `name_prefix` is the
-            # explicit prefix given in a signature
-            (fullname, name_prefix) = self.names[-1]
-            if self.allow_nesting:
-                prefix = fullname
-            elif name_prefix:
-                prefix = name_prefix.strip('.')
-        if prefix:
-            self.env.ref_context['py:class'] = prefix
-            if self.allow_nesting:
-                classes = self.env.ref_context.setdefault('py:classes', [])
-                classes.append(prefix)
-        if 'module' in self.options:
-            modules = self.env.ref_context.setdefault('py:modules', [])
-            modules.append(self.env.ref_context.get('py:module'))
-            self.env.ref_context['py:module'] = self.options['module']
-
-    def after_content(self) -> None:
-        """Handle object de-nesting after content
-
-        If this class is a nestable object, removing the last nested class prefix
-        ends further nesting in the object.
-
-        If this class is not a nestable object, the list of classes should not
-        be altered as we didn't affect the nesting levels in
-        :py:meth:`before_content`.
-        """
-        classes = self.env.ref_context.setdefault('py:classes', [])
-        if self.allow_nesting:
-            try:
-                classes.pop()
-            except IndexError:
-                pass
-        self.env.ref_context['py:class'] = (classes[-1] if len(classes) > 0
-                                            else None)
-        if 'module' in self.options:
-            modules = self.env.ref_context.setdefault('py:modules', [])
-            if modules:
-                self.env.ref_context['py:module'] = modules.pop()
-            else:
-                self.env.ref_context.pop('py:module')
-
-
-class PyFunction(PyObject):
-    """Description of a function."""
-
-    option_spec: OptionSpec = PyObject.option_spec.copy()
-    option_spec.update({
-        'async': directives.flag,
-    })
-
-    def get_signature_prefix(self, sig: str) -> str:
-        if 'async' in self.options:
-            return 'async '
-        else:
-            return ''
-
-    def needs_arglist(self) -> bool:
-        return True
-
-    def add_target_and_index(self, name_cls: Tuple[str, str], sig: str,
-                             signode: desc_signature) -> None:
-        super().add_target_and_index(name_cls, sig, signode)
-        if 'noindexentry' not in self.options:
-            modname = self.options.get('module', self.env.ref_context.get('py:module'))
-            node_id = signode['ids'][0]
-
-            name, cls = name_cls
-            if modname:
-                text = _('%s() (in module %s)') % (name, modname)
-                self.indexnode['entries'].append(('single', text, node_id, '', None))
-            else:
-                text = '%s; %s()' % (pairindextypes['builtin'], name)
-                self.indexnode['entries'].append(('pair', text, node_id, '', None))
-
-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:
-        # add index in own add_target_and_index() instead.
-        return None
-
-
-class PyDecoratorFunction(PyFunction):
-    """Description of a decorator."""
-
-    def run(self) -> List[Node]:
-        # a decorator function is a function after all
-        self.name = 'py:function'
-        return super().run()
-
-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:
-        ret = super().handle_signature(sig, signode)
-        signode.insert(0, addnodes.desc_addname('@', '@'))
-        return ret
-
-    def needs_arglist(self) -> bool:
-        return False
-
-
-class PyVariable(PyObject):
-    """Description of a variable."""
-
-    option_spec: OptionSpec = PyObject.option_spec.copy()
-    option_spec.update({
-        'type': directives.unchanged,
-        'value': directives.unchanged,
-    })
-
-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:
-        fullname, prefix = super().handle_signature(sig, signode)
-
-        typ = self.options.get('type')
-        if typ:
-            annotations = _parse_annotation(typ, self.env)
-            signode += addnodes.desc_annotation(typ, '', nodes.Text(': '), *annotations)
-
-        value = self.options.get('value')
-        if value:
-            signode += addnodes.desc_annotation(value, ' = ' + value)
-
-        return fullname, prefix
-
-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:
-        name, cls = name_cls
-        if modname:
-            return _('%s (in module %s)') % (name, modname)
-        else:
-            return _('%s (built-in variable)') % name
-
-
-class PyClasslike(PyObject):
-    """
-    Description of a class-like object (classes, interfaces, exceptions).
-    """
-
-    option_spec: OptionSpec = PyObject.option_spec.copy()
-    option_spec.update({
-        'final': directives.flag,
-    })
-
-    allow_nesting = True
-
-    def get_signature_prefix(self, sig: str) -> str:
-        if 'final' in self.options:
-            return 'final %s ' % self.objtype
-        else:
-            return '%s ' % self.objtype
-
-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:
-        if self.objtype == 'class':
-            if not modname:
-                return _('%s (built-in class)') % name_cls[0]
-            return _('%s (class in %s)') % (name_cls[0], modname)
-        elif self.objtype == 'exception':
-            return name_cls[0]
-        else:
-            return ''
-
-
-class PyMethod(PyObject):
-    """Description of a method."""
-
-    option_spec: OptionSpec = PyObject.option_spec.copy()
-    option_spec.update({
-        'abstractmethod': directives.flag,
-        'async': directives.flag,
-        'classmethod': directives.flag,
-        'final': directives.flag,
-        'property': directives.flag,
-        'staticmethod': directives.flag,
-    })
-
-    def needs_arglist(self) -> bool:
-        if 'property' in self.options:
-            return False
-        else:
-            return True
-
-    def get_signature_prefix(self, sig: str) -> str:
-        prefix = []
-        if 'final' in self.options:
-            prefix.append('final')
-        if 'abstractmethod' in self.options:
-            prefix.append('abstract')
-        if 'async' in self.options:
-            prefix.append('async')
-        if 'classmethod' in self.options:
-            prefix.append('classmethod')
-        if 'property' in self.options:
-            prefix.append('property')
-        if 'staticmethod' in self.options:
-            prefix.append('static')
-
-        if prefix:
-            return ' '.join(prefix) + ' '
-        else:
-            return ''
-
-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:
-        name, cls = name_cls
-        try:
-            clsname, methname = name.rsplit('.', 1)
-            if modname and self.env.config.add_module_names:
-                clsname = '.'.join([modname, clsname])
-        except ValueError:
-            if modname:
-                return _('%s() (in module %s)') % (name, modname)
-            else:
-                return '%s()' % name
-
-        if 'classmethod' in self.options:
-            return _('%s() (%s class method)') % (methname, clsname)
-        elif 'property' in self.options:
-            return _('%s() (%s property)') % (methname, clsname)
-        elif 'staticmethod' in self.options:
-            return _('%s() (%s static method)') % (methname, clsname)
-        else:
-            return _('%s() (%s method)') % (methname, clsname)
-
-
-class PyClassMethod(PyMethod):
-    """Description of a classmethod."""
-
-    option_spec: OptionSpec = PyObject.option_spec.copy()
-
-    def run(self) -> List[Node]:
-        self.name = 'py:method'
-        self.options['classmethod'] = True
-
-        return super().run()
-
-
-class PyStaticMethod(PyMethod):
-    """Description of a staticmethod."""
-
-    option_spec: OptionSpec = PyObject.option_spec.copy()
-
-    def run(self) -> List[Node]:
-        self.name = 'py:method'
-        self.options['staticmethod'] = True
-
-        return super().run()
-
-
-class PyDecoratorMethod(PyMethod):
-    """Description of a decoratormethod."""
-
-    def run(self) -> List[Node]:
-        self.name = 'py:method'
-        return super().run()
-
-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:
-        ret = super().handle_signature(sig, signode)
-        signode.insert(0, addnodes.desc_addname('@', '@'))
-        return ret
-
-    def needs_arglist(self) -> bool:
-        return False
-
-
-class PyAttribute(PyObject):
-    """Description of an attribute."""
-
-    option_spec: OptionSpec = PyObject.option_spec.copy()
-    option_spec.update({
-        'type': directives.unchanged,
-        'value': directives.unchanged,
-    })
-
-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:
-        fullname, prefix = super().handle_signature(sig, signode)
-
-        typ = self.options.get('type')
-        if typ:
-            annotations = _parse_annotation(typ, self.env)
-            signode += addnodes.desc_annotation(typ, '', nodes.Text(': '), *annotations)
-
-        value = self.options.get('value')
-        if value:
-            signode += addnodes.desc_annotation(value, ' = ' + value)
-
-        return fullname, prefix
-
-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:
-        name, cls = name_cls
-        try:
-            clsname, attrname = name.rsplit('.', 1)
-            if modname and self.env.config.add_module_names:
-                clsname = '.'.join([modname, clsname])
-        except ValueError:
-            if modname:
-                return _('%s (in module %s)') % (name, modname)
-            else:
-                return name
-
-        return _('%s (%s attribute)') % (attrname, clsname)
-
-
-class PyProperty(PyObject):
-    """Description of an attribute."""
-
-    option_spec = PyObject.option_spec.copy()
-    option_spec.update({
-        'abstractmethod': directives.flag,
-        'type': directives.unchanged,
-    })
-
-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:
-        fullname, prefix = super().handle_signature(sig, signode)
-
-        typ = self.options.get('type')
-        if typ:
-            signode += addnodes.desc_annotation(typ, ': ' + typ)
-
-        return fullname, prefix
-
-    def get_signature_prefix(self, sig: str) -> str:
-        prefix = ['property']
-        if 'abstractmethod' in self.options:
-            prefix.insert(0, 'abstract')
-
-        return ' '.join(prefix) + ' '
-
-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:
-        name, cls = name_cls
-        try:
-            clsname, attrname = name.rsplit('.', 1)
-            if modname and self.env.config.add_module_names:
-                clsname = '.'.join([modname, clsname])
-        except ValueError:
-            if modname:
-                return _('%s (in module %s)') % (name, modname)
-            else:
-                return name
-
-        return _('%s (%s property)') % (attrname, clsname)
-
-
-class PyDecoratorMixin:
-    """
-    Mixin for decorator directives.
-    """
-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:
-        for cls in self.__class__.__mro__:
-            if cls.__name__ != 'DirectiveAdapter':
-                warnings.warn('PyDecoratorMixin is deprecated. '
-                              'Please check the implementation of %s' % cls,
-                              RemovedInSphinx50Warning, stacklevel=2)
-                break
-        else:
-            warnings.warn('PyDecoratorMixin is deprecated',
-                          RemovedInSphinx50Warning, stacklevel=2)
-
-        ret = super().handle_signature(sig, signode)  # type: ignore
-        signode.insert(0, addnodes.desc_addname('@', '@'))
-        return ret
-
-    def needs_arglist(self) -> bool:
-        return False
-
-
-class PyModule(SphinxDirective):
-    """
-    Directive to mark description of a new module.
-    """
-
-    has_content = False
-    required_arguments = 1
-    optional_arguments = 0
-    final_argument_whitespace = False
-    option_spec: OptionSpec = {
-        'platform': lambda x: x,
-        'synopsis': lambda x: x,
-        'noindex': directives.flag,
-        'deprecated': directives.flag,
-    }
-
-    def run(self) -> List[Node]:
-        domain = cast(PythonDomain, self.env.get_domain('py'))
-
-        modname = self.arguments[0].strip()
-        noindex = 'noindex' in self.options
-        self.env.ref_context['py:module'] = modname
-        ret: List[Node] = []
-        if not noindex:
-            # note module to the domain
-            node_id = make_id(self.env, self.state.document, 'module', modname)
-            target = nodes.target('', '', ids=[node_id], ismod=True)
-            self.set_source_info(target)
-
-            # Assign old styled node_id not to break old hyperlinks (if possible)
-            # Note: Will removed in Sphinx-5.0  (RemovedInSphinx50Warning)
-            old_node_id = self.make_old_id(modname)
-            if node_id != old_node_id and old_node_id not in self.state.document.ids:
-                target['ids'].append(old_node_id)
-
-            self.state.document.note_explicit_target(target)
-
-            domain.note_module(modname,
-                               node_id,
-                               self.options.get('synopsis', ''),
-                               self.options.get('platform', ''),
-                               'deprecated' in self.options)
-            domain.note_object(modname, 'module', node_id, location=target)
-
-            # the platform and synopsis aren't printed; in fact, they are only
-            # used in the modindex currently
-            ret.append(target)
-            indextext = '%s; %s' % (pairindextypes['module'], modname)
-            inode = addnodes.index(entries=[('pair', indextext, node_id, '', None)])
-            ret.append(inode)
-        return ret
-
-    def make_old_id(self, name: str) -> str:
-        """Generate old styled node_id.
-
-        Old styled node_id is incompatible with docutils' node_id.
-        It can contain dots and hyphens.
-
-        .. note:: Old styled node_id was mainly used until Sphinx-3.0.
-        """
-        return 'module-%s' % name
-
-
-class PyCurrentModule(SphinxDirective):
-    """
-    This directive is just to tell Sphinx that we're documenting
-    stuff in module foo, but links to module foo won't lead here.
-    """
-
-    has_content = False
-    required_arguments = 1
-    optional_arguments = 0
-    final_argument_whitespace = False
-    option_spec: OptionSpec = {}
-
-    def run(self) -> List[Node]:
-        modname = self.arguments[0].strip()
-        if modname == 'None':
-            self.env.ref_context.pop('py:module', None)
-        else:
-            self.env.ref_context['py:module'] = modname
-        return []
-
-
-class PyXRefRole(XRefRole):
-    def process_link(self, env: BuildEnvironment, refnode: Element,
-                     has_explicit_title: bool, title: str, target: str) -> Tuple[str, str]:
-        refnode['py:module'] = env.ref_context.get('py:module')
-        refnode['py:class'] = env.ref_context.get('py:class')
-        if not has_explicit_title:
-            title = title.lstrip('.')    # only has a meaning for the target
-            target = target.lstrip('~')  # only has a meaning for the title
-            # if the first character is a tilde, don't display the module/class
-            # parts of the contents
-            if title[0:1] == '~':
-                title = title[1:]
-                dot = title.rfind('.')
-                if dot != -1:
-                    title = title[dot + 1:]
-        # if the first character is a dot, search more specific namespaces first
-        # else search builtins first
-        if target[0:1] == '.':
-            target = target[1:]
-            refnode['refspecific'] = True
-        return title, target
-
-
-def filter_meta_fields(app: Sphinx, domain: str, objtype: str, content: Element) -> None:
-    """Filter ``:meta:`` field from its docstring."""
-    if domain != 'py':
-        return
-
-    for node in content:
-        if isinstance(node, nodes.field_list):
-            fields = cast(List[nodes.field], node)
-            for field in fields:
-                field_name = cast(nodes.field_body, field[0]).astext().strip()
-                if field_name == 'meta' or field_name.startswith('meta '):
-                    node.remove(field)
-                    break
-
-
-class PythonModuleIndex(Index):
-    """
-    Index subclass to provide the Python module index.
-    """
-
-    name = 'modindex'
-    localname = _('Python Module Index')
-    shortname = _('modules')
-
-    def generate(self, docnames: Iterable[str] = None
-                 ) -> Tuple[List[Tuple[str, List[IndexEntry]]], bool]:
-        content: Dict[str, List[IndexEntry]] = {}
-        # list of prefixes to ignore
-        ignores: List[str] = self.domain.env.config['modindex_common_prefix']
-        ignores = sorted(ignores, key=len, reverse=True)
-        # list of all modules, sorted by module name
-        modules = sorted(self.domain.data['modules'].items(),
-                         key=lambda x: x[0].lower())
-        # sort out collapsable modules
-        prev_modname = ''
-        num_toplevels = 0
-        for modname, (docname, node_id, synopsis, platforms, deprecated) in modules:
-            if docnames and docname not in docnames:
-                continue
-
-            for ignore in ignores:
-                if modname.startswith(ignore):
-                    modname = modname[len(ignore):]
-                    stripped = ignore
-                    break
-            else:
-                stripped = ''
-
-            # we stripped the whole module name?
-            if not modname:
-                modname, stripped = stripped, ''
-
-            entries = content.setdefault(modname[0].lower(), [])
-
-            package = modname.split('.')[0]
-            if package != modname:
-                # it's a submodule
-                if prev_modname == package:
-                    # first submodule - make parent a group head
-                    if entries:
-                        last = entries[-1]
-                        entries[-1] = IndexEntry(last[0], 1, last[2], last[3],
-                                                 last[4], last[5], last[6])
-                elif not prev_modname.startswith(package):
-                    # submodule without parent in list, add dummy entry
-                    entries.append(IndexEntry(stripped + package, 1, '', '', '', '', ''))
-                subtype = 2
-            else:
-                num_toplevels += 1
-                subtype = 0
-
-            qualifier = _('Deprecated') if deprecated else ''
-            entries.append(IndexEntry(stripped + modname, subtype, docname,
-                                      node_id, platforms, qualifier, synopsis))
-            prev_modname = modname
-
-        # apply heuristics when to collapse modindex at page load:
-        # only collapse if number of toplevel modules is larger than
-        # number of submodules
-        collapse = len(modules) - num_toplevels < num_toplevels
-
-        # sort by first letter
-        sorted_content = sorted(content.items())
-
-        return sorted_content, collapse
-
-
-class PythonDomain(Domain):
-    """Python language domain."""
-    name = 'py'
-    label = 'Python'
-    object_types: Dict[str, ObjType] = {
-        'function':     ObjType(_('function'),      'func', 'obj'),
-        'data':         ObjType(_('data'),          'data', 'obj'),
-        'class':        ObjType(_('class'),         'class', 'exc', 'obj'),
-        'exception':    ObjType(_('exception'),     'exc', 'class', 'obj'),
-        'method':       ObjType(_('method'),        'meth', 'obj'),
-        'classmethod':  ObjType(_('class method'),  'meth', 'obj'),
-        'staticmethod': ObjType(_('static method'), 'meth', 'obj'),
-        'attribute':    ObjType(_('attribute'),     'attr', 'obj'),
-        'property':     ObjType(_('property'),      'attr', '_prop', 'obj'),
-        'module':       ObjType(_('module'),        'mod', 'obj'),
-    }
-
-    directives = {
-        'function':        PyFunction,
-        'data':            PyVariable,
-        'class':           PyClasslike,
-        'exception':       PyClasslike,
-        'method':          PyMethod,
-        'classmethod':     PyClassMethod,
-        'staticmethod':    PyStaticMethod,
-        'attribute':       PyAttribute,
-        'property':        PyProperty,
-        'module':          PyModule,
-        'currentmodule':   PyCurrentModule,
-        'decorator':       PyDecoratorFunction,
-        'decoratormethod': PyDecoratorMethod,
-    }
-    roles = {
-        'data':  PyXRefRole(),
-        'exc':   PyXRefRole(),
-        'func':  PyXRefRole(fix_parens=True),
-        'class': PyXRefRole(),
-        'const': PyXRefRole(),
-        'attr':  PyXRefRole(),
-        'meth':  PyXRefRole(fix_parens=True),
-        'mod':   PyXRefRole(),
-        'obj':   PyXRefRole(),
-    }
-    initial_data: Dict[str, Dict[str, Tuple[Any]]] = {
-        'objects': {},  # fullname -> docname, objtype
-        'modules': {},  # modname -> docname, synopsis, platform, deprecated
-    }
-    indices = [
-        PythonModuleIndex,
-    ]
-
-    @property
-    def objects(self) -> Dict[str, ObjectEntry]:
-        return self.data.setdefault('objects', {})  # fullname -> ObjectEntry
-
-    def note_object(self, name: str, objtype: str, node_id: str,
-                    aliased: bool = False, location: Any = None) -> None:
-        """Note a python object for cross reference.
-
-        .. versionadded:: 2.1
-        """
-        if name in self.objects:
-            other = self.objects[name]
-            if other.aliased and aliased is False:
-                # The original definition found. Override it!
-                pass
-            elif other.aliased is False and aliased:
-                # The original definition is already registered.
-                return
-            else:
-                # duplicated
-                logger.warning(__('duplicate object description of %s, '
-                                  'other instance in %s, use :noindex: for one of them'),
-                               name, other.docname, location=location)
-        self.objects[name] = ObjectEntry(self.env.docname, node_id, objtype, aliased)
-
-    @property
-    def modules(self) -> Dict[str, ModuleEntry]:
-        return self.data.setdefault('modules', {})  # modname -> ModuleEntry
-
-    def note_module(self, name: str, node_id: str, synopsis: str,
-                    platform: str, deprecated: bool) -> None:
-        """Note a python module for cross reference.
-
-        .. versionadded:: 2.1
-        """
-        self.modules[name] = ModuleEntry(self.env.docname, node_id,
-                                         synopsis, platform, deprecated)
-
-    def clear_doc(self, docname: str) -> None:
-        for fullname, obj in list(self.objects.items()):
-            if obj.docname == docname:
-                del self.objects[fullname]
-        for modname, mod in list(self.modules.items()):
-            if mod.docname == docname:
-                del self.modules[modname]
-
-    def merge_domaindata(self, docnames: List[str], otherdata: Dict) -> None:
-        # XXX check duplicates?
-        for fullname, obj in otherdata['objects'].items():
-            if obj.docname in docnames:
-                self.objects[fullname] = obj
-        for modname, mod in otherdata['modules'].items():
-            if mod.docname in docnames:
-                self.modules[modname] = mod
-
-    def find_obj(self, env: BuildEnvironment, modname: str, classname: str,
-                 name: str, type: str, searchmode: int = 0
-                 ) -> List[Tuple[str, ObjectEntry]]:
-        """Find a Python object for "name", perhaps using the given module
-        and/or classname.  Returns a list of (name, object entry) tuples.
-        """
-        # skip parens
-        if name[-2:] == '()':
-            name = name[:-2]
-
-        if not name:
-            return []
-
-        matches: List[Tuple[str, ObjectEntry]] = []
-
-        newname = None
-        if searchmode == 1:
-            if type is None:
-                objtypes = list(self.object_types)
-            else:
-                objtypes = self.objtypes_for_role(type)
-            if objtypes is not None:
-                if modname and classname:
-                    fullname = modname + '.' + classname + '.' + name
-                    if fullname in self.objects and self.objects[fullname].objtype in objtypes:
-                        newname = fullname
-                if not newname:
-                    if modname and modname + '.' + name in self.objects and \
-                       self.objects[modname + '.' + name].objtype in objtypes:
-                        newname = modname + '.' + name
-                    elif name in self.objects and self.objects[name].objtype in objtypes:
-                        newname = name
-                    else:
-                        # "fuzzy" searching mode
-                        searchname = '.' + name
-                        matches = [(oname, self.objects[oname]) for oname in self.objects
-                                   if oname.endswith(searchname) and
-                                   self.objects[oname].objtype in objtypes]
-        else:
-            # NOTE: searching for exact match, object type is not considered
-            if name in self.objects:
-                newname = name
-            elif type == 'mod':
-                # only exact matches allowed for modules
-                return []
-            elif classname and classname + '.' + name in self.objects:
-                newname = classname + '.' + name
-            elif modname and modname + '.' + name in self.objects:
-                newname = modname + '.' + name
-            elif modname and classname and \
-                    modname + '.' + classname + '.' + name in self.objects:
-                newname = modname + '.' + classname + '.' + name
-        if newname is not None:
-            matches.append((newname, self.objects[newname]))
-        return matches
-
-    def resolve_xref(self, env: BuildEnvironment, fromdocname: str, builder: Builder,
-                     type: str, target: str, node: pending_xref, contnode: Element
-                     ) -> Optional[Element]:
-        modname = node.get('py:module')
-        clsname = node.get('py:class')
-        searchmode = 1 if node.hasattr('refspecific') else 0
-        matches = self.find_obj(env, modname, clsname, target,
-                                type, searchmode)
-
-        if not matches and type == 'attr':
-            # fallback to meth (for property; Sphinx-2.4.x)
-            # this ensures that `:attr:` role continues to refer to the old property entry
-            # that defined by ``method`` directive in old reST files.
-            matches = self.find_obj(env, modname, clsname, target, 'meth', searchmode)
-        if not matches and type == 'meth':
-            # fallback to attr (for property)
-            # this ensures that `:meth:` in the old reST files can refer to the property
-            # entry that defined by ``property`` directive.
-            #
-            # Note: _prop is a secret role only for internal look-up.
-            matches = self.find_obj(env, modname, clsname, target, '_prop', searchmode)
-
-        if not matches:
-            return None
-        elif len(matches) > 1:
-            canonicals = [m for m in matches if not m[1].aliased]
-            if len(canonicals) == 1:
-                matches = canonicals
-            else:
-                logger.warning(__('more than one target found for cross-reference %r: %s'),
-                               target, ', '.join(match[0] for match in matches),
-                               type='ref', subtype='python', location=node)
-        name, obj = matches[0]
-
-        if obj[2] == 'module':
-            return self._make_module_refnode(builder, fromdocname, name, contnode)
-        else:
-            # determine the content of the reference by conditions
-            content = find_pending_xref_condition(node, 'resolved')
-            if content:
-                children = content.children
-            else:
-                # if not found, use contnode
-                children = [contnode]
-
-            return make_refnode(builder, fromdocname, obj[0], obj[1], children, name)
-
-    def resolve_any_xref(self, env: BuildEnvironment, fromdocname: str, builder: Builder,
-                         target: str, node: pending_xref, contnode: Element
-                         ) -> List[Tuple[str, Element]]:
-        modname = node.get('py:module')
-        clsname = node.get('py:class')
-        results: List[Tuple[str, Element]] = []
-
-        # always search in "refspecific" mode with the :any: role
-        matches = self.find_obj(env, modname, clsname, target, None, 1)
-        for name, obj in matches:
-            if obj[2] == 'module':
-                results.append(('py:mod',
-                                self._make_module_refnode(builder, fromdocname,
-                                                          name, contnode)))
-            else:
-                # determine the content of the reference by conditions
-                content = find_pending_xref_condition(node, 'resolved')
-                if content:
-                    children = content.children
-                else:
-                    # if not found, use contnode
-                    children = [contnode]
-
-                results.append(('py:' + self.role_for_objtype(obj[2]),
-                                make_refnode(builder, fromdocname, obj[0], obj[1],
-                                             children, name)))
-        return results
-
-    def _make_module_refnode(self, builder: Builder, fromdocname: str, name: str,
-                             contnode: Node) -> Element:
-        # get additional info for modules
-        module = self.modules[name]
-        title = name
-        if module.synopsis:
-            title += ': ' + module.synopsis
-        if module.deprecated:
-            title += _(' (deprecated)')
-        if module.platform:
-            title += ' (' + module.platform + ')'
-        return make_refnode(builder, fromdocname, module.docname, module.node_id,
-                            contnode, title)
-
-    def get_objects(self) -> Iterator[Tuple[str, str, str, str, str, int]]:
-        for modname, mod in self.modules.items():
-            yield (modname, modname, 'module', mod.docname, mod.node_id, 0)
-        for refname, obj in self.objects.items():
-            if obj.objtype != 'module':  # modules are already handled
-                if obj.aliased:
-                    # aliased names are not full-text searchable.
-                    yield (refname, refname, obj.objtype, obj.docname, obj.node_id, -1)
-                else:
-                    yield (refname, refname, obj.objtype, obj.docname, obj.node_id, 1)
-
-    def get_full_qualified_name(self, node: Element) -> Optional[str]:
-        modname = node.get('py:module')
-        clsname = node.get('py:class')
-        target = node.get('reftarget')
-        if target is None:
-            return None
-        else:
-            return '.'.join(filter(None, [modname, clsname, target]))
-
-
-def builtin_resolver(app: Sphinx, env: BuildEnvironment,
-                     node: pending_xref, contnode: Element) -> Element:
-    """Do not emit nitpicky warnings for built-in types."""
-    def istyping(s: str) -> bool:
-        if s.startswith('typing.'):
-            s = s.split('.', 1)[1]
-
-        return s in typing.__all__  # type: ignore
-
-    if node.get('refdomain') != 'py':
-        return None
-    elif node.get('reftype') in ('class', 'obj') and node.get('reftarget') == 'None':
-        return contnode
-    elif node.get('reftype') in ('class', 'exc'):
-        reftarget = node.get('reftarget')
-        if inspect.isclass(getattr(builtins, reftarget, None)):
-            # built-in class
-            return contnode
-        elif istyping(reftarget):
-            # typing class
-            return contnode
-
-    return None
-
-
-def setup(app: Sphinx) -> Dict[str, Any]:
-    app.setup_extension('sphinx.directives')
-
-    app.add_domain(PythonDomain)
-    app.add_config_value('python_use_unqualified_type_names', False, 'env')
-    app.connect('object-description-transform', filter_meta_fields)
-    app.connect('missing-reference', builtin_resolver, priority=900)
-
-    return {
-        'version': 'builtin',
-        'env_version': 3,
-        'parallel_read_safe': True,
-        'parallel_write_safe': True,
-    }
+    return params
\ No newline at end of file
diff --git a/tox.ini b/tox.ini
index a363e187f..3b8bc12d0 100644
--- a/tox.ini
+++ b/tox.ini
@@ -27,7 +27,7 @@ setenv =
     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils,ignore::DeprecationWarning:pip._vendor.packaging.version
     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes
 commands=
-    python -X dev -m pytest --durations 25 {posargs}
+    python -X dev -m pytest -rA --durations 25 {posargs}
 
 [testenv:flake8]
 basepython = python3
+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z x ']'
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1='(testbed) '
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=3
+++ CONDA_SHLVL=3
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=4
++ CONDA_SHLVL=4
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_3=/opt/miniconda3
++ CONDA_PREFIX_3=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ python -m pip install -e '.[test]'
Obtaining file:///testbed
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: sphinxcontrib-applehelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (1.0.4)
Requirement already satisfied: sphinxcontrib-devhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (1.0.2)
Requirement already satisfied: sphinxcontrib-jsmath in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (1.0.1)
Requirement already satisfied: sphinxcontrib-htmlhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (2.0.1)
Requirement already satisfied: sphinxcontrib-serializinghtml in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (1.1.5)
Requirement already satisfied: sphinxcontrib-qthelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (1.0.3)
Requirement already satisfied: Jinja2>=2.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (2.11.3)
Requirement already satisfied: Pygments>=2.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (2.19.1)
Requirement already satisfied: docutils<0.18,>=0.14 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (0.17.1)
Requirement already satisfied: snowballstemmer>=1.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (2.2.0)
Requirement already satisfied: babel>=1.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (2.17.0)
Requirement already satisfied: alabaster<0.8,>=0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (0.7.11)
Requirement already satisfied: imagesize in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (1.4.1)
Requirement already satisfied: requests>=2.5.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (2.32.3)
Requirement already satisfied: setuptools in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (75.8.0)
Requirement already satisfied: packaging in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (24.2)
Requirement already satisfied: pytest in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (8.3.4)
Requirement already satisfied: pytest-cov in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (6.0.0)
Requirement already satisfied: html5lib in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (1.1)
Requirement already satisfied: cython in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==4.1.0.dev20250315) (3.0.11)
Requirement already satisfied: MarkupSafe>=0.23 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Jinja2>=2.3->Sphinx==4.1.0.dev20250315) (2.0.1)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==4.1.0.dev20250315) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==4.1.0.dev20250315) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==4.1.0.dev20250315) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==4.1.0.dev20250315) (2025.1.31)
Requirement already satisfied: six>=1.9 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==4.1.0.dev20250315) (1.17.0)
Requirement already satisfied: webencodings in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==4.1.0.dev20250315) (0.5.1)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==4.1.0.dev20250315) (1.2.2)
Requirement already satisfied: iniconfig in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==4.1.0.dev20250315) (2.0.0)
Requirement already satisfied: pluggy<2,>=1.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==4.1.0.dev20250315) (1.5.0)
Requirement already satisfied: tomli>=1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==4.1.0.dev20250315) (2.2.1)
Requirement already satisfied: coverage>=7.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from coverage[toml]>=7.5->pytest-cov->Sphinx==4.1.0.dev20250315) (7.6.10)
Installing collected packages: Sphinx
  Attempting uninstall: Sphinx
    Found existing installation: Sphinx 4.1.0.dev20250204
    Uninstalling Sphinx-4.1.0.dev20250204:
      Successfully uninstalled Sphinx-4.1.0.dev20250204
  DEPRECATION: Legacy editable install of Sphinx[test]==4.1.0.dev20250315 from file:///testbed (setup.py develop) is deprecated. pip 25.1 will enforce this behaviour change. A possible replacement is to add a pyproject.toml or enable --use-pep517, and use setuptools >= 64. If the resulting installation is not behaving as expected, try using --config-settings editable_mode=compat. Please consult the setuptools documentation for more information. Discussion can be found at https://github.com/pypa/pip/issues/11457
  Running setup.py develop for Sphinx
Successfully installed Sphinx
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
+ git checkout 8ec06e9a1bd862cd713b9db748e039ccc7b3e15b tests/test_util_inspect.py
Updated 0 paths from c46f02ca4
+ git apply -v -
Checking patch tests/test_util_inspect.py...
Applied patch tests/test_util_inspect.py cleanly.
+ tox --current-env -epy39 -v -- tests/test_util_inspect.py
py39: commands[0]> python -X dev -m pytest -rA --durations 25 tests/test_util_inspect.py
[1m============================= test session starts ==============================[0m
platform linux -- Python 3.9.21, pytest-8.3.4, pluggy-1.5.0
cachedir: .tox/py39/.pytest_cache
libraries: Sphinx-4.1.0, docutils-0.17.1
base tempdir: /tmp/pytest-of-root/pytest-0
rootdir: /testbed
configfile: setup.cfg
plugins: cov-6.0.0
collected 39 items

tests/test_util_inspect.py [32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[31mE[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[31mF[0m[32m.[0m[31mE[0m[31mE[0m[31mE[0m[31mE[0m[31mE[0m[31mE[0m[31mE[0m[31mE[0m[31mE[0m[32m.[0m[32m.[0m[32m.[0m[31m       [100%][0m

==================================== ERRORS ====================================
[31m[1m______________ ERROR at setup of test_signature_annotations_py38 _______________[0m

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07ba326eb0>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07ba326f00>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
>               mod = import_module(extname)[90m[39;49;00m

[1m[31msphinx/registry.py[0m:426: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31m/opt/miniconda3/envs/testbed/lib/python3.9/importlib/__init__.py[0m:127: in import_module
    [0m[94mreturn[39;49;00m _bootstrap._gcd_import(name[level:], package, level)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    [0m[33m"""[39;49;00m
    [33m    sphinx.domains.javascript[39;49;00m
    [33m    ~~~~~~~~~~~~~~~~~~~~~~~~~[39;49;00m
    [33m[39;49;00m
    [33m    The JavaScript domain.[39;49;00m
    [33m[39;49;00m
    [33m    :copyright: Copyright 2007-2021 by the Sphinx team, see AUTHORS.[39;49;00m
    [33m    :license: BSD, see LICENSE for details.[39;49;00m
    [33m"""[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mtyping[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Any, Dict, Iterator, List, Optional, Tuple, cast[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[90m [39;49;00m[94mimport[39;49;00m nodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Element, Node[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mparsers[39;49;00m[04m[96m.[39;49;00m[04m[96mrst[39;49;00m[90m [39;49;00m[94mimport[39;49;00m directives[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[90m [39;49;00m[94mimport[39;49;00m addnodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96maddnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m desc_signature, pending_xref[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mapplication[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Sphinx[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mbuilders[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Builder[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdirectives[39;49;00m[90m [39;49;00m[94mimport[39;49;00m ObjectDescription[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Domain, ObjType[90m[39;49;00m
>   [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[04m[96m.[39;49;00m[04m[96mpython[39;49;00m[90m [39;49;00m[94mimport[39;49;00m _pseudo_parse_arglist[90m[39;49;00m
[1m[31mE   ImportError: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py)[0m

[1m[31msphinx/domains/javascript.py[0m:23: ImportError

[33mThe above exception was the direct cause of the following exception:[0m

test_params = {'shared_result': None}
app_params = app_params(args=[], kwargs={'srcdir': path('/tmp/pytest-of-root/pytest-0/ext-autodoc')})
make_app = <function make_app.<locals>.make at 0x7c07ba91ec30>
shared_result = <sphinx.testing.fixtures.SharedResult object at 0x7c07ba326b40>

    [0m[37m@pytest[39;49;00m.fixture(scope=[33m'[39;49;00m[33mfunction[39;49;00m[33m'[39;49;00m)[90m[39;49;00m
    [94mdef[39;49;00m[90m [39;49;00m[92mapp[39;49;00m(test_params: Dict, app_params: Tuple[Dict, Dict], make_app: Callable,[90m[39;49;00m
            shared_result: SharedResult) -> Generator[SphinxTestApp, [94mNone[39;49;00m, [94mNone[39;49;00m]:[90m[39;49;00m
    [90m    [39;49;00m[33m"""[39;49;00m
    [33m    provides sphinx.application.Sphinx object[39;49;00m
    [33m    """[39;49;00m[90m[39;49;00m
        args, kwargs = app_params[90m[39;49;00m
>       app_ = make_app(*args, **kwargs)[90m[39;49;00m

[1m[31msphinx/testing/fixtures.py[0m:147: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31msphinx/testing/fixtures.py[0m:193: in make
    [0mapp_: Any = SphinxTestApp(*args, **kwargs)[90m[39;49;00m
[1m[31msphinx/testing/util.py[0m:130: in __init__
    [0m[96msuper[39;49;00m().[92m__init__[39;49;00m(srcdir, confdir, outdir, doctreedir,[90m[39;49;00m
[1m[31msphinx/application.py[0m:233: in __init__
    [0m[96mself[39;49;00m.setup_extension(extension)[90m[39;49;00m
[1m[31msphinx/application.py[0m:393: in setup_extension
    [0m[96mself[39;49;00m.registry.load_extension([96mself[39;49;00m, extname)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07ba326eb0>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07ba326f00>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
                mod = import_module(extname)[90m[39;49;00m
            [94mexcept[39;49;00m [96mImportError[39;49;00m [94mas[39;49;00m err:[90m[39;49;00m
                logger.verbose(__([33m'[39;49;00m[33mOriginal exception:[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m) + traceback.format_exc())[90m[39;49;00m
>               [94mraise[39;49;00m ExtensionError(__([33m'[39;49;00m[33mCould not import extension [39;49;00m[33m%s[39;49;00m[33m'[39;49;00m) % extname,[90m[39;49;00m
                                     err) [94mfrom[39;49;00m[90m [39;49;00m[04m[96merr[39;49;00m[90m[39;49;00m
[1m[31mE               sphinx.errors.ExtensionError: Could not import extension sphinx.domains.javascript (exception: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py))[0m

[1m[31msphinx/registry.py[0m:429: ExtensionError
[31m[1m_____________________ ERROR at setup of test_isclassmethod _____________________[0m

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9776410>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b978a1e0>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
>               mod = import_module(extname)[90m[39;49;00m

[1m[31msphinx/registry.py[0m:426: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31m/opt/miniconda3/envs/testbed/lib/python3.9/importlib/__init__.py[0m:127: in import_module
    [0m[94mreturn[39;49;00m _bootstrap._gcd_import(name[level:], package, level)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    [0m[33m"""[39;49;00m
    [33m    sphinx.domains.javascript[39;49;00m
    [33m    ~~~~~~~~~~~~~~~~~~~~~~~~~[39;49;00m
    [33m[39;49;00m
    [33m    The JavaScript domain.[39;49;00m
    [33m[39;49;00m
    [33m    :copyright: Copyright 2007-2021 by the Sphinx team, see AUTHORS.[39;49;00m
    [33m    :license: BSD, see LICENSE for details.[39;49;00m
    [33m"""[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mtyping[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Any, Dict, Iterator, List, Optional, Tuple, cast[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[90m [39;49;00m[94mimport[39;49;00m nodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Element, Node[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mparsers[39;49;00m[04m[96m.[39;49;00m[04m[96mrst[39;49;00m[90m [39;49;00m[94mimport[39;49;00m directives[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[90m [39;49;00m[94mimport[39;49;00m addnodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96maddnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m desc_signature, pending_xref[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mapplication[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Sphinx[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mbuilders[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Builder[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdirectives[39;49;00m[90m [39;49;00m[94mimport[39;49;00m ObjectDescription[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Domain, ObjType[90m[39;49;00m
>   [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[04m[96m.[39;49;00m[04m[96mpython[39;49;00m[90m [39;49;00m[94mimport[39;49;00m _pseudo_parse_arglist[90m[39;49;00m
[1m[31mE   ImportError: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py)[0m

[1m[31msphinx/domains/javascript.py[0m:23: ImportError

[33mThe above exception was the direct cause of the following exception:[0m

test_params = {'shared_result': None}
app_params = app_params(args=[], kwargs={'srcdir': path('/tmp/pytest-of-root/pytest-0/ext-autodoc')})
make_app = <function make_app.<locals>.make at 0x7c07b9717eb0>
shared_result = <sphinx.testing.fixtures.SharedResult object at 0x7c07b978a230>

    [0m[37m@pytest[39;49;00m.fixture(scope=[33m'[39;49;00m[33mfunction[39;49;00m[33m'[39;49;00m)[90m[39;49;00m
    [94mdef[39;49;00m[90m [39;49;00m[92mapp[39;49;00m(test_params: Dict, app_params: Tuple[Dict, Dict], make_app: Callable,[90m[39;49;00m
            shared_result: SharedResult) -> Generator[SphinxTestApp, [94mNone[39;49;00m, [94mNone[39;49;00m]:[90m[39;49;00m
    [90m    [39;49;00m[33m"""[39;49;00m
    [33m    provides sphinx.application.Sphinx object[39;49;00m
    [33m    """[39;49;00m[90m[39;49;00m
        args, kwargs = app_params[90m[39;49;00m
>       app_ = make_app(*args, **kwargs)[90m[39;49;00m

[1m[31msphinx/testing/fixtures.py[0m:147: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31msphinx/testing/fixtures.py[0m:193: in make
    [0mapp_: Any = SphinxTestApp(*args, **kwargs)[90m[39;49;00m
[1m[31msphinx/testing/util.py[0m:130: in __init__
    [0m[96msuper[39;49;00m().[92m__init__[39;49;00m(srcdir, confdir, outdir, doctreedir,[90m[39;49;00m
[1m[31msphinx/application.py[0m:233: in __init__
    [0m[96mself[39;49;00m.setup_extension(extension)[90m[39;49;00m
[1m[31msphinx/application.py[0m:393: in setup_extension
    [0m[96mself[39;49;00m.registry.load_extension([96mself[39;49;00m, extname)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9776410>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b978a1e0>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
                mod = import_module(extname)[90m[39;49;00m
            [94mexcept[39;49;00m [96mImportError[39;49;00m [94mas[39;49;00m err:[90m[39;49;00m
                logger.verbose(__([33m'[39;49;00m[33mOriginal exception:[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m) + traceback.format_exc())[90m[39;49;00m
>               [94mraise[39;49;00m ExtensionError(__([33m'[39;49;00m[33mCould not import extension [39;49;00m[33m%s[39;49;00m[33m'[39;49;00m) % extname,[90m[39;49;00m
                                     err) [94mfrom[39;49;00m[90m [39;49;00m[04m[96merr[39;49;00m[90m[39;49;00m
[1m[31mE               sphinx.errors.ExtensionError: Could not import extension sphinx.domains.javascript (exception: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py))[0m

[1m[31msphinx/registry.py[0m:429: ExtensionError
[31m[1m____________________ ERROR at setup of test_isstaticmethod _____________________[0m

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9c85190>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9c85f50>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
>               mod = import_module(extname)[90m[39;49;00m

[1m[31msphinx/registry.py[0m:426: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31m/opt/miniconda3/envs/testbed/lib/python3.9/importlib/__init__.py[0m:127: in import_module
    [0m[94mreturn[39;49;00m _bootstrap._gcd_import(name[level:], package, level)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    [0m[33m"""[39;49;00m
    [33m    sphinx.domains.javascript[39;49;00m
    [33m    ~~~~~~~~~~~~~~~~~~~~~~~~~[39;49;00m
    [33m[39;49;00m
    [33m    The JavaScript domain.[39;49;00m
    [33m[39;49;00m
    [33m    :copyright: Copyright 2007-2021 by the Sphinx team, see AUTHORS.[39;49;00m
    [33m    :license: BSD, see LICENSE for details.[39;49;00m
    [33m"""[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mtyping[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Any, Dict, Iterator, List, Optional, Tuple, cast[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[90m [39;49;00m[94mimport[39;49;00m nodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Element, Node[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mparsers[39;49;00m[04m[96m.[39;49;00m[04m[96mrst[39;49;00m[90m [39;49;00m[94mimport[39;49;00m directives[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[90m [39;49;00m[94mimport[39;49;00m addnodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96maddnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m desc_signature, pending_xref[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mapplication[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Sphinx[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mbuilders[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Builder[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdirectives[39;49;00m[90m [39;49;00m[94mimport[39;49;00m ObjectDescription[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Domain, ObjType[90m[39;49;00m
>   [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[04m[96m.[39;49;00m[04m[96mpython[39;49;00m[90m [39;49;00m[94mimport[39;49;00m _pseudo_parse_arglist[90m[39;49;00m
[1m[31mE   ImportError: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py)[0m

[1m[31msphinx/domains/javascript.py[0m:23: ImportError

[33mThe above exception was the direct cause of the following exception:[0m

test_params = {'shared_result': None}
app_params = app_params(args=[], kwargs={'srcdir': path('/tmp/pytest-of-root/pytest-0/ext-autodoc')})
make_app = <function make_app.<locals>.make at 0x7c07b9a5f7d0>
shared_result = <sphinx.testing.fixtures.SharedResult object at 0x7c07b9c85c30>

    [0m[37m@pytest[39;49;00m.fixture(scope=[33m'[39;49;00m[33mfunction[39;49;00m[33m'[39;49;00m)[90m[39;49;00m
    [94mdef[39;49;00m[90m [39;49;00m[92mapp[39;49;00m(test_params: Dict, app_params: Tuple[Dict, Dict], make_app: Callable,[90m[39;49;00m
            shared_result: SharedResult) -> Generator[SphinxTestApp, [94mNone[39;49;00m, [94mNone[39;49;00m]:[90m[39;49;00m
    [90m    [39;49;00m[33m"""[39;49;00m
    [33m    provides sphinx.application.Sphinx object[39;49;00m
    [33m    """[39;49;00m[90m[39;49;00m
        args, kwargs = app_params[90m[39;49;00m
>       app_ = make_app(*args, **kwargs)[90m[39;49;00m

[1m[31msphinx/testing/fixtures.py[0m:147: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31msphinx/testing/fixtures.py[0m:193: in make
    [0mapp_: Any = SphinxTestApp(*args, **kwargs)[90m[39;49;00m
[1m[31msphinx/testing/util.py[0m:130: in __init__
    [0m[96msuper[39;49;00m().[92m__init__[39;49;00m(srcdir, confdir, outdir, doctreedir,[90m[39;49;00m
[1m[31msphinx/application.py[0m:233: in __init__
    [0m[96mself[39;49;00m.setup_extension(extension)[90m[39;49;00m
[1m[31msphinx/application.py[0m:393: in setup_extension
    [0m[96mself[39;49;00m.registry.load_extension([96mself[39;49;00m, extname)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9c85190>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9c85f50>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
                mod = import_module(extname)[90m[39;49;00m
            [94mexcept[39;49;00m [96mImportError[39;49;00m [94mas[39;49;00m err:[90m[39;49;00m
                logger.verbose(__([33m'[39;49;00m[33mOriginal exception:[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m) + traceback.format_exc())[90m[39;49;00m
>               [94mraise[39;49;00m ExtensionError(__([33m'[39;49;00m[33mCould not import extension [39;49;00m[33m%s[39;49;00m[33m'[39;49;00m) % extname,[90m[39;49;00m
                                     err) [94mfrom[39;49;00m[90m [39;49;00m[04m[96merr[39;49;00m[90m[39;49;00m
[1m[31mE               sphinx.errors.ExtensionError: Could not import extension sphinx.domains.javascript (exception: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py))[0m

[1m[31msphinx/registry.py[0m:429: ExtensionError
[31m[1m__________________ ERROR at setup of test_iscoroutinefunction __________________[0m

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9c4fcd0>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9c4f960>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
>               mod = import_module(extname)[90m[39;49;00m

[1m[31msphinx/registry.py[0m:426: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31m/opt/miniconda3/envs/testbed/lib/python3.9/importlib/__init__.py[0m:127: in import_module
    [0m[94mreturn[39;49;00m _bootstrap._gcd_import(name[level:], package, level)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    [0m[33m"""[39;49;00m
    [33m    sphinx.domains.javascript[39;49;00m
    [33m    ~~~~~~~~~~~~~~~~~~~~~~~~~[39;49;00m
    [33m[39;49;00m
    [33m    The JavaScript domain.[39;49;00m
    [33m[39;49;00m
    [33m    :copyright: Copyright 2007-2021 by the Sphinx team, see AUTHORS.[39;49;00m
    [33m    :license: BSD, see LICENSE for details.[39;49;00m
    [33m"""[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mtyping[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Any, Dict, Iterator, List, Optional, Tuple, cast[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[90m [39;49;00m[94mimport[39;49;00m nodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Element, Node[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mparsers[39;49;00m[04m[96m.[39;49;00m[04m[96mrst[39;49;00m[90m [39;49;00m[94mimport[39;49;00m directives[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[90m [39;49;00m[94mimport[39;49;00m addnodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96maddnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m desc_signature, pending_xref[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mapplication[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Sphinx[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mbuilders[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Builder[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdirectives[39;49;00m[90m [39;49;00m[94mimport[39;49;00m ObjectDescription[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Domain, ObjType[90m[39;49;00m
>   [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[04m[96m.[39;49;00m[04m[96mpython[39;49;00m[90m [39;49;00m[94mimport[39;49;00m _pseudo_parse_arglist[90m[39;49;00m
[1m[31mE   ImportError: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py)[0m

[1m[31msphinx/domains/javascript.py[0m:23: ImportError

[33mThe above exception was the direct cause of the following exception:[0m

test_params = {'shared_result': None}
app_params = app_params(args=[], kwargs={'srcdir': path('/tmp/pytest-of-root/pytest-0/ext-autodoc')})
make_app = <function make_app.<locals>.make at 0x7c07b97890f0>
shared_result = <sphinx.testing.fixtures.SharedResult object at 0x7c07b9c4f2d0>

    [0m[37m@pytest[39;49;00m.fixture(scope=[33m'[39;49;00m[33mfunction[39;49;00m[33m'[39;49;00m)[90m[39;49;00m
    [94mdef[39;49;00m[90m [39;49;00m[92mapp[39;49;00m(test_params: Dict, app_params: Tuple[Dict, Dict], make_app: Callable,[90m[39;49;00m
            shared_result: SharedResult) -> Generator[SphinxTestApp, [94mNone[39;49;00m, [94mNone[39;49;00m]:[90m[39;49;00m
    [90m    [39;49;00m[33m"""[39;49;00m
    [33m    provides sphinx.application.Sphinx object[39;49;00m
    [33m    """[39;49;00m[90m[39;49;00m
        args, kwargs = app_params[90m[39;49;00m
>       app_ = make_app(*args, **kwargs)[90m[39;49;00m

[1m[31msphinx/testing/fixtures.py[0m:147: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31msphinx/testing/fixtures.py[0m:193: in make
    [0mapp_: Any = SphinxTestApp(*args, **kwargs)[90m[39;49;00m
[1m[31msphinx/testing/util.py[0m:130: in __init__
    [0m[96msuper[39;49;00m().[92m__init__[39;49;00m(srcdir, confdir, outdir, doctreedir,[90m[39;49;00m
[1m[31msphinx/application.py[0m:233: in __init__
    [0m[96mself[39;49;00m.setup_extension(extension)[90m[39;49;00m
[1m[31msphinx/application.py[0m:393: in setup_extension
    [0m[96mself[39;49;00m.registry.load_extension([96mself[39;49;00m, extname)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9c4fcd0>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9c4f960>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
                mod = import_module(extname)[90m[39;49;00m
            [94mexcept[39;49;00m [96mImportError[39;49;00m [94mas[39;49;00m err:[90m[39;49;00m
                logger.verbose(__([33m'[39;49;00m[33mOriginal exception:[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m) + traceback.format_exc())[90m[39;49;00m
>               [94mraise[39;49;00m ExtensionError(__([33m'[39;49;00m[33mCould not import extension [39;49;00m[33m%s[39;49;00m[33m'[39;49;00m) % extname,[90m[39;49;00m
                                     err) [94mfrom[39;49;00m[90m [39;49;00m[04m[96merr[39;49;00m[90m[39;49;00m
[1m[31mE               sphinx.errors.ExtensionError: Could not import extension sphinx.domains.javascript (exception: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py))[0m

[1m[31msphinx/registry.py[0m:429: ExtensionError
[31m[1m______________________ ERROR at setup of test_isfunction _______________________[0m

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9a54280>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9a54960>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
>               mod = import_module(extname)[90m[39;49;00m

[1m[31msphinx/registry.py[0m:426: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31m/opt/miniconda3/envs/testbed/lib/python3.9/importlib/__init__.py[0m:127: in import_module
    [0m[94mreturn[39;49;00m _bootstrap._gcd_import(name[level:], package, level)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    [0m[33m"""[39;49;00m
    [33m    sphinx.domains.javascript[39;49;00m
    [33m    ~~~~~~~~~~~~~~~~~~~~~~~~~[39;49;00m
    [33m[39;49;00m
    [33m    The JavaScript domain.[39;49;00m
    [33m[39;49;00m
    [33m    :copyright: Copyright 2007-2021 by the Sphinx team, see AUTHORS.[39;49;00m
    [33m    :license: BSD, see LICENSE for details.[39;49;00m
    [33m"""[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mtyping[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Any, Dict, Iterator, List, Optional, Tuple, cast[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[90m [39;49;00m[94mimport[39;49;00m nodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Element, Node[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mparsers[39;49;00m[04m[96m.[39;49;00m[04m[96mrst[39;49;00m[90m [39;49;00m[94mimport[39;49;00m directives[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[90m [39;49;00m[94mimport[39;49;00m addnodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96maddnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m desc_signature, pending_xref[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mapplication[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Sphinx[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mbuilders[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Builder[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdirectives[39;49;00m[90m [39;49;00m[94mimport[39;49;00m ObjectDescription[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Domain, ObjType[90m[39;49;00m
>   [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[04m[96m.[39;49;00m[04m[96mpython[39;49;00m[90m [39;49;00m[94mimport[39;49;00m _pseudo_parse_arglist[90m[39;49;00m
[1m[31mE   ImportError: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py)[0m

[1m[31msphinx/domains/javascript.py[0m:23: ImportError

[33mThe above exception was the direct cause of the following exception:[0m

test_params = {'shared_result': None}
app_params = app_params(args=[], kwargs={'srcdir': path('/tmp/pytest-of-root/pytest-0/ext-autodoc')})
make_app = <function make_app.<locals>.make at 0x7c07b981eaf0>
shared_result = <sphinx.testing.fixtures.SharedResult object at 0x7c07b9a54730>

    [0m[37m@pytest[39;49;00m.fixture(scope=[33m'[39;49;00m[33mfunction[39;49;00m[33m'[39;49;00m)[90m[39;49;00m
    [94mdef[39;49;00m[90m [39;49;00m[92mapp[39;49;00m(test_params: Dict, app_params: Tuple[Dict, Dict], make_app: Callable,[90m[39;49;00m
            shared_result: SharedResult) -> Generator[SphinxTestApp, [94mNone[39;49;00m, [94mNone[39;49;00m]:[90m[39;49;00m
    [90m    [39;49;00m[33m"""[39;49;00m
    [33m    provides sphinx.application.Sphinx object[39;49;00m
    [33m    """[39;49;00m[90m[39;49;00m
        args, kwargs = app_params[90m[39;49;00m
>       app_ = make_app(*args, **kwargs)[90m[39;49;00m

[1m[31msphinx/testing/fixtures.py[0m:147: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31msphinx/testing/fixtures.py[0m:193: in make
    [0mapp_: Any = SphinxTestApp(*args, **kwargs)[90m[39;49;00m
[1m[31msphinx/testing/util.py[0m:130: in __init__
    [0m[96msuper[39;49;00m().[92m__init__[39;49;00m(srcdir, confdir, outdir, doctreedir,[90m[39;49;00m
[1m[31msphinx/application.py[0m:233: in __init__
    [0m[96mself[39;49;00m.setup_extension(extension)[90m[39;49;00m
[1m[31msphinx/application.py[0m:393: in setup_extension
    [0m[96mself[39;49;00m.registry.load_extension([96mself[39;49;00m, extname)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9a54280>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9a54960>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
                mod = import_module(extname)[90m[39;49;00m
            [94mexcept[39;49;00m [96mImportError[39;49;00m [94mas[39;49;00m err:[90m[39;49;00m
                logger.verbose(__([33m'[39;49;00m[33mOriginal exception:[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m) + traceback.format_exc())[90m[39;49;00m
>               [94mraise[39;49;00m ExtensionError(__([33m'[39;49;00m[33mCould not import extension [39;49;00m[33m%s[39;49;00m[33m'[39;49;00m) % extname,[90m[39;49;00m
                                     err) [94mfrom[39;49;00m[90m [39;49;00m[04m[96merr[39;49;00m[90m[39;49;00m
[1m[31mE               sphinx.errors.ExtensionError: Could not import extension sphinx.domains.javascript (exception: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py))[0m

[1m[31msphinx/registry.py[0m:429: ExtensionError
[31m[1m_______________________ ERROR at setup of test_isbuiltin _______________________[0m

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b97e0640>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b97e00f0>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
>               mod = import_module(extname)[90m[39;49;00m

[1m[31msphinx/registry.py[0m:426: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31m/opt/miniconda3/envs/testbed/lib/python3.9/importlib/__init__.py[0m:127: in import_module
    [0m[94mreturn[39;49;00m _bootstrap._gcd_import(name[level:], package, level)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    [0m[33m"""[39;49;00m
    [33m    sphinx.domains.javascript[39;49;00m
    [33m    ~~~~~~~~~~~~~~~~~~~~~~~~~[39;49;00m
    [33m[39;49;00m
    [33m    The JavaScript domain.[39;49;00m
    [33m[39;49;00m
    [33m    :copyright: Copyright 2007-2021 by the Sphinx team, see AUTHORS.[39;49;00m
    [33m    :license: BSD, see LICENSE for details.[39;49;00m
    [33m"""[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mtyping[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Any, Dict, Iterator, List, Optional, Tuple, cast[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[90m [39;49;00m[94mimport[39;49;00m nodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Element, Node[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mparsers[39;49;00m[04m[96m.[39;49;00m[04m[96mrst[39;49;00m[90m [39;49;00m[94mimport[39;49;00m directives[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[90m [39;49;00m[94mimport[39;49;00m addnodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96maddnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m desc_signature, pending_xref[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mapplication[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Sphinx[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mbuilders[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Builder[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdirectives[39;49;00m[90m [39;49;00m[94mimport[39;49;00m ObjectDescription[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Domain, ObjType[90m[39;49;00m
>   [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[04m[96m.[39;49;00m[04m[96mpython[39;49;00m[90m [39;49;00m[94mimport[39;49;00m _pseudo_parse_arglist[90m[39;49;00m
[1m[31mE   ImportError: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py)[0m

[1m[31msphinx/domains/javascript.py[0m:23: ImportError

[33mThe above exception was the direct cause of the following exception:[0m

test_params = {'shared_result': None}
app_params = app_params(args=[], kwargs={'srcdir': path('/tmp/pytest-of-root/pytest-0/ext-autodoc')})
make_app = <function make_app.<locals>.make at 0x7c07b9cd44b0>
shared_result = <sphinx.testing.fixtures.SharedResult object at 0x7c07b97e0910>

    [0m[37m@pytest[39;49;00m.fixture(scope=[33m'[39;49;00m[33mfunction[39;49;00m[33m'[39;49;00m)[90m[39;49;00m
    [94mdef[39;49;00m[90m [39;49;00m[92mapp[39;49;00m(test_params: Dict, app_params: Tuple[Dict, Dict], make_app: Callable,[90m[39;49;00m
            shared_result: SharedResult) -> Generator[SphinxTestApp, [94mNone[39;49;00m, [94mNone[39;49;00m]:[90m[39;49;00m
    [90m    [39;49;00m[33m"""[39;49;00m
    [33m    provides sphinx.application.Sphinx object[39;49;00m
    [33m    """[39;49;00m[90m[39;49;00m
        args, kwargs = app_params[90m[39;49;00m
>       app_ = make_app(*args, **kwargs)[90m[39;49;00m

[1m[31msphinx/testing/fixtures.py[0m:147: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31msphinx/testing/fixtures.py[0m:193: in make
    [0mapp_: Any = SphinxTestApp(*args, **kwargs)[90m[39;49;00m
[1m[31msphinx/testing/util.py[0m:130: in __init__
    [0m[96msuper[39;49;00m().[92m__init__[39;49;00m(srcdir, confdir, outdir, doctreedir,[90m[39;49;00m
[1m[31msphinx/application.py[0m:233: in __init__
    [0m[96mself[39;49;00m.setup_extension(extension)[90m[39;49;00m
[1m[31msphinx/application.py[0m:393: in setup_extension
    [0m[96mself[39;49;00m.registry.load_extension([96mself[39;49;00m, extname)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b97e0640>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b97e00f0>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
                mod = import_module(extname)[90m[39;49;00m
            [94mexcept[39;49;00m [96mImportError[39;49;00m [94mas[39;49;00m err:[90m[39;49;00m
                logger.verbose(__([33m'[39;49;00m[33mOriginal exception:[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m) + traceback.format_exc())[90m[39;49;00m
>               [94mraise[39;49;00m ExtensionError(__([33m'[39;49;00m[33mCould not import extension [39;49;00m[33m%s[39;49;00m[33m'[39;49;00m) % extname,[90m[39;49;00m
                                     err) [94mfrom[39;49;00m[90m [39;49;00m[04m[96merr[39;49;00m[90m[39;49;00m
[1m[31mE               sphinx.errors.ExtensionError: Could not import extension sphinx.domains.javascript (exception: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py))[0m

[1m[31msphinx/registry.py[0m:429: ExtensionError
[31m[1m_____________________ ERROR at setup of test_isdescriptor ______________________[0m

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9c83e10>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9dcd690>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
>               mod = import_module(extname)[90m[39;49;00m

[1m[31msphinx/registry.py[0m:426: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31m/opt/miniconda3/envs/testbed/lib/python3.9/importlib/__init__.py[0m:127: in import_module
    [0m[94mreturn[39;49;00m _bootstrap._gcd_import(name[level:], package, level)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    [0m[33m"""[39;49;00m
    [33m    sphinx.domains.javascript[39;49;00m
    [33m    ~~~~~~~~~~~~~~~~~~~~~~~~~[39;49;00m
    [33m[39;49;00m
    [33m    The JavaScript domain.[39;49;00m
    [33m[39;49;00m
    [33m    :copyright: Copyright 2007-2021 by the Sphinx team, see AUTHORS.[39;49;00m
    [33m    :license: BSD, see LICENSE for details.[39;49;00m
    [33m"""[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mtyping[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Any, Dict, Iterator, List, Optional, Tuple, cast[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[90m [39;49;00m[94mimport[39;49;00m nodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Element, Node[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mparsers[39;49;00m[04m[96m.[39;49;00m[04m[96mrst[39;49;00m[90m [39;49;00m[94mimport[39;49;00m directives[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[90m [39;49;00m[94mimport[39;49;00m addnodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96maddnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m desc_signature, pending_xref[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mapplication[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Sphinx[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mbuilders[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Builder[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdirectives[39;49;00m[90m [39;49;00m[94mimport[39;49;00m ObjectDescription[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Domain, ObjType[90m[39;49;00m
>   [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[04m[96m.[39;49;00m[04m[96mpython[39;49;00m[90m [39;49;00m[94mimport[39;49;00m _pseudo_parse_arglist[90m[39;49;00m
[1m[31mE   ImportError: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py)[0m

[1m[31msphinx/domains/javascript.py[0m:23: ImportError

[33mThe above exception was the direct cause of the following exception:[0m

test_params = {'shared_result': None}
app_params = app_params(args=[], kwargs={'srcdir': path('/tmp/pytest-of-root/pytest-0/ext-autodoc')})
make_app = <function make_app.<locals>.make at 0x7c07b9dc9410>
shared_result = <sphinx.testing.fixtures.SharedResult object at 0x7c07b9dcd1e0>

    [0m[37m@pytest[39;49;00m.fixture(scope=[33m'[39;49;00m[33mfunction[39;49;00m[33m'[39;49;00m)[90m[39;49;00m
    [94mdef[39;49;00m[90m [39;49;00m[92mapp[39;49;00m(test_params: Dict, app_params: Tuple[Dict, Dict], make_app: Callable,[90m[39;49;00m
            shared_result: SharedResult) -> Generator[SphinxTestApp, [94mNone[39;49;00m, [94mNone[39;49;00m]:[90m[39;49;00m
    [90m    [39;49;00m[33m"""[39;49;00m
    [33m    provides sphinx.application.Sphinx object[39;49;00m
    [33m    """[39;49;00m[90m[39;49;00m
        args, kwargs = app_params[90m[39;49;00m
>       app_ = make_app(*args, **kwargs)[90m[39;49;00m

[1m[31msphinx/testing/fixtures.py[0m:147: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31msphinx/testing/fixtures.py[0m:193: in make
    [0mapp_: Any = SphinxTestApp(*args, **kwargs)[90m[39;49;00m
[1m[31msphinx/testing/util.py[0m:130: in __init__
    [0m[96msuper[39;49;00m().[92m__init__[39;49;00m(srcdir, confdir, outdir, doctreedir,[90m[39;49;00m
[1m[31msphinx/application.py[0m:233: in __init__
    [0m[96mself[39;49;00m.setup_extension(extension)[90m[39;49;00m
[1m[31msphinx/application.py[0m:393: in setup_extension
    [0m[96mself[39;49;00m.registry.load_extension([96mself[39;49;00m, extname)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9c83e10>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9dcd690>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
                mod = import_module(extname)[90m[39;49;00m
            [94mexcept[39;49;00m [96mImportError[39;49;00m [94mas[39;49;00m err:[90m[39;49;00m
                logger.verbose(__([33m'[39;49;00m[33mOriginal exception:[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m) + traceback.format_exc())[90m[39;49;00m
>               [94mraise[39;49;00m ExtensionError(__([33m'[39;49;00m[33mCould not import extension [39;49;00m[33m%s[39;49;00m[33m'[39;49;00m) % extname,[90m[39;49;00m
                                     err) [94mfrom[39;49;00m[90m [39;49;00m[04m[96merr[39;49;00m[90m[39;49;00m
[1m[31mE               sphinx.errors.ExtensionError: Could not import extension sphinx.domains.javascript (exception: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py))[0m

[1m[31msphinx/registry.py[0m:429: ExtensionError
[31m[1m_________________ ERROR at setup of test_isattributedescriptor _________________[0m

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9caa2d0>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9de4c30>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
>               mod = import_module(extname)[90m[39;49;00m

[1m[31msphinx/registry.py[0m:426: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31m/opt/miniconda3/envs/testbed/lib/python3.9/importlib/__init__.py[0m:127: in import_module
    [0m[94mreturn[39;49;00m _bootstrap._gcd_import(name[level:], package, level)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    [0m[33m"""[39;49;00m
    [33m    sphinx.domains.javascript[39;49;00m
    [33m    ~~~~~~~~~~~~~~~~~~~~~~~~~[39;49;00m
    [33m[39;49;00m
    [33m    The JavaScript domain.[39;49;00m
    [33m[39;49;00m
    [33m    :copyright: Copyright 2007-2021 by the Sphinx team, see AUTHORS.[39;49;00m
    [33m    :license: BSD, see LICENSE for details.[39;49;00m
    [33m"""[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mtyping[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Any, Dict, Iterator, List, Optional, Tuple, cast[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[90m [39;49;00m[94mimport[39;49;00m nodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Element, Node[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mparsers[39;49;00m[04m[96m.[39;49;00m[04m[96mrst[39;49;00m[90m [39;49;00m[94mimport[39;49;00m directives[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[90m [39;49;00m[94mimport[39;49;00m addnodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96maddnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m desc_signature, pending_xref[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mapplication[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Sphinx[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mbuilders[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Builder[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdirectives[39;49;00m[90m [39;49;00m[94mimport[39;49;00m ObjectDescription[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Domain, ObjType[90m[39;49;00m
>   [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[04m[96m.[39;49;00m[04m[96mpython[39;49;00m[90m [39;49;00m[94mimport[39;49;00m _pseudo_parse_arglist[90m[39;49;00m
[1m[31mE   ImportError: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py)[0m

[1m[31msphinx/domains/javascript.py[0m:23: ImportError

[33mThe above exception was the direct cause of the following exception:[0m

test_params = {'shared_result': None}
app_params = app_params(args=[], kwargs={'srcdir': path('/tmp/pytest-of-root/pytest-0/ext-autodoc')})
make_app = <function make_app.<locals>.make at 0x7c07ba91ec30>
shared_result = <sphinx.testing.fixtures.SharedResult object at 0x7c07b9de45a0>

    [0m[37m@pytest[39;49;00m.fixture(scope=[33m'[39;49;00m[33mfunction[39;49;00m[33m'[39;49;00m)[90m[39;49;00m
    [94mdef[39;49;00m[90m [39;49;00m[92mapp[39;49;00m(test_params: Dict, app_params: Tuple[Dict, Dict], make_app: Callable,[90m[39;49;00m
            shared_result: SharedResult) -> Generator[SphinxTestApp, [94mNone[39;49;00m, [94mNone[39;49;00m]:[90m[39;49;00m
    [90m    [39;49;00m[33m"""[39;49;00m
    [33m    provides sphinx.application.Sphinx object[39;49;00m
    [33m    """[39;49;00m[90m[39;49;00m
        args, kwargs = app_params[90m[39;49;00m
>       app_ = make_app(*args, **kwargs)[90m[39;49;00m

[1m[31msphinx/testing/fixtures.py[0m:147: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31msphinx/testing/fixtures.py[0m:193: in make
    [0mapp_: Any = SphinxTestApp(*args, **kwargs)[90m[39;49;00m
[1m[31msphinx/testing/util.py[0m:130: in __init__
    [0m[96msuper[39;49;00m().[92m__init__[39;49;00m(srcdir, confdir, outdir, doctreedir,[90m[39;49;00m
[1m[31msphinx/application.py[0m:233: in __init__
    [0m[96mself[39;49;00m.setup_extension(extension)[90m[39;49;00m
[1m[31msphinx/application.py[0m:393: in setup_extension
    [0m[96mself[39;49;00m.registry.load_extension([96mself[39;49;00m, extname)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9caa2d0>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9de4c30>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
                mod = import_module(extname)[90m[39;49;00m
            [94mexcept[39;49;00m [96mImportError[39;49;00m [94mas[39;49;00m err:[90m[39;49;00m
                logger.verbose(__([33m'[39;49;00m[33mOriginal exception:[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m) + traceback.format_exc())[90m[39;49;00m
>               [94mraise[39;49;00m ExtensionError(__([33m'[39;49;00m[33mCould not import extension [39;49;00m[33m%s[39;49;00m[33m'[39;49;00m) % extname,[90m[39;49;00m
                                     err) [94mfrom[39;49;00m[90m [39;49;00m[04m[96merr[39;49;00m[90m[39;49;00m
[1m[31mE               sphinx.errors.ExtensionError: Could not import extension sphinx.domains.javascript (exception: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py))[0m

[1m[31msphinx/registry.py[0m:429: ExtensionError
[31m[1m______________________ ERROR at setup of test_isproperty _______________________[0m

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9d4ab90>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9d4af50>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
>               mod = import_module(extname)[90m[39;49;00m

[1m[31msphinx/registry.py[0m:426: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31m/opt/miniconda3/envs/testbed/lib/python3.9/importlib/__init__.py[0m:127: in import_module
    [0m[94mreturn[39;49;00m _bootstrap._gcd_import(name[level:], package, level)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    [0m[33m"""[39;49;00m
    [33m    sphinx.domains.javascript[39;49;00m
    [33m    ~~~~~~~~~~~~~~~~~~~~~~~~~[39;49;00m
    [33m[39;49;00m
    [33m    The JavaScript domain.[39;49;00m
    [33m[39;49;00m
    [33m    :copyright: Copyright 2007-2021 by the Sphinx team, see AUTHORS.[39;49;00m
    [33m    :license: BSD, see LICENSE for details.[39;49;00m
    [33m"""[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mtyping[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Any, Dict, Iterator, List, Optional, Tuple, cast[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[90m [39;49;00m[94mimport[39;49;00m nodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Element, Node[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mparsers[39;49;00m[04m[96m.[39;49;00m[04m[96mrst[39;49;00m[90m [39;49;00m[94mimport[39;49;00m directives[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[90m [39;49;00m[94mimport[39;49;00m addnodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96maddnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m desc_signature, pending_xref[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mapplication[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Sphinx[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mbuilders[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Builder[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdirectives[39;49;00m[90m [39;49;00m[94mimport[39;49;00m ObjectDescription[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Domain, ObjType[90m[39;49;00m
>   [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[04m[96m.[39;49;00m[04m[96mpython[39;49;00m[90m [39;49;00m[94mimport[39;49;00m _pseudo_parse_arglist[90m[39;49;00m
[1m[31mE   ImportError: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py)[0m

[1m[31msphinx/domains/javascript.py[0m:23: ImportError

[33mThe above exception was the direct cause of the following exception:[0m

test_params = {'shared_result': None}
app_params = app_params(args=[], kwargs={'srcdir': path('/tmp/pytest-of-root/pytest-0/root')})
make_app = <function make_app.<locals>.make at 0x7c07b97c3230>
shared_result = <sphinx.testing.fixtures.SharedResult object at 0x7c07b9d4a5f0>

    [0m[37m@pytest[39;49;00m.fixture(scope=[33m'[39;49;00m[33mfunction[39;49;00m[33m'[39;49;00m)[90m[39;49;00m
    [94mdef[39;49;00m[90m [39;49;00m[92mapp[39;49;00m(test_params: Dict, app_params: Tuple[Dict, Dict], make_app: Callable,[90m[39;49;00m
            shared_result: SharedResult) -> Generator[SphinxTestApp, [94mNone[39;49;00m, [94mNone[39;49;00m]:[90m[39;49;00m
    [90m    [39;49;00m[33m"""[39;49;00m
    [33m    provides sphinx.application.Sphinx object[39;49;00m
    [33m    """[39;49;00m[90m[39;49;00m
        args, kwargs = app_params[90m[39;49;00m
>       app_ = make_app(*args, **kwargs)[90m[39;49;00m

[1m[31msphinx/testing/fixtures.py[0m:147: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31msphinx/testing/fixtures.py[0m:193: in make
    [0mapp_: Any = SphinxTestApp(*args, **kwargs)[90m[39;49;00m
[1m[31msphinx/testing/util.py[0m:130: in __init__
    [0m[96msuper[39;49;00m().[92m__init__[39;49;00m(srcdir, confdir, outdir, doctreedir,[90m[39;49;00m
[1m[31msphinx/application.py[0m:233: in __init__
    [0m[96mself[39;49;00m.setup_extension(extension)[90m[39;49;00m
[1m[31msphinx/application.py[0m:393: in setup_extension
    [0m[96mself[39;49;00m.registry.load_extension([96mself[39;49;00m, extname)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9d4ab90>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9d4af50>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
                mod = import_module(extname)[90m[39;49;00m
            [94mexcept[39;49;00m [96mImportError[39;49;00m [94mas[39;49;00m err:[90m[39;49;00m
                logger.verbose(__([33m'[39;49;00m[33mOriginal exception:[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m) + traceback.format_exc())[90m[39;49;00m
>               [94mraise[39;49;00m ExtensionError(__([33m'[39;49;00m[33mCould not import extension [39;49;00m[33m%s[39;49;00m[33m'[39;49;00m) % extname,[90m[39;49;00m
                                     err) [94mfrom[39;49;00m[90m [39;49;00m[04m[96merr[39;49;00m[90m[39;49;00m
[1m[31mE               sphinx.errors.ExtensionError: Could not import extension sphinx.domains.javascript (exception: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py))[0m

[1m[31msphinx/registry.py[0m:429: ExtensionError
[31m[1m____________________ ERROR at setup of test_isgenericalias _____________________[0m

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9c6ccd0>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9c6c550>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
>               mod = import_module(extname)[90m[39;49;00m

[1m[31msphinx/registry.py[0m:426: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31m/opt/miniconda3/envs/testbed/lib/python3.9/importlib/__init__.py[0m:127: in import_module
    [0m[94mreturn[39;49;00m _bootstrap._gcd_import(name[level:], package, level)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

    [0m[33m"""[39;49;00m
    [33m    sphinx.domains.javascript[39;49;00m
    [33m    ~~~~~~~~~~~~~~~~~~~~~~~~~[39;49;00m
    [33m[39;49;00m
    [33m    The JavaScript domain.[39;49;00m
    [33m[39;49;00m
    [33m    :copyright: Copyright 2007-2021 by the Sphinx team, see AUTHORS.[39;49;00m
    [33m    :license: BSD, see LICENSE for details.[39;49;00m
    [33m"""[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mtyping[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Any, Dict, Iterator, List, Optional, Tuple, cast[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[90m [39;49;00m[94mimport[39;49;00m nodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Element, Node[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96mdocutils[39;49;00m[04m[96m.[39;49;00m[04m[96mparsers[39;49;00m[04m[96m.[39;49;00m[04m[96mrst[39;49;00m[90m [39;49;00m[94mimport[39;49;00m directives[90m[39;49;00m
    [90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[90m [39;49;00m[94mimport[39;49;00m addnodes[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96maddnodes[39;49;00m[90m [39;49;00m[94mimport[39;49;00m desc_signature, pending_xref[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mapplication[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Sphinx[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mbuilders[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Builder[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdirectives[39;49;00m[90m [39;49;00m[94mimport[39;49;00m ObjectDescription[90m[39;49;00m
    [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[90m [39;49;00m[94mimport[39;49;00m Domain, ObjType[90m[39;49;00m
>   [94mfrom[39;49;00m[90m [39;49;00m[04m[96msphinx[39;49;00m[04m[96m.[39;49;00m[04m[96mdomains[39;49;00m[04m[96m.[39;49;00m[04m[96mpython[39;49;00m[90m [39;49;00m[94mimport[39;49;00m _pseudo_parse_arglist[90m[39;49;00m
[1m[31mE   ImportError: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py)[0m

[1m[31msphinx/domains/javascript.py[0m:23: ImportError

[33mThe above exception was the direct cause of the following exception:[0m

test_params = {'shared_result': None}
app_params = app_params(args=[], kwargs={'srcdir': path('/tmp/pytest-of-root/pytest-0/ext-autodoc')})
make_app = <function make_app.<locals>.make at 0x7c07b9726b90>
shared_result = <sphinx.testing.fixtures.SharedResult object at 0x7c07b9c6c780>

    [0m[37m@pytest[39;49;00m.fixture(scope=[33m'[39;49;00m[33mfunction[39;49;00m[33m'[39;49;00m)[90m[39;49;00m
    [94mdef[39;49;00m[90m [39;49;00m[92mapp[39;49;00m(test_params: Dict, app_params: Tuple[Dict, Dict], make_app: Callable,[90m[39;49;00m
            shared_result: SharedResult) -> Generator[SphinxTestApp, [94mNone[39;49;00m, [94mNone[39;49;00m]:[90m[39;49;00m
    [90m    [39;49;00m[33m"""[39;49;00m
    [33m    provides sphinx.application.Sphinx object[39;49;00m
    [33m    """[39;49;00m[90m[39;49;00m
        args, kwargs = app_params[90m[39;49;00m
>       app_ = make_app(*args, **kwargs)[90m[39;49;00m

[1m[31msphinx/testing/fixtures.py[0m:147: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
[1m[31msphinx/testing/fixtures.py[0m:193: in make
    [0mapp_: Any = SphinxTestApp(*args, **kwargs)[90m[39;49;00m
[1m[31msphinx/testing/util.py[0m:130: in __init__
    [0m[96msuper[39;49;00m().[92m__init__[39;49;00m(srcdir, confdir, outdir, doctreedir,[90m[39;49;00m
[1m[31msphinx/application.py[0m:233: in __init__
    [0m[96mself[39;49;00m.setup_extension(extension)[90m[39;49;00m
[1m[31msphinx/application.py[0m:393: in setup_extension
    [0m[96mself[39;49;00m.registry.load_extension([96mself[39;49;00m, extname)[90m[39;49;00m
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sphinx.registry.SphinxComponentRegistry object at 0x7c07b9c6ccd0>
app = <[AttributeError("'NoneType' object has no attribute 'name'") raised in repr()] SphinxTestApp object at 0x7c07b9c6c550>
extname = 'sphinx.domains.javascript'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mload_extension[39;49;00m([96mself[39;49;00m, app: [33m"[39;49;00m[33mSphinx[39;49;00m[33m"[39;49;00m, extname: [96mstr[39;49;00m) -> [94mNone[39;49;00m:[90m[39;49;00m
    [90m    [39;49;00m[33m"""Load a Sphinx extension."""[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m app.extensions:  [90m# already loaded[39;49;00m[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
        [94mif[39;49;00m extname [95min[39;49;00m EXTENSION_BLACKLIST:[90m[39;49;00m
            logger.warning(__([33m'[39;49;00m[33mthe extension [39;49;00m[33m%r[39;49;00m[33m was already merged with Sphinx since [39;49;00m[33m'[39;49;00m[90m[39;49;00m
                              [33m'[39;49;00m[33mversion [39;49;00m[33m%s[39;49;00m[33m; this extension is ignored.[39;49;00m[33m'[39;49;00m),[90m[39;49;00m
                           extname, EXTENSION_BLACKLIST[extname])[90m[39;49;00m
            [94mreturn[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
        [90m# update loading context[39;49;00m[90m[39;49;00m
        prefix = __([33m'[39;49;00m[33mwhile setting up extension [39;49;00m[33m%s[39;49;00m[33m:[39;49;00m[33m'[39;49;00m) % extname[90m[39;49;00m
        [94mwith[39;49;00m prefixed_warnings(prefix):[90m[39;49;00m
            [94mtry[39;49;00m:[90m[39;49;00m
                mod = import_module(extname)[90m[39;49;00m
            [94mexcept[39;49;00m [96mImportError[39;49;00m [94mas[39;49;00m err:[90m[39;49;00m
                logger.verbose(__([33m'[39;49;00m[33mOriginal exception:[39;49;00m[33m\n[39;49;00m[33m'[39;49;00m) + traceback.format_exc())[90m[39;49;00m
>               [94mraise[39;49;00m ExtensionError(__([33m'[39;49;00m[33mCould not import extension [39;49;00m[33m%s[39;49;00m[33m'[39;49;00m) % extname,[90m[39;49;00m
                                     err) [94mfrom[39;49;00m[90m [39;49;00m[04m[96merr[39;49;00m[90m[39;49;00m
[1m[31mE               sphinx.errors.ExtensionError: Could not import extension sphinx.domains.javascript (exception: cannot import name '_pseudo_parse_arglist' from 'sphinx.domains.python' (/testbed/sphinx/domains/python.py))[0m

[1m[31msphinx/registry.py[0m:429: ExtensionError
=================================== FAILURES ===================================
[31m[1m_________________________ test_object_description_enum _________________________[0m

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mtest_object_description_enum[39;49;00m():[90m[39;49;00m
        [94mclass[39;49;00m[90m [39;49;00m[04m[92mMyEnum[39;49;00m(enum.Enum):[90m[39;49;00m
            FOO = [94m1[39;49;00m[90m[39;49;00m
            BAR = [94m2[39;49;00m[90m[39;49;00m
    [90m[39;49;00m
>       [94massert[39;49;00m inspect.object_description(MyEnum.FOO) == [33m"[39;49;00m[33mMyEnum.FOO[39;49;00m[33m"[39;49;00m[90m[39;49;00m
[1m[31mE       AssertionError: assert '<MyEnum.FOO: 1>' == 'MyEnum.FOO'[0m
[1m[31mE         [0m
[1m[31mE         - MyEnum.FOO[0m
[1m[31mE         + <MyEnum.FOO: 1>[0m
[1m[31mE         ? +          ++++[0m

[1m[31mtests/test_util_inspect.py[0m:525: AssertionError
[33m=============================== warnings summary ===============================[0m
sphinx/util/docutils.py:44
  /testbed/sphinx/util/docutils.py:44: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
    __version_info__ = tuple(LooseVersion(docutils.__version__).version)

sphinx/highlighting.py:67
  /testbed/sphinx/highlighting.py:67: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
    if tuple(LooseVersion(pygmentsversion).version) <= (2, 7, 4):

sphinx/registry.py:24
  /testbed/sphinx/registry.py:24: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
    from pkg_resources import iter_entry_points

../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('sphinxcontrib')`.
  Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
    declare_namespace(pkg)

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
==================================== PASSES ====================================
============================= slowest 25 durations =============================
0.33s setup    tests/test_util_inspect.py::test_signature_annotations_py38
0.01s setup    tests/test_util_inspect.py::test_isproperty
0.01s setup    tests/test_util_inspect.py::test_isfunction
0.01s setup    tests/test_util_inspect.py::test_iscoroutinefunction
0.01s setup    tests/test_util_inspect.py::test_isclassmethod
0.01s setup    tests/test_util_inspect.py::test_isstaticmethod
0.01s setup    tests/test_util_inspect.py::test_isbuiltin
0.01s setup    tests/test_util_inspect.py::test_isdescriptor
0.01s setup    tests/test_util_inspect.py::test_isgenericalias
0.01s setup    tests/test_util_inspect.py::test_isattributedescriptor

(15 durations < 0.005s hidden.  Use -vv to show these durations.)
[36m[1m=========================== short test summary info ============================[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_TypeAliasNamespace[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_signature[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_signature_partial[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_signature_methods[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_signature_partialmethod[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_signature_annotations[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_signature_from_str_basic[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_signature_from_str_default_values[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_signature_from_str_annotations[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_signature_from_str_complex_annotations[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_signature_from_str_kwonly_args[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_signature_from_str_positionaly_only_args[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_signature_from_str_invalid[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_signature_from_ast[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_safe_getattr_with_default[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_safe_getattr_with_exception[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_safe_getattr_with_property_exception[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_safe_getattr_with___dict___override[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_dictionary_sorting[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_set_sorting[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_set_sorting_fallback[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_frozenset_sorting[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_frozenset_sorting_fallback[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_dict_customtype[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_getslots[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_unpartial[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_getdoc_inherited_decorated_method[0m
[32mPASSED[0m tests/test_util_inspect.py::[1mtest_is_builtin_class_method[0m
[31mERROR[0m tests/test_util_inspect.py::[1mtest_signature_annotations_py38[0m - sphinx.errors.ExtensionError: Could not import extension sphinx.domains.jav...
[31mERROR[0m tests/test_util_inspect.py::[1mtest_isclassmethod[0m - sphinx.errors.ExtensionError: Could not import extension sphinx.domains.jav...
[31mERROR[0m tests/test_util_inspect.py::[1mtest_isstaticmethod[0m - sphinx.errors.ExtensionError: Could not import extension sphinx.domains.jav...
[31mERROR[0m tests/test_util_inspect.py::[1mtest_iscoroutinefunction[0m - sphinx.errors.ExtensionError: Could not import extension sphinx.domains.jav...
[31mERROR[0m tests/test_util_inspect.py::[1mtest_isfunction[0m - sphinx.errors.ExtensionError: Could not import extension sphinx.domains.jav...
[31mERROR[0m tests/test_util_inspect.py::[1mtest_isbuiltin[0m - sphinx.errors.ExtensionError: Could not import extension sphinx.domains.jav...
[31mERROR[0m tests/test_util_inspect.py::[1mtest_isdescriptor[0m - sphinx.errors.ExtensionError: Could not import extension sphinx.domains.jav...
[31mERROR[0m tests/test_util_inspect.py::[1mtest_isattributedescriptor[0m - sphinx.errors.ExtensionError: Could not import extension sphinx.domains.jav...
[31mERROR[0m tests/test_util_inspect.py::[1mtest_isproperty[0m - sphinx.errors.ExtensionError: Could not import extension sphinx.domains.jav...
[31mERROR[0m tests/test_util_inspect.py::[1mtest_isgenericalias[0m - sphinx.errors.ExtensionError: Could not import extension sphinx.domains.jav...
[31mFAILED[0m tests/test_util_inspect.py::[1mtest_object_description_enum[0m - AssertionError: assert '<MyEnum.FOO: 1>' == 'MyEnum.FOO'
[31m============= [31m[1m1 failed[0m, [32m28 passed[0m, [33m7 warnings[0m, [31m[1m10 errors[0m[31m in 1.25s[0m[31m ==============[0m
py39: exit 1 (1.79 seconds) /testbed> python -X dev -m pytest -rA --durations 25 tests/test_util_inspect.py pid=111
  py39: FAIL code 1 (1.80=setup[0.01]+cmd[1.79] seconds)
  evaluation failed :( (1.89 seconds)
+ git checkout 8ec06e9a1bd862cd713b9db748e039ccc7b3e15b tests/test_util_inspect.py
Updated 1 path from c46f02ca4
