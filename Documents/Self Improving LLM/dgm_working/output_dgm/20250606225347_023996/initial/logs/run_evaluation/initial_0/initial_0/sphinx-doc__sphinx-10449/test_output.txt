+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=2
++ CONDA_SHLVL=2
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_1=/opt/miniconda3
++ CONDA_PREFIX_1=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ cd /testbed
+ git config --global --add safe.directory /testbed
+ cd /testbed
+ git status
On branch master
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   sphinx/ext/autodoc/typehints.py
	modified:   tox.ini

no changes added to commit (use "git add" and/or "git commit -a")
+ git show
commit 36367765fe780f962bba861bf368a765380bbc68
Merge: 23ab36bcd 51fc4d180
Author: Takeshi KOMIYA <<EMAIL>>
Date:   Sun May 15 12:13:04 2022 +0900

    Merge pull request #10450 from sphinx-doc/bot/pull-translations
    
    Update message catalogs

+ git diff 36367765fe780f962bba861bf368a765380bbc68
diff --git a/sphinx/ext/autodoc/typehints.py b/sphinx/ext/autodoc/typehints.py
index 06768168e..53f949beb 100644
--- a/sphinx/ext/autodoc/typehints.py
+++ b/sphinx/ext/autodoc/typehints.py
@@ -28,7 +28,8 @@ def record_typehints(app: Sphinx, objtype: str, name: str, obj: Any,
             for param in sig.parameters.values():
                 if param.annotation is not param.empty:
                     annotation[param.name] = typing.stringify(param.annotation, mode)
-            if sig.return_annotation is not sig.empty:
+            if sig.return_annotation is not sig.empty and objtype != 'class':
+                # Skip return annotation for classes to avoid showing __init__ return type
                 annotation['return'] = typing.stringify(sig.return_annotation, mode)
     except (TypeError, ValueError):
         pass
@@ -187,4 +188,4 @@ def setup(app: Sphinx) -> Dict[str, Any]:
         'version': 'builtin',
         'parallel_read_safe': True,
         'parallel_write_safe': True,
-    }
+    }
\ No newline at end of file
diff --git a/tox.ini b/tox.ini
index 105a02597..90ada0821 100644
--- a/tox.ini
+++ b/tox.ini
@@ -29,7 +29,7 @@ setenv =
     PYTHONWARNINGS = all
     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes
 commands=
-    python -X dev -X warn_default_encoding -m pytest --durations 25 {posargs}
+    python -X dev -X warn_default_encoding -m pytest -rA --durations 25 {posargs}
 
 [testenv:du-latest]
 commands =
+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z x ']'
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1='(testbed) '
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=3
+++ CONDA_SHLVL=3
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=4
++ CONDA_SHLVL=4
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_3=/opt/miniconda3
++ CONDA_PREFIX_3=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ python -m pip install -e '.[test]'
Obtaining file:///testbed
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: sphinxcontrib-applehelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (2.0.0)
Requirement already satisfied: sphinxcontrib-devhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (2.0.0)
Requirement already satisfied: sphinxcontrib-jsmath in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (1.0.1)
Requirement already satisfied: sphinxcontrib-htmlhelp>=2.0.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (2.1.0)
Requirement already satisfied: sphinxcontrib-serializinghtml>=1.1.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (2.0.0)
Requirement already satisfied: sphinxcontrib-qthelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (2.0.0)
Requirement already satisfied: Jinja2>=2.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (3.1.6)
Requirement already satisfied: Pygments>=2.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (2.19.1)
Requirement already satisfied: docutils<0.19,>=0.14 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (0.18.1)
Requirement already satisfied: snowballstemmer>=1.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (2.2.0)
Requirement already satisfied: babel>=1.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (2.17.0)
Requirement already satisfied: alabaster<0.8,>=0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (0.7.16)
Requirement already satisfied: imagesize in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (1.4.1)
Requirement already satisfied: requests>=2.5.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (2.32.3)
Requirement already satisfied: packaging in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (24.2)
Requirement already satisfied: importlib-metadata>=4.4 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (8.6.1)
Requirement already satisfied: pytest>=4.6 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (8.3.5)
Requirement already satisfied: html5lib in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (1.1)
Requirement already satisfied: cython in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==5.1.0.dev20250315) (3.0.12)
Requirement already satisfied: zipp>=3.20 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from importlib-metadata>=4.4->Sphinx==5.1.0.dev20250315) (3.21.0)
Requirement already satisfied: MarkupSafe>=2.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Jinja2>=2.3->Sphinx==5.1.0.dev20250315) (3.0.2)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest>=4.6->Sphinx==5.1.0.dev20250315) (1.2.2)
Requirement already satisfied: iniconfig in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest>=4.6->Sphinx==5.1.0.dev20250315) (2.0.0)
Requirement already satisfied: pluggy<2,>=1.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest>=4.6->Sphinx==5.1.0.dev20250315) (1.5.0)
Requirement already satisfied: tomli>=1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest>=4.6->Sphinx==5.1.0.dev20250315) (2.2.1)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==5.1.0.dev20250315) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==5.1.0.dev20250315) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==5.1.0.dev20250315) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==5.1.0.dev20250315) (2025.1.31)
Requirement already satisfied: six>=1.9 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==5.1.0.dev20250315) (1.17.0)
Requirement already satisfied: webencodings in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==5.1.0.dev20250315) (0.5.1)
Installing collected packages: Sphinx
  Attempting uninstall: Sphinx
    Found existing installation: Sphinx 5.1.0.dev20250313
    Uninstalling Sphinx-5.1.0.dev20250313:
      Successfully uninstalled Sphinx-5.1.0.dev20250313
  DEPRECATION: Legacy editable install of Sphinx[test]==5.1.0.dev20250315 from file:///testbed (setup.py develop) is deprecated. pip 25.1 will enforce this behaviour change. A possible replacement is to add a pyproject.toml or enable --use-pep517, and use setuptools >= 64. If the resulting installation is not behaving as expected, try using --config-settings editable_mode=compat. Please consult the setuptools documentation for more information. Discussion can be found at https://github.com/pypa/pip/issues/11457
  Running setup.py develop for Sphinx
Successfully installed Sphinx
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
+ git checkout 36367765fe780f962bba861bf368a765380bbc68 tests/test_ext_autodoc_configs.py
Updated 0 paths from b1e49e693
+ git apply -v -
Checking patch tests/test_ext_autodoc_configs.py...
Applied patch tests/test_ext_autodoc_configs.py cleanly.
+ tox --current-env -epy39 -v -- tests/test_ext_autodoc_configs.py
py39: commands[0]> python -X dev -X warn_default_encoding -m pytest -rA --durations 25 tests/test_ext_autodoc_configs.py
[1m============================= test session starts ==============================[0m
platform linux -- Python 3.9.21, pytest-8.3.5, pluggy-1.5.0
cachedir: .tox/py39/.pytest_cache
libraries: Sphinx-5.1.0+/36367765f, docutils-0.18.1
base tempdir: /tmp/pytest-of-root/pytest-0
rootdir: /testbed
configfile: setup.cfg
collected 31 items

tests/test_ext_autodoc_configs.py [32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m        [100%][0m

==================================== PASSES ====================================
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: text
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mbuilding [mo]: [39;49;00mtargets for 0 po files that are out of date
[01mbuilding [text]: [39;49;00mtargets for 1 source files that are out of date
[01mupdating environment: [39;49;00m[new config] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
[01mbuild succeeded.[39;49;00m

The text files are in ../tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text.

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: text
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [mo]: [39;49;00mtargets for 0 po files that are out of date
[01mbuilding [text]: [39;49;00mtargets for 1 source files that are out of date
[01mupdating environment: [39;49;00m[config changed ('autodoc_typehints_description_target')] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
[01mbuild succeeded.[39;49;00m

The text files are in ../tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text.

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: text
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [mo]: [39;49;00mtargets for 0 po files that are out of date
[01mbuilding [text]: [39;49;00mtargets for 1 source files that are out of date
[01mupdating environment: [39;49;00m[config changed ('autodoc_typehints_description_target')] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
[01mbuild succeeded.[39;49;00m

The text files are in ../tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text.

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: text
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [mo]: [39;49;00mtargets for 0 po files that are out of date
[01mbuilding [text]: [39;49;00mtargets for 1 source files that are out of date
[01mupdating environment: [39;49;00m[config changed ('autodoc_typehints_description_target')] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
[01mbuild succeeded.[39;49;00m

The text files are in ../tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text.

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: text
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [mo]: [39;49;00mtargets for 0 po files that are out of date
[01mbuilding [text]: [39;49;00mtargets for 1 source files that are out of date
[01mupdating environment: [39;49;00m[config changed ('autodoc_typehints_description_target')] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
[01mbuild succeeded.[39;49;00m

The text files are in ../tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text.

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: text
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [mo]: [39;49;00mtargets for 0 po files that are out of date
[01mbuilding [text]: [39;49;00mtargets for 1 source files that are out of date
[01mupdating environment: [39;49;00m[config changed ('autodoc_typehints_description_target')] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
[01mbuild succeeded.[39;49;00m

The text files are in ../tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text.

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: text
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: text
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [mo]: [39;49;00mtargets for 0 po files that are out of date
[01mbuilding [text]: [39;49;00mtargets for 1 source files that are out of date
[01mupdating environment: [39;49;00m[config changed ('autodoc_typehints')] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
[01mbuild succeeded.[39;49;00m

The text files are in ../tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text.

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: text
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/text
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: text
# srcdir: /tmp/pytest-of-root/pytest-0/autodoc_typehints_description_and_type_aliases
# outdir: /tmp/pytest-of-root/pytest-0/autodoc_typehints_description_and_type_aliases/_build/text
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mbuilding [mo]: [39;49;00mtargets for 0 po files that are out of date
[01mbuilding [text]: [39;49;00mtargets for 1 source files that are out of date
[01mupdating environment: [39;49;00m[new config] 2 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[ 50%] [35mautodoc_type_aliases[39;49;00m                                 
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 50%] [32mautodoc_type_aliases[39;49;00m                                  
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
[01mbuild succeeded, 4 warnings.[39;49;00m

The text files are in ../tmp/pytest-of-root/pytest-0/autodoc_typehints_description_and_type_aliases/_build/text.

# warning: 
[91m/tmp/pytest-of-root/pytest-0/autodoc_typehints_description_and_type_aliases/autodoc_type_aliases.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-autodoc/target/autodoc_type_aliases.py:docstring of target.autodoc_type_aliases.sum:: WARNING: py:class reference target not found: myint[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-autodoc/target/autodoc_type_aliases.py:docstring of target.autodoc_type_aliases.sum:: WARNING: py:class reference target not found: myint[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/ext-autodoc/target/autodoc_type_aliases.py:docstring of target.autodoc_type_aliases.sum:: WARNING: py:class reference target not found: myint[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone

# warning: 

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v5.1.0+/36367765f[39;49;00m
[01mloading pickled environment... [39;49;00mdone

# warning: 

============================= slowest 25 durations =============================
0.41s setup    tests/test_ext_autodoc_configs.py::test_autoclass_content_class
0.16s call     tests/test_ext_autodoc_configs.py::test_autodoc_typehints_format_fully_qualified
0.13s call     tests/test_ext_autodoc_configs.py::test_autodoc_typehints_signature
0.13s call     tests/test_ext_autodoc_configs.py::test_autodoc_typehints_none
0.09s call     tests/test_ext_autodoc_configs.py::test_autodoc_default_options
0.08s call     tests/test_ext_autodoc_configs.py::test_autodoc_default_options_with_values
0.07s call     tests/test_ext_autodoc_configs.py::test_autodoc_typehints_description
0.06s call     tests/test_ext_autodoc_configs.py::test_autodoc_typehints_description_and_type_aliases
0.04s call     tests/test_ext_autodoc_configs.py::test_autodoc_typehints_both
0.04s setup    tests/test_ext_autodoc_configs.py::test_autodoc_typehints_none
0.03s call     tests/test_ext_autodoc_configs.py::test_autodoc_typehints_description_no_undoc_doc_rtype
0.03s call     tests/test_ext_autodoc_configs.py::test_autodoc_typehints_description_no_undoc
0.02s call     tests/test_ext_autodoc_configs.py::test_autodoc_type_aliases
0.02s call     tests/test_ext_autodoc_configs.py::test_autodoc_typehints_description_with_documented_init_no_undoc
0.02s call     tests/test_ext_autodoc_configs.py::test_autodoc_typehints_description_with_documented_init
0.02s call     tests/test_ext_autodoc_configs.py::test_autodoc_typehints_description_with_documented_init_no_undoc_doc_rtype
0.02s setup    tests/test_ext_autodoc_configs.py::test_autodoc_typehints_description_and_type_aliases
0.02s setup    tests/test_ext_autodoc_configs.py::test_autodoc_typehints_signature
0.02s call     tests/test_ext_autodoc_configs.py::test_autoclass_content_class
0.02s setup    tests/test_ext_autodoc_configs.py::test_autodoc_class_signature_separated_new
0.02s call     tests/test_ext_autodoc_configs.py::test_autodoc_docstring_signature
0.02s setup    tests/test_ext_autodoc_configs.py::test_autodoc_default_options_with_values
0.02s setup    tests/test_ext_autodoc_configs.py::test_autodoc_typehints_format_fully_qualified
0.02s setup    tests/test_ext_autodoc_configs.py::test_autoclass_content_init
0.02s setup    tests/test_ext_autodoc_configs.py::test_autodoc_typehints_format_fully_qualified_for_class_alias
[36m[1m=========================== short test summary info ============================[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autoclass_content_class[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autoclass_content_init[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_class_signature_mixed[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_class_signature_separated_init[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_class_signature_separated_new[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autoclass_content_both[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_inherit_docstrings[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_docstring_signature[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autoclass_content_and_docstring_signature_class[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autoclass_content_and_docstring_signature_init[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autoclass_content_and_docstring_signature_both[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_mocked_module_imports[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_signature[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_none[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_none_for_overload[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_description[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_description_no_undoc[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_description_no_undoc_doc_rtype[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_description_with_documented_init[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_description_with_documented_init_no_undoc[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_description_with_documented_init_no_undoc_doc_rtype[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_description_for_invalid_node[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_both[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_type_aliases[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_description_and_type_aliases[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_format_fully_qualified[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_format_fully_qualified_for_class_alias[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_format_fully_qualified_for_generic_alias[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_typehints_format_fully_qualified_for_newtype_alias[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_default_options[0m
[32mPASSED[0m tests/test_ext_autodoc_configs.py::[1mtest_autodoc_default_options_with_values[0m
[32m============================== [32m[1m31 passed[0m[32m in 2.15s[0m[32m ==============================[0m
py39: exit 0 (2.62 seconds) /testbed> python -X dev -X warn_default_encoding -m pytest -rA --durations 25 tests/test_ext_autodoc_configs.py pid=113
  py39: OK (2.63=setup[0.01]+cmd[2.62] seconds)
  congratulations :) (2.72 seconds)
+ git checkout 36367765fe780f962bba861bf368a765380bbc68 tests/test_ext_autodoc_configs.py
Updated 1 path from b1e49e693
