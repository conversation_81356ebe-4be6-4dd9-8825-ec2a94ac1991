{"django__django-15930": {"patch_is_None": false, "patch_exists": true, "patch_successfully_applied": true, "resolved": true, "tests_status": {"FAIL_TO_PASS": {"success": ["test_annotate_with_full_when (expressions_case.tests.CaseExpressionTests)"], "failure": []}, "PASS_TO_PASS": {"success": ["test_empty_q_object (expressions_case.tests.CaseWhenTests)", "test_invalid_when_constructor_args (expressions_case.tests.CaseWhenTests)", "test_only_when_arguments (expressions_case.tests.CaseWhenTests)", "test_conditional_aggregation_example (expressions_case.tests.CaseDocumentationExamples)", "test_conditional_update_example (expressions_case.tests.CaseDocumentationExamples)", "test_filter_example (expressions_case.tests.CaseDocumentationExamples)", "test_hash (expressions_case.tests.CaseDocumentationExamples)", "test_lookup_example (expressions_case.tests.CaseDocumentationExamples)", "test_simple_example (expressions_case.tests.CaseDocumentationExamples)", "test_aggregate (expressions_case.tests.CaseExpressionTests)", "test_aggregate_with_expression_as_condition (expressions_case.tests.CaseExpressionTests)", "test_aggregate_with_expression_as_value (expressions_case.tests.CaseExpressionTests)", "test_aggregation_empty_cases (expressions_case.tests.CaseExpressionTests)", "test_annotate (expressions_case.tests.CaseExpressionTests)", "test_annotate_exclude (expressions_case.tests.CaseExpressionTests)", "test_annotate_filter_decimal (expressions_case.tests.CaseExpressionTests)", "test_annotate_values_not_in_order_by (expressions_case.tests.CaseExpressionTests)", "test_annotate_with_aggregation_in_condition (expressions_case.tests.CaseExpressionTests)", "test_annotate_with_aggregation_in_predicate (expressions_case.tests.CaseExpressionTests)", "test_annotate_with_aggregation_in_value (expressions_case.tests.CaseExpressionTests)", "test_annotate_with_annotation_in_condition (expressions_case.tests.CaseExpressionTests)", "test_annotate_with_annotation_in_predicate (expressions_case.tests.CaseExpressionTests)", "test_annotate_with_annotation_in_value (expressions_case.tests.CaseExpressionTests)", "test_annotate_with_empty_when (expressions_case.tests.CaseExpressionTests)", "test_annotate_with_expression_as_condition (expressions_case.tests.CaseExpressionTests)", "test_annotate_with_expression_as_value (expressions_case.tests.CaseExpressionTests)", "test_annotate_with_in_clause (expressions_case.tests.CaseExpressionTests)", "test_annotate_with_join_in_condition (expressions_case.tests.CaseExpressionTests)", "test_annotate_with_join_in_predicate (expressions_case.tests.CaseExpressionTests)", "test_annotate_with_join_in_value (expressions_case.tests.CaseExpressionTests)", "test_annotate_without_default (expressions_case.tests.CaseExpressionTests)", "test_case_reuse (expressions_case.tests.CaseExpressionTests)", "test_combined_expression (expressions_case.tests.CaseExpressionTests)", "test_combined_q_object (expressions_case.tests.CaseExpressionTests)", "test_condition_with_lookups (expressions_case.tests.CaseExpressionTests)", "test_filter (expressions_case.tests.CaseExpressionTests)", "test_filter_with_aggregation_in_condition (expressions_case.tests.CaseExpressionTests)", "test_filter_with_aggregation_in_predicate (expressions_case.tests.CaseExpressionTests)", "test_filter_with_aggregation_in_value (expressions_case.tests.CaseExpressionTests)", "test_filter_with_annotation_in_condition (expressions_case.tests.CaseExpressionTests)", "test_filter_with_annotation_in_predicate (expressions_case.tests.CaseExpressionTests)", "test_filter_with_annotation_in_value (expressions_case.tests.CaseExpressionTests)", "test_filter_with_expression_as_condition (expressions_case.tests.CaseExpressionTests)", "test_filter_with_expression_as_value (expressions_case.tests.CaseExpressionTests)", "test_filter_with_join_in_condition (expressions_case.tests.CaseExpressionTests)", "test_filter_with_join_in_predicate (expressions_case.tests.CaseExpressionTests)", "test_filter_with_join_in_value (expressions_case.tests.CaseExpressionTests)", "test_filter_without_default (expressions_case.tests.CaseExpressionTests)", "test_in_subquery (expressions_case.tests.CaseExpressionTests)", "test_join_promotion (expressions_case.tests.CaseExpressionTests)", "test_join_promotion_multiple_annotations (expressions_case.tests.CaseExpressionTests)", "test_lookup_different_fields (expressions_case.tests.CaseExpressionTests)", "test_lookup_in_condition (expressions_case.tests.CaseExpressionTests)", "test_m2m_exclude (expressions_case.tests.CaseExpressionTests)", "test_m2m_reuse (expressions_case.tests.CaseExpressionTests)", "test_order_by_conditional_explicit (expressions_case.tests.CaseExpressionTests)", "test_order_by_conditional_implicit (expressions_case.tests.CaseExpressionTests)", "test_update (expressions_case.tests.CaseExpressionTests)", "test_update_big_integer (expressions_case.tests.CaseExpressionTests)", "test_update_binary (expressions_case.tests.CaseExpressionTests)", "test_update_boolean (expressions_case.tests.CaseExpressionTests)", "test_update_date (expressions_case.tests.CaseExpressionTests)", "test_update_date_time (expressions_case.tests.CaseExpressionTests)", "test_update_decimal (expressions_case.tests.CaseExpressionTests)", "test_update_duration (expressions_case.tests.CaseExpressionTests)", "test_update_email (expressions_case.tests.CaseExpressionTests)", "test_update_file (expressions_case.tests.CaseExpressionTests)", "test_update_file_path (expressions_case.tests.CaseExpressionTests)", "test_update_fk (expressions_case.tests.CaseExpressionTests)", "test_update_float (expressions_case.tests.CaseExpressionTests)", "test_update_generic_ip_address (expressions_case.tests.CaseExpressionTests)", "test_update_image (expressions_case.tests.CaseExpressionTests)", "test_update_null_boolean (expressions_case.tests.CaseExpressionTests)", "test_update_positive_big_integer (expressions_case.tests.CaseExpressionTests)", "test_update_positive_integer (expressions_case.tests.CaseExpressionTests)", "test_update_positive_small_integer (expressions_case.tests.CaseExpressionTests)", "test_update_slug (expressions_case.tests.CaseExpressionTests)", "test_update_small_integer (expressions_case.tests.CaseExpressionTests)", "test_update_string (expressions_case.tests.CaseExpressionTests)", "test_update_text (expressions_case.tests.CaseExpressionTests)", "test_update_time (expressions_case.tests.CaseExpressionTests)", "test_update_url (expressions_case.tests.CaseExpressionTests)", "test_update_uuid (expressions_case.tests.CaseExpressionTests)", "test_update_with_expression_as_condition (expressions_case.tests.CaseExpressionTests)", "test_update_with_expression_as_value (expressions_case.tests.CaseExpressionTests)", "test_update_with_join_in_condition_raise_field_error (expressions_case.tests.CaseExpressionTests)", "test_update_with_join_in_predicate_raise_field_error (expressions_case.tests.CaseExpressionTests)", "test_update_without_default (expressions_case.tests.CaseExpressionTests)"], "failure": []}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}}}