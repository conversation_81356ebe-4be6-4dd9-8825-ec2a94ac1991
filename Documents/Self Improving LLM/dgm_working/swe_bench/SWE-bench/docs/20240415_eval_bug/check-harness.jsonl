{"instance_id": "astropy__astropy-7858", "model_name_or_path": "gold", "model_patch": "diff --git a/astropy/wcs/wcs.py b/astropy/wcs/wcs.py\n--- a/astropy/wcs/wcs.py\n+++ b/astropy/wcs/wcs.py\n@@ -1256,7 +1256,7 @@ def _return_single_array(xy, origin):\n                 raise TypeError(\n                     \"When providing two arguments, they must be \"\n                     \"(coords[N][{0}], origin)\".format(self.naxis))\n-            if self.naxis == 1 and len(xy.shape) == 1:\n+            if xy.shape == () or len(xy.shape) == 1:\n                 return _return_list_of_arrays([xy], origin)\n             return _return_single_array(xy, origin)\n \n"}
{"instance_id": "astropy__astropy-8339", "model_name_or_path": "gold", "model_patch": "diff --git a/astropy/stats/bayesian_blocks.py b/astropy/stats/bayesian_blocks.py\n--- a/astropy/stats/bayesian_blocks.py\n+++ b/astropy/stats/bayesian_blocks.py\n@@ -289,15 +289,14 @@ def compute_ncp_prior(self, N):\n         If ``ncp_prior`` is not explicitly defined, compute it from ``gamma``\n         or ``p0``.\n         \"\"\"\n-        if self.ncp_prior is not None:\n-            return self.ncp_prior\n-        elif self.gamma is not None:\n+\n+        if self.gamma is not None:\n             return -np.log(self.gamma)\n         elif self.p0 is not None:\n             return self.p0_prior(N)\n         else:\n-            raise ValueError(\"``ncp_prior`` is not defined, and cannot compute \"\n-                             \"it as neither ``gamma`` nor ``p0`` is defined.\")\n+            raise ValueError(\"``ncp_prior`` cannot be computed as neither \"\n+                             \"``gamma`` nor ``p0`` is defined.\")\n \n     def fit(self, t, x=None, sigma=None):\n         \"\"\"Fit the Bayesian Blocks model given the specified fitness function.\n@@ -340,6 +339,9 @@ def fit(self, t, x=None, sigma=None):\n         # Compute ncp_prior if not defined\n         if self.ncp_prior is None:\n             ncp_prior = self.compute_ncp_prior(N)\n+        else:\n+            ncp_prior = self.ncp_prior\n+\n         # ----------------------------------------------------------------\n         # Start with first data cell; add one cell at each iteration\n         # ----------------------------------------------------------------\n"}
{"instance_id": "astropy__astropy-8872", "model_name_or_path": "gold", "model_patch": "diff --git a/astropy/units/quantity.py b/astropy/units/quantity.py\n--- a/astropy/units/quantity.py\n+++ b/astropy/units/quantity.py\n@@ -215,8 +215,8 @@ class Quantity(np.ndarray, metaclass=InheritDocstrings):\n     dtype : ~numpy.dtype, optional\n         The dtype of the resulting Numpy array or scalar that will\n         hold the value.  If not provided, it is determined from the input,\n-        except that any input that cannot represent float (integer and bool)\n-        is converted to float.\n+        except that any integer and (non-Quantity) object inputs are converted\n+        to float by default.\n \n     copy : bool, optional\n         If `True` (default), then the value is copied.  Otherwise, a copy will\n@@ -296,8 +296,7 @@ def __new__(cls, value, unit=None, dtype=None, copy=True, order=None,\n                 if not copy:\n                     return value\n \n-                if not (np.can_cast(np.float32, value.dtype) or\n-                        value.dtype.fields):\n+                if value.dtype.kind in 'iu':\n                     dtype = float\n \n             return np.array(value, dtype=dtype, copy=copy, order=order,\n@@ -377,9 +376,7 @@ def __new__(cls, value, unit=None, dtype=None, copy=True, order=None,\n                             \"Numpy numeric type.\")\n \n         # by default, cast any integer, boolean, etc., to float\n-        if dtype is None and (not (np.can_cast(np.float32, value.dtype)\n-                                   or value.dtype.fields)\n-                              or value.dtype.kind == 'O'):\n+        if dtype is None and value.dtype.kind in 'iuO':\n             value = value.astype(float)\n \n         value = value.view(cls)\n"}
{"instance_id": "astropy__astropy-11693", "model_name_or_path": "gold", "model_patch": "diff --git a/astropy/wcs/wcsapi/fitswcs.py b/astropy/wcs/wcsapi/fitswcs.py\n--- a/astropy/wcs/wcsapi/fitswcs.py\n+++ b/astropy/wcs/wcsapi/fitswcs.py\n@@ -323,7 +323,17 @@ def pixel_to_world_values(self, *pixel_arrays):\n         return world[0] if self.world_n_dim == 1 else tuple(world)\n \n     def world_to_pixel_values(self, *world_arrays):\n-        pixel = self.all_world2pix(*world_arrays, 0)\n+        # avoid circular import\n+        from astropy.wcs.wcs import NoConvergence\n+        try:\n+            pixel = self.all_world2pix(*world_arrays, 0)\n+        except NoConvergence as e:\n+            warnings.warn(str(e))\n+            # use best_solution contained in the exception and format the same\n+            # way as all_world2pix does (using _array_converter)\n+            pixel = self._array_converter(lambda *args: e.best_solution,\n+                                         'input', *world_arrays, 0)\n+\n         return pixel[0] if self.pixel_n_dim == 1 else tuple(pixel)\n \n     @property\n"}
{"instance_id": "astropy__astropy-13033", "model_name_or_path": "gold", "model_patch": "diff --git a/astropy/timeseries/core.py b/astropy/timeseries/core.py\n--- a/astropy/timeseries/core.py\n+++ b/astropy/timeseries/core.py\n@@ -55,6 +55,13 @@ class BaseTimeSeries(QTable):\n     _required_columns_relax = False\n \n     def _check_required_columns(self):\n+        def as_scalar_or_list_str(obj):\n+            if not hasattr(obj, \"__len__\"):\n+                return f\"'{obj}'\"\n+            elif len(obj) == 1:\n+                return f\"'{obj[0]}'\"\n+            else:\n+                return str(obj)\n \n         if not self._required_columns_enabled:\n             return\n@@ -76,9 +83,10 @@ def _check_required_columns(self):\n \n             elif self.colnames[:len(required_columns)] != required_columns:\n \n-                raise ValueError(\"{} object is invalid - expected '{}' \"\n-                                 \"as the first column{} but found '{}'\"\n-                                 .format(self.__class__.__name__, required_columns[0], plural, self.colnames[0]))\n+                raise ValueError(\"{} object is invalid - expected {} \"\n+                                 \"as the first column{} but found {}\"\n+                                 .format(self.__class__.__name__, as_scalar_or_list_str(required_columns),\n+                                            plural, as_scalar_or_list_str(self.colnames[:len(required_columns)])))\n \n             if (self._required_columns_relax\n                     and self._required_columns == self.colnames[:len(self._required_columns)]):\n"}
{"instance_id": "astropy__astropy-13842", "model_name_or_path": "gold", "model_patch": "diff --git a/astropy/table/table.py b/astropy/table/table.py\n--- a/astropy/table/table.py\n+++ b/astropy/table/table.py\n@@ -1264,8 +1264,10 @@ def _convert_data_to_col(self, data, copy=True, default_name=None, dtype=None, n\n \n         elif data_is_mixin:\n             # Copy the mixin column attributes if they exist since the copy below\n-            # may not get this attribute.\n-            col = col_copy(data, copy_indices=self._init_indices) if copy else data\n+            # may not get this attribute. If not copying, take a slice\n+            # to ensure we get a new instance and we do not share metadata\n+            # like info.\n+            col = col_copy(data, copy_indices=self._init_indices) if copy else data[:]\n             col.info.name = name\n             return col\n \ndiff --git a/astropy/table/table_helpers.py b/astropy/table/table_helpers.py\n--- a/astropy/table/table_helpers.py\n+++ b/astropy/table/table_helpers.py\n@@ -168,8 +168,8 @@ class ArrayWrapper:\n     \"\"\"\n     info = ArrayWrapperInfo()\n \n-    def __init__(self, data):\n-        self.data = np.array(data)\n+    def __init__(self, data, copy=True):\n+        self.data = np.array(data, copy=copy)\n         if 'info' in getattr(data, '__dict__', ()):\n             self.info = data.info\n \n@@ -177,7 +177,7 @@ def __getitem__(self, item):\n         if isinstance(item, (int, np.integer)):\n             out = self.data[item]\n         else:\n-            out = self.__class__(self.data[item])\n+            out = self.__class__(self.data[item], copy=False)\n             if 'info' in self.__dict__:\n                 out.info = self.info\n         return out\n"}
{"instance_id": "astropy__astropy-14590", "model_name_or_path": "gold", "model_patch": "diff --git a/astropy/utils/masked/core.py b/astropy/utils/masked/core.py\n--- a/astropy/utils/masked/core.py\n+++ b/astropy/utils/masked/core.py\n@@ -671,20 +671,35 @@ def __ne__(self, other):\n         )\n         return result.any(axis=-1)\n \n-    def _combine_masks(self, masks, out=None):\n+    def _combine_masks(self, masks, out=None, where=True, copy=True):\n+        \"\"\"Combine masks, possibly storing it in some output.\n+\n+        Parameters\n+        ----------\n+        masks : tuple of array of bool or None\n+            Input masks.  Any that are `None` or `False` are ignored.\n+            Should broadcast to each other.\n+        out : output mask array, optional\n+            Possible output array to hold the result.\n+        where : array of bool, optional\n+            Which elements of the output array to fill.\n+        copy : bool optional\n+            Whether to ensure a copy is made. Only relevant if a single\n+            input mask is not `None`, and ``out`` is not given.\n+        \"\"\"\n         masks = [m for m in masks if m is not None and m is not False]\n         if not masks:\n             return False\n         if len(masks) == 1:\n             if out is None:\n-                return masks[0].copy()\n+                return masks[0].copy() if copy else masks[0]\n             else:\n-                np.copyto(out, masks[0])\n+                np.copyto(out, masks[0], where=where)\n                 return out\n \n-        out = np.logical_or(masks[0], masks[1], out=out)\n+        out = np.logical_or(masks[0], masks[1], out=out, where=where)\n         for mask in masks[2:]:\n-            np.logical_or(out, mask, out=out)\n+            np.logical_or(out, mask, out=out, where=where)\n         return out\n \n     def __array_ufunc__(self, ufunc, method, *inputs, **kwargs):\n@@ -701,6 +716,15 @@ def __array_ufunc__(self, ufunc, method, *inputs, **kwargs):\n                 elif out_mask is None:\n                     out_mask = m\n \n+        # TODO: where is only needed for __call__ and reduce;\n+        # this is very fast, but still worth separating out?\n+        where = kwargs.pop(\"where\", True)\n+        if where is True:\n+            where_unmasked = True\n+            where_mask = None\n+        else:\n+            where_unmasked, where_mask = self._get_data_and_mask(where)\n+\n         unmasked, masks = self._get_data_and_masks(*inputs)\n \n         if ufunc.signature:\n@@ -731,7 +755,7 @@ def __array_ufunc__(self, ufunc, method, *inputs, **kwargs):\n                         else np.logical_or.reduce(mask1)\n                     )\n \n-                mask = self._combine_masks(masks, out=out_mask)\n+                mask = self._combine_masks(masks, out=out_mask, copy=False)\n \n             else:\n                 # Parse signature with private numpy function. Note it\n@@ -769,7 +793,11 @@ def __array_ufunc__(self, ufunc, method, *inputs, **kwargs):\n \n         elif method == \"__call__\":\n             # Regular ufunc call.\n-            mask = self._combine_masks(masks, out=out_mask)\n+            # Combine the masks from the input, possibly selecting elements.\n+            mask = self._combine_masks(masks, out=out_mask, where=where_unmasked)\n+            # If relevant, also mask output elements for which where was masked.\n+            if where_mask is not None:\n+                mask |= where_mask\n \n         elif method == \"outer\":\n             # Must have two arguments; adjust masks as will be done for data.\n@@ -779,51 +807,50 @@ def __array_ufunc__(self, ufunc, method, *inputs, **kwargs):\n \n         elif method in {\"reduce\", \"accumulate\"}:\n             # Reductions like np.add.reduce (sum).\n-            if masks[0] is not None:\n+            # Treat any masked where as if the input element was masked.\n+            mask = self._combine_masks((masks[0], where_mask), copy=False)\n+            if mask is not False:\n                 # By default, we simply propagate masks, since for\n                 # things like np.sum, it makes no sense to do otherwise.\n                 # Individual methods need to override as needed.\n-                # TODO: take care of 'out' too?\n                 if method == \"reduce\":\n                     axis = kwargs.get(\"axis\", None)\n                     keepdims = kwargs.get(\"keepdims\", False)\n-                    where = kwargs.get(\"where\", True)\n                     mask = np.logical_or.reduce(\n-                        masks[0],\n-                        where=where,\n+                        mask,\n+                        where=where_unmasked,\n                         axis=axis,\n                         keepdims=keepdims,\n                         out=out_mask,\n                     )\n-                    if where is not True:\n-                        # Mask also whole rows that were not selected by where,\n-                        # so would have been left as unmasked above.\n-                        mask |= np.logical_and.reduce(\n-                            masks[0], where=where, axis=axis, keepdims=keepdims\n+                    if where_unmasked is not True:\n+                        # Mask also whole rows in which no elements were selected;\n+                        # those will have been left as unmasked above.\n+                        mask |= ~np.logical_or.reduce(\n+                            where_unmasked, axis=axis, keepdims=keepdims\n                         )\n \n                 else:\n                     # Accumulate\n                     axis = kwargs.get(\"axis\", 0)\n-                    mask = np.logical_or.accumulate(masks[0], axis=axis, out=out_mask)\n+                    mask = np.logical_or.accumulate(mask, axis=axis, out=out_mask)\n \n-            elif out is not None:\n-                mask = False\n-\n-            else:  # pragma: no cover\n+            elif out is None:\n                 # Can only get here if neither input nor output was masked, but\n-                # perhaps axis or where was masked (in NUMPY_LT_1_21 this is\n-                # possible).  We don't support this.\n+                # perhaps where was masked (possible in \"not NUMPY_LT_1_25\" and\n+                # in NUMPY_LT_1_21 (latter also allowed axis).\n+                # We don't support this.\n                 return NotImplemented\n \n         elif method in {\"reduceat\", \"at\"}:  # pragma: no cover\n-            # TODO: implement things like np.add.accumulate (used for cumsum).\n             raise NotImplementedError(\n                 \"masked instances cannot yet deal with 'reduceat' or 'at'.\"\n             )\n \n         if out_unmasked is not None:\n             kwargs[\"out\"] = out_unmasked\n+        if where_unmasked is not True:\n+            kwargs[\"where\"] = where_unmasked\n         result = getattr(ufunc, method)(*unmasked, **kwargs)\n \n         if result is None:  # pragma: no cover\n"}
{"instance_id": "astropy__astropy-14995", "model_name_or_path": "gold", "model_patch": "diff --git a/astropy/nddata/mixins/ndarithmetic.py b/astropy/nddata/mixins/ndarithmetic.py\n--- a/astropy/nddata/mixins/ndarithmetic.py\n+++ b/astropy/nddata/mixins/ndarithmetic.py\n@@ -520,10 +520,10 @@ def _arithmetic_mask(self, operation, operand, handle_mask, axis=None, **kwds):\n         elif self.mask is None and operand is not None:\n             # Make a copy so there is no reference in the result.\n             return deepcopy(operand.mask)\n-        elif operand is None:\n+        elif operand.mask is None:\n             return deepcopy(self.mask)\n         else:\n-            # Now lets calculate the resulting mask (operation enforces copy)\n+            # Now let's calculate the resulting mask (operation enforces copy)\n             return handle_mask(self.mask, operand.mask, **kwds)\n \n     def _arithmetic_wcs(self, operation, operand, compare_wcs, **kwds):\n"}
{"instance_id": "django__django-5158", "model_name_or_path": "gold", "model_patch": "diff --git a/django/core/management/__init__.py b/django/core/management/__init__.py\n--- a/django/core/management/__init__.py\n+++ b/django/core/management/__init__.py\n@@ -177,8 +177,14 @@ def fetch_command(self, subcommand):\n         try:\n             app_name = commands[subcommand]\n         except KeyError:\n-            # This might trigger ImproperlyConfigured (masked in get_commands)\n-            settings.INSTALLED_APPS\n+            if os.environ.get('DJANGO_SETTINGS_MODULE'):\n+                # If `subcommand` is missing due to misconfigured settings, the\n+                # following line will retrigger an ImproperlyConfigured exception\n+                # (get_commands() swallows the original one) so the user is\n+                # informed about it.\n+                settings.INSTALLED_APPS\n+            else:\n+                sys.stderr.write(\"No Django settings specified.\\n\")\n             sys.stderr.write(\"Unknown command: %r\\nType '%s help' for usage.\\n\" %\n                 (subcommand, self.prog_name))\n             sys.exit(1)\n"}
{"instance_id": "django__django-5470", "model_name_or_path": "gold", "model_patch": "diff --git a/django/__init__.py b/django/__init__.py\n--- a/django/__init__.py\n+++ b/django/__init__.py\n@@ -1,3 +1,5 @@\n+from __future__ import unicode_literals\n+\n from django.utils.version import get_version\n \n VERSION = (1, 10, 0, 'alpha', 0)\n@@ -5,14 +7,21 @@\n __version__ = get_version(VERSION)\n \n \n-def setup():\n+def setup(set_prefix=True):\n     \"\"\"\n     Configure the settings (this happens as a side effect of accessing the\n     first setting), configure logging and populate the app registry.\n+    Set the thread-local urlresolvers script prefix if `set_prefix` is True.\n     \"\"\"\n     from django.apps import apps\n     from django.conf import settings\n+    from django.core.urlresolvers import set_script_prefix\n+    from django.utils.encoding import force_text\n     from django.utils.log import configure_logging\n \n     configure_logging(settings.LOGGING_CONFIG, settings.LOGGING)\n+    if set_prefix:\n+        set_script_prefix(\n+            '/' if settings.FORCE_SCRIPT_NAME is None else force_text(settings.FORCE_SCRIPT_NAME)\n+        )\n     apps.populate(settings.INSTALLED_APPS)\ndiff --git a/django/core/wsgi.py b/django/core/wsgi.py\n--- a/django/core/wsgi.py\n+++ b/django/core/wsgi.py\n@@ -10,5 +10,5 @@ def get_wsgi_application():\n     Allows us to avoid making django.core.handlers.WSGIHandler public API, in\n     case the internal WSGI implementation changes or moves in the future.\n     \"\"\"\n-    django.setup()\n+    django.setup(set_prefix=False)\n     return WSGIHandler()\n"}
{"instance_id": "django__django-9003", "model_name_or_path": "gold", "model_patch": "diff --git a/django/db/models/sql/query.py b/django/db/models/sql/query.py\n--- a/django/db/models/sql/query.py\n+++ b/django/db/models/sql/query.py\n@@ -133,7 +133,7 @@ def __init__(self, model, where=WhereNode):\n         # types they are. The key is the alias of the joined table (possibly\n         # the table name) and the value is a Join-like object (see\n         # sql.datastructures.Join for more information).\n-        self.alias_map = {}\n+        self.alias_map = OrderedDict()\n         # Sometimes the query contains references to aliases in outer queries (as\n         # a result of split_exclude). Correct alias quoting needs to know these\n         # aliases too.\n"}
{"instance_id": "django__django-8961", "model_name_or_path": "gold", "model_patch": "diff --git a/django/core/management/__init__.py b/django/core/management/__init__.py\n--- a/django/core/management/__init__.py\n+++ b/django/core/management/__init__.py\n@@ -149,6 +149,8 @@ class ManagementUtility:\n     def __init__(self, argv=None):\n         self.argv = argv or sys.argv[:]\n         self.prog_name = os.path.basename(self.argv[0])\n+        if self.prog_name == '__main__.py':\n+            self.prog_name = 'python -m django'\n         self.settings_exception = None\n \n     def main_help_text(self, commands_only=False):\n"}
{"instance_id": "django__django-9871", "model_name_or_path": "gold", "model_patch": "diff --git a/django/core/management/base.py b/django/core/management/base.py\n--- a/django/core/management/base.py\n+++ b/django/core/management/base.py\n@@ -228,6 +228,9 @@ def create_parser(self, prog_name, subcommand):\n             self, prog=\"%s %s\" % (os.path.basename(prog_name), subcommand),\n             description=self.help or None,\n         )\n+        # Add command-specific arguments first so that they appear in the\n+        # --help output before arguments common to all commands.\n+        self.add_arguments(parser)\n         parser.add_argument('--version', action='version', version=self.get_version())\n         parser.add_argument(\n             '-v', '--verbosity', action='store', dest='verbosity', default=1,\n@@ -251,7 +254,6 @@ def create_parser(self, prog_name, subcommand):\n             '--no-color', action='store_true', dest='no_color',\n             help=\"Don't colorize the command output.\",\n         )\n-        self.add_arguments(parser)\n         return parser\n \n     def add_arguments(self, parser):\n"}
{"instance_id": "django__django-10426", "model_name_or_path": "gold", "model_patch": "diff --git a/django/core/management/commands/showmigrations.py b/django/core/management/commands/showmigrations.py\n--- a/django/core/management/commands/showmigrations.py\n+++ b/django/core/management/commands/showmigrations.py\n@@ -1,4 +1,7 @@\n-from django.core.management.base import BaseCommand, CommandError\n+import sys\n+\n+from django.apps import apps\n+from django.core.management.base import BaseCommand\n from django.db import DEFAULT_DB_ALIAS, connections\n from django.db.migrations.loader import MigrationLoader\n \n@@ -45,12 +48,15 @@ def handle(self, *args, **options):\n             return self.show_list(connection, options['app_label'])\n \n     def _validate_app_names(self, loader, app_names):\n-        invalid_apps = []\n+        has_bad_names = False\n         for app_name in app_names:\n-            if app_name not in loader.migrated_apps:\n-                invalid_apps.append(app_name)\n-        if invalid_apps:\n-            raise CommandError('No migrations present for: %s' % (', '.join(sorted(invalid_apps))))\n+            try:\n+                apps.get_app_config(app_name)\n+            except LookupError as err:\n+                self.stderr.write(str(err))\n+                has_bad_names = True\n+        if has_bad_names:\n+            sys.exit(2)\n \n     def show_list(self, connection, app_names=None):\n         \"\"\"\n@@ -129,3 +135,5 @@ def print_deps(node):\n                 self.stdout.write(\"[X]  %s.%s%s\" % (node.key[0], node.key[1], deps))\n             else:\n                 self.stdout.write(\"[ ]  %s.%s%s\" % (node.key[0], node.key[1], deps))\n+        if not plan:\n+            self.stdout.write('(no migrations)', self.style.ERROR)\n"}
{"instance_id": "django__django-13199", "model_name_or_path": "gold", "model_patch": "diff --git a/django/contrib/messages/storage/cookie.py b/django/contrib/messages/storage/cookie.py\n--- a/django/contrib/messages/storage/cookie.py\n+++ b/django/contrib/messages/storage/cookie.py\n@@ -89,7 +89,11 @@ def _update_cookie(self, encoded_data, response):\n                 samesite=settings.SESSION_COOKIE_SAMESITE,\n             )\n         else:\n-            response.delete_cookie(self.cookie_name, domain=settings.SESSION_COOKIE_DOMAIN)\n+            response.delete_cookie(\n+                self.cookie_name,\n+                domain=settings.SESSION_COOKIE_DOMAIN,\n+                samesite=settings.SESSION_COOKIE_SAMESITE,\n+            )\n \n     def _store(self, messages, response, remove_oldest=True, *args, **kwargs):\n         \"\"\"\ndiff --git a/django/contrib/sessions/middleware.py b/django/contrib/sessions/middleware.py\n--- a/django/contrib/sessions/middleware.py\n+++ b/django/contrib/sessions/middleware.py\n@@ -38,6 +38,7 @@ def process_response(self, request, response):\n                 settings.SESSION_COOKIE_NAME,\n                 path=settings.SESSION_COOKIE_PATH,\n                 domain=settings.SESSION_COOKIE_DOMAIN,\n+                samesite=settings.SESSION_COOKIE_SAMESITE,\n             )\n             patch_vary_headers(response, ('Cookie',))\n         else:\ndiff --git a/django/http/response.py b/django/http/response.py\n--- a/django/http/response.py\n+++ b/django/http/response.py\n@@ -209,13 +209,13 @@ def set_signed_cookie(self, key, value, salt='', **kwargs):\n         value = signing.get_cookie_signer(salt=key + salt).sign(value)\n         return self.set_cookie(key, value, **kwargs)\n \n-    def delete_cookie(self, key, path='/', domain=None):\n+    def delete_cookie(self, key, path='/', domain=None, samesite=None):\n         # Most browsers ignore the Set-Cookie header if the cookie name starts\n         # with __Host- or __Secure- and the cookie doesn't use the secure flag.\n         secure = key.startswith(('__Secure-', '__Host-'))\n         self.set_cookie(\n             key, max_age=0, path=path, domain=domain, secure=secure,\n-            expires='Thu, 01 Jan 1970 00:00:00 GMT',\n+            expires='Thu, 01 Jan 1970 00:00:00 GMT', samesite=samesite,\n         )\n \n     # Common methods used by subclasses\n"}
{"instance_id": "django__django-12869", "model_name_or_path": "gold", "model_patch": "diff --git a/django/contrib/staticfiles/apps.py b/django/contrib/staticfiles/apps.py\n--- a/django/contrib/staticfiles/apps.py\n+++ b/django/contrib/staticfiles/apps.py\n@@ -10,4 +10,4 @@ class StaticFilesConfig(AppConfig):\n     ignore_patterns = ['CVS', '.*', '*~']\n \n     def ready(self):\n-        checks.register(check_finders, 'staticfiles')\n+        checks.register(check_finders, checks.Tags.staticfiles)\ndiff --git a/django/contrib/staticfiles/management/commands/collectstatic.py b/django/contrib/staticfiles/management/commands/collectstatic.py\n--- a/django/contrib/staticfiles/management/commands/collectstatic.py\n+++ b/django/contrib/staticfiles/management/commands/collectstatic.py\n@@ -3,6 +3,7 @@\n from django.apps import apps\n from django.contrib.staticfiles.finders import get_finders\n from django.contrib.staticfiles.storage import staticfiles_storage\n+from django.core.checks import Tags\n from django.core.files.storage import FileSystemStorage\n from django.core.management.base import BaseCommand, CommandError\n from django.core.management.color import no_style\n@@ -35,6 +36,10 @@ def local(self):\n         return True\n \n     def add_arguments(self, parser):\n+        parser.add_argument(\n+            '--skip-checks', action='store_true',\n+            help='Skip system checks.',\n+        )\n         parser.add_argument(\n             '--noinput', '--no-input', action='store_false', dest='interactive',\n             help=\"Do NOT prompt the user for input of any kind.\",\n@@ -146,6 +151,8 @@ def collect(self):\n \n     def handle(self, **options):\n         self.set_options(**options)\n+        if not options['skip_checks']:\n+            self.check(tags=[Tags.staticfiles])\n \n         message = ['\\n']\n         if self.dry_run:\ndiff --git a/django/core/checks/registry.py b/django/core/checks/registry.py\n--- a/django/core/checks/registry.py\n+++ b/django/core/checks/registry.py\n@@ -15,6 +15,7 @@ class Tags:\n     models = 'models'\n     security = 'security'\n     signals = 'signals'\n+    staticfiles = 'staticfiles'\n     templates = 'templates'\n     translation = 'translation'\n     urls = 'urls'\n"}
{"instance_id": "django__django-14267", "model_name_or_path": "gold", "model_patch": "diff --git a/django/db/models/query_utils.py b/django/db/models/query_utils.py\n--- a/django/db/models/query_utils.py\n+++ b/django/db/models/query_utils.py\n@@ -112,14 +112,10 @@ def deconstruct(self):\n         path = '%s.%s' % (self.__class__.__module__, self.__class__.__name__)\n         if path.startswith('django.db.models.query_utils'):\n             path = path.replace('django.db.models.query_utils', 'django.db.models')\n-        args, kwargs = (), {}\n-        if len(self.children) == 1 and not isinstance(self.children[0], Q):\n-            child = self.children[0]\n-            kwargs = {child[0]: child[1]}\n-        else:\n-            args = tuple(self.children)\n-            if self.connector != self.default:\n-                kwargs = {'_connector': self.connector}\n+        args = tuple(self.children)\n+        kwargs = {}\n+        if self.connector != self.default:\n+            kwargs['_connector'] = self.connector\n         if self.negated:\n             kwargs['_negated'] = True\n         return path, args, kwargs\n"}
{"instance_id": "django__django-14855", "model_name_or_path": "gold", "model_patch": "diff --git a/django/contrib/admin/helpers.py b/django/contrib/admin/helpers.py\n--- a/django/contrib/admin/helpers.py\n+++ b/django/contrib/admin/helpers.py\n@@ -209,7 +209,11 @@ def get_admin_url(self, remote_field, remote_obj):\n             remote_field.model._meta.model_name,\n         )\n         try:\n-            url = reverse(url_name, args=[quote(remote_obj.pk)])\n+            url = reverse(\n+                url_name,\n+                args=[quote(remote_obj.pk)],\n+                current_app=self.model_admin.admin_site.name,\n+            )\n             return format_html('<a href=\"{}\">{}</a>', url, remote_obj)\n         except NoReverseMatch:\n             return str(remote_obj)\n"}
{"instance_id": "django__django-15695", "model_name_or_path": "gold", "model_patch": "diff --git a/django/db/migrations/operations/models.py b/django/db/migrations/operations/models.py\n--- a/django/db/migrations/operations/models.py\n+++ b/django/db/migrations/operations/models.py\n@@ -960,6 +960,9 @@ def database_forwards(self, app_label, schema_editor, from_state, to_state):\n         else:\n             from_model_state = from_state.models[app_label, self.model_name_lower]\n             old_index = from_model_state.get_index_by_name(self.old_name)\n+        # Don't alter when the index name is not changed.\n+        if old_index.name == self.new_name:\n+            return\n \n         to_model_state = to_state.models[app_label, self.model_name_lower]\n         new_index = to_model_state.get_index_by_name(self.new_name)\n"}
{"instance_id": "django__django-16429", "model_name_or_path": "gold", "model_patch": "diff --git a/django/utils/timesince.py b/django/utils/timesince.py\n--- a/django/utils/timesince.py\n+++ b/django/utils/timesince.py\n@@ -97,6 +97,7 @@ def timesince(d, now=None, reversed=False, time_strings=None, depth=2):\n             d.hour,\n             d.minute,\n             d.second,\n+            tzinfo=d.tzinfo,\n         )\n     else:\n         pivot = d\n"}
{"instance_id": "django__django-17087", "model_name_or_path": "gold", "model_patch": "diff --git a/django/db/migrations/serializer.py b/django/db/migrations/serializer.py\n--- a/django/db/migrations/serializer.py\n+++ b/django/db/migrations/serializer.py\n@@ -168,7 +168,7 @@ def serialize(self):\n         ):\n             klass = self.value.__self__\n             module = klass.__module__\n-            return \"%s.%s.%s\" % (module, klass.__name__, self.value.__name__), {\n+            return \"%s.%s.%s\" % (module, klass.__qualname__, self.value.__name__), {\n                 \"import %s\" % module\n             }\n         # Further error checking\n"}
{"instance_id": "matplotlib__matplotlib-23742", "model_name_or_path": "gold", "model_patch": "diff --git a/examples/user_interfaces/embedding_webagg_sgskip.py b/examples/user_interfaces/embedding_webagg_sgskip.py\n--- a/examples/user_interfaces/embedding_webagg_sgskip.py\n+++ b/examples/user_interfaces/embedding_webagg_sgskip.py\n@@ -30,7 +30,7 @@\n \n \n import matplotlib as mpl\n-from matplotlib.backends.backend_webagg_core import (\n+from matplotlib.backends.backend_webagg import (\n     FigureManagerWebAgg, new_figure_manager_given_figure)\n from matplotlib.figure import Figure\n \ndiff --git a/lib/matplotlib/backends/backend_webagg_core.py b/lib/matplotlib/backends/backend_webagg_core.py\n--- a/lib/matplotlib/backends/backend_webagg_core.py\n+++ b/lib/matplotlib/backends/backend_webagg_core.py\n@@ -427,7 +427,9 @@ def set_history_buttons(self):\n \n \n class FigureManagerWebAgg(backend_bases.FigureManagerBase):\n-    _toolbar2_class = ToolbarCls = NavigationToolbar2WebAgg\n+    # This must be None to not break ipympl\n+    _toolbar2_class = None\n+    ToolbarCls = NavigationToolbar2WebAgg\n \n     def __init__(self, canvas, num):\n         self.web_sockets = set()\n"}
{"instance_id": "matplotlib__matplotlib-25129", "model_name_or_path": "gold", "model_patch": "diff --git a/examples/event_handling/cursor_demo.py b/examples/event_handling/cursor_demo.py\n--- a/examples/event_handling/cursor_demo.py\n+++ b/examples/event_handling/cursor_demo.py\n@@ -28,6 +28,8 @@\n import matplotlib.pyplot as plt\n import numpy as np\n \n+from matplotlib.backend_bases import MouseEvent\n+\n \n class Cursor:\n     \"\"\"\n@@ -71,6 +73,11 @@ def on_mouse_move(self, event):\n cursor = Cursor(ax)\n fig.canvas.mpl_connect('motion_notify_event', cursor.on_mouse_move)\n \n+# Simulate a mouse move to (0.5, 0.5), needed for online docs\n+t = ax.transData\n+MouseEvent(\n+    \"motion_notify_event\", ax.figure.canvas, *t.transform((0.5, 0.5))\n+)._process()\n \n # %%\n # Faster redrawing using blitting\n@@ -85,6 +92,7 @@ def on_mouse_move(self, event):\n # created whenever the figure changes. This is achieved by connecting to the\n # ``'draw_event'``.\n \n+\n class BlittedCursor:\n     \"\"\"\n     A cross-hair cursor using blitting for faster redraw.\n@@ -152,6 +160,11 @@ def on_mouse_move(self, event):\n blitted_cursor = BlittedCursor(ax)\n fig.canvas.mpl_connect('motion_notify_event', blitted_cursor.on_mouse_move)\n \n+# Simulate a mouse move to (0.5, 0.5), needed for online docs\n+t = ax.transData\n+MouseEvent(\n+    \"motion_notify_event\", ax.figure.canvas, *t.transform((0.5, 0.5))\n+)._process()\n \n # %%\n # Snapping to data points\n@@ -165,6 +178,7 @@ def on_mouse_move(self, event):\n # the lag due to many redraws. Of course, blitting could still be added on top\n # for additional speedup.\n \n+\n class SnappingCursor:\n     \"\"\"\n     A cross-hair cursor that snaps to the data point of a line, which is\n@@ -218,4 +232,11 @@ def on_mouse_move(self, event):\n line, = ax.plot(x, y, 'o')\n snap_cursor = SnappingCursor(ax, line)\n fig.canvas.mpl_connect('motion_notify_event', snap_cursor.on_mouse_move)\n+\n+# Simulate a mouse move to (0.5, 0.5), needed for online docs\n+t = ax.transData\n+MouseEvent(\n+    \"motion_notify_event\", ax.figure.canvas, *t.transform((0.5, 0.5))\n+)._process()\n+\n plt.show()\ndiff --git a/examples/widgets/annotated_cursor.py b/examples/widgets/annotated_cursor.py\n--- a/examples/widgets/annotated_cursor.py\n+++ b/examples/widgets/annotated_cursor.py\n@@ -24,6 +24,8 @@\n import numpy as np\n import matplotlib.pyplot as plt\n \n+from matplotlib.backend_bases import MouseEvent\n+\n \n class AnnotatedCursor(Cursor):\n     \"\"\"\n@@ -312,6 +314,12 @@ def _update(self):\n     color='red',\n     linewidth=2)\n \n+# Simulate a mouse move to (-2, 10), needed for online docs\n+t = ax.transData\n+MouseEvent(\n+    \"motion_notify_event\", ax.figure.canvas, *t.transform((-2, 10))\n+)._process()\n+\n plt.show()\n \n # %%\n@@ -339,4 +347,10 @@ def _update(self):\n     useblit=True,\n     color='red', linewidth=2)\n \n+# Simulate a mouse move to (-2, 10), needed for online docs\n+t = ax.transData\n+MouseEvent(\n+    \"motion_notify_event\", ax.figure.canvas, *t.transform((-2, 10))\n+)._process()\n+\n plt.show()\ndiff --git a/lib/matplotlib/widgets.py b/lib/matplotlib/widgets.py\n--- a/lib/matplotlib/widgets.py\n+++ b/lib/matplotlib/widgets.py\n@@ -1953,8 +1953,8 @@ def __init__(self, ax, horizOn=True, vertOn=True, useblit=False,\n                  **lineprops):\n         super().__init__(ax)\n \n-        self.connect_event('motion_notify_event', self._onmove)\n-        self.connect_event('draw_event', self._clear)\n+        self.connect_event('motion_notify_event', self.onmove)\n+        self.connect_event('draw_event', self.clear)\n \n         self.visible = True\n         self.horizOn = horizOn\n@@ -1967,29 +1967,16 @@ def __init__(self, ax, horizOn=True, vertOn=True, useblit=False,\n         self.linev = ax.axvline(ax.get_xbound()[0], visible=False, **lineprops)\n \n         self.background = None\n-        self._needclear = False\n-\n-    needclear = _api.deprecate_privatize_attribute(\"3.7\")\n+        self.needclear = False\n \n-    @_api.deprecated('3.7')\n     def clear(self, event):\n-        \"\"\"Internal event handler to clear the cursor.\"\"\"\n-        self._clear(event)\n-        if self.ignore(event):\n-            return\n-        self.linev.set_visible(False)\n-        self.lineh.set_visible(False)\n-\n-    def _clear(self, event):\n         \"\"\"Internal event handler to clear the cursor.\"\"\"\n         if self.ignore(event):\n             return\n         if self.useblit:\n             self.background = self.canvas.copy_from_bbox(self.ax.bbox)\n \n-    onmove = _api.deprecate_privatize_attribute('3.7')\n-\n-    def _onmove(self, event):\n+    def onmove(self, event):\n         \"\"\"Internal event handler to draw the cursor when the mouse moves.\"\"\"\n         if self.ignore(event):\n             return\n@@ -1999,11 +1986,11 @@ def _onmove(self, event):\n             self.linev.set_visible(False)\n             self.lineh.set_visible(False)\n \n-            if self._needclear:\n+            if self.needclear:\n                 self.canvas.draw()\n-                self._needclear = False\n+                self.needclear = False\n             return\n-        self._needclear = True\n+        self.needclear = True\n \n         self.linev.set_xdata((event.xdata, event.xdata))\n         self.linev.set_visible(self.visible and self.vertOn)\n@@ -2106,8 +2093,8 @@ def connect(self):\n         \"\"\"Connect events.\"\"\"\n         for canvas, info in self._canvas_infos.items():\n             info[\"cids\"] = [\n-                canvas.mpl_connect('motion_notify_event', self._onmove),\n-                canvas.mpl_connect('draw_event', self._clear),\n+                canvas.mpl_connect('motion_notify_event', self.onmove),\n+                canvas.mpl_connect('draw_event', self.clear),\n             ]\n \n     def disconnect(self):\n@@ -2117,16 +2104,7 @@ def disconnect(self):\n                 canvas.mpl_disconnect(cid)\n             info[\"cids\"].clear()\n \n-    @_api.deprecated('3.7')\n     def clear(self, event):\n-        \"\"\"Clear the cursor.\"\"\"\n-        if self.ignore(event):\n-            return\n-        self._clear(event)\n-        for line in self.vlines + self.hlines:\n-            line.set_visible(False)\n-\n-    def _clear(self, event):\n         \"\"\"Clear the cursor.\"\"\"\n         if self.ignore(event):\n             return\n@@ -2134,9 +2112,7 @@ def _clear(self, event):\n             for canvas, info in self._canvas_infos.items():\n                 info[\"background\"] = canvas.copy_from_bbox(canvas.figure.bbox)\n \n-    onmove = _api.deprecate_privatize_attribute('3.7')\n-\n-    def _onmove(self, event):\n+    def onmove(self, event):\n         if (self.ignore(event)\n                 or event.inaxes not in self.axes\n                 or not event.canvas.widgetlock.available(self)):\n"}
{"instance_id": "matplotlib__matplotlib-26532", "model_name_or_path": "gold", "model_patch": "diff --git a/lib/mpl_toolkits/mplot3d/art3d.py b/lib/mpl_toolkits/mplot3d/art3d.py\n--- a/lib/mpl_toolkits/mplot3d/art3d.py\n+++ b/lib/mpl_toolkits/mplot3d/art3d.py\n@@ -905,7 +905,7 @@ def __init__(self, verts, *args, zsort='average', shade=False,\n                 kwargs['edgecolors'] = _shade_colors(\n                     edgecolors, normals, lightsource\n                 )\n-            if facecolors is None and edgecolors in None:\n+            if facecolors is None and edgecolors is None:\n                 raise ValueError(\n                     \"You must provide facecolors, edgecolors, or both for \"\n                     \"shade to work.\")\n"}
{"instance_id": "matplotlib__matplotlib-14623", "model_name_or_path": "gold", "model_patch": "diff --git a/lib/matplotlib/axes/_base.py b/lib/matplotlib/axes/_base.py\n--- a/lib/matplotlib/axes/_base.py\n+++ b/lib/matplotlib/axes/_base.py\n@@ -3262,8 +3262,11 @@ def set_xlim(self, left=None, right=None, emit=True, auto=False,\n             cbook._warn_external(\n                 f\"Attempting to set identical left == right == {left} results \"\n                 f\"in singular transformations; automatically expanding.\")\n+        swapped = left > right\n         left, right = self.xaxis.get_major_locator().nonsingular(left, right)\n         left, right = self.xaxis.limit_range_for_scale(left, right)\n+        if swapped:\n+            left, right = right, left\n \n         self.viewLim.intervalx = (left, right)\n         if auto is not None:\n@@ -3642,8 +3645,11 @@ def set_ylim(self, bottom=None, top=None, emit=True, auto=False,\n                 f\"Attempting to set identical bottom == top == {bottom} \"\n                 f\"results in singular transformations; automatically \"\n                 f\"expanding.\")\n+        swapped = bottom > top\n         bottom, top = self.yaxis.get_major_locator().nonsingular(bottom, top)\n         bottom, top = self.yaxis.limit_range_for_scale(bottom, top)\n+        if swapped:\n+            bottom, top = top, bottom\n \n         self.viewLim.intervaly = (bottom, top)\n         if auto is not None:\ndiff --git a/lib/matplotlib/ticker.py b/lib/matplotlib/ticker.py\n--- a/lib/matplotlib/ticker.py\n+++ b/lib/matplotlib/ticker.py\n@@ -1521,8 +1521,8 @@ def raise_if_exceeds(self, locs):\n         return locs\n \n     def nonsingular(self, v0, v1):\n-        \"\"\"Modify the endpoints of a range as needed to avoid singularities.\"\"\"\n-        return mtransforms.nonsingular(v0, v1, increasing=False, expander=.05)\n+        \"\"\"Expand a range as needed to avoid singularities.\"\"\"\n+        return mtransforms.nonsingular(v0, v1, expander=.05)\n \n     def view_limits(self, vmin, vmax):\n         \"\"\"\ndiff --git a/lib/mpl_toolkits/mplot3d/axes3d.py b/lib/mpl_toolkits/mplot3d/axes3d.py\n--- a/lib/mpl_toolkits/mplot3d/axes3d.py\n+++ b/lib/mpl_toolkits/mplot3d/axes3d.py\n@@ -623,8 +623,11 @@ def set_xlim3d(self, left=None, right=None, emit=True, auto=False,\n             cbook._warn_external(\n                 f\"Attempting to set identical left == right == {left} results \"\n                 f\"in singular transformations; automatically expanding.\")\n+        swapped = left > right\n         left, right = self.xaxis.get_major_locator().nonsingular(left, right)\n         left, right = self.xaxis.limit_range_for_scale(left, right)\n+        if swapped:\n+            left, right = right, left\n         self.xy_viewLim.intervalx = (left, right)\n \n         if auto is not None:\n@@ -681,8 +684,11 @@ def set_ylim3d(self, bottom=None, top=None, emit=True, auto=False,\n                 f\"Attempting to set identical bottom == top == {bottom} \"\n                 f\"results in singular transformations; automatically \"\n                 f\"expanding.\")\n+        swapped = bottom > top\n         bottom, top = self.yaxis.get_major_locator().nonsingular(bottom, top)\n         bottom, top = self.yaxis.limit_range_for_scale(bottom, top)\n+        if swapped:\n+            bottom, top = top, bottom\n         self.xy_viewLim.intervaly = (bottom, top)\n \n         if auto is not None:\n@@ -739,8 +745,11 @@ def set_zlim3d(self, bottom=None, top=None, emit=True, auto=False,\n                 f\"Attempting to set identical bottom == top == {bottom} \"\n                 f\"results in singular transformations; automatically \"\n                 f\"expanding.\")\n+        swapped = bottom > top\n         bottom, top = self.zaxis.get_major_locator().nonsingular(bottom, top)\n         bottom, top = self.zaxis.limit_range_for_scale(bottom, top)\n+        if swapped:\n+            bottom, top = top, bottom\n         self.zz_viewLim.intervalx = (bottom, top)\n \n         if auto is not None:\n"}
{"instance_id": "matplotlib__matplotlib-17810", "model_name_or_path": "gold", "model_patch": "diff --git a/lib/matplotlib/animation.py b/lib/matplotlib/animation.py\n--- a/lib/matplotlib/animation.py\n+++ b/lib/matplotlib/animation.py\n@@ -1666,8 +1666,21 @@ def _init_draw(self):\n         # For blitting, the init_func should return a sequence of modified\n         # artists.\n         if self._init_func is None:\n-            self._draw_frame(next(self.new_frame_seq()))\n-\n+            try:\n+                frame_data = next(self.new_frame_seq())\n+            except StopIteration:\n+                # we can't start the iteration, it may have already been\n+                # exhausted by a previous save or just be 0 length.\n+                # warn and bail.\n+                warnings.warn(\n+                    \"Can not start iterating the frames for the initial draw. \"\n+                    \"This can be caused by passing in a 0 length sequence \"\n+                    \"for *frames*.\\n\\n\"\n+                    \"If you passed *frames* as a generator \"\n+                    \"it may be exhausted due to a previous display or save.\"\n+                )\n+                return\n+            self._draw_frame(frame_data)\n         else:\n             self._drawn_artists = self._init_func()\n             if self._blit:\n"}
{"instance_id": "matplotlib__matplotlib-19763", "model_name_or_path": "gold", "model_patch": "diff --git a/lib/matplotlib/widgets.py b/lib/matplotlib/widgets.py\n--- a/lib/matplotlib/widgets.py\n+++ b/lib/matplotlib/widgets.py\n@@ -1600,8 +1600,8 @@ def __init__(self, ax, horizOn=True, vertOn=True, useblit=False,\n                  **lineprops):\n         super().__init__(ax)\n \n-        self.connect_event('motion_notify_event', self.onmove)\n-        self.connect_event('draw_event', self.clear)\n+        self.connect_event('motion_notify_event', self._onmove)\n+        self.connect_event('draw_event', self._clear)\n \n         self.visible = True\n         self.horizOn = horizOn\n@@ -1616,16 +1616,25 @@ def __init__(self, ax, horizOn=True, vertOn=True, useblit=False,\n         self.background = None\n         self.needclear = False\n \n+    @_api.deprecated('3.5')\n     def clear(self, event):\n         \"\"\"Internal event handler to clear the cursor.\"\"\"\n+        self._clear(event)\n         if self.ignore(event):\n             return\n-        if self.useblit:\n-            self.background = self.canvas.copy_from_bbox(self.ax.bbox)\n         self.linev.set_visible(False)\n         self.lineh.set_visible(False)\n \n-    def onmove(self, event):\n+    def _clear(self, event):\n+        \"\"\"Internal event handler to clear the cursor.\"\"\"\n+        if self.ignore(event):\n+            return\n+        if self.useblit:\n+            self.background = self.canvas.copy_from_bbox(self.ax.bbox)\n+\n+    onmove = _api.deprecate_privatize_attribute('3.5')\n+\n+    def _onmove(self, event):\n         \"\"\"Internal event handler to draw the cursor when the mouse moves.\"\"\"\n         if self.ignore(event):\n             return\n@@ -1640,15 +1649,15 @@ def onmove(self, event):\n                 self.needclear = False\n             return\n         self.needclear = True\n-        if not self.visible:\n-            return\n+\n         self.linev.set_xdata((event.xdata, event.xdata))\n+        self.linev.set_visible(self.visible and self.vertOn)\n \n         self.lineh.set_ydata((event.ydata, event.ydata))\n-        self.linev.set_visible(self.visible and self.vertOn)\n         self.lineh.set_visible(self.visible and self.horizOn)\n \n-        self._update()\n+        if self.visible and (self.vertOn or self.horizOn):\n+            self._update()\n \n     def _update(self):\n         if self.useblit:\n@@ -1749,8 +1758,8 @@ def connect(self):\n         \"\"\"Connect events.\"\"\"\n         for canvas, info in self._canvas_infos.items():\n             info[\"cids\"] = [\n-                canvas.mpl_connect('motion_notify_event', self.onmove),\n-                canvas.mpl_connect('draw_event', self.clear),\n+                canvas.mpl_connect('motion_notify_event', self._onmove),\n+                canvas.mpl_connect('draw_event', self._clear),\n             ]\n \n     def disconnect(self):\n@@ -1760,24 +1769,31 @@ def disconnect(self):\n                 canvas.mpl_disconnect(cid)\n             info[\"cids\"].clear()\n \n+    @_api.deprecated('3.5')\n     def clear(self, event):\n+        \"\"\"Clear the cursor.\"\"\"\n+        if self.ignore(event):\n+            return\n+        self._clear(event)\n+        for line in self.vlines + self.hlines:\n+            line.set_visible(False)\n+\n+    def _clear(self, event):\n         \"\"\"Clear the cursor.\"\"\"\n         if self.ignore(event):\n             return\n         if self.useblit:\n             for canvas, info in self._canvas_infos.items():\n                 info[\"background\"] = canvas.copy_from_bbox(canvas.figure.bbox)\n-        for line in self.vlines + self.hlines:\n-            line.set_visible(False)\n \n-    def onmove(self, event):\n+    onmove = _api.deprecate_privatize_attribute('3.5')\n+\n+    def _onmove(self, event):\n         if (self.ignore(event)\n                 or event.inaxes not in self.axes\n                 or not event.canvas.widgetlock.available(self)):\n             return\n         self.needclear = True\n-        if not self.visible:\n-            return\n         if self.vertOn:\n             for line in self.vlines:\n                 line.set_xdata((event.xdata, event.xdata))\n@@ -1786,7 +1802,8 @@ def onmove(self, event):\n             for line in self.hlines:\n                 line.set_ydata((event.ydata, event.ydata))\n                 line.set_visible(self.visible)\n-        self._update()\n+        if self.visible and (self.vertOn or self.horizOn):\n+            self._update()\n \n     def _update(self):\n         if self.useblit:\n"}
{"instance_id": "matplotlib__matplotlib-21617", "model_name_or_path": "gold", "model_patch": "diff --git a/lib/matplotlib/backends/backend_ps.py b/lib/matplotlib/backends/backend_ps.py\n--- a/lib/matplotlib/backends/backend_ps.py\n+++ b/lib/matplotlib/backends/backend_ps.py\n@@ -429,7 +429,7 @@ def _get_clip_cmd(self, gc):\n             key = (path, id(trf))\n             custom_clip_cmd = self._clip_paths.get(key)\n             if custom_clip_cmd is None:\n-                custom_clip_cmd = \"c%x\" % len(self._clip_paths)\n+                custom_clip_cmd = \"c%d\" % len(self._clip_paths)\n                 self._pswriter.write(f\"\"\"\\\n /{custom_clip_cmd} {{\n {self._convert_path(path, trf, simplify=False)}\n@@ -570,7 +570,7 @@ def draw_path_collection(self, gc, master_transform, paths, all_transforms,\n         path_codes = []\n         for i, (path, transform) in enumerate(self._iter_collection_raw_paths(\n                 master_transform, paths, all_transforms)):\n-            name = 'p%x_%x' % (self._path_collection_id, i)\n+            name = 'p%d_%d' % (self._path_collection_id, i)\n             path_bytes = self._convert_path(path, transform, simplify=False)\n             self._pswriter.write(f\"\"\"\\\n /{name} {{\n"}
{"instance_id": "matplotlib__matplotlib-14043", "model_name_or_path": "gold", "model_patch": "diff --git a/lib/matplotlib/axes/_axes.py b/lib/matplotlib/axes/_axes.py\n--- a/lib/matplotlib/axes/_axes.py\n+++ b/lib/matplotlib/axes/_axes.py\n@@ -2291,6 +2291,14 @@ def bar(self, x, height, width=0.8, bottom=None, *, align=\"center\",\n         xerr = kwargs.pop('xerr', None)\n         yerr = kwargs.pop('yerr', None)\n         error_kw = kwargs.pop('error_kw', {})\n+        ezorder = error_kw.pop('zorder', None)\n+        if ezorder is None:\n+            ezorder = kwargs.get('zorder', None)\n+            if ezorder is not None:\n+                # If using the bar zorder, increment slightly to make sure\n+                # errorbars are drawn on top of bars\n+                ezorder += 0.01\n+        error_kw.setdefault('zorder', ezorder)\n         ecolor = kwargs.pop('ecolor', 'k')\n         capsize = kwargs.pop('capsize', rcParams[\"errorbar.capsize\"])\n         error_kw.setdefault('ecolor', ecolor)\n"}
{"instance_id": "mwaskom__seaborn-2766", "model_name_or_path": "gold", "model_patch": "diff --git a/seaborn/_core.py b/seaborn/_core.py\n--- a/seaborn/_core.py\n+++ b/seaborn/_core.py\n@@ -5,7 +5,6 @@\n from collections.abc import Iterable, Sequence, Mapping\n from numbers import Number\n from datetime import datetime\n-from distutils.version import LooseVersion\n \n import numpy as np\n import pandas as pd\n@@ -14,6 +13,7 @@\n from ._decorators import (\n     share_init_params_with_map,\n )\n+from .external.version import Version\n from .palettes import (\n     QUAL_PALETTES,\n     color_palette,\n@@ -1162,7 +1162,7 @@ def _attach(self, obj, allowed_types=None, log_scale=None):\n                         if scale is True:\n                             set_scale(\"log\")\n                         else:\n-                            if LooseVersion(mpl.__version__) >= \"3.3\":\n+                            if Version(mpl.__version__) >= Version(\"3.3\"):\n                                 set_scale(\"log\", base=scale)\n                             else:\n                                 set_scale(\"log\", **{f\"base{axis}\": scale})\ndiff --git a/seaborn/_statistics.py b/seaborn/_statistics.py\n--- a/seaborn/_statistics.py\n+++ b/seaborn/_statistics.py\n@@ -24,12 +24,12 @@\n   class instantiation.\n \n \"\"\"\n-from distutils.version import LooseVersion\n from numbers import Number\n import numpy as np\n import scipy as sp\n from scipy import stats\n \n+from .external.version import Version\n from .utils import _check_argument\n \n \n@@ -129,7 +129,7 @@ def _fit(self, fit_data, weights=None):\n         \"\"\"Fit the scipy kde while adding bw_adjust logic and version check.\"\"\"\n         fit_kws = {\"bw_method\": self.bw_method}\n         if weights is not None:\n-            if LooseVersion(sp.__version__) < \"1.2.0\":\n+            if Version(sp.__version__) < Version(\"1.2.0\"):\n                 msg = \"Weighted KDE requires scipy >= 1.2.0\"\n                 raise RuntimeError(msg)\n             fit_kws[\"weights\"] = weights\ndiff --git a/seaborn/axisgrid.py b/seaborn/axisgrid.py\n--- a/seaborn/axisgrid.py\n+++ b/seaborn/axisgrid.py\n@@ -2,7 +2,6 @@\n from inspect import signature\n import warnings\n from textwrap import dedent\n-from distutils.version import LooseVersion\n \n import numpy as np\n import pandas as pd\n@@ -11,6 +10,7 @@\n \n from ._core import VectorPlotter, variable_type, categorical_order\n from . import utils\n+from .external.version import Version\n from .utils import _check_argument, adjust_legend_subtitles, _draw_figure\n from .palettes import color_palette, blend_palette\n from ._decorators import _deprecate_positional_args\n@@ -127,7 +127,7 @@ def add_legend(self, legend_data=None, title=None, label_order=None,\n         blank_handle = mpl.patches.Patch(alpha=0, linewidth=0)\n         handles = [legend_data.get(l, blank_handle) for l in label_order]\n         title = self._hue_var if title is None else title\n-        if LooseVersion(mpl.__version__) < LooseVersion(\"3.0\"):\n+        if Version(mpl.__version__) < Version(\"3.0\"):\n             try:\n                 title_size = mpl.rcParams[\"axes.labelsize\"] * .85\n             except TypeError:  # labelsize is something like \"large\"\ndiff --git a/seaborn/categorical.py b/seaborn/categorical.py\n--- a/seaborn/categorical.py\n+++ b/seaborn/categorical.py\n@@ -9,10 +9,10 @@\n import matplotlib.patches as Patches\n import matplotlib.pyplot as plt\n import warnings\n-from distutils.version import LooseVersion\n \n from ._core import variable_type, infer_orient, categorical_order\n from . import utils\n+from .external.version import Version\n from .utils import remove_na\n from .algorithms import bootstrap\n from .palettes import color_palette, husl_palette, light_palette, dark_palette\n@@ -378,7 +378,7 @@ def annotate_axes(self, ax):\n         if self.hue_names is not None:\n             leg = ax.legend(loc=\"best\", title=self.hue_title)\n             if self.hue_title is not None:\n-                if LooseVersion(mpl.__version__) < \"3.0\":\n+                if Version(mpl.__version__) < Version(\"3.0\"):\n                     # Old Matplotlib has no legend title size rcparam\n                     try:\n                         title_size = mpl.rcParams[\"axes.labelsize\"] * .85\ndiff --git a/seaborn/external/version.py b/seaborn/external/version.py\nnew file mode 100644\n--- /dev/null\n+++ b/seaborn/external/version.py\n@@ -0,0 +1,461 @@\n+\"\"\"Extract reference documentation from the pypa/packaging source tree.\n+\n+In the process of copying, some unused methods / classes were removed.\n+These include:\n+\n+- parse()\n+- anything involving LegacyVersion\n+\n+This software is made available under the terms of *either* of the licenses\n+found in LICENSE.APACHE or LICENSE.BSD. Contributions to this software is made\n+under the terms of *both* these licenses.\n+\n+Vendored from:\n+- https://github.com/pypa/packaging/\n+- commit ba07d8287b4554754ac7178d177033ea3f75d489 (09/09/2021)\n+\"\"\"\n+\n+\n+# This file is dual licensed under the terms of the Apache License, Version\n+# 2.0, and the BSD License. See the LICENSE file in the root of this repository\n+# for complete details.\n+\n+\n+import collections\n+import itertools\n+import re\n+from typing import Callable, Optional, SupportsInt, Tuple, Union\n+\n+__all__ = [\"Version\", \"InvalidVersion\", \"VERSION_PATTERN\"]\n+\n+\n+# Vendored from https://github.com/pypa/packaging/blob/main/packaging/_structures.py\n+\n+class InfinityType:\n+    def __repr__(self) -> str:\n+        return \"Infinity\"\n+\n+    def __hash__(self) -> int:\n+        return hash(repr(self))\n+\n+    def __lt__(self, other: object) -> bool:\n+        return False\n+\n+    def __le__(self, other: object) -> bool:\n+        return False\n+\n+    def __eq__(self, other: object) -> bool:\n+        return isinstance(other, self.__class__)\n+\n+    def __ne__(self, other: object) -> bool:\n+        return not isinstance(other, self.__class__)\n+\n+    def __gt__(self, other: object) -> bool:\n+        return True\n+\n+    def __ge__(self, other: object) -> bool:\n+        return True\n+\n+    def __neg__(self: object) -> \"NegativeInfinityType\":\n+        return NegativeInfinity\n+\n+\n+Infinity = InfinityType()\n+\n+\n+class NegativeInfinityType:\n+    def __repr__(self) -> str:\n+        return \"-Infinity\"\n+\n+    def __hash__(self) -> int:\n+        return hash(repr(self))\n+\n+    def __lt__(self, other: object) -> bool:\n+        return True\n+\n+    def __le__(self, other: object) -> bool:\n+        return True\n+\n+    def __eq__(self, other: object) -> bool:\n+        return isinstance(other, self.__class__)\n+\n+    def __ne__(self, other: object) -> bool:\n+        return not isinstance(other, self.__class__)\n+\n+    def __gt__(self, other: object) -> bool:\n+        return False\n+\n+    def __ge__(self, other: object) -> bool:\n+        return False\n+\n+    def __neg__(self: object) -> InfinityType:\n+        return Infinity\n+\n+\n+NegativeInfinity = NegativeInfinityType()\n+\n+\n+# Vendored from https://github.com/pypa/packaging/blob/main/packaging/version.py\n+\n+InfiniteTypes = Union[InfinityType, NegativeInfinityType]\n+PrePostDevType = Union[InfiniteTypes, Tuple[str, int]]\n+SubLocalType = Union[InfiniteTypes, int, str]\n+LocalType = Union[\n+    NegativeInfinityType,\n+    Tuple[\n+        Union[\n+            SubLocalType,\n+            Tuple[SubLocalType, str],\n+            Tuple[NegativeInfinityType, SubLocalType],\n+        ],\n+        ...,\n+    ],\n+]\n+CmpKey = Tuple[\n+    int, Tuple[int, ...], PrePostDevType, PrePostDevType, PrePostDevType, LocalType\n+]\n+LegacyCmpKey = Tuple[int, Tuple[str, ...]]\n+VersionComparisonMethod = Callable[\n+    [Union[CmpKey, LegacyCmpKey], Union[CmpKey, LegacyCmpKey]], bool\n+]\n+\n+_Version = collections.namedtuple(\n+    \"_Version\", [\"epoch\", \"release\", \"dev\", \"pre\", \"post\", \"local\"]\n+)\n+\n+\n+\n+class InvalidVersion(ValueError):\n+    \"\"\"\n+    An invalid version was found, users should refer to PEP 440.\n+    \"\"\"\n+\n+\n+class _BaseVersion:\n+    _key: Union[CmpKey, LegacyCmpKey]\n+\n+    def __hash__(self) -> int:\n+        return hash(self._key)\n+\n+    # Please keep the duplicated `isinstance` check\n+    # in the six comparisons hereunder\n+    # unless you find a way to avoid adding overhead function calls.\n+    def __lt__(self, other: \"_BaseVersion\") -> bool:\n+        if not isinstance(other, _BaseVersion):\n+            return NotImplemented\n+\n+        return self._key < other._key\n+\n+    def __le__(self, other: \"_BaseVersion\") -> bool:\n+        if not isinstance(other, _BaseVersion):\n+            return NotImplemented\n+\n+        return self._key <= other._key\n+\n+    def __eq__(self, other: object) -> bool:\n+        if not isinstance(other, _BaseVersion):\n+            return NotImplemented\n+\n+        return self._key == other._key\n+\n+    def __ge__(self, other: \"_BaseVersion\") -> bool:\n+        if not isinstance(other, _BaseVersion):\n+            return NotImplemented\n+\n+        return self._key >= other._key\n+\n+    def __gt__(self, other: \"_BaseVersion\") -> bool:\n+        if not isinstance(other, _BaseVersion):\n+            return NotImplemented\n+\n+        return self._key > other._key\n+\n+    def __ne__(self, other: object) -> bool:\n+        if not isinstance(other, _BaseVersion):\n+            return NotImplemented\n+\n+        return self._key != other._key\n+\n+\n+# Deliberately not anchored to the start and end of the string, to make it\n+# easier for 3rd party code to reuse\n+VERSION_PATTERN = r\"\"\"\n+    v?\n+    (?:\n+        (?:(?P<epoch>[0-9]+)!)?                           # epoch\n+        (?P<release>[0-9]+(?:\\.[0-9]+)*)                  # release segment\n+        (?P<pre>                                          # pre-release\n+            [-_\\.]?\n+            (?P<pre_l>(a|b|c|rc|alpha|beta|pre|preview))\n+            [-_\\.]?\n+            (?P<pre_n>[0-9]+)?\n+        )?\n+        (?P<post>                                         # post release\n+            (?:-(?P<post_n1>[0-9]+))\n+            |\n+            (?:\n+                [-_\\.]?\n+                (?P<post_l>post|rev|r)\n+                [-_\\.]?\n+                (?P<post_n2>[0-9]+)?\n+            )\n+        )?\n+        (?P<dev>                                          # dev release\n+            [-_\\.]?\n+            (?P<dev_l>dev)\n+            [-_\\.]?\n+            (?P<dev_n>[0-9]+)?\n+        )?\n+    )\n+    (?:\\+(?P<local>[a-z0-9]+(?:[-_\\.][a-z0-9]+)*))?       # local version\n+\"\"\"\n+\n+\n+class Version(_BaseVersion):\n+\n+    _regex = re.compile(r\"^\\s*\" + VERSION_PATTERN + r\"\\s*$\", re.VERBOSE | re.IGNORECASE)\n+\n+    def __init__(self, version: str) -> None:\n+\n+        # Validate the version and parse it into pieces\n+        match = self._regex.search(version)\n+        if not match:\n+            raise InvalidVersion(f\"Invalid version: '{version}'\")\n+\n+        # Store the parsed out pieces of the version\n+        self._version = _Version(\n+            epoch=int(match.group(\"epoch\")) if match.group(\"epoch\") else 0,\n+            release=tuple(int(i) for i in match.group(\"release\").split(\".\")),\n+            pre=_parse_letter_version(match.group(\"pre_l\"), match.group(\"pre_n\")),\n+            post=_parse_letter_version(\n+                match.group(\"post_l\"), match.group(\"post_n1\") or match.group(\"post_n2\")\n+            ),\n+            dev=_parse_letter_version(match.group(\"dev_l\"), match.group(\"dev_n\")),\n+            local=_parse_local_version(match.group(\"local\")),\n+        )\n+\n+        # Generate a key which will be used for sorting\n+        self._key = _cmpkey(\n+            self._version.epoch,\n+            self._version.release,\n+            self._version.pre,\n+            self._version.post,\n+            self._version.dev,\n+            self._version.local,\n+        )\n+\n+    def __repr__(self) -> str:\n+        return f\"<Version('{self}')>\"\n+\n+    def __str__(self) -> str:\n+        parts = []\n+\n+        # Epoch\n+        if self.epoch != 0:\n+            parts.append(f\"{self.epoch}!\")\n+\n+        # Release segment\n+        parts.append(\".\".join(str(x) for x in self.release))\n+\n+        # Pre-release\n+        if self.pre is not None:\n+            parts.append(\"\".join(str(x) for x in self.pre))\n+\n+        # Post-release\n+        if self.post is not None:\n+            parts.append(f\".post{self.post}\")\n+\n+        # Development release\n+        if self.dev is not None:\n+            parts.append(f\".dev{self.dev}\")\n+\n+        # Local version segment\n+        if self.local is not None:\n+            parts.append(f\"+{self.local}\")\n+\n+        return \"\".join(parts)\n+\n+    @property\n+    def epoch(self) -> int:\n+        _epoch: int = self._version.epoch\n+        return _epoch\n+\n+    @property\n+    def release(self) -> Tuple[int, ...]:\n+        _release: Tuple[int, ...] = self._version.release\n+        return _release\n+\n+    @property\n+    def pre(self) -> Optional[Tuple[str, int]]:\n+        _pre: Optional[Tuple[str, int]] = self._version.pre\n+        return _pre\n+\n+    @property\n+    def post(self) -> Optional[int]:\n+        return self._version.post[1] if self._version.post else None\n+\n+    @property\n+    def dev(self) -> Optional[int]:\n+        return self._version.dev[1] if self._version.dev else None\n+\n+    @property\n+    def local(self) -> Optional[str]:\n+        if self._version.local:\n+            return \".\".join(str(x) for x in self._version.local)\n+        else:\n+            return None\n+\n+    @property\n+    def public(self) -> str:\n+        return str(self).split(\"+\", 1)[0]\n+\n+    @property\n+    def base_version(self) -> str:\n+        parts = []\n+\n+        # Epoch\n+        if self.epoch != 0:\n+            parts.append(f\"{self.epoch}!\")\n+\n+        # Release segment\n+        parts.append(\".\".join(str(x) for x in self.release))\n+\n+        return \"\".join(parts)\n+\n+    @property\n+    def is_prerelease(self) -> bool:\n+        return self.dev is not None or self.pre is not None\n+\n+    @property\n+    def is_postrelease(self) -> bool:\n+        return self.post is not None\n+\n+    @property\n+    def is_devrelease(self) -> bool:\n+        return self.dev is not None\n+\n+    @property\n+    def major(self) -> int:\n+        return self.release[0] if len(self.release) >= 1 else 0\n+\n+    @property\n+    def minor(self) -> int:\n+        return self.release[1] if len(self.release) >= 2 else 0\n+\n+    @property\n+    def micro(self) -> int:\n+        return self.release[2] if len(self.release) >= 3 else 0\n+\n+\n+def _parse_letter_version(\n+    letter: str, number: Union[str, bytes, SupportsInt]\n+) -> Optional[Tuple[str, int]]:\n+\n+    if letter:\n+        # We consider there to be an implicit 0 in a pre-release if there is\n+        # not a numeral associated with it.\n+        if number is None:\n+            number = 0\n+\n+        # We normalize any letters to their lower case form\n+        letter = letter.lower()\n+\n+        # We consider some words to be alternate spellings of other words and\n+        # in those cases we want to normalize the spellings to our preferred\n+        # spelling.\n+        if letter == \"alpha\":\n+            letter = \"a\"\n+        elif letter == \"beta\":\n+            letter = \"b\"\n+        elif letter in [\"c\", \"pre\", \"preview\"]:\n+            letter = \"rc\"\n+        elif letter in [\"rev\", \"r\"]:\n+            letter = \"post\"\n+\n+        return letter, int(number)\n+    if not letter and number:\n+        # We assume if we are given a number, but we are not given a letter\n+        # then this is using the implicit post release syntax (e.g. 1.0-1)\n+        letter = \"post\"\n+\n+        return letter, int(number)\n+\n+    return None\n+\n+\n+_local_version_separators = re.compile(r\"[\\._-]\")\n+\n+\n+def _parse_local_version(local: str) -> Optional[LocalType]:\n+    \"\"\"\n+    Takes a string like abc.1.twelve and turns it into (\"abc\", 1, \"twelve\").\n+    \"\"\"\n+    if local is not None:\n+        return tuple(\n+            part.lower() if not part.isdigit() else int(part)\n+            for part in _local_version_separators.split(local)\n+        )\n+    return None\n+\n+\n+def _cmpkey(\n+    epoch: int,\n+    release: Tuple[int, ...],\n+    pre: Optional[Tuple[str, int]],\n+    post: Optional[Tuple[str, int]],\n+    dev: Optional[Tuple[str, int]],\n+    local: Optional[Tuple[SubLocalType]],\n+) -> CmpKey:\n+\n+    # When we compare a release version, we want to compare it with all of the\n+    # trailing zeros removed. So we'll use a reverse the list, drop all the now\n+    # leading zeros until we come to something non zero, then take the rest\n+    # re-reverse it back into the correct order and make it a tuple and use\n+    # that for our sorting key.\n+    _release = tuple(\n+        reversed(list(itertools.dropwhile(lambda x: x == 0, reversed(release))))\n+    )\n+\n+    # We need to \"trick\" the sorting algorithm to put 1.0.dev0 before 1.0a0.\n+    # We'll do this by abusing the pre segment, but we _only_ want to do this\n+    # if there is not a pre or a post segment. If we have one of those then\n+    # the normal sorting rules will handle this case correctly.\n+    if pre is None and post is None and dev is not None:\n+        _pre: PrePostDevType = NegativeInfinity\n+    # Versions without a pre-release (except as noted above) should sort after\n+    # those with one.\n+    elif pre is None:\n+        _pre = Infinity\n+    else:\n+        _pre = pre\n+\n+    # Versions without a post segment should sort before those with one.\n+    if post is None:\n+        _post: PrePostDevType = NegativeInfinity\n+\n+    else:\n+        _post = post\n+\n+    # Versions without a development segment should sort after those with one.\n+    if dev is None:\n+        _dev: PrePostDevType = Infinity\n+\n+    else:\n+        _dev = dev\n+\n+    if local is None:\n+        # Versions without a local segment should sort before those with one.\n+        _local: LocalType = NegativeInfinity\n+    else:\n+        # Versions with a local segment need that segment parsed to implement\n+        # the sorting rules in PEP440.\n+        # - Alpha numeric segments sort before numeric segments\n+        # - Alpha numeric segments sort lexicographically\n+        # - Numeric segments sort numerically\n+        # - Shorter versions sort before longer versions when the prefixes\n+        #   match exactly\n+        _local = tuple(\n+            (i, \"\") if isinstance(i, int) else (NegativeInfinity, i) for i in local\n+        )\n+\n+    return epoch, _release, _pre, _post, _dev, _local\ndiff --git a/seaborn/rcmod.py b/seaborn/rcmod.py\n--- a/seaborn/rcmod.py\n+++ b/seaborn/rcmod.py\n@@ -1,10 +1,10 @@\n \"\"\"Control plot style and scaling using the matplotlib rcParams interface.\"\"\"\n import warnings\n import functools\n-from distutils.version import LooseVersion\n import matplotlib as mpl\n from cycler import cycler\n from . import palettes\n+from .external.version import Version\n \n \n __all__ = [\"set_theme\", \"set\", \"reset_defaults\", \"reset_orig\",\n@@ -79,7 +79,7 @@\n \n ]\n \n-if LooseVersion(mpl.__version__) >= \"3.0\":\n+if Version(mpl.__version__) >= Version(\"3.0\"):\n     _context_keys.append(\"legend.title_fontsize\")\n \n \n@@ -397,7 +397,7 @@ def plotting_context(context=None, font_scale=1, rc=None):\n \n         }\n \n-        if LooseVersion(mpl.__version__) >= \"3.0\":\n+        if Version(mpl.__version__) >= Version(\"3.0\"):\n             texts_base_context[\"legend.title_fontsize\"] = 12\n \n         base_context = {\n"}
{"instance_id": "mwaskom__seaborn-3202", "model_name_or_path": "gold", "model_patch": "diff --git a/seaborn/_core/plot.py b/seaborn/_core/plot.py\n--- a/seaborn/_core/plot.py\n+++ b/seaborn/_core/plot.py\n@@ -1466,8 +1466,6 @@ def _setup_split_generator(\n         self, grouping_vars: list[str], df: DataFrame, subplots: list[dict[str, Any]],\n     ) -> Callable[[], Generator]:\n \n-        allow_empty = False  # TODO will need to recreate previous categorical plots\n-\n         grouping_keys = []\n         grouping_vars = [\n             v for v in grouping_vars if v in df and v not in [\"col\", \"row\"]\n@@ -1506,7 +1504,8 @@ def split_generator(keep_na=False) -> Generator:\n                         subplot_keys[dim] = view[dim]\n \n                 if not grouping_vars or not any(grouping_keys):\n-                    yield subplot_keys, axes_df.copy(), view[\"ax\"]\n+                    if not axes_df.empty:\n+                        yield subplot_keys, axes_df.copy(), view[\"ax\"]\n                     continue\n \n                 grouped_df = axes_df.groupby(grouping_vars, sort=False, as_index=False)\n@@ -1526,7 +1525,7 @@ def split_generator(keep_na=False) -> Generator:\n                         # case this option could be removed\n                         df_subset = axes_df.loc[[]]\n \n-                    if df_subset.empty and not allow_empty:\n+                    if df_subset.empty:\n                         continue\n \n                     sub_vars = dict(zip(grouping_vars, key))\ndiff --git a/seaborn/_core/scales.py b/seaborn/_core/scales.py\n--- a/seaborn/_core/scales.py\n+++ b/seaborn/_core/scales.py\n@@ -163,7 +163,7 @@ def _setup(\n             new = new.label()\n \n         # TODO flexibility over format() which isn't great for numbers / dates\n-        stringify = np.vectorize(format)\n+        stringify = np.vectorize(format, otypes=[\"object\"])\n \n         units_seed = categorical_order(data, new.order)\n \n"}
{"instance_id": "mwaskom__seaborn-3407", "model_name_or_path": "gold", "model_patch": "diff --git a/seaborn/axisgrid.py b/seaborn/axisgrid.py\n--- a/seaborn/axisgrid.py\n+++ b/seaborn/axisgrid.py\n@@ -1472,8 +1472,8 @@ def map_diag(self, func, **kwargs):\n                 for ax in diag_axes[1:]:\n                     share_axis(diag_axes[0], ax, \"y\")\n \n-            self.diag_vars = np.array(diag_vars, np.object_)\n-            self.diag_axes = np.array(diag_axes, np.object_)\n+            self.diag_vars = diag_vars\n+            self.diag_axes = diag_axes\n \n         if \"hue\" not in signature(func).parameters:\n             return self._map_diag_iter_hue(func, **kwargs)\n"}
{"instance_id": "pallets__flask-4169", "model_name_or_path": "gold", "model_patch": "diff --git a/src/flask/cli.py b/src/flask/cli.py\n--- a/src/flask/cli.py\n+++ b/src/flask/cli.py\n@@ -312,7 +312,7 @@ def __init__(self, loader, use_eager_loading=None):\n         self.loader = loader\n         self._app = None\n         self._lock = Lock()\n-        self._bg_loading_exc_info = None\n+        self._bg_loading_exc = None\n \n         if use_eager_loading is None:\n             use_eager_loading = os.environ.get(\"WERKZEUG_RUN_MAIN\") != \"true\"\n@@ -328,23 +328,24 @@ def _load_app():\n             with self._lock:\n                 try:\n                     self._load_unlocked()\n-                except Exception:\n-                    self._bg_loading_exc_info = sys.exc_info()\n+                except Exception as e:\n+                    self._bg_loading_exc = e\n \n         t = Thread(target=_load_app, args=())\n         t.start()\n \n     def _flush_bg_loading_exception(self):\n         __traceback_hide__ = True  # noqa: F841\n-        exc_info = self._bg_loading_exc_info\n-        if exc_info is not None:\n-            self._bg_loading_exc_info = None\n-            raise exc_info\n+        exc = self._bg_loading_exc\n+\n+        if exc is not None:\n+            self._bg_loading_exc = None\n+            raise exc\n \n     def _load_unlocked(self):\n         __traceback_hide__ = True  # noqa: F841\n         self._app = rv = self.loader()\n-        self._bg_loading_exc_info = None\n+        self._bg_loading_exc = None\n         return rv\n \n     def __call__(self, environ, start_response):\n"}
{"instance_id": "pallets__flask-4544", "model_name_or_path": "gold", "model_patch": "diff --git a/src/flask/cli.py b/src/flask/cli.py\n--- a/src/flask/cli.py\n+++ b/src/flask/cli.py\n@@ -763,7 +763,10 @@ def convert(self, value, param, ctx):\n @click.option(\"--host\", \"-h\", default=\"127.0.0.1\", help=\"The interface to bind to.\")\n @click.option(\"--port\", \"-p\", default=5000, help=\"The port to bind to.\")\n @click.option(\n-    \"--cert\", type=CertParamType(), help=\"Specify a certificate file to use HTTPS.\"\n+    \"--cert\",\n+    type=CertParamType(),\n+    help=\"Specify a certificate file to use HTTPS.\",\n+    is_eager=True,\n )\n @click.option(\n     \"--key\",\n"}
{"instance_id": "pallets__flask-4642", "model_name_or_path": "gold", "model_patch": "diff --git a/src/flask/app.py b/src/flask/app.py\n--- a/src/flask/app.py\n+++ b/src/flask/app.py\n@@ -10,6 +10,7 @@\n from threading import Lock\n from types import TracebackType\n \n+import click\n from werkzeug.datastructures import Headers\n from werkzeug.datastructures import ImmutableDict\n from werkzeug.exceptions import Aborter\n@@ -23,6 +24,7 @@\n from werkzeug.routing import RequestRedirect\n from werkzeug.routing import RoutingException\n from werkzeug.routing import Rule\n+from werkzeug.serving import is_running_from_reloader\n from werkzeug.urls import url_quote\n from werkzeug.utils import redirect as _wz_redirect\n from werkzeug.wrappers import Response as BaseResponse\n@@ -908,12 +910,18 @@ def run(\n             The default port is now picked from the ``SERVER_NAME``\n             variable.\n         \"\"\"\n-        # Change this into a no-op if the server is invoked from the\n-        # command line. Have a look at cli.py for more information.\n+        # Ignore this call so that it doesn't start another server if\n+        # the 'flask run' command is used.\n         if os.environ.get(\"FLASK_RUN_FROM_CLI\") == \"true\":\n-            from .debughelpers import explain_ignored_app_run\n+            if not is_running_from_reloader():\n+                click.secho(\n+                    \" * Ignoring a call to 'app.run()', the server is\"\n+                    \" already being run with the 'flask run' command.\\n\"\n+                    \"   Only call 'app.run()' in an 'if __name__ ==\"\n+                    ' \"__main__\"\\' guard.',\n+                    fg=\"red\",\n+                )\n \n-            explain_ignored_app_run()\n             return\n \n         if get_load_dotenv(load_dotenv):\ndiff --git a/src/flask/cli.py b/src/flask/cli.py\n--- a/src/flask/cli.py\n+++ b/src/flask/cli.py\n@@ -5,12 +5,14 @@\n import re\n import sys\n import traceback\n+import typing as t\n from functools import update_wrapper\n from operator import attrgetter\n from threading import Lock\n from threading import Thread\n \n import click\n+from werkzeug.serving import is_running_from_reloader\n from werkzeug.utils import import_string\n \n from .globals import current_app\n@@ -273,7 +275,7 @@ def __init__(self, loader, use_eager_loading=None):\n         self._bg_loading_exc = None\n \n         if use_eager_loading is None:\n-            use_eager_loading = os.environ.get(\"WERKZEUG_RUN_MAIN\") != \"true\"\n+            use_eager_loading = not is_running_from_reloader()\n \n         if use_eager_loading:\n             self._load_unlocked()\n@@ -477,7 +479,13 @@ def __init__(\n         if add_version_option:\n             params.append(version_option)\n \n-        AppGroup.__init__(self, params=params, **extra)\n+        if \"context_settings\" not in extra:\n+            extra[\"context_settings\"] = {}\n+\n+        extra[\"context_settings\"].setdefault(\"auto_envvar_prefix\", \"FLASK\")\n+\n+        super().__init__(params=params, **extra)\n+\n         self.create_app = create_app\n         self.load_dotenv = load_dotenv\n         self.set_debug_flag = set_debug_flag\n@@ -545,26 +553,22 @@ def list_commands(self, ctx):\n \n         return sorted(rv)\n \n-    def main(self, *args, **kwargs):\n-        # Set a global flag that indicates that we were invoked from the\n-        # command line interface. This is detected by Flask.run to make the\n-        # call into a no-op. This is necessary to avoid ugly errors when the\n-        # script that is loaded here also attempts to start a server.\n-        os.environ[\"FLASK_RUN_FROM_CLI\"] = \"true\"\n-\n+    def make_context(\n+        self,\n+        info_name: t.Optional[str],\n+        args: t.List[str],\n+        parent: t.Optional[click.Context] = None,\n+        **extra: t.Any,\n+    ) -> click.Context:\n         if get_load_dotenv(self.load_dotenv):\n             load_dotenv()\n \n-        obj = kwargs.get(\"obj\")\n-\n-        if obj is None:\n-            obj = ScriptInfo(\n+        if \"obj\" not in extra and \"obj\" not in self.context_settings:\n+            extra[\"obj\"] = ScriptInfo(\n                 create_app=self.create_app, set_debug_flag=self.set_debug_flag\n             )\n \n-        kwargs[\"obj\"] = obj\n-        kwargs.setdefault(\"auto_envvar_prefix\", \"FLASK\")\n-        return super().main(*args, **kwargs)\n+        return super().make_context(info_name, args, parent=parent, **extra)\n \n \n def _path_is_ancestor(path, other):\n@@ -637,7 +641,7 @@ def show_server_banner(env, debug, app_import_path, eager_loading):\n     \"\"\"Show extra startup messages the first time the server is run,\n     ignoring the reloader.\n     \"\"\"\n-    if os.environ.get(\"WERKZEUG_RUN_MAIN\") == \"true\":\n+    if is_running_from_reloader():\n         return\n \n     if app_import_path is not None:\n@@ -653,10 +657,10 @@ def show_server_banner(env, debug, app_import_path, eager_loading):\n     if env == \"production\":\n         click.secho(\n             \"   WARNING: This is a development server. Do not use it in\"\n-            \" a production deployment.\",\n+            \" a production deployment.\\n   Use a production WSGI server\"\n+            \" instead.\",\n             fg=\"red\",\n         )\n-        click.secho(\"   Use a production WSGI server instead.\", dim=True)\n \n     if debug is not None:\n         click.echo(f\" * Debug mode: {'on' if debug else 'off'}\")\n@@ -963,6 +967,7 @@ def routes_command(sort: str, all_methods: bool) -> None:\n \n \n cli = FlaskGroup(\n+    name=\"flask\",\n     help=\"\"\"\\\n A general utility script for Flask applications.\n \n@@ -978,7 +983,7 @@ def routes_command(sort: str, all_methods: bool) -> None:\n \"\"\".format(\n         cmd=\"export\" if os.name == \"posix\" else \"set\",\n         prefix=\"$ \" if os.name == \"posix\" else \"> \",\n-    )\n+    ),\n )\n \n \ndiff --git a/src/flask/debughelpers.py b/src/flask/debughelpers.py\n--- a/src/flask/debughelpers.py\n+++ b/src/flask/debughelpers.py\n@@ -1,6 +1,4 @@\n-import os\n import typing as t\n-from warnings import warn\n \n from .app import Flask\n from .blueprints import Blueprint\n@@ -159,16 +157,3 @@ def explain_template_loading_attempts(app: Flask, template, attempts) -> None:\n         info.append(\"  See https://flask.palletsprojects.com/blueprints/#templates\")\n \n     app.logger.info(\"\\n\".join(info))\n-\n-\n-def explain_ignored_app_run() -> None:\n-    if os.environ.get(\"WERKZEUG_RUN_MAIN\") != \"true\":\n-        warn(\n-            Warning(\n-                \"Silently ignoring app.run() because the application is\"\n-                \" run from the flask command line executable. Consider\"\n-                ' putting app.run() behind an if __name__ == \"__main__\"'\n-                \" guard to silence this warning.\"\n-            ),\n-            stacklevel=3,\n-        )\n"}
{"instance_id": "pallets__flask-5063", "model_name_or_path": "gold", "model_patch": "diff --git a/src/flask/cli.py b/src/flask/cli.py\n--- a/src/flask/cli.py\n+++ b/src/flask/cli.py\n@@ -9,7 +9,7 @@\n import traceback\n import typing as t\n from functools import update_wrapper\n-from operator import attrgetter\n+from operator import itemgetter\n \n import click\n from click.core import ParameterSource\n@@ -989,49 +989,62 @@ def shell_command() -> None:\n @click.option(\n     \"--sort\",\n     \"-s\",\n-    type=click.Choice((\"endpoint\", \"methods\", \"rule\", \"match\")),\n+    type=click.Choice((\"endpoint\", \"methods\", \"domain\", \"rule\", \"match\")),\n     default=\"endpoint\",\n     help=(\n-        'Method to sort routes by. \"match\" is the order that Flask will match '\n-        \"routes when dispatching a request.\"\n+        \"Method to sort routes by. 'match' is the order that Flask will match routes\"\n+        \" when dispatching a request.\"\n     ),\n )\n @click.option(\"--all-methods\", is_flag=True, help=\"Show HEAD and OPTIONS methods.\")\n @with_appcontext\n def routes_command(sort: str, all_methods: bool) -> None:\n     \"\"\"Show all registered routes with endpoints and methods.\"\"\"\n-\n     rules = list(current_app.url_map.iter_rules())\n+\n     if not rules:\n         click.echo(\"No routes were registered.\")\n         return\n \n-    ignored_methods = set(() if all_methods else (\"HEAD\", \"OPTIONS\"))\n+    ignored_methods = set() if all_methods else {\"HEAD\", \"OPTIONS\"}\n+    host_matching = current_app.url_map.host_matching\n+    has_domain = any(rule.host if host_matching else rule.subdomain for rule in rules)\n+    rows = []\n \n-    if sort in (\"endpoint\", \"rule\"):\n-        rules = sorted(rules, key=attrgetter(sort))\n-    elif sort == \"methods\":\n-        rules = sorted(rules, key=lambda rule: sorted(rule.methods))  # type: ignore\n+    for rule in rules:\n+        row = [\n+            rule.endpoint,\n+            \", \".join(sorted((rule.methods or set()) - ignored_methods)),\n+        ]\n \n-    rule_methods = [\n-        \", \".join(sorted(rule.methods - ignored_methods))  # type: ignore\n-        for rule in rules\n-    ]\n+        if has_domain:\n+            row.append((rule.host if host_matching else rule.subdomain) or \"\")\n \n-    headers = (\"Endpoint\", \"Methods\", \"Rule\")\n-    widths = (\n-        max(len(rule.endpoint) for rule in rules),\n-        max(len(methods) for methods in rule_methods),\n-        max(len(rule.rule) for rule in rules),\n-    )\n-    widths = [max(len(h), w) for h, w in zip(headers, widths)]\n-    row = \"{{0:<{0}}}  {{1:<{1}}}  {{2:<{2}}}\".format(*widths)\n+        row.append(rule.rule)\n+        rows.append(row)\n+\n+    headers = [\"Endpoint\", \"Methods\"]\n+    sorts = [\"endpoint\", \"methods\"]\n+\n+    if has_domain:\n+        headers.append(\"Host\" if host_matching else \"Subdomain\")\n+        sorts.append(\"domain\")\n+\n+    headers.append(\"Rule\")\n+    sorts.append(\"rule\")\n+\n+    try:\n+        rows.sort(key=itemgetter(sorts.index(sort)))\n+    except ValueError:\n+        pass\n \n-    click.echo(row.format(*headers).strip())\n-    click.echo(row.format(*(\"-\" * width for width in widths)))\n+    rows.insert(0, headers)\n+    widths = [max(len(row[i]) for row in rows) for i in range(len(headers))]\n+    rows.insert(1, [\"-\" * w for w in widths])\n+    template = \"  \".join(f\"{{{i}:<{w}}}\" for i, w in enumerate(widths))\n \n-    for rule, methods in zip(rules, rule_methods):\n-        click.echo(row.format(rule.endpoint, methods, rule.rule).rstrip())\n+    for row in rows:\n+        click.echo(template.format(*row))\n \n \n cli = FlaskGroup(\n"}
{"instance_id": "psf__requests-774", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/models.py b/requests/models.py\n--- a/requests/models.py\n+++ b/requests/models.py\n@@ -413,7 +413,10 @@ def full_url(self):\n         if not scheme in SCHEMAS:\n             raise InvalidSchema(\"Invalid scheme %r\" % scheme)\n \n-        netloc = netloc.encode('idna').decode('utf-8')\n+        try:\n+            netloc = netloc.encode('idna').decode('utf-8')\n+        except UnicodeError:\n+            raise InvalidURL('URL has an invalid label.')\n \n         if not path:\n             path = '/'\n"}
{"instance_id": "psf__requests-863", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/models.py b/requests/models.py\n--- a/requests/models.py\n+++ b/requests/models.py\n@@ -462,8 +462,10 @@ def path_url(self):\n \n     def register_hook(self, event, hook):\n         \"\"\"Properly register a hook.\"\"\"\n-\n-        self.hooks[event].append(hook)\n+        if isinstance(hook, (list, tuple, set)):\n+            self.hooks[event].extend(hook)\n+        else:\n+            self.hooks[event].append(hook)\n \n     def deregister_hook(self, event, hook):\n         \"\"\"Deregister a previously registered hook.\n"}
{"instance_id": "psf__requests-1142", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/models.py b/requests/models.py\n--- a/requests/models.py\n+++ b/requests/models.py\n@@ -386,13 +386,14 @@ def prepare_body(self, data, files):\n         self.body = body\n \n     def prepare_content_length(self, body):\n-        self.headers['Content-Length'] = '0'\n         if hasattr(body, 'seek') and hasattr(body, 'tell'):\n             body.seek(0, 2)\n             self.headers['Content-Length'] = str(body.tell())\n             body.seek(0, 0)\n         elif body is not None:\n             self.headers['Content-Length'] = str(len(body))\n+        elif self.method not in ('GET', 'HEAD'):\n+            self.headers['Content-Length'] = '0'\n \n     def prepare_auth(self, auth):\n         \"\"\"Prepares the given HTTP auth data.\"\"\"\n"}
{"instance_id": "psf__requests-1537", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/models.py b/requests/models.py\n--- a/requests/models.py\n+++ b/requests/models.py\n@@ -106,6 +106,10 @@ def _encode_files(files, data):\n                 val = [val]\n             for v in val:\n                 if v is not None:\n+                    # Don't call str() on bytestrings: in Py3 it all goes wrong.\n+                    if not isinstance(v, bytes):\n+                        v = str(v)\n+\n                     new_fields.append(\n                         (field.decode('utf-8') if isinstance(field, bytes) else field,\n                          v.encode('utf-8') if isinstance(v, str) else v))\n"}
{"instance_id": "psf__requests-1776", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/auth.py b/requests/auth.py\n--- a/requests/auth.py\n+++ b/requests/auth.py\n@@ -16,6 +16,7 @@\n from base64 import b64encode\n \n from .compat import urlparse, str\n+from .cookies import extract_cookies_to_jar\n from .utils import parse_dict_header\n \n log = logging.getLogger(__name__)\n@@ -169,7 +170,8 @@ def handle_401(self, r, **kwargs):\n             r.content\n             r.raw.release_conn()\n             prep = r.request.copy()\n-            prep.prepare_cookies(r.cookies)\n+            extract_cookies_to_jar(prep._cookies, r.request, r.raw)\n+            prep.prepare_cookies(prep._cookies)\n \n             prep.headers['Authorization'] = self.build_digest_header(\n                 prep.method, prep.url)\ndiff --git a/requests/models.py b/requests/models.py\n--- a/requests/models.py\n+++ b/requests/models.py\n@@ -270,6 +270,9 @@ def __init__(self):\n         self.url = None\n         #: dictionary of HTTP headers.\n         self.headers = None\n+        # The `CookieJar` used to create the Cookie header will be stored here\n+        # after prepare_cookies is called\n+        self._cookies = None\n         #: request body to send to the server.\n         self.body = None\n         #: dictionary of callback hooks, for internal usage.\n@@ -299,6 +302,7 @@ def copy(self):\n         p.method = self.method\n         p.url = self.url\n         p.headers = self.headers.copy()\n+        p._cookies = self._cookies.copy()\n         p.body = self.body\n         p.hooks = self.hooks\n         return p\n@@ -474,14 +478,13 @@ def prepare_cookies(self, cookies):\n         \"\"\"Prepares the given HTTP cookie data.\"\"\"\n \n         if isinstance(cookies, cookielib.CookieJar):\n-            cookies = cookies\n+            self._cookies = cookies\n         else:\n-            cookies = cookiejar_from_dict(cookies)\n+            self._cookies = cookiejar_from_dict(cookies)\n \n-        if 'cookie' not in self.headers:\n-            cookie_header = get_cookie_header(cookies, self)\n-            if cookie_header is not None:\n-                self.headers['Cookie'] = cookie_header\n+        cookie_header = get_cookie_header(self._cookies, self)\n+        if cookie_header is not None:\n+            self.headers['Cookie'] = cookie_header\n \n     def prepare_hooks(self, hooks):\n         \"\"\"Prepares the given hooks.\"\"\"\ndiff --git a/requests/sessions.py b/requests/sessions.py\n--- a/requests/sessions.py\n+++ b/requests/sessions.py\n@@ -153,7 +153,9 @@ def resolve_redirects(self, resp, req, stream=False, timeout=None,\n             except KeyError:\n                 pass\n \n-            prepared_request.prepare_cookies(self.cookies)\n+            extract_cookies_to_jar(prepared_request._cookies,\n+                                   prepared_request, resp.raw)\n+            prepared_request.prepare_cookies(prepared_request._cookies)\n \n             resp = self.send(\n                 prepared_request,\n@@ -345,9 +347,6 @@ def request(self, method, url,\n         )\n         prep = self.prepare_request(req)\n \n-        # Add param cookies to session cookies\n-        self.cookies = merge_cookies(self.cookies, cookies)\n-\n         proxies = proxies or {}\n \n         # Gather clues from the surrounding environment.\n"}
{"instance_id": "psf__requests-1944", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/adapters.py b/requests/adapters.py\n--- a/requests/adapters.py\n+++ b/requests/adapters.py\n@@ -385,9 +385,4 @@ def send(self, request, stream=False, timeout=None, verify=True, cert=None, prox\n             else:\n                 raise\n \n-        r = self.build_response(request, resp)\n-\n-        if not stream:\n-            r.content\n-\n-        return r\n+        return self.build_response(request, resp)\ndiff --git a/requests/sessions.py b/requests/sessions.py\n--- a/requests/sessions.py\n+++ b/requests/sessions.py\n@@ -19,7 +19,8 @@\n from .models import Request, PreparedRequest, DEFAULT_REDIRECT_LIMIT\n from .hooks import default_hooks, dispatch_hook\n from .utils import to_key_val_list, default_headers, to_native_string\n-from .exceptions import TooManyRedirects, InvalidSchema\n+from .exceptions import (\n+    TooManyRedirects, InvalidSchema, ChunkedEncodingError, ContentDecodingError)\n from .structures import CaseInsensitiveDict\n \n from .adapters import HTTPAdapter\n@@ -94,7 +95,10 @@ def resolve_redirects(self, resp, req, stream=False, timeout=None,\n         while resp.is_redirect:\n             prepared_request = req.copy()\n \n-            resp.content  # Consume socket so it can be released\n+            try:\n+                resp.content  # Consume socket so it can be released\n+            except (ChunkedEncodingError, ContentDecodingError, RuntimeError):\n+                resp.raw.read(decode_content=False)\n \n             if i >= self.max_redirects:\n                 raise TooManyRedirects('Exceeded %s redirects.' % self.max_redirects)\n@@ -588,6 +592,9 @@ def send(self, request, **kwargs):\n             r = history.pop()\n             r.history = history\n \n+        if not stream:\n+            r.content\n+\n         return r\n \n     def get_adapter(self, url):\n"}
{"instance_id": "psf__requests-2153", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/compat.py b/requests/compat.py\n--- a/requests/compat.py\n+++ b/requests/compat.py\n@@ -92,7 +92,6 @@\n     from Cookie import Morsel\n     from StringIO import StringIO\n     from .packages.urllib3.packages.ordered_dict import OrderedDict\n-    from httplib import IncompleteRead\n \n     builtin_str = str\n     bytes = str\n@@ -108,7 +107,6 @@\n     from http.cookies import Morsel\n     from io import StringIO\n     from collections import OrderedDict\n-    from http.client import IncompleteRead\n \n     builtin_str = str\n     str = str\ndiff --git a/requests/models.py b/requests/models.py\n--- a/requests/models.py\n+++ b/requests/models.py\n@@ -9,7 +9,6 @@\n \n import collections\n import datetime\n-import socket\n \n from io import BytesIO, UnsupportedOperation\n from .hooks import default_hooks\n@@ -20,7 +19,8 @@\n from .packages.urllib3.fields import RequestField\n from .packages.urllib3.filepost import encode_multipart_formdata\n from .packages.urllib3.util import parse_url\n-from .packages.urllib3.exceptions import DecodeError\n+from .packages.urllib3.exceptions import (\n+    DecodeError, ReadTimeoutError, ProtocolError)\n from .exceptions import (\n     HTTPError, RequestException, MissingSchema, InvalidURL,\n     ChunkedEncodingError, ContentDecodingError, ConnectionError)\n@@ -30,7 +30,7 @@\n     iter_slices, guess_json_utf, super_len, to_native_string)\n from .compat import (\n     cookielib, urlunparse, urlsplit, urlencode, str, bytes, StringIO,\n-    is_py2, chardet, json, builtin_str, basestring, IncompleteRead)\n+    is_py2, chardet, json, builtin_str, basestring)\n from .status_codes import codes\n \n #: The set of HTTP status codes that indicate an automatically\n@@ -637,11 +637,11 @@ def generate():\n                 try:\n                     for chunk in self.raw.stream(chunk_size, decode_content=True):\n                         yield chunk\n-                except IncompleteRead as e:\n+                except ProtocolError as e:\n                     raise ChunkedEncodingError(e)\n                 except DecodeError as e:\n                     raise ContentDecodingError(e)\n-                except socket.error as e:\n+                except ReadTimeoutError as e:\n                     raise ConnectionError(e)\n             except AttributeError:\n                 # Standard file-like object.\n"}
{"instance_id": "psf__requests-2317", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/sessions.py b/requests/sessions.py\n--- a/requests/sessions.py\n+++ b/requests/sessions.py\n@@ -13,7 +13,7 @@\n from datetime import datetime\n \n from .auth import _basic_auth_str\n-from .compat import cookielib, OrderedDict, urljoin, urlparse, builtin_str\n+from .compat import cookielib, OrderedDict, urljoin, urlparse\n from .cookies import (\n     cookiejar_from_dict, extract_cookies_to_jar, RequestsCookieJar, merge_cookies)\n from .models import Request, PreparedRequest, DEFAULT_REDIRECT_LIMIT\n@@ -425,7 +425,7 @@ def request(self, method, url,\n             If Tuple, ('cert', 'key') pair.\n         \"\"\"\n \n-        method = builtin_str(method)\n+        method = to_native_string(method)\n \n         # Create the Request.\n         req = Request(\n"}
{"instance_id": "psf__requests-2466", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/packages/__init__.py b/requests/packages/__init__.py\n--- a/requests/packages/__init__.py\n+++ b/requests/packages/__init__.py\n@@ -27,9 +27,13 @@\n \n class VendorAlias(object):\n \n-    def __init__(self):\n+    def __init__(self, package_names):\n+        self._package_names = package_names\n         self._vendor_name = __name__\n         self._vendor_pkg = self._vendor_name + \".\"\n+        self._vendor_pkgs = [\n+            self._vendor_pkg + name for name in self._package_names\n+        ]\n \n     def find_module(self, fullname, path=None):\n         if fullname.startswith(self._vendor_pkg):\n@@ -44,6 +48,14 @@ def load_module(self, name):\n                 )\n             )\n \n+        if not (name == self._vendor_name or\n+                any(name.startswith(pkg) for pkg in self._vendor_pkgs)):\n+            raise ImportError(\n+                \"Cannot import %s, must be one of %s.\" % (\n+                    name, self._vendor_pkgs\n+                )\n+            )\n+\n         # Check to see if we already have this item in sys.modules, if we do\n         # then simply return that.\n         if name in sys.modules:\n@@ -92,4 +104,4 @@ def load_module(self, name):\n         return module\n \n \n-sys.meta_path.append(VendorAlias())\n+sys.meta_path.append(VendorAlias([\"urllib3\", \"chardet\"]))\n"}
{"instance_id": "psf__requests-2678", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/adapters.py b/requests/adapters.py\n--- a/requests/adapters.py\n+++ b/requests/adapters.py\n@@ -19,6 +19,7 @@\n from .utils import (DEFAULT_CA_BUNDLE_PATH, get_encoding_from_headers,\n                     prepend_scheme_if_needed, get_auth_from_url, urldefragauth)\n from .structures import CaseInsensitiveDict\n+from .packages.urllib3.exceptions import ClosedPoolError\n from .packages.urllib3.exceptions import ConnectTimeoutError\n from .packages.urllib3.exceptions import HTTPError as _HTTPError\n from .packages.urllib3.exceptions import MaxRetryError\n@@ -421,6 +422,9 @@ def send(self, request, stream=False, timeout=None, verify=True, cert=None, prox\n \n             raise ConnectionError(e, request=request)\n \n+        except ClosedPoolError as e:\n+            raise ConnectionError(e, request=request)\n+\n         except _ProxyError as e:\n             raise ProxyError(e)\n \ndiff --git a/requests/auth.py b/requests/auth.py\n--- a/requests/auth.py\n+++ b/requests/auth.py\n@@ -11,6 +11,7 @@\n import re\n import time\n import hashlib\n+import threading\n \n from base64 import b64encode\n \n@@ -63,19 +64,26 @@ class HTTPDigestAuth(AuthBase):\n     def __init__(self, username, password):\n         self.username = username\n         self.password = password\n-        self.last_nonce = ''\n-        self.nonce_count = 0\n-        self.chal = {}\n-        self.pos = None\n-        self.num_401_calls = 1\n+        # Keep state in per-thread local storage\n+        self._thread_local = threading.local()\n+\n+    def init_per_thread_state(self):\n+        # Ensure state is initialized just once per-thread\n+        if not hasattr(self._thread_local, 'init'):\n+            self._thread_local.init = True\n+            self._thread_local.last_nonce = ''\n+            self._thread_local.nonce_count = 0\n+            self._thread_local.chal = {}\n+            self._thread_local.pos = None\n+            self._thread_local.num_401_calls = None\n \n     def build_digest_header(self, method, url):\n \n-        realm = self.chal['realm']\n-        nonce = self.chal['nonce']\n-        qop = self.chal.get('qop')\n-        algorithm = self.chal.get('algorithm')\n-        opaque = self.chal.get('opaque')\n+        realm = self._thread_local.chal['realm']\n+        nonce = self._thread_local.chal['nonce']\n+        qop = self._thread_local.chal.get('qop')\n+        algorithm = self._thread_local.chal.get('algorithm')\n+        opaque = self._thread_local.chal.get('opaque')\n \n         if algorithm is None:\n             _algorithm = 'MD5'\n@@ -114,12 +122,12 @@ def sha_utf8(x):\n         HA1 = hash_utf8(A1)\n         HA2 = hash_utf8(A2)\n \n-        if nonce == self.last_nonce:\n-            self.nonce_count += 1\n+        if nonce == self._thread_local.last_nonce:\n+            self._thread_local.nonce_count += 1\n         else:\n-            self.nonce_count = 1\n-        ncvalue = '%08x' % self.nonce_count\n-        s = str(self.nonce_count).encode('utf-8')\n+            self._thread_local.nonce_count = 1\n+        ncvalue = '%08x' % self._thread_local.nonce_count\n+        s = str(self._thread_local.nonce_count).encode('utf-8')\n         s += nonce.encode('utf-8')\n         s += time.ctime().encode('utf-8')\n         s += os.urandom(8)\n@@ -139,7 +147,7 @@ def sha_utf8(x):\n             # XXX handle auth-int.\n             return None\n \n-        self.last_nonce = nonce\n+        self._thread_local.last_nonce = nonce\n \n         # XXX should the partial digests be encoded too?\n         base = 'username=\"%s\", realm=\"%s\", nonce=\"%s\", uri=\"%s\", ' \\\n@@ -158,23 +166,22 @@ def sha_utf8(x):\n     def handle_redirect(self, r, **kwargs):\n         \"\"\"Reset num_401_calls counter on redirects.\"\"\"\n         if r.is_redirect:\n-            self.num_401_calls = 1\n+            self._thread_local.num_401_calls = 1\n \n     def handle_401(self, r, **kwargs):\n         \"\"\"Takes the given response and tries digest-auth, if needed.\"\"\"\n \n-        if self.pos is not None:\n+        if self._thread_local.pos is not None:\n             # Rewind the file position indicator of the body to where\n             # it was to resend the request.\n-            r.request.body.seek(self.pos)\n-        num_401_calls = getattr(self, 'num_401_calls', 1)\n+            r.request.body.seek(self._thread_local.pos)\n         s_auth = r.headers.get('www-authenticate', '')\n \n-        if 'digest' in s_auth.lower() and num_401_calls < 2:\n+        if 'digest' in s_auth.lower() and self._thread_local.num_401_calls < 2:\n \n-            self.num_401_calls += 1\n+            self._thread_local.num_401_calls += 1\n             pat = re.compile(r'digest ', flags=re.IGNORECASE)\n-            self.chal = parse_dict_header(pat.sub('', s_auth, count=1))\n+            self._thread_local.chal = parse_dict_header(pat.sub('', s_auth, count=1))\n \n             # Consume content and release the original connection\n             # to allow our new request to reuse the same one.\n@@ -192,21 +199,25 @@ def handle_401(self, r, **kwargs):\n \n             return _r\n \n-        self.num_401_calls = 1\n+        self._thread_local.num_401_calls = 1\n         return r\n \n     def __call__(self, r):\n+        # Initialize per-thread state, if needed\n+        self.init_per_thread_state()\n         # If we have a saved nonce, skip the 401\n-        if self.last_nonce:\n+        if self._thread_local.last_nonce:\n             r.headers['Authorization'] = self.build_digest_header(r.method, r.url)\n         try:\n-            self.pos = r.body.tell()\n+            self._thread_local.pos = r.body.tell()\n         except AttributeError:\n             # In the case of HTTPDigestAuth being reused and the body of\n             # the previous request was a file-like object, pos has the\n             # file position of the previous body. Ensure it's set to\n             # None.\n-            self.pos = None\n+            self._thread_local.pos = None\n         r.register_hook('response', self.handle_401)\n         r.register_hook('response', self.handle_redirect)\n+        self._thread_local.num_401_calls = 1\n+\n         return r\ndiff --git a/requests/packages/__init__.py b/requests/packages/__init__.py\n--- a/requests/packages/__init__.py\n+++ b/requests/packages/__init__.py\n@@ -1,3 +1,36 @@\n+'''\n+Debian and other distributions \"unbundle\" requests' vendored dependencies, and\n+rewrite all imports to use the global versions of ``urllib3`` and ``chardet``.\n+The problem with this is that not only requests itself imports those\n+dependencies, but third-party code outside of the distros' control too.\n+\n+In reaction to these problems, the distro maintainers replaced\n+``requests.packages`` with a magical \"stub module\" that imports the correct\n+modules. The implementations were varying in quality and all had severe\n+problems. For example, a symlink (or hardlink) that links the correct modules\n+into place introduces problems regarding object identity, since you now have\n+two modules in `sys.modules` with the same API, but different identities::\n+\n+    requests.packages.urllib3 is not urllib3\n+\n+With version ``2.5.2``, requests started to maintain its own stub, so that\n+distro-specific breakage would be reduced to a minimum, even though the whole\n+issue is not requests' fault in the first place. See\n+https://github.com/kennethreitz/requests/pull/2375 for the corresponding pull\n+request.\n+'''\n+\n from __future__ import absolute_import\n+import sys\n+\n+try:\n+    from . import urllib3\n+except ImportError:\n+    import urllib3\n+    sys.modules['%s.urllib3' % __name__] = urllib3\n \n-from . import urllib3\n+try:\n+    from . import chardet\n+except ImportError:\n+    import chardet\n+    sys.modules['%s.chardet' % __name__] = chardet\ndiff --git a/requests/packages/urllib3/__init__.py b/requests/packages/urllib3/__init__.py\n--- a/requests/packages/urllib3/__init__.py\n+++ b/requests/packages/urllib3/__init__.py\n@@ -4,7 +4,7 @@\n \n __author__ = 'Andrey Petrov (<EMAIL>)'\n __license__ = 'MIT'\n-__version__ = '1.10.4'\n+__version__ = 'dev'\n \n \n from .connectionpool import (\n@@ -58,6 +58,8 @@ def add_stderr_logger(level=logging.DEBUG):\n import warnings\n # SecurityWarning's always go off by default.\n warnings.simplefilter('always', exceptions.SecurityWarning, append=True)\n+# SubjectAltNameWarning's should go off once per host\n+warnings.simplefilter('default', exceptions.SubjectAltNameWarning)\n # InsecurePlatformWarning's don't vary between requests, so we keep it default.\n warnings.simplefilter('default', exceptions.InsecurePlatformWarning,\n                       append=True)\ndiff --git a/requests/packages/urllib3/connection.py b/requests/packages/urllib3/connection.py\n--- a/requests/packages/urllib3/connection.py\n+++ b/requests/packages/urllib3/connection.py\n@@ -38,7 +38,7 @@ class ConnectionError(Exception):\n from .exceptions import (\n     ConnectTimeoutError,\n     SystemTimeWarning,\n-    SecurityWarning,\n+    SubjectAltNameWarning,\n )\n from .packages.ssl_match_hostname import match_hostname\n \n@@ -192,6 +192,9 @@ def set_cert(self, key_file=None, cert_file=None,\n                  cert_reqs=None, ca_certs=None,\n                  assert_hostname=None, assert_fingerprint=None):\n \n+        if ca_certs and cert_reqs is None:\n+            cert_reqs = 'CERT_REQUIRED'\n+\n         self.key_file = key_file\n         self.cert_file = cert_file\n         self.cert_reqs = cert_reqs\n@@ -245,10 +248,11 @@ def connect(self):\n             cert = self.sock.getpeercert()\n             if not cert.get('subjectAltName', ()):\n                 warnings.warn((\n-                    'Certificate has no `subjectAltName`, falling back to check for a `commonName` for now. '\n-                    'This feature is being removed by major browsers and deprecated by RFC 2818. '\n-                    '(See https://github.com/shazow/urllib3/issues/497 for details.)'),\n-                    SecurityWarning\n+                    'Certificate for {0} has no `subjectAltName`, falling back to check for a '\n+                    '`commonName` for now. This feature is being removed by major browsers and '\n+                    'deprecated by RFC 2818. (See https://github.com/shazow/urllib3/issues/497 '\n+                    'for details.)'.format(hostname)),\n+                    SubjectAltNameWarning\n                 )\n             match_hostname(cert, self.assert_hostname or hostname)\n \ndiff --git a/requests/packages/urllib3/connectionpool.py b/requests/packages/urllib3/connectionpool.py\n--- a/requests/packages/urllib3/connectionpool.py\n+++ b/requests/packages/urllib3/connectionpool.py\n@@ -17,6 +17,7 @@\n     ClosedPoolError,\n     ProtocolError,\n     EmptyPoolError,\n+    HeaderParsingError,\n     HostChangedError,\n     LocationValueError,\n     MaxRetryError,\n@@ -38,9 +39,10 @@\n from .response import HTTPResponse\n \n from .util.connection import is_connection_dropped\n+from .util.response import assert_header_parsing\n from .util.retry import Retry\n from .util.timeout import Timeout\n-from .util.url import get_host\n+from .util.url import get_host, Url\n \n \n xrange = six.moves.xrange\n@@ -120,7 +122,7 @@ class HTTPConnectionPool(ConnectionPool, RequestMethods):\n \n     :param maxsize:\n         Number of connections to save that can be reused. More than 1 is useful\n-        in multithreaded situations. If ``block`` is set to false, more\n+        in multithreaded situations. If ``block`` is set to False, more\n         connections will be created but they will not be saved once they've\n         been used.\n \n@@ -381,8 +383,19 @@ def _make_request(self, conn, method, url, timeout=_Default,\n         log.debug(\"\\\"%s %s %s\\\" %s %s\" % (method, url, http_version,\n                                           httplib_response.status,\n                                           httplib_response.length))\n+\n+        try:\n+            assert_header_parsing(httplib_response.msg)\n+        except HeaderParsingError as hpe:  # Platform-specific: Python 3\n+            log.warning(\n+                'Failed to parse headers (url=%s): %s',\n+                self._absolute_url(url), hpe, exc_info=True)\n+\n         return httplib_response\n \n+    def _absolute_url(self, path):\n+        return Url(scheme=self.scheme, host=self.host, port=self.port, path=path).url\n+\n     def close(self):\n         \"\"\"\n         Close all pooled connections and disable the pool.\n@@ -409,7 +422,7 @@ def is_same_host(self, url):\n \n         # TODO: Add optional support for socket.gethostbyname checking.\n         scheme, host, port = get_host(url)\n-\n+ \n         # Use explicit default port for comparison when none is given\n         if self.port and not port:\n             port = port_by_scheme.get(scheme)\n@@ -568,25 +581,22 @@ def urlopen(self, method, url, body=None, headers=None, retries=None,\n             # Close the connection. If a connection is reused on which there\n             # was a Certificate error, the next request will certainly raise\n             # another Certificate error.\n-            if conn:\n-                conn.close()\n-                conn = None\n+            conn = conn and conn.close()\n+            release_conn = True\n             raise SSLError(e)\n \n         except SSLError:\n             # Treat SSLError separately from BaseSSLError to preserve\n             # traceback.\n-            if conn:\n-                conn.close()\n-                conn = None\n+            conn = conn and conn.close()\n+            release_conn = True\n             raise\n \n         except (TimeoutError, HTTPException, SocketError, ConnectionError) as e:\n-            if conn:\n-                # Discard the connection for these exceptions. It will be\n-                # be replaced during the next _get_conn() call.\n-                conn.close()\n-                conn = None\n+            # Discard the connection for these exceptions. It will be\n+            # be replaced during the next _get_conn() call.\n+            conn = conn and conn.close()\n+            release_conn = True\n \n             if isinstance(e, SocketError) and self.proxy:\n                 e = ProxyError('Cannot connect to proxy.', e)\n@@ -626,6 +636,9 @@ def urlopen(self, method, url, body=None, headers=None, retries=None,\n                 retries = retries.increment(method, url, response=response, _pool=self)\n             except MaxRetryError:\n                 if retries.raise_on_redirect:\n+                    # Release the connection for this response, since we're not\n+                    # returning it to be released manually.\n+                    response.release_conn()\n                     raise\n                 return response\n \n@@ -683,6 +696,10 @@ def __init__(self, host, port=None,\n         HTTPConnectionPool.__init__(self, host, port, strict, timeout, maxsize,\n                                     block, headers, retries, _proxy, _proxy_headers,\n                                     **conn_kw)\n+\n+        if ca_certs and cert_reqs is None:\n+            cert_reqs = 'CERT_REQUIRED'\n+\n         self.key_file = key_file\n         self.cert_file = cert_file\n         self.cert_reqs = cert_reqs\ndiff --git a/requests/packages/urllib3/contrib/appengine.py b/requests/packages/urllib3/contrib/appengine.py\nnew file mode 100644\n--- /dev/null\n+++ b/requests/packages/urllib3/contrib/appengine.py\n@@ -0,0 +1,222 @@\n+import logging\n+import os\n+import warnings\n+\n+from ..exceptions import (\n+    HTTPError,\n+    HTTPWarning,\n+    MaxRetryError,\n+    ProtocolError,\n+    TimeoutError,\n+    SSLError\n+)\n+\n+from ..packages.six import BytesIO\n+from ..request import RequestMethods\n+from ..response import HTTPResponse\n+from ..util.timeout import Timeout\n+from ..util.retry import Retry\n+\n+try:\n+    from google.appengine.api import urlfetch\n+except ImportError:\n+    urlfetch = None\n+\n+\n+log = logging.getLogger(__name__)\n+\n+\n+class AppEnginePlatformWarning(HTTPWarning):\n+    pass\n+\n+\n+class AppEnginePlatformError(HTTPError):\n+    pass\n+\n+\n+class AppEngineManager(RequestMethods):\n+    \"\"\"\n+    Connection manager for Google App Engine sandbox applications.\n+\n+    This manager uses the URLFetch service directly instead of using the\n+    emulated httplib, and is subject to URLFetch limitations as described in\n+    the App Engine documentation here:\n+\n+        https://cloud.google.com/appengine/docs/python/urlfetch\n+\n+    Notably it will raise an AppEnginePlatformError if:\n+        * URLFetch is not available.\n+        * If you attempt to use this on GAEv2 (Managed VMs), as full socket\n+          support is available.\n+        * If a request size is more than 10 megabytes.\n+        * If a response size is more than 32 megabtyes.\n+        * If you use an unsupported request method such as OPTIONS.\n+\n+    Beyond those cases, it will raise normal urllib3 errors.\n+    \"\"\"\n+\n+    def __init__(self, headers=None, retries=None, validate_certificate=True):\n+        if not urlfetch:\n+            raise AppEnginePlatformError(\n+                \"URLFetch is not available in this environment.\")\n+\n+        if is_prod_appengine_v2():\n+            raise AppEnginePlatformError(\n+                \"Use normal urllib3.PoolManager instead of AppEngineManager\"\n+                \"on Managed VMs, as using URLFetch is not necessary in \"\n+                \"this environment.\")\n+\n+        warnings.warn(\n+            \"urllib3 is using URLFetch on Google App Engine sandbox instead \"\n+            \"of sockets. To use sockets directly instead of URLFetch see \"\n+            \"https://urllib3.readthedocs.org/en/latest/contrib.html.\",\n+            AppEnginePlatformWarning)\n+\n+        RequestMethods.__init__(self, headers)\n+        self.validate_certificate = validate_certificate\n+\n+        self.retries = retries or Retry.DEFAULT\n+\n+    def __enter__(self):\n+        return self\n+\n+    def __exit__(self, exc_type, exc_val, exc_tb):\n+        # Return False to re-raise any potential exceptions\n+        return False\n+\n+    def urlopen(self, method, url, body=None, headers=None,\n+                retries=None, redirect=True, timeout=Timeout.DEFAULT_TIMEOUT,\n+                **response_kw):\n+\n+        retries = self._get_retries(retries, redirect)\n+\n+        try:\n+            response = urlfetch.fetch(\n+                url,\n+                payload=body,\n+                method=method,\n+                headers=headers or {},\n+                allow_truncated=False,\n+                follow_redirects=(\n+                    redirect and\n+                    retries.redirect != 0 and\n+                    retries.total),\n+                deadline=self._get_absolute_timeout(timeout),\n+                validate_certificate=self.validate_certificate,\n+            )\n+        except urlfetch.DeadlineExceededError as e:\n+            raise TimeoutError(self, e)\n+\n+        except urlfetch.InvalidURLError as e:\n+            if 'too large' in e.message:\n+                raise AppEnginePlatformError(\n+                    \"URLFetch request too large, URLFetch only \"\n+                    \"supports requests up to 10mb in size.\", e)\n+            raise ProtocolError(e)\n+\n+        except urlfetch.DownloadError as e:\n+            if 'Too many redirects' in e.message:\n+                raise MaxRetryError(self, url, reason=e)\n+            raise ProtocolError(e)\n+\n+        except urlfetch.ResponseTooLargeError as e:\n+            raise AppEnginePlatformError(\n+                \"URLFetch response too large, URLFetch only supports\"\n+                \"responses up to 32mb in size.\", e)\n+\n+        except urlfetch.SSLCertificateError as e:\n+            raise SSLError(e)\n+\n+        except urlfetch.InvalidMethodError as e:\n+            raise AppEnginePlatformError(\n+                \"URLFetch does not support method: %s\" % method, e)\n+\n+        http_response = self._urlfetch_response_to_http_response(\n+            response, **response_kw)\n+\n+        # Check for redirect response\n+        if (http_response.get_redirect_location() and\n+                retries.raise_on_redirect and redirect):\n+            raise MaxRetryError(self, url, \"too many redirects\")\n+\n+        # Check if we should retry the HTTP response.\n+        if retries.is_forced_retry(method, status_code=http_response.status):\n+            retries = retries.increment(\n+                method, url, response=http_response, _pool=self)\n+            log.info(\"Forced retry: %s\" % url)\n+            retries.sleep()\n+            return self.urlopen(\n+                method, url,\n+                body=body, headers=headers,\n+                retries=retries, redirect=redirect,\n+                timeout=timeout, **response_kw)\n+\n+        return http_response\n+\n+    def _urlfetch_response_to_http_response(self, urlfetch_resp, **response_kw):\n+\n+        if is_prod_appengine_v1():\n+            # Production GAE handles deflate encoding automatically, but does\n+            # not remove the encoding header.\n+            content_encoding = urlfetch_resp.headers.get('content-encoding')\n+\n+            if content_encoding == 'deflate':\n+                del urlfetch_resp.headers['content-encoding']\n+\n+        return HTTPResponse(\n+            # In order for decoding to work, we must present the content as\n+            # a file-like object.\n+            body=BytesIO(urlfetch_resp.content),\n+            headers=urlfetch_resp.headers,\n+            status=urlfetch_resp.status_code,\n+            **response_kw\n+        )\n+\n+    def _get_absolute_timeout(self, timeout):\n+        if timeout is Timeout.DEFAULT_TIMEOUT:\n+            return 5  # 5s is the default timeout for URLFetch.\n+        if isinstance(timeout, Timeout):\n+            if not timeout.read is timeout.connect:\n+                warnings.warn(\n+                    \"URLFetch does not support granular timeout settings, \"\n+                    \"reverting to total timeout.\", AppEnginePlatformWarning)\n+            return timeout.total\n+        return timeout\n+\n+    def _get_retries(self, retries, redirect):\n+        if not isinstance(retries, Retry):\n+            retries = Retry.from_int(\n+                retries, redirect=redirect, default=self.retries)\n+\n+        if retries.connect or retries.read or retries.redirect:\n+            warnings.warn(\n+                \"URLFetch only supports total retries and does not \"\n+                \"recognize connect, read, or redirect retry parameters.\",\n+                AppEnginePlatformWarning)\n+\n+        return retries\n+\n+\n+def is_appengine():\n+    return (is_local_appengine() or\n+            is_prod_appengine_v1() or\n+            is_prod_appengine_v2())\n+\n+\n+def is_appengine_sandbox():\n+    return is_appengine() and not is_prod_appengine_v2()\n+\n+\n+def is_local_appengine():\n+    return ('APPENGINE_RUNTIME' in os.environ and\n+            'Development/' in os.environ['SERVER_SOFTWARE'])\n+\n+\n+def is_prod_appengine_v1():\n+    return ('APPENGINE_RUNTIME' in os.environ and\n+            'Google App Engine/' in os.environ['SERVER_SOFTWARE'] and\n+            not is_prod_appengine_v2())\n+\n+\n+def is_prod_appengine_v2():\n+    return os.environ.get('GAE_VM', False) == 'true'\ndiff --git a/requests/packages/urllib3/contrib/pyopenssl.py b/requests/packages/urllib3/contrib/pyopenssl.py\n--- a/requests/packages/urllib3/contrib/pyopenssl.py\n+++ b/requests/packages/urllib3/contrib/pyopenssl.py\n@@ -85,6 +85,14 @@\n \n DEFAULT_SSL_CIPHER_LIST = util.ssl_.DEFAULT_CIPHERS\n \n+# OpenSSL will only write 16K at a time\n+SSL_WRITE_BLOCKSIZE = 16384\n+\n+try:\n+    _ = memoryview\n+    has_memoryview = True\n+except NameError:\n+    has_memoryview = False\n \n orig_util_HAS_SNI = util.HAS_SNI\n orig_connection_ssl_wrap_socket = connection.ssl_wrap_socket\n@@ -204,13 +212,21 @@ def _send_until_done(self, data):\n                 continue\n \n     def sendall(self, data):\n-        while len(data):\n-            sent = self._send_until_done(data)\n-            data = data[sent:]\n+        if has_memoryview and not isinstance(data, memoryview):\n+            data = memoryview(data)\n+\n+        total_sent = 0\n+        while total_sent < len(data):\n+            sent = self._send_until_done(data[total_sent:total_sent+SSL_WRITE_BLOCKSIZE])\n+            total_sent += sent\n+\n+    def shutdown(self):\n+        # FIXME rethrow compatible exceptions should we ever use this\n+        self.connection.shutdown()\n \n     def close(self):\n         if self._makefile_refs < 1:\n-            return self.connection.shutdown()\n+            return self.connection.close()\n         else:\n             self._makefile_refs -= 1\n \n@@ -287,7 +303,7 @@ def ssl_wrap_socket(sock, keyfile=None, certfile=None, cert_reqs=None,\n                 raise timeout('select timed out')\n             continue\n         except OpenSSL.SSL.Error as e:\n-            raise ssl.SSLError('bad handshake', e)\n+            raise ssl.SSLError('bad handshake: %r' % e)\n         break\n \n     return WrappedSocket(cnx, sock)\ndiff --git a/requests/packages/urllib3/exceptions.py b/requests/packages/urllib3/exceptions.py\n--- a/requests/packages/urllib3/exceptions.py\n+++ b/requests/packages/urllib3/exceptions.py\n@@ -149,6 +149,11 @@ class SecurityWarning(HTTPWarning):\n     pass\n \n \n+class SubjectAltNameWarning(SecurityWarning):\n+    \"Warned when connecting to a host with a certificate missing a SAN.\"\n+    pass\n+\n+\n class InsecureRequestWarning(SecurityWarning):\n     \"Warned when making an unverified HTTPS request.\"\n     pass\n@@ -167,3 +172,19 @@ class InsecurePlatformWarning(SecurityWarning):\n class ResponseNotChunked(ProtocolError, ValueError):\n     \"Response needs to be chunked in order to read it as chunks.\"\n     pass\n+\n+\n+class ProxySchemeUnknown(AssertionError, ValueError):\n+    \"ProxyManager does not support the supplied scheme\"\n+    # TODO(t-8ch): Stop inheriting from AssertionError in v2.0.\n+\n+    def __init__(self, scheme):\n+        message = \"Not supported proxy scheme %s\" % scheme\n+        super(ProxySchemeUnknown, self).__init__(message)\n+\n+\n+class HeaderParsingError(HTTPError):\n+    \"Raised by assert_header_parsing, but we convert it to a log.warning statement.\"\n+    def __init__(self, defects, unparsed_data):\n+        message = '%s, unparsed data: %r' % (defects or 'Unknown', unparsed_data)\n+        super(HeaderParsingError, self).__init__(message)\ndiff --git a/requests/packages/urllib3/poolmanager.py b/requests/packages/urllib3/poolmanager.py\n--- a/requests/packages/urllib3/poolmanager.py\n+++ b/requests/packages/urllib3/poolmanager.py\n@@ -8,7 +8,7 @@\n from ._collections import RecentlyUsedContainer\n from .connectionpool import HTTPConnectionPool, HTTPSConnectionPool\n from .connectionpool import port_by_scheme\n-from .exceptions import LocationValueError, MaxRetryError\n+from .exceptions import LocationValueError, MaxRetryError, ProxySchemeUnknown\n from .request import RequestMethods\n from .util.url import parse_url\n from .util.retry import Retry\n@@ -227,8 +227,8 @@ def __init__(self, proxy_url, num_pools=10, headers=None,\n             port = port_by_scheme.get(proxy.scheme, 80)\n             proxy = proxy._replace(port=port)\n \n-        assert proxy.scheme in (\"http\", \"https\"), \\\n-            'Not supported proxy scheme %s' % proxy.scheme\n+        if proxy.scheme not in (\"http\", \"https\"):\n+            raise ProxySchemeUnknown(proxy.scheme)\n \n         self.proxy = proxy\n         self.proxy_headers = proxy_headers or {}\ndiff --git a/requests/packages/urllib3/request.py b/requests/packages/urllib3/request.py\n--- a/requests/packages/urllib3/request.py\n+++ b/requests/packages/urllib3/request.py\n@@ -71,14 +71,22 @@ def request(self, method, url, fields=None, headers=None, **urlopen_kw):\n                                             headers=headers,\n                                             **urlopen_kw)\n \n-    def request_encode_url(self, method, url, fields=None, **urlopen_kw):\n+    def request_encode_url(self, method, url, fields=None, headers=None,\n+                           **urlopen_kw):\n         \"\"\"\n         Make a request using :meth:`urlopen` with the ``fields`` encoded in\n         the url. This is useful for request methods like GET, HEAD, DELETE, etc.\n         \"\"\"\n+        if headers is None:\n+            headers = self.headers\n+\n+        extra_kw = {'headers': headers}\n+        extra_kw.update(urlopen_kw)\n+\n         if fields:\n             url += '?' + urlencode(fields)\n-        return self.urlopen(method, url, **urlopen_kw)\n+\n+        return self.urlopen(method, url, **extra_kw)\n \n     def request_encode_body(self, method, url, fields=None, headers=None,\n                             encode_multipart=True, multipart_boundary=None,\ndiff --git a/requests/packages/urllib3/response.py b/requests/packages/urllib3/response.py\n--- a/requests/packages/urllib3/response.py\n+++ b/requests/packages/urllib3/response.py\n@@ -2,6 +2,7 @@\n     import http.client as httplib\n except ImportError:\n     import httplib\n+from contextlib import contextmanager\n import zlib\n import io\n from socket import timeout as SocketTimeout\n@@ -12,7 +13,7 @@\n )\n from .packages.six import string_types as basestring, binary_type, PY3\n from .connection import HTTPException, BaseSSLError\n-from .util.response import is_fp_closed\n+from .util.response import is_fp_closed, is_response_to_head\n \n \n class DeflateDecoder(object):\n@@ -202,6 +203,47 @@ def _decode(self, data, decode_content, flush_decoder):\n \n         return data\n \n+    @contextmanager\n+    def _error_catcher(self):\n+        \"\"\"\n+        Catch low-level python exceptions, instead re-raising urllib3\n+        variants, so that low-level exceptions are not leaked in the\n+        high-level api.\n+\n+        On exit, release the connection back to the pool.\n+        \"\"\"\n+        try:\n+            try:\n+                yield\n+\n+            except SocketTimeout:\n+                # FIXME: Ideally we'd like to include the url in the ReadTimeoutError but\n+                # there is yet no clean way to get at it from this context.\n+                raise ReadTimeoutError(self._pool, None, 'Read timed out.')\n+\n+            except BaseSSLError as e:\n+                # FIXME: Is there a better way to differentiate between SSLErrors?\n+                if 'read operation timed out' not in str(e):  # Defensive:\n+                    # This shouldn't happen but just in case we're missing an edge\n+                    # case, let's avoid swallowing SSL errors.\n+                    raise\n+\n+                raise ReadTimeoutError(self._pool, None, 'Read timed out.')\n+\n+            except HTTPException as e:\n+                # This includes IncompleteRead.\n+                raise ProtocolError('Connection broken: %r' % e, e)\n+        except Exception:\n+            # The response may not be closed but we're not going to use it anymore\n+            # so close it now to ensure that the connection is released back to the pool.\n+            if self._original_response and not self._original_response.isclosed():\n+                self._original_response.close()\n+\n+            raise\n+        finally:\n+            if self._original_response and self._original_response.isclosed():\n+                self.release_conn()\n+\n     def read(self, amt=None, decode_content=None, cache_content=False):\n         \"\"\"\n         Similar to :meth:`httplib.HTTPResponse.read`, but with two additional\n@@ -231,45 +273,28 @@ def read(self, amt=None, decode_content=None, cache_content=False):\n             return\n \n         flush_decoder = False\n-\n-        try:\n-            try:\n-                if amt is None:\n-                    # cStringIO doesn't like amt=None\n-                    data = self._fp.read()\n+        data = None\n+\n+        with self._error_catcher():\n+            if amt is None:\n+                # cStringIO doesn't like amt=None\n+                data = self._fp.read()\n+                flush_decoder = True\n+            else:\n+                cache_content = False\n+                data = self._fp.read(amt)\n+                if amt != 0 and not data:  # Platform-specific: Buggy versions of Python.\n+                    # Close the connection when no data is returned\n+                    #\n+                    # This is redundant to what httplib/http.client _should_\n+                    # already do.  However, versions of python released before\n+                    # December 15, 2012 (http://bugs.python.org/issue16298) do\n+                    # not properly close the connection in all cases. There is\n+                    # no harm in redundantly calling close.\n+                    self._fp.close()\n                     flush_decoder = True\n-                else:\n-                    cache_content = False\n-                    data = self._fp.read(amt)\n-                    if amt != 0 and not data:  # Platform-specific: Buggy versions of Python.\n-                        # Close the connection when no data is returned\n-                        #\n-                        # This is redundant to what httplib/http.client _should_\n-                        # already do.  However, versions of python released before\n-                        # December 15, 2012 (http://bugs.python.org/issue16298) do\n-                        # not properly close the connection in all cases. There is\n-                        # no harm in redundantly calling close.\n-                        self._fp.close()\n-                        flush_decoder = True\n-\n-            except SocketTimeout:\n-                # FIXME: Ideally we'd like to include the url in the ReadTimeoutError but\n-                # there is yet no clean way to get at it from this context.\n-                raise ReadTimeoutError(self._pool, None, 'Read timed out.')\n-\n-            except BaseSSLError as e:\n-                # FIXME: Is there a better way to differentiate between SSLErrors?\n-                if 'read operation timed out' not in str(e):  # Defensive:\n-                    # This shouldn't happen but just in case we're missing an edge\n-                    # case, let's avoid swallowing SSL errors.\n-                    raise\n-\n-                raise ReadTimeoutError(self._pool, None, 'Read timed out.')\n-\n-            except HTTPException as e:\n-                # This includes IncompleteRead.\n-                raise ProtocolError('Connection broken: %r' % e, e)\n \n+        if data:\n             self._fp_bytes_read += len(data)\n \n             data = self._decode(data, decode_content, flush_decoder)\n@@ -277,11 +302,8 @@ def read(self, amt=None, decode_content=None, cache_content=False):\n             if cache_content:\n                 self._body = data\n \n-            return data\n+        return data\n \n-        finally:\n-            if self._original_response and self._original_response.isclosed():\n-                self.release_conn()\n \n     def stream(self, amt=2**16, decode_content=None):\n         \"\"\"\n@@ -319,6 +341,7 @@ def from_httplib(ResponseCls, r, **response_kw):\n         with ``original_response=r``.\n         \"\"\"\n         headers = r.msg\n+\n         if not isinstance(headers, HTTPHeaderDict):\n             if PY3: # Python 3\n                 headers = HTTPHeaderDict(headers.items())\n@@ -437,30 +460,29 @@ def read_chunked(self, amt=None, decode_content=None):\n             raise ResponseNotChunked(\"Response is not chunked. \"\n                 \"Header 'transfer-encoding: chunked' is missing.\")\n \n-        if self._original_response and self._original_response._method.upper() == 'HEAD':\n-            # Don't bother reading the body of a HEAD request.\n-            # FIXME: Can we do this somehow without accessing private httplib _method?\n+        # Don't bother reading the body of a HEAD request.\n+        if self._original_response and is_response_to_head(self._original_response):\n             self._original_response.close()\n             return\n \n-        while True:\n-            self._update_chunk_length()\n-            if self.chunk_left == 0:\n-                break\n-            chunk = self._handle_chunk(amt)\n-            yield self._decode(chunk, decode_content=decode_content,\n-                               flush_decoder=True)\n-\n-        # Chunk content ends with \\r\\n: discard it.\n-        while True:\n-            line = self._fp.fp.readline()\n-            if not line:\n-                # Some sites may not end with '\\r\\n'.\n-                break\n-            if line == b'\\r\\n':\n-                break\n-\n-        # We read everything; close the \"file\".\n-        if self._original_response:\n-            self._original_response.close()\n-        self.release_conn()\n+        with self._error_catcher():\n+            while True:\n+                self._update_chunk_length()\n+                if self.chunk_left == 0:\n+                    break\n+                chunk = self._handle_chunk(amt)\n+                yield self._decode(chunk, decode_content=decode_content,\n+                                   flush_decoder=True)\n+\n+            # Chunk content ends with \\r\\n: discard it.\n+            while True:\n+                line = self._fp.fp.readline()\n+                if not line:\n+                    # Some sites may not end with '\\r\\n'.\n+                    break\n+                if line == b'\\r\\n':\n+                    break\n+\n+            # We read everything; close the \"file\".\n+            if self._original_response:\n+                self._original_response.close()\ndiff --git a/requests/packages/urllib3/util/connection.py b/requests/packages/urllib3/util/connection.py\n--- a/requests/packages/urllib3/util/connection.py\n+++ b/requests/packages/urllib3/util/connection.py\n@@ -60,6 +60,8 @@ def create_connection(address, timeout=socket._GLOBAL_DEFAULT_TIMEOUT,\n     \"\"\"\n \n     host, port = address\n+    if host.startswith('['):\n+        host = host.strip('[]')\n     err = None\n     for res in socket.getaddrinfo(host, port, 0, socket.SOCK_STREAM):\n         af, socktype, proto, canonname, sa = res\ndiff --git a/requests/packages/urllib3/util/response.py b/requests/packages/urllib3/util/response.py\n--- a/requests/packages/urllib3/util/response.py\n+++ b/requests/packages/urllib3/util/response.py\n@@ -1,3 +1,11 @@\n+try:\n+    import http.client as httplib\n+except ImportError:\n+    import httplib\n+\n+from ..exceptions import HeaderParsingError\n+\n+\n def is_fp_closed(obj):\n     \"\"\"\n     Checks whether a given file-like object is closed.\n@@ -20,3 +28,49 @@ def is_fp_closed(obj):\n         pass\n \n     raise ValueError(\"Unable to determine whether fp is closed.\")\n+\n+\n+def assert_header_parsing(headers):\n+    \"\"\"\n+    Asserts whether all headers have been successfully parsed.\n+    Extracts encountered errors from the result of parsing headers.\n+\n+    Only works on Python 3.\n+\n+    :param headers: Headers to verify.\n+    :type headers: `httplib.HTTPMessage`.\n+\n+    :raises urllib3.exceptions.HeaderParsingError:\n+        If parsing errors are found.\n+    \"\"\"\n+\n+    # This will fail silently if we pass in the wrong kind of parameter.\n+    # To make debugging easier add an explicit check.\n+    if not isinstance(headers, httplib.HTTPMessage):\n+        raise TypeError('expected httplib.Message, got {}.'.format(\n+            type(headers)))\n+\n+    defects = getattr(headers, 'defects', None)\n+    get_payload = getattr(headers, 'get_payload', None)\n+\n+    unparsed_data = None\n+    if get_payload:  # Platform-specific: Python 3.\n+        unparsed_data = get_payload()\n+\n+    if defects or unparsed_data:\n+        raise HeaderParsingError(defects=defects, unparsed_data=unparsed_data)\n+\n+\n+def is_response_to_head(response):\n+    \"\"\"\n+    Checks, wether a the request of a response has been a HEAD-request.\n+    Handles the quirks of AppEngine.\n+\n+    :param conn:\n+    :type conn: :class:`httplib.HTTPResponse`\n+    \"\"\"\n+    # FIXME: Can we do this somehow without accessing private httplib _method?\n+    method = response._method\n+    if isinstance(method, int):  # Platform-specific: Appengine\n+        return method == 3\n+    return method.upper() == 'HEAD'\ndiff --git a/requests/packages/urllib3/util/retry.py b/requests/packages/urllib3/util/retry.py\n--- a/requests/packages/urllib3/util/retry.py\n+++ b/requests/packages/urllib3/util/retry.py\n@@ -94,7 +94,7 @@ class Retry(object):\n \n         seconds. If the backoff_factor is 0.1, then :func:`.sleep` will sleep\n         for [0.1s, 0.2s, 0.4s, ...] between retries. It will never be longer\n-        than :attr:`Retry.MAX_BACKOFF`.\n+        than :attr:`Retry.BACKOFF_MAX`.\n \n         By default, backoff is disabled (set to 0).\n \ndiff --git a/requests/packages/urllib3/util/ssl_.py b/requests/packages/urllib3/util/ssl_.py\n--- a/requests/packages/urllib3/util/ssl_.py\n+++ b/requests/packages/urllib3/util/ssl_.py\n@@ -8,6 +8,13 @@\n HAS_SNI = False\n create_default_context = None\n \n+# Maps the length of a digest to a possible hash function producing this digest\n+HASHFUNC_MAP = {\n+    32: md5,\n+    40: sha1,\n+    64: sha256,\n+}\n+\n import errno\n import warnings\n \n@@ -112,31 +119,21 @@ def assert_fingerprint(cert, fingerprint):\n         Fingerprint as string of hexdigits, can be interspersed by colons.\n     \"\"\"\n \n-    # Maps the length of a digest to a possible hash function producing\n-    # this digest.\n-    hashfunc_map = {\n-        16: md5,\n-        20: sha1,\n-        32: sha256,\n-    }\n-\n     fingerprint = fingerprint.replace(':', '').lower()\n-    digest_length, odd = divmod(len(fingerprint), 2)\n-\n-    if odd or digest_length not in hashfunc_map:\n-        raise SSLError('Fingerprint is of invalid length.')\n+    digest_length = len(fingerprint)\n+    hashfunc = HASHFUNC_MAP.get(digest_length)\n+    if not hashfunc:\n+        raise SSLError(\n+            'Fingerprint of invalid length: {0}'.format(fingerprint))\n \n     # We need encode() here for py32; works on py2 and p33.\n     fingerprint_bytes = unhexlify(fingerprint.encode())\n \n-    hashfunc = hashfunc_map[digest_length]\n-\n     cert_digest = hashfunc(cert).digest()\n \n-    if not cert_digest == fingerprint_bytes:\n+    if cert_digest != fingerprint_bytes:\n         raise SSLError('Fingerprints did not match. Expected \"{0}\", got \"{1}\".'\n-                       .format(hexlify(fingerprint_bytes),\n-                               hexlify(cert_digest)))\n+                       .format(fingerprint, hexlify(cert_digest)))\n \n \n def resolve_cert_reqs(candidate):\n"}
{"instance_id": "psf__requests-2873", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/utils.py b/requests/utils.py\n--- a/requests/utils.py\n+++ b/requests/utils.py\n@@ -48,19 +48,26 @@ def dict_to_sequence(d):\n \n \n def super_len(o):\n+    total_length = 0\n+    current_position = 0\n+\n     if hasattr(o, '__len__'):\n-        return len(o)\n+        total_length = len(o)\n+\n+    elif hasattr(o, 'len'):\n+        total_length = o.len\n \n-    if hasattr(o, 'len'):\n-        return o.len\n+    elif hasattr(o, 'getvalue'):\n+        # e.g. BytesIO, cStringIO.StringIO\n+        total_length = len(o.getvalue())\n \n-    if hasattr(o, 'fileno'):\n+    elif hasattr(o, 'fileno'):\n         try:\n             fileno = o.fileno()\n         except io.UnsupportedOperation:\n             pass\n         else:\n-            filesize = os.fstat(fileno).st_size\n+            total_length = os.fstat(fileno).st_size\n \n             # Having used fstat to determine the file length, we need to\n             # confirm that this file was opened up in binary mode.\n@@ -75,11 +82,10 @@ def super_len(o):\n                     FileModeWarning\n                 )\n \n-            return filesize\n+    if hasattr(o, 'tell'):\n+        current_position = o.tell()\n \n-    if hasattr(o, 'getvalue'):\n-        # e.g. BytesIO, cStringIO.StringIO\n-        return len(o.getvalue())\n+    return max(0, total_length - current_position)\n \n \n def get_netrc_auth(url, raise_errors=False):\n"}
{"instance_id": "psf__requests-2931", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/models.py b/requests/models.py\n--- a/requests/models.py\n+++ b/requests/models.py\n@@ -81,7 +81,7 @@ def _encode_params(data):\n         \"\"\"\n \n         if isinstance(data, (str, bytes)):\n-            return to_native_string(data)\n+            return data\n         elif hasattr(data, 'read'):\n             return data\n         elif hasattr(data, '__iter__'):\n@@ -385,6 +385,9 @@ def prepare_url(self, url, params):\n             if isinstance(fragment, str):\n                 fragment = fragment.encode('utf-8')\n \n+        if isinstance(params, (str, bytes)):\n+            params = to_native_string(params)\n+\n         enc_params = self._encode_params(params)\n         if enc_params:\n             if query:\n"}
{"instance_id": "psf__requests-3362", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/utils.py b/requests/utils.py\n--- a/requests/utils.py\n+++ b/requests/utils.py\n@@ -358,13 +358,20 @@ def get_encoding_from_headers(headers):\n \n def stream_decode_response_unicode(iterator, r):\n     \"\"\"Stream decodes a iterator.\"\"\"\n+    encoding = r.encoding\n \n-    if r.encoding is None:\n-        for item in iterator:\n-            yield item\n-        return\n+    if encoding is None:\n+        encoding = r.apparent_encoding\n+\n+    try:\n+        decoder = codecs.getincrementaldecoder(encoding)(errors='replace')\n+    except (LookupError, TypeError):\n+        # A LookupError is raised if the encoding was not found which could\n+        # indicate a misspelling or similar mistake.\n+        #\n+        # A TypeError can be raised if encoding is None\n+        raise UnicodeError(\"Unable to decode contents with encoding %s.\" % encoding)\n \n-    decoder = codecs.getincrementaldecoder(r.encoding)(errors='replace')\n     for chunk in iterator:\n         rv = decoder.decode(chunk)\n         if rv:\n"}
{"instance_id": "psf__requests-3738", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/models.py b/requests/models.py\n--- a/requests/models.py\n+++ b/requests/models.py\n@@ -347,9 +347,9 @@ def prepare_url(self, url, params):\n         url = url.lstrip()\n \n         # Don't do any URL preparation for non-HTTP schemes like `mailto`,\n-        # `data`, `http+unix` etc to work around exceptions from `url_parse`,\n-        # which handles RFC 3986 only.\n-        if ':' in url and not url.lower().startswith(('http://', 'https://')):\n+        # `data` etc to work around exceptions from `url_parse`, which\n+        # handles RFC 3986 only.\n+        if ':' in url and not url.lower().startswith('http'):\n             self.url = url\n             return\n \n"}
{"instance_id": "psf__requests-4106", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/packages.py b/requests/packages.py\n--- a/requests/packages.py\n+++ b/requests/packages.py\n@@ -4,7 +4,7 @@\n # I don't like it either. Just look the other way. :)\n \n for package in ('urllib3', 'idna', 'chardet'):\n-    __import__(package)\n+    locals()[package] = __import__(package)\n     # This traversal is apparently necessary such that the identities are\n     # preserved (requests.packages.urllib3.* is urllib3.*)\n     for mod in list(sys.modules):\n"}
{"instance_id": "psf__requests-4356", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/adapters.py b/requests/adapters.py\n--- a/requests/adapters.py\n+++ b/requests/adapters.py\n@@ -13,6 +13,7 @@\n \n from urllib3.poolmanager import PoolManager, proxy_from_url\n from urllib3.response import HTTPResponse\n+from urllib3.util import parse_url\n from urllib3.util import Timeout as TimeoutSauce\n from urllib3.util.retry import Retry\n from urllib3.exceptions import ClosedPoolError\n@@ -34,7 +35,7 @@\n from .structures import CaseInsensitiveDict\n from .cookies import extract_cookies_to_jar\n from .exceptions import (ConnectionError, ConnectTimeout, ReadTimeout, SSLError,\n-                         ProxyError, RetryError, InvalidSchema)\n+                         ProxyError, RetryError, InvalidSchema, InvalidProxyURL)\n from .auth import _basic_auth_str\n \n try:\n@@ -300,6 +301,10 @@ def get_connection(self, url, proxies=None):\n \n         if proxy:\n             proxy = prepend_scheme_if_needed(proxy, 'http')\n+            proxy_url = parse_url(proxy)\n+            if not proxy_url.host:\n+                raise InvalidProxyURL(\"Please check proxy URL. It is malformed\"\n+                                      \" and could be missing the host.\")\n             proxy_manager = self.proxy_manager_for(proxy)\n             conn = proxy_manager.connection_from_url(url)\n         else:\ndiff --git a/requests/exceptions.py b/requests/exceptions.py\n--- a/requests/exceptions.py\n+++ b/requests/exceptions.py\n@@ -85,6 +85,10 @@ class InvalidHeader(RequestException, ValueError):\n     \"\"\"The header value provided was somehow invalid.\"\"\"\n \n \n+class InvalidProxyURL(InvalidURL):\n+    \"\"\"The proxy URL provided is invalid.\"\"\"\n+\n+\n class ChunkedEncodingError(RequestException):\n     \"\"\"The server declared chunked encoding but sent an invalid chunk.\"\"\"\n \n"}
{"instance_id": "psf__requests-4718", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/sessions.py b/requests/sessions.py\n--- a/requests/sessions.py\n+++ b/requests/sessions.py\n@@ -115,6 +115,22 @@ def get_redirect_target(self, resp):\n             return to_native_string(location, 'utf8')\n         return None\n \n+    def should_strip_auth(self, old_url, new_url):\n+        \"\"\"Decide whether Authorization header should be removed when redirecting\"\"\"\n+        old_parsed = urlparse(old_url)\n+        new_parsed = urlparse(new_url)\n+        if old_parsed.hostname != new_parsed.hostname:\n+            return True\n+        # Special case: allow http -> https redirect when using the standard\n+        # ports. This isn't specified by RFC 7235, but is kept to avoid\n+        # breaking backwards compatibility with older versions of requests\n+        # that allowed any redirects on the same host.\n+        if (old_parsed.scheme == 'http' and old_parsed.port in (80, None)\n+                and new_parsed.scheme == 'https' and new_parsed.port in (443, None)):\n+            return False\n+        # Standard case: root URI must match\n+        return old_parsed.port != new_parsed.port or old_parsed.scheme != new_parsed.scheme\n+\n     def resolve_redirects(self, resp, req, stream=False, timeout=None,\n                           verify=True, cert=None, proxies=None, yield_requests=False, **adapter_kwargs):\n         \"\"\"Receives a Response. Returns a generator of Responses or Requests.\"\"\"\n@@ -236,14 +252,10 @@ def rebuild_auth(self, prepared_request, response):\n         headers = prepared_request.headers\n         url = prepared_request.url\n \n-        if 'Authorization' in headers:\n+        if 'Authorization' in headers and self.should_strip_auth(response.request.url, url):\n             # If we get redirected to a new host, we should strip out any\n             # authentication headers.\n-            original_parsed = urlparse(response.request.url)\n-            redirect_parsed = urlparse(url)\n-\n-            if (original_parsed.hostname != redirect_parsed.hostname):\n-                del headers['Authorization']\n+            del headers['Authorization']\n \n         # .netrc might have more auth for us on our new host.\n         new_auth = get_netrc_auth(url) if self.trust_env else None\n"}
{"instance_id": "psf__requests-5087", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/models.py b/requests/models.py\n--- a/requests/models.py\n+++ b/requests/models.py\n@@ -640,6 +640,10 @@ def __init__(self):\n         #: is a response.\n         self.request = None\n \n+        #: If there was an error in the processing of content,\n+        #: then save the error that would return the same error when you re-appeal.\n+        self._error = None\n+\n     def __enter__(self):\n         return self\n \n@@ -749,12 +753,21 @@ def generate():\n                 try:\n                     for chunk in self.raw.stream(chunk_size, decode_content=True):\n                         yield chunk\n+\n                 except ProtocolError as e:\n-                    raise ChunkedEncodingError(e)\n+                    self._error = ChunkedEncodingError(e)\n+\n                 except DecodeError as e:\n-                    raise ContentDecodingError(e)\n+                    self._error = ContentDecodingError(e)\n+\n                 except ReadTimeoutError as e:\n-                    raise ConnectionError(e)\n+                    self._error = ConnectionError(e)\n+\n+                finally:\n+                    # if we had an error - throw the saved error\n+                    if self._error:\n+                        raise self._error\n+\n             else:\n                 # Standard file-like object.\n                 while True:\n@@ -827,6 +840,10 @@ def content(self):\n             else:\n                 self._content = b''.join(self.iter_content(CONTENT_CHUNK_SIZE)) or b''\n \n+        # if we had an error - throw the saved error\n+        if self._error is not None:\n+            raise self._error\n+\n         self._content_consumed = True\n         # don't need to release the connection; that's been handled by urllib3\n         # since we exhausted the data.\n"}
{"instance_id": "psf__requests-5414", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/models.py b/requests/models.py\n--- a/requests/models.py\n+++ b/requests/models.py\n@@ -403,7 +403,7 @@ def prepare_url(self, url, params):\n                 host = self._get_idna_encoded_host(host)\n             except UnicodeError:\n                 raise InvalidURL('URL has an invalid label.')\n-        elif host.startswith(u'*'):\n+        elif host.startswith((u'*', u'.')):\n             raise InvalidURL('URL has an invalid label.')\n \n         # Carefully reconstruct the network location\n"}
{"instance_id": "psf__requests-6028", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/utils.py b/requests/utils.py\n--- a/requests/utils.py\n+++ b/requests/utils.py\n@@ -974,6 +974,10 @@ def prepend_scheme_if_needed(url, new_scheme):\n     if not netloc:\n         netloc, path = path, netloc\n \n+    if auth:\n+        # parse_url doesn't provide the netloc with auth\n+        # so we'll add it ourselves.\n+        netloc = '@'.join([auth, netloc])\n     if scheme is None:\n         scheme = new_scheme\n     if path is None:\n"}
{"instance_id": "psf__requests-2754", "model_name_or_path": "gold", "model_patch": "diff --git a/requests/sessions.py b/requests/sessions.py\n--- a/requests/sessions.py\n+++ b/requests/sessions.py\n@@ -13,7 +13,7 @@\n from datetime import datetime\n \n from .auth import _basic_auth_str\n-from .compat import cookielib, OrderedDict, urljoin, urlparse\n+from .compat import cookielib, OrderedDict, urljoin, urlparse, is_py3, str\n from .cookies import (\n     cookiejar_from_dict, extract_cookies_to_jar, RequestsCookieJar, merge_cookies)\n from .models import Request, PreparedRequest, DEFAULT_REDIRECT_LIMIT\n@@ -132,6 +132,13 @@ def resolve_redirects(self, response, stream=False, timeout=None,\n             parsed = urlparse(location_url)\n             location_url = parsed.geturl()\n \n+            # On Python 3, the location header was decoded using Latin 1, but\n+            # urlparse in requote_uri will encode it with UTF-8 before quoting.\n+            # Because of this insanity, we need to fix it up ourselves by\n+            # sending the URL back to bytes ourselves.\n+            if is_py3 and isinstance(location_url, str):\n+                location_url = location_url.encode('latin1')\n+\n             # Facilitate relative 'location' headers, as allowed by RFC 7231.\n             # (e.g. '/path/to/resource' instead of 'http://domain.tld/path/to/resource')\n             # Compliant with RFC3986, we percent encode the url.\ndiff --git a/requests/utils.py b/requests/utils.py\n--- a/requests/utils.py\n+++ b/requests/utils.py\n@@ -26,7 +26,7 @@\n from .compat import parse_http_list as _parse_list_header\n from .compat import (quote, urlparse, bytes, str, OrderedDict, unquote, is_py2,\n                      builtin_str, getproxies, proxy_bypass, urlunparse,\n-                     basestring)\n+                     basestring, is_py3)\n from .cookies import RequestsCookieJar, cookiejar_from_dict\n from .structures import CaseInsensitiveDict\n from .exceptions import InvalidURL, FileModeWarning\n@@ -422,7 +422,26 @@ def unquote_unreserved(uri):\n     \"\"\"Un-escape any percent-escape sequences in a URI that are unreserved\n     characters. This leaves all reserved, illegal and non-ASCII bytes encoded.\n     \"\"\"\n-    parts = uri.split('%')\n+    # This convert function is used to optionally convert the output of `chr`.\n+    # In Python 3, `chr` returns a unicode string, while in Python 2 it returns\n+    # a bytestring. Here we deal with that by optionally converting.\n+    def convert(is_bytes, c):\n+        if is_py2 and not is_bytes:\n+            return c.decode('ascii')\n+        elif is_py3 and is_bytes:\n+            return c.encode('ascii')\n+        else:\n+            return c\n+\n+    # Handle both bytestrings and unicode strings.\n+    is_bytes = isinstance(uri, bytes)\n+    splitchar = u'%'\n+    base = u''\n+    if is_bytes:\n+        splitchar = splitchar.encode('ascii')\n+        base = base.encode('ascii')\n+\n+    parts = uri.split(splitchar)\n     for i in range(1, len(parts)):\n         h = parts[i][0:2]\n         if len(h) == 2 and h.isalnum():\n@@ -432,12 +451,12 @@ def unquote_unreserved(uri):\n                 raise InvalidURL(\"Invalid percent-escape sequence: '%s'\" % h)\n \n             if c in UNRESERVED_SET:\n-                parts[i] = c + parts[i][2:]\n+                parts[i] = convert(is_bytes, c) + parts[i][2:]\n             else:\n-                parts[i] = '%' + parts[i]\n+                parts[i] = splitchar + parts[i]\n         else:\n-            parts[i] = '%' + parts[i]\n-    return ''.join(parts)\n+            parts[i] = splitchar + parts[i]\n+    return base.join(parts)\n \n \n def requote_uri(uri):\n"}
{"instance_id": "pydata__xarray-5233", "model_name_or_path": "gold", "model_patch": "diff --git a/xarray/__init__.py b/xarray/__init__.py\n--- a/xarray/__init__.py\n+++ b/xarray/__init__.py\n@@ -9,7 +9,7 @@\n )\n from .backends.rasterio_ import open_rasterio\n from .backends.zarr import open_zarr\n-from .coding.cftime_offsets import cftime_range\n+from .coding.cftime_offsets import cftime_range, date_range, date_range_like\n from .coding.cftimeindex import CFTimeIndex\n from .coding.frequencies import infer_freq\n from .conventions import SerializationWarning, decode_cf\n@@ -65,6 +65,8 @@\n     \"combine_by_coords\",\n     \"combine_nested\",\n     \"concat\",\n+    \"date_range\",\n+    \"date_range_like\",\n     \"decode_cf\",\n     \"dot\",\n     \"cov\",\ndiff --git a/xarray/coding/calendar_ops.py b/xarray/coding/calendar_ops.py\nnew file mode 100644\n--- /dev/null\n+++ b/xarray/coding/calendar_ops.py\n@@ -0,0 +1,341 @@\n+import numpy as np\n+import pandas as pd\n+\n+from ..core.common import _contains_datetime_like_objects, is_np_datetime_like\n+from .cftime_offsets import date_range_like, get_date_type\n+from .cftimeindex import CFTimeIndex\n+from .times import _should_cftime_be_used, convert_times\n+\n+try:\n+    import cftime\n+except ImportError:\n+    cftime = None\n+\n+\n+_CALENDARS_WITHOUT_YEAR_ZERO = [\n+    \"gregorian\",\n+    \"proleptic_gregorian\",\n+    \"julian\",\n+    \"standard\",\n+]\n+\n+\n+def _days_in_year(year, calendar, use_cftime=True):\n+    \"\"\"Return the number of days in the input year according to the input calendar.\"\"\"\n+    date_type = get_date_type(calendar, use_cftime=use_cftime)\n+    if year == -1 and calendar in _CALENDARS_WITHOUT_YEAR_ZERO:\n+        difference = date_type(year + 2, 1, 1) - date_type(year, 1, 1)\n+    else:\n+        difference = date_type(year + 1, 1, 1) - date_type(year, 1, 1)\n+    return difference.days\n+\n+\n+def convert_calendar(\n+    obj,\n+    calendar,\n+    dim=\"time\",\n+    align_on=None,\n+    missing=None,\n+    use_cftime=None,\n+):\n+    \"\"\"Transform a time-indexed Dataset or DataArray to one that uses another calendar.\n+\n+    This function only converts the individual timestamps; it does not modify any\n+    data except in dropping invalid/surplus dates, or inserting values for missing dates.\n+\n+    If the source and target calendars are both from a standard type, only the\n+    type of the time array is modified. When converting to a calendar with a\n+    leap year from to a calendar without a leap year, the 29th of February will\n+    be removed from the array. In the other direction the 29th of February will\n+    be missing in the output, unless `missing` is specified, in which case that\n+    value is inserted. For conversions involving the `360_day` calendar, see Notes.\n+\n+    This method is safe to use with sub-daily data as it doesn't touch the time\n+    part of the timestamps.\n+\n+    Parameters\n+    ----------\n+    obj : DataArray or Dataset\n+      Input DataArray or Dataset with a time coordinate of a valid dtype\n+      (:py:class:`numpy.datetime64`  or :py:class:`cftime.datetime`).\n+    calendar : str\n+      The target calendar name.\n+    dim : str\n+      Name of the time coordinate in the input DataArray or Dataset.\n+    align_on : {None, 'date', 'year'}\n+      Must be specified when either the source or target is a `\"360_day\"`\n+      calendar; ignored otherwise. See Notes.\n+    missing : any, optional\n+      By default, i.e. if the value is None, this method will simply attempt\n+      to convert the dates in the source calendar to the same dates in the\n+      target calendar, and drop any of those that are not possible to\n+      represent.  If a value is provided, a new time coordinate will be\n+      created in the target calendar with the same frequency as the original\n+      time coordinate; for any dates that are not present in the source, the\n+      data will be filled with this value.  Note that using this mode requires\n+      that the source data have an inferable frequency; for more information\n+      see :py:func:`xarray.infer_freq`.  For certain frequency, source, and\n+      target calendar combinations, this could result in many missing values, see notes.\n+    use_cftime : bool, optional\n+      Whether to use cftime objects in the output, only used if `calendar` is\n+      one of {\"proleptic_gregorian\", \"gregorian\" or \"standard\"}.\n+      If True, the new time axis uses cftime objects.\n+      If None (default), it uses :py:class:`numpy.datetime64` values if the date\n+          range permits it, and :py:class:`cftime.datetime` objects if not.\n+      If False, it uses :py:class:`numpy.datetime64`  or fails.\n+\n+    Returns\n+    -------\n+      Copy of source with the time coordinate converted to the target calendar.\n+      If `missing` was None (default), invalid dates in the new calendar are\n+      dropped, but missing dates are not inserted.\n+      If `missing` was given, the new data is reindexed to have a time axis\n+      with the same frequency as the source, but in the new calendar; any\n+      missing datapoints are filled with `missing`.\n+\n+    Notes\n+    -----\n+    Passing a value to `missing` is only usable if the source's time coordinate as an\n+    inferrable frequencies (see :py:func:`~xarray.infer_freq`) and is only appropriate\n+    if the target coordinate, generated from this frequency, has dates equivalent to the\n+    source. It is usually **not** appropriate to use this mode with:\n+\n+    - Period-end frequencies: 'A', 'Y', 'Q' or 'M', in opposition to 'AS' 'YS', 'QS' and 'MS'\n+    - Sub-monthly frequencies that do not divide a day evenly: 'W', 'nD' where `n != 1`\n+      or 'mH' where 24 % m != 0).\n+\n+    If one of the source or target calendars is `\"360_day\"`, `align_on` must\n+    be specified and two options are offered.\n+\n+    \"year\"\n+      The dates are translated according to their relative position in the year,\n+      ignoring their original month and day information, meaning that the\n+      missing/surplus days are added/removed at regular intervals.\n+\n+      From a `360_day` to a standard calendar, the output will be missing the\n+      following dates (day of year in parentheses):\n+        To a leap year:\n+          January 31st (31), March 31st (91), June 1st (153), July 31st (213),\n+          September 31st (275) and November 30th (335).\n+        To a non-leap year:\n+          February 6th (36), April 19th (109), July 2nd (183),\n+          September 12th (255), November 25th (329).\n+\n+      From a standard calendar to a `\"360_day\"`, the following dates in the\n+      source array will be dropped:\n+        From a leap year:\n+          January 31st (31), April 1st (92), June 1st (153), August 1st (214),\n+          September 31st (275), December 1st (336)\n+        From a non-leap year:\n+          February 6th (37), April 20th (110), July 2nd (183),\n+          September 13th (256), November 25th (329)\n+\n+      This option is best used on daily and subdaily data.\n+\n+    \"date\"\n+      The month/day information is conserved and invalid dates are dropped\n+      from the output. This means that when converting from a `\"360_day\"` to a\n+      standard calendar, all 31sts (Jan, March, May, July, August, October and\n+      December) will be missing as there is no equivalent dates in the\n+      `\"360_day\"` calendar and the 29th (on non-leap years) and 30th of February\n+      will be dropped as there are no equivalent dates in a standard calendar.\n+\n+      This option is best used with data on a frequency coarser than daily.\n+    \"\"\"\n+    from ..core.dataarray import DataArray\n+\n+    time = obj[dim]\n+    if not _contains_datetime_like_objects(time):\n+        raise ValueError(f\"Coordinate {dim} must contain datetime objects.\")\n+\n+    use_cftime = _should_cftime_be_used(time, calendar, use_cftime)\n+\n+    source_calendar = time.dt.calendar\n+    # Do nothing if request calendar is the same as the source\n+    # AND source is np XOR use_cftime\n+    if source_calendar == calendar and is_np_datetime_like(time.dtype) ^ use_cftime:\n+        return obj\n+\n+    if (time.dt.year == 0).any() and calendar in _CALENDARS_WITHOUT_YEAR_ZERO:\n+        raise ValueError(\n+            f\"Source time coordinate contains dates with year 0, which is not supported by target calendar {calendar}.\"\n+        )\n+\n+    if (source_calendar == \"360_day\" or calendar == \"360_day\") and align_on is None:\n+        raise ValueError(\n+            \"Argument `align_on` must be specified with either 'date' or \"\n+            \"'year' when converting to or from a '360_day' calendar.\"\n+        )\n+\n+    if source_calendar != \"360_day\" and calendar != \"360_day\":\n+        align_on = \"date\"\n+\n+    out = obj.copy()\n+\n+    if align_on == \"year\":\n+        # Special case for conversion involving 360_day calendar\n+        # Instead of translating dates directly, this tries to keep the position within a year similar.\n+\n+        new_doy = time.groupby(f\"{dim}.year\").map(\n+            _interpolate_day_of_year, target_calendar=calendar, use_cftime=use_cftime\n+        )\n+\n+        # Convert the source datetimes, but override the day of year with our new day of years.\n+        out[dim] = DataArray(\n+            [\n+                _convert_to_new_calendar_with_new_day_of_year(\n+                    date, newdoy, calendar, use_cftime\n+                )\n+                for date, newdoy in zip(time.variable._data.array, new_doy)\n+            ],\n+            dims=(dim,),\n+            name=dim,\n+        )\n+        # Remove duplicate timestamps, happens when reducing the number of days\n+        out = out.isel({dim: np.unique(out[dim], return_index=True)[1]})\n+    elif align_on == \"date\":\n+        new_times = convert_times(\n+            time.data,\n+            get_date_type(calendar, use_cftime=use_cftime),\n+            raise_on_invalid=False,\n+        )\n+        out[dim] = new_times\n+\n+        # Remove NaN that where put on invalid dates in target calendar\n+        out = out.where(out[dim].notnull(), drop=True)\n+\n+    if missing is not None:\n+        time_target = date_range_like(time, calendar=calendar, use_cftime=use_cftime)\n+        out = out.reindex({dim: time_target}, fill_value=missing)\n+\n+    # Copy attrs but remove `calendar` if still present.\n+    out[dim].attrs.update(time.attrs)\n+    out[dim].attrs.pop(\"calendar\", None)\n+    return out\n+\n+\n+def _interpolate_day_of_year(time, target_calendar, use_cftime):\n+    \"\"\"Returns the nearest day in the target calendar of the corresponding\n+    \"decimal year\" in the source calendar.\n+    \"\"\"\n+    year = int(time.dt.year[0])\n+    source_calendar = time.dt.calendar\n+    return np.round(\n+        _days_in_year(year, target_calendar, use_cftime)\n+        * time.dt.dayofyear\n+        / _days_in_year(year, source_calendar, use_cftime)\n+    ).astype(int)\n+\n+\n+def _convert_to_new_calendar_with_new_day_of_year(\n+    date, day_of_year, calendar, use_cftime\n+):\n+    \"\"\"Convert a datetime object to another calendar with a new day of year.\n+\n+    Redefines the day of year (and thus ignores the month and day information\n+    from the source datetime).\n+    Nanosecond information is lost as cftime.datetime doesn't support it.\n+    \"\"\"\n+    new_date = cftime.num2date(\n+        day_of_year - 1,\n+        f\"days since {date.year}-01-01\",\n+        calendar=calendar if use_cftime else \"standard\",\n+    )\n+    try:\n+        return get_date_type(calendar, use_cftime)(\n+            date.year,\n+            new_date.month,\n+            new_date.day,\n+            date.hour,\n+            date.minute,\n+            date.second,\n+            date.microsecond,\n+        )\n+    except ValueError:\n+        return np.nan\n+\n+\n+def _datetime_to_decimal_year(times, dim=\"time\", calendar=None):\n+    \"\"\"Convert a datetime DataArray to decimal years according to its calendar or the given one.\n+\n+    The decimal year of a timestamp is its year plus its sub-year component\n+    converted to the fraction of its year.\n+    Ex: '2000-03-01 12:00' is 2000.1653 in a standard calendar,\n+      2000.16301 in a \"noleap\" or 2000.16806 in a \"360_day\".\n+    \"\"\"\n+    from ..core.dataarray import DataArray\n+\n+    calendar = calendar or times.dt.calendar\n+\n+    if is_np_datetime_like(times.dtype):\n+        times = times.copy(data=convert_times(times.values, get_date_type(\"standard\")))\n+\n+    def _make_index(time):\n+        year = int(time.dt.year[0])\n+        doys = cftime.date2num(time, f\"days since {year:04d}-01-01\", calendar=calendar)\n+        return DataArray(\n+            year + doys / _days_in_year(year, calendar),\n+            dims=(dim,),\n+            coords=time.coords,\n+            name=dim,\n+        )\n+\n+    return times.groupby(f\"{dim}.year\").map(_make_index)\n+\n+\n+def interp_calendar(source, target, dim=\"time\"):\n+    \"\"\"Interpolates a DataArray or Dataset indexed by a time coordinate to\n+    another calendar based on decimal year measure.\n+\n+    Each timestamp in `source` and `target` are first converted to their decimal\n+    year equivalent then `source` is interpolated on the target coordinate.\n+    The decimal year of a timestamp is its year plus its sub-year component\n+    converted to the fraction of its year. For example \"2000-03-01 12:00\" is\n+    2000.1653 in a standard calendar or 2000.16301 in a `\"noleap\"` calendar.\n+\n+    This method should only be used when the time (HH:MM:SS) information of\n+    time coordinate is not important.\n+\n+    Parameters\n+    ----------\n+    source: DataArray or Dataset\n+      The source data to interpolate; must have a time coordinate of a valid\n+      dtype (:py:class:`numpy.datetime64` or :py:class:`cftime.datetime` objects)\n+    target: DataArray, DatetimeIndex, or CFTimeIndex\n+      The target time coordinate of a valid dtype (np.datetime64 or cftime objects)\n+    dim : str\n+      The time coordinate name.\n+\n+    Return\n+    ------\n+    DataArray or Dataset\n+      The source interpolated on the decimal years of target,\n+    \"\"\"\n+    from ..core.dataarray import DataArray\n+\n+    if isinstance(target, (pd.DatetimeIndex, CFTimeIndex)):\n+        target = DataArray(target, dims=(dim,), name=dim)\n+\n+    if not _contains_datetime_like_objects(\n+        source[dim]\n+    ) or not _contains_datetime_like_objects(target):\n+        raise ValueError(\n+            f\"Both 'source.{dim}' and 'target' must contain datetime objects.\"\n+        )\n+\n+    source_calendar = source[dim].dt.calendar\n+    target_calendar = target.dt.calendar\n+\n+    if (\n+        source[dim].time.dt.year == 0\n+    ).any() and target_calendar in _CALENDARS_WITHOUT_YEAR_ZERO:\n+        raise ValueError(\n+            f\"Source time coordinate contains dates with year 0, which is not supported by target calendar {target_calendar}.\"\n+        )\n+\n+    out = source.copy()\n+    out[dim] = _datetime_to_decimal_year(source[dim], dim=dim, calendar=source_calendar)\n+    target_idx = _datetime_to_decimal_year(target, dim=dim, calendar=target_calendar)\n+    out = out.interp(**{dim: target_idx})\n+    out[dim] = target\n+    return out\ndiff --git a/xarray/coding/cftime_offsets.py b/xarray/coding/cftime_offsets.py\n--- a/xarray/coding/cftime_offsets.py\n+++ b/xarray/coding/cftime_offsets.py\n@@ -41,15 +41,22 @@\n # OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n \n import re\n-from datetime import timedelta\n+from datetime import datetime, timedelta\n from functools import partial\n from typing import ClassVar, Optional\n \n import numpy as np\n+import pandas as pd\n \n+from ..core.common import _contains_datetime_like_objects, is_np_datetime_like\n from ..core.pdcompat import count_not_none\n from .cftimeindex import CFTimeIndex, _parse_iso8601_with_reso\n-from .times import format_cftime_datetime\n+from .times import (\n+    _is_standard_calendar,\n+    _should_cftime_be_used,\n+    convert_time_or_go_back,\n+    format_cftime_datetime,\n+)\n \n try:\n     import cftime\n@@ -57,11 +64,14 @@\n     cftime = None\n \n \n-def get_date_type(calendar):\n+def get_date_type(calendar, use_cftime=True):\n     \"\"\"Return the cftime date type for a given calendar name.\"\"\"\n     if cftime is None:\n         raise ImportError(\"cftime is required for dates with non-standard calendars\")\n     else:\n+        if _is_standard_calendar(calendar) and not use_cftime:\n+            return pd.Timestamp\n+\n         calendars = {\n             \"noleap\": cftime.DatetimeNoLeap,\n             \"360_day\": cftime.Datetime360Day,\n@@ -700,6 +710,8 @@ def to_cftime_datetime(date_str_or_date, calendar=None):\n         return date\n     elif isinstance(date_str_or_date, cftime.datetime):\n         return date_str_or_date\n+    elif isinstance(date_str_or_date, (datetime, pd.Timestamp)):\n+        return cftime.DatetimeProlepticGregorian(*date_str_or_date.timetuple())\n     else:\n         raise TypeError(\n             \"date_str_or_date must be a string or a \"\n@@ -1009,3 +1021,178 @@ def cftime_range(\n         dates = dates[:-1]\n \n     return CFTimeIndex(dates, name=name)\n+\n+\n+def date_range(\n+    start=None,\n+    end=None,\n+    periods=None,\n+    freq=\"D\",\n+    tz=None,\n+    normalize=False,\n+    name=None,\n+    closed=None,\n+    calendar=\"standard\",\n+    use_cftime=None,\n+):\n+    \"\"\"Return a fixed frequency datetime index.\n+\n+    The type (:py:class:`xarray.CFTimeIndex` or :py:class:`pandas.DatetimeIndex`)\n+    of the returned index depends on the requested calendar and on `use_cftime`.\n+\n+    Parameters\n+    ----------\n+    start : str or datetime-like, optional\n+        Left bound for generating dates.\n+    end : str or datetime-like, optional\n+        Right bound for generating dates.\n+    periods : int, optional\n+        Number of periods to generate.\n+    freq : str or None, default: \"D\"\n+        Frequency strings can have multiples, e.g. \"5H\".\n+    tz : str or tzinfo, optional\n+        Time zone name for returning localized DatetimeIndex, for example\n+        'Asia/Hong_Kong'. By default, the resulting DatetimeIndex is\n+        timezone-naive. Only valid with pandas DatetimeIndex.\n+    normalize : bool, default: False\n+        Normalize start/end dates to midnight before generating date range.\n+    name : str, default: None\n+        Name of the resulting index\n+    closed : {\"left\", \"right\"} or None, default: None\n+        Make the interval closed with respect to the given frequency to the\n+        \"left\", \"right\", or both sides (None).\n+    calendar : str, default: \"standard\"\n+        Calendar type for the datetimes.\n+    use_cftime : boolean, optional\n+        If True, always return a CFTimeIndex.\n+        If False, return a pd.DatetimeIndex if possible or raise a ValueError.\n+        If None (default), return a pd.DatetimeIndex if possible,\n+        otherwise return a CFTimeIndex. Defaults to False if `tz` is not None.\n+\n+    Returns\n+    -------\n+    CFTimeIndex or pd.DatetimeIndex\n+\n+    See also\n+    --------\n+    pandas.date_range\n+    cftime_range\n+    date_range_like\n+    \"\"\"\n+    from .times import _is_standard_calendar\n+\n+    if tz is not None:\n+        use_cftime = False\n+\n+    if _is_standard_calendar(calendar) and use_cftime is not True:\n+        try:\n+            return pd.date_range(\n+                start=start,\n+                end=end,\n+                periods=periods,\n+                freq=freq,\n+                tz=tz,\n+                normalize=normalize,\n+                name=name,\n+                closed=closed,\n+            )\n+        except pd.errors.OutOfBoundsDatetime as err:\n+            if use_cftime is False:\n+                raise ValueError(\n+                    \"Date range is invalid for pandas DatetimeIndex, try using `use_cftime=True`.\"\n+                ) from err\n+    elif use_cftime is False:\n+        raise ValueError(\n+            f\"Invalid calendar {calendar} for pandas DatetimeIndex, try using `use_cftime=True`.\"\n+        )\n+\n+    return cftime_range(\n+        start=start,\n+        end=end,\n+        periods=periods,\n+        freq=freq,\n+        normalize=normalize,\n+        name=name,\n+        closed=closed,\n+        calendar=calendar,\n+    )\n+\n+\n+def date_range_like(source, calendar, use_cftime=None):\n+    \"\"\"Generate a datetime array with the same frequency, start and end as\n+    another one, but in a different calendar.\n+\n+    Parameters\n+    ----------\n+    source : DataArray, CFTimeIndex, or pd.DatetimeIndex\n+        1D datetime array\n+    calendar : str\n+        New calendar name.\n+    use_cftime : bool, optional\n+        If True, the output uses :py:class:`cftime.datetime` objects.\n+        If None (default), :py:class:`numpy.datetime64` values are used if possible.\n+        If False, :py:class:`numpy.datetime64` values are used or an error is raised.\n+\n+    Returns\n+    -------\n+    DataArray\n+        1D datetime coordinate with the same start, end and frequency as the\n+        source, but in the new calendar. The start date is assumed to exist in\n+        the target calendar. If the end date doesn't exist, the code tries 1\n+        and 2 calendar days before. There is a special case when the source time\n+        series is daily or coarser and the end of the input range is on the\n+        last day of the month. Then the output range will also end on the last\n+        day of the month in the new calendar.\n+    \"\"\"\n+    from ..core.dataarray import DataArray\n+    from .frequencies import infer_freq\n+\n+    if not isinstance(source, (pd.DatetimeIndex, CFTimeIndex)) and (\n+        isinstance(source, DataArray)\n+        and (source.ndim != 1)\n+        or not _contains_datetime_like_objects(source)\n+    ):\n+        raise ValueError(\n+            \"'source' must be a 1D array of datetime objects for inferring its range.\"\n+        )\n+\n+    freq = infer_freq(source)\n+    if freq is None:\n+        raise ValueError(\n+            \"`date_range_like` was unable to generate a range as the source frequency was not inferrable.\"\n+        )\n+\n+    use_cftime = _should_cftime_be_used(source, calendar, use_cftime)\n+\n+    source_start = source.values.min()\n+    source_end = source.values.max()\n+    if is_np_datetime_like(source.dtype):\n+        # We want to use datetime fields (datetime64 object don't have them)\n+        source_calendar = \"standard\"\n+        source_start = pd.Timestamp(source_start)\n+        source_end = pd.Timestamp(source_end)\n+    else:\n+        if isinstance(source, CFTimeIndex):\n+            source_calendar = source.calendar\n+        else:  # DataArray\n+            source_calendar = source.dt.calendar\n+\n+    if calendar == source_calendar and is_np_datetime_like(source.dtype) ^ use_cftime:\n+        return source\n+\n+    date_type = get_date_type(calendar, use_cftime)\n+    start = convert_time_or_go_back(source_start, date_type)\n+    end = convert_time_or_go_back(source_end, date_type)\n+\n+    # For the cases where the source ends on the end of the month, we expect the same in the new calendar.\n+    if source_end.day == source_end.daysinmonth and isinstance(\n+        to_offset(freq), (YearEnd, QuarterEnd, MonthEnd, Day)\n+    ):\n+        end = end.replace(day=end.daysinmonth)\n+\n+    return date_range(\n+        start=start.isoformat(),\n+        end=end.isoformat(),\n+        freq=freq,\n+        calendar=calendar,\n+    )\ndiff --git a/xarray/coding/times.py b/xarray/coding/times.py\n--- a/xarray/coding/times.py\n+++ b/xarray/coding/times.py\n@@ -8,8 +8,9 @@\n from pandas.errors import OutOfBoundsDatetime\n \n from ..core import indexing\n-from ..core.common import contains_cftime_datetimes\n+from ..core.common import contains_cftime_datetimes, is_np_datetime_like\n from ..core.formatting import first_n_items, format_timestamp, last_item\n+from ..core.pycompat import is_duck_dask_array\n from ..core.variable import Variable\n from .variables import (\n     SerializationWarning,\n@@ -76,6 +77,26 @@ def _is_standard_calendar(calendar):\n     return calendar.lower() in _STANDARD_CALENDARS\n \n \n+def _is_numpy_compatible_time_range(times):\n+    if is_np_datetime_like(times.dtype):\n+        return True\n+    # times array contains cftime objects\n+    times = np.asarray(times)\n+    tmin = times.min()\n+    tmax = times.max()\n+    try:\n+        convert_time_or_go_back(tmin, pd.Timestamp)\n+        convert_time_or_go_back(tmax, pd.Timestamp)\n+    except pd.errors.OutOfBoundsDatetime:\n+        return False\n+    except ValueError as err:\n+        if err.args[0] == \"year 0 is out of range\":\n+            return False\n+        raise\n+    else:\n+        return True\n+\n+\n def _netcdf_to_numpy_timeunit(units):\n     units = units.lower()\n     if not units.endswith(\"s\"):\n@@ -322,10 +343,21 @@ def _infer_time_units_from_diff(unique_timedeltas):\n \n def infer_calendar_name(dates):\n     \"\"\"Given an array of datetimes, infer the CF calendar name\"\"\"\n-    if np.asarray(dates).dtype == \"datetime64[ns]\":\n+    if is_np_datetime_like(dates.dtype):\n         return \"proleptic_gregorian\"\n-    else:\n-        return np.asarray(dates).ravel()[0].calendar\n+    elif dates.dtype == np.dtype(\"O\") and dates.size > 0:\n+        # Logic copied from core.common.contains_cftime_datetimes.\n+        if cftime is not None:\n+            sample = dates.ravel()[0]\n+            if is_duck_dask_array(sample):\n+                sample = sample.compute()\n+                if isinstance(sample, np.ndarray):\n+                    sample = sample.item()\n+            if isinstance(sample, cftime.datetime):\n+                return sample.calendar\n+\n+    # Error raise if dtype is neither datetime or \"O\", if cftime is not importable, and if element of 'O' dtype is not cftime.\n+    raise ValueError(\"Array does not contain datetime objects.\")\n \n \n def infer_datetime_units(dates):\n@@ -373,9 +405,12 @@ def infer_timedelta_units(deltas):\n     return _infer_time_units_from_diff(unique_timedeltas)\n \n \n-def cftime_to_nptime(times):\n+def cftime_to_nptime(times, raise_on_invalid=True):\n     \"\"\"Given an array of cftime.datetime objects, return an array of\n-    numpy.datetime64 objects of the same size\"\"\"\n+    numpy.datetime64 objects of the same size\n+\n+    If raise_on_invalid is True (default), invalid dates trigger a ValueError.\n+    Otherwise, the invalid element is replaced by np.NaT.\"\"\"\n     times = np.asarray(times)\n     new = np.empty(times.shape, dtype=\"M8[ns]\")\n     for i, t in np.ndenumerate(times):\n@@ -388,14 +423,125 @@ def cftime_to_nptime(times):\n                 t.year, t.month, t.day, t.hour, t.minute, t.second, t.microsecond\n             )\n         except ValueError as e:\n-            raise ValueError(\n-                \"Cannot convert date {} to a date in the \"\n-                \"standard calendar.  Reason: {}.\".format(t, e)\n-            )\n+            if raise_on_invalid:\n+                raise ValueError(\n+                    \"Cannot convert date {} to a date in the \"\n+                    \"standard calendar.  Reason: {}.\".format(t, e)\n+                )\n+            else:\n+                dt = \"NaT\"\n         new[i] = np.datetime64(dt)\n     return new\n \n \n+def convert_times(times, date_type, raise_on_invalid=True):\n+    \"\"\"Given an array of datetimes, return the same dates in another cftime or numpy date type.\n+\n+    Useful to convert between calendars in numpy and cftime or between cftime calendars.\n+\n+    If raise_on_valid is True (default), invalid dates trigger a ValueError.\n+    Otherwise, the invalid element is replaced by np.NaN for cftime types and np.NaT for np.datetime64.\n+    \"\"\"\n+    if date_type in (pd.Timestamp, np.datetime64) and not is_np_datetime_like(\n+        times.dtype\n+    ):\n+        return cftime_to_nptime(times, raise_on_invalid=raise_on_invalid)\n+    if is_np_datetime_like(times.dtype):\n+        # Convert datetime64 objects to Timestamps since those have year, month, day, etc. attributes\n+        times = pd.DatetimeIndex(times)\n+    new = np.empty(times.shape, dtype=\"O\")\n+    for i, t in enumerate(times):\n+        try:\n+            dt = date_type(\n+                t.year, t.month, t.day, t.hour, t.minute, t.second, t.microsecond\n+            )\n+        except ValueError as e:\n+            if raise_on_invalid:\n+                raise ValueError(\n+                    \"Cannot convert date {} to a date in the \"\n+                    \"{} calendar.  Reason: {}.\".format(\n+                        t, date_type(2000, 1, 1).calendar, e\n+                    )\n+                )\n+            else:\n+                dt = np.NaN\n+\n+        new[i] = dt\n+    return new\n+\n+\n+def convert_time_or_go_back(date, date_type):\n+    \"\"\"Convert a single date to a new date_type (cftime.datetime or pd.Timestamp).\n+\n+    If the new date is invalid, it goes back a day and tries again. If it is still\n+    invalid, goes back a second day.\n+\n+    This is meant to convert end-of-month dates into a new calendar.\n+    \"\"\"\n+    try:\n+        return date_type(\n+            date.year,\n+            date.month,\n+            date.day,\n+            date.hour,\n+            date.minute,\n+            date.second,\n+            date.microsecond,\n+        )\n+    except OutOfBoundsDatetime:\n+        raise\n+    except ValueError:\n+        # Day is invalid, happens at the end of months, try again the day before\n+        try:\n+            return date_type(\n+                date.year,\n+                date.month,\n+                date.day - 1,\n+                date.hour,\n+                date.minute,\n+                date.second,\n+                date.microsecond,\n+            )\n+        except ValueError:\n+            # Still invalid, happens for 360_day to non-leap february. Try again 2 days before date.\n+            return date_type(\n+                date.year,\n+                date.month,\n+                date.day - 2,\n+                date.hour,\n+                date.minute,\n+                date.second,\n+                date.microsecond,\n+            )\n+\n+\n+def _should_cftime_be_used(source, target_calendar, use_cftime):\n+    \"\"\"Return whether conversion of the source to the target calendar should\n+    result in a cftime-backed array.\n+\n+    Source is a 1D datetime array, target_cal a string (calendar name) and\n+    use_cftime is a boolean or None. If use_cftime is None, this returns True\n+    if the source's range and target calendar are convertible to np.datetime64 objects.\n+    \"\"\"\n+    # Arguments Checks for target\n+    if use_cftime is not True:\n+        if _is_standard_calendar(target_calendar):\n+            if _is_numpy_compatible_time_range(source):\n+                # Conversion is possible with pandas, force False if it was None\n+                use_cftime = False\n+            elif use_cftime is False:\n+                raise ValueError(\n+                    \"Source time range is not valid for numpy datetimes. Try using `use_cftime=True`.\"\n+                )\n+        elif use_cftime is False:\n+            raise ValueError(\n+                f\"Calendar '{target_calendar}' is only valid with cftime. Try using `use_cftime=True`.\"\n+            )\n+        else:\n+            use_cftime = True\n+    return use_cftime\n+\n+\n def _cleanup_netcdf_time_units(units):\n     delta, ref_date = _unpack_netcdf_time_units(units)\n     try:\ndiff --git a/xarray/core/accessor_dt.py b/xarray/core/accessor_dt.py\n--- a/xarray/core/accessor_dt.py\n+++ b/xarray/core/accessor_dt.py\n@@ -3,6 +3,7 @@\n import numpy as np\n import pandas as pd\n \n+from ..coding.times import infer_calendar_name\n from .common import (\n     _contains_datetime_like_objects,\n     is_np_datetime_like,\n@@ -440,6 +441,15 @@ def weekofyear(self):\n         \"is_leap_year\", \"Boolean indicator if the date belongs to a leap year.\", bool\n     )\n \n+    @property\n+    def calendar(self):\n+        \"\"\"The name of the calendar of the dates.\n+\n+        Only relevant for arrays of :py:class:`cftime.datetime` objects,\n+        returns \"proleptic_gregorian\" for arrays of :py:class:`numpy.datetime64` values.\n+        \"\"\"\n+        return infer_calendar_name(self._obj.data)\n+\n \n class TimedeltaAccessor(Properties):\n     \"\"\"Access Timedelta fields for DataArrays with Timedelta-like dtypes.\ndiff --git a/xarray/core/dataarray.py b/xarray/core/dataarray.py\n--- a/xarray/core/dataarray.py\n+++ b/xarray/core/dataarray.py\n@@ -21,6 +21,8 @@\n import numpy as np\n import pandas as pd\n \n+from ..coding.calendar_ops import convert_calendar, interp_calendar\n+from ..coding.cftimeindex import CFTimeIndex\n from ..plot.plot import _PlotMethods\n from ..plot.utils import _get_units_from_attrs\n from . import (\n@@ -4656,6 +4658,160 @@ def drop_duplicates(\n         indexes = {dim: ~self.get_index(dim).duplicated(keep=keep)}\n         return self.isel(indexes)\n \n+    def convert_calendar(\n+        self,\n+        calendar: str,\n+        dim: str = \"time\",\n+        align_on: Optional[str] = None,\n+        missing: Optional[Any] = None,\n+        use_cftime: Optional[bool] = None,\n+    ) -> \"DataArray\":\n+        \"\"\"Convert the DataArray to another calendar.\n+\n+        Only converts the individual timestamps, does not modify any data except\n+        in dropping invalid/surplus dates or inserting missing dates.\n+\n+        If the source and target calendars are either no_leap, all_leap or a\n+        standard type, only the type of the time array is modified.\n+        When converting to a leap year from a non-leap year, the 29th of February\n+        is removed from the array. In the other direction the 29th of February\n+        will be missing in the output, unless `missing` is specified,\n+        in which case that value is inserted.\n+\n+        For conversions involving `360_day` calendars, see Notes.\n+\n+        This method is safe to use with sub-daily data as it doesn't touch the\n+        time part of the timestamps.\n+\n+        Parameters\n+        ---------\n+        calendar : str\n+            The target calendar name.\n+        dim : str\n+            Name of the time coordinate.\n+        align_on : {None, 'date', 'year'}\n+            Must be specified when either source or target is a `360_day` calendar,\n+           ignored otherwise. See Notes.\n+        missing : Optional[any]\n+            By default, i.e. if the value is None, this method will simply attempt\n+            to convert the dates in the source calendar to the same dates in the\n+            target calendar, and drop any of those that are not possible to\n+            represent.  If a value is provided, a new time coordinate will be\n+            created in the target calendar with the same frequency as the original\n+            time coordinate; for any dates that are not present in the source, the\n+            data will be filled with this value.  Note that using this mode requires\n+            that the source data have an inferable frequency; for more information\n+            see :py:func:`xarray.infer_freq`.  For certain frequency, source, and\n+            target calendar combinations, this could result in many missing values, see notes.\n+        use_cftime : boolean, optional\n+            Whether to use cftime objects in the output, only used if `calendar`\n+            is one of {\"proleptic_gregorian\", \"gregorian\" or \"standard\"}.\n+            If True, the new time axis uses cftime objects.\n+            If None (default), it uses :py:class:`numpy.datetime64` values if the\n+            date range permits it, and :py:class:`cftime.datetime` objects if not.\n+            If False, it uses :py:class:`numpy.datetime64`  or fails.\n+\n+        Returns\n+        -------\n+        DataArray\n+            Copy of the dataarray with the time coordinate converted to the\n+            target calendar. If 'missing' was None (default), invalid dates in\n+            the new calendar are dropped, but missing dates are not inserted.\n+            If `missing` was given, the new data is reindexed to have a time axis\n+            with the same frequency as the source, but in the new calendar; any\n+            missing datapoints are filled with `missing`.\n+\n+        Notes\n+        -----\n+        Passing a value to `missing` is only usable if the source's time coordinate as an\n+        inferrable frequencies (see :py:func:`~xarray.infer_freq`) and is only appropriate\n+        if the target coordinate, generated from this frequency, has dates equivalent to the\n+        source. It is usually **not** appropriate to use this mode with:\n+\n+        - Period-end frequencies : 'A', 'Y', 'Q' or 'M', in opposition to 'AS' 'YS', 'QS' and 'MS'\n+        - Sub-monthly frequencies that do not divide a day evenly : 'W', 'nD' where `N != 1`\n+            or 'mH' where 24 % m != 0).\n+\n+        If one of the source or target calendars is `\"360_day\"`, `align_on` must\n+        be specified and two options are offered.\n+\n+        - \"year\"\n+            The dates are translated according to their relative position in the year,\n+            ignoring their original month and day information, meaning that the\n+            missing/surplus days are added/removed at regular intervals.\n+\n+            From a `360_day` to a standard calendar, the output will be missing the\n+            following dates (day of year in parentheses):\n+\n+            To a leap year:\n+                January 31st (31), March 31st (91), June 1st (153), July 31st (213),\n+                September 31st (275) and November 30th (335).\n+            To a non-leap year:\n+                February 6th (36), April 19th (109), July 2nd (183),\n+                September 12th (255), November 25th (329).\n+\n+            From a standard calendar to a `\"360_day\"`, the following dates in the\n+            source array will be dropped:\n+\n+            From a leap year:\n+                January 31st (31), April 1st (92), June 1st (153), August 1st (214),\n+                September 31st (275), December 1st (336)\n+            From a non-leap year:\n+                February 6th (37), April 20th (110), July 2nd (183),\n+                September 13th (256), November 25th (329)\n+\n+            This option is best used on daily and subdaily data.\n+\n+        - \"date\"\n+            The month/day information is conserved and invalid dates are dropped\n+            from the output. This means that when converting from a `\"360_day\"` to a\n+            standard calendar, all 31st (Jan, March, May, July, August, October and\n+            December) will be missing as there is no equivalent dates in the\n+            `\"360_day\"` calendar and the 29th (on non-leap years) and 30th of February\n+            will be dropped as there are no equivalent dates in a standard calendar.\n+\n+            This option is best used with data on a frequency coarser than daily.\n+        \"\"\"\n+        return convert_calendar(\n+            self,\n+            calendar,\n+            dim=dim,\n+            align_on=align_on,\n+            missing=missing,\n+            use_cftime=use_cftime,\n+        )\n+\n+    def interp_calendar(\n+        self,\n+        target: Union[pd.DatetimeIndex, CFTimeIndex, \"DataArray\"],\n+        dim: str = \"time\",\n+    ) -> \"DataArray\":\n+        \"\"\"Interpolates the DataArray to another calendar based on decimal year measure.\n+\n+        Each timestamp in `source` and `target` are first converted to their decimal\n+        year equivalent then `source` is interpolated on the target coordinate.\n+        The decimal year of a timestamp is its year plus its sub-year component\n+        converted to the fraction of its year. For example \"2000-03-01 12:00\" is\n+        2000.1653 in a standard calendar or 2000.16301 in a `\"noleap\"` calendar.\n+\n+        This method should only be used when the time (HH:MM:SS) information of\n+        time coordinate is not important.\n+\n+        Parameters\n+        ----------\n+        target: DataArray or DatetimeIndex or CFTimeIndex\n+            The target time coordinate of a valid dtype\n+            (np.datetime64 or cftime objects)\n+        dim : str\n+            The time coordinate name.\n+\n+        Return\n+        ------\n+        DataArray\n+            The source interpolated on the decimal years of target,\n+        \"\"\"\n+        return interp_calendar(self, target, dim=dim)\n+\n     # this needs to be at the end, or mypy will confuse with `str`\n     # https://mypy.readthedocs.io/en/latest/common_issues.html#dealing-with-conflicting-names\n     str = utils.UncachedAccessor(StringAccessor)\ndiff --git a/xarray/core/dataset.py b/xarray/core/dataset.py\n--- a/xarray/core/dataset.py\n+++ b/xarray/core/dataset.py\n@@ -35,7 +35,8 @@\n \n import xarray as xr\n \n-from ..coding.cftimeindex import _parse_array_of_cftime_strings\n+from ..coding.calendar_ops import convert_calendar, interp_calendar\n+from ..coding.cftimeindex import CFTimeIndex, _parse_array_of_cftime_strings\n from ..plot.dataset_plot import _Dataset_PlotMethods\n from . import (\n     alignment,\n@@ -7731,3 +7732,157 @@ def _wrapper(Y, *coords_, **kwargs):\n         result.attrs = self.attrs.copy()\n \n         return result\n+\n+    def convert_calendar(\n+        self,\n+        calendar: str,\n+        dim: str = \"time\",\n+        align_on: Optional[str] = None,\n+        missing: Optional[Any] = None,\n+        use_cftime: Optional[bool] = None,\n+    ) -> \"Dataset\":\n+        \"\"\"Convert the Dataset to another calendar.\n+\n+        Only converts the individual timestamps, does not modify any data except\n+        in dropping invalid/surplus dates or inserting missing dates.\n+\n+        If the source and target calendars are either no_leap, all_leap or a\n+        standard type, only the type of the time array is modified.\n+        When converting to a leap year from a non-leap year, the 29th of February\n+        is removed from the array. In the other direction the 29th of February\n+        will be missing in the output, unless `missing` is specified,\n+        in which case that value is inserted.\n+\n+        For conversions involving `360_day` calendars, see Notes.\n+\n+        This method is safe to use with sub-daily data as it doesn't touch the\n+        time part of the timestamps.\n+\n+        Parameters\n+        ---------\n+        calendar : str\n+            The target calendar name.\n+        dim : str\n+            Name of the time coordinate.\n+        align_on : {None, 'date', 'year'}\n+            Must be specified when either source or target is a `360_day` calendar,\n+            ignored otherwise. See Notes.\n+        missing : Optional[any]\n+            By default, i.e. if the value is None, this method will simply attempt\n+            to convert the dates in the source calendar to the same dates in the\n+            target calendar, and drop any of those that are not possible to\n+            represent.  If a value is provided, a new time coordinate will be\n+            created in the target calendar with the same frequency as the original\n+            time coordinate; for any dates that are not present in the source, the\n+            data will be filled with this value.  Note that using this mode requires\n+            that the source data have an inferable frequency; for more information\n+            see :py:func:`xarray.infer_freq`.  For certain frequency, source, and\n+            target calendar combinations, this could result in many missing values, see notes.\n+        use_cftime : boolean, optional\n+            Whether to use cftime objects in the output, only used if `calendar`\n+            is one of {\"proleptic_gregorian\", \"gregorian\" or \"standard\"}.\n+            If True, the new time axis uses cftime objects.\n+            If None (default), it uses :py:class:`numpy.datetime64` values if the\n+            date range permits it, and :py:class:`cftime.datetime` objects if not.\n+            If False, it uses :py:class:`numpy.datetime64`  or fails.\n+\n+        Returns\n+        -------\n+        Dataset\n+            Copy of the dataarray with the time coordinate converted to the\n+            target calendar. If 'missing' was None (default), invalid dates in\n+            the new calendar are dropped, but missing dates are not inserted.\n+            If `missing` was given, the new data is reindexed to have a time axis\n+            with the same frequency as the source, but in the new calendar; any\n+            missing datapoints are filled with `missing`.\n+\n+        Notes\n+        -----\n+        Passing a value to `missing` is only usable if the source's time coordinate as an\n+        inferrable frequencies (see :py:func:`~xarray.infer_freq`) and is only appropriate\n+        if the target coordinate, generated from this frequency, has dates equivalent to the\n+        source. It is usually **not** appropriate to use this mode with:\n+\n+        - Period-end frequencies : 'A', 'Y', 'Q' or 'M', in opposition to 'AS' 'YS', 'QS' and 'MS'\n+        - Sub-monthly frequencies that do not divide a day evenly : 'W', 'nD' where `N != 1`\n+            or 'mH' where 24 % m != 0).\n+\n+        If one of the source or target calendars is `\"360_day\"`, `align_on` must\n+        be specified and two options are offered.\n+\n+        - \"year\"\n+            The dates are translated according to their relative position in the year,\n+            ignoring their original month and day information, meaning that the\n+            missing/surplus days are added/removed at regular intervals.\n+\n+            From a `360_day` to a standard calendar, the output will be missing the\n+            following dates (day of year in parentheses):\n+\n+            To a leap year:\n+                January 31st (31), March 31st (91), June 1st (153), July 31st (213),\n+                September 31st (275) and November 30th (335).\n+            To a non-leap year:\n+                February 6th (36), April 19th (109), July 2nd (183),\n+                September 12th (255), November 25th (329).\n+\n+            From a standard calendar to a `\"360_day\"`, the following dates in the\n+            source array will be dropped:\n+\n+            From a leap year:\n+                January 31st (31), April 1st (92), June 1st (153), August 1st (214),\n+                September 31st (275), December 1st (336)\n+            From a non-leap year:\n+                February 6th (37), April 20th (110), July 2nd (183),\n+                September 13th (256), November 25th (329)\n+\n+            This option is best used on daily and subdaily data.\n+\n+        - \"date\"\n+            The month/day information is conserved and invalid dates are dropped\n+            from the output. This means that when converting from a `\"360_day\"` to a\n+            standard calendar, all 31st (Jan, March, May, July, August, October and\n+            December) will be missing as there is no equivalent dates in the\n+            `\"360_day\"` calendar and the 29th (on non-leap years) and 30th of February\n+            will be dropped as there are no equivalent dates in a standard calendar.\n+\n+            This option is best used with data on a frequency coarser than daily.\n+        \"\"\"\n+        return convert_calendar(\n+            self,\n+            calendar,\n+            dim=dim,\n+            align_on=align_on,\n+            missing=missing,\n+            use_cftime=use_cftime,\n+        )\n+\n+    def interp_calendar(\n+        self,\n+        target: Union[pd.DatetimeIndex, CFTimeIndex, \"DataArray\"],\n+        dim: str = \"time\",\n+    ) -> \"Dataset\":\n+        \"\"\"Interpolates the Dataset to another calendar based on decimal year measure.\n+\n+        Each timestamp in `source` and `target` are first converted to their decimal\n+        year equivalent then `source` is interpolated on the target coordinate.\n+        The decimal year of a timestamp is its year plus its sub-year component\n+        converted to the fraction of its year. For example \"2000-03-01 12:00\" is\n+        2000.1653 in a standard calendar or 2000.16301 in a `\"noleap\"` calendar.\n+\n+        This method should only be used when the time (HH:MM:SS) information of\n+        time coordinate is not important.\n+\n+        Parameters\n+        ----------\n+        target: DataArray or DatetimeIndex or CFTimeIndex\n+            The target time coordinate of a valid dtype\n+            (np.datetime64 or cftime objects)\n+        dim : str\n+            The time coordinate name.\n+\n+        Return\n+        ------\n+        DataArray\n+            The source interpolated on the decimal years of target,\n+        \"\"\"\n+        return interp_calendar(self, target, dim=dim)\n"}
{"instance_id": "pydata__xarray-5580", "model_name_or_path": "gold", "model_patch": "diff --git a/xarray/core/formatting.py b/xarray/core/formatting.py\n--- a/xarray/core/formatting.py\n+++ b/xarray/core/formatting.py\n@@ -377,14 +377,12 @@ def _mapping_repr(\n ):\n     if col_width is None:\n         col_width = _calculate_col_width(mapping)\n-    if max_rows is None:\n-        max_rows = OPTIONS[\"display_max_rows\"]\n     summary = [f\"{title}:\"]\n     if mapping:\n         len_mapping = len(mapping)\n         if not _get_boolean_with_default(expand_option_name, default=True):\n             summary = [f\"{summary[0]} ({len_mapping})\"]\n-        elif len_mapping > max_rows:\n+        elif max_rows is not None and len_mapping > max_rows:\n             summary = [f\"{summary[0]} ({max_rows}/{len_mapping})\"]\n             first_rows = max_rows // 2 + max_rows % 2\n             keys = list(mapping.keys())\n@@ -418,7 +416,7 @@ def _mapping_repr(\n )\n \n \n-def coords_repr(coords, col_width=None):\n+def coords_repr(coords, col_width=None, max_rows=None):\n     if col_width is None:\n         col_width = _calculate_col_width(_get_col_items(coords))\n     return _mapping_repr(\n@@ -427,6 +425,7 @@ def coords_repr(coords, col_width=None):\n         summarizer=summarize_coord,\n         expand_option_name=\"display_expand_coords\",\n         col_width=col_width,\n+        max_rows=max_rows,\n     )\n \n \n@@ -544,21 +543,22 @@ def dataset_repr(ds):\n     summary = [\"<xarray.{}>\".format(type(ds).__name__)]\n \n     col_width = _calculate_col_width(_get_col_items(ds.variables))\n+    max_rows = OPTIONS[\"display_max_rows\"]\n \n     dims_start = pretty_print(\"Dimensions:\", col_width)\n     summary.append(\"{}({})\".format(dims_start, dim_summary(ds)))\n \n     if ds.coords:\n-        summary.append(coords_repr(ds.coords, col_width=col_width))\n+        summary.append(coords_repr(ds.coords, col_width=col_width, max_rows=max_rows))\n \n     unindexed_dims_str = unindexed_dims_repr(ds.dims, ds.coords)\n     if unindexed_dims_str:\n         summary.append(unindexed_dims_str)\n \n-    summary.append(data_vars_repr(ds.data_vars, col_width=col_width))\n+    summary.append(data_vars_repr(ds.data_vars, col_width=col_width, max_rows=max_rows))\n \n     if ds.attrs:\n-        summary.append(attrs_repr(ds.attrs))\n+        summary.append(attrs_repr(ds.attrs, max_rows=max_rows))\n \n     return \"\\n\".join(summary)\n \n"}
{"instance_id": "pydata__xarray-5731", "model_name_or_path": "gold", "model_patch": "diff --git a/xarray/core/computation.py b/xarray/core/computation.py\n--- a/xarray/core/computation.py\n+++ b/xarray/core/computation.py\n@@ -1359,7 +1359,8 @@ def _get_valid_values(da, other):\n             da = da.where(~missing_vals)\n             return da\n         else:\n-            return da\n+            # ensure consistent return dtype\n+            return da.astype(float)\n \n     da_a = da_a.map_blocks(_get_valid_values, args=[da_b])\n     da_b = da_b.map_blocks(_get_valid_values, args=[da_a])\ndiff --git a/xarray/core/parallel.py b/xarray/core/parallel.py\n--- a/xarray/core/parallel.py\n+++ b/xarray/core/parallel.py\n@@ -23,6 +23,7 @@\n from .alignment import align\n from .dataarray import DataArray\n from .dataset import Dataset\n+from .pycompat import is_dask_collection\n \n try:\n     import dask\n@@ -328,13 +329,13 @@ def _wrapper(\n         raise TypeError(\"kwargs must be a mapping (for example, a dict)\")\n \n     for value in kwargs.values():\n-        if dask.is_dask_collection(value):\n+        if is_dask_collection(value):\n             raise TypeError(\n                 \"Cannot pass dask collections in kwargs yet. Please compute or \"\n                 \"load values before passing to map_blocks.\"\n             )\n \n-    if not dask.is_dask_collection(obj):\n+    if not is_dask_collection(obj):\n         return func(obj, *args, **kwargs)\n \n     all_args = [obj] + list(args)\ndiff --git a/xarray/core/pycompat.py b/xarray/core/pycompat.py\n--- a/xarray/core/pycompat.py\n+++ b/xarray/core/pycompat.py\n@@ -43,15 +43,19 @@ def __init__(self, mod):\n         self.available = duck_array_module is not None\n \n \n-def is_duck_dask_array(x):\n+def is_dask_collection(x):\n     if DuckArrayModule(\"dask\").available:\n         from dask.base import is_dask_collection\n \n-        return is_duck_array(x) and is_dask_collection(x)\n+        return is_dask_collection(x)\n     else:\n         return False\n \n \n+def is_duck_dask_array(x):\n+    return is_duck_array(x) and is_dask_collection(x)\n+\n+\n dsk = DuckArrayModule(\"dask\")\n dask_version = dsk.version\n dask_array_type = dsk.type\n"}
{"instance_id": "pydata__xarray-6135", "model_name_or_path": "gold", "model_patch": "diff --git a/xarray/coding/cftime_offsets.py b/xarray/coding/cftime_offsets.py\n--- a/xarray/coding/cftime_offsets.py\n+++ b/xarray/coding/cftime_offsets.py\n@@ -39,11 +39,12 @@\n # THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n # (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n # OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n+from __future__ import annotations\n \n import re\n from datetime import datetime, timedelta\n from functools import partial\n-from typing import ClassVar, Optional\n+from typing import ClassVar\n \n import numpy as np\n import pandas as pd\n@@ -87,10 +88,10 @@ def get_date_type(calendar, use_cftime=True):\n \n \n class BaseCFTimeOffset:\n-    _freq: ClassVar[Optional[str]] = None\n-    _day_option: ClassVar[Optional[str]] = None\n+    _freq: ClassVar[str | None] = None\n+    _day_option: ClassVar[str | None] = None\n \n-    def __init__(self, n=1):\n+    def __init__(self, n: int = 1):\n         if not isinstance(n, int):\n             raise TypeError(\n                 \"The provided multiple 'n' must be an integer. \"\n@@ -122,6 +123,8 @@ def __sub__(self, other):\n             return NotImplemented\n \n     def __mul__(self, other):\n+        if not isinstance(other, int):\n+            return NotImplemented\n         return type(self)(n=other * self.n)\n \n     def __neg__(self):\n@@ -171,6 +174,40 @@ def _get_offset_day(self, other):\n         return _get_day_of_month(other, self._day_option)\n \n \n+class Tick(BaseCFTimeOffset):\n+    # analogous https://github.com/pandas-dev/pandas/blob/ccb25ab1d24c4fb9691270706a59c8d319750870/pandas/_libs/tslibs/offsets.pyx#L806\n+\n+    def _next_higher_resolution(self):\n+        self_type = type(self)\n+        if self_type not in [Day, Hour, Minute, Second, Millisecond]:\n+            raise ValueError(\"Could not convert to integer offset at any resolution\")\n+        if type(self) is Day:\n+            return Hour(self.n * 24)\n+        if type(self) is Hour:\n+            return Minute(self.n * 60)\n+        if type(self) is Minute:\n+            return Second(self.n * 60)\n+        if type(self) is Second:\n+            return Millisecond(self.n * 1000)\n+        if type(self) is Millisecond:\n+            return Microsecond(self.n * 1000)\n+\n+    def __mul__(self, other):\n+        if not isinstance(other, (int, float)):\n+            return NotImplemented\n+        if isinstance(other, float):\n+            n = other * self.n\n+            # If the new `n` is an integer, we can represent it using the\n+            #  same BaseCFTimeOffset subclass as self, otherwise we need to move up\n+            #  to a higher-resolution subclass\n+            if np.isclose(n % 1, 0):\n+                return type(self)(int(n))\n+\n+            new_self = self._next_higher_resolution()\n+            return new_self * other\n+        return type(self)(n=other * self.n)\n+\n+\n def _get_day_of_month(other, day_option):\n     \"\"\"Find the day in `other`'s month that satisfies a BaseCFTimeOffset's\n     onOffset policy, as described by the `day_option` argument.\n@@ -396,6 +433,8 @@ def __sub__(self, other):\n             return NotImplemented\n \n     def __mul__(self, other):\n+        if isinstance(other, float):\n+            return NotImplemented\n         return type(self)(n=other * self.n, month=self.month)\n \n     def rule_code(self):\n@@ -482,6 +521,8 @@ def __sub__(self, other):\n             return NotImplemented\n \n     def __mul__(self, other):\n+        if isinstance(other, float):\n+            return NotImplemented\n         return type(self)(n=other * self.n, month=self.month)\n \n     def rule_code(self):\n@@ -541,7 +582,7 @@ def rollback(self, date):\n             return date - YearEnd(month=self.month)\n \n \n-class Day(BaseCFTimeOffset):\n+class Day(Tick):\n     _freq = \"D\"\n \n     def as_timedelta(self):\n@@ -551,7 +592,7 @@ def __apply__(self, other):\n         return other + self.as_timedelta()\n \n \n-class Hour(BaseCFTimeOffset):\n+class Hour(Tick):\n     _freq = \"H\"\n \n     def as_timedelta(self):\n@@ -561,7 +602,7 @@ def __apply__(self, other):\n         return other + self.as_timedelta()\n \n \n-class Minute(BaseCFTimeOffset):\n+class Minute(Tick):\n     _freq = \"T\"\n \n     def as_timedelta(self):\n@@ -571,7 +612,7 @@ def __apply__(self, other):\n         return other + self.as_timedelta()\n \n \n-class Second(BaseCFTimeOffset):\n+class Second(Tick):\n     _freq = \"S\"\n \n     def as_timedelta(self):\n@@ -581,7 +622,7 @@ def __apply__(self, other):\n         return other + self.as_timedelta()\n \n \n-class Millisecond(BaseCFTimeOffset):\n+class Millisecond(Tick):\n     _freq = \"L\"\n \n     def as_timedelta(self):\n@@ -591,7 +632,7 @@ def __apply__(self, other):\n         return other + self.as_timedelta()\n \n \n-class Microsecond(BaseCFTimeOffset):\n+class Microsecond(Tick):\n     _freq = \"U\"\n \n     def as_timedelta(self):\ndiff --git a/xarray/coding/cftimeindex.py b/xarray/coding/cftimeindex.py\n--- a/xarray/coding/cftimeindex.py\n+++ b/xarray/coding/cftimeindex.py\n@@ -38,11 +38,11 @@\n # THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n # (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n # OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n+from __future__ import annotations\n \n import re\n import warnings\n from datetime import timedelta\n-from typing import Tuple, Type\n \n import numpy as np\n import pandas as pd\n@@ -66,7 +66,7 @@\n REPR_ELLIPSIS_SHOW_ITEMS_FRONT_END = 10\n \n \n-OUT_OF_BOUNDS_TIMEDELTA_ERRORS: Tuple[Type[Exception], ...]\n+OUT_OF_BOUNDS_TIMEDELTA_ERRORS: tuple[type[Exception], ...]\n try:\n     OUT_OF_BOUNDS_TIMEDELTA_ERRORS = (pd.errors.OutOfBoundsTimedelta, OverflowError)\n except AttributeError:\n@@ -511,7 +511,7 @@ def contains(self, key):\n         \"\"\"Needed for .loc based partial-string indexing\"\"\"\n         return self.__contains__(key)\n \n-    def shift(self, n, freq):\n+    def shift(self, n: int | float, freq: str | timedelta):\n         \"\"\"Shift the CFTimeIndex a multiple of the given frequency.\n \n         See the documentation for :py:func:`~xarray.cftime_range` for a\n@@ -519,7 +519,7 @@ def shift(self, n, freq):\n \n         Parameters\n         ----------\n-        n : int\n+        n : int, float if freq of days or below\n             Periods to shift by\n         freq : str or datetime.timedelta\n             A frequency string or datetime.timedelta object to shift by\n@@ -541,14 +541,15 @@ def shift(self, n, freq):\n         >>> index.shift(1, \"M\")\n         CFTimeIndex([2000-02-29 00:00:00],\n                     dtype='object', length=1, calendar='standard', freq=None)\n+        >>> index.shift(1.5, \"D\")\n+        CFTimeIndex([2000-02-01 12:00:00],\n+                    dtype='object', length=1, calendar='standard', freq=None)\n         \"\"\"\n-        from .cftime_offsets import to_offset\n-\n-        if not isinstance(n, int):\n-            raise TypeError(f\"'n' must be an int, got {n}.\")\n         if isinstance(freq, timedelta):\n             return self + n * freq\n         elif isinstance(freq, str):\n+            from .cftime_offsets import to_offset\n+\n             return self + n * to_offset(freq)\n         else:\n             raise TypeError(\n"}
{"instance_id": "pydata__xarray-6601", "model_name_or_path": "gold", "model_patch": "diff --git a/xarray/core/computation.py b/xarray/core/computation.py\n--- a/xarray/core/computation.py\n+++ b/xarray/core/computation.py\n@@ -1909,7 +1909,7 @@ def polyval(\n \n     # using Horner's method\n     # https://en.wikipedia.org/wiki/Horner%27s_method\n-    res = coeffs.isel({degree_dim: max_deg}, drop=True) + zeros_like(coord)\n+    res = zeros_like(coord) + coeffs.isel({degree_dim: max_deg}, drop=True)\n     for deg in range(max_deg - 1, -1, -1):\n         res *= coord\n         res += coeffs.isel({degree_dim: deg}, drop=True)\n"}
{"instance_id": "pydata__xarray-7105", "model_name_or_path": "gold", "model_patch": "diff --git a/xarray/core/alignment.py b/xarray/core/alignment.py\n--- a/xarray/core/alignment.py\n+++ b/xarray/core/alignment.py\n@@ -24,8 +24,15 @@\n \n from . import dtypes\n from .common import DataWithCoords\n-from .indexes import Index, Indexes, PandasIndex, PandasMultiIndex, indexes_all_equal\n-from .utils import is_dict_like, is_full_slice, safe_cast_to_index\n+from .indexes import (\n+    Index,\n+    Indexes,\n+    PandasIndex,\n+    PandasMultiIndex,\n+    indexes_all_equal,\n+    safe_cast_to_index,\n+)\n+from .utils import is_dict_like, is_full_slice\n from .variable import Variable, as_compatible_data, calculate_dimensions\n \n if TYPE_CHECKING:\ndiff --git a/xarray/core/dataarray.py b/xarray/core/dataarray.py\n--- a/xarray/core/dataarray.py\n+++ b/xarray/core/dataarray.py\n@@ -757,6 +757,9 @@ def as_numpy(self: T_DataArray) -> T_DataArray:\n     def _in_memory(self) -> bool:\n         return self.variable._in_memory\n \n+    def _to_index(self) -> pd.Index:\n+        return self.variable._to_index()\n+\n     def to_index(self) -> pd.Index:\n         \"\"\"Convert this variable to a pandas.Index. Only possible for 1D\n         arrays.\ndiff --git a/xarray/core/groupby.py b/xarray/core/groupby.py\n--- a/xarray/core/groupby.py\n+++ b/xarray/core/groupby.py\n@@ -27,20 +27,17 @@\n from .common import ImplementsArrayReduce, ImplementsDatasetReduce\n from .concat import concat\n from .formatting import format_array_flat\n-from .indexes import create_default_index_implicit, filter_indexes_from_coords\n+from .indexes import (\n+    create_default_index_implicit,\n+    filter_indexes_from_coords,\n+    safe_cast_to_index,\n+)\n from .npcompat import QUANTILE_METHODS, ArrayLike\n from .ops import IncludeCumMethods\n from .options import _get_keep_attrs\n from .pycompat import integer_types\n from .types import Dims, T_Xarray\n-from .utils import (\n-    either_dict_or_kwargs,\n-    hashable,\n-    is_scalar,\n-    maybe_wrap_array,\n-    peek_at,\n-    safe_cast_to_index,\n-)\n+from .utils import either_dict_or_kwargs, hashable, is_scalar, maybe_wrap_array, peek_at\n from .variable import IndexVariable, Variable\n \n if TYPE_CHECKING:\ndiff --git a/xarray/core/indexes.py b/xarray/core/indexes.py\n--- a/xarray/core/indexes.py\n+++ b/xarray/core/indexes.py\n@@ -133,6 +133,48 @@ def __getitem__(self, indexer: Any):\n         raise NotImplementedError()\n \n \n+def _maybe_cast_to_cftimeindex(index: pd.Index) -> pd.Index:\n+    from ..coding.cftimeindex import CFTimeIndex\n+\n+    if len(index) > 0 and index.dtype == \"O\":\n+        try:\n+            return CFTimeIndex(index)\n+        except (ImportError, TypeError):\n+            return index\n+    else:\n+        return index\n+\n+\n+def safe_cast_to_index(array: Any) -> pd.Index:\n+    \"\"\"Given an array, safely cast it to a pandas.Index.\n+\n+    If it is already a pandas.Index, return it unchanged.\n+\n+    Unlike pandas.Index, if the array has dtype=object or dtype=timedelta64,\n+    this function will not attempt to do automatic type conversion but will\n+    always return an index with dtype=object.\n+    \"\"\"\n+    from .dataarray import DataArray\n+    from .variable import Variable\n+\n+    if isinstance(array, pd.Index):\n+        index = array\n+    elif isinstance(array, (DataArray, Variable)):\n+        # returns the original multi-index for pandas.MultiIndex level coordinates\n+        index = array._to_index()\n+    elif isinstance(array, Index):\n+        index = array.to_pandas_index()\n+    elif isinstance(array, PandasIndexingAdapter):\n+        index = array.array\n+    else:\n+        kwargs = {}\n+        if hasattr(array, \"dtype\") and array.dtype.kind == \"O\":\n+            kwargs[\"dtype\"] = object\n+        index = pd.Index(np.asarray(array), **kwargs)\n+\n+    return _maybe_cast_to_cftimeindex(index)\n+\n+\n def _sanitize_slice_element(x):\n     from .dataarray import DataArray\n     from .variable import Variable\n@@ -236,7 +278,7 @@ def __init__(self, array: Any, dim: Hashable, coord_dtype: Any = None):\n         # make a shallow copy: cheap and because the index name may be updated\n         # here or in other constructors (cannot use pd.Index.rename as this\n         # constructor is also called from PandasMultiIndex)\n-        index = utils.safe_cast_to_index(array).copy()\n+        index = safe_cast_to_index(array).copy()\n \n         if index.name is None:\n             index.name = dim\n@@ -637,7 +679,7 @@ def stack(\n         \"\"\"\n         _check_dim_compat(variables, all_dims=\"different\")\n \n-        level_indexes = [utils.safe_cast_to_index(var) for var in variables.values()]\n+        level_indexes = [safe_cast_to_index(var) for var in variables.values()]\n         for name, idx in zip(variables, level_indexes):\n             if isinstance(idx, pd.MultiIndex):\n                 raise ValueError(\ndiff --git a/xarray/core/indexing.py b/xarray/core/indexing.py\n--- a/xarray/core/indexing.py\n+++ b/xarray/core/indexing.py\n@@ -24,7 +24,6 @@\n     NDArrayMixin,\n     either_dict_or_kwargs,\n     get_valid_numpy_dtype,\n-    safe_cast_to_index,\n     to_0d_array,\n )\n \n@@ -1415,6 +1414,8 @@ class PandasIndexingAdapter(ExplicitlyIndexedNDArrayMixin):\n     __slots__ = (\"array\", \"_dtype\")\n \n     def __init__(self, array: pd.Index, dtype: DTypeLike = None):\n+        from .indexes import safe_cast_to_index\n+\n         self.array = safe_cast_to_index(array)\n \n         if dtype is None:\ndiff --git a/xarray/core/utils.py b/xarray/core/utils.py\n--- a/xarray/core/utils.py\n+++ b/xarray/core/utils.py\n@@ -62,18 +62,6 @@ def wrapper(*args, **kwargs):\n     return wrapper\n \n \n-def _maybe_cast_to_cftimeindex(index: pd.Index) -> pd.Index:\n-    from ..coding.cftimeindex import CFTimeIndex\n-\n-    if len(index) > 0 and index.dtype == \"O\":\n-        try:\n-            return CFTimeIndex(index)\n-        except (ImportError, TypeError):\n-            return index\n-    else:\n-        return index\n-\n-\n def get_valid_numpy_dtype(array: np.ndarray | pd.Index):\n     \"\"\"Return a numpy compatible dtype from either\n     a numpy array or a pandas.Index.\n@@ -112,34 +100,6 @@ def maybe_coerce_to_str(index, original_coords):\n     return index\n \n \n-def safe_cast_to_index(array: Any) -> pd.Index:\n-    \"\"\"Given an array, safely cast it to a pandas.Index.\n-\n-    If it is already a pandas.Index, return it unchanged.\n-\n-    Unlike pandas.Index, if the array has dtype=object or dtype=timedelta64,\n-    this function will not attempt to do automatic type conversion but will\n-    always return an index with dtype=object.\n-    \"\"\"\n-    if isinstance(array, pd.Index):\n-        index = array\n-    elif hasattr(array, \"to_index\"):\n-        # xarray Variable\n-        index = array.to_index()\n-    elif hasattr(array, \"to_pandas_index\"):\n-        # xarray Index\n-        index = array.to_pandas_index()\n-    elif hasattr(array, \"array\") and isinstance(array.array, pd.Index):\n-        # xarray PandasIndexingAdapter\n-        index = array.array\n-    else:\n-        kwargs = {}\n-        if hasattr(array, \"dtype\") and array.dtype.kind == \"O\":\n-            kwargs[\"dtype\"] = object\n-        index = pd.Index(np.asarray(array), **kwargs)\n-    return _maybe_cast_to_cftimeindex(index)\n-\n-\n def maybe_wrap_array(original, new_array):\n     \"\"\"Wrap a transformed array with __array_wrap__ if it can be done safely.\n \ndiff --git a/xarray/core/variable.py b/xarray/core/variable.py\n--- a/xarray/core/variable.py\n+++ b/xarray/core/variable.py\n@@ -577,6 +577,9 @@ def to_index_variable(self) -> IndexVariable:\n \n     to_coord = utils.alias(to_index_variable, \"to_coord\")\n \n+    def _to_index(self) -> pd.Index:\n+        return self.to_index_variable()._to_index()\n+\n     def to_index(self) -> pd.Index:\n         \"\"\"Convert this variable to a pandas.Index\"\"\"\n         return self.to_index_variable().to_index()\n@@ -2943,7 +2946,7 @@ def equals(self, other, equiv=None):\n             return False\n \n     def _data_equals(self, other):\n-        return self.to_index().equals(other.to_index())\n+        return self._to_index().equals(other._to_index())\n \n     def to_index_variable(self) -> IndexVariable:\n         \"\"\"Return this variable as an xarray.IndexVariable\"\"\"\n@@ -2951,10 +2954,11 @@ def to_index_variable(self) -> IndexVariable:\n \n     to_coord = utils.alias(to_index_variable, \"to_coord\")\n \n-    def to_index(self) -> pd.Index:\n-        \"\"\"Convert this variable to a pandas.Index\"\"\"\n+    def _to_index(self) -> pd.Index:\n         # n.b. creating a new pandas.Index from an old pandas.Index is\n-        # basically free as pandas.Index objects are immutable\n+        # basically free as pandas.Index objects are immutable.\n+        # n.b.2. this method returns the multi-index instance for\n+        # a pandas multi-index level variable.\n         assert self.ndim == 1\n         index = self._data.array\n         if isinstance(index, pd.MultiIndex):\n@@ -2969,6 +2973,16 @@ def to_index(self) -> pd.Index:\n             index = index.set_names(self.name)\n         return index\n \n+    def to_index(self) -> pd.Index:\n+        \"\"\"Convert this variable to a pandas.Index\"\"\"\n+        index = self._to_index()\n+        level = getattr(self._data, \"level\", None)\n+        if level is not None:\n+            # return multi-index level converted to a single index\n+            return index.get_level_values(level)\n+        else:\n+            return index\n+\n     @property\n     def level_names(self) -> list[str] | None:\n         \"\"\"Return MultiIndex level names or None if this IndexVariable has no\n"}
{"instance_id": "pydata__xarray-7444", "model_name_or_path": "gold", "model_patch": "diff --git a/xarray/core/common.py b/xarray/core/common.py\n--- a/xarray/core/common.py\n+++ b/xarray/core/common.py\n@@ -13,8 +13,14 @@\n from xarray.core import dtypes, duck_array_ops, formatting, formatting_html, ops\n from xarray.core.indexing import BasicIndexer, ExplicitlyIndexed\n from xarray.core.options import OPTIONS, _get_keep_attrs\n+from xarray.core.pdcompat import _convert_base_to_offset\n from xarray.core.pycompat import is_duck_dask_array\n-from xarray.core.utils import Frozen, either_dict_or_kwargs, is_scalar\n+from xarray.core.utils import (\n+    Frozen,\n+    either_dict_or_kwargs,\n+    emit_user_level_warning,\n+    is_scalar,\n+)\n \n try:\n     import cftime\n@@ -845,6 +851,12 @@ def _resample(\n             For frequencies that evenly subdivide 1 day, the \"origin\" of the\n             aggregated intervals. For example, for \"24H\" frequency, base could\n             range from 0 through 23.\n+\n+            .. deprecated:: 2023.03.0\n+                Following pandas, the ``base`` parameter is deprecated in favor\n+                of the ``origin`` and ``offset`` parameters, and will be removed\n+                in a future version of xarray.\n+\n         origin : {'epoch', 'start', 'start_day', 'end', 'end_day'}, pd.Timestamp, datetime.datetime, np.datetime64, or cftime.datetime, default 'start_day'\n             The datetime on which to adjust the grouping. The timezone of origin\n             must match the timezone of the index.\n@@ -860,6 +872,12 @@ def _resample(\n         loffset : timedelta or str, optional\n             Offset used to adjust the resampled time labels. Some pandas date\n             offset strings are supported.\n+\n+            .. deprecated:: 2023.03.0\n+                Following pandas, the ``loffset`` parameter is deprecated in favor\n+                of using time offset arithmetic, and will be removed in a future\n+                version of xarray.\n+\n         restore_coord_dims : bool, optional\n             If True, also restore the dimension order of multi-dimensional\n             coordinates.\n@@ -930,8 +948,8 @@ def _resample(\n         \"\"\"\n         # TODO support non-string indexer after removing the old API.\n \n-        from xarray.coding.cftimeindex import CFTimeIndex\n         from xarray.core.dataarray import DataArray\n+        from xarray.core.groupby import TimeResampleGrouper\n         from xarray.core.resample import RESAMPLE_DIM\n \n         if keep_attrs is not None:\n@@ -961,28 +979,36 @@ def _resample(\n         dim_name: Hashable = dim\n         dim_coord = self[dim]\n \n-        if isinstance(self._indexes[dim_name].to_pandas_index(), CFTimeIndex):\n-            from xarray.core.resample_cftime import CFTimeGrouper\n-\n-            grouper = CFTimeGrouper(\n-                freq=freq,\n-                closed=closed,\n-                label=label,\n-                base=base,\n-                loffset=loffset,\n-                origin=origin,\n-                offset=offset,\n+        if loffset is not None:\n+            emit_user_level_warning(\n+                \"Following pandas, the `loffset` parameter to resample will be deprecated \"\n+                \"in a future version of xarray.  Switch to using time offset arithmetic.\",\n+                FutureWarning,\n             )\n-        else:\n-            grouper = pd.Grouper(\n-                freq=freq,\n-                closed=closed,\n-                label=label,\n-                base=base,\n-                offset=offset,\n-                origin=origin,\n-                loffset=loffset,\n+\n+        if base is not None:\n+            emit_user_level_warning(\n+                \"Following pandas, the `base` parameter to resample will be deprecated in \"\n+                \"a future version of xarray.  Switch to using `origin` or `offset` instead.\",\n+                FutureWarning,\n             )\n+\n+        if base is not None and offset is not None:\n+            raise ValueError(\"base and offset cannot be present at the same time\")\n+\n+        if base is not None:\n+            index = self._indexes[dim_name].to_pandas_index()\n+            offset = _convert_base_to_offset(base, freq, index)\n+\n+        grouper = TimeResampleGrouper(\n+            freq=freq,\n+            closed=closed,\n+            label=label,\n+            origin=origin,\n+            offset=offset,\n+            loffset=loffset,\n+        )\n+\n         group = DataArray(\n             dim_coord, coords=dim_coord.coords, dims=dim_coord.dims, name=RESAMPLE_DIM\n         )\ndiff --git a/xarray/core/groupby.py b/xarray/core/groupby.py\n--- a/xarray/core/groupby.py\n+++ b/xarray/core/groupby.py\n@@ -40,6 +40,7 @@\n \n     from xarray.core.dataarray import DataArray\n     from xarray.core.dataset import Dataset\n+    from xarray.core.types import DatetimeLike, SideOptions\n     from xarray.core.utils import Frozen\n \n     GroupKey = Any\n@@ -245,7 +246,10 @@ def _unique_and_monotonic(group: T_Group) -> bool:\n     return index.is_unique and index.is_monotonic_increasing\n \n \n-def _apply_loffset(grouper, result):\n+def _apply_loffset(\n+    loffset: str | pd.DateOffset | datetime.timedelta | pd.Timedelta,\n+    result: pd.Series | pd.DataFrame,\n+):\n     \"\"\"\n     (copied from pandas)\n     if loffset is set, offset the result index\n@@ -258,17 +262,25 @@ def _apply_loffset(grouper, result):\n     result : Series or DataFrame\n         the result of resample\n     \"\"\"\n+    # pd.Timedelta is a subclass of datetime.timedelta so we do not need to\n+    # include it in instance checks.\n+    if not isinstance(loffset, (str, pd.DateOffset, datetime.timedelta)):\n+        raise ValueError(\n+            f\"`loffset` must be a str, pd.DateOffset, datetime.timedelta, or pandas.Timedelta object. \"\n+            f\"Got {loffset}.\"\n+        )\n+\n+    if isinstance(loffset, str):\n+        loffset = pd.tseries.frequencies.to_offset(loffset)\n \n     needs_offset = (\n-        isinstance(grouper.loffset, (pd.DateOffset, datetime.timedelta))\n+        isinstance(loffset, (pd.DateOffset, datetime.timedelta))\n         and isinstance(result.index, pd.DatetimeIndex)\n         and len(result.index) > 0\n     )\n \n     if needs_offset:\n-        result.index = result.index + grouper.loffset\n-\n-    grouper.loffset = None\n+        result.index = result.index + loffset\n \n \n class GroupBy(Generic[T_Xarray]):\n@@ -530,14 +542,7 @@ def __repr__(self) -> str:\n         )\n \n     def _get_index_and_items(self, index, grouper):\n-        from xarray.core.resample_cftime import CFTimeGrouper\n-\n-        s = pd.Series(np.arange(index.size), index)\n-        if isinstance(grouper, CFTimeGrouper):\n-            first_items = grouper.first_items(index)\n-        else:\n-            first_items = s.groupby(grouper).first()\n-            _apply_loffset(grouper, first_items)\n+        first_items = grouper.first_items(index)\n         full_index = first_items.index\n         if first_items.isnull().any():\n             first_items = first_items.dropna()\n@@ -1365,3 +1370,50 @@ class DatasetGroupBy(  # type: ignore[misc]\n     ImplementsDatasetReduce,\n ):\n     __slots__ = ()\n+\n+\n+class TimeResampleGrouper:\n+    def __init__(\n+        self,\n+        freq: str,\n+        closed: SideOptions | None,\n+        label: SideOptions | None,\n+        origin: str | DatetimeLike,\n+        offset: pd.Timedelta | datetime.timedelta | str | None,\n+        loffset: datetime.timedelta | str | None,\n+    ):\n+        self.freq = freq\n+        self.closed = closed\n+        self.label = label\n+        self.origin = origin\n+        self.offset = offset\n+        self.loffset = loffset\n+\n+    def first_items(self, index):\n+        from xarray import CFTimeIndex\n+        from xarray.core.resample_cftime import CFTimeGrouper\n+\n+        if isinstance(index, CFTimeIndex):\n+            grouper = CFTimeGrouper(\n+                freq=self.freq,\n+                closed=self.closed,\n+                label=self.label,\n+                origin=self.origin,\n+                offset=self.offset,\n+                loffset=self.loffset,\n+            )\n+            return grouper.first_items(index)\n+        else:\n+            s = pd.Series(np.arange(index.size), index)\n+            grouper = pd.Grouper(\n+                freq=self.freq,\n+                closed=self.closed,\n+                label=self.label,\n+                origin=self.origin,\n+                offset=self.offset,\n+            )\n+\n+            first_items = s.groupby(grouper).first()\n+            if self.loffset is not None:\n+                _apply_loffset(self.loffset, first_items)\n+        return first_items\ndiff --git a/xarray/core/pdcompat.py b/xarray/core/pdcompat.py\n--- a/xarray/core/pdcompat.py\n+++ b/xarray/core/pdcompat.py\n@@ -38,6 +38,10 @@\n from enum import Enum\n from typing import Literal\n \n+import pandas as pd\n+\n+from xarray.coding import cftime_offsets\n+\n \n def count_not_none(*args) -> int:\n     \"\"\"Compute the number of non-None arguments.\n@@ -68,3 +72,22 @@ def __repr__(self) -> str:\n     _NoDefault.no_default\n )  # Sentinel indicating the default value following pandas\n NoDefault = Literal[_NoDefault.no_default]  # For typing following pandas\n+\n+\n+def _convert_base_to_offset(base, freq, index):\n+    \"\"\"Required until we officially deprecate the base argument to resample.  This\n+    translates a provided `base` argument to an `offset` argument, following logic\n+    from pandas.\n+    \"\"\"\n+    from xarray.coding.cftimeindex import CFTimeIndex\n+\n+    if isinstance(index, pd.DatetimeIndex):\n+        freq = pd.tseries.frequencies.to_offset(freq)\n+        if isinstance(freq, pd.offsets.Tick):\n+            return pd.Timedelta(base * freq.nanos // freq.n)\n+    elif isinstance(index, CFTimeIndex):\n+        freq = cftime_offsets.to_offset(freq)\n+        if isinstance(freq, cftime_offsets.Tick):\n+            return base * freq.as_timedelta() // freq.n\n+    else:\n+        raise ValueError(\"Can only resample using a DatetimeIndex or CFTimeIndex.\")\ndiff --git a/xarray/core/resample_cftime.py b/xarray/core/resample_cftime.py\n--- a/xarray/core/resample_cftime.py\n+++ b/xarray/core/resample_cftime.py\n@@ -71,7 +71,6 @@ def __init__(\n         freq: str | BaseCFTimeOffset,\n         closed: SideOptions | None = None,\n         label: SideOptions | None = None,\n-        base: int | None = None,\n         loffset: str | datetime.timedelta | BaseCFTimeOffset | None = None,\n         origin: str | CFTimeDatetime = \"start_day\",\n         offset: str | datetime.timedelta | None = None,\n@@ -79,10 +78,6 @@ def __init__(\n         self.offset: datetime.timedelta | None\n         self.closed: SideOptions\n         self.label: SideOptions\n-\n-        if base is not None and offset is not None:\n-            raise ValueError(\"base and offset cannot be provided at the same time\")\n-\n         self.freq = to_offset(freq)\n         self.loffset = loffset\n         self.origin = origin\n@@ -122,9 +117,6 @@ def __init__(\n                 else:\n                     self.label = label\n \n-        if base is not None and isinstance(self.freq, Tick):\n-            offset = type(self.freq)(n=base % self.freq.n).as_timedelta()\n-\n         if offset is not None:\n             try:\n                 self.offset = _convert_offset_to_timedelta(offset)\n@@ -150,6 +142,16 @@ def first_items(self, index: CFTimeIndex):\n             index, self.freq, self.closed, self.label, self.origin, self.offset\n         )\n         if self.loffset is not None:\n+            if not isinstance(\n+                self.loffset, (str, datetime.timedelta, BaseCFTimeOffset)\n+            ):\n+                # BaseCFTimeOffset is not public API so we do not include it in\n+                # the error message for now.\n+                raise ValueError(\n+                    f\"`loffset` must be a str or datetime.timedelta object. \"\n+                    f\"Got {self.loffset}.\"\n+                )\n+\n             if isinstance(self.loffset, datetime.timedelta):\n                 labels = labels + self.loffset\n             else:\n"}
{"instance_id": "pylint-dev__pylint-4970", "model_name_or_path": "gold", "model_patch": "diff --git a/pylint/checkers/similar.py b/pylint/checkers/similar.py\n--- a/pylint/checkers/similar.py\n+++ b/pylint/checkers/similar.py\n@@ -390,6 +390,8 @@ def append_stream(self, streamid: str, stream: TextIO, encoding=None) -> None:\n \n     def run(self) -> None:\n         \"\"\"start looking for similarities and display results on stdout\"\"\"\n+        if self.min_lines == 0:\n+            return\n         self._display_sims(self._compute_sims())\n \n     def _compute_sims(self) -> List[Tuple[int, Set[LinesChunkLimits_T]]]:\n"}
{"instance_id": "pylint-dev__pylint-5231", "model_name_or_path": "gold", "model_patch": "diff --git a/pylint/extensions/_check_docs_utils.py b/pylint/extensions/_check_docs_utils.py\n--- a/pylint/extensions/_check_docs_utils.py\n+++ b/pylint/extensions/_check_docs_utils.py\n@@ -23,7 +23,7 @@\n \"\"\"Utility methods for docstring checking.\"\"\"\n \n import re\n-from typing import List\n+from typing import List, Set, Tuple\n \n import astroid\n from astroid import nodes\n@@ -731,9 +731,8 @@ class NumpyDocstring(GoogleDocstring):\n \n     re_param_line = re.compile(\n         fr\"\"\"\n-        \\s*  (\\*{{0,2}}\\w+)                                                 # identifier with potential asterisks\n-        \\s*  :\n-        \\s*  (?:({GoogleDocstring.re_multiple_type})(?:,\\s+optional)?)?     # optional type declaration\n+        \\s*  (\\*{{0,2}}\\w+)(\\s?(:|\\n))                                      # identifier with potential asterisks\n+        \\s*  (?:({GoogleDocstring.re_multiple_type})(?:,\\s+optional)?\\n)?   # optional type declaration\n         \\s* (.*)                                                            # optional description\n     \"\"\",\n         re.X | re.S,\n@@ -772,6 +771,38 @@ class NumpyDocstring(GoogleDocstring):\n \n     supports_yields = True\n \n+    def match_param_docs(self) -> Tuple[Set[str], Set[str]]:\n+        \"\"\"Matches parameter documentation section to parameter documentation rules\"\"\"\n+        params_with_doc = set()\n+        params_with_type = set()\n+\n+        entries = self._parse_section(self.re_param_section)\n+        print(entries)\n+        entries.extend(self._parse_section(self.re_keyword_param_section))\n+        for entry in entries:\n+            match = self.re_param_line.match(entry)\n+            if not match:\n+                continue\n+\n+            # check if parameter has description only\n+            re_only_desc = re.match(r\"\\s*  (\\*{0,2}\\w+)\\s*:?\\n\", entry)\n+            if re_only_desc:\n+                param_name = match.group(1)\n+                param_desc = match.group(2)\n+                param_type = None\n+            else:\n+                param_name = match.group(1)\n+                param_type = match.group(3)\n+                param_desc = match.group(4)\n+\n+            if param_type:\n+                params_with_type.add(param_name)\n+\n+            if param_desc:\n+                params_with_doc.add(param_name)\n+\n+        return params_with_doc, params_with_type\n+\n     @staticmethod\n     def min_section_indent(section_match):\n         return len(section_match.group(1))\n"}
{"instance_id": "pylint-dev__pylint-5951", "model_name_or_path": "gold", "model_patch": "diff --git a/pylint/pyreverse/dot_printer.py b/pylint/pyreverse/dot_printer.py\n--- a/pylint/pyreverse/dot_printer.py\n+++ b/pylint/pyreverse/dot_printer.py\n@@ -19,7 +19,7 @@\n from astroid import nodes\n \n from pylint.pyreverse.printer import EdgeType, Layout, NodeProperties, NodeType, Printer\n-from pylint.pyreverse.utils import check_graphviz_availability, get_annotation_label\n+from pylint.pyreverse.utils import get_annotation_label\n \n ALLOWED_CHARSETS: FrozenSet[str] = frozenset((\"utf-8\", \"iso-8859-1\", \"latin1\"))\n SHAPES: Dict[NodeType, str] = {\n@@ -152,7 +152,6 @@ def generate(self, outputfile: str) -> None:\n         with open(dot_sourcepath, \"w\", encoding=\"utf8\") as outfile:\n             outfile.writelines(self.lines)\n         if target not in graphviz_extensions:\n-            check_graphviz_availability()\n             use_shell = sys.platform == \"win32\"\n             subprocess.call(\n                 [\"dot\", \"-T\", target, dot_sourcepath, \"-o\", outputfile],\ndiff --git a/pylint/pyreverse/main.py b/pylint/pyreverse/main.py\n--- a/pylint/pyreverse/main.py\n+++ b/pylint/pyreverse/main.py\n@@ -32,7 +32,20 @@\n from pylint.pyreverse import writer\n from pylint.pyreverse.diadefslib import DiadefsHandler\n from pylint.pyreverse.inspector import Linker, project_from_files\n-from pylint.pyreverse.utils import check_graphviz_availability, insert_default_options\n+from pylint.pyreverse.utils import (\n+    check_graphviz_availability,\n+    check_if_graphviz_supports_format,\n+    insert_default_options,\n+)\n+\n+DIRECTLY_SUPPORTED_FORMATS = (\n+    \"dot\",\n+    \"vcg\",\n+    \"puml\",\n+    \"plantuml\",\n+    \"mmd\",\n+    \"html\",\n+)\n \n OPTIONS = (\n     (\n@@ -139,7 +152,10 @@\n             action=\"store\",\n             default=\"dot\",\n             metavar=\"<format>\",\n-            help=\"create a *.<format> output file if format available.\",\n+            help=(\n+                f\"create a *.<format> output file if format is available. Available formats are: {', '.join(DIRECTLY_SUPPORTED_FORMATS)}. \"\n+                f\"Any other format will be tried to create by means of the 'dot' command line tool, which requires a graphviz installation.\"\n+            ),\n         ),\n     ),\n     (\n@@ -205,15 +221,12 @@ def __init__(self, args: Iterable[str]):\n         super().__init__(usage=__doc__)\n         insert_default_options()\n         args = self.load_command_line_configuration(args)\n-        if self.config.output_format not in (\n-            \"dot\",\n-            \"vcg\",\n-            \"puml\",\n-            \"plantuml\",\n-            \"mmd\",\n-            \"html\",\n-        ):\n+        if self.config.output_format not in DIRECTLY_SUPPORTED_FORMATS:\n             check_graphviz_availability()\n+            print(\n+                f\"Format {self.config.output_format} is not supported natively. Pyreverse will try to generate it using Graphviz...\"\n+            )\n+            check_if_graphviz_supports_format(self.config.output_format)\n \n         sys.exit(self.run(args))\n \ndiff --git a/pylint/pyreverse/utils.py b/pylint/pyreverse/utils.py\n--- a/pylint/pyreverse/utils.py\n+++ b/pylint/pyreverse/utils.py\n@@ -23,6 +23,7 @@\n import os\n import re\n import shutil\n+import subprocess\n import sys\n from typing import Optional, Union\n \n@@ -290,9 +291,33 @@ def check_graphviz_availability():\n     from *.dot or *.gv into the final output format.\n     \"\"\"\n     if shutil.which(\"dot\") is None:\n+        print(\"'Graphviz' needs to be installed for your chosen output format.\")\n+        sys.exit(32)\n+\n+\n+def check_if_graphviz_supports_format(output_format: str) -> None:\n+    \"\"\"Check if the ``dot`` command supports the requested output format.\n+\n+    This is needed if image output is desired and ``dot`` is used to convert\n+    from *.gv into the final output format.\n+    \"\"\"\n+    dot_output = subprocess.run(\n+        [\"dot\", \"-T?\"], capture_output=True, check=False, encoding=\"utf-8\"\n+    )\n+    match = re.match(\n+        pattern=r\".*Use one of: (?P<formats>(\\S*\\s?)+)\",\n+        string=dot_output.stderr.strip(),\n+    )\n+    if not match:\n+        print(\n+            \"Unable to determine Graphviz supported output formats. \"\n+            \"Pyreverse will continue, but subsequent error messages \"\n+            \"regarding the output format may come from Graphviz directly.\"\n+        )\n+        return\n+    supported_formats = match.group(\"formats\")\n+    if output_format not in supported_formats.split():\n         print(\n-            \"The requested output format is currently not available.\\n\"\n-            \"Please install 'Graphviz' to have other output formats \"\n-            \"than 'dot' or 'vcg'.\"\n+            f\"Format {output_format} is not supported by Graphviz. It supports: {supported_formats}\"\n         )\n         sys.exit(32)\n"}
{"instance_id": "pylint-dev__pylint-6556", "model_name_or_path": "gold", "model_patch": "diff --git a/pylint/lint/pylinter.py b/pylint/lint/pylinter.py\n--- a/pylint/lint/pylinter.py\n+++ b/pylint/lint/pylinter.py\n@@ -937,8 +937,6 @@ def _check_astroid_module(\n             self.process_tokens(tokens)\n             if self._ignore_file:\n                 return False\n-            # walk ast to collect line numbers\n-            self.file_state.collect_block_lines(self.msgs_store, node)\n             # run raw and tokens checkers\n             for raw_checker in rawcheckers:\n                 raw_checker.process_module(node)\ndiff --git a/pylint/utils/file_state.py b/pylint/utils/file_state.py\n--- a/pylint/utils/file_state.py\n+++ b/pylint/utils/file_state.py\n@@ -74,25 +74,32 @@ def collect_block_lines(\n         self, msgs_store: MessageDefinitionStore, module_node: nodes.Module\n     ) -> None:\n         \"\"\"Walk the AST to collect block level options line numbers.\"\"\"\n+        warnings.warn(\n+            \"'collect_block_lines' has been deprecated and will be removed in pylint 3.0.\",\n+            DeprecationWarning,\n+        )\n         for msg, lines in self._module_msgs_state.items():\n             self._raw_module_msgs_state[msg] = lines.copy()\n         orig_state = self._module_msgs_state.copy()\n         self._module_msgs_state = {}\n         self._suppression_mapping = {}\n         self._effective_max_line_number = module_node.tolineno\n-        self._collect_block_lines(msgs_store, module_node, orig_state)\n+        for msgid, lines in orig_state.items():\n+            for msgdef in msgs_store.get_message_definitions(msgid):\n+                self._set_state_on_block_lines(msgs_store, module_node, msgdef, lines)\n \n-    def _collect_block_lines(\n+    def _set_state_on_block_lines(\n         self,\n         msgs_store: MessageDefinitionStore,\n         node: nodes.NodeNG,\n-        msg_state: MessageStateDict,\n+        msg: MessageDefinition,\n+        msg_state: dict[int, bool],\n     ) -> None:\n         \"\"\"Recursively walk (depth first) AST to collect block level options\n-        line numbers.\n+        line numbers and set the state correctly.\n         \"\"\"\n         for child in node.get_children():\n-            self._collect_block_lines(msgs_store, child, msg_state)\n+            self._set_state_on_block_lines(msgs_store, child, msg, msg_state)\n         # first child line number used to distinguish between disable\n         # which are the first child of scoped node with those defined later.\n         # For instance in the code below:\n@@ -115,9 +122,7 @@ def _collect_block_lines(\n             firstchildlineno = node.body[0].fromlineno\n         else:\n             firstchildlineno = node.tolineno\n-        for msgid, lines in msg_state.items():\n-            for msg in msgs_store.get_message_definitions(msgid):\n-                self._set_message_state_in_block(msg, lines, node, firstchildlineno)\n+        self._set_message_state_in_block(msg, msg_state, node, firstchildlineno)\n \n     def _set_message_state_in_block(\n         self,\n@@ -139,18 +144,61 @@ def _set_message_state_in_block(\n                 if lineno > firstchildlineno:\n                     state = True\n                 first_, last_ = node.block_range(lineno)\n+                # pylint: disable=useless-suppression\n+                # For block nodes first_ is their definition line. For example, we\n+                # set the state of line zero for a module to allow disabling\n+                # invalid-name for the module. For example:\n+                # 1. # pylint: disable=invalid-name\n+                # 2. ...\n+                # OR\n+                # 1. \"\"\"Module docstring\"\"\"\n+                # 2. # pylint: disable=invalid-name\n+                # 3. ...\n+                #\n+                # But if we already visited line 0 we don't need to set its state again\n+                # 1. # pylint: disable=invalid-name\n+                # 2. # pylint: enable=invalid-name\n+                # 3. ...\n+                # The state should come from line 1, not from line 2\n+                # Therefore, if the 'fromlineno' is already in the states we just start\n+                # with the lineno we were originally visiting.\n+                # pylint: enable=useless-suppression\n+                if (\n+                    first_ == node.fromlineno\n+                    and first_ >= firstchildlineno\n+                    and node.fromlineno in self._module_msgs_state.get(msg.msgid, ())\n+                ):\n+                    first_ = lineno\n+\n             else:\n                 first_ = lineno\n                 last_ = last\n             for line in range(first_, last_ + 1):\n-                # do not override existing entries\n-                if line in self._module_msgs_state.get(msg.msgid, ()):\n+                # Do not override existing entries. This is especially important\n+                # when parsing the states for a scoped node where some line-disables\n+                # have already been parsed.\n+                if (\n+                    (\n+                        isinstance(node, nodes.Module)\n+                        and node.fromlineno <= line < lineno\n+                    )\n+                    or (\n+                        not isinstance(node, nodes.Module)\n+                        and node.fromlineno < line < lineno\n+                    )\n+                ) and line in self._module_msgs_state.get(msg.msgid, ()):\n                     continue\n                 if line in lines:  # state change in the same block\n                     state = lines[line]\n                     original_lineno = line\n+\n+                # Update suppression mapping\n                 if not state:\n                     self._suppression_mapping[(msg.msgid, line)] = original_lineno\n+                else:\n+                    self._suppression_mapping.pop((msg.msgid, line), None)\n+\n+                # Update message state for respective line\n                 try:\n                     self._module_msgs_state[msg.msgid][line] = state\n                 except KeyError:\n@@ -160,10 +208,20 @@ def _set_message_state_in_block(\n     def set_msg_status(self, msg: MessageDefinition, line: int, status: bool) -> None:\n         \"\"\"Set status (enabled/disable) for a given message at a given line.\"\"\"\n         assert line > 0\n+        assert self._module\n+        # TODO: 3.0: Remove unnecessary assertion\n+        assert self._msgs_store\n+\n+        # Expand the status to cover all relevant block lines\n+        self._set_state_on_block_lines(\n+            self._msgs_store, self._module, msg, {line: status}\n+        )\n+\n+        # Store the raw value\n         try:\n-            self._module_msgs_state[msg.msgid][line] = status\n+            self._raw_module_msgs_state[msg.msgid][line] = status\n         except KeyError:\n-            self._module_msgs_state[msg.msgid] = {line: status}\n+            self._raw_module_msgs_state[msg.msgid] = {line: status}\n \n     def handle_ignored_message(\n         self, state_scope: Literal[0, 1, 2] | None, msgid: str, line: int | None\n"}
{"instance_id": "pylint-dev__pylint-7993", "model_name_or_path": "gold", "model_patch": "diff --git a/pylint/reporters/text.py b/pylint/reporters/text.py\n--- a/pylint/reporters/text.py\n+++ b/pylint/reporters/text.py\n@@ -175,7 +175,7 @@ def on_set_current_module(self, module: str, filepath: str | None) -> None:\n         self._template = template\n \n         # Check to see if all parameters in the template are attributes of the Message\n-        arguments = re.findall(r\"\\{(.+?)(:.*)?\\}\", template)\n+        arguments = re.findall(r\"\\{(\\w+?)(:.*)?\\}\", template)\n         for argument in arguments:\n             if argument[0] not in MESSAGE_FIELDS:\n                 warnings.warn(\n"}
{"instance_id": "pylint-dev__pylint-8124", "model_name_or_path": "gold", "model_patch": "diff --git a/pylint/checkers/imports.py b/pylint/checkers/imports.py\n--- a/pylint/checkers/imports.py\n+++ b/pylint/checkers/imports.py\n@@ -439,6 +439,15 @@ class ImportsChecker(DeprecatedMixin, BaseChecker):\n                 \"help\": \"Allow wildcard imports from modules that define __all__.\",\n             },\n         ),\n+        (\n+            \"allow-reexport-from-package\",\n+            {\n+                \"default\": False,\n+                \"type\": \"yn\",\n+                \"metavar\": \"<y or n>\",\n+                \"help\": \"Allow explicit reexports by alias from a package __init__.\",\n+            },\n+        ),\n     )\n \n     def __init__(self, linter: PyLinter) -> None:\n@@ -461,6 +470,7 @@ def open(self) -> None:\n         self.linter.stats = self.linter.stats\n         self.import_graph = defaultdict(set)\n         self._module_pkg = {}  # mapping of modules to the pkg they belong in\n+        self._current_module_package = False\n         self._excluded_edges: defaultdict[str, set[str]] = defaultdict(set)\n         self._ignored_modules: Sequence[str] = self.linter.config.ignored_modules\n         # Build a mapping {'module': 'preferred-module'}\n@@ -470,6 +480,7 @@ def open(self) -> None:\n             if \":\" in module\n         )\n         self._allow_any_import_level = set(self.linter.config.allow_any_import_level)\n+        self._allow_reexport_package = self.linter.config.allow_reexport_from_package\n \n     def _import_graph_without_ignored_edges(self) -> defaultdict[str, set[str]]:\n         filtered_graph = copy.deepcopy(self.import_graph)\n@@ -495,6 +506,10 @@ def deprecated_modules(self) -> set[str]:\n                 all_deprecated_modules = all_deprecated_modules.union(mod_set)\n         return all_deprecated_modules\n \n+    def visit_module(self, node: nodes.Module) -> None:\n+        \"\"\"Store if current module is a package, i.e. an __init__ file.\"\"\"\n+        self._current_module_package = node.package\n+\n     def visit_import(self, node: nodes.Import) -> None:\n         \"\"\"Triggered when an import statement is seen.\"\"\"\n         self._check_reimport(node)\n@@ -917,8 +932,11 @@ def _check_import_as_rename(self, node: ImportNode) -> None:\n             if import_name != aliased_name:\n                 continue\n \n-            if len(splitted_packages) == 1:\n-                self.add_message(\"useless-import-alias\", node=node)\n+            if len(splitted_packages) == 1 and (\n+                self._allow_reexport_package is False\n+                or self._current_module_package is False\n+            ):\n+                self.add_message(\"useless-import-alias\", node=node, confidence=HIGH)\n             elif len(splitted_packages) == 2:\n                 self.add_message(\n                     \"consider-using-from-import\",\n"}
{"instance_id": "pylint-dev__pylint-8312", "model_name_or_path": "gold", "model_patch": "diff --git a/pylint/config/config_initialization.py b/pylint/config/config_initialization.py\n--- a/pylint/config/config_initialization.py\n+++ b/pylint/config/config_initialization.py\n@@ -5,6 +5,8 @@\n from __future__ import annotations\n \n import sys\n+from glob import glob\n+from itertools import chain\n from pathlib import Path\n from typing import TYPE_CHECKING\n \n@@ -118,6 +120,14 @@ def _config_initialization(\n     # Link the base Namespace object on the current directory\n     linter._directory_namespaces[Path(\".\").resolve()] = (linter.config, {})\n \n-    # parsed_args_list should now only be a list of files/directories to lint.\n+    # parsed_args_list should now only be a list of inputs to lint.\n     # All other options have been removed from the list.\n-    return parsed_args_list\n+    return list(\n+        chain.from_iterable(\n+            # NOTE: 'or [arg]' is needed in the case the input file or directory does not exist and 'glob(arg)' cannot\n+            # find anything. Without this we would not be able to output the fatal import error for this module later\n+            # on, as it would get silently ignored.\n+            glob(arg, recursive=True) or [arg]\n+            for arg in parsed_args_list\n+        )\n+    )\n"}
{"instance_id": "pylint-dev__pylint-4516", "model_name_or_path": "gold", "model_patch": "diff --git a/pylint/lint/expand_modules.py b/pylint/lint/expand_modules.py\n--- a/pylint/lint/expand_modules.py\n+++ b/pylint/lint/expand_modules.py\n@@ -1,5 +1,6 @@\n import os\n import sys\n+from typing import List, Pattern, Tuple\n \n from astroid import modutils\n \n@@ -28,32 +29,33 @@ def get_python_path(filepath: str) -> str:\n             return os.getcwd()\n \n \n-def _basename_in_ignore_list_re(base_name, ignore_list_re):\n-    \"\"\"Determines if the basename is matched in a regex ignorelist\n-\n-    :param str base_name: The basename of the file\n-    :param list ignore_list_re: A collection of regex patterns to match against.\n-        Successful matches are ignored.\n-\n-    :returns: `True` if the basename is ignored, `False` otherwise.\n-    :rtype: bool\n-    \"\"\"\n+def _is_in_ignore_list_re(element: str, ignore_list_re: List[Pattern]) -> bool:\n+    \"\"\"determines if the element is matched in a regex ignore-list\"\"\"\n     for file_pattern in ignore_list_re:\n-        if file_pattern.match(base_name):\n+        if file_pattern.match(element):\n             return True\n     return False\n \n \n-def expand_modules(files_or_modules, ignore_list, ignore_list_re):\n-    \"\"\"Take a list of files/modules/packages and return the list of tuple\n-    (file, module name) which have to be actually checked.\"\"\"\n+def expand_modules(\n+    files_or_modules: List[str],\n+    ignore_list: List[str],\n+    ignore_list_re: List[Pattern],\n+    ignore_list_paths_re: List[Pattern],\n+) -> Tuple[List[dict], List[dict]]:\n+    \"\"\"take a list of files/modules/packages and return the list of tuple\n+    (file, module name) which have to be actually checked\n+    \"\"\"\n     result = []\n     errors = []\n     path = sys.path.copy()\n+\n     for something in files_or_modules:\n         basename = os.path.basename(something)\n-        if basename in ignore_list or _basename_in_ignore_list_re(\n-            basename, ignore_list_re\n+        if (\n+            basename in ignore_list\n+            or _is_in_ignore_list_re(os.path.basename(something), ignore_list_re)\n+            or _is_in_ignore_list_re(something, ignore_list_paths_re)\n         ):\n             continue\n         module_path = get_python_path(something)\n@@ -117,10 +119,11 @@ def expand_modules(files_or_modules, ignore_list, ignore_list_re):\n             ):\n                 if filepath == subfilepath:\n                     continue\n-                if _basename_in_ignore_list_re(\n+                if _is_in_ignore_list_re(\n                     os.path.basename(subfilepath), ignore_list_re\n-                ):\n+                ) or _is_in_ignore_list_re(subfilepath, ignore_list_paths_re):\n                     continue\n+\n                 modpath = _modpath_from_file(\n                     subfilepath, is_namespace, path=additional_search_path\n                 )\ndiff --git a/pylint/lint/pylinter.py b/pylint/lint/pylinter.py\n--- a/pylint/lint/pylinter.py\n+++ b/pylint/lint/pylinter.py\n@@ -186,6 +186,17 @@ def make_options():\n                     \" skipped. The regex matches against base names, not paths.\",\n                 },\n             ),\n+            (\n+                \"ignore-paths\",\n+                {\n+                    \"type\": \"regexp_csv\",\n+                    \"metavar\": \"<pattern>[,<pattern>...]\",\n+                    \"dest\": \"ignore_list_paths_re\",\n+                    \"default\": (),\n+                    \"help\": \"Add files or directories matching the regex patterns to the\"\n+                    \" ignore-list. The regex matches against paths.\",\n+                },\n+            ),\n             (\n                 \"persistent\",\n                 {\n@@ -1046,7 +1057,10 @@ def _iterate_file_descrs(self, files_or_modules):\n     def _expand_files(self, modules):\n         \"\"\"get modules and errors from a list of modules and handle errors\"\"\"\n         result, errors = expand_modules(\n-            modules, self.config.black_list, self.config.black_list_re\n+            modules,\n+            self.config.black_list,\n+            self.config.black_list_re,\n+            self.config.ignore_list_paths_re,\n         )\n         for error in errors:\n             message = modname = error[\"mod\"]\n"}
{"instance_id": "pylint-dev__pylint-4703", "model_name_or_path": "gold", "model_patch": "diff --git a/pylint/checkers/imports.py b/pylint/checkers/imports.py\n--- a/pylint/checkers/imports.py\n+++ b/pylint/checkers/imports.py\n@@ -813,7 +813,9 @@ def _get_imported_module(self, importnode, modname):\n             self.add_message(\"import-error\", args=repr(dotted_modname), node=importnode)\n         return None\n \n-    def _add_imported_module(self, node, importedmodname):\n+    def _add_imported_module(\n+        self, node: Union[astroid.Import, astroid.ImportFrom], importedmodname: str\n+    ) -> None:\n         \"\"\"notify an imported module, used to analyze dependencies\"\"\"\n         module_file = node.root().file\n         context_name = node.root().name\n@@ -826,6 +828,10 @@ def _add_imported_module(self, node, importedmodname):\n         except ImportError:\n             pass\n \n+        in_type_checking_block = (\n+            isinstance(node.parent, astroid.If) and node.parent.is_typing_guard()\n+        )\n+\n         if context_name == importedmodname:\n             self.add_message(\"import-self\", node=node)\n \n@@ -845,7 +851,10 @@ def _add_imported_module(self, node, importedmodname):\n \n             # update import graph\n             self.import_graph[context_name].add(importedmodname)\n-            if not self.linter.is_message_enabled(\"cyclic-import\", line=node.lineno):\n+            if (\n+                not self.linter.is_message_enabled(\"cyclic-import\", line=node.lineno)\n+                or in_type_checking_block\n+            ):\n                 self._excluded_edges[context_name].add(importedmodname)\n \n     def _check_preferred_module(self, node, mod_path):\n"}
{"instance_id": "pylint-dev__pylint-8929", "model_name_or_path": "gold", "model_patch": "diff --git a/pylint/interfaces.py b/pylint/interfaces.py\n--- a/pylint/interfaces.py\n+++ b/pylint/interfaces.py\n@@ -35,3 +35,4 @@ class Confidence(NamedTuple):\n \n CONFIDENCE_LEVELS = [HIGH, CONTROL_FLOW, INFERENCE, INFERENCE_FAILURE, UNDEFINED]\n CONFIDENCE_LEVEL_NAMES = [i.name for i in CONFIDENCE_LEVELS]\n+CONFIDENCE_MAP = {i.name: i for i in CONFIDENCE_LEVELS}\ndiff --git a/pylint/lint/base_options.py b/pylint/lint/base_options.py\n--- a/pylint/lint/base_options.py\n+++ b/pylint/lint/base_options.py\n@@ -102,9 +102,10 @@ def _make_linter_options(linter: PyLinter) -> Options:\n                 \"metavar\": \"<format>\",\n                 \"short\": \"f\",\n                 \"group\": \"Reports\",\n-                \"help\": \"Set the output format. Available formats are text,\"\n-                \" parseable, colorized, json and msvs (visual studio).\"\n-                \" You can also give a reporter class, e.g. mypackage.mymodule.\"\n+                \"help\": \"Set the output format. Available formats are: text, \"\n+                \"parseable, colorized, json2 (improved json format), json \"\n+                \"(old json format) and msvs (visual studio). \"\n+                \"You can also give a reporter class, e.g. mypackage.mymodule.\"\n                 \"MyReporterClass.\",\n                 \"kwargs\": {\"linter\": linter},\n             },\ndiff --git a/pylint/reporters/__init__.py b/pylint/reporters/__init__.py\n--- a/pylint/reporters/__init__.py\n+++ b/pylint/reporters/__init__.py\n@@ -11,7 +11,7 @@\n from pylint import utils\n from pylint.reporters.base_reporter import BaseReporter\n from pylint.reporters.collecting_reporter import CollectingReporter\n-from pylint.reporters.json_reporter import JSONReporter\n+from pylint.reporters.json_reporter import JSON2Reporter, JSONReporter\n from pylint.reporters.multi_reporter import MultiReporter\n from pylint.reporters.reports_handler_mix_in import ReportsHandlerMixIn\n \n@@ -28,6 +28,7 @@ def initialize(linter: PyLinter) -> None:\n     \"BaseReporter\",\n     \"ReportsHandlerMixIn\",\n     \"JSONReporter\",\n+    \"JSON2Reporter\",\n     \"CollectingReporter\",\n     \"MultiReporter\",\n ]\ndiff --git a/pylint/reporters/json_reporter.py b/pylint/reporters/json_reporter.py\n--- a/pylint/reporters/json_reporter.py\n+++ b/pylint/reporters/json_reporter.py\n@@ -9,7 +9,7 @@\n import json\n from typing import TYPE_CHECKING, Optional, TypedDict\n \n-from pylint.interfaces import UNDEFINED\n+from pylint.interfaces import CONFIDENCE_MAP, UNDEFINED\n from pylint.message import Message\n from pylint.reporters.base_reporter import BaseReporter\n from pylint.typing import MessageLocationTuple\n@@ -37,8 +37,12 @@\n )\n \n \n-class BaseJSONReporter(BaseReporter):\n-    \"\"\"Report messages and layouts in JSON.\"\"\"\n+class JSONReporter(BaseReporter):\n+    \"\"\"Report messages and layouts in JSON.\n+\n+    Consider using JSON2Reporter instead, as it is superior and this reporter\n+    is no longer maintained.\n+    \"\"\"\n \n     name = \"json\"\n     extension = \"json\"\n@@ -54,25 +58,6 @@ def display_reports(self, layout: Section) -> None:\n     def _display(self, layout: Section) -> None:\n         \"\"\"Do nothing.\"\"\"\n \n-    @staticmethod\n-    def serialize(message: Message) -> OldJsonExport:\n-        raise NotImplementedError\n-\n-    @staticmethod\n-    def deserialize(message_as_json: OldJsonExport) -> Message:\n-        raise NotImplementedError\n-\n-\n-class JSONReporter(BaseJSONReporter):\n-\n-    \"\"\"\n-    TODO: 3.0: Remove this JSONReporter in favor of the new one handling abs-path\n-    and confidence.\n-\n-    TODO: 3.0: Add a new JSONReporter handling abs-path, confidence and scores.\n-    (Ultimately all other breaking change related to json for 3.0).\n-    \"\"\"\n-\n     @staticmethod\n     def serialize(message: Message) -> OldJsonExport:\n         return {\n@@ -96,7 +81,6 @@ def deserialize(message_as_json: OldJsonExport) -> Message:\n             symbol=message_as_json[\"symbol\"],\n             msg=message_as_json[\"message\"],\n             location=MessageLocationTuple(\n-                # TODO: 3.0: Add abs-path and confidence in a new JSONReporter\n                 abspath=message_as_json[\"path\"],\n                 path=message_as_json[\"path\"],\n                 module=message_as_json[\"module\"],\n@@ -106,10 +90,112 @@ def deserialize(message_as_json: OldJsonExport) -> Message:\n                 end_line=message_as_json[\"endLine\"],\n                 end_column=message_as_json[\"endColumn\"],\n             ),\n-            # TODO: 3.0: Make confidence available in a new JSONReporter\n             confidence=UNDEFINED,\n         )\n \n \n+class JSONMessage(TypedDict):\n+    type: str\n+    message: str\n+    messageId: str\n+    symbol: str\n+    confidence: str\n+    module: str\n+    path: str\n+    absolutePath: str\n+    line: int\n+    endLine: int | None\n+    column: int\n+    endColumn: int | None\n+    obj: str\n+\n+\n+class JSON2Reporter(BaseReporter):\n+    name = \"json2\"\n+    extension = \"json2\"\n+\n+    def display_reports(self, layout: Section) -> None:\n+        \"\"\"Don't do anything in this reporter.\"\"\"\n+\n+    def _display(self, layout: Section) -> None:\n+        \"\"\"Do nothing.\"\"\"\n+\n+    def display_messages(self, layout: Section | None) -> None:\n+        \"\"\"Launch layouts display.\"\"\"\n+        output = {\n+            \"messages\": [self.serialize(message) for message in self.messages],\n+            \"statistics\": self.serialize_stats(),\n+        }\n+        print(json.dumps(output, indent=4), file=self.out)\n+\n+    @staticmethod\n+    def serialize(message: Message) -> JSONMessage:\n+        return JSONMessage(\n+            type=message.category,\n+            symbol=message.symbol,\n+            message=message.msg or \"\",\n+            messageId=message.msg_id,\n+            confidence=message.confidence.name,\n+            module=message.module,\n+            obj=message.obj,\n+            line=message.line,\n+            column=message.column,\n+            endLine=message.end_line,\n+            endColumn=message.end_column,\n+            path=message.path,\n+            absolutePath=message.abspath,\n+        )\n+\n+    @staticmethod\n+    def deserialize(message_as_json: JSONMessage) -> Message:\n+        return Message(\n+            msg_id=message_as_json[\"messageId\"],\n+            symbol=message_as_json[\"symbol\"],\n+            msg=message_as_json[\"message\"],\n+            location=MessageLocationTuple(\n+                abspath=message_as_json[\"absolutePath\"],\n+                path=message_as_json[\"path\"],\n+                module=message_as_json[\"module\"],\n+                obj=message_as_json[\"obj\"],\n+                line=message_as_json[\"line\"],\n+                column=message_as_json[\"column\"],\n+                end_line=message_as_json[\"endLine\"],\n+                end_column=message_as_json[\"endColumn\"],\n+            ),\n+            confidence=CONFIDENCE_MAP[message_as_json[\"confidence\"]],\n+        )\n+\n+    def serialize_stats(self) -> dict[str, str | int | dict[str, int]]:\n+        \"\"\"Serialize the linter stats into something JSON dumpable.\"\"\"\n+        stats = self.linter.stats\n+\n+        counts_dict = {\n+            \"fatal\": stats.fatal,\n+            \"error\": stats.error,\n+            \"warning\": stats.warning,\n+            \"refactor\": stats.refactor,\n+            \"convention\": stats.convention,\n+            \"info\": stats.info,\n+        }\n+\n+        # Calculate score based on the evaluation option\n+        evaluation = self.linter.config.evaluation\n+        try:\n+            note: int = eval(  # pylint: disable=eval-used\n+                evaluation, {}, {**counts_dict, \"statement\": stats.statement or 1}\n+            )\n+        except Exception as ex:  # pylint: disable=broad-except\n+            score: str | int = f\"An exception occurred while rating: {ex}\"\n+        else:\n+            score = round(note, 2)\n+\n+        return {\n+            \"messageTypeCount\": counts_dict,\n+            \"modulesLinted\": len(stats.by_module),\n+            \"score\": score,\n+        }\n+\n+\n def register(linter: PyLinter) -> None:\n     linter.register_reporter(JSONReporter)\n+    linter.register_reporter(JSON2Reporter)\ndiff --git a/pylint/testutils/_primer/primer_run_command.py b/pylint/testutils/_primer/primer_run_command.py\n--- a/pylint/testutils/_primer/primer_run_command.py\n+++ b/pylint/testutils/_primer/primer_run_command.py\n@@ -13,8 +13,7 @@\n \n from pylint.lint import Run\n from pylint.message import Message\n-from pylint.reporters import JSONReporter\n-from pylint.reporters.json_reporter import OldJsonExport\n+from pylint.reporters.json_reporter import JSONReporter, OldJsonExport\n from pylint.testutils._primer.package_to_lint import PackageToLint\n from pylint.testutils._primer.primer_command import (\n     PackageData,\n"}
{"instance_id": "pytest-dev__pytest-5227", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/logging.py b/src/_pytest/logging.py\n--- a/src/_pytest/logging.py\n+++ b/src/_pytest/logging.py\n@@ -15,7 +15,7 @@\n from _pytest.config import create_terminal_writer\n from _pytest.pathlib import Path\n \n-DEFAULT_LOG_FORMAT = \"%(filename)-25s %(lineno)4d %(levelname)-8s %(message)s\"\n+DEFAULT_LOG_FORMAT = \"%(levelname)-8s %(name)s:%(filename)s:%(lineno)d %(message)s\"\n DEFAULT_LOG_DATE_FORMAT = \"%H:%M:%S\"\n \n \n"}
{"instance_id": "pytest-dev__pytest-5281", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/capture.py b/src/_pytest/capture.py\n--- a/src/_pytest/capture.py\n+++ b/src/_pytest/capture.py\n@@ -447,6 +447,10 @@ def name(self):\n         \"\"\"Ensure that file.name is a string.\"\"\"\n         return repr(self.buffer)\n \n+    @property\n+    def mode(self):\n+        return self.buffer.mode.replace(\"b\", \"\")\n+\n     def __getattr__(self, name):\n         return getattr(object.__getattribute__(self, \"buffer\"), name)\n \ndiff --git a/src/_pytest/terminal.py b/src/_pytest/terminal.py\n--- a/src/_pytest/terminal.py\n+++ b/src/_pytest/terminal.py\n@@ -998,7 +998,15 @@ def _get_line_with_reprcrash_message(config, rep, termwidth):\n                     # u'\ud83d\ude04' will result in a High Surrogate (U+D83D) character, which is\n                     # rendered as u'\ufffd'; in this case we just strip that character out as it\n                     # serves no purpose being rendered\n-                    msg = msg.rstrip(u\"\\uD83D\")\n+                    try:\n+                        surrogate = six.unichr(0xD83D)\n+                        msg = msg.rstrip(surrogate)\n+                    except ValueError:  # pragma: no cover\n+                        # Jython cannot represent this lone surrogate at all (#5256):\n+                        # ValueError: unichr() arg is a lone surrogate in range\n+                        #     (0xD800, 0xDFFF) (Jython UTF-16 encoding)\n+                        # ignore this case as it shouldn't appear in the string anyway\n+                        pass\n                 msg += ellipsis\n             line += sep + msg\n     return line\n"}
{"instance_id": "pytest-dev__pytest-7314", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/terminal.py b/src/_pytest/terminal.py\n--- a/src/_pytest/terminal.py\n+++ b/src/_pytest/terminal.py\n@@ -166,7 +166,11 @@ def getreportopt(config):\n         reportchars += \"w\"\n     elif config.option.disable_warnings and \"w\" in reportchars:\n         reportchars = reportchars.replace(\"w\", \"\")\n+    aliases = {\"F\", \"S\"}\n     for char in reportchars:\n+        # handle old aliases\n+        if char in aliases:\n+            char = char.lower()\n         if char == \"a\":\n             reportopts = \"sxXwEf\"\n         elif char == \"A\":\n@@ -179,15 +183,18 @@ def getreportopt(config):\n \n @pytest.hookimpl(trylast=True)  # after _pytest.runner\n def pytest_report_teststatus(report):\n+    letter = \"F\"\n     if report.passed:\n         letter = \".\"\n     elif report.skipped:\n         letter = \"s\"\n-    elif report.failed:\n-        letter = \"F\"\n-        if report.when != \"call\":\n-            letter = \"f\"\n-    return report.outcome, letter, report.outcome.upper()\n+\n+    outcome = report.outcome\n+    if report.when in (\"collect\", \"setup\", \"teardown\") and outcome == \"failed\":\n+        outcome = \"error\"\n+        letter = \"E\"\n+\n+    return outcome, letter, outcome.upper()\n \n \n @attr.s\n@@ -935,9 +942,7 @@ def show_skipped(lines):\n             \"x\": show_xfailed,\n             \"X\": show_xpassed,\n             \"f\": partial(show_simple, \"failed\"),\n-            \"F\": partial(show_simple, \"failed\"),\n             \"s\": show_skipped,\n-            \"S\": show_skipped,\n             \"p\": partial(show_simple, \"passed\"),\n             \"E\": partial(show_simple, \"error\"),\n         }\n"}
{"instance_id": "pytest-dev__pytest-5692", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/junitxml.py b/src/_pytest/junitxml.py\n--- a/src/_pytest/junitxml.py\n+++ b/src/_pytest/junitxml.py\n@@ -10,9 +10,11 @@\n \"\"\"\n import functools\n import os\n+import platform\n import re\n import sys\n import time\n+from datetime import datetime\n \n import py\n \n@@ -666,6 +668,8 @@ def pytest_sessionfinish(self):\n             skipped=self.stats[\"skipped\"],\n             tests=numtests,\n             time=\"%.3f\" % suite_time_delta,\n+            timestamp=datetime.fromtimestamp(self.suite_start_time).isoformat(),\n+            hostname=platform.node(),\n         )\n         logfile.write(Junit.testsuites([suite_node]).unicode(indent=0))\n         logfile.close()\n"}
{"instance_id": "pytest-dev__pytest-5840", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/config/__init__.py b/src/_pytest/config/__init__.py\n--- a/src/_pytest/config/__init__.py\n+++ b/src/_pytest/config/__init__.py\n@@ -30,7 +30,6 @@\n from _pytest.compat import importlib_metadata\n from _pytest.outcomes import fail\n from _pytest.outcomes import Skipped\n-from _pytest.pathlib import unique_path\n from _pytest.warning_types import PytestConfigWarning\n \n hookimpl = HookimplMarker(\"pytest\")\n@@ -367,7 +366,7 @@ def _set_initial_conftests(self, namespace):\n         \"\"\"\n         current = py.path.local()\n         self._confcutdir = (\n-            unique_path(current.join(namespace.confcutdir, abs=True))\n+            current.join(namespace.confcutdir, abs=True)\n             if namespace.confcutdir\n             else None\n         )\n@@ -406,13 +405,11 @@ def _getconftestmodules(self, path):\n         else:\n             directory = path\n \n-        directory = unique_path(directory)\n-\n         # XXX these days we may rather want to use config.rootdir\n         # and allow users to opt into looking into the rootdir parent\n         # directories instead of requiring to specify confcutdir\n         clist = []\n-        for parent in directory.parts():\n+        for parent in directory.realpath().parts():\n             if self._confcutdir and self._confcutdir.relto(parent):\n                 continue\n             conftestpath = parent.join(\"conftest.py\")\n@@ -432,12 +429,14 @@ def _rget_with_confmod(self, name, path):\n         raise KeyError(name)\n \n     def _importconftest(self, conftestpath):\n-        # Use realpath to avoid loading the same conftest twice\n+        # Use a resolved Path object as key to avoid loading the same conftest twice\n         # with build systems that create build directories containing\n         # symlinks to actual files.\n-        conftestpath = unique_path(conftestpath)\n+        # Using Path().resolve() is better than py.path.realpath because\n+        # it resolves to the correct path/drive in case-insensitive file systems (#5792)\n+        key = Path(str(conftestpath)).resolve()\n         try:\n-            return self._conftestpath2mod[conftestpath]\n+            return self._conftestpath2mod[key]\n         except KeyError:\n             pkgpath = conftestpath.pypkgpath()\n             if pkgpath is None:\n@@ -454,7 +453,7 @@ def _importconftest(self, conftestpath):\n                 raise ConftestImportFailure(conftestpath, sys.exc_info())\n \n             self._conftest_plugins.add(mod)\n-            self._conftestpath2mod[conftestpath] = mod\n+            self._conftestpath2mod[key] = mod\n             dirpath = conftestpath.dirpath()\n             if dirpath in self._dirpath2confmods:\n                 for path, mods in self._dirpath2confmods.items():\ndiff --git a/src/_pytest/pathlib.py b/src/_pytest/pathlib.py\n--- a/src/_pytest/pathlib.py\n+++ b/src/_pytest/pathlib.py\n@@ -11,7 +11,6 @@\n from os.path import expanduser\n from os.path import expandvars\n from os.path import isabs\n-from os.path import normcase\n from os.path import sep\n from posixpath import sep as posix_sep\n \n@@ -335,12 +334,3 @@ def fnmatch_ex(pattern, path):\n def parts(s):\n     parts = s.split(sep)\n     return {sep.join(parts[: i + 1]) or sep for i in range(len(parts))}\n-\n-\n-def unique_path(path):\n-    \"\"\"Returns a unique path in case-insensitive (but case-preserving) file\n-    systems such as Windows.\n-\n-    This is needed only for ``py.path.local``; ``pathlib.Path`` handles this\n-    natively with ``resolve()``.\"\"\"\n-    return type(path)(normcase(str(path.realpath())))\n"}
{"instance_id": "pytest-dev__pytest-6214", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/setupplan.py b/src/_pytest/setupplan.py\n--- a/src/_pytest/setupplan.py\n+++ b/src/_pytest/setupplan.py\n@@ -16,7 +16,8 @@ def pytest_addoption(parser):\n def pytest_fixture_setup(fixturedef, request):\n     # Will return a dummy fixture if the setuponly option is provided.\n     if request.config.option.setupplan:\n-        fixturedef.cached_result = (None, None, None)\n+        my_cache_key = fixturedef.cache_key(request)\n+        fixturedef.cached_result = (None, my_cache_key, None)\n         return fixturedef.cached_result\n \n \n"}
{"instance_id": "pytest-dev__pytest-6680", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/deprecated.py b/src/_pytest/deprecated.py\n--- a/src/_pytest/deprecated.py\n+++ b/src/_pytest/deprecated.py\n@@ -36,7 +36,10 @@\n \n NODE_USE_FROM_PARENT = UnformattedWarning(\n     PytestDeprecationWarning,\n-    \"direct construction of {name} has been deprecated, please use {name}.from_parent\",\n+    \"Direct construction of {name} has been deprecated, please use {name}.from_parent.\\n\"\n+    \"See \"\n+    \"https://docs.pytest.org/en/latest/deprecations.html#node-construction-changed-to-node-from-parent\"\n+    \" for more details.\",\n )\n \n JUNIT_XML_DEFAULT_FAMILY = PytestDeprecationWarning(\n"}
{"instance_id": "pytest-dev__pytest-7468", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/logging.py b/src/_pytest/logging.py\n--- a/src/_pytest/logging.py\n+++ b/src/_pytest/logging.py\n@@ -531,11 +531,17 @@ def __init__(self, config: Config) -> None:\n         # File logging.\n         self.log_file_level = get_log_level_for_setting(config, \"log_file_level\")\n         log_file = get_option_ini(config, \"log_file\") or os.devnull\n+        if log_file != os.devnull:\n+            directory = os.path.dirname(os.path.abspath(log_file))\n+            if not os.path.isdir(directory):\n+                os.makedirs(directory)\n+\n         self.log_file_handler = _FileHandler(log_file, mode=\"w\", encoding=\"UTF-8\")\n         log_file_format = get_option_ini(config, \"log_file_format\", \"log_format\")\n         log_file_date_format = get_option_ini(\n             config, \"log_file_date_format\", \"log_date_format\"\n         )\n+\n         log_file_formatter = logging.Formatter(\n             log_file_format, datefmt=log_file_date_format\n         )\n"}
{"instance_id": "pytest-dev__pytest-7749", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/assertion/rewrite.py b/src/_pytest/assertion/rewrite.py\n--- a/src/_pytest/assertion/rewrite.py\n+++ b/src/_pytest/assertion/rewrite.py\n@@ -687,13 +687,18 @@ def run(self, mod: ast.Module) -> None:\n                     return\n                 expect_docstring = False\n             elif (\n-                not isinstance(item, ast.ImportFrom)\n-                or item.level > 0\n-                or item.module != \"__future__\"\n+                isinstance(item, ast.ImportFrom)\n+                and item.level == 0\n+                and item.module == \"__future__\"\n             ):\n-                lineno = item.lineno\n+                pass\n+            else:\n                 break\n             pos += 1\n+        # Special case: for a decorated function, set the lineno to that of the\n+        # first decorator, not the `def`. Issue #4984.\n+        if isinstance(item, ast.FunctionDef) and item.decorator_list:\n+            lineno = item.decorator_list[0].lineno\n         else:\n             lineno = item.lineno\n         imports = [\n"}
{"instance_id": "pytest-dev__pytest-8124", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/hookspec.py b/src/_pytest/hookspec.py\n--- a/src/_pytest/hookspec.py\n+++ b/src/_pytest/hookspec.py\n@@ -808,6 +808,27 @@ def pytest_warning_recorded(\n     \"\"\"\n \n \n+# -------------------------------------------------------------------------\n+# Hooks for influencing skipping\n+# -------------------------------------------------------------------------\n+\n+\n+def pytest_markeval_namespace(config: \"Config\") -> Dict[str, Any]:\n+    \"\"\"Called when constructing the globals dictionary used for\n+    evaluating string conditions in xfail/skipif markers.\n+\n+    This is useful when the condition for a marker requires\n+    objects that are expensive or impossible to obtain during\n+    collection time, which is required by normal boolean\n+    conditions.\n+\n+    .. versionadded:: 6.2\n+\n+    :param _pytest.config.Config config: The pytest config object.\n+    :returns: A dictionary of additional globals to add.\n+    \"\"\"\n+\n+\n # -------------------------------------------------------------------------\n # error handling and internal debugging hooks\n # -------------------------------------------------------------------------\ndiff --git a/src/_pytest/skipping.py b/src/_pytest/skipping.py\n--- a/src/_pytest/skipping.py\n+++ b/src/_pytest/skipping.py\n@@ -3,6 +3,7 @@\n import platform\n import sys\n import traceback\n+from collections.abc import Mapping\n from typing import Generator\n from typing import Optional\n from typing import Tuple\n@@ -98,6 +99,16 @@ def evaluate_condition(item: Item, mark: Mark, condition: object) -> Tuple[bool,\n             \"platform\": platform,\n             \"config\": item.config,\n         }\n+        for dictionary in reversed(\n+            item.ihook.pytest_markeval_namespace(config=item.config)\n+        ):\n+            if not isinstance(dictionary, Mapping):\n+                raise ValueError(\n+                    \"pytest_markeval_namespace() needs to return a dict, got {!r}\".format(\n+                        dictionary\n+                    )\n+                )\n+            globals_.update(dictionary)\n         if hasattr(item, \"obj\"):\n             globals_.update(item.obj.__globals__)  # type: ignore[attr-defined]\n         try:\n"}
{"instance_id": "pytest-dev__pytest-8641", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/logging.py b/src/_pytest/logging.py\n--- a/src/_pytest/logging.py\n+++ b/src/_pytest/logging.py\n@@ -59,7 +59,7 @@ class ColoredLevelFormatter(logging.Formatter):\n         logging.DEBUG: {\"purple\"},\n         logging.NOTSET: set(),\n     }\n-    LEVELNAME_FMT_REGEX = re.compile(r\"%\\(levelname\\)([+-.]?\\d*s)\")\n+    LEVELNAME_FMT_REGEX = re.compile(r\"%\\(levelname\\)([+-.]?\\d*(?:\\.\\d+)?s)\")\n \n     def __init__(self, terminalwriter: TerminalWriter, *args, **kwargs) -> None:\n         super().__init__(*args, **kwargs)\n"}
{"instance_id": "pytest-dev__pytest-9359", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/_code/source.py b/src/_pytest/_code/source.py\n--- a/src/_pytest/_code/source.py\n+++ b/src/_pytest/_code/source.py\n@@ -149,6 +149,11 @@ def get_statement_startend2(lineno: int, node: ast.AST) -> Tuple[int, Optional[i\n     values: List[int] = []\n     for x in ast.walk(node):\n         if isinstance(x, (ast.stmt, ast.ExceptHandler)):\n+            # Before Python 3.8, the lineno of a decorated class or function pointed at the decorator.\n+            # Since Python 3.8, the lineno points to the class/def, so need to include the decorators.\n+            if isinstance(x, (ast.ClassDef, ast.FunctionDef, ast.AsyncFunctionDef)):\n+                for d in x.decorator_list:\n+                    values.append(d.lineno - 1)\n             values.append(x.lineno - 1)\n             for name in (\"finalbody\", \"orelse\"):\n                 val: Optional[List[ast.stmt]] = getattr(x, name, None)\n"}
{"instance_id": "pytest-dev__pytest-9709", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/python_api.py b/src/_pytest/python_api.py\n--- a/src/_pytest/python_api.py\n+++ b/src/_pytest/python_api.py\n@@ -1,5 +1,6 @@\n import math\n import pprint\n+from collections.abc import Collection\n from collections.abc import Sized\n from decimal import Decimal\n from numbers import Complex\n@@ -8,7 +9,6 @@\n from typing import Callable\n from typing import cast\n from typing import Generic\n-from typing import Iterable\n from typing import List\n from typing import Mapping\n from typing import Optional\n@@ -306,12 +306,12 @@ def _check_type(self) -> None:\n                 raise TypeError(msg.format(key, value, pprint.pformat(self.expected)))\n \n \n-class ApproxSequencelike(ApproxBase):\n+class ApproxSequenceLike(ApproxBase):\n     \"\"\"Perform approximate comparisons where the expected value is a sequence of numbers.\"\"\"\n \n     def __repr__(self) -> str:\n         seq_type = type(self.expected)\n-        if seq_type not in (tuple, list, set):\n+        if seq_type not in (tuple, list):\n             seq_type = list\n         return \"approx({!r})\".format(\n             seq_type(self._approx_scalar(x) for x in self.expected)\n@@ -515,7 +515,7 @@ class ApproxDecimal(ApproxScalar):\n \n \n def approx(expected, rel=None, abs=None, nan_ok: bool = False) -> ApproxBase:\n-    \"\"\"Assert that two numbers (or two sets of numbers) are equal to each other\n+    \"\"\"Assert that two numbers (or two ordered sequences of numbers) are equal to each other\n     within some tolerance.\n \n     Due to the :std:doc:`tutorial/floatingpoint`, numbers that we\n@@ -547,16 +547,11 @@ def approx(expected, rel=None, abs=None, nan_ok: bool = False) -> ApproxBase:\n         >>> 0.1 + 0.2 == approx(0.3)\n         True\n \n-    The same syntax also works for sequences of numbers::\n+    The same syntax also works for ordered sequences of numbers::\n \n         >>> (0.1 + 0.2, 0.2 + 0.4) == approx((0.3, 0.6))\n         True\n \n-    Dictionary *values*::\n-\n-        >>> {'a': 0.1 + 0.2, 'b': 0.2 + 0.4} == approx({'a': 0.3, 'b': 0.6})\n-        True\n-\n     ``numpy`` arrays::\n \n         >>> import numpy as np                                                          # doctest: +SKIP\n@@ -569,6 +564,20 @@ def approx(expected, rel=None, abs=None, nan_ok: bool = False) -> ApproxBase:\n         >>> np.array([0.1, 0.2]) + np.array([0.2, 0.1]) == approx(0.3) # doctest: +SKIP\n         True\n \n+    Only ordered sequences are supported, because ``approx`` needs\n+    to infer the relative position of the sequences without ambiguity. This means\n+    ``sets`` and other unordered sequences are not supported.\n+\n+    Finally, dictionary *values* can also be compared::\n+\n+        >>> {'a': 0.1 + 0.2, 'b': 0.2 + 0.4} == approx({'a': 0.3, 'b': 0.6})\n+        True\n+\n+    The comparision will be true if both mappings have the same keys and their\n+    respective values match the expected tolerances.\n+\n+    **Tolerances**\n+\n     By default, ``approx`` considers numbers within a relative tolerance of\n     ``1e-6`` (i.e. one part in a million) of its expected value to be equal.\n     This treatment would lead to surprising results if the expected value was\n@@ -708,12 +717,19 @@ def approx(expected, rel=None, abs=None, nan_ok: bool = False) -> ApproxBase:\n         expected = _as_numpy_array(expected)\n         cls = ApproxNumpy\n     elif (\n-        isinstance(expected, Iterable)\n+        hasattr(expected, \"__getitem__\")\n         and isinstance(expected, Sized)\n         # Type ignored because the error is wrong -- not unreachable.\n         and not isinstance(expected, STRING_TYPES)  # type: ignore[unreachable]\n     ):\n-        cls = ApproxSequencelike\n+        cls = ApproxSequenceLike\n+    elif (\n+        isinstance(expected, Collection)\n+        # Type ignored because the error is wrong -- not unreachable.\n+        and not isinstance(expected, STRING_TYPES)  # type: ignore[unreachable]\n+    ):\n+        msg = f\"pytest.approx() only supports ordered sequences, but got: {repr(expected)}\"\n+        raise TypeError(msg)\n     else:\n         cls = ApproxScalar\n \n"}
{"instance_id": "pytest-dev__pytest-10758", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/assertion/rewrite.py b/src/_pytest/assertion/rewrite.py\n--- a/src/_pytest/assertion/rewrite.py\n+++ b/src/_pytest/assertion/rewrite.py\n@@ -44,9 +44,13 @@\n if TYPE_CHECKING:\n     from _pytest.assertion import AssertionState\n \n+if sys.version_info >= (3, 8):\n+    namedExpr = ast.NamedExpr\n+else:\n+    namedExpr = ast.Expr\n \n-assertstate_key = StashKey[\"AssertionState\"]()\n \n+assertstate_key = StashKey[\"AssertionState\"]()\n \n # pytest caches rewritten pycs in pycache dirs\n PYTEST_TAG = f\"{sys.implementation.cache_tag}-pytest-{version}\"\n@@ -635,8 +639,12 @@ class AssertionRewriter(ast.NodeVisitor):\n        .push_format_context() and .pop_format_context() which allows\n        to build another %-formatted string while already building one.\n \n-    This state is reset on every new assert statement visited and used\n-    by the other visitors.\n+    :variables_overwrite: A dict filled with references to variables\n+       that change value within an assert. This happens when a variable is\n+       reassigned with the walrus operator\n+\n+    This state, except the variables_overwrite,  is reset on every new assert\n+    statement visited and used by the other visitors.\n     \"\"\"\n \n     def __init__(\n@@ -652,6 +660,7 @@ def __init__(\n         else:\n             self.enable_assertion_pass_hook = False\n         self.source = source\n+        self.variables_overwrite: Dict[str, str] = {}\n \n     def run(self, mod: ast.Module) -> None:\n         \"\"\"Find all assert statements in *mod* and rewrite them.\"\"\"\n@@ -666,7 +675,7 @@ def run(self, mod: ast.Module) -> None:\n         if doc is not None and self.is_rewrite_disabled(doc):\n             return\n         pos = 0\n-        lineno = 1\n+        item = None\n         for item in mod.body:\n             if (\n                 expect_docstring\n@@ -937,6 +946,18 @@ def visit_Assert(self, assert_: ast.Assert) -> List[ast.stmt]:\n                 ast.copy_location(node, assert_)\n         return self.statements\n \n+    def visit_NamedExpr(self, name: namedExpr) -> Tuple[namedExpr, str]:\n+        # This method handles the 'walrus operator' repr of the target\n+        # name if it's a local variable or _should_repr_global_name()\n+        # thinks it's acceptable.\n+        locs = ast.Call(self.builtin(\"locals\"), [], [])\n+        target_id = name.target.id  # type: ignore[attr-defined]\n+        inlocs = ast.Compare(ast.Str(target_id), [ast.In()], [locs])\n+        dorepr = self.helper(\"_should_repr_global_name\", name)\n+        test = ast.BoolOp(ast.Or(), [inlocs, dorepr])\n+        expr = ast.IfExp(test, self.display(name), ast.Str(target_id))\n+        return name, self.explanation_param(expr)\n+\n     def visit_Name(self, name: ast.Name) -> Tuple[ast.Name, str]:\n         # Display the repr of the name if it's a local variable or\n         # _should_repr_global_name() thinks it's acceptable.\n@@ -963,6 +984,20 @@ def visit_BoolOp(self, boolop: ast.BoolOp) -> Tuple[ast.Name, str]:\n                 # cond is set in a prior loop iteration below\n                 self.expl_stmts.append(ast.If(cond, fail_inner, []))  # noqa\n                 self.expl_stmts = fail_inner\n+                # Check if the left operand is a namedExpr and the value has already been visited\n+                if (\n+                    isinstance(v, ast.Compare)\n+                    and isinstance(v.left, namedExpr)\n+                    and v.left.target.id\n+                    in [\n+                        ast_expr.id\n+                        for ast_expr in boolop.values[:i]\n+                        if hasattr(ast_expr, \"id\")\n+                    ]\n+                ):\n+                    pytest_temp = self.variable()\n+                    self.variables_overwrite[v.left.target.id] = pytest_temp\n+                    v.left.target.id = pytest_temp\n             self.push_format_context()\n             res, expl = self.visit(v)\n             body.append(ast.Assign([ast.Name(res_var, ast.Store())], res))\n@@ -1038,6 +1073,9 @@ def visit_Attribute(self, attr: ast.Attribute) -> Tuple[ast.Name, str]:\n \n     def visit_Compare(self, comp: ast.Compare) -> Tuple[ast.expr, str]:\n         self.push_format_context()\n+        # We first check if we have overwritten a variable in the previous assert\n+        if isinstance(comp.left, ast.Name) and comp.left.id in self.variables_overwrite:\n+            comp.left.id = self.variables_overwrite[comp.left.id]\n         left_res, left_expl = self.visit(comp.left)\n         if isinstance(comp.left, (ast.Compare, ast.BoolOp)):\n             left_expl = f\"({left_expl})\"\n@@ -1049,6 +1087,13 @@ def visit_Compare(self, comp: ast.Compare) -> Tuple[ast.expr, str]:\n         syms = []\n         results = [left_res]\n         for i, op, next_operand in it:\n+            if (\n+                isinstance(next_operand, namedExpr)\n+                and isinstance(left_res, ast.Name)\n+                and next_operand.target.id == left_res.id\n+            ):\n+                next_operand.target.id = self.variable()\n+                self.variables_overwrite[left_res.id] = next_operand.target.id\n             next_res, next_expl = self.visit(next_operand)\n             if isinstance(next_operand, (ast.Compare, ast.BoolOp)):\n                 next_expl = f\"({next_expl})\"\n@@ -1072,6 +1117,7 @@ def visit_Compare(self, comp: ast.Compare) -> Tuple[ast.expr, str]:\n             res: ast.expr = ast.BoolOp(ast.And(), load_names)\n         else:\n             res = load_names[0]\n+\n         return res, self.explanation_param(self.pop_format_context(expl_call))\n \n \n"}
{"instance_id": "pytest-dev__pytest-11125", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/config/__init__.py b/src/_pytest/config/__init__.py\n--- a/src/_pytest/config/__init__.py\n+++ b/src/_pytest/config/__init__.py\n@@ -527,9 +527,12 @@ def pytest_configure(self, config: \"Config\") -> None:\n     #\n     def _set_initial_conftests(\n         self,\n-        namespace: argparse.Namespace,\n+        args: Sequence[Union[str, Path]],\n+        pyargs: bool,\n+        noconftest: bool,\n         rootpath: Path,\n-        testpaths_ini: Sequence[str],\n+        confcutdir: Optional[Path],\n+        importmode: Union[ImportMode, str],\n     ) -> None:\n         \"\"\"Load initial conftest files given a preparsed \"namespace\".\n \n@@ -539,17 +542,12 @@ def _set_initial_conftests(\n         common options will not confuse our logic here.\n         \"\"\"\n         current = Path.cwd()\n-        self._confcutdir = (\n-            absolutepath(current / namespace.confcutdir)\n-            if namespace.confcutdir\n-            else None\n-        )\n-        self._noconftest = namespace.noconftest\n-        self._using_pyargs = namespace.pyargs\n-        testpaths = namespace.file_or_dir + testpaths_ini\n+        self._confcutdir = absolutepath(current / confcutdir) if confcutdir else None\n+        self._noconftest = noconftest\n+        self._using_pyargs = pyargs\n         foundanchor = False\n-        for testpath in testpaths:\n-            path = str(testpath)\n+        for intitial_path in args:\n+            path = str(intitial_path)\n             # remove node-id syntax\n             i = path.find(\"::\")\n             if i != -1:\n@@ -563,10 +561,10 @@ def _set_initial_conftests(\n             except OSError:  # pragma: no cover\n                 anchor_exists = False\n             if anchor_exists:\n-                self._try_load_conftest(anchor, namespace.importmode, rootpath)\n+                self._try_load_conftest(anchor, importmode, rootpath)\n                 foundanchor = True\n         if not foundanchor:\n-            self._try_load_conftest(current, namespace.importmode, rootpath)\n+            self._try_load_conftest(current, importmode, rootpath)\n \n     def _is_in_confcutdir(self, path: Path) -> bool:\n         \"\"\"Whether a path is within the confcutdir.\n@@ -1140,10 +1138,25 @@ def _processopt(self, opt: \"Argument\") -> None:\n \n     @hookimpl(trylast=True)\n     def pytest_load_initial_conftests(self, early_config: \"Config\") -> None:\n+        # We haven't fully parsed the command line arguments yet, so\n+        # early_config.args it not set yet. But we need it for\n+        # discovering the initial conftests. So \"pre-run\" the logic here.\n+        # It will be done for real in `parse()`.\n+        args, args_source = early_config._decide_args(\n+            args=early_config.known_args_namespace.file_or_dir,\n+            pyargs=early_config.known_args_namespace.pyargs,\n+            testpaths=early_config.getini(\"testpaths\"),\n+            invocation_dir=early_config.invocation_params.dir,\n+            rootpath=early_config.rootpath,\n+            warn=False,\n+        )\n         self.pluginmanager._set_initial_conftests(\n-            early_config.known_args_namespace,\n+            args=args,\n+            pyargs=early_config.known_args_namespace.pyargs,\n+            noconftest=early_config.known_args_namespace.noconftest,\n             rootpath=early_config.rootpath,\n-            testpaths_ini=self.getini(\"testpaths\"),\n+            confcutdir=early_config.known_args_namespace.confcutdir,\n+            importmode=early_config.known_args_namespace.importmode,\n         )\n \n     def _initini(self, args: Sequence[str]) -> None:\n@@ -1223,6 +1236,49 @@ def _validate_args(self, args: List[str], via: str) -> List[str]:\n \n         return args\n \n+    def _decide_args(\n+        self,\n+        *,\n+        args: List[str],\n+        pyargs: List[str],\n+        testpaths: List[str],\n+        invocation_dir: Path,\n+        rootpath: Path,\n+        warn: bool,\n+    ) -> Tuple[List[str], ArgsSource]:\n+        \"\"\"Decide the args (initial paths/nodeids) to use given the relevant inputs.\n+\n+        :param warn: Whether can issue warnings.\n+        \"\"\"\n+        if args:\n+            source = Config.ArgsSource.ARGS\n+            result = args\n+        else:\n+            if invocation_dir == rootpath:\n+                source = Config.ArgsSource.TESTPATHS\n+                if pyargs:\n+                    result = testpaths\n+                else:\n+                    result = []\n+                    for path in testpaths:\n+                        result.extend(sorted(glob.iglob(path, recursive=True)))\n+                    if testpaths and not result:\n+                        if warn:\n+                            warning_text = (\n+                                \"No files were found in testpaths; \"\n+                                \"consider removing or adjusting your testpaths configuration. \"\n+                                \"Searching recursively from the current directory instead.\"\n+                            )\n+                            self.issue_config_time_warning(\n+                                PytestConfigWarning(warning_text), stacklevel=3\n+                            )\n+            else:\n+                result = []\n+            if not result:\n+                source = Config.ArgsSource.INCOVATION_DIR\n+                result = [str(invocation_dir)]\n+        return result, source\n+\n     def _preparse(self, args: List[str], addopts: bool = True) -> None:\n         if addopts:\n             env_addopts = os.environ.get(\"PYTEST_ADDOPTS\", \"\")\n@@ -1371,34 +1427,17 @@ def parse(self, args: List[str], addopts: bool = True) -> None:\n         self.hook.pytest_cmdline_preparse(config=self, args=args)\n         self._parser.after_preparse = True  # type: ignore\n         try:\n-            source = Config.ArgsSource.ARGS\n             args = self._parser.parse_setoption(\n                 args, self.option, namespace=self.option\n             )\n-            if not args:\n-                if self.invocation_params.dir == self.rootpath:\n-                    source = Config.ArgsSource.TESTPATHS\n-                    testpaths: List[str] = self.getini(\"testpaths\")\n-                    if self.known_args_namespace.pyargs:\n-                        args = testpaths\n-                    else:\n-                        args = []\n-                        for path in testpaths:\n-                            args.extend(sorted(glob.iglob(path, recursive=True)))\n-                        if testpaths and not args:\n-                            warning_text = (\n-                                \"No files were found in testpaths; \"\n-                                \"consider removing or adjusting your testpaths configuration. \"\n-                                \"Searching recursively from the current directory instead.\"\n-                            )\n-                            self.issue_config_time_warning(\n-                                PytestConfigWarning(warning_text), stacklevel=3\n-                            )\n-                if not args:\n-                    source = Config.ArgsSource.INCOVATION_DIR\n-                    args = [str(self.invocation_params.dir)]\n-            self.args = args\n-            self.args_source = source\n+            self.args, self.args_source = self._decide_args(\n+                args=args,\n+                pyargs=self.known_args_namespace.pyargs,\n+                testpaths=self.getini(\"testpaths\"),\n+                invocation_dir=self.invocation_params.dir,\n+                rootpath=self.rootpath,\n+                warn=True,\n+            )\n         except PrintHelp:\n             pass\n \n"}
{"instance_id": "pytest-dev__pytest-11217", "model_name_or_path": "gold", "model_patch": "diff --git a/src/_pytest/fixtures.py b/src/_pytest/fixtures.py\n--- a/src/_pytest/fixtures.py\n+++ b/src/_pytest/fixtures.py\n@@ -1162,9 +1162,10 @@ def pytest_fixture_setup(\n     try:\n         result = call_fixture_func(fixturefunc, request, kwargs)\n     except TEST_OUTCOME as e:\n-        if isinstance(e, skip.Exception) and not fixturefunc.__name__.startswith(\n-            \"xunit_setup\"\n-        ):\n+        if isinstance(e, skip.Exception):\n+            # The test requested a fixture which caused a skip.\n+            # Don't show the fixture as the skip location, as then the user\n+            # wouldn't know which test skipped.\n             e._use_item_location = True\n         fixturedef.cached_result = (None, my_cache_key, e)\n         raise\n"}
{"instance_id": "scikit-learn__scikit-learn-12784", "model_name_or_path": "gold", "model_patch": "diff --git a/build_tools/generate_authors_table.py b/build_tools/generate_authors_table.py\n--- a/build_tools/generate_authors_table.py\n+++ b/build_tools/generate_authors_table.py\n@@ -97,7 +97,7 @@ def key(profile):\n contributors = get_contributors()\n \n print(\".. raw :: html\\n\")\n-print(\"    <!-- Generated by gen_authors.py -->\")\n+print(\"    <!-- Generated by generate_authors_table.py -->\")\n print(\"    <table>\")\n print(\"    <col style='width:%d%%' span='%d'>\"\n       % (int(100 / ROW_SIZE), ROW_SIZE))\ndiff --git a/examples/applications/plot_stock_market.py b/examples/applications/plot_stock_market.py\n--- a/examples/applications/plot_stock_market.py\n+++ b/examples/applications/plot_stock_market.py\n@@ -65,7 +65,6 @@\n # <AUTHOR> <EMAIL>\n #\n import numpy as np\n+import matplotlib\n import matplotlib.pyplot as plt\n+from distutils.version import LooseVersion\n from scipy.stats import norm\n from sklearn.neighbors import KernelDensity\n \n+# `normed` is being deprecated in favor of `density` in histograms\n+if LooseVersion(matplotlib.__version__) >= '2.1':\n+    density_param = {'density': True}\n+else:\n+    density_param = {'normed': True}\n \n #----------------------------------------------------------------------\n # Plot the progression of histograms to kernels\n@@ -47,11 +54,11 @@\n fig.subplots_adjust(hspace=0.05, wspace=0.05)\n \n # histogram 1\n-ax[0, 0].hist(X[:, 0], bins=bins, fc='#AAAAFF', normed=True)\n+ax[0, 0].hist(X[:, 0], bins=bins, fc='#AAAAFF', **density_param)\n ax[0, 0].text(-3.5, 0.31, \"Histogram\")\n \n # histogram 2\n-ax[0, 1].hist(X[:, 0], bins=bins + 0.75, fc='#AAAAFF', normed=True)\n+ax[0, 1].hist(X[:, 0], bins=bins + 0.75, fc='#AAAAFF', **density_param)\n ax[0, 1].text(-3.5, 0.31, \"Histogram, bins shifted\")\n \n # tophat KDE\ndiff --git a/examples/plot_anomaly_comparison.py b/examples/plot_anomaly_comparison.py\n--- a/examples/plot_anomaly_comparison.py\n+++ b/examples/plot_anomaly_comparison.py\n@@ -14,32 +14,34 @@\n except for Local Outlier Factor (LOF) as it has no predict method to be applied\n on new data when it is used for outlier detection.\n \n-The :class:`svm.OneClassSVM` is known to be sensitive to outliers and thus does\n-not perform very well for outlier detection. This estimator is best suited for\n-novelty detection when the training set is not contaminated by outliers.\n-That said, outlier detection in high-dimension, or without any assumptions on\n-the distribution of the inlying data is very challenging, and a One-class SVM\n-might give useful results in these situations depending on the value of its\n-hyperparameters.\n-\n-:class:`covariance.EllipticEnvelope` assumes the data is Gaussian and learns\n-an ellipse. It thus degrades when the data is not unimodal. Notice however\n-that this estimator is robust to outliers.\n-\n-:class:`ensemble.IsolationForest` and :class:`neighbors.LocalOutlierFactor`\n-seem to perform reasonably well for multi-modal data sets. The advantage of\n-:class:`neighbors.LocalOutlierFactor` over the other estimators is shown for\n-the third data set, where the two modes have different densities. This\n-advantage is explained by the local aspect of LOF, meaning that it only\n+The :class:`sklearn.svm.OneClassSVM` is known to be sensitive to outliers and\n+thus does not perform very well for outlier detection. This estimator is best\n+suited for novelty detection when the training set is not contaminated by\n+outliers. That said, outlier detection in high-dimension, or without any\n+assumptions on the distribution of the inlying data is very challenging, and a\n+One-class SVM might give useful results in these situations depending on the\n+value of its hyperparameters.\n+\n+:class:`sklearn.covariance.EllipticEnvelope` assumes the data is Gaussian and\n+learns an ellipse. It thus degrades when the data is not unimodal. Notice\n+however that this estimator is robust to outliers.\n+\n+:class:`sklearn.ensemble.IsolationForest` and\n+:class:`sklearn.neighbors.LocalOutlierFactor` seem to perform reasonably well\n+for multi-modal data sets. The advantage of\n+:class:`sklearn.neighbors.LocalOutlierFactor` over the other estimators is\n+shown for the third data set, where the two modes have different densities.\n+This advantage is explained by the local aspect of LOF, meaning that it only\n compares the score of abnormality of one sample with the scores of its\n neighbors.\n \n Finally, for the last data set, it is hard to say that one sample is more\n abnormal than another sample as they are uniformly distributed in a\n-hypercube. Except for the :class:`svm.OneClassSVM` which overfits a little, all\n-estimators present decent solutions for this situation. In such a case, it\n-would be wise to look more closely at the scores of abnormality of the samples\n-as a good estimator should assign similar scores to all the samples.\n+hypercube. Except for the :class:`sklearn.svm.OneClassSVM` which overfits a\n+little, all estimators present decent solutions for this situation. In such a\n+case, it would be wise to look more closely at the scores of abnormality of\n+the samples as a good estimator should assign similar scores to all the\n+samples.\n \n While these examples give some intuition about the algorithms, this\n intuition might not apply to very high dimensional data.\ndiff --git a/examples/plot_johnson_lindenstrauss_bound.py b/examples/plot_johnson_lindenstrauss_bound.py\n--- a/examples/plot_johnson_lindenstrauss_bound.py\n+++ b/examples/plot_johnson_lindenstrauss_bound.py\n@@ -94,13 +94,21 @@\n import sys\n from time import time\n import numpy as np\n+import matplotlib\n import matplotlib.pyplot as plt\n+from distutils.version import LooseVersion\n from sklearn.random_projection import johnson_lindenstrauss_min_dim\n from sklearn.random_projection import SparseRandomProjection\n from sklearn.datasets import fetch_20newsgroups_vectorized\n from sklearn.datasets import load_digits\n from sklearn.metrics.pairwise import euclidean_distances\n \n+# `normed` is being deprecated in favor of `density` in histograms\n+if LooseVersion(matplotlib.__version__) >= '2.1':\n+    density_param = {'density': True}\n+else:\n+    density_param = {'normed': True}\n+\n # Part 1: plot the theoretical dependency between n_components_min and\n # n_samples\n \n@@ -187,7 +195,7 @@\n           % (np.mean(rates), np.std(rates)))\n \n     plt.figure()\n-    plt.hist(rates, bins=50, normed=True, range=(0., 2.), edgecolor='k')\n+    plt.hist(rates, bins=50, range=(0., 2.), edgecolor='k', **density_param)\n     plt.xlabel(\"Squared distances rate: projected / original\")\n     plt.ylabel(\"Distribution of samples pairs\")\n     plt.title(\"Histogram of pairwise distance rates for n_components=%d\" %\ndiff --git a/examples/text/plot_document_classification_20newsgroups.py b/examples/text/plot_document_classification_20newsgroups.py\n--- a/examples/text/plot_document_classification_20newsgroups.py\n+++ b/examples/text/plot_document_classification_20newsgroups.py\n@@ -145,7 +145,7 @@ def size_mb(docs):\n     len(data_train.data), data_train_size_mb))\n print(\"%d documents - %0.3fMB (test set)\" % (\n     len(data_test.data), data_test_size_mb))\n-print(\"%d categories\" % len(categories))\n+print(\"%d categories\" % len(target_names))\n print()\n \n # split a training set and a test set\ndiff --git a/sklearn/compose/_column_transformer.py b/sklearn/compose/_column_transformer.py\n--- a/sklearn/compose/_column_transformer.py\n+++ b/sklearn/compose/_column_transformer.py\n@@ -692,7 +692,7 @@ def _validate_transformers(transformers):\n         return True\n \n     for t in transformers:\n-        if t in ('drop', 'passthrough'):\n+        if isinstance(t, six.string_types) and t in ('drop', 'passthrough'):\n             continue\n         if (not (hasattr(t, \"fit\") or hasattr(t, \"fit_transform\")) or not\n                 hasattr(t, \"transform\")):\ndiff --git a/sklearn/feature_extraction/text.py b/sklearn/feature_extraction/text.py\n--- a/sklearn/feature_extraction/text.py\n+++ b/sklearn/feature_extraction/text.py\n@@ -1356,8 +1356,10 @@ class TfidfVectorizer(CountVectorizer):\n         preprocessing and n-grams generation steps.\n         Only applies if ``analyzer == 'word'``.\n \n-    analyzer : string, {'word', 'char'} or callable\n+    analyzer : string, {'word', 'char', 'char_wb'} or callable\n         Whether the feature should be made of word or character n-grams.\n+        Option 'char_wb' creates character n-grams only from text inside\n+        word boundaries; n-grams at the edges of words are padded with space.\n \n         If a callable is passed it is used to extract the sequence of features\n         out of the raw, unprocessed input.\ndiff --git a/sklearn/linear_model/logistic.py b/sklearn/linear_model/logistic.py\n--- a/sklearn/linear_model/logistic.py\n+++ b/sklearn/linear_model/logistic.py\n@@ -1142,6 +1142,9 @@ class LogisticRegression(BaseEstimator, LinearClassifierMixin,\n     Attributes\n     ----------\n \n+    classes_ : array, shape (n_classes, )\n+        A list of class labels known to the classifier.\n+\n     coef_ : array, shape (1, n_features) or (n_classes, n_features)\n         Coefficient of the features in the decision function.\n \n@@ -1594,6 +1597,9 @@ class LogisticRegressionCV(LogisticRegression, BaseEstimator,\n \n     Attributes\n     ----------\n+    classes_ : array, shape (n_classes, )\n+        A list of class labels known to the classifier.\n+\n     coef_ : array, shape (1, n_features) or (n_classes, n_features)\n         Coefficient of the features in the decision function.\n \ndiff --git a/sklearn/metrics/pairwise.py b/sklearn/metrics/pairwise.py\n--- a/sklearn/metrics/pairwise.py\n+++ b/sklearn/metrics/pairwise.py\n@@ -1136,6 +1136,24 @@ def _check_chunk_size(reduced, chunk_size):\n                           chunk_size))\n \n \n+def _precompute_metric_params(X, Y, metric=None, **kwds):\n+    \"\"\"Precompute data-derived metric parameters if not provided\n+    \"\"\"\n+    if metric == \"seuclidean\" and 'V' not in kwds:\n+        if X is Y:\n+            V = np.var(X, axis=0, ddof=1)\n+        else:\n+            V = np.var(np.vstack([X, Y]), axis=0, ddof=1)\n+        return {'V': V}\n+    if metric == \"mahalanobis\" and 'VI' not in kwds:\n+        if X is Y:\n+            VI = np.linalg.inv(np.cov(X.T)).T\n+        else:\n+            VI = np.linalg.inv(np.cov(np.vstack([X, Y]).T)).T\n+        return {'VI': VI}\n+    return {}\n+\n+\n def pairwise_distances_chunked(X, Y=None, reduce_func=None,\n                                metric='euclidean', n_jobs=None,\n                                working_memory=None, **kwds):\n@@ -1271,6 +1289,10 @@ def pairwise_distances_chunked(X, Y=None, reduce_func=None,\n                                         working_memory=working_memory)\n         slices = gen_batches(n_samples_X, chunk_n_rows)\n \n+    # precompute data-derived metric params\n+    params = _precompute_metric_params(X, Y, metric=metric, **kwds)\n+    kwds.update(**params)\n+\n     for sl in slices:\n         if sl.start == 0 and sl.stop == n_samples_X:\n             X_chunk = X  # enable optimised paths for X is Y\n@@ -1398,6 +1420,10 @@ def pairwise_distances(X, Y=None, metric=\"euclidean\", n_jobs=None, **kwds):\n         dtype = bool if metric in PAIRWISE_BOOLEAN_FUNCTIONS else None\n         X, Y = check_pairwise_arrays(X, Y, dtype=dtype)\n \n+        # precompute data-derived metric params\n+        params = _precompute_metric_params(X, Y, metric=metric, **kwds)\n+        kwds.update(**params)\n+\n         if effective_n_jobs(n_jobs) == 1 and X is Y:\n             return distance.squareform(distance.pdist(X, metric=metric,\n                                                       **kwds))\ndiff --git a/sklearn/metrics/regression.py b/sklearn/metrics/regression.py\n--- a/sklearn/metrics/regression.py\n+++ b/sklearn/metrics/regression.py\n@@ -514,19 +514,19 @@ def r2_score(y_true, y_pred, sample_weight=None,\n     0.948...\n     >>> y_true = [[0.5, 1], [-1, 1], [7, -6]]\n     >>> y_pred = [[0, 2], [-1, 2], [8, -5]]\n-    >>> r2_score(y_true, y_pred, multioutput='variance_weighted')\n-    ... # doctest: +ELLIPSIS\n+    >>> r2_score(y_true, y_pred,\n+    ...          multioutput='variance_weighted') # doctest: +ELLIPSIS\n     0.938...\n-    >>> y_true = [1,2,3]\n-    >>> y_pred = [1,2,3]\n+    >>> y_true = [1, 2, 3]\n+    >>> y_pred = [1, 2, 3]\n     >>> r2_score(y_true, y_pred)\n     1.0\n-    >>> y_true = [1,2,3]\n-    >>> y_pred = [2,2,2]\n+    >>> y_true = [1, 2, 3]\n+    >>> y_pred = [2, 2, 2]\n     >>> r2_score(y_true, y_pred)\n     0.0\n-    >>> y_true = [1,2,3]\n-    >>> y_pred = [3,2,1]\n+    >>> y_true = [1, 2, 3]\n+    >>> y_pred = [3, 2, 1]\n     >>> r2_score(y_true, y_pred)\n     -3.0\n     \"\"\"\ndiff --git a/sklearn/neural_network/multilayer_perceptron.py b/sklearn/neural_network/multilayer_perceptron.py\n--- a/sklearn/neural_network/multilayer_perceptron.py\n+++ b/sklearn/neural_network/multilayer_perceptron.py\n@@ -619,7 +619,7 @@ def fit(self, X, y):\n \n     @property\n     def partial_fit(self):\n-        \"\"\"Fit the model to data matrix X and target y.\n+        \"\"\"Update the model with a single iteration over the given data.\n \n         Parameters\n         ----------\n@@ -978,7 +978,7 @@ def fit(self, X, y):\n \n     @property\n     def partial_fit(self):\n-        \"\"\"Fit the model to data matrix X and target y.\n+        \"\"\"Update the model with a single iteration over the given data.\n \n         Parameters\n         ----------\ndiff --git a/sklearn/preprocessing/data.py b/sklearn/preprocessing/data.py\n--- a/sklearn/preprocessing/data.py\n+++ b/sklearn/preprocessing/data.py\n@@ -1988,7 +1988,7 @@ class QuantileTransformer(BaseEstimator, TransformerMixin):\n     (marginal) outliers: this is therefore a robust preprocessing scheme.\n \n     The transformation is applied on each feature independently.\n-    The cumulative density function of a feature is used to project the\n+    The cumulative distribution function of a feature is used to project the\n     original values. Features values of new/unseen data that fall below\n     or above the fitted range will be mapped to the bounds of the output\n     distribution. Note that this transform is non-linear. It may distort linear\n@@ -2001,7 +2001,7 @@ class QuantileTransformer(BaseEstimator, TransformerMixin):\n     ----------\n     n_quantiles : int, optional (default=1000)\n         Number of quantiles to be computed. It corresponds to the number\n-        of landmarks used to discretize the cumulative density function.\n+        of landmarks used to discretize the cumulative distribution function.\n \n     output_distribution : str, optional (default='uniform')\n         Marginal distribution for the transformed data. The choices are\n@@ -2378,7 +2378,7 @@ def quantile_transform(X, axis=0, n_quantiles=1000,\n     (marginal) outliers: this is therefore a robust preprocessing scheme.\n \n     The transformation is applied on each feature independently.\n-    The cumulative density function of a feature is used to project the\n+    The cumulative distribution function of a feature is used to project the\n     original values. Features values of new/unseen data that fall below\n     or above the fitted range will be mapped to the bounds of the output\n     distribution. Note that this transform is non-linear. It may distort linear\n@@ -2398,7 +2398,7 @@ def quantile_transform(X, axis=0, n_quantiles=1000,\n \n     n_quantiles : int, optional (default=1000)\n         Number of quantiles to be computed. It corresponds to the number\n-        of landmarks used to discretize the cumulative density function.\n+        of landmarks used to discretize the cumulative distribution function.\n \n     output_distribution : str, optional (default='uniform')\n         Marginal distribution for the transformed data. The choices are\ndiff --git a/sklearn/svm/base.py b/sklearn/svm/base.py\n--- a/sklearn/svm/base.py\n+++ b/sklearn/svm/base.py\n@@ -384,7 +384,7 @@ def _compute_kernel(self, X):\n         return X\n \n     def _decision_function(self, X):\n-        \"\"\"Distance of the samples X to the separating hyperplane.\n+        \"\"\"Evaluates the decision function for the samples in X.\n \n         Parameters\n         ----------\n@@ -529,7 +529,7 @@ def _validate_targets(self, y):\n         return np.asarray(y, dtype=np.float64, order='C')\n \n     def decision_function(self, X):\n-        \"\"\"Distance of the samples X to the separating hyperplane.\n+        \"\"\"Evaluates the decision function for the samples in X.\n \n         Parameters\n         ----------\n@@ -541,7 +541,16 @@ def decision_function(self, X):\n             Returns the decision function of the sample for each class\n             in the model.\n             If decision_function_shape='ovr', the shape is (n_samples,\n-            n_classes)\n+            n_classes).\n+\n+        Notes\n+        ------\n+        If decision_function_shape='ovo', the function values are proportional\n+        to the distance of the samples X to the separating hyperplane. If the\n+        exact distances are required, divide the function values by the norm of\n+        the weight vector (``coef_``). See also `this question\n+        <https://stats.stackexchange.com/questions/14876/\n+        interpreting-distance-from-hyperplane-in-svm>`_ for further details.\n         \"\"\"\n         dec = self._decision_function(X)\n         if self.decision_function_shape == 'ovr' and len(self.classes_) > 2:\ndiff --git a/sklearn/utils/testing.py b/sklearn/utils/testing.py\n--- a/sklearn/utils/testing.py\n+++ b/sklearn/utils/testing.py\n@@ -42,12 +42,21 @@\n except NameError:\n     WindowsError = None\n \n+from numpy.testing import assert_allclose\n+from numpy.testing import assert_almost_equal\n+from numpy.testing import assert_approx_equal\n+from numpy.testing import assert_array_equal\n+from numpy.testing import assert_array_almost_equal\n+from numpy.testing import assert_array_less\n+import numpy as np\n+\n import sklearn\n-from sklearn.base import BaseEstimator\n+from sklearn.base import (BaseEstimator, ClassifierMixin, ClusterMixin,\n+                          RegressorMixin, TransformerMixin)\n+from sklearn.utils import deprecated, IS_PYPY, _IS_32BIT\n from sklearn.utils._joblib import joblib\n+from sklearn.utils._unittest_backport import TestCase\n from sklearn.utils.fixes import signature\n-from sklearn.utils import deprecated, IS_PYPY, _IS_32BIT\n-\n \n additional_names_in_all = []\n try:\n@@ -73,24 +82,13 @@\n except ImportError:\n     pass\n \n-from numpy.testing import assert_almost_equal\n-from numpy.testing import assert_array_equal\n-from numpy.testing import assert_array_almost_equal\n-from numpy.testing import assert_array_less\n-from numpy.testing import assert_approx_equal\n-import numpy as np\n-\n-from sklearn.base import (ClassifierMixin, RegressorMixin, TransformerMixin,\n-                          ClusterMixin)\n-from sklearn.utils._unittest_backport import TestCase\n-\n __all__ = [\"assert_equal\", \"assert_not_equal\", \"assert_raises\",\n            \"assert_raises_regexp\", \"assert_true\",\n            \"assert_false\", \"assert_almost_equal\", \"assert_array_equal\",\n            \"assert_array_almost_equal\", \"assert_array_less\",\n            \"assert_less\", \"assert_less_equal\",\n            \"assert_greater\", \"assert_greater_equal\",\n-           \"assert_approx_equal\", \"SkipTest\"]\n+           \"assert_approx_equal\", \"assert_allclose\", \"SkipTest\"]\n __all__.extend(additional_names_in_all)\n \n _dummy = TestCase('__init__')\n@@ -379,11 +377,6 @@ def __exit__(self, *exc_info):\n         clean_warning_registry()\n \n \n-assert_less = _dummy.assertLess\n-assert_greater = _dummy.assertGreater\n-\n-assert_allclose = np.testing.assert_allclose\n-\n def assert_raise_message(exceptions, message, function, *args, **kwargs):\n     \"\"\"Helper function to test the message raised in an exception.\n \ndiff --git a/sklearn/utils/validation.py b/sklearn/utils/validation.py\n--- a/sklearn/utils/validation.py\n+++ b/sklearn/utils/validation.py\n@@ -477,7 +477,7 @@ def check_array(array, accept_sparse=False, accept_large_sparse=True,\n     # check if the object contains several dtypes (typically a pandas\n     # DataFrame), and store them. If not, store None.\n     dtypes_orig = None\n-    if hasattr(array, \"dtypes\") and len(array.dtypes):\n+    if hasattr(array, \"dtypes\") and hasattr(array.dtypes, '__array__'):\n         dtypes_orig = np.array(array.dtypes)\n \n     if dtype_numeric:\n"}
{"instance_id": "scikit-learn__scikit-learn-13915", "model_name_or_path": "gold", "model_patch": "diff --git a/sklearn/__init__.py b/sklearn/__init__.py\n--- a/sklearn/__init__.py\n+++ b/sklearn/__init__.py\n@@ -45,7 +45,7 @@\n # Dev branch marker is: 'X.Y.dev' or 'X.Y.devN' where N is an integer.\n # 'X.Y.dev0' is the canonical version of 'X.Y.dev'\n #\n-__version__ = '0.21.1'\n+__version__ = '0.21.2'\n \n \n # On OSX, we can get a runtime error due to multiple OpenMP libraries loaded\ndiff --git a/sklearn/cross_decomposition/pls_.py b/sklearn/cross_decomposition/pls_.py\n--- a/sklearn/cross_decomposition/pls_.py\n+++ b/sklearn/cross_decomposition/pls_.py\n@@ -285,6 +285,7 @@ def fit(self, X, Y):\n         self.n_iter_ = []\n \n         # NIPALS algo: outer loop, over components\n+        Y_eps = np.finfo(Yk.dtype).eps\n         for k in range(self.n_components):\n             if np.all(np.dot(Yk.T, Yk) < np.finfo(np.double).eps):\n                 # Yk constant\n@@ -293,6 +294,10 @@ def fit(self, X, Y):\n             # 1) weights estimation (inner loop)\n             # -----------------------------------\n             if self.algorithm == \"nipals\":\n+                # Replace columns that are all close to zero with zeros\n+                Yk_mask = np.all(np.abs(Yk) < 10 * Y_eps, axis=0)\n+                Yk[:, Yk_mask] = 0.0\n+\n                 x_weights, y_weights, n_iter_ = \\\n                     _nipals_twoblocks_inner_loop(\n                         X=Xk, Y=Yk, mode=self.mode, max_iter=self.max_iter,\ndiff --git a/sklearn/experimental/enable_iterative_imputer.py b/sklearn/experimental/enable_iterative_imputer.py\n--- a/sklearn/experimental/enable_iterative_imputer.py\n+++ b/sklearn/experimental/enable_iterative_imputer.py\n@@ -1,6 +1,6 @@\n \"\"\"Enables IterativeImputer\n \n-The API and results of this estimators might change without any deprecation\n+The API and results of this estimator might change without any deprecation\n cycle.\n \n Importing this file dynamically sets :class:`sklearn.impute.IterativeImputer`\ndiff --git a/sklearn/metrics/classification.py b/sklearn/metrics/classification.py\n--- a/sklearn/metrics/classification.py\n+++ b/sklearn/metrics/classification.py\n@@ -1066,7 +1066,7 @@ def fbeta_score(y_true, y_pred, beta, labels=None, pos_label=1,\n     The F-beta score is the weighted harmonic mean of precision and recall,\n     reaching its optimal value at 1 and its worst value at 0.\n \n-    The `beta` parameter determines the weight of precision in the combined\n+    The `beta` parameter determines the weight of recall in the combined\n     score. ``beta < 1`` lends more weight to precision, while ``beta > 1``\n     favors recall (``beta -> 0`` considers only precision, ``beta -> inf``\n     only recall).\ndiff --git a/sklearn/metrics/pairwise.py b/sklearn/metrics/pairwise.py\n--- a/sklearn/metrics/pairwise.py\n+++ b/sklearn/metrics/pairwise.py\n@@ -283,7 +283,7 @@ def euclidean_distances(X, Y=None, Y_norm_squared=None, squared=False,\n     return distances if squared else np.sqrt(distances, out=distances)\n \n \n-def _euclidean_distances_upcast(X, XX=None, Y=None, YY=None):\n+def _euclidean_distances_upcast(X, XX=None, Y=None, YY=None, batch_size=None):\n     \"\"\"Euclidean distances between X and Y\n \n     Assumes X and Y have float32 dtype.\n@@ -298,28 +298,28 @@ def _euclidean_distances_upcast(X, XX=None, Y=None, YY=None):\n \n     distances = np.empty((n_samples_X, n_samples_Y), dtype=np.float32)\n \n-    x_density = X.nnz / np.prod(X.shape) if issparse(X) else 1\n-    y_density = Y.nnz / np.prod(Y.shape) if issparse(Y) else 1\n-\n-    # Allow 10% more memory than X, Y and the distance matrix take (at least\n-    # 10MiB)\n-    maxmem = max(\n-        ((x_density * n_samples_X + y_density * n_samples_Y) * n_features\n-         + (x_density * n_samples_X * y_density * n_samples_Y)) / 10,\n-        10 * 2 ** 17)\n-\n-    # The increase amount of memory in 8-byte blocks is:\n-    # - x_density * batch_size * n_features (copy of chunk of X)\n-    # - y_density * batch_size * n_features (copy of chunk of Y)\n-    # - batch_size * batch_size (chunk of distance matrix)\n-    # Hence x\u00b2 + (xd+yd)kx = M, where x=batch_size, k=n_features, M=maxmem\n-    #                                 xd=x_density and yd=y_density\n-    tmp = (x_density + y_density) * n_features\n-    batch_size = (-tmp + np.sqrt(tmp ** 2 + 4 * maxmem)) / 2\n-    batch_size = max(int(batch_size), 1)\n-\n-    x_batches = gen_batches(X.shape[0], batch_size)\n-    y_batches = gen_batches(Y.shape[0], batch_size)\n+    if batch_size is None:\n+        x_density = X.nnz / np.prod(X.shape) if issparse(X) else 1\n+        y_density = Y.nnz / np.prod(Y.shape) if issparse(Y) else 1\n+\n+        # Allow 10% more memory than X, Y and the distance matrix take (at\n+        # least 10MiB)\n+        maxmem = max(\n+            ((x_density * n_samples_X + y_density * n_samples_Y) * n_features\n+             + (x_density * n_samples_X * y_density * n_samples_Y)) / 10,\n+            10 * 2 ** 17)\n+\n+        # The increase amount of memory in 8-byte blocks is:\n+        # - x_density * batch_size * n_features (copy of chunk of X)\n+        # - y_density * batch_size * n_features (copy of chunk of Y)\n+        # - batch_size * batch_size (chunk of distance matrix)\n+        # Hence x\u00b2 + (xd+yd)kx = M, where x=batch_size, k=n_features, M=maxmem\n+        #                                 xd=x_density and yd=y_density\n+        tmp = (x_density + y_density) * n_features\n+        batch_size = (-tmp + np.sqrt(tmp ** 2 + 4 * maxmem)) / 2\n+        batch_size = max(int(batch_size), 1)\n+\n+    x_batches = gen_batches(n_samples_X, batch_size)\n \n     for i, x_slice in enumerate(x_batches):\n         X_chunk = X[x_slice].astype(np.float64)\n@@ -328,6 +328,8 @@ def _euclidean_distances_upcast(X, XX=None, Y=None, YY=None):\n         else:\n             XX_chunk = XX[x_slice]\n \n+        y_batches = gen_batches(n_samples_Y, batch_size)\n+\n         for j, y_slice in enumerate(y_batches):\n             if X is Y and j < i:\n                 # when X is Y the distance matrix is symmetric so we only need\ndiff --git a/sklearn/preprocessing/_encoders.py b/sklearn/preprocessing/_encoders.py\n--- a/sklearn/preprocessing/_encoders.py\n+++ b/sklearn/preprocessing/_encoders.py\n@@ -367,7 +367,8 @@ def _handle_deprecations(self, X):\n             msg = (\n                 \"Passing 'n_values' is deprecated in version 0.20 and will be \"\n                 \"removed in 0.22. You can use the 'categories' keyword \"\n-                \"instead. 'n_values=n' corresponds to 'categories=[range(n)]'.\"\n+                \"instead. 'n_values=n' corresponds to \"\n+                \"'categories=[range(n)] * n_features'.\"\n             )\n             warnings.warn(msg, DeprecationWarning)\n             self._legacy_mode = True\n@@ -847,6 +848,8 @@ def get_feature_names(self, input_features=None):\n         for i in range(len(cats)):\n             names = [\n                 input_features[i] + '_' + str(t) for t in cats[i]]\n+            if self.drop is not None:\n+                names.pop(self.drop_idx_[i])\n             feature_names.extend(names)\n \n         return np.array(feature_names, dtype=object)\ndiff --git a/sklearn/utils/sparsefuncs.py b/sklearn/utils/sparsefuncs.py\n--- a/sklearn/utils/sparsefuncs.py\n+++ b/sklearn/utils/sparsefuncs.py\n@@ -341,6 +341,11 @@ def inplace_swap_column(X, m, n):\n \n def _minor_reduce(X, ufunc):\n     major_index = np.flatnonzero(np.diff(X.indptr))\n+\n+    # reduceat tries casts X.indptr to intp, which errors\n+    # if it is int64 on a 32 bit system.\n+    # Reinitializing prevents this where possible, see #13737\n+    X = type(X)((X.data, X.indices, X.indptr), shape=X.shape)\n     value = ufunc.reduceat(X.data, X.indptr[major_index])\n     return major_index, value\n \n"}
{"instance_id": "scikit-learn__scikit-learn-15625", "model_name_or_path": "gold", "model_patch": "diff --git a/sklearn/metrics/_classification.py b/sklearn/metrics/_classification.py\n--- a/sklearn/metrics/_classification.py\n+++ b/sklearn/metrics/_classification.py\n@@ -193,8 +193,9 @@ def accuracy_score(y_true, y_pred, normalize=True, sample_weight=None):\n     return _weighted_sum(score, sample_weight, normalize)\n \n \n-def confusion_matrix(y_true, y_pred, labels=None, sample_weight=None):\n-    \"\"\"Compute confusion matrix to evaluate the accuracy of a classification\n+def confusion_matrix(y_true, y_pred, labels=None, sample_weight=None,\n+                     normalize=None):\n+    \"\"\"Compute confusion matrix to evaluate the accuracy of a classification.\n \n     By definition a confusion matrix :math:`C` is such that :math:`C_{i, j}`\n     is equal to the number of observations known to be in group :math:`i` and\n@@ -208,25 +209,30 @@ def confusion_matrix(y_true, y_pred, labels=None, sample_weight=None):\n \n     Parameters\n     ----------\n-    y_true : array, shape = [n_samples]\n+    y_true : array-like of shape (n_samples,)\n         Ground truth (correct) target values.\n \n-    y_pred : array, shape = [n_samples]\n+    y_pred : array-like of shape (n_samples,)\n         Estimated targets as returned by a classifier.\n \n-    labels : array, shape = [n_classes], optional\n+    labels : array-like of shape (n_classes), default=None\n         List of labels to index the matrix. This may be used to reorder\n         or select a subset of labels.\n-        If none is given, those that appear at least once\n+        If ``None`` is given, those that appear at least once\n         in ``y_true`` or ``y_pred`` are used in sorted order.\n \n     sample_weight : array-like of shape (n_samples,), default=None\n         Sample weights.\n \n+    normalize : {'true', 'pred', 'all'}, default=None\n+        Normalizes confusion matrix over the true (rows), predicted (columns)\n+        conditions or all the population. If None, confusion matrix will not be\n+        normalized.\n+\n     Returns\n     -------\n     C : ndarray of shape (n_classes, n_classes)\n-        Confusion matrix\n+        Confusion matrix.\n \n     References\n     ----------\n@@ -296,11 +302,20 @@ def confusion_matrix(y_true, y_pred, labels=None, sample_weight=None):\n     else:\n         dtype = np.float64\n \n-    CM = coo_matrix((sample_weight, (y_true, y_pred)),\n+    cm = coo_matrix((sample_weight, (y_true, y_pred)),\n                     shape=(n_labels, n_labels), dtype=dtype,\n                     ).toarray()\n \n-    return CM\n+    with np.errstate(all='ignore'):\n+        if normalize == 'true':\n+            cm = cm / cm.sum(axis=1, keepdims=True)\n+        elif normalize == 'pred':\n+            cm = cm / cm.sum(axis=0, keepdims=True)\n+        elif normalize == 'all':\n+            cm = cm / cm.sum()\n+        cm = np.nan_to_num(cm)\n+\n+    return cm\n \n \n def multilabel_confusion_matrix(y_true, y_pred, sample_weight=None,\ndiff --git a/sklearn/metrics/_plot/confusion_matrix.py b/sklearn/metrics/_plot/confusion_matrix.py\n--- a/sklearn/metrics/_plot/confusion_matrix.py\n+++ b/sklearn/metrics/_plot/confusion_matrix.py\n@@ -155,7 +155,7 @@ def plot_confusion_matrix(estimator, X, y_true, sample_weight=None,\n         Includes values in confusion matrix.\n \n     normalize : {'true', 'pred', 'all'}, default=None\n-        Normalizes confusion matrix over the true (rows), predicited (columns)\n+        Normalizes confusion matrix over the true (rows), predicted (columns)\n         conditions or all the population. If None, confusion matrix will not be\n         normalized.\n \n@@ -190,14 +190,7 @@ def plot_confusion_matrix(estimator, X, y_true, sample_weight=None,\n \n     y_pred = estimator.predict(X)\n     cm = confusion_matrix(y_true, y_pred, sample_weight=sample_weight,\n-                          labels=labels)\n-\n-    if normalize == 'true':\n-        cm = cm / cm.sum(axis=1, keepdims=True)\n-    elif normalize == 'pred':\n-        cm = cm / cm.sum(axis=0, keepdims=True)\n-    elif normalize == 'all':\n-        cm = cm / cm.sum()\n+                          labels=labels, normalize=normalize)\n \n     if display_labels is None:\n         if labels is None:\n"}
{"instance_id": "scikit-learn__scikit-learn-26400", "model_name_or_path": "gold", "model_patch": "diff --git a/sklearn/preprocessing/_data.py b/sklearn/preprocessing/_data.py\n--- a/sklearn/preprocessing/_data.py\n+++ b/sklearn/preprocessing/_data.py\n@@ -3311,9 +3311,13 @@ def _box_cox_optimize(self, x):\n \n         We here use scipy builtins which uses the brent optimizer.\n         \"\"\"\n+        mask = np.isnan(x)\n+        if np.all(mask):\n+            raise ValueError(\"Column must not be all nan.\")\n+\n         # the computation of lambda is influenced by NaNs so we need to\n         # get rid of them\n-        _, lmbda = stats.boxcox(x[~np.isnan(x)], lmbda=None)\n+        _, lmbda = stats.boxcox(x[~mask], lmbda=None)\n \n         return lmbda\n \n"}
{"instance_id": "scikit-learn__scikit-learn-26644", "model_name_or_path": "gold", "model_patch": "diff --git a/sklearn/inspection/_plot/partial_dependence.py b/sklearn/inspection/_plot/partial_dependence.py\n--- a/sklearn/inspection/_plot/partial_dependence.py\n+++ b/sklearn/inspection/_plot/partial_dependence.py\n@@ -86,8 +86,9 @@ class PartialDependenceDisplay:\n \n         .. note::\n            The fast ``method='recursion'`` option is only available for\n-           ``kind='average'``. Plotting individual dependencies requires using\n-           the slower ``method='brute'`` option.\n+           `kind='average'` and `sample_weights=None`. Computing individual\n+           dependencies and doing weighted averages requires using the slower\n+           `method='brute'`.\n \n         .. versionadded:: 0.24\n            Add `kind` parameter with `'average'`, `'individual'`, and `'both'`\n@@ -247,6 +248,7 @@ def from_estimator(\n         X,\n         features,\n         *,\n+        sample_weight=None,\n         categorical_features=None,\n         feature_names=None,\n         target=None,\n@@ -337,6 +339,14 @@ def from_estimator(\n             with `kind='average'`). Each tuple must be of size 2.\n             If any entry is a string, then it must be in ``feature_names``.\n \n+        sample_weight : array-like of shape (n_samples,), default=None\n+            Sample weights are used to calculate weighted means when averaging the\n+            model output. If `None`, then samples are equally weighted. If\n+            `sample_weight` is not `None`, then `method` will be set to `'brute'`.\n+            Note that `sample_weight` is ignored for `kind='individual'`.\n+\n+            .. versionadded:: 1.3\n+\n         categorical_features : array-like of shape (n_features,) or shape \\\n                 (n_categorical_features,), dtype={bool, int, str}, default=None\n             Indicates the categorical features.\n@@ -409,7 +419,8 @@ def from_estimator(\n               computationally intensive.\n \n             - `'auto'`: the `'recursion'` is used for estimators that support it,\n-              and `'brute'` is used otherwise.\n+              and `'brute'` is used otherwise. If `sample_weight` is not `None`,\n+              then `'brute'` is used regardless of the estimator.\n \n             Please see :ref:`this note <pdp_method_differences>` for\n             differences between the `'brute'` and `'recursion'` method.\n@@ -464,9 +475,10 @@ def from_estimator(\n             - ``kind='average'`` results in the traditional PD plot;\n             - ``kind='individual'`` results in the ICE plot.\n \n-           Note that the fast ``method='recursion'`` option is only available for\n-           ``kind='average'``. Plotting individual dependencies requires using the\n-           slower ``method='brute'`` option.\n+           Note that the fast `method='recursion'` option is only available for\n+           `kind='average'` and `sample_weights=None`. Computing individual\n+           dependencies and doing weighted averages requires using the slower\n+           `method='brute'`.\n \n         centered : bool, default=False\n             If `True`, the ICE and PD lines will start at the origin of the\n@@ -693,6 +705,7 @@ def from_estimator(\n                 estimator,\n                 X,\n                 fxs,\n+                sample_weight=sample_weight,\n                 feature_names=feature_names,\n                 categorical_features=categorical_features,\n                 response_method=response_method,\n"}
{"instance_id": "sphinx-doc__sphinx-7501", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/domains/std.py b/sphinx/domains/std.py\n--- a/sphinx/domains/std.py\n+++ b/sphinx/domains/std.py\n@@ -789,6 +789,8 @@ def resolve_xref(self, env: \"BuildEnvironment\", fromdocname: str, builder: \"Buil\n                           RemovedInSphinx40Warning)\n             domain = env.get_domain('citation')\n             return domain.resolve_xref(env, fromdocname, builder, typ, target, node, contnode)\n+        elif typ == 'term':\n+            resolver = self._resolve_term_xref\n         else:\n             resolver = self._resolve_obj_xref\n \n@@ -923,6 +925,28 @@ def _resolve_option_xref(self, env: \"BuildEnvironment\", fromdocname: str,\n         return make_refnode(builder, fromdocname, docname,\n                             labelid, contnode)\n \n+    def _resolve_term_xref(self, env: \"BuildEnvironment\", fromdocname: str,\n+                           builder: \"Builder\", typ: str, target: str,\n+                           node: pending_xref, contnode: Element) -> Element:\n+        result = self._resolve_obj_xref(env, fromdocname, builder, typ,\n+                                        target, node, contnode)\n+        if result:\n+            return result\n+        else:\n+            for objtype, term in self.objects:\n+                if objtype == 'term' and term.lower() == target.lower():\n+                    docname, labelid = self.objects[objtype, term]\n+                    logger.warning(__('term %s not found in case sensitive match.'\n+                                      'made a reference to %s instead.'),\n+                                   target, term, location=node, type='ref', subtype='term')\n+                    break\n+            else:\n+                docname, labelid = '', ''\n+            if not docname:\n+                return None\n+            return make_refnode(builder, fromdocname, docname,\n+                                labelid, contnode)\n+\n     def _resolve_obj_xref(self, env: \"BuildEnvironment\", fromdocname: str,\n                           builder: \"Builder\", typ: str, target: str,\n                           node: pending_xref, contnode: Element) -> Element:\n"}
{"instance_id": "sphinx-doc__sphinx-7859", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/ext/autodoc/typehints.py b/sphinx/ext/autodoc/typehints.py\n--- a/sphinx/ext/autodoc/typehints.py\n+++ b/sphinx/ext/autodoc/typehints.py\n@@ -46,11 +46,16 @@ def merge_typehints(app: Sphinx, domain: str, objtype: str, contentnode: Element\n     if objtype == 'class' and app.config.autoclass_content not in ('init', 'both'):\n         return\n \n-    signature = cast(addnodes.desc_signature, contentnode.parent[0])\n-    if signature['module']:\n-        fullname = '.'.join([signature['module'], signature['fullname']])\n-    else:\n-        fullname = signature['fullname']\n+    try:\n+        signature = cast(addnodes.desc_signature, contentnode.parent[0])\n+        if signature['module']:\n+            fullname = '.'.join([signature['module'], signature['fullname']])\n+        else:\n+            fullname = signature['fullname']\n+    except KeyError:\n+        # signature node does not have valid context info for the target object\n+        return\n+\n     annotations = app.env.temp_data.get('annotations', {})\n     if annotations.get(fullname, {}):\n         field_lists = [n for n in contentnode if isinstance(n, nodes.field_list)]\n"}
{"instance_id": "sphinx-doc__sphinx-8058", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/builders/gettext.py b/sphinx/builders/gettext.py\n--- a/sphinx/builders/gettext.py\n+++ b/sphinx/builders/gettext.py\n@@ -316,7 +316,7 @@ def finish(self) -> None:\n def setup(app: Sphinx) -> Dict[str, Any]:\n     app.add_builder(MessageCatalogBuilder)\n \n-    app.add_config_value('gettext_compact', True, 'gettext')\n+    app.add_config_value('gettext_compact', True, 'gettext', Any)\n     app.add_config_value('gettext_location', True, 'gettext')\n     app.add_config_value('gettext_uuid', False, 'gettext')\n     app.add_config_value('gettext_auto_build', True, 'env')\ndiff --git a/sphinx/util/i18n.py b/sphinx/util/i18n.py\n--- a/sphinx/util/i18n.py\n+++ b/sphinx/util/i18n.py\n@@ -14,7 +14,7 @@\n from collections import namedtuple\n from datetime import datetime, timezone\n from os import path\n-from typing import Callable, Generator, List, Set, Tuple\n+from typing import Callable, Generator, List, Set, Tuple, Union\n \n import babel.dates\n from babel.messages.mofile import write_mo\n@@ -128,8 +128,10 @@ def find_catalog(docname: str, compaction: bool) -> str:\n     return ret\n \n \n-def docname_to_domain(docname: str, compation: bool) -> str:\n+def docname_to_domain(docname: str, compation: Union[bool, str]) -> str:\n     \"\"\"Convert docname to domain for catalogs.\"\"\"\n+    if isinstance(compation, str):\n+        return compation\n     if compation:\n         return docname.split(SEP, 1)[0]\n     else:\n"}
{"instance_id": "sphinx-doc__sphinx-8282", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/ext/autodoc/__init__.py b/sphinx/ext/autodoc/__init__.py\n--- a/sphinx/ext/autodoc/__init__.py\n+++ b/sphinx/ext/autodoc/__init__.py\n@@ -1240,7 +1240,9 @@ def add_directive_header(self, sig: str) -> None:\n \n     def format_signature(self, **kwargs: Any) -> str:\n         sigs = []\n-        if self.analyzer and '.'.join(self.objpath) in self.analyzer.overloads:\n+        if (self.analyzer and\n+                '.'.join(self.objpath) in self.analyzer.overloads and\n+                self.env.config.autodoc_typehints == 'signature'):\n             # Use signatures for overloaded functions instead of the implementation function.\n             overloaded = True\n         else:\n@@ -1474,7 +1476,7 @@ def format_signature(self, **kwargs: Any) -> str:\n         sigs = []\n \n         overloads = self.get_overloaded_signatures()\n-        if overloads:\n+        if overloads and self.env.config.autodoc_typehints == 'signature':\n             # Use signatures for overloaded methods instead of the implementation method.\n             method = safe_getattr(self._signature_class, self._signature_method_name, None)\n             __globals__ = safe_getattr(method, '__globals__', {})\n@@ -1882,7 +1884,9 @@ def document_members(self, all_members: bool = False) -> None:\n \n     def format_signature(self, **kwargs: Any) -> str:\n         sigs = []\n-        if self.analyzer and '.'.join(self.objpath) in self.analyzer.overloads:\n+        if (self.analyzer and\n+                '.'.join(self.objpath) in self.analyzer.overloads and\n+                self.env.config.autodoc_typehints == 'signature'):\n             # Use signatures for overloaded methods instead of the implementation method.\n             overloaded = True\n         else:\n"}
{"instance_id": "sphinx-doc__sphinx-8611", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/ext/autodoc/__init__.py b/sphinx/ext/autodoc/__init__.py\n--- a/sphinx/ext/autodoc/__init__.py\n+++ b/sphinx/ext/autodoc/__init__.py\n@@ -2092,18 +2092,36 @@ class NonDataDescriptorMixin(DataDocumenterMixinBase):\n               and :value: header will be suppressed unexpectedly.\n     \"\"\"\n \n+    def import_object(self, raiseerror: bool = False) -> bool:\n+        ret = super().import_object(raiseerror)  # type: ignore\n+        if ret and not inspect.isattributedescriptor(self.object):\n+            self.non_data_descriptor = True\n+        else:\n+            self.non_data_descriptor = False\n+\n+        return ret\n+\n     def should_suppress_value_header(self) -> bool:\n-        return (inspect.isattributedescriptor(self.object) or\n+        return (not getattr(self, 'non_data_descriptor', False) or\n                 super().should_suppress_directive_header())\n \n     def get_doc(self, encoding: str = None, ignore: int = None) -> List[List[str]]:\n-        if not inspect.isattributedescriptor(self.object):\n+        if getattr(self, 'non_data_descriptor', False):\n             # the docstring of non datadescriptor is very probably the wrong thing\n             # to display\n             return []\n         else:\n             return super().get_doc(encoding, ignore)  # type: ignore\n \n+    def add_content(self, more_content: Optional[StringList], no_docstring: bool = False\n+                    ) -> None:\n+        if getattr(self, 'non_data_descriptor', False):\n+            # the docstring of non datadescriptor is very probably the wrong thing\n+            # to display\n+            no_docstring = True\n+\n+        super().add_content(more_content, no_docstring=no_docstring)  # type: ignore\n+\n \n class SlotsMixin(DataDocumenterMixinBase):\n     \"\"\"\n"}
{"instance_id": "sphinx-doc__sphinx-8801", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/ext/autodoc/importer.py b/sphinx/ext/autodoc/importer.py\n--- a/sphinx/ext/autodoc/importer.py\n+++ b/sphinx/ext/autodoc/importer.py\n@@ -294,24 +294,35 @@ def get_class_members(subject: Any, objpath: List[str], attrgetter: Callable\n \n     try:\n         for cls in getmro(subject):\n+            try:\n+                modname = safe_getattr(cls, '__module__')\n+                qualname = safe_getattr(cls, '__qualname__')\n+                analyzer = ModuleAnalyzer.for_module(modname)\n+                analyzer.analyze()\n+            except AttributeError:\n+                qualname = None\n+                analyzer = None\n+            except PycodeError:\n+                analyzer = None\n+\n             # annotation only member (ex. attr: int)\n             for name in getannotations(cls):\n                 name = unmangle(cls, name)\n                 if name and name not in members:\n-                    members[name] = ObjectMember(name, INSTANCEATTR, class_=cls)\n+                    if analyzer and (qualname, name) in analyzer.attr_docs:\n+                        docstring = '\\n'.join(analyzer.attr_docs[qualname, name])\n+                    else:\n+                        docstring = None\n+\n+                    members[name] = ObjectMember(name, INSTANCEATTR, class_=cls,\n+                                                 docstring=docstring)\n \n             # append instance attributes (cf. self.attr1) if analyzer knows\n-            try:\n-                modname = safe_getattr(cls, '__module__')\n-                qualname = safe_getattr(cls, '__qualname__')\n-                analyzer = ModuleAnalyzer.for_module(modname)\n-                analyzer.analyze()\n+            if analyzer:\n                 for (ns, name), docstring in analyzer.attr_docs.items():\n                     if ns == qualname and name not in members:\n                         members[name] = ObjectMember(name, INSTANCEATTR, class_=cls,\n                                                      docstring='\\n'.join(docstring))\n-            except (AttributeError, PycodeError):\n-                pass\n     except AttributeError:\n         pass\n \n"}
{"instance_id": "sphinx-doc__sphinx-9246", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/transforms/post_transforms/__init__.py b/sphinx/transforms/post_transforms/__init__.py\n--- a/sphinx/transforms/post_transforms/__init__.py\n+++ b/sphinx/transforms/post_transforms/__init__.py\n@@ -8,10 +8,10 @@\n     :license: BSD, see LICENSE for details.\n \"\"\"\n \n-from typing import Any, Dict, List, Optional, Tuple, Type, cast\n+from typing import Any, Dict, List, Optional, Sequence, Tuple, Type, cast\n \n from docutils import nodes\n-from docutils.nodes import Element\n+from docutils.nodes import Element, Node\n \n from sphinx import addnodes\n from sphinx.addnodes import pending_xref\n@@ -26,10 +26,6 @@\n \n logger = logging.getLogger(__name__)\n \n-if False:\n-    # For type annotation\n-    from docutils.nodes import Node\n-\n \n class SphinxPostTransform(SphinxTransform):\n     \"\"\"A base class of post-transforms.\n@@ -71,7 +67,12 @@ class ReferencesResolver(SphinxPostTransform):\n \n     def run(self, **kwargs: Any) -> None:\n         for node in self.document.traverse(addnodes.pending_xref):\n-            contnode = cast(nodes.TextElement, node[0].deepcopy())\n+            content = self.find_pending_xref_condition(node, (\"resolved\", \"*\"))\n+            if content:\n+                contnode = cast(Element, content[0].deepcopy())\n+            else:\n+                contnode = cast(Element, node[0].deepcopy())\n+\n             newnode = None\n \n             typ = node['reftype']\n@@ -108,9 +109,9 @@ def run(self, **kwargs: Any) -> None:\n             else:\n                 newnodes = [contnode]\n                 if newnode is None and isinstance(node[0], addnodes.pending_xref_condition):\n-                    matched = find_pending_xref_condition(node, \"*\")\n+                    matched = self.find_pending_xref_condition(node, (\"*\",))\n                     if matched:\n-                        newnodes = matched.children\n+                        newnodes = matched\n                     else:\n                         logger.warning(__('Could not determine the fallback text for the '\n                                           'cross-reference. Might be a bug.'), location=node)\n@@ -193,6 +194,15 @@ def warn_missing_reference(self, refdoc: str, typ: str, target: str,\n             msg = __('%r reference target not found: %s') % (typ, target)\n         logger.warning(msg, location=node, type='ref', subtype=typ)\n \n+    def find_pending_xref_condition(self, node: pending_xref, conditions: Sequence[str]\n+                                    ) -> Optional[List[Node]]:\n+        for condition in conditions:\n+            matched = find_pending_xref_condition(node, condition)\n+            if matched:\n+                return matched.children\n+        else:\n+            return None\n+\n \n class OnlyNodeTransform(SphinxPostTransform):\n     default_priority = 50\n"}
{"instance_id": "sphinx-doc__sphinx-9467", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/builders/linkcheck.py b/sphinx/builders/linkcheck.py\n--- a/sphinx/builders/linkcheck.py\n+++ b/sphinx/builders/linkcheck.py\n@@ -714,7 +714,10 @@ def setup(app: Sphinx) -> Dict[str, Any]:\n     app.add_event('linkcheck-process-uri')\n \n     app.connect('config-inited', compile_linkcheck_allowed_redirects, priority=800)\n-    app.connect('linkcheck-process-uri', rewrite_github_anchor)\n+\n+    # FIXME: Disable URL rewrite handler for github.com temporarily.\n+    # ref: https://github.com/sphinx-doc/sphinx/issues/9435\n+    # app.connect('linkcheck-process-uri', rewrite_github_anchor)\n \n     return {\n         'version': 'builtin',\n"}
{"instance_id": "sphinx-doc__sphinx-9602", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/domains/python.py b/sphinx/domains/python.py\n--- a/sphinx/domains/python.py\n+++ b/sphinx/domains/python.py\n@@ -123,7 +123,7 @@ def unparse(node: ast.AST) -> List[Node]:\n             if node.value is Ellipsis:\n                 return [addnodes.desc_sig_punctuation('', \"...\")]\n             else:\n-                return [nodes.Text(node.value)]\n+                return [nodes.Text(repr(node.value))]\n         elif isinstance(node, ast.Expr):\n             return unparse(node.value)\n         elif isinstance(node, ast.Index):\n@@ -149,6 +149,12 @@ def unparse(node: ast.AST) -> List[Node]:\n             result.append(addnodes.desc_sig_punctuation('', '['))\n             result.extend(unparse(node.slice))\n             result.append(addnodes.desc_sig_punctuation('', ']'))\n+\n+            # Wrap the Text nodes inside brackets by literal node if the subscript is a Literal\n+            if result[0] in ('Literal', 'typing.Literal'):\n+                for i, subnode in enumerate(result[1:], start=1):\n+                    if isinstance(subnode, nodes.Text):\n+                        result[i] = nodes.literal('', '', subnode)\n             return result\n         elif isinstance(node, ast.Tuple):\n             if node.elts:\n@@ -179,7 +185,9 @@ def unparse(node: ast.AST) -> List[Node]:\n         tree = ast_parse(annotation)\n         result = unparse(tree)\n         for i, node in enumerate(result):\n-            if isinstance(node, nodes.Text) and node.strip():\n+            if isinstance(node, nodes.literal):\n+                result[i] = node[0]\n+            elif isinstance(node, nodes.Text) and node.strip():\n                 result[i] = type_to_xref(str(node), env)\n         return result\n     except SyntaxError:\n"}
{"instance_id": "sphinx-doc__sphinx-9829", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/ext/mathjax.py b/sphinx/ext/mathjax.py\n--- a/sphinx/ext/mathjax.py\n+++ b/sphinx/ext/mathjax.py\n@@ -81,7 +81,7 @@ def install_mathjax(app: Sphinx, pagename: str, templatename: str, context: Dict\n     domain = cast(MathDomain, app.env.get_domain('math'))\n     if app.registry.html_assets_policy == 'always' or domain.has_equations(pagename):\n         # Enable mathjax only if equations exists\n-        options = {'async': 'async'}\n+        options = {'defer': 'defer'}\n         if app.config.mathjax_options:\n             options.update(app.config.mathjax_options)\n         app.add_js_file(app.config.mathjax_path, **options)  # type: ignore\n"}
{"instance_id": "sphinx-doc__sphinx-10097", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/domains/std.py b/sphinx/domains/std.py\n--- a/sphinx/domains/std.py\n+++ b/sphinx/domains/std.py\n@@ -242,7 +242,7 @@ def add_target_and_index(self, firstname: str, sig: str, signode: desc_signature\n             descr = _('%s command line option') % currprogram\n         else:\n             descr = _('command line option')\n-        for option in sig.split(', '):\n+        for option in signode.get('allnames', []):\n             entry = '; '.join([descr, option])\n             self.indexnode['entries'].append(('pair', entry, signode['ids'][0], '', None))\n \n"}
{"instance_id": "sphinx-doc__sphinx-10504", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/builders/html/transforms.py b/sphinx/builders/html/transforms.py\n--- a/sphinx/builders/html/transforms.py\n+++ b/sphinx/builders/html/transforms.py\n@@ -40,7 +40,9 @@ class KeyboardTransform(SphinxPostTransform):\n \n     def run(self, **kwargs: Any) -> None:\n         matcher = NodeMatcher(nodes.literal, classes=[\"kbd\"])\n-        for node in self.document.findall(matcher):  # type: nodes.literal\n+        # this list must be pre-created as during iteration new nodes\n+        # are added which match the condition in the NodeMatcher.\n+        for node in list(self.document.findall(matcher)):  # type: nodes.literal\n             parts = self.pattern.split(node[-1].astext())\n             if len(parts) == 1 or self.is_multiwords_key(parts):\n                 continue\n"}
{"instance_id": "sphinx-doc__sphinx-10551", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/pycode/ast.py b/sphinx/pycode/ast.py\n--- a/sphinx/pycode/ast.py\n+++ b/sphinx/pycode/ast.py\n@@ -141,6 +141,9 @@ def visit_Attribute(self, node: ast.Attribute) -> str:\n         return \"%s.%s\" % (self.visit(node.value), node.attr)\n \n     def visit_BinOp(self, node: ast.BinOp) -> str:\n+        # Special case ``**`` to not have surrounding spaces.\n+        if isinstance(node.op, ast.Pow):\n+            return \"\".join(map(self.visit, (node.left, node.op, node.right)))\n         return \" \".join(self.visit(e) for e in [node.left, node.op, node.right])\n \n     def visit_BoolOp(self, node: ast.BoolOp) -> str:\n@@ -202,7 +205,11 @@ def is_simple_tuple(value: ast.AST) -> bool:\n             return \"%s[%s]\" % (self.visit(node.value), self.visit(node.slice))\n \n     def visit_UnaryOp(self, node: ast.UnaryOp) -> str:\n-        return \"%s %s\" % (self.visit(node.op), self.visit(node.operand))\n+        # UnaryOp is one of {UAdd, USub, Invert, Not}, which refer to ``+x``,\n+        # ``-x``, ``~x``, and ``not x``. Only Not needs a space.\n+        if isinstance(node.op, ast.Not):\n+            return \"%s %s\" % (self.visit(node.op), self.visit(node.operand))\n+        return \"%s%s\" % (self.visit(node.op), self.visit(node.operand))\n \n     def visit_Tuple(self, node: ast.Tuple) -> str:\n         if len(node.elts) == 0:\n"}
{"instance_id": "sphinx-doc__sphinx-10819", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/search/__init__.py b/sphinx/search/__init__.py\n--- a/sphinx/search/__init__.py\n+++ b/sphinx/search/__init__.py\n@@ -14,6 +14,7 @@\n from sphinx import addnodes, package_dir\n from sphinx.deprecation import RemovedInSphinx70Warning\n from sphinx.environment import BuildEnvironment\n+from sphinx.util import split_into\n \n \n class SearchLanguage:\n@@ -242,6 +243,7 @@ def __init__(self, env: BuildEnvironment, lang: str, options: Dict, scoring: str\n         # stemmed words in titles -> set(docname)\n         self._title_mapping: Dict[str, Set[str]] = {}\n         self._all_titles: Dict[str, List[Tuple[str, str]]] = {}  # docname -> all titles\n+        self._index_entries: Dict[str, List[Tuple[str, str, str]]] = {}  # docname -> index entry\n         self._stem_cache: Dict[str, str] = {}       # word -> stemmed word\n         self._objtypes: Dict[Tuple[str, str], int] = {}     # objtype -> index\n         # objtype index -> (domain, type, objname (localized))\n@@ -380,10 +382,15 @@ def freeze(self) -> Dict[str, Any]:\n             for title, titleid in titlelist:\n                 alltitles.setdefault(title, []).append((fn2index[docname],  titleid))\n \n+        index_entries: Dict[str, List[Tuple[int, str]]] = {}\n+        for docname, entries in self._index_entries.items():\n+            for entry, entry_id, main_entry in entries:\n+                index_entries.setdefault(entry.lower(), []).append((fn2index[docname],  entry_id))\n+\n         return dict(docnames=docnames, filenames=filenames, titles=titles, terms=terms,\n                     objects=objects, objtypes=objtypes, objnames=objnames,\n                     titleterms=title_terms, envversion=self.env.version,\n-                    alltitles=alltitles)\n+                    alltitles=alltitles, indexentries=index_entries)\n \n     def label(self) -> str:\n         return \"%s (code: %s)\" % (self.lang.language_name, self.lang.lang)\n@@ -441,6 +448,38 @@ def stem(word: str) -> str:\n             if _filter(stemmed_word) and not already_indexed:\n                 self._mapping.setdefault(stemmed_word, set()).add(docname)\n \n+        # find explicit entries within index directives\n+        _index_entries: Set[Tuple[str, str, str]] = set()\n+        for node in doctree.findall(addnodes.index):\n+            for entry_type, value, tid, main, *index_key in node['entries']:\n+                tid = tid or ''\n+                try:\n+                    if entry_type == 'single':\n+                        try:\n+                            entry, subentry = split_into(2, 'single', value)\n+                        except ValueError:\n+                            entry, = split_into(1, 'single', value)\n+                            subentry = ''\n+                        _index_entries.add((entry, tid, main))\n+                        if subentry:\n+                            _index_entries.add((subentry, tid, main))\n+                    elif entry_type == 'pair':\n+                        first, second = split_into(2, 'pair', value)\n+                        _index_entries.add((first, tid, main))\n+                        _index_entries.add((second, tid, main))\n+                    elif entry_type == 'triple':\n+                        first, second, third = split_into(3, 'triple', value)\n+                        _index_entries.add((first, tid, main))\n+                        _index_entries.add((second, tid, main))\n+                        _index_entries.add((third, tid, main))\n+                    elif entry_type in {'see', 'seealso'}:\n+                        first, second = split_into(2, 'see', value)\n+                        _index_entries.add((first, tid, main))\n+                except ValueError:\n+                    pass\n+\n+        self._index_entries[docname] = sorted(_index_entries)\n+\n     def context_for_searchtool(self) -> Dict[str, Any]:\n         if self.lang.js_splitter_code:\n             js_splitter_code = self.lang.js_splitter_code\n"}
{"instance_id": "sphinx-doc__sphinx-10137", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/ext/extlinks.py b/sphinx/ext/extlinks.py\n--- a/sphinx/ext/extlinks.py\n+++ b/sphinx/ext/extlinks.py\n@@ -72,7 +72,11 @@ def check_uri(self, refnode: nodes.reference) -> None:\n                 uri_pattern = re.compile(re.escape(base_uri).replace('%s', '(?P<value>.+)'))\n \n             match = uri_pattern.match(uri)\n-            if match and match.groupdict().get('value'):\n+            if (\n+                match and\n+                match.groupdict().get('value') and\n+                '/' not in match.groupdict()['value']\n+            ):\n                 # build a replacement suggestion\n                 msg = __('hardcoded link %r could be replaced by an extlink '\n                          '(try using %r instead)')\n"}
{"instance_id": "sphinx-doc__sphinx-11266", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/writers/latex.py b/sphinx/writers/latex.py\n--- a/sphinx/writers/latex.py\n+++ b/sphinx/writers/latex.py\n@@ -820,7 +820,7 @@ def depart_desc_annotation(self, node: Element) -> None:\n \n     def visit_seealso(self, node: Element) -> None:\n         self.body.append(BLANKLINE)\n-        self.body.append(r'\\begin{sphinxseealso}{%s}' % admonitionlabels['seealso'] + CR)\n+        self.body.append(r'\\begin{sphinxseealso}{%s:}' % admonitionlabels['seealso'] + CR)\n \n     def depart_seealso(self, node: Element) -> None:\n         self.body.append(BLANKLINE)\n"}
{"instance_id": "sphinx-doc__sphinx-11544", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/builders/linkcheck.py b/sphinx/builders/linkcheck.py\n--- a/sphinx/builders/linkcheck.py\n+++ b/sphinx/builders/linkcheck.py\n@@ -406,7 +406,8 @@ def _check_uri(self, uri: str, hyperlink: Hyperlink) -> tuple[str, str, int]:\n                     _user_agent=self.user_agent,\n                     _tls_info=(self.tls_verify, self.tls_cacerts),\n                 ) as response:\n-                    if response.ok and anchor and not contains_anchor(response, anchor):\n+                    if (self.check_anchors and response.ok and anchor\n+                            and not contains_anchor(response, anchor)):\n                         raise Exception(__(f'Anchor {anchor!r} not found'))\n \n                 # Copy data we need from the (closed) response\n"}
{"instance_id": "sphinx-doc__sphinx-11550", "model_name_or_path": "gold", "model_patch": "diff --git a/sphinx/ext/autodoc/preserve_defaults.py b/sphinx/ext/autodoc/preserve_defaults.py\n--- a/sphinx/ext/autodoc/preserve_defaults.py\n+++ b/sphinx/ext/autodoc/preserve_defaults.py\n@@ -8,17 +8,23 @@\n \n import ast\n import inspect\n-from typing import TYPE_CHECKING, Any\n+import types\n+import warnings\n+from typing import TYPE_CHECKING\n \n import sphinx\n+from sphinx.deprecation import RemovedInSphinx90Warning\n from sphinx.locale import __\n from sphinx.pycode.ast import unparse as ast_unparse\n from sphinx.util import logging\n \n if TYPE_CHECKING:\n+    from typing import Any\n+\n     from sphinx.application import Sphinx\n \n logger = logging.getLogger(__name__)\n+_LAMBDA_NAME = (lambda: None).__name__\n \n \n class DefaultValue:\n@@ -31,12 +37,19 @@ def __repr__(self) -> str:\n \n def get_function_def(obj: Any) -> ast.FunctionDef | None:\n     \"\"\"Get FunctionDef object from living object.\n+\n     This tries to parse original code for living object and returns\n     AST node for given *obj*.\n     \"\"\"\n+    warnings.warn('sphinx.ext.autodoc.preserve_defaults.get_function_def is'\n+                  ' deprecated and scheduled for removal in Sphinx 9.'\n+                  ' Use sphinx.ext.autodoc.preserve_defaults._get_arguments() to'\n+                  ' extract AST arguments objects from a lambda or regular'\n+                  ' function.', RemovedInSphinx90Warning, stacklevel=2)\n+\n     try:\n         source = inspect.getsource(obj)\n-        if source.startswith((' ', r'\\t')):\n+        if source.startswith((' ', '\\t')):\n             # subject is placed inside class or block.  To read its docstring,\n             # this adds if-block before the declaration.\n             module = ast.parse('if True:\\n' + source)\n@@ -48,6 +61,53 @@ def get_function_def(obj: Any) -> ast.FunctionDef | None:\n         return None\n \n \n+def _get_arguments(obj: Any, /) -> ast.arguments | None:\n+    \"\"\"Parse 'ast.arguments' from an object.\n+\n+    This tries to parse the original code for an object and returns\n+    an 'ast.arguments' node.\n+    \"\"\"\n+    try:\n+        source = inspect.getsource(obj)\n+        if source.startswith((' ', '\\t')):\n+            # 'obj' is in some indented block.\n+            module = ast.parse('if True:\\n' + source)\n+            subject = module.body[0].body[0]  # type: ignore[attr-defined]\n+        else:\n+            module = ast.parse(source)\n+            subject = module.body[0]\n+    except (OSError, TypeError):\n+        # bail; failed to load source for 'obj'.\n+        return None\n+    except SyntaxError:\n+        if _is_lambda(obj):\n+            # Most likely a multi-line arising from detecting a lambda, e.g.:\n+            #\n+            # class Egg:\n+            #     x = property(\n+            #         lambda self: 1, doc=\"...\")\n+            return None\n+\n+        # Other syntax errors that are not due to the fact that we are\n+        # documenting a lambda function are propagated\n+        # (in particular if a lambda is renamed by the user).\n+        raise\n+\n+    return _get_arguments_inner(subject)\n+\n+\n+def _is_lambda(x, /):\n+    return isinstance(x, types.LambdaType) and x.__name__ == _LAMBDA_NAME\n+\n+\n+def _get_arguments_inner(x: Any, /) -> ast.arguments | None:\n+    if isinstance(x, (ast.AsyncFunctionDef, ast.FunctionDef, ast.Lambda)):\n+        return x.args\n+    if isinstance(x, (ast.Assign, ast.AnnAssign)):\n+        return _get_arguments_inner(x.value)\n+    return None\n+\n+\n def get_default_value(lines: list[str], position: ast.AST) -> str | None:\n     try:\n         if position.lineno == position.end_lineno:\n@@ -67,18 +127,24 @@ def update_defvalue(app: Sphinx, obj: Any, bound_method: bool) -> None:\n \n     try:\n         lines = inspect.getsource(obj).splitlines()\n-        if lines[0].startswith((' ', r'\\t')):\n-            lines.insert(0, '')  # insert a dummy line to follow what get_function_def() does.\n+        if lines[0].startswith((' ', '\\t')):\n+            # insert a dummy line to follow what _get_arguments() does.\n+            lines.insert(0, '')\n     except (OSError, TypeError):\n         lines = []\n \n     try:\n-        function = get_function_def(obj)\n-        assert function is not None  # for mypy\n-        if function.args.defaults or function.args.kw_defaults:\n+        args = _get_arguments(obj)\n+        if args is None:\n+            # If the object is a built-in, we won't be always able to recover\n+            # the function definition and its arguments. This happens if *obj*\n+            # is the `__init__` method generated automatically for dataclasses.\n+            return\n+\n+        if args.defaults or args.kw_defaults:\n             sig = inspect.signature(obj)\n-            defaults = list(function.args.defaults)\n-            kw_defaults = list(function.args.kw_defaults)\n+            defaults = list(args.defaults)\n+            kw_defaults = list(args.kw_defaults)\n             parameters = list(sig.parameters.values())\n             for i, param in enumerate(parameters):\n                 if param.default is param.empty:\n"}
{"instance_id": "sympy__sympy-12812", "model_name_or_path": "gold", "model_patch": "diff --git a/sympy/diffgeom/diffgeom.py b/sympy/diffgeom/diffgeom.py\n--- a/sympy/diffgeom/diffgeom.py\n+++ b/sympy/diffgeom/diffgeom.py\n@@ -12,6 +12,7 @@\n from sympy.core.compatibility import reduce\n from sympy.combinatorics import Permutation\n \n+\n # TODO you are a bit excessive in the use of Dummies\n # TODO dummy point, literal field\n # TODO too often one needs to call doit or simplify on the output, check the\n@@ -574,6 +575,9 @@ def __call__(self, scalar_field):\n         if covariant_order(scalar_field) or contravariant_order(scalar_field):\n             raise ValueError('Only scalar fields can be supplied as arguments to vector fields.')\n \n+        if scalar_field is None:\n+            return self\n+\n         base_scalars = list(scalar_field.atoms(BaseScalarField))\n \n         # First step: e_x(x+r**2) -> e_x(x) + 2*r*e_x(r)\n@@ -789,10 +793,10 @@ class TensorProduct(Expr):\n     \"\"\"Tensor product of forms.\n \n     The tensor product permits the creation of multilinear functionals (i.e.\n-    higher order tensors) out of lower order forms (e.g. 1-forms). However, the\n-    higher tensors thus created lack the interesting features provided by the\n-    other type of product, the wedge product, namely they are not antisymmetric\n-    and hence are not form fields.\n+    higher order tensors) out of lower order fields (e.g. 1-forms and vector\n+    fields). However, the higher tensors thus created lack the interesting\n+    features provided by the other type of product, the wedge product, namely\n+    they are not antisymmetric and hence are not form fields.\n \n     Examples\n     ========\n@@ -810,6 +814,11 @@ class TensorProduct(Expr):\n     0\n     >>> TensorProduct(R2.dx, R2.x*R2.dy)(R2.x*R2.e_x, R2.e_y)\n     x**2\n+    >>> TensorProduct(R2.e_x, R2.e_y)(R2.x**2, R2.y**2)\n+    4*x*y\n+    >>> TensorProduct(R2.e_y, R2.dx)(R2.y)\n+    dx\n+\n \n     You can nest tensor products.\n \n@@ -833,14 +842,12 @@ class TensorProduct(Expr):\n \n     \"\"\"\n     def __new__(cls, *args):\n-        if any(contravariant_order(a) for a in args):\n-            raise ValueError('A vector field was supplied as an argument to TensorProduct.')\n-        scalar = Mul(*[m for m in args if covariant_order(m) == 0])\n-        forms = [m for m in args if covariant_order(m)]\n-        if forms:\n-            if len(forms) == 1:\n-                return scalar*forms[0]\n-            return scalar*super(TensorProduct, cls).__new__(cls, *forms)\n+        scalar = Mul(*[m for m in args if covariant_order(m) + contravariant_order(m) == 0])\n+        multifields = [m for m in args if covariant_order(m) + contravariant_order(m)]\n+        if multifields:\n+            if len(multifields) == 1:\n+                return scalar*multifields[0]\n+            return scalar*super(TensorProduct, cls).__new__(cls, *multifields)\n         else:\n             return scalar\n \n@@ -848,25 +855,25 @@ def __init__(self, *args):\n         super(TensorProduct, self).__init__()\n         self._args = args\n \n-    def __call__(self, *v_fields):\n-        \"\"\"Apply on a list of vector_fields.\n+    def __call__(self, *fields):\n+        \"\"\"Apply on a list of fields.\n \n-        If the number of vector fields supplied is not equal to the order of\n-        the form field the list of arguments is padded with ``None``'s.\n+        If the number of input fields supplied is not equal to the order of\n+        the tensor product field, the list of arguments is padded with ``None``'s.\n \n         The list of arguments is divided in sublists depending on the order of\n         the forms inside the tensor product. The sublists are provided as\n         arguments to these forms and the resulting expressions are given to the\n         constructor of ``TensorProduct``.\n         \"\"\"\n-        tot_order = covariant_order(self)\n-        tot_args = len(v_fields)\n+        tot_order = covariant_order(self) + contravariant_order(self)\n+        tot_args = len(fields)\n         if tot_args != tot_order:\n-            v_fields = list(v_fields) + [None]*(tot_order - tot_args)\n-        orders = [covariant_order(f) for f in self._args]\n+            fields = list(fields) + [None]*(tot_order - tot_args)\n+        orders = [covariant_order(f) + contravariant_order(f) for f in self._args]\n         indices = [sum(orders[:i + 1]) for i in range(len(orders) - 1)]\n-        v_fields = [v_fields[i:j] for i, j in zip([0] + indices, indices + [None])]\n-        multipliers = [t[0].rcall(*t[1]) for t in zip(self._args, v_fields)]\n+        fields = [fields[i:j] for i, j in zip([0] + indices, indices + [None])]\n+        multipliers = [t[0].rcall(*t[1]) for t in zip(self._args, fields)]\n         return TensorProduct(*multipliers)\n \n     def _latex(self, printer, *args):\n@@ -896,6 +903,8 @@ class WedgeProduct(TensorProduct):\n     -1\n     >>> WedgeProduct(R2.dx, R2.x*R2.dy)(R2.x*R2.e_x, R2.e_y)\n     x**2\n+    >>> WedgeProduct(R2.e_x,R2.e_y)(R2.y,None)\n+    -e_x\n \n     You can nest wedge products.\n \n@@ -906,15 +915,15 @@ class WedgeProduct(TensorProduct):\n     \"\"\"\n     # TODO the calculation of signatures is slow\n     # TODO you do not need all these permutations (neither the prefactor)\n-    def __call__(self, *vector_fields):\n+    def __call__(self, *fields):\n         \"\"\"Apply on a list of vector_fields.\n \n         The expression is rewritten internally in terms of tensor products and evaluated.\"\"\"\n-        orders = (covariant_order(e) for e in self.args)\n+        orders = (covariant_order(e) + contravariant_order(e) for e in self.args)\n         mul = 1/Mul(*(factorial(o) for o in orders))\n-        perms = permutations(vector_fields)\n+        perms = permutations(fields)\n         perms_par = (Permutation(\n-            p).signature() for p in permutations(list(range(len(vector_fields)))))\n+            p).signature() for p in permutations(list(range(len(fields)))))\n         tensor_prod = TensorProduct(*self.args)\n         return mul*Add(*[tensor_prod(*p[0])*p[1] for p in zip(perms, perms_par)])\n \n@@ -1340,6 +1349,8 @@ def contravariant_order(expr, _strict=False):\n         return 0\n     elif isinstance(expr, BaseVectorField):\n         return 1\n+    elif isinstance(expr, TensorProduct):\n+        return sum(contravariant_order(a) for a in expr.args)\n     elif not _strict or expr.atoms(BaseScalarField):\n         return 0\n     else:  # If it does not contain anything related to the diffgeom module and it is _strict\n"}
{"instance_id": "sympy__sympy-14817", "model_name_or_path": "gold", "model_patch": "diff --git a/sympy/printing/pretty/pretty.py b/sympy/printing/pretty/pretty.py\n--- a/sympy/printing/pretty/pretty.py\n+++ b/sympy/printing/pretty/pretty.py\n@@ -825,7 +825,8 @@ def _print_MatAdd(self, expr):\n             if s is None:\n                 s = pform     # First element\n             else:\n-                if S(item.args[0]).is_negative:\n+                coeff = item.as_coeff_mmul()[0]\n+                if _coeff_isneg(S(coeff)):\n                     s = prettyForm(*stringPict.next(s, ' '))\n                     pform = self._print(item)\n                 else:\n"}
{"instance_id": "sympy__sympy-22914", "model_name_or_path": "gold", "model_patch": "diff --git a/sympy/printing/pycode.py b/sympy/printing/pycode.py\n--- a/sympy/printing/pycode.py\n+++ b/sympy/printing/pycode.py\n@@ -18,6 +18,8 @@\n \n _known_functions = {\n     'Abs': 'abs',\n+    'Min': 'min',\n+    'Max': 'max',\n }\n _known_functions_math = {\n     'acos': 'acos',\n"}
{"instance_id": "sympy__sympy-23729", "model_name_or_path": "gold", "model_patch": "diff --git a/sympy/printing/julia.py b/sympy/printing/julia.py\n--- a/sympy/printing/julia.py\n+++ b/sympy/printing/julia.py\n@@ -153,11 +153,12 @@ def _print_Mul(self, expr):\n                     if len(item.args[0].args) != 1 and isinstance(item.base, Mul):   # To avoid situations like #14160\n                         pow_paren.append(item)\n                     b.append(Pow(item.base, -item.exp))\n-            elif item.is_Rational and item is not S.Infinity:\n-                if item.p != 1:\n-                    a.append(Rational(item.p))\n-                if item.q != 1:\n-                    b.append(Rational(item.q))\n+            elif item.is_Rational and item is not S.Infinity and item.p == 1:\n+                # Save the Rational type in julia Unless the numerator is 1.\n+                # For example:\n+                # julia_code(Rational(3, 7)*x) --> (3 // 7) * x\n+                # julia_code(x/3) --> x / 3 but not x * (1 // 3)\n+                b.append(Rational(item.q))\n             else:\n                 a.append(item)\n \n@@ -177,18 +178,17 @@ def multjoin(a, a_str):\n             r = a_str[0]\n             for i in range(1, len(a)):\n                 mulsym = '*' if a[i-1].is_number else '.*'\n-                r = r + mulsym + a_str[i]\n+                r = \"%s %s %s\" % (r, mulsym, a_str[i])\n             return r\n \n         if not b:\n             return sign + multjoin(a, a_str)\n         elif len(b) == 1:\n             divsym = '/' if b[0].is_number else './'\n-            return sign + multjoin(a, a_str) + divsym + b_str[0]\n+            return \"%s %s %s\" % (sign+multjoin(a, a_str), divsym, b_str[0])\n         else:\n             divsym = '/' if all(bi.is_number for bi in b) else './'\n-            return (sign + multjoin(a, a_str) +\n-                    divsym + \"(%s)\" % multjoin(b, b_str))\n+            return \"%s %s (%s)\" % (sign + multjoin(a, a_str), divsym, multjoin(b, b_str))\n \n     def _print_Relational(self, expr):\n         lhs_code = self._print(expr.lhs)\n@@ -207,18 +207,18 @@ def _print_Pow(self, expr):\n         if expr.is_commutative:\n             if expr.exp == -S.Half:\n                 sym = '/' if expr.base.is_number else './'\n-                return \"1\" + sym + \"sqrt(%s)\" % self._print(expr.base)\n+                return \"1 %s sqrt(%s)\" % (sym, self._print(expr.base))\n             if expr.exp == -S.One:\n                 sym = '/' if expr.base.is_number else './'\n-                return \"1\" + sym + \"%s\" % self.parenthesize(expr.base, PREC)\n+                return  \"1 %s %s\" % (sym, self.parenthesize(expr.base, PREC))\n \n-        return '%s%s%s' % (self.parenthesize(expr.base, PREC), powsymbol,\n+        return '%s %s %s' % (self.parenthesize(expr.base, PREC), powsymbol,\n                            self.parenthesize(expr.exp, PREC))\n \n \n     def _print_MatPow(self, expr):\n         PREC = precedence(expr)\n-        return '%s^%s' % (self.parenthesize(expr.base, PREC),\n+        return '%s ^ %s' % (self.parenthesize(expr.base, PREC),\n                           self.parenthesize(expr.exp, PREC))\n \n \n@@ -395,7 +395,7 @@ def _print_Identity(self, expr):\n         return \"eye(%s)\" % self._print(expr.shape[0])\n \n     def _print_HadamardProduct(self, expr):\n-        return '.*'.join([self.parenthesize(arg, precedence(expr))\n+        return ' .* '.join([self.parenthesize(arg, precedence(expr))\n                           for arg in expr.args])\n \n     def _print_HadamardPower(self, expr):\n@@ -405,7 +405,12 @@ def _print_HadamardPower(self, expr):\n             self.parenthesize(expr.exp, PREC)\n             ])\n \n-    # Note: as of 2015, Julia doesn't have spherical Bessel functions\n+    def _print_Rational(self, expr):\n+        if expr.q == 1:\n+            return str(expr.p)\n+        return \"%s // %s\" % (expr.p, expr.q)\n+\n+    # Note: as of 2022, Julia doesn't have spherical Bessel functions\n     def _print_jn(self, expr):\n         from sympy.functions import sqrt, besselj\n         x = expr.argument\n@@ -456,6 +461,23 @@ def _print_Piecewise(self, expr):\n                     lines.append(\"end\")\n             return \"\\n\".join(lines)\n \n+    def _print_MatMul(self, expr):\n+        c, m = expr.as_coeff_mmul()\n+\n+        sign = \"\"\n+        if c.is_number:\n+            re, im = c.as_real_imag()\n+            if im.is_zero and re.is_negative:\n+                expr = _keep_coeff(-c, m)\n+                sign = \"-\"\n+            elif re.is_zero and im.is_negative:\n+                expr = _keep_coeff(-c, m)\n+                sign = \"-\"\n+\n+        return sign + ' * '.join(\n+            (self.parenthesize(arg, precedence(expr)) for arg in expr.args)\n+        )\n+\n \n     def indent_code(self, code):\n         \"\"\"Accepts a string of code or a list of code lines\"\"\"\n@@ -530,19 +552,19 @@ def julia_code(expr, assign_to=None, **settings):\n     >>> from sympy import julia_code, symbols, sin, pi\n     >>> x = symbols('x')\n     >>> julia_code(sin(x).series(x).removeO())\n-    'x.^5/120 - x.^3/6 + x'\n+    'x .^ 5 / 120 - x .^ 3 / 6 + x'\n \n     >>> from sympy import Rational, ceiling\n     >>> x, y, tau = symbols(\"x, y, tau\")\n     >>> julia_code((2*tau)**Rational(7, 2))\n-    '8*sqrt(2)*tau.^(7/2)'\n+    '8 * sqrt(2) * tau .^ (7 // 2)'\n \n     Note that element-wise (Hadamard) operations are used by default between\n     symbols.  This is because its possible in Julia to write \"vectorized\"\n     code.  It is harmless if the values are scalars.\n \n     >>> julia_code(sin(pi*x*y), assign_to=\"s\")\n-    's = sin(pi*x.*y)'\n+    's = sin(pi * x .* y)'\n \n     If you need a matrix product \"*\" or matrix power \"^\", you can specify the\n     symbol as a ``MatrixSymbol``.\n@@ -551,7 +573,7 @@ def julia_code(expr, assign_to=None, **settings):\n     >>> n = Symbol('n', integer=True, positive=True)\n     >>> A = MatrixSymbol('A', n, n)\n     >>> julia_code(3*pi*A**3)\n-    '(3*pi)*A^3'\n+    '(3 * pi) * A ^ 3'\n \n     This class uses several rules to decide which symbol to use a product.\n     Pure numbers use \"*\", Symbols use \".*\" and MatrixSymbols use \"*\".\n@@ -562,7 +584,7 @@ def julia_code(expr, assign_to=None, **settings):\n     while a human programmer might write \"(x^2*y)*A^3\", we generate:\n \n     >>> julia_code(x**2*y*A**3)\n-    '(x.^2.*y)*A^3'\n+    '(x .^ 2 .* y) * A ^ 3'\n \n     Matrices are supported using Julia inline notation.  When using\n     ``assign_to`` with matrices, the name can be specified either as a string\n@@ -571,7 +593,7 @@ def julia_code(expr, assign_to=None, **settings):\n     >>> from sympy import Matrix, MatrixSymbol\n     >>> mat = Matrix([[x**2, sin(x), ceiling(x)]])\n     >>> julia_code(mat, assign_to='A')\n-    'A = [x.^2 sin(x) ceil(x)]'\n+    'A = [x .^ 2 sin(x) ceil(x)]'\n \n     ``Piecewise`` expressions are implemented with logical masking by default.\n     Alternatively, you can pass \"inline=False\" to use if-else conditionals.\n@@ -589,7 +611,7 @@ def julia_code(expr, assign_to=None, **settings):\n \n     >>> mat = Matrix([[x**2, pw, sin(x)]])\n     >>> julia_code(mat, assign_to='A')\n-    'A = [x.^2 ((x > 0) ? (x + 1) : (x)) sin(x)]'\n+    'A = [x .^ 2 ((x > 0) ? (x + 1) : (x)) sin(x)]'\n \n     Custom printing can be defined for certain types by passing a dictionary of\n     \"type\" : \"function\" to the ``user_functions`` kwarg.  Alternatively, the\n@@ -621,7 +643,7 @@ def julia_code(expr, assign_to=None, **settings):\n     >>> i = Idx('i', len_y-1)\n     >>> e = Eq(Dy[i], (y[i+1]-y[i])/(t[i+1]-t[i]))\n     >>> julia_code(e.rhs, assign_to=e.lhs, contract=False)\n-    'Dy[i] = (y[i + 1] - y[i])./(t[i + 1] - t[i])'\n+    'Dy[i] = (y[i + 1] - y[i]) ./ (t[i + 1] - t[i])'\n     \"\"\"\n     return JuliaCodePrinter(settings).doprint(expr, assign_to)\n \n"}
{"instance_id": "sympy__sympy-24723", "model_name_or_path": "gold", "model_patch": "diff --git a/sympy/stats/matrix_distributions.py b/sympy/stats/matrix_distributions.py\n--- a/sympy/stats/matrix_distributions.py\n+++ b/sympy/stats/matrix_distributions.py\n@@ -450,7 +450,7 @@ def pdf(self, x):\n                     \"or MatrixSymbol\" % str(x))\n         term1 = Inverse(V)*Transpose(x - M)*Inverse(U)*(x - M)\n         num = exp(-Trace(term1)/S(2))\n-        den = (2*pi)**(S(n*p)/2) * Determinant(U)**S(p)/2 * Determinant(V)**S(n)/2\n+        den = (2*pi)**(S(n*p)/2) * Determinant(U)**(S(p)/2) * Determinant(V)**(S(n)/2)\n         return num/den\n \n def MatrixNormal(symbol, location_matrix, scale_matrix_1, scale_matrix_2):\n@@ -482,11 +482,11 @@ def MatrixNormal(symbol, location_matrix, scale_matrix_1, scale_matrix_2):\n     >>> M = MatrixNormal('M', [[1, 2]], [1], [[1, 0], [0, 1]])\n     >>> X = MatrixSymbol('X', 1, 2)\n     >>> density(M)(X).doit()\n-    2*exp(-Trace((Matrix([\n+    exp(-Trace((Matrix([\n     [-1],\n-    [-2]]) + X.T)*(Matrix([[-1, -2]]) + X))/2)/pi\n+    [-2]]) + X.T)*(Matrix([[-1, -2]]) + X))/2)/(2*pi)\n     >>> density(M)([[3, 4]]).doit()\n-    2*exp(-4)/pi\n+    exp(-4)/(2*pi)\n \n     References\n     ==========\n"}
{"instance_id": "sympy__sympy-15151", "model_name_or_path": "gold", "model_patch": "diff --git a/sympy/printing/latex.py b/sympy/printing/latex.py\n--- a/sympy/printing/latex.py\n+++ b/sympy/printing/latex.py\n@@ -607,7 +607,8 @@ def _print_BasisDependent(self, expr):\n         return outstr\n \n     def _print_Indexed(self, expr):\n-        tex = self._print(expr.base)+'_{%s}' % ','.join(\n+        tex_base = self._print(expr.base)\n+        tex = '{'+tex_base+'}'+'_{%s}' % ','.join(\n             map(self._print, expr.indices))\n         return tex\n \n"}
{"instance_id": "sympy__sympy-16334", "model_name_or_path": "gold", "model_patch": "diff --git a/sympy/core/power.py b/sympy/core/power.py\n--- a/sympy/core/power.py\n+++ b/sympy/core/power.py\n@@ -437,6 +437,9 @@ def _eval_is_positive(self):\n                 return True\n             if self.exp.is_odd:\n                 return False\n+        elif self.base.is_zero:\n+            if self.exp.is_real:\n+                return self.exp.is_zero\n         elif self.base.is_nonpositive:\n             if self.exp.is_odd:\n                 return False\n@@ -459,6 +462,9 @@ def _eval_is_negative(self):\n         elif self.base.is_positive:\n             if self.exp.is_real:\n                 return False\n+        elif self.base.is_zero:\n+            if self.exp.is_real:\n+                return False\n         elif self.base.is_nonnegative:\n             if self.exp.is_nonnegative:\n                 return False\n"}
{"instance_id": "sympy__sympy-18763", "model_name_or_path": "gold", "model_patch": "diff --git a/sympy/printing/latex.py b/sympy/printing/latex.py\n--- a/sympy/printing/latex.py\n+++ b/sympy/printing/latex.py\n@@ -703,7 +703,7 @@ def _print_Subs(self, subs):\n         latex_new = (self._print(e) for e in new)\n         latex_subs = r'\\\\ '.join(\n             e[0] + '=' + e[1] for e in zip(latex_old, latex_new))\n-        return r'\\left. %s \\right|_{\\substack{ %s }}' % (latex_expr,\n+        return r'\\left. \\left(%s\\right) \\right|_{\\substack{ %s }}' % (latex_expr,\n                                                          latex_subs)\n \n     def _print_Integral(self, expr):\n"}
{"instance_id": "sympy__sympy-19601", "model_name_or_path": "gold", "model_patch": "diff --git a/sympy/simplify/radsimp.py b/sympy/simplify/radsimp.py\n--- a/sympy/simplify/radsimp.py\n+++ b/sympy/simplify/radsimp.py\n@@ -178,7 +178,7 @@ def collect(expr, syms, func=None, evaluate=None, exact=False, distribute_order_\n         if not isinstance(rv, dict):\n             return rv.xreplace(urep)\n         else:\n-            return {urep.get(k, k): v for k, v in rv.items()}\n+            return {urep.get(k, k): v.xreplace(urep) for k, v in rv.items()}\n \n     if evaluate is None:\n         evaluate = global_parameters.evaluate\n"}
{"instance_id": "sympy__sympy-20590", "model_name_or_path": "gold", "model_patch": "diff --git a/sympy/core/_print_helpers.py b/sympy/core/_print_helpers.py\n--- a/sympy/core/_print_helpers.py\n+++ b/sympy/core/_print_helpers.py\n@@ -17,6 +17,11 @@ class Printable:\n     This also adds support for LaTeX printing in jupyter notebooks.\n     \"\"\"\n \n+    # Since this class is used as a mixin we set empty slots. That means that\n+    # instances of any subclasses that use slots will not need to have a\n+    # __dict__.\n+    __slots__ = ()\n+\n     # Note, we always use the default ordering (lex) in __str__ and __repr__,\n     # regardless of the global setting. See issue 5487.\n     def __str__(self):\n"}
{"instance_id": "sympy__sympy-21260", "model_name_or_path": "gold", "model_patch": "diff --git a/sympy/core/basic.py b/sympy/core/basic.py\n--- a/sympy/core/basic.py\n+++ b/sympy/core/basic.py\n@@ -7,7 +7,6 @@\n from .cache import cacheit\n from .sympify import _sympify, sympify, SympifyError\n from .compatibility import iterable, ordered\n-from .singleton import S\n from .kind import UndefinedKind\n from ._print_helpers import Printable\n \n@@ -121,19 +120,17 @@ def __new__(cls, *args):\n     def copy(self):\n         return self.func(*self.args)\n \n-    def __reduce_ex__(self, proto):\n-        \"\"\" Pickling support.\"\"\"\n-        return type(self), self.__getnewargs__(), self.__getstate__()\n-\n     def __getnewargs__(self):\n         return self.args\n \n     def __getstate__(self):\n-        return {}\n+        return None\n \n-    def __setstate__(self, state):\n-        for k, v in state.items():\n-            setattr(self, k, v)\n+    def __reduce_ex__(self, protocol):\n+        if protocol < 2:\n+            msg = \"Only pickle protocol 2 or higher is supported by sympy\"\n+            raise NotImplementedError(msg)\n+        return super().__reduce_ex__(protocol)\n \n     def __hash__(self):\n         # hash cannot be cached using cache_it because infinite recurrence\n@@ -2060,3 +2057,7 @@ def _make_find_query(query):\n     elif isinstance(query, Basic):\n         return lambda expr: expr.match(query) is not None\n     return query\n+\n+\n+# Delayed to avoid cyclic import\n+from .singleton import S\ndiff --git a/sympy/core/numbers.py b/sympy/core/numbers.py\n--- a/sympy/core/numbers.py\n+++ b/sympy/core/numbers.py\n@@ -1206,11 +1206,8 @@ def _new(cls, _mpf_, _prec, zero=True):\n         return obj\n \n     # mpz can't be pickled\n-    def __getnewargs__(self):\n-        return (mlib.to_pickable(self._mpf_),)\n-\n-    def __getstate__(self):\n-        return {'_prec': self._prec}\n+    def __getnewargs_ex__(self):\n+        return ((mlib.to_pickable(self._mpf_),), {'precision': self._prec})\n \n     def _hashable_content(self):\n         return (self._mpf_, self._prec)\n@@ -2665,6 +2662,7 @@ class One(IntegerConstant, metaclass=Singleton):\n     .. [1] https://en.wikipedia.org/wiki/1_%28number%29\n     \"\"\"\n     is_number = True\n+    is_positive = True\n \n     p = 1\n     q = 1\ndiff --git a/sympy/core/singleton.py b/sympy/core/singleton.py\n--- a/sympy/core/singleton.py\n+++ b/sympy/core/singleton.py\n@@ -1,8 +1,6 @@\n \"\"\"Singleton mechanism\"\"\"\n \n \n-from typing import Any, Dict, Type\n-\n from .core import Registry\n from .assumptions import ManagedProperties\n from .sympify import sympify\n@@ -171,26 +169,14 @@ class is instantiated. Additionally, this instance can be accessed through\n     subclasses to have a different metaclass than the superclass, except the\n     subclass may use a subclassed metaclass).\n     \"\"\"\n+    def __init__(cls, *args, **kwargs):\n+        super().__init__(cls, *args, **kwargs)\n+        cls._instance = obj = Basic.__new__(cls)\n+        cls.__new__ = lambda cls: obj\n+        cls.__getnewargs__ = lambda obj: ()\n+        cls.__getstate__ = lambda obj: None\n+        S.register(cls)\n+\n \n-    _instances = {}  # type: Dict[Type[Any], Any]\n-    \"Maps singleton classes to their instances.\"\n-\n-    def __new__(cls, *args, **kwargs):\n-        result = super().__new__(cls, *args, **kwargs)\n-        S.register(result)\n-        return result\n-\n-    def __call__(self, *args, **kwargs):\n-        # Called when application code says SomeClass(), where SomeClass is a\n-        # class of which Singleton is the metaclas.\n-        # __call__ is invoked first, before __new__() and __init__().\n-        if self not in Singleton._instances:\n-            Singleton._instances[self] = \\\n-                super().__call__(*args, **kwargs)\n-                # Invokes the standard constructor of SomeClass.\n-        return Singleton._instances[self]\n-\n-        # Inject pickling support.\n-        def __getnewargs__(self):\n-            return ()\n-        self.__getnewargs__ = __getnewargs__\n+# Delayed to avoid cyclic import\n+from .basic import Basic\ndiff --git a/sympy/core/symbol.py b/sympy/core/symbol.py\n--- a/sympy/core/symbol.py\n+++ b/sympy/core/symbol.py\n@@ -300,11 +300,8 @@ def __new_stage2__(cls, name, **assumptions):\n     __xnew_cached_ = staticmethod(\n         cacheit(__new_stage2__))   # symbols are always cached\n \n-    def __getnewargs__(self):\n-        return (self.name,)\n-\n-    def __getstate__(self):\n-        return {'_assumptions': self._assumptions}\n+    def __getnewargs_ex__(self):\n+        return ((self.name,), self.assumptions0)\n \n     def _hashable_content(self):\n         # Note: user-specified assumptions not hashed, just derived ones\n@@ -414,8 +411,8 @@ def __new__(cls, name=None, dummy_index=None, **assumptions):\n \n         return obj\n \n-    def __getstate__(self):\n-        return {'_assumptions': self._assumptions, 'dummy_index': self.dummy_index}\n+    def __getnewargs_ex__(self):\n+        return ((self.name, self.dummy_index), self.assumptions0)\n \n     @cacheit\n     def sort_key(self, order=None):\ndiff --git a/sympy/functions/elementary/miscellaneous.py b/sympy/functions/elementary/miscellaneous.py\n--- a/sympy/functions/elementary/miscellaneous.py\n+++ b/sympy/functions/elementary/miscellaneous.py\n@@ -42,17 +42,16 @@ class IdentityFunction(Lambda, metaclass=Singleton):\n \n     \"\"\"\n \n-    def __new__(cls):\n-        x = Dummy('x')\n-        #construct \"by hand\" to avoid infinite loop\n-        return Expr.__new__(cls, Tuple(x), x)\n+    _symbol = Dummy('x')\n \n     @property\n-    def args(self):\n-        return ()\n+    def signature(self):\n+        return Tuple(self._symbol)\n+\n+    @property\n+    def expr(self):\n+        return self._symbol\n \n-    def __getnewargs__(self):\n-        return ()\n \n Id = S.IdentityFunction\n \ndiff --git a/sympy/physics/paulialgebra.py b/sympy/physics/paulialgebra.py\n--- a/sympy/physics/paulialgebra.py\n+++ b/sympy/physics/paulialgebra.py\n@@ -131,8 +131,11 @@ def __new__(cls, i, label=\"sigma\"):\n         obj.label = label\n         return obj\n \n-    def __getnewargs__(self):\n-        return (self.i,self.label,)\n+    def __getnewargs_ex__(self):\n+        return (self.i, self.label), {}\n+\n+    def _hashable_content(self):\n+        return (self.i, self.label)\n \n     # FIXME don't work for -I*Pauli(2)*Pauli(3)\n     def __mul__(self, other):\ndiff --git a/sympy/sets/fancysets.py b/sympy/sets/fancysets.py\n--- a/sympy/sets/fancysets.py\n+++ b/sympy/sets/fancysets.py\n@@ -255,8 +255,21 @@ class Reals(Interval, metaclass=Singleton):\n \n     ComplexRegion\n     \"\"\"\n-    def __new__(cls):\n-        return Interval.__new__(cls, S.NegativeInfinity, S.Infinity)\n+    @property\n+    def start(self):\n+        return S.NegativeInfinity\n+\n+    @property\n+    def end(self):\n+        return S.Infinity\n+\n+    @property\n+    def left_open(self):\n+        return True\n+\n+    @property\n+    def right_open(self):\n+        return True\n \n     def __eq__(self, other):\n         return other == Interval(S.NegativeInfinity, S.Infinity)\ndiff --git a/sympy/sets/sets.py b/sympy/sets/sets.py\n--- a/sympy/sets/sets.py\n+++ b/sympy/sets/sets.py\n@@ -971,23 +971,6 @@ def start(self):\n         \"\"\"\n         return self._args[0]\n \n-    _inf = left = start\n-\n-    @classmethod\n-    def open(cls, a, b):\n-        \"\"\"Return an interval including neither boundary.\"\"\"\n-        return cls(a, b, True, True)\n-\n-    @classmethod\n-    def Lopen(cls, a, b):\n-        \"\"\"Return an interval not including the left boundary.\"\"\"\n-        return cls(a, b, True, False)\n-\n-    @classmethod\n-    def Ropen(cls, a, b):\n-        \"\"\"Return an interval not including the right boundary.\"\"\"\n-        return cls(a, b, False, True)\n-\n     @property\n     def end(self):\n         \"\"\"\n@@ -1005,8 +988,6 @@ def end(self):\n         \"\"\"\n         return self._args[1]\n \n-    _sup = right = end\n-\n     @property\n     def left_open(self):\n         \"\"\"\n@@ -1041,6 +1022,37 @@ def right_open(self):\n         \"\"\"\n         return self._args[3]\n \n+    @classmethod\n+    def open(cls, a, b):\n+        \"\"\"Return an interval including neither boundary.\"\"\"\n+        return cls(a, b, True, True)\n+\n+    @classmethod\n+    def Lopen(cls, a, b):\n+        \"\"\"Return an interval not including the left boundary.\"\"\"\n+        return cls(a, b, True, False)\n+\n+    @classmethod\n+    def Ropen(cls, a, b):\n+        \"\"\"Return an interval not including the right boundary.\"\"\"\n+        return cls(a, b, False, True)\n+\n+    @property\n+    def _inf(self):\n+        return self.start\n+\n+    @property\n+    def _sup(self):\n+        return self.end\n+\n+    @property\n+    def left(self):\n+        return self.start\n+\n+    @property\n+    def right(self):\n+        return self.end\n+\n     @property\n     def is_empty(self):\n         if self.left_open or self.right_open:\n"}
{"instance_id": "sympy__sympy-22005", "model_name_or_path": "gold", "model_patch": "diff --git a/sympy/solvers/polysys.py b/sympy/solvers/polysys.py\n--- a/sympy/solvers/polysys.py\n+++ b/sympy/solvers/polysys.py\n@@ -240,6 +240,12 @@ def _solve_reduced_system(system, gens, entry=False):\n \n         univariate = list(filter(_is_univariate, basis))\n \n+        if len(basis) < len(gens):\n+            raise NotImplementedError(filldedent('''\n+                only zero-dimensional systems supported\n+                (finite number of solutions)\n+                '''))\n+\n         if len(univariate) == 1:\n             f = univariate.pop()\n         else:\n"}
{"instance_id": "sympy__sympy-24909", "model_name_or_path": "gold", "model_patch": "diff --git a/sympy/physics/units/prefixes.py b/sympy/physics/units/prefixes.py\n--- a/sympy/physics/units/prefixes.py\n+++ b/sympy/physics/units/prefixes.py\n@@ -6,7 +6,7 @@\n \"\"\"\n from sympy.core.expr import Expr\n from sympy.core.sympify import sympify\n-\n+from sympy.core.singleton import S\n \n class Prefix(Expr):\n     \"\"\"\n@@ -85,9 +85,9 @@ def __mul__(self, other):\n \n         fact = self.scale_factor * other.scale_factor\n \n-        if fact == 1:\n-            return 1\n-        elif isinstance(other, Prefix):\n+        if isinstance(other, Prefix):\n+            if fact == 1:\n+                return S.One\n             # simplify prefix\n             for p in PREFIXES:\n                 if PREFIXES[p].scale_factor == fact:\n@@ -103,7 +103,7 @@ def __truediv__(self, other):\n         fact = self.scale_factor / other.scale_factor\n \n         if fact == 1:\n-            return 1\n+            return S.One\n         elif isinstance(other, Prefix):\n             for p in PREFIXES:\n                 if PREFIXES[p].scale_factor == fact:\n"}
