{"instance_id": "django__django-12209", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/tests/__init__.py b/tests/__init__.py\nnew file mode 100644\nindex 0000000000..e69de29bb2\ndiff --git a/tests/model_test_app/__init__.py b/tests/model_test_app/__init__.py\nnew file mode 100644\nindex 0000000000..e69de29bb2\ndiff --git a/tests/model_test_app/apps.py b/tests/model_test_app/apps.py\nnew file mode 100644\nindex 0000000000..337e0bc052\n--- /dev/null\n+++ b/tests/model_test_app/apps.py\n@@ -0,0 +1,5 @@\n+from django.apps import AppConfig\n+\n+class ModelTestAppConfig(AppConfig):\n+    name = 'tests.model_test_app'\n+    label = 'model_test_app'\n\\ No newline at end of file\ndiff --git a/tests/model_test_app/migrations/0001_initial.py b/tests/model_test_app/migrations/0001_initial.py\nnew file mode 100644\nindex 0000000000..51922388da\n--- /dev/null\n+++ b/tests/model_test_app/migrations/0001_initial.py\n@@ -0,0 +1,19 @@\n+from django.db import migrations, models\n+import uuid\n+\n+class Migration(migrations.Migration):\n+\n+    initial = True\n+\n+    dependencies = [\n+    ]\n+\n+    operations = [\n+        migrations.CreateModel(\n+            name='Sample',\n+            fields=[\n+                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),\n+                ('name', models.CharField(blank=True, max_length=100)),\n+            ],\n+        ),\n+    ]\n\\ No newline at end of file\ndiff --git a/tests/model_test_app/migrations/__init__.py b/tests/model_test_app/migrations/__init__.py\nnew file mode 100644\nindex 0000000000..e69de29bb2\ndiff --git a/tests/model_test_app/models.py b/tests/model_test_app/models.py\nnew file mode 100644\nindex 0000000000..7e0c843ffe\n--- /dev/null\n+++ b/tests/model_test_app/models.py\n@@ -0,0 +1,9 @@\n+from uuid import uuid4\n+from django.db import models\n+\n+class Sample(models.Model):\n+    id = models.UUIDField(primary_key=True, default=uuid4)\n+    name = models.CharField(blank=True, max_length=100)\n+    \n+    class Meta:\n+        app_label = 'model_test_app'\n\\ No newline at end of file\ndiff --git a/tests/model_test_app/tests.py b/tests/model_test_app/tests.py\nnew file mode 100644\nindex 0000000000..95535193b7\n--- /dev/null\n+++ b/tests/model_test_app/tests.py\n@@ -0,0 +1,36 @@\n+from django.test import SimpleTestCase\n+from django.db import models\n+\n+class UUIDPKUpdateTests(SimpleTestCase):\n+    def test_force_insert_determination(self):\n+        \"\"\"\n+        Test that force_insert is determined correctly when saving a model instance\n+        with an explicit UUID primary key.\n+        \"\"\"\n+        class SampleModel(models.Model):\n+            id = models.UUIDField(primary_key=True, default=lambda: 'test-uuid')\n+            name = models.CharField(max_length=100)\n+            \n+            class Meta:\n+                app_label = 'test_app'\n+        \n+        # Create a model and get its _state.adding value before save()\n+        instance = SampleModel()\n+        self.assertTrue(instance._state.adding)\n+        \n+        # Set an explicit PK value and simulate saving\n+        instance.pk = 'test-uuid'\n+        instance._state.adding = False  # This is what happens after first save\n+        \n+        # Create a new instance with same PK to simulate update case\n+        new_instance = SampleModel(pk='test-uuid', name='Test 1')\n+        \n+        # We need to check if force_insert would be True in the save logic\n+        # This is the actual condition from Model.save_base():\n+        would_force_insert = not any([\n+            new_instance._state.adding and new_instance.pk is None,\n+            models.NOT_PROVIDED,\n+        ])\n+        \n+        # For an existing PK, force_insert should be False\n+        self.assertFalse(would_force_insert)\n\\ No newline at end of file\ndiff --git a/tests/test_settings.py b/tests/test_settings.py\nnew file mode 100644\nindex 0000000000..5697f74829\n--- /dev/null\n+++ b/tests/test_settings.py\n@@ -0,0 +1,33 @@\n+DATABASES = {\n+    'default': {\n+        'ENGINE': 'django.db.backends.sqlite3',\n+        'NAME': ':memory:',\n+    },\n+}\n+\n+INSTALLED_APPS = [\n+    'django.contrib.auth',\n+    'django.contrib.contenttypes',\n+    'django.contrib.sessions',\n+    'django.contrib.sites',\n+    'django.contrib.messages',\n+    'django.contrib.staticfiles',\n+    'django.contrib.admin',\n+    'tests.model_test_app',\n+]\n+\n+MIDDLEWARE = [\n+    'django.middleware.security.SecurityMiddleware',\n+    'django.contrib.sessions.middleware.SessionMiddleware',\n+    'django.middleware.common.CommonMiddleware',\n+    'django.middleware.csrf.CsrfViewMiddleware',\n+    'django.contrib.auth.middleware.AuthenticationMiddleware',\n+    'django.contrib.messages.middleware.MessageMiddleware',\n+    'django.middleware.clickjacking.XFrameOptionsMiddleware',\n+]\n+\n+SECRET_KEY = 'dummy-key-for-tests'\n+\n+DATABASE_ROUTERS = []\n+\n+TEST_RUNNER = 'django.test.runner.DiscoverRunner'\n\\ No newline at end of file\ndiff --git a/tests/test_uuid_pk_update.py b/tests/test_uuid_pk_update.py\nnew file mode 100644\nindex 0000000000..9c7aed33b7\n--- /dev/null\n+++ b/tests/test_uuid_pk_update.py\n@@ -0,0 +1,25 @@\n+from uuid import uuid4\n+import django\n+from django.db import models\n+from django.test import TestCase\n+\n+class Sample(models.Model):\n+    id = models.UUIDField(primary_key=True, default=uuid4)\n+    name = models.CharField(blank=True, max_length=100)\n+\n+class UUIDPKUpdateTests(TestCase):\n+    def test_explicit_pk_with_default_update(self):\n+        # First create a sample object\n+        s0 = Sample.objects.create()\n+        \n+        # Now try to update it by creating a new instance with same pk\n+        s1 = Sample(pk=s0.pk, name='Test 1')\n+        s1.save()  # This should do an UPDATE, not an INSERT\n+        \n+        # Verify we have only one object and it's updated\n+        self.assertEqual(Sample.objects.count(), 1)\n+        s0.refresh_from_db()\n+        self.assertEqual(s0.name, 'Test 1')\n+        \n+        # Verify the objects are the same\n+        self.assertEqual(s0.pk, s1.pk)\n\\ No newline at end of file\n", "proposed_model_patches": []}