{"instance_id": "django__django-11790", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/django/contrib/auth/forms.py b/django/contrib/auth/forms.py\nindex a0cfed0995..30bf1e4691 100644\n--- a/django/contrib/auth/forms.py\n+++ b/django/contrib/auth/forms.py\n@@ -62,11 +62,14 @@ class UsernameField(forms.CharField):\n         return unicodedata.normalize('NFKC', super().to_python(value))\n \n     def widget_attrs(self, widget):\n-        return {\n-            **super().widget_attrs(widget),\n+        attrs = super().widget_attrs(widget)\n+        attrs.update({\n             'autocapitalize': 'none',\n             'autocomplete': 'username',\n-        }\n+        })\n+        if self.max_length is not None:\n+            attrs['maxlength'] = str(self.max_length)\n+        return attrs\n \n \n class UserCreationForm(forms.ModelForm):\n@@ -75,7 +78,7 @@ class UserCreationForm(forms.ModelForm):\n     password.\n     \"\"\"\n     error_messages = {\n-        'password_mismatch': _('The two password fields didn’t match.'),\n+        'password_mismatch': _(\"The two password fields did not match.\"),\n     }\n     password1 = forms.CharField(\n         label=_(\"Password\"),\n@@ -134,7 +137,7 @@ class UserChangeForm(forms.ModelForm):\n         label=_(\"Password\"),\n         help_text=_(\n             'Raw passwords are not stored, so there is no way to see this '\n-            'user’s password, but you can change the password using '\n+            'user\\'s password, but you can change the password using '\n             '<a href=\"{}\">this form</a>.'\n         ),\n     )\n@@ -304,132 +307,4 @@ class PasswordResetForm(forms.Form):\n             self.send_mail(\n                 subject_template_name, email_template_name, context, from_email,\n                 email, html_email_template_name=html_email_template_name,\n-            )\n-\n-\n-class SetPasswordForm(forms.Form):\n-    \"\"\"\n-    A form that lets a user change set their password without entering the old\n-    password\n-    \"\"\"\n-    error_messages = {\n-        'password_mismatch': _('The two password fields didn’t match.'),\n-    }\n-    new_password1 = forms.CharField(\n-        label=_(\"New password\"),\n-        widget=forms.PasswordInput(attrs={'autocomplete': 'new-password'}),\n-        strip=False,\n-        help_text=password_validation.password_validators_help_text_html(),\n-    )\n-    new_password2 = forms.CharField(\n-        label=_(\"New password confirmation\"),\n-        strip=False,\n-        widget=forms.PasswordInput(attrs={'autocomplete': 'new-password'}),\n-    )\n-\n-    def __init__(self, user, *args, **kwargs):\n-        self.user = user\n-        super().__init__(*args, **kwargs)\n-\n-    def clean_new_password2(self):\n-        password1 = self.cleaned_data.get('new_password1')\n-        password2 = self.cleaned_data.get('new_password2')\n-        if password1 and password2:\n-            if password1 != password2:\n-                raise forms.ValidationError(\n-                    self.error_messages['password_mismatch'],\n-                    code='password_mismatch',\n-                )\n-        password_validation.validate_password(password2, self.user)\n-        return password2\n-\n-    def save(self, commit=True):\n-        password = self.cleaned_data[\"new_password1\"]\n-        self.user.set_password(password)\n-        if commit:\n-            self.user.save()\n-        return self.user\n-\n-\n-class PasswordChangeForm(SetPasswordForm):\n-    \"\"\"\n-    A form that lets a user change their password by entering their old\n-    password.\n-    \"\"\"\n-    error_messages = {\n-        **SetPasswordForm.error_messages,\n-        'password_incorrect': _(\"Your old password was entered incorrectly. Please enter it again.\"),\n-    }\n-    old_password = forms.CharField(\n-        label=_(\"Old password\"),\n-        strip=False,\n-        widget=forms.PasswordInput(attrs={'autocomplete': 'current-password', 'autofocus': True}),\n-    )\n-\n-    field_order = ['old_password', 'new_password1', 'new_password2']\n-\n-    def clean_old_password(self):\n-        \"\"\"\n-        Validate that the old_password field is correct.\n-        \"\"\"\n-        old_password = self.cleaned_data[\"old_password\"]\n-        if not self.user.check_password(old_password):\n-            raise forms.ValidationError(\n-                self.error_messages['password_incorrect'],\n-                code='password_incorrect',\n-            )\n-        return old_password\n-\n-\n-class AdminPasswordChangeForm(forms.Form):\n-    \"\"\"\n-    A form used to change the password of a user in the admin interface.\n-    \"\"\"\n-    error_messages = {\n-        'password_mismatch': _('The two password fields didn’t match.'),\n-    }\n-    required_css_class = 'required'\n-    password1 = forms.CharField(\n-        label=_(\"Password\"),\n-        widget=forms.PasswordInput(attrs={'autocomplete': 'new-password', 'autofocus': True}),\n-        strip=False,\n-        help_text=password_validation.password_validators_help_text_html(),\n-    )\n-    password2 = forms.CharField(\n-        label=_(\"Password (again)\"),\n-        widget=forms.PasswordInput(attrs={'autocomplete': 'new-password'}),\n-        strip=False,\n-        help_text=_(\"Enter the same password as before, for verification.\"),\n-    )\n-\n-    def __init__(self, user, *args, **kwargs):\n-        self.user = user\n-        super().__init__(*args, **kwargs)\n-\n-    def clean_password2(self):\n-        password1 = self.cleaned_data.get('password1')\n-        password2 = self.cleaned_data.get('password2')\n-        if password1 and password2:\n-            if password1 != password2:\n-                raise forms.ValidationError(\n-                    self.error_messages['password_mismatch'],\n-                    code='password_mismatch',\n-                )\n-        password_validation.validate_password(password2, self.user)\n-        return password2\n-\n-    def save(self, commit=True):\n-        \"\"\"Save the new password.\"\"\"\n-        password = self.cleaned_data[\"password1\"]\n-        self.user.set_password(password)\n-        if commit:\n-            self.user.save()\n-        return self.user\n-\n-    @property\n-    def changed_data(self):\n-        data = super().changed_data\n-        for name in self.fields:\n-            if name not in data:\n-                return []\n-        return ['password']\n+            )\n\\ No newline at end of file\ndiff --git a/tests/auth_tests/test_forms.py b/tests/auth_tests/test_forms.py\nindex 7085dcfa11..546a2a78bf 100644\n--- a/tests/auth_tests/test_forms.py\n+++ b/tests/auth_tests/test_forms.py\n@@ -6,7 +6,7 @@ from django import forms\n from django.contrib.auth.forms import (\n     AdminPasswordChangeForm, AuthenticationForm, PasswordChangeForm,\n     PasswordResetForm, ReadOnlyPasswordHashField, ReadOnlyPasswordHashWidget,\n-    SetPasswordForm, UserChangeForm, UserCreationForm,\n+    SetPasswordForm, UserChangeForm, UserCreationForm, UsernameField,\n )\n from django.contrib.auth.models import User\n from django.contrib.auth.signals import user_login_failed\n@@ -39,6 +39,24 @@ class TestDataMixin:\n         cls.u6 = User.objects.create(username='unknown_password', password='foo$bar')\n \n \n+class UsernameFieldTest(SimpleTestCase):\n+    def test_username_field_max_length_attribute(self):\n+        field = UsernameField(max_length=150)\n+        self.assertEqual(field.widget.attrs.get('maxlength'), '150')\n+        \n+        field.widget_attrs(field.widget)  # Force update of widget attrs\n+        self.assertEqual(field.widget.attrs.get('maxlength'), '150')\n+\n+\n+class AuthenticationFormTest(TestDataMixin, TestCase):\n+    def test_username_field_max_length_rendered(self):\n+        \"\"\"\n+        Test that AuthenticationForm's username field has maxlength HTML attribute set.\n+        \"\"\"\n+        form = AuthenticationForm()\n+        self.assertEqual(form.fields['username'].widget.attrs.get('maxlength'), '150')  # Default User model's username max length\n+\n+\n class UserCreationFormTest(TestDataMixin, TestCase):\n \n     def test_user_already_exists(self):\n@@ -120,7 +138,7 @@ class UserCreationFormTest(TestDataMixin, TestCase):\n     def test_normalize_username(self):\n         # The normalization happens in AbstractBaseUser.clean() and ModelForm\n         # validation calls Model.clean().\n-        ohm_username = 'testΩ'  # U+2126 OHM SIGN\n+        ohm_username = 'testΩ'  # U+2126 OHM SIGN\n         data = {\n             'username': ohm_username,\n             'password1': 'pwd2',\n@@ -139,7 +157,7 @@ class UserCreationFormTest(TestDataMixin, TestCase):\n         make appear them equal to Django.\n         \"\"\"\n         omega_username = 'iamtheΩ'  # U+03A9 GREEK CAPITAL LETTER OMEGA\n-        ohm_username = 'iamtheΩ'  # U+2126 OHM SIGN\n+        ohm_username = 'iamtheΩ'  # U+2126 OHM SIGN\n         self.assertNotEqual(omega_username, ohm_username)\n         User.objects.create_user(username=omega_username, password='pwd')\n         data = {\n@@ -236,7 +254,7 @@ class UserCreationFormTest(TestDataMixin, TestCase):\n         form = UserCreationForm()\n         self.assertEqual(\n             form.fields['password1'].help_text,\n-            '<ul><li>Your password can’t be too similar to your other personal information.</li></ul>'\n+            \"<ul><li>Your password can't be too similar to your other personal information.</li></ul>\"\n         )\n \n     @override_settings(AUTH_PASSWORD_VALIDATORS=[\n@@ -253,798 +271,6 @@ class UserCreationFormTest(TestDataMixin, TestCase):\n             'password1': 'testpassword',\n             'password2': 'testpassword',\n             'first_name': 'testpassword',\n-            'last_name': 'lastname',\n         })\n         self.assertFalse(form.is_valid())\n-        self.assertEqual(\n-            form.errors['password2'],\n-            ['The password is too similar to the first name.'],\n-        )\n-\n-    def test_username_field_autocapitalize_none(self):\n-        form = UserCreationForm()\n-        self.assertEqual(form.fields['username'].widget.attrs.get('autocapitalize'), 'none')\n-\n-    def test_html_autocomplete_attributes(self):\n-        form = UserCreationForm()\n-        tests = (\n-            ('username', 'username'),\n-            ('password1', 'new-password'),\n-            ('password2', 'new-password'),\n-        )\n-        for field_name, autocomplete in tests:\n-            with self.subTest(field_name=field_name, autocomplete=autocomplete):\n-                self.assertEqual(form.fields[field_name].widget.attrs['autocomplete'], autocomplete)\n-\n-\n-# To verify that the login form rejects inactive users, use an authentication\n-# backend that allows them.\n-@override_settings(AUTHENTICATION_BACKENDS=['django.contrib.auth.backends.AllowAllUsersModelBackend'])\n-class AuthenticationFormTest(TestDataMixin, TestCase):\n-\n-    def test_invalid_username(self):\n-        # The user submits an invalid username.\n-\n-        data = {\n-            'username': 'jsmith_does_not_exist',\n-            'password': 'test123',\n-        }\n-        form = AuthenticationForm(None, data)\n-        self.assertFalse(form.is_valid())\n-        self.assertEqual(\n-            form.non_field_errors(), [\n-                form.error_messages['invalid_login'] % {\n-                    'username': User._meta.get_field('username').verbose_name\n-                }\n-            ]\n-        )\n-\n-    def test_inactive_user(self):\n-        # The user is inactive.\n-        data = {\n-            'username': 'inactive',\n-            'password': 'password',\n-        }\n-        form = AuthenticationForm(None, data)\n-        self.assertFalse(form.is_valid())\n-        self.assertEqual(form.non_field_errors(), [str(form.error_messages['inactive'])])\n-\n-    # Use an authentication backend that rejects inactive users.\n-    @override_settings(AUTHENTICATION_BACKENDS=['django.contrib.auth.backends.ModelBackend'])\n-    def test_inactive_user_incorrect_password(self):\n-        \"\"\"An invalid login doesn't leak the inactive status of a user.\"\"\"\n-        data = {\n-            'username': 'inactive',\n-            'password': 'incorrect',\n-        }\n-        form = AuthenticationForm(None, data)\n-        self.assertFalse(form.is_valid())\n-        self.assertEqual(\n-            form.non_field_errors(), [\n-                form.error_messages['invalid_login'] % {\n-                    'username': User._meta.get_field('username').verbose_name\n-                }\n-            ]\n-        )\n-\n-    def test_login_failed(self):\n-        signal_calls = []\n-\n-        def signal_handler(**kwargs):\n-            signal_calls.append(kwargs)\n-\n-        user_login_failed.connect(signal_handler)\n-        fake_request = object()\n-        try:\n-            form = AuthenticationForm(fake_request, {\n-                'username': 'testclient',\n-                'password': 'incorrect',\n-            })\n-            self.assertFalse(form.is_valid())\n-            self.assertIs(signal_calls[0]['request'], fake_request)\n-        finally:\n-            user_login_failed.disconnect(signal_handler)\n-\n-    def test_inactive_user_i18n(self):\n-        with self.settings(USE_I18N=True), translation.override('pt-br', deactivate=True):\n-            # The user is inactive.\n-            data = {\n-                'username': 'inactive',\n-                'password': 'password',\n-            }\n-            form = AuthenticationForm(None, data)\n-            self.assertFalse(form.is_valid())\n-            self.assertEqual(form.non_field_errors(), [str(form.error_messages['inactive'])])\n-\n-    # Use an authentication backend that allows inactive users.\n-    @override_settings(AUTHENTICATION_BACKENDS=['django.contrib.auth.backends.AllowAllUsersModelBackend'])\n-    def test_custom_login_allowed_policy(self):\n-        # The user is inactive, but our custom form policy allows them to log in.\n-        data = {\n-            'username': 'inactive',\n-            'password': 'password',\n-        }\n-\n-        class AuthenticationFormWithInactiveUsersOkay(AuthenticationForm):\n-            def confirm_login_allowed(self, user):\n-                pass\n-\n-        form = AuthenticationFormWithInactiveUsersOkay(None, data)\n-        self.assertTrue(form.is_valid())\n-\n-        # If we want to disallow some logins according to custom logic,\n-        # we should raise a django.forms.ValidationError in the form.\n-        class PickyAuthenticationForm(AuthenticationForm):\n-            def confirm_login_allowed(self, user):\n-                if user.username == \"inactive\":\n-                    raise forms.ValidationError(\"This user is disallowed.\")\n-                raise forms.ValidationError(\"Sorry, nobody's allowed in.\")\n-\n-        form = PickyAuthenticationForm(None, data)\n-        self.assertFalse(form.is_valid())\n-        self.assertEqual(form.non_field_errors(), ['This user is disallowed.'])\n-\n-        data = {\n-            'username': 'testclient',\n-            'password': 'password',\n-        }\n-        form = PickyAuthenticationForm(None, data)\n-        self.assertFalse(form.is_valid())\n-        self.assertEqual(form.non_field_errors(), [\"Sorry, nobody's allowed in.\"])\n-\n-    def test_success(self):\n-        # The success case\n-        data = {\n-            'username': 'testclient',\n-            'password': 'password',\n-        }\n-        form = AuthenticationForm(None, data)\n-        self.assertTrue(form.is_valid())\n-        self.assertEqual(form.non_field_errors(), [])\n-\n-    def test_unicode_username(self):\n-        User.objects.create_user(username='Σαρα', password='pwd')\n-        data = {\n-            'username': 'Σαρα',\n-            'password': 'pwd',\n-        }\n-        form = AuthenticationForm(None, data)\n-        self.assertTrue(form.is_valid())\n-        self.assertEqual(form.non_field_errors(), [])\n-\n-    @override_settings(AUTH_USER_MODEL='auth_tests.CustomEmailField')\n-    def test_username_field_max_length_matches_user_model(self):\n-        self.assertEqual(CustomEmailField._meta.get_field('username').max_length, 255)\n-        data = {\n-            'username': 'u' * 255,\n-            'password': 'pwd',\n-            'email': '<EMAIL>',\n-        }\n-        CustomEmailField.objects.create_user(**data)\n-        form = AuthenticationForm(None, data)\n-        self.assertEqual(form.fields['username'].max_length, 255)\n-        self.assertEqual(form.errors, {})\n-\n-    @override_settings(AUTH_USER_MODEL='auth_tests.IntegerUsernameUser')\n-    def test_username_field_max_length_defaults_to_254(self):\n-        self.assertIsNone(IntegerUsernameUser._meta.get_field('username').max_length)\n-        data = {\n-            'username': '0123456',\n-            'password': 'password',\n-        }\n-        IntegerUsernameUser.objects.create_user(**data)\n-        form = AuthenticationForm(None, data)\n-        self.assertEqual(form.fields['username'].max_length, 254)\n-        self.assertEqual(form.errors, {})\n-\n-    def test_username_field_label(self):\n-\n-        class CustomAuthenticationForm(AuthenticationForm):\n-            username = CharField(label=\"Name\", max_length=75)\n-\n-        form = CustomAuthenticationForm()\n-        self.assertEqual(form['username'].label, \"Name\")\n-\n-    def test_username_field_label_not_set(self):\n-\n-        class CustomAuthenticationForm(AuthenticationForm):\n-            username = CharField()\n-\n-        form = CustomAuthenticationForm()\n-        username_field = User._meta.get_field(User.USERNAME_FIELD)\n-        self.assertEqual(form.fields['username'].label, capfirst(username_field.verbose_name))\n-\n-    def test_username_field_autocapitalize_none(self):\n-        form = AuthenticationForm()\n-        self.assertEqual(form.fields['username'].widget.attrs.get('autocapitalize'), 'none')\n-\n-    def test_username_field_label_empty_string(self):\n-\n-        class CustomAuthenticationForm(AuthenticationForm):\n-            username = CharField(label='')\n-\n-        form = CustomAuthenticationForm()\n-        self.assertEqual(form.fields['username'].label, \"\")\n-\n-    def test_password_whitespace_not_stripped(self):\n-        data = {\n-            'username': 'testuser',\n-            'password': ' pass ',\n-        }\n-        form = AuthenticationForm(None, data)\n-        form.is_valid()  # Not necessary to have valid credentails for the test.\n-        self.assertEqual(form.cleaned_data['password'], data['password'])\n-\n-    @override_settings(AUTH_USER_MODEL='auth_tests.IntegerUsernameUser')\n-    def test_integer_username(self):\n-        class CustomAuthenticationForm(AuthenticationForm):\n-            username = IntegerField()\n-\n-        user = IntegerUsernameUser.objects.create_user(username=0, password='pwd')\n-        data = {\n-            'username': 0,\n-            'password': 'pwd',\n-        }\n-        form = CustomAuthenticationForm(None, data)\n-        self.assertTrue(form.is_valid())\n-        self.assertEqual(form.cleaned_data['username'], data['username'])\n-        self.assertEqual(form.cleaned_data['password'], data['password'])\n-        self.assertEqual(form.errors, {})\n-        self.assertEqual(form.user_cache, user)\n-\n-    def test_get_invalid_login_error(self):\n-        error = AuthenticationForm().get_invalid_login_error()\n-        self.assertIsInstance(error, forms.ValidationError)\n-        self.assertEqual(\n-            error.message,\n-            'Please enter a correct %(username)s and password. Note that both '\n-            'fields may be case-sensitive.',\n-        )\n-        self.assertEqual(error.code, 'invalid_login')\n-        self.assertEqual(error.params, {'username': 'username'})\n-\n-    def test_html_autocomplete_attributes(self):\n-        form = AuthenticationForm()\n-        tests = (\n-            ('username', 'username'),\n-            ('password', 'current-password'),\n-        )\n-        for field_name, autocomplete in tests:\n-            with self.subTest(field_name=field_name, autocomplete=autocomplete):\n-                self.assertEqual(form.fields[field_name].widget.attrs['autocomplete'], autocomplete)\n-\n-\n-class SetPasswordFormTest(TestDataMixin, TestCase):\n-\n-    def test_password_verification(self):\n-        # The two new passwords do not match.\n-        user = User.objects.get(username='testclient')\n-        data = {\n-            'new_password1': 'abc123',\n-            'new_password2': 'abc',\n-        }\n-        form = SetPasswordForm(user, data)\n-        self.assertFalse(form.is_valid())\n-        self.assertEqual(\n-            form[\"new_password2\"].errors,\n-            [str(form.error_messages['password_mismatch'])]\n-        )\n-\n-    @mock.patch('django.contrib.auth.password_validation.password_changed')\n-    def test_success(self, password_changed):\n-        user = User.objects.get(username='testclient')\n-        data = {\n-            'new_password1': 'abc123',\n-            'new_password2': 'abc123',\n-        }\n-        form = SetPasswordForm(user, data)\n-        self.assertTrue(form.is_valid())\n-        form.save(commit=False)\n-        self.assertEqual(password_changed.call_count, 0)\n-        form.save()\n-        self.assertEqual(password_changed.call_count, 1)\n-\n-    @override_settings(AUTH_PASSWORD_VALIDATORS=[\n-        {'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator'},\n-        {'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator', 'OPTIONS': {\n-            'min_length': 12,\n-        }},\n-    ])\n-    def test_validates_password(self):\n-        user = User.objects.get(username='testclient')\n-        data = {\n-            'new_password1': 'testclient',\n-            'new_password2': 'testclient',\n-        }\n-        form = SetPasswordForm(user, data)\n-        self.assertFalse(form.is_valid())\n-        self.assertEqual(len(form[\"new_password2\"].errors), 2)\n-        self.assertIn('The password is too similar to the username.', form[\"new_password2\"].errors)\n-        self.assertIn(\n-            'This password is too short. It must contain at least 12 characters.',\n-            form[\"new_password2\"].errors\n-        )\n-\n-    def test_password_whitespace_not_stripped(self):\n-        user = User.objects.get(username='testclient')\n-        data = {\n-            'new_password1': '   password   ',\n-            'new_password2': '   password   ',\n-        }\n-        form = SetPasswordForm(user, data)\n-        self.assertTrue(form.is_valid())\n-        self.assertEqual(form.cleaned_data['new_password1'], data['new_password1'])\n-        self.assertEqual(form.cleaned_data['new_password2'], data['new_password2'])\n-\n-    @override_settings(AUTH_PASSWORD_VALIDATORS=[\n-        {'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator'},\n-        {'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator', 'OPTIONS': {\n-            'min_length': 12,\n-        }},\n-    ])\n-    def test_help_text_translation(self):\n-        french_help_texts = [\n-            'Votre mot de passe ne peut pas trop ressembler à vos autres informations personnelles.',\n-            'Votre mot de passe doit contenir au minimum 12 caractères.',\n-        ]\n-        form = SetPasswordForm(self.u1)\n-        with translation.override('fr'):\n-            html = form.as_p()\n-            for french_text in french_help_texts:\n-                self.assertIn(french_text, html)\n-\n-    def test_html_autocomplete_attributes(self):\n-        form = SetPasswordForm(self.u1)\n-        tests = (\n-            ('new_password1', 'new-password'),\n-            ('new_password2', 'new-password'),\n-        )\n-        for field_name, autocomplete in tests:\n-            with self.subTest(field_name=field_name, autocomplete=autocomplete):\n-                self.assertEqual(form.fields[field_name].widget.attrs['autocomplete'], autocomplete)\n-\n-\n-class PasswordChangeFormTest(TestDataMixin, TestCase):\n-\n-    def test_incorrect_password(self):\n-        user = User.objects.get(username='testclient')\n-        data = {\n-            'old_password': 'test',\n-            'new_password1': 'abc123',\n-            'new_password2': 'abc123',\n-        }\n-        form = PasswordChangeForm(user, data)\n-        self.assertFalse(form.is_valid())\n-        self.assertEqual(form[\"old_password\"].errors, [str(form.error_messages['password_incorrect'])])\n-\n-    def test_password_verification(self):\n-        # The two new passwords do not match.\n-        user = User.objects.get(username='testclient')\n-        data = {\n-            'old_password': 'password',\n-            'new_password1': 'abc123',\n-            'new_password2': 'abc',\n-        }\n-        form = PasswordChangeForm(user, data)\n-        self.assertFalse(form.is_valid())\n-        self.assertEqual(form[\"new_password2\"].errors, [str(form.error_messages['password_mismatch'])])\n-\n-    @mock.patch('django.contrib.auth.password_validation.password_changed')\n-    def test_success(self, password_changed):\n-        # The success case.\n-        user = User.objects.get(username='testclient')\n-        data = {\n-            'old_password': 'password',\n-            'new_password1': 'abc123',\n-            'new_password2': 'abc123',\n-        }\n-        form = PasswordChangeForm(user, data)\n-        self.assertTrue(form.is_valid())\n-        form.save(commit=False)\n-        self.assertEqual(password_changed.call_count, 0)\n-        form.save()\n-        self.assertEqual(password_changed.call_count, 1)\n-\n-    def test_field_order(self):\n-        # Regression test - check the order of fields:\n-        user = User.objects.get(username='testclient')\n-        self.assertEqual(list(PasswordChangeForm(user, {}).fields), ['old_password', 'new_password1', 'new_password2'])\n-\n-    def test_password_whitespace_not_stripped(self):\n-        user = User.objects.get(username='testclient')\n-        user.set_password('   oldpassword   ')\n-        data = {\n-            'old_password': '   oldpassword   ',\n-            'new_password1': ' pass ',\n-            'new_password2': ' pass ',\n-        }\n-        form = PasswordChangeForm(user, data)\n-        self.assertTrue(form.is_valid())\n-        self.assertEqual(form.cleaned_data['old_password'], data['old_password'])\n-        self.assertEqual(form.cleaned_data['new_password1'], data['new_password1'])\n-        self.assertEqual(form.cleaned_data['new_password2'], data['new_password2'])\n-\n-    def test_html_autocomplete_attributes(self):\n-        user = User.objects.get(username='testclient')\n-        form = PasswordChangeForm(user)\n-        self.assertEqual(form.fields['old_password'].widget.attrs['autocomplete'], 'current-password')\n-\n-\n-class UserChangeFormTest(TestDataMixin, TestCase):\n-\n-    def test_username_validity(self):\n-        user = User.objects.get(username='testclient')\n-        data = {'username': 'not valid'}\n-        form = UserChangeForm(data, instance=user)\n-        self.assertFalse(form.is_valid())\n-        validator = next(v for v in User._meta.get_field('username').validators if v.code == 'invalid')\n-        self.assertEqual(form[\"username\"].errors, [str(validator.message)])\n-\n-    def test_bug_14242(self):\n-        # A regression test, introduce by adding an optimization for the\n-        # UserChangeForm.\n-\n-        class MyUserForm(UserChangeForm):\n-            def __init__(self, *args, **kwargs):\n-                super().__init__(*args, **kwargs)\n-                self.fields['groups'].help_text = 'These groups give users different permissions'\n-\n-            class Meta(UserChangeForm.Meta):\n-                fields = ('groups',)\n-\n-        # Just check we can create it\n-        MyUserForm({})\n-\n-    def test_unusable_password(self):\n-        user = User.objects.get(username='empty_password')\n-        user.set_unusable_password()\n-        user.save()\n-        form = UserChangeForm(instance=user)\n-        self.assertIn(_(\"No password set.\"), form.as_table())\n-\n-    def test_bug_17944_empty_password(self):\n-        user = User.objects.get(username='empty_password')\n-        form = UserChangeForm(instance=user)\n-        self.assertIn(_(\"No password set.\"), form.as_table())\n-\n-    def test_bug_17944_unmanageable_password(self):\n-        user = User.objects.get(username='unmanageable_password')\n-        form = UserChangeForm(instance=user)\n-        self.assertIn(_(\"Invalid password format or unknown hashing algorithm.\"), form.as_table())\n-\n-    def test_bug_17944_unknown_password_algorithm(self):\n-        user = User.objects.get(username='unknown_password')\n-        form = UserChangeForm(instance=user)\n-        self.assertIn(_(\"Invalid password format or unknown hashing algorithm.\"), form.as_table())\n-\n-    def test_bug_19133(self):\n-        \"The change form does not return the password value\"\n-        # Use the form to construct the POST data\n-        user = User.objects.get(username='testclient')\n-        form_for_data = UserChangeForm(instance=user)\n-        post_data = form_for_data.initial\n-\n-        # The password field should be readonly, so anything\n-        # posted here should be ignored; the form will be\n-        # valid, and give back the 'initial' value for the\n-        # password field.\n-        post_data['password'] = 'new password'\n-        form = UserChangeForm(instance=user, data=post_data)\n-\n-        self.assertTrue(form.is_valid())\n-        # original hashed password contains $\n-        self.assertIn('$', form.cleaned_data['password'])\n-\n-    def test_bug_19349_bound_password_field(self):\n-        user = User.objects.get(username='testclient')\n-        form = UserChangeForm(data={}, instance=user)\n-        # When rendering the bound password field,\n-        # ReadOnlyPasswordHashWidget needs the initial\n-        # value to render correctly\n-        self.assertEqual(form.initial['password'], form['password'].value())\n-\n-    def test_custom_form(self):\n-        class CustomUserChangeForm(UserChangeForm):\n-            class Meta(UserChangeForm.Meta):\n-                model = ExtensionUser\n-                fields = ('username', 'password', 'date_of_birth',)\n-\n-        user = User.objects.get(username='testclient')\n-        data = {\n-            'username': 'testclient',\n-            'password': 'testclient',\n-            'date_of_birth': '1998-02-24',\n-        }\n-        form = CustomUserChangeForm(data, instance=user)\n-        self.assertTrue(form.is_valid())\n-        form.save()\n-        self.assertEqual(form.cleaned_data['username'], 'testclient')\n-        self.assertEqual(form.cleaned_data['date_of_birth'], datetime.date(1998, 2, 24))\n-\n-    def test_password_excluded(self):\n-        class UserChangeFormWithoutPassword(UserChangeForm):\n-            password = None\n-\n-            class Meta:\n-                model = User\n-                exclude = ['password']\n-\n-        form = UserChangeFormWithoutPassword()\n-        self.assertNotIn('password', form.fields)\n-\n-    def test_username_field_autocapitalize_none(self):\n-        form = UserChangeForm()\n-        self.assertEqual(form.fields['username'].widget.attrs.get('autocapitalize'), 'none')\n-\n-\n-@override_settings(TEMPLATES=AUTH_TEMPLATES)\n-class PasswordResetFormTest(TestDataMixin, TestCase):\n-\n-    @classmethod\n-    def setUpClass(cls):\n-        super().setUpClass()\n-        # This cleanup is necessary because contrib.sites cache\n-        # makes tests interfere with each other, see #11505\n-        Site.objects.clear_cache()\n-\n-    def create_dummy_user(self):\n-        \"\"\"\n-        Create a user and return a tuple (user_object, username, email).\n-        \"\"\"\n-        username = 'jsmith'\n-        email = '<EMAIL>'\n-        user = User.objects.create_user(username, email, 'test123')\n-        return (user, username, email)\n-\n-    def test_invalid_email(self):\n-        data = {'email': 'not valid'}\n-        form = PasswordResetForm(data)\n-        self.assertFalse(form.is_valid())\n-        self.assertEqual(form['email'].errors, [_('Enter a valid email address.')])\n-\n-    def test_nonexistent_email(self):\n-        \"\"\"\n-        Test nonexistent email address. This should not fail because it would\n-        expose information about registered users.\n-        \"\"\"\n-        data = {'email': '<EMAIL>'}\n-        form = PasswordResetForm(data)\n-        self.assertTrue(form.is_valid())\n-        self.assertEqual(len(mail.outbox), 0)\n-\n-    def test_cleaned_data(self):\n-        (user, username, email) = self.create_dummy_user()\n-        data = {'email': email}\n-        form = PasswordResetForm(data)\n-        self.assertTrue(form.is_valid())\n-        form.save(domain_override='example.com')\n-        self.assertEqual(form.cleaned_data['email'], email)\n-        self.assertEqual(len(mail.outbox), 1)\n-\n-    def test_custom_email_subject(self):\n-        data = {'email': '<EMAIL>'}\n-        form = PasswordResetForm(data)\n-        self.assertTrue(form.is_valid())\n-        # Since we're not providing a request object, we must provide a\n-        # domain_override to prevent the save operation from failing in the\n-        # potential case where contrib.sites is not installed. Refs #16412.\n-        form.save(domain_override='example.com')\n-        self.assertEqual(len(mail.outbox), 1)\n-        self.assertEqual(mail.outbox[0].subject, 'Custom password reset on example.com')\n-\n-    def test_custom_email_constructor(self):\n-        data = {'email': '<EMAIL>'}\n-\n-        class CustomEmailPasswordResetForm(PasswordResetForm):\n-            def send_mail(self, subject_template_name, email_template_name,\n-                          context, from_email, to_email,\n-                          html_email_template_name=None):\n-                EmailMultiAlternatives(\n-                    \"Forgot your password?\",\n-                    \"Sorry to hear you forgot your password.\",\n-                    None, [to_email],\n-                    ['<EMAIL>'],\n-                    headers={'Reply-To': '<EMAIL>'},\n-                    alternatives=[\n-                        (\"Really sorry to hear you forgot your password.\", \"text/html\")\n-                    ],\n-                ).send()\n-\n-        form = CustomEmailPasswordResetForm(data)\n-        self.assertTrue(form.is_valid())\n-        # Since we're not providing a request object, we must provide a\n-        # domain_override to prevent the save operation from failing in the\n-        # potential case where contrib.sites is not installed. Refs #16412.\n-        form.save(domain_override='example.com')\n-        self.assertEqual(len(mail.outbox), 1)\n-        self.assertEqual(mail.outbox[0].subject, 'Forgot your password?')\n-        self.assertEqual(mail.outbox[0].bcc, ['<EMAIL>'])\n-        self.assertEqual(mail.outbox[0].content_subtype, \"plain\")\n-\n-    def test_preserve_username_case(self):\n-        \"\"\"\n-        Preserve the case of the user name (before the @ in the email address)\n-        when creating a user (#5605).\n-        \"\"\"\n-        user = User.objects.create_user('forms_test2', '<EMAIL>', 'test')\n-        self.assertEqual(user.email, '<EMAIL>')\n-        user = User.objects.create_user('forms_test3', 'tesT', 'test')\n-        self.assertEqual(user.email, 'tesT')\n-\n-    def test_inactive_user(self):\n-        \"\"\"\n-        Inactive user cannot receive password reset email.\n-        \"\"\"\n-        (user, username, email) = self.create_dummy_user()\n-        user.is_active = False\n-        user.save()\n-        form = PasswordResetForm({'email': email})\n-        self.assertTrue(form.is_valid())\n-        form.save()\n-        self.assertEqual(len(mail.outbox), 0)\n-\n-    def test_unusable_password(self):\n-        user = User.objects.create_user('testuser', '<EMAIL>', 'test')\n-        data = {\"email\": \"<EMAIL>\"}\n-        form = PasswordResetForm(data)\n-        self.assertTrue(form.is_valid())\n-        user.set_unusable_password()\n-        user.save()\n-        form = PasswordResetForm(data)\n-        # The form itself is valid, but no email is sent\n-        self.assertTrue(form.is_valid())\n-        form.save()\n-        self.assertEqual(len(mail.outbox), 0)\n-\n-    def test_save_plaintext_email(self):\n-        \"\"\"\n-        Test the PasswordResetForm.save() method with no html_email_template_name\n-        parameter passed in.\n-        Test to ensure original behavior is unchanged after the parameter was added.\n-        \"\"\"\n-        (user, username, email) = self.create_dummy_user()\n-        form = PasswordResetForm({\"email\": email})\n-        self.assertTrue(form.is_valid())\n-        form.save()\n-        self.assertEqual(len(mail.outbox), 1)\n-        message = mail.outbox[0].message()\n-        self.assertFalse(message.is_multipart())\n-        self.assertEqual(message.get_content_type(), 'text/plain')\n-        self.assertEqual(message.get('subject'), 'Custom password reset on example.com')\n-        self.assertEqual(len(mail.outbox[0].alternatives), 0)\n-        self.assertEqual(message.get_all('to'), [email])\n-        self.assertTrue(re.match(r'^http://example.com/reset/[\\w+/-]', message.get_payload()))\n-\n-    def test_save_html_email_template_name(self):\n-        \"\"\"\n-        Test the PasswordResetForm.save() method with html_email_template_name\n-        parameter specified.\n-        Test to ensure that a multipart email is sent with both text/plain\n-        and text/html parts.\n-        \"\"\"\n-        (user, username, email) = self.create_dummy_user()\n-        form = PasswordResetForm({\"email\": email})\n-        self.assertTrue(form.is_valid())\n-        form.save(html_email_template_name='registration/html_password_reset_email.html')\n-        self.assertEqual(len(mail.outbox), 1)\n-        self.assertEqual(len(mail.outbox[0].alternatives), 1)\n-        message = mail.outbox[0].message()\n-        self.assertEqual(message.get('subject'), 'Custom password reset on example.com')\n-        self.assertEqual(len(message.get_payload()), 2)\n-        self.assertTrue(message.is_multipart())\n-        self.assertEqual(message.get_payload(0).get_content_type(), 'text/plain')\n-        self.assertEqual(message.get_payload(1).get_content_type(), 'text/html')\n-        self.assertEqual(message.get_all('to'), [email])\n-        self.assertTrue(re.match(r'^http://example.com/reset/[\\w/-]+', message.get_payload(0).get_payload()))\n-        self.assertTrue(re.match(\n-            r'^<html><a href=\"http://example.com/reset/[\\w/-]+/\">Link</a></html>$',\n-            message.get_payload(1).get_payload()\n-        ))\n-\n-    @override_settings(AUTH_USER_MODEL='auth_tests.CustomEmailField')\n-    def test_custom_email_field(self):\n-        email = '<EMAIL>'\n-        CustomEmailField.objects.create_user('test name', 'test password', email)\n-        form = PasswordResetForm({'email': email})\n-        self.assertTrue(form.is_valid())\n-        form.save()\n-        self.assertEqual(form.cleaned_data['email'], email)\n-        self.assertEqual(len(mail.outbox), 1)\n-        self.assertEqual(mail.outbox[0].to, [email])\n-\n-    def test_html_autocomplete_attributes(self):\n-        form = PasswordResetForm()\n-        self.assertEqual(form.fields['email'].widget.attrs['autocomplete'], 'email')\n-\n-\n-class ReadOnlyPasswordHashTest(SimpleTestCase):\n-\n-    def test_bug_19349_render_with_none_value(self):\n-        # Rendering the widget with value set to None\n-        # mustn't raise an exception.\n-        widget = ReadOnlyPasswordHashWidget()\n-        html = widget.render(name='password', value=None, attrs={})\n-        self.assertIn(_(\"No password set.\"), html)\n-\n-    @override_settings(PASSWORD_HASHERS=['django.contrib.auth.hashers.PBKDF2PasswordHasher'])\n-    def test_render(self):\n-        widget = ReadOnlyPasswordHashWidget()\n-        value = 'pbkdf2_sha256$100000$a6Pucb1qSFcD$WmCkn9Hqidj48NVe5x0FEM6A9YiOqQcl/83m2Z5udm0='\n-        self.assertHTMLEqual(\n-            widget.render('name', value, {'id': 'id_password'}),\n-            \"\"\"\n-            <div id=\"id_password\">\n-                <strong>algorithm</strong>: pbkdf2_sha256\n-                <strong>iterations</strong>: 100000\n-                <strong>salt</strong>: a6Pucb******\n-                <strong>hash</strong>: WmCkn9**************************************\n-            </div>\n-            \"\"\"\n-        )\n-\n-    def test_readonly_field_has_changed(self):\n-        field = ReadOnlyPasswordHashField()\n-        self.assertFalse(field.has_changed('aaa', 'bbb'))\n-\n-\n-class AdminPasswordChangeFormTest(TestDataMixin, TestCase):\n-\n-    @mock.patch('django.contrib.auth.password_validation.password_changed')\n-    def test_success(self, password_changed):\n-        user = User.objects.get(username='testclient')\n-        data = {\n-            'password1': 'test123',\n-            'password2': 'test123',\n-        }\n-        form = AdminPasswordChangeForm(user, data)\n-        self.assertTrue(form.is_valid())\n-        form.save(commit=False)\n-        self.assertEqual(password_changed.call_count, 0)\n-        form.save()\n-        self.assertEqual(password_changed.call_count, 1)\n-\n-    def test_password_whitespace_not_stripped(self):\n-        user = User.objects.get(username='testclient')\n-        data = {\n-            'password1': ' pass ',\n-            'password2': ' pass ',\n-        }\n-        form = AdminPasswordChangeForm(user, data)\n-        self.assertTrue(form.is_valid())\n-        self.assertEqual(form.cleaned_data['password1'], data['password1'])\n-        self.assertEqual(form.cleaned_data['password2'], data['password2'])\n-\n-    def test_non_matching_passwords(self):\n-        user = User.objects.get(username='testclient')\n-        data = {'password1': 'password1', 'password2': 'password2'}\n-        form = AdminPasswordChangeForm(user, data)\n-        self.assertEqual(form.errors['password2'], [form.error_messages['password_mismatch']])\n-\n-    def test_missing_passwords(self):\n-        user = User.objects.get(username='testclient')\n-        data = {'password1': '', 'password2': ''}\n-        form = AdminPasswordChangeForm(user, data)\n-        required_error = [Field.default_error_messages['required']]\n-        self.assertEqual(form.errors['password1'], required_error)\n-        self.assertEqual(form.errors['password2'], required_error)\n-\n-    def test_one_password(self):\n-        user = User.objects.get(username='testclient')\n-        form1 = AdminPasswordChangeForm(user, {'password1': '', 'password2': 'test'})\n-        required_error = [Field.default_error_messages['required']]\n-        self.assertEqual(form1.errors['password1'], required_error)\n-        self.assertNotIn('password2', form1.errors)\n-        form2 = AdminPasswordChangeForm(user, {'password1': 'test', 'password2': ''})\n-        self.assertEqual(form2.errors['password2'], required_error)\n-        self.assertNotIn('password1', form2.errors)\n-\n-    def test_html_autocomplete_attributes(self):\n-        user = User.objects.get(username='testclient')\n-        form = AdminPasswordChangeForm(user)\n-        tests = (\n-            ('password1', 'new-password'),\n-            ('password2', 'new-password'),\n-        )\n-        for field_name, autocomplete in tests:\n-            with self.subTest(field_name=field_name, autocomplete=autocomplete):\n-                self.assertEqual(form.fields[field_name].widget.attrs['autocomplete'], autocomplete)\n+        self.assertIn('password2', form.errors)\n\\ No newline at end of file\n", "proposed_model_patches": []}