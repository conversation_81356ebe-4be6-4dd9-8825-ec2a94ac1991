{"django__django-11848": {"patch_is_None": false, "patch_exists": true, "patch_successfully_applied": true, "resolved": false, "tests_status": {"FAIL_TO_PASS": {"success": [], "failure": ["test_parsing_rfc850 (utils_tests.test_http.HttpDateProcessingTests)", "test_parsing_year_less_than_70 (utils_tests.test_http.HttpDateProcessingTests)"]}, "PASS_TO_PASS": {"success": [], "failure": ["test_input_too_large (utils_tests.test_http.Base36IntTests)", "test_invalid_literal (utils_tests.test_http.Base36IntTests)", "test_negative_input (utils_tests.test_http.Base36IntTests)", "test_roundtrip (utils_tests.test_http.Base36IntTests)", "test_to_base36_errors (utils_tests.test_http.Base36IntTests)", "test_to_int_errors (utils_tests.test_http.Base36IntTests)", "test_values (utils_tests.test_http.Base36IntTests)", "test (utils_tests.test_http.EscapeLeadingSlashesTests)", "test_quote (utils_tests.test_http.URLQuoteTests)", "test_quote_plus (utils_tests.test_http.URLQuoteTests)", "test_unquote (utils_tests.test_http.URLQuoteTests)", "test_unquote_plus (utils_tests.test_http.URLQuoteTests)", "test_parsing (utils_tests.test_http.ETagProcessingTests)", "test_quoting (utils_tests.test_http.ETagProcessingTests)", "test_allowed_hosts_str (utils_tests.test_http.IsSafeURLTests)", "test_bad_urls (utils_tests.test_http.IsSafeURLTests)", "test_basic_auth (utils_tests.test_http.IsSafeURLTests)", "test_good_urls (utils_tests.test_http.IsSafeURLTests)", "test_is_safe_url_deprecated (utils_tests.test_http.IsSafeURLTests)", "test_no_allowed_hosts (utils_tests.test_http.IsSafeURLTests)", "test_secure_param_https_urls (utils_tests.test_http.IsSafeURLTests)", "test_secure_param_non_https_urls (utils_tests.test_http.IsSafeURLTests)", "test_bad (utils_tests.test_http.IsSameDomainTests)", "test_good (utils_tests.test_http.IsSameDomainTests)", "test_roundtrip (utils_tests.test_http.URLSafeBase64Tests)", "test_http_date (utils_tests.test_http.HttpDateProcessingTests)", "test_parsing_asctime (utils_tests.test_http.HttpDateProcessingTests)", "test_parsing_rfc1123 (utils_tests.test_http.HttpDateProcessingTests)", "test_custom_iterable_not_doseq (utils_tests.test_http.URLEncodeTests)", "test_dict (utils_tests.test_http.URLEncodeTests)", "test_dict_containing_empty_sequence_doseq (utils_tests.test_http.URLEncodeTests)", "test_dict_containing_sequence_doseq (utils_tests.test_http.URLEncodeTests)", "test_dict_containing_sequence_not_doseq (utils_tests.test_http.URLEncodeTests)", "test_dict_containing_tuple_not_doseq (utils_tests.test_http.URLEncodeTests)", "test_dict_with_bytearray (utils_tests.test_http.URLEncodeTests)", "test_dict_with_bytes_values (utils_tests.test_http.URLEncodeTests)", "test_dict_with_sequence_of_bytes (utils_tests.test_http.URLEncodeTests)", "test_generator (utils_tests.test_http.URLEncodeTests)", "test_multivaluedict (utils_tests.test_http.URLEncodeTests)", "test_none (utils_tests.test_http.URLEncodeTests)", "test_none_in_generator (utils_tests.test_http.URLEncodeTests)", "test_none_in_sequence (utils_tests.test_http.URLEncodeTests)", "test_tuples (utils_tests.test_http.URLEncodeTests)"]}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}}}