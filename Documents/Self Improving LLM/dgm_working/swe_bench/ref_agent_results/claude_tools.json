{"no_generation": ["django__django-10097", "django__django-14404", "django__django-14672", "django__django-15280", "django__django-15695", "django__django-7530", "matplotlib__matplotlib-13989", "matplotlib__matplotlib-26208", "pylint-dev__pylint-7080", "sphinx-doc__sphinx-7440", "sphinx-doc__sphinx-9229", "sympy__sympy-13551", "sympy__sympy-13757", "sympy__sympy-21379"], "no_logs": ["psf__requests-1142", "sphinx-doc__sphinx-8459", "sphinx-doc__sphinx-8595"], "resolved": ["astropy__astropy-12907", "astropy__astropy-13453", "astropy__astropy-13579", "astropy__astropy-14096", "astropy__astropy-14309", "astropy__astropy-14508", "astropy__astropy-14539", "astropy__astropy-14995", "astropy__astropy-7166", "astropy__astropy-7336", "astropy__astropy-7671", "django__django-10914", "django__django-10973", "django__django-11066", "django__django-11095", "django__django-11099", "django__django-11119", "django__django-11133", "django__django-11163", "django__django-11179", "django__django-11211", "django__django-11265", "django__django-11276", "django__django-11292", "django__django-11451", "django__django-11532", "django__django-11551", "django__django-11603", "django__django-11740", "django__django-11815", "django__django-11880", "django__django-11951", "django__django-11999", "django__django-12050", "django__django-12143", "django__django-12155", "django__django-12193", "django__django-12209", "django__django-12262", "django__django-12276", "django__django-12304", "django__django-12419", "django__django-12708", "django__django-12713", "django__django-12754", "django__django-13012", "django__django-13028", "django__django-13033", "django__django-13089", "django__django-13109", "django__django-13112", "django__django-13121", "django__django-13128", "django__django-13279", "django__django-13315", "django__django-13343", "django__django-13346", "django__django-13363", "django__django-13401", "django__django-13410", "django__django-13417", "django__django-13516", "django__django-13569", "django__django-13658", "django__django-13670", "django__django-13741", "django__django-13786", "django__django-13809", "django__django-13820", "django__django-13821", "django__django-13837", "django__django-13933", "django__django-13964", "django__django-14007", "django__django-14017", "django__django-14089", "django__django-14238", "django__django-14349", "django__django-14373", "django__django-14434", "django__django-14493", "django__django-14539", "django__django-14608", "django__django-14752", "django__django-14765", "django__django-14787", "django__django-14855", "django__django-14915", "django__django-15022", "django__django-15104", "django__django-15161", "django__django-15268", "django__django-15277", "django__django-15278", "django__django-15315", "django__django-15368", "django__django-15380", "django__django-15467", "django__django-15499", "django__django-15525", "django__django-15561", "django__django-15569", "django__django-15731", "django__django-15741", "django__django-15851", "django__django-15863", "django__django-15930", "django__django-15987", "django__django-16082", "django__django-16100", "django__django-16116", "django__django-16136", "django__django-16139", "django__django-16255", "django__django-16333", "django__django-16429", "django__django-16454", "django__django-16493", "django__django-16527", "django__django-16569", "django__django-16595", "django__django-16612", "django__django-16662", "django__django-16801", "django__django-16819", "django__django-16899", "django__django-16901", "django__django-17029", "django__django-17087", "django__django-9296", "matplotlib__matplotlib-22719", "matplotlib__matplotlib-23314", "matplotlib__matplotlib-23412", "matplotlib__matplotlib-24026", "matplotlib__matplotlib-24149", "matplotlib__matplotlib-24970", "matplotlib__matplotlib-25122", "matplotlib__matplotlib-25287", "matplotlib__matplotlib-25311", "matplotlib__matplotlib-26113", "matplotlib__matplotlib-26291", "matplotlib__matplotlib-26342", "pallets__flask-5014", "psf__requests-1766", "psf__requests-1921", "psf__requests-2317", "psf__requests-5414", "pydata__xarray-2905", "pydata__xarray-3151", "pydata__xarray-3305", "pydata__xarray-3677", "pydata__xarray-4075", "pydata__xarray-4356", "pydata__xarray-4629", "pydata__xarray-4695", "pydata__xarray-4966", "pydata__xarray-6461", "pydata__xarray-7233", "pydata__xarray-7393", "pylint-dev__pylint-6528", "pylint-dev__pylint-6903", "pylint-dev__pylint-7277", "pytest-dev__pytest-10081", "pytest-dev__pytest-5262", "pytest-dev__pytest-5631", "pytest-dev__pytest-5809", "pytest-dev__pytest-6202", "pytest-dev__pytest-7205", "pytest-dev__pytest-7490", "pytest-dev__pytest-7521", "pytest-dev__pytest-7571", "pytest-dev__pytest-7982", "pytest-dev__pytest-8399", "scikit-learn__scikit-learn-10297", "scikit-learn__scikit-learn-10844", "scikit-learn__scikit-learn-11310", "scikit-learn__scikit-learn-11578", "scikit-learn__scikit-learn-12585", "scikit-learn__scikit-learn-13124", "scikit-learn__scikit-learn-13135", "scikit-learn__scikit-learn-13142", "scikit-learn__scikit-learn-13328", "scikit-learn__scikit-learn-13439", "scikit-learn__scikit-learn-13496", "scikit-learn__scikit-learn-13779", "scikit-learn__scikit-learn-14053", "scikit-learn__scikit-learn-14087", "scikit-learn__scikit-learn-14141", "scikit-learn__scikit-learn-14496", "scikit-learn__scikit-learn-14710", "scikit-learn__scikit-learn-14894", "scikit-learn__scikit-learn-14983", "scikit-learn__scikit-learn-15100", "scikit-learn__scikit-learn-25931", "scikit-learn__scikit-learn-25973", "scikit-learn__scikit-learn-26323", "scikit-learn__scikit-learn-9288", "sphinx-doc__sphinx-10449", "sphinx-doc__sphinx-10466", "sphinx-doc__sphinx-7889", "sphinx-doc__sphinx-7985", "sphinx-doc__sphinx-8035", "sphinx-doc__sphinx-8269", "sphinx-doc__sphinx-8475", "sphinx-doc__sphinx-8721", "sphinx-doc__sphinx-9281", "sphinx-doc__sphinx-9320", "sphinx-doc__sphinx-9367", "sphinx-doc__sphinx-9698", "sphinx-doc__sphinx-9711", "sympy__sympy-11618", "sympy__sympy-12096", "sympy__sympy-12419", "sympy__sympy-12481", "sympy__sympy-13372", "sympy__sympy-13480", "sympy__sympy-13647", "sympy__sympy-13878", "sympy__sympy-14531", "sympy__sympy-14711", "sympy__sympy-14976", "sympy__sympy-15345", "sympy__sympy-15809", "sympy__sympy-16450", "sympy__sympy-16766", "sympy__sympy-16792", "sympy__sympy-16886", "sympy__sympy-17655", "sympy__sympy-18763", "sympy__sympy-19346", "sympy__sympy-19637", "sympy__sympy-19783", "sympy__sympy-19954", "sympy__sympy-20801", "sympy__sympy-21847", "sympy__sympy-22456", "sympy__sympy-22714", "sympy__sympy-22914", "sympy__sympy-23262", "sympy__sympy-23824", "sympy__sympy-23950", "sympy__sympy-24066", "sympy__sympy-24213", "sympy__sympy-24443", "sympy__sympy-24539"]}