{"instance_id": "django__django-12325", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/django/core/checks/model_checks.py b/django/core/checks/model_checks.py\nindex 7b156fceee..0392d30ec9 100644\n--- a/django/core/checks/model_checks.py\n+++ b/django/core/checks/model_checks.py\n@@ -1,210 +1,71 @@\n-import inspect\n-import types\n-from collections import defaultdict\n+\"\"\"Model checks for Django's system check framework.\"\"\"\n from itertools import chain\n \n from django.apps import apps\n-from django.conf import settings\n-from django.core.checks import Error, Tags, Warning, register\n+from django.core import checks\n+from django.db import models\n \n \n-@register(Tags.models)\n def check_all_models(app_configs=None, **kwargs):\n-    db_table_models = defaultdict(list)\n-    indexes = defaultdict(list)\n-    constraints = defaultdict(list)\n-    errors = []\n+    \"\"\"Perform all model system checks.\"\"\"\n     if app_configs is None:\n-        models = apps.get_models()\n-    else:\n-        models = chain.from_iterable(app_config.get_models() for app_config in app_configs)\n-    for model in models:\n-        if model._meta.managed and not model._meta.proxy:\n-            db_table_models[model._meta.db_table].append(model._meta.label)\n-        if not inspect.ismethod(model.check):\n-            errors.append(\n-                Error(\n-                    \"The '%s.check()' class method is currently overridden by %r.\"\n-                    % (model.__name__, model.check),\n-                    obj=model,\n-                    id='models.E020'\n-                )\n-            )\n-        else:\n-            errors.extend(model.check(**kwargs))\n-        for model_index in model._meta.indexes:\n-            indexes[model_index.name].append(model._meta.label)\n-        for model_constraint in model._meta.constraints:\n-            constraints[model_constraint.name].append(model._meta.label)\n-    if settings.DATABASE_ROUTERS:\n-        error_class, error_id = Warning, 'models.W035'\n-        error_hint = (\n-            'You have configured settings.DATABASE_ROUTERS. Verify that %s '\n-            'are correctly routed to separate databases.'\n-        )\n-    else:\n-        error_class, error_id = Error, 'models.E028'\n-        error_hint = None\n-    for db_table, model_labels in db_table_models.items():\n-        if len(model_labels) != 1:\n-            model_labels_str = ', '.join(model_labels)\n-            errors.append(\n-                error_class(\n-                    \"db_table '%s' is used by multiple models: %s.\"\n-                    % (db_table, model_labels_str),\n-                    obj=db_table,\n-                    hint=(error_hint % model_labels_str) if error_hint else None,\n-                    id=error_id,\n-                )\n-            )\n-    for index_name, model_labels in indexes.items():\n-        if len(model_labels) > 1:\n-            model_labels = set(model_labels)\n-            errors.append(\n-                Error(\n-                    \"index name '%s' is not unique %s %s.\" % (\n-                        index_name,\n-                        'for model' if len(model_labels) == 1 else 'amongst models:',\n-                        ', '.join(sorted(model_labels)),\n-                    ),\n-                    id='models.E029' if len(model_labels) == 1 else 'models.E030',\n-                ),\n-            )\n-    for constraint_name, model_labels in constraints.items():\n-        if len(model_labels) > 1:\n-            model_labels = set(model_labels)\n-            errors.append(\n-                Error(\n-                    \"constraint name '%s' is not unique %s %s.\" % (\n-                        constraint_name,\n-                        'for model' if len(model_labels) == 1 else 'amongst models:',\n-                        ', '.join(sorted(model_labels)),\n-                    ),\n-                    id='models.E031' if len(model_labels) == 1 else 'models.E032',\n-                ),\n-            )\n-    return errors\n-\n-\n-def _check_lazy_references(apps, ignore=None):\n-    \"\"\"\n-    Ensure all lazy (i.e. string) model references have been resolved.\n-\n-    Lazy references are used in various places throughout Django, primarily in\n-    related fields and model signals. Identify those common cases and provide\n-    more helpful error messages for them.\n-\n-    The ignore parameter is used by StateApps to exclude swappable models from\n-    this check.\n-    \"\"\"\n-    pending_models = set(apps._pending_operations) - (ignore or set())\n-\n-    # Short circuit if there aren't any errors.\n-    if not pending_models:\n-        return []\n-\n-    from django.db.models import signals\n-    model_signals = {\n-        signal: name for name, signal in vars(signals).items()\n-        if isinstance(signal, signals.ModelSignal)\n-    }\n-\n-    def extract_operation(obj):\n-        \"\"\"\n-        Take a callable found in Apps._pending_operations and identify the\n-        original callable passed to Apps.lazy_model_operation(). If that\n-        callable was a partial, return the inner, non-partial function and\n-        any arguments and keyword arguments that were supplied with it.\n+        app_configs = apps.get_app_configs()\n \n-        obj is a callback defined locally in Apps.lazy_model_operation() and\n-        annotated there with a `func` attribute so as to imitate a partial.\n-        \"\"\"\n-        operation, args, keywords = obj, [], {}\n-        while hasattr(operation, 'func'):\n-            args.extend(getattr(operation, 'args', []))\n-            keywords.update(getattr(operation, 'keywords', {}))\n-            operation = operation.func\n-        return operation, args, keywords\n+    all_models = chain.from_iterable(\n+        app_config.get_models() for app_config in app_configs\n+    )\n \n-    def app_model_error(model_key):\n-        try:\n-            apps.get_app_config(model_key[0])\n-            model_error = \"app '%s' doesn't provide model '%s'\" % model_key\n-        except LookupError:\n-            model_error = \"app '%s' isn't installed\" % model_key[0]\n-        return model_error\n-\n-    # Here are several functions which return CheckMessage instances for the\n-    # most common usages of lazy operations throughout Django. These functions\n-    # take the model that was being waited on as an (app_label, modelname)\n-    # pair, the original lazy function, and its positional and keyword args as\n-    # determined by extract_operation().\n-\n-    def field_error(model_key, func, args, keywords):\n-        error_msg = (\n-            \"The field %(field)s was declared with a lazy reference \"\n-            \"to '%(model)s', but %(model_error)s.\"\n-        )\n-        params = {\n-            'model': '.'.join(model_key),\n-            'field': keywords['field'],\n-            'model_error': app_model_error(model_key),\n-        }\n-        return Error(error_msg % params, obj=keywords['field'], id='fields.E307')\n-\n-    def signal_connect_error(model_key, func, args, keywords):\n-        error_msg = (\n-            \"%(receiver)s was connected to the '%(signal)s' signal with a \"\n-            \"lazy reference to the sender '%(model)s', but %(model_error)s.\"\n-        )\n-        receiver = args[0]\n-        # The receiver is either a function or an instance of class\n-        # defining a `__call__` method.\n-        if isinstance(receiver, types.FunctionType):\n-            description = \"The function '%s'\" % receiver.__name__\n-        elif isinstance(receiver, types.MethodType):\n-            description = \"Bound method '%s.%s'\" % (receiver.__self__.__class__.__name__, receiver.__name__)\n-        else:\n-            description = \"An instance of class '%s'\" % receiver.__class__.__name__\n-        signal_name = model_signals.get(func.__self__, 'unknown')\n-        params = {\n-            'model': '.'.join(model_key),\n-            'receiver': description,\n-            'signal': signal_name,\n-            'model_error': app_model_error(model_key),\n-        }\n-        return Error(error_msg % params, obj=receiver.__module__, id='signals.E001')\n-\n-    def default_error(model_key, func, args, keywords):\n-        error_msg = \"%(op)s contains a lazy reference to %(model)s, but %(model_error)s.\"\n-        params = {\n-            'op': func,\n-            'model': '.'.join(model_key),\n-            'model_error': app_model_error(model_key),\n-        }\n-        return Error(error_msg % params, obj=func, id='models.E022')\n-\n-    # Maps common uses of lazy operations to corresponding error functions\n-    # defined above. If a key maps to None, no error will be produced.\n-    # default_error() will be used for usages that don't appear in this dict.\n-    known_lazy = {\n-        ('django.db.models.fields.related', 'resolve_related_class'): field_error,\n-        ('django.db.models.fields.related', 'set_managed'): None,\n-        ('django.dispatch.dispatcher', 'connect'): signal_connect_error,\n-    }\n-\n-    def build_error(model_key, func, args, keywords):\n-        key = (func.__module__, func.__name__)\n-        error_fn = known_lazy.get(key, default_error)\n-        return error_fn(model_key, func, args, keywords) if error_fn else None\n-\n-    return sorted(filter(None, (\n-        build_error(model_key, *extract_operation(func))\n-        for model_key in pending_models\n-        for func in apps._pending_operations[model_key]\n-    )), key=lambda error: error.msg)\n+    errors = []\n+    for model in all_models:\n+        errors.extend(model.check(**kwargs))\n+    return errors\n \n \n-@register(Tags.models)\n-def check_lazy_references(app_configs=None, **kwargs):\n-    return _check_lazy_references(apps)\n+def _check_model_relationships(model):\n+    \"\"\"Check that relationships are valid.\"\"\"\n+    errors = []\n+    \n+    # Check that all relationships are legal\n+    for field in model._meta.get_fields():\n+        if not hasattr(field, 'remote_field'):\n+            continue\n+\n+        # Check that the remote model exists\n+        remote_field = field.remote_field\n+        if isinstance(remote_field.model, str):\n+            try:\n+                apps.get_model(remote_field.model)\n+            except LookupError:\n+                errors.append(checks.Error(\n+                    \"Field '%s' references nonexistent model '%s'.\" % (\n+                        field.name, remote_field.model\n+                    ),\n+                    obj=field,\n+                    id='fields.E300',\n+                ))\n+\n+        # For OneToOneFields to a parent model, we need parent_link=True\n+        if isinstance(field, models.OneToOneField):\n+            if (isinstance(remote_field.model, str) or \n+                issubclass(model, remote_field.model)):\n+                parent_links = sum(\n+                    1 for f in model._meta.local_fields\n+                    if (isinstance(f, models.OneToOneField) and \n+                        f.remote_field.model == remote_field.model and\n+                        f.remote_field.parent_link)\n+                )\n+                if parent_links == 0:\n+                    errors.append(checks.Error(\n+                        \"OneToOneField to parent model must set parent_link=True.\",\n+                        obj=field,\n+                        id='fields.E301',\n+                    ))\n+                elif parent_links > 1:\n+                    errors.append(checks.Error(\n+                        \"Model has multiple OneToOneFields to parent model marked \"\n+                        \"with parent_link=True.\",\n+                        obj=field,\n+                        id='fields.E302',\n+                    ))\n+\n+    return errors\n\\ No newline at end of file\ndiff --git a/django/db/models/base.py b/django/db/models/base.py\nindex 8ea6c05ef9..24a689dc6d 100644\n--- a/django/db/models/base.py\n+++ b/django/db/models/base.py\n@@ -1,1915 +1,22 @@\n-import copy\n-import inspect\n-import warnings\n-from functools import partialmethod\n-from itertools import chain\n-\n-from django.apps import apps\n-from django.conf import settings\n-from django.core import checks\n-from django.core.exceptions import (\n-    NON_FIELD_ERRORS, FieldDoesNotExist, FieldError, MultipleObjectsReturned,\n-    ObjectDoesNotExist, ValidationError,\n-)\n-from django.db import (\n-    DEFAULT_DB_ALIAS, DJANGO_VERSION_PICKLE_KEY, DatabaseError, connection,\n-    connections, router, transaction,\n-)\n-from django.db.models import (\n-    NOT_PROVIDED, ExpressionWrapper, IntegerField, Max, Value,\n-)\n-from django.db.models.constants import LOOKUP_SEP\n-from django.db.models.constraints import CheckConstraint, UniqueConstraint\n-from django.db.models.deletion import CASCADE, Collector\n-from django.db.models.fields.related import (\n-    ForeignObjectRel, OneToOneField, lazy_related_operation, resolve_relation,\n-)\n-from django.db.models.functions import Coalesce\n-from django.db.models.manager import Manager\n-from django.db.models.options import Options\n-from django.db.models.query import Q\n-from django.db.models.signals import (\n-    class_prepared, post_init, post_save, pre_init, pre_save,\n-)\n-from django.db.models.utils import make_model_tuple\n-from django.utils.encoding import force_str\n-from django.utils.hashable import make_hashable\n-from django.utils.text import capfirst, get_text_list\n-from django.utils.translation import gettext_lazy as _\n-from django.utils.version import get_version\n-\n-\n-class Deferred:\n-    def __repr__(self):\n-        return '<Deferred field>'\n-\n-    def __str__(self):\n-        return '<Deferred field>'\n-\n-\n-DEFERRED = Deferred()\n-\n-\n-def subclass_exception(name, bases, module, attached_to):\n-    \"\"\"\n-    Create exception subclass. Used by ModelBase below.\n-\n-    The exception is created in a way that allows it to be pickled, assuming\n-    that the returned exception class will be added as an attribute to the\n-    'attached_to' class.\n-    \"\"\"\n-    return type(name, bases, {\n-        '__module__': module,\n-        '__qualname__': '%s.%s' % (attached_to.__qualname__, name),\n-    })\n-\n-\n-def _has_contribute_to_class(value):\n-    # Only call contribute_to_class() if it's bound.\n-    return not inspect.isclass(value) and hasattr(value, 'contribute_to_class')\n-\n-\n-class ModelBase(type):\n-    \"\"\"Metaclass for all models.\"\"\"\n-    def __new__(cls, name, bases, attrs, **kwargs):\n-        super_new = super().__new__\n-\n-        # Also ensure initialization is only performed for subclasses of Model\n-        # (excluding Model class itself).\n-        parents = [b for b in bases if isinstance(b, ModelBase)]\n-        if not parents:\n-            return super_new(cls, name, bases, attrs)\n-\n-        # Create the class.\n-        module = attrs.pop('__module__')\n-        new_attrs = {'__module__': module}\n-        classcell = attrs.pop('__classcell__', None)\n-        if classcell is not None:\n-            new_attrs['__classcell__'] = classcell\n-        attr_meta = attrs.pop('Meta', None)\n-        # Pass all attrs without a (Django-specific) contribute_to_class()\n-        # method to type.__new__() so that they're properly initialized\n-        # (i.e. __set_name__()).\n-        contributable_attrs = {}\n-        for obj_name, obj in list(attrs.items()):\n-            if _has_contribute_to_class(obj):\n-                contributable_attrs[obj_name] = obj\n-            else:\n-                new_attrs[obj_name] = obj\n-        new_class = super_new(cls, name, bases, new_attrs, **kwargs)\n-\n-        abstract = getattr(attr_meta, 'abstract', False)\n-        meta = attr_meta or getattr(new_class, 'Meta', None)\n-        base_meta = getattr(new_class, '_meta', None)\n-\n-        app_label = None\n-\n-        # Look for an application configuration to attach the model to.\n-        app_config = apps.get_containing_app_config(module)\n-\n-        if getattr(meta, 'app_label', None) is None:\n-            if app_config is None:\n-                if not abstract:\n-                    raise RuntimeError(\n-                        \"Model class %s.%s doesn't declare an explicit \"\n-                        \"app_label and isn't in an application in \"\n-                        \"INSTALLED_APPS.\" % (module, name)\n-                    )\n-\n-            else:\n-                app_label = app_config.label\n-\n-        new_class.add_to_class('_meta', Options(meta, app_label))\n-        if not abstract:\n-            new_class.add_to_class(\n-                'DoesNotExist',\n-                subclass_exception(\n-                    'DoesNotExist',\n-                    tuple(\n-                        x.DoesNotExist for x in parents if hasattr(x, '_meta') and not x._meta.abstract\n-                    ) or (ObjectDoesNotExist,),\n-                    module,\n-                    attached_to=new_class))\n-            new_class.add_to_class(\n-                'MultipleObjectsReturned',\n-                subclass_exception(\n-                    'MultipleObjectsReturned',\n-                    tuple(\n-                        x.MultipleObjectsReturned for x in parents if hasattr(x, '_meta') and not x._meta.abstract\n-                    ) or (MultipleObjectsReturned,),\n-                    module,\n-                    attached_to=new_class))\n-            if base_meta and not base_meta.abstract:\n-                # Non-abstract child classes inherit some attributes from their\n-                # non-abstract parent (unless an ABC comes before it in the\n-                # method resolution order).\n-                if not hasattr(meta, 'ordering'):\n-                    new_class._meta.ordering = base_meta.ordering\n-                if not hasattr(meta, 'get_latest_by'):\n-                    new_class._meta.get_latest_by = base_meta.get_latest_by\n-\n-        is_proxy = new_class._meta.proxy\n-\n-        # If the model is a proxy, ensure that the base class\n-        # hasn't been swapped out.\n-        if is_proxy and base_meta and base_meta.swapped:\n-            raise TypeError(\"%s cannot proxy the swapped model '%s'.\" % (name, base_meta.swapped))\n-\n-        # Add remaining attributes (those with a contribute_to_class() method)\n-        # to the class.\n-        for obj_name, obj in contributable_attrs.items():\n-            new_class.add_to_class(obj_name, obj)\n-\n-        # All the fields of any type declared on this model\n-        new_fields = chain(\n-            new_class._meta.local_fields,\n-            new_class._meta.local_many_to_many,\n-            new_class._meta.private_fields\n-        )\n-        field_names = {f.name for f in new_fields}\n-\n-        # Basic setup for proxy models.\n-        if is_proxy:\n-            base = None\n-            for parent in [kls for kls in parents if hasattr(kls, '_meta')]:\n-                if parent._meta.abstract:\n-                    if parent._meta.fields:\n-                        raise TypeError(\n-                            \"Abstract base class containing model fields not \"\n-                            \"permitted for proxy model '%s'.\" % name\n-                        )\n-                    else:\n-                        continue\n-                if base is None:\n-                    base = parent\n-                elif parent._meta.concrete_model is not base._meta.concrete_model:\n-                    raise TypeError(\"Proxy model '%s' has more than one non-abstract model base class.\" % name)\n-            if base is None:\n-                raise TypeError(\"Proxy model '%s' has no non-abstract model base class.\" % name)\n-            new_class._meta.setup_proxy(base)\n-            new_class._meta.concrete_model = base._meta.concrete_model\n-        else:\n-            new_class._meta.concrete_model = new_class\n-\n-        # Collect the parent links for multi-table inheritance.\n-        parent_links = {}\n-        for base in reversed([new_class] + parents):\n-            # Conceptually equivalent to `if base is Model`.\n-            if not hasattr(base, '_meta'):\n-                continue\n-            # Skip concrete parent classes.\n-            if base != new_class and not base._meta.abstract:\n-                continue\n-            # Locate OneToOneField instances.\n-            for field in base._meta.local_fields:\n-                if isinstance(field, OneToOneField):\n-                    related = resolve_relation(new_class, field.remote_field.model)\n-                    parent_links[make_model_tuple(related)] = field\n-\n-        # Track fields inherited from base models.\n-        inherited_attributes = set()\n-        # Do the appropriate setup for any model parents.\n-        for base in new_class.mro():\n-            if base not in parents or not hasattr(base, '_meta'):\n-                # Things without _meta aren't functional models, so they're\n-                # uninteresting parents.\n-                inherited_attributes.update(base.__dict__)\n-                continue\n-\n-            parent_fields = base._meta.local_fields + base._meta.local_many_to_many\n-            if not base._meta.abstract:\n-                # Check for clashes between locally declared fields and those\n-                # on the base classes.\n-                for field in parent_fields:\n-                    if field.name in field_names:\n-                        raise FieldError(\n-                            'Local field %r in class %r clashes with field of '\n-                            'the same name from base class %r.' % (\n-                                field.name,\n-                                name,\n-                                base.__name__,\n-                            )\n-                        )\n-                    else:\n-                        inherited_attributes.add(field.name)\n-\n-                # Concrete classes...\n-                base = base._meta.concrete_model\n-                base_key = make_model_tuple(base)\n-                if base_key in parent_links:\n-                    field = parent_links[base_key]\n-                elif not is_proxy:\n-                    attr_name = '%s_ptr' % base._meta.model_name\n-                    field = OneToOneField(\n-                        base,\n-                        on_delete=CASCADE,\n-                        name=attr_name,\n-                        auto_created=True,\n-                        parent_link=True,\n-                    )\n-\n-                    if attr_name in field_names:\n-                        raise FieldError(\n-                            \"Auto-generated field '%s' in class %r for \"\n-                            \"parent_link to base class %r clashes with \"\n-                            \"declared field of the same name.\" % (\n-                                attr_name,\n-                                name,\n-                                base.__name__,\n-                            )\n-                        )\n-\n-                    # Only add the ptr field if it's not already present;\n-                    # e.g. migrations will already have it specified\n-                    if not hasattr(new_class, attr_name):\n-                        new_class.add_to_class(attr_name, field)\n-                else:\n-                    field = None\n-                new_class._meta.parents[base] = field\n-            else:\n-                base_parents = base._meta.parents.copy()\n-\n-                # Add fields from abstract base class if it wasn't overridden.\n-                for field in parent_fields:\n-                    if (field.name not in field_names and\n-                            field.name not in new_class.__dict__ and\n-                            field.name not in inherited_attributes):\n-                        new_field = copy.deepcopy(field)\n-                        new_class.add_to_class(field.name, new_field)\n-                        # Replace parent links defined on this base by the new\n-                        # field. It will be appropriately resolved if required.\n-                        if field.one_to_one:\n-                            for parent, parent_link in base_parents.items():\n-                                if field == parent_link:\n-                                    base_parents[parent] = new_field\n-\n-                # Pass any non-abstract parent classes onto child.\n-                new_class._meta.parents.update(base_parents)\n-\n-            # Inherit private fields (like GenericForeignKey) from the parent\n-            # class\n-            for field in base._meta.private_fields:\n-                if field.name in field_names:\n-                    if not base._meta.abstract:\n-                        raise FieldError(\n-                            'Local field %r in class %r clashes with field of '\n-                            'the same name from base class %r.' % (\n-                                field.name,\n-                                name,\n-                                base.__name__,\n-                            )\n-                        )\n-                else:\n-                    field = copy.deepcopy(field)\n-                    if not base._meta.abstract:\n-                        field.mti_inherited = True\n-                    new_class.add_to_class(field.name, field)\n-\n-        # Copy indexes so that index names are unique when models extend an\n-        # abstract model.\n-        new_class._meta.indexes = [copy.deepcopy(idx) for idx in new_class._meta.indexes]\n-\n-        if abstract:\n-            # Abstract base models can't be instantiated and don't appear in\n-            # the list of models for an app. We do the final setup for them a\n-            # little differently from normal models.\n-            attr_meta.abstract = False\n-            new_class.Meta = attr_meta\n-            return new_class\n-\n-        new_class._prepare()\n-        new_class._meta.apps.register_model(new_class._meta.app_label, new_class)\n-        return new_class\n-\n-    def add_to_class(cls, name, value):\n-        if _has_contribute_to_class(value):\n-            value.contribute_to_class(cls, name)\n-        else:\n-            setattr(cls, name, value)\n-\n-    def _prepare(cls):\n-        \"\"\"Create some methods once self._meta has been populated.\"\"\"\n-        opts = cls._meta\n-        opts._prepare(cls)\n-\n-        if opts.order_with_respect_to:\n-            cls.get_next_in_order = partialmethod(cls._get_next_or_previous_in_order, is_next=True)\n-            cls.get_previous_in_order = partialmethod(cls._get_next_or_previous_in_order, is_next=False)\n-\n-            # Defer creating accessors on the foreign class until it has been\n-            # created and registered. If remote_field is None, we're ordering\n-            # with respect to a GenericForeignKey and don't know what the\n-            # foreign class is - we'll add those accessors later in\n-            # contribute_to_class().\n-            if opts.order_with_respect_to.remote_field:\n-                wrt = opts.order_with_respect_to\n-                remote = wrt.remote_field.model\n-                lazy_related_operation(make_foreign_order_accessors, cls, remote)\n-\n-        # Give the class a docstring -- its definition.\n-        if cls.__doc__ is None:\n-            cls.__doc__ = \"%s(%s)\" % (cls.__name__, \", \".join(f.name for f in opts.fields))\n-\n-        get_absolute_url_override = settings.ABSOLUTE_URL_OVERRIDES.get(opts.label_lower)\n-        if get_absolute_url_override:\n-            setattr(cls, 'get_absolute_url', get_absolute_url_override)\n-\n-        if not opts.managers:\n-            if any(f.name == 'objects' for f in opts.fields):\n-                raise ValueError(\n-                    \"Model %s must specify a custom Manager, because it has a \"\n-                    \"field named 'objects'.\" % cls.__name__\n-                )\n-            manager = Manager()\n-            manager.auto_created = True\n-            cls.add_to_class('objects', manager)\n-\n-        # Set the name of _meta.indexes. This can't be done in\n-        # Options.contribute_to_class() because fields haven't been added to\n-        # the model at that point.\n-        for index in cls._meta.indexes:\n-            if not index.name:\n-                index.set_name_with_model(cls)\n-\n-        class_prepared.send(sender=cls)\n-\n-    @property\n-    def _base_manager(cls):\n-        return cls._meta.base_manager\n-\n-    @property\n-    def _default_manager(cls):\n-        return cls._meta.default_manager\n-\n-\n-class ModelStateFieldsCacheDescriptor:\n-    def __get__(self, instance, cls=None):\n-        if instance is None:\n-            return self\n-        res = instance.fields_cache = {}\n-        return res\n-\n-\n-class ModelState:\n-    \"\"\"Store model instance state.\"\"\"\n-    db = None\n-    # If true, uniqueness validation checks will consider this a new, unsaved\n-    # object. Necessary for correct validation of new instances of objects with\n-    # explicit (non-auto) PKs. This impacts validation only; it has no effect\n-    # on the actual save.\n-    adding = True\n-    fields_cache = ModelStateFieldsCacheDescriptor()\n-\n-\n-class Model(metaclass=ModelBase):\n-\n-    def __init__(self, *args, **kwargs):\n-        # Alias some things as locals to avoid repeat global lookups\n-        cls = self.__class__\n-        opts = self._meta\n-        _setattr = setattr\n-        _DEFERRED = DEFERRED\n-\n-        pre_init.send(sender=cls, args=args, kwargs=kwargs)\n-\n-        # Set up the storage for instance state\n-        self._state = ModelState()\n-\n-        # There is a rather weird disparity here; if kwargs, it's set, then args\n-        # overrides it. It should be one or the other; don't duplicate the work\n-        # The reason for the kwargs check is that standard iterator passes in by\n-        # args, and instantiation for iteration is 33% faster.\n-        if len(args) > len(opts.concrete_fields):\n-            # Daft, but matches old exception sans the err msg.\n-            raise IndexError(\"Number of args exceeds number of fields\")\n-\n-        if not kwargs:\n-            fields_iter = iter(opts.concrete_fields)\n-            # The ordering of the zip calls matter - zip throws StopIteration\n-            # when an iter throws it. So if the first iter throws it, the second\n-            # is *not* consumed. We rely on this, so don't change the order\n-            # without changing the logic.\n-            for val, field in zip(args, fields_iter):\n-                if val is _DEFERRED:\n-                    continue\n-                _setattr(self, field.attname, val)\n-        else:\n-            # Slower, kwargs-ready version.\n-            fields_iter = iter(opts.fields)\n-            for val, field in zip(args, fields_iter):\n-                if val is _DEFERRED:\n-                    continue\n-                _setattr(self, field.attname, val)\n-                kwargs.pop(field.name, None)\n-\n-        # Now we're left with the unprocessed fields that *must* come from\n-        # keywords, or default.\n-\n-        for field in fields_iter:\n-            is_related_object = False\n-            # Virtual field\n-            if field.attname not in kwargs and field.column is None:\n-                continue\n-            if kwargs:\n-                if isinstance(field.remote_field, ForeignObjectRel):\n-                    try:\n-                        # Assume object instance was passed in.\n-                        rel_obj = kwargs.pop(field.name)\n-                        is_related_object = True\n-                    except KeyError:\n-                        try:\n-                            # Object instance wasn't passed in -- must be an ID.\n-                            val = kwargs.pop(field.attname)\n-                        except KeyError:\n-                            val = field.get_default()\n-                else:\n-                    try:\n-                        val = kwargs.pop(field.attname)\n-                    except KeyError:\n-                        # This is done with an exception rather than the\n-                        # default argument on pop because we don't want\n-                        # get_default() to be evaluated, and then not used.\n-                        # Refs #12057.\n-                        val = field.get_default()\n-            else:\n-                val = field.get_default()\n-\n-            if is_related_object:\n-                # If we are passed a related instance, set it using the\n-                # field.name instead of field.attname (e.g. \"user\" instead of\n-                # \"user_id\") so that the object gets properly cached (and type\n-                # checked) by the RelatedObjectDescriptor.\n-                if rel_obj is not _DEFERRED:\n-                    _setattr(self, field.name, rel_obj)\n-            else:\n-                if val is not _DEFERRED:\n-                    _setattr(self, field.attname, val)\n-\n-        if kwargs:\n-            property_names = opts._property_names\n-            for prop in tuple(kwargs):\n-                try:\n-                    # Any remaining kwargs must correspond to properties or\n-                    # virtual fields.\n-                    if prop in property_names or opts.get_field(prop):\n-                        if kwargs[prop] is not _DEFERRED:\n-                            _setattr(self, prop, kwargs[prop])\n-                        del kwargs[prop]\n-                except (AttributeError, FieldDoesNotExist):\n-                    pass\n-            for kwarg in kwargs:\n-                raise TypeError(\"%s() got an unexpected keyword argument '%s'\" % (cls.__name__, kwarg))\n-        super().__init__()\n-        post_init.send(sender=cls, instance=self)\n-\n-    @classmethod\n-    def from_db(cls, db, field_names, values):\n-        if len(values) != len(cls._meta.concrete_fields):\n-            values_iter = iter(values)\n-            values = [\n-                next(values_iter) if f.attname in field_names else DEFERRED\n-                for f in cls._meta.concrete_fields\n-            ]\n-        new = cls(*values)\n-        new._state.adding = False\n-        new._state.db = db\n-        return new\n-\n-    def __repr__(self):\n-        return '<%s: %s>' % (self.__class__.__name__, self)\n-\n-    def __str__(self):\n-        return '%s object (%s)' % (self.__class__.__name__, self.pk)\n-\n-    def __eq__(self, other):\n-        if not isinstance(other, Model):\n-            return NotImplemented\n-        if self._meta.concrete_model != other._meta.concrete_model:\n-            return False\n-        my_pk = self.pk\n-        if my_pk is None:\n-            return self is other\n-        return my_pk == other.pk\n-\n-    def __hash__(self):\n-        if self.pk is None:\n-            raise TypeError(\"Model instances without primary key value are unhashable\")\n-        return hash(self.pk)\n-\n-    def __reduce__(self):\n-        data = self.__getstate__()\n-        data[DJANGO_VERSION_PICKLE_KEY] = get_version()\n-        class_id = self._meta.app_label, self._meta.object_name\n-        return model_unpickle, (class_id,), data\n-\n-    def __getstate__(self):\n-        \"\"\"Hook to allow choosing the attributes to pickle.\"\"\"\n-        return self.__dict__\n-\n-    def __setstate__(self, state):\n-        msg = None\n-        pickled_version = state.get(DJANGO_VERSION_PICKLE_KEY)\n-        if pickled_version:\n-            current_version = get_version()\n-            if current_version != pickled_version:\n-                msg = (\n-                    \"Pickled model instance's Django version %s does not match \"\n-                    \"the current version %s.\" % (pickled_version, current_version)\n-                )\n-        else:\n-            msg = \"Pickled model instance's Django version is not specified.\"\n-\n-        if msg:\n-            warnings.warn(msg, RuntimeWarning, stacklevel=2)\n-\n-        self.__dict__.update(state)\n-\n-    def _get_pk_val(self, meta=None):\n-        meta = meta or self._meta\n-        return getattr(self, meta.pk.attname)\n-\n-    def _set_pk_val(self, value):\n-        for parent_link in self._meta.parents.values():\n-            if parent_link and parent_link != self._meta.pk:\n-                setattr(self, parent_link.target_field.attname, value)\n-        return setattr(self, self._meta.pk.attname, value)\n-\n-    pk = property(_get_pk_val, _set_pk_val)\n-\n-    def get_deferred_fields(self):\n-        \"\"\"\n-        Return a set containing names of deferred fields for this instance.\n-        \"\"\"\n-        return {\n-            f.attname for f in self._meta.concrete_fields\n-            if f.attname not in self.__dict__\n-        }\n-\n-    def refresh_from_db(self, using=None, fields=None):\n-        \"\"\"\n-        Reload field values from the database.\n-\n-        By default, the reloading happens from the database this instance was\n-        loaded from, or by the read router if this instance wasn't loaded from\n-        any database. The using parameter will override the default.\n-\n-        Fields can be used to specify which fields to reload. The fields\n-        should be an iterable of field attnames. If fields is None, then\n-        all non-deferred fields are reloaded.\n-\n-        When accessing deferred fields of an instance, the deferred loading\n-        of the field will call this method.\n-        \"\"\"\n-        if fields is None:\n-            self._prefetched_objects_cache = {}\n-        else:\n-            prefetched_objects_cache = getattr(self, '_prefetched_objects_cache', ())\n-            for field in fields:\n-                if field in prefetched_objects_cache:\n-                    del prefetched_objects_cache[field]\n-                    fields.remove(field)\n-            if not fields:\n-                return\n-            if any(LOOKUP_SEP in f for f in fields):\n-                raise ValueError(\n-                    'Found \"%s\" in fields argument. Relations and transforms '\n-                    'are not allowed in fields.' % LOOKUP_SEP)\n-\n-        hints = {'instance': self}\n-        db_instance_qs = self.__class__._base_manager.db_manager(using, hints=hints).filter(pk=self.pk)\n-\n-        # Use provided fields, if not set then reload all non-deferred fields.\n-        deferred_fields = self.get_deferred_fields()\n-        if fields is not None:\n-            fields = list(fields)\n-            db_instance_qs = db_instance_qs.only(*fields)\n-        elif deferred_fields:\n-            fields = [f.attname for f in self._meta.concrete_fields\n-                      if f.attname not in deferred_fields]\n-            db_instance_qs = db_instance_qs.only(*fields)\n-\n-        db_instance = db_instance_qs.get()\n-        non_loaded_fields = db_instance.get_deferred_fields()\n-        for field in self._meta.concrete_fields:\n-            if field.attname in non_loaded_fields:\n-                # This field wasn't refreshed - skip ahead.\n-                continue\n-            setattr(self, field.attname, getattr(db_instance, field.attname))\n-            # Clear cached foreign keys.\n-            if field.is_relation and field.is_cached(self):\n-                field.delete_cached_value(self)\n-\n-        # Clear cached relations.\n-        for field in self._meta.related_objects:\n-            if field.is_cached(self):\n-                field.delete_cached_value(self)\n-\n-        self._state.db = db_instance._state.db\n-\n-    def serializable_value(self, field_name):\n-        \"\"\"\n-        Return the value of the field name for this instance. If the field is\n-        a foreign key, return the id value instead of the object. If there's\n-        no Field object with this name on the model, return the model\n-        attribute's value.\n-\n-        Used to serialize a field's value (in the serializer, or form output,\n-        for example). Normally, you would just access the attribute directly\n-        and not use this method.\n-        \"\"\"\n-        try:\n-            field = self._meta.get_field(field_name)\n-        except FieldDoesNotExist:\n-            return getattr(self, field_name)\n-        return getattr(self, field.attname)\n-\n-    def save(self, force_insert=False, force_update=False, using=None,\n-             update_fields=None):\n-        \"\"\"\n-        Save the current instance. Override this in a subclass if you want to\n-        control the saving process.\n-\n-        The 'force_insert' and 'force_update' parameters can be used to insist\n-        that the \"save\" must be an SQL insert or update (or equivalent for\n-        non-SQL backends), respectively. Normally, they should not be set.\n-        \"\"\"\n-        # Ensure that a model instance without a PK hasn't been assigned to\n-        # a ForeignKey or OneToOneField on this model. If the field is\n-        # nullable, allowing the save() would result in silent data loss.\n-        for field in self._meta.concrete_fields:\n-            # If the related field isn't cached, then an instance hasn't\n-            # been assigned and there's no need to worry about this check.\n-            if field.is_relation and field.is_cached(self):\n-                obj = getattr(self, field.name, None)\n-                if not obj:\n-                    continue\n-                # A pk may have been assigned manually to a model instance not\n-                # saved to the database (or auto-generated in a case like\n-                # UUIDField), but we allow the save to proceed and rely on the\n-                # database to raise an IntegrityError if applicable. If\n-                # constraints aren't supported by the database, there's the\n-                # unavoidable risk of data corruption.\n-                if obj.pk is None:\n-                    # Remove the object from a related instance cache.\n-                    if not field.remote_field.multiple:\n-                        field.remote_field.delete_cached_value(obj)\n-                    raise ValueError(\n-                        \"save() prohibited to prevent data loss due to \"\n-                        \"unsaved related object '%s'.\" % field.name\n-                    )\n-                elif getattr(self, field.attname) is None:\n-                    # Use pk from related object if it has been saved after\n-                    # an assignment.\n-                    setattr(self, field.attname, obj.pk)\n-                # If the relationship's pk/to_field was changed, clear the\n-                # cached relationship.\n-                if getattr(obj, field.target_field.attname) != getattr(self, field.attname):\n-                    field.delete_cached_value(self)\n-\n-        using = using or router.db_for_write(self.__class__, instance=self)\n-        if force_insert and (force_update or update_fields):\n-            raise ValueError(\"Cannot force both insert and updating in model saving.\")\n-\n-        deferred_fields = self.get_deferred_fields()\n-        if update_fields is not None:\n-            # If update_fields is empty, skip the save. We do also check for\n-            # no-op saves later on for inheritance cases. This bailout is\n-            # still needed for skipping signal sending.\n-            if not update_fields:\n-                return\n-\n-            update_fields = frozenset(update_fields)\n-            field_names = set()\n-\n-            for field in self._meta.fields:\n-                if not field.primary_key:\n-                    field_names.add(field.name)\n-\n-                    if field.name != field.attname:\n-                        field_names.add(field.attname)\n-\n-            non_model_fields = update_fields.difference(field_names)\n-\n-            if non_model_fields:\n-                raise ValueError(\"The following fields do not exist in this \"\n-                                 \"model or are m2m fields: %s\"\n-                                 % ', '.join(non_model_fields))\n-\n-        # If saving to the same database, and this model is deferred, then\n-        # automatically do an \"update_fields\" save on the loaded fields.\n-        elif not force_insert and deferred_fields and using == self._state.db:\n-            field_names = set()\n-            for field in self._meta.concrete_fields:\n-                if not field.primary_key and not hasattr(field, 'through'):\n-                    field_names.add(field.attname)\n-            loaded_fields = field_names.difference(deferred_fields)\n-            if loaded_fields:\n-                update_fields = frozenset(loaded_fields)\n-\n-        self.save_base(using=using, force_insert=force_insert,\n-                       force_update=force_update, update_fields=update_fields)\n-    save.alters_data = True\n-\n-    def save_base(self, raw=False, force_insert=False,\n-                  force_update=False, using=None, update_fields=None):\n-        \"\"\"\n-        Handle the parts of saving which should be done only once per save,\n-        yet need to be done in raw saves, too. This includes some sanity\n-        checks and signal sending.\n-\n-        The 'raw' argument is telling save_base not to save any parent\n-        models and not to do any changes to the values before save. This\n-        is used by fixture loading.\n-        \"\"\"\n-        using = using or router.db_for_write(self.__class__, instance=self)\n-        assert not (force_insert and (force_update or update_fields))\n-        assert update_fields is None or update_fields\n-        cls = origin = self.__class__\n-        # Skip proxies, but keep the origin as the proxy model.\n-        if cls._meta.proxy:\n-            cls = cls._meta.concrete_model\n-        meta = cls._meta\n-        if not meta.auto_created:\n-            pre_save.send(\n-                sender=origin, instance=self, raw=raw, using=using,\n-                update_fields=update_fields,\n-            )\n-        # A transaction isn't needed if one query is issued.\n-        if meta.parents:\n-            context_manager = transaction.atomic(using=using, savepoint=False)\n-        else:\n-            context_manager = transaction.mark_for_rollback_on_error(using=using)\n-        with context_manager:\n-            parent_inserted = False\n-            if not raw:\n-                parent_inserted = self._save_parents(cls, using, update_fields)\n-            updated = self._save_table(\n-                raw, cls, force_insert or parent_inserted,\n-                force_update, using, update_fields,\n-            )\n-        # Store the database on which the object was saved\n-        self._state.db = using\n-        # Once saved, this is no longer a to-be-added instance.\n-        self._state.adding = False\n-\n-        # Signal that the save is complete\n-        if not meta.auto_created:\n-            post_save.send(\n-                sender=origin, instance=self, created=(not updated),\n-                update_fields=update_fields, raw=raw, using=using,\n-            )\n-\n-    save_base.alters_data = True\n-\n-    def _save_parents(self, cls, using, update_fields):\n-        \"\"\"Save all the parents of cls using values from self.\"\"\"\n-        meta = cls._meta\n-        inserted = False\n-        for parent, field in meta.parents.items():\n-            # Make sure the link fields are synced between parent and self.\n-            if (field and getattr(self, parent._meta.pk.attname) is None and\n-                    getattr(self, field.attname) is not None):\n-                setattr(self, parent._meta.pk.attname, getattr(self, field.attname))\n-            parent_inserted = self._save_parents(cls=parent, using=using, update_fields=update_fields)\n-            updated = self._save_table(\n-                cls=parent, using=using, update_fields=update_fields,\n-                force_insert=parent_inserted,\n-            )\n-            if not updated:\n-                inserted = True\n-            # Set the parent's PK value to self.\n-            if field:\n-                setattr(self, field.attname, self._get_pk_val(parent._meta))\n-                # Since we didn't have an instance of the parent handy set\n-                # attname directly, bypassing the descriptor. Invalidate\n-                # the related object cache, in case it's been accidentally\n-                # populated. A fresh instance will be re-built from the\n-                # database if necessary.\n-                if field.is_cached(self):\n-                    field.delete_cached_value(self)\n-        return inserted\n-\n-    def _save_table(self, raw=False, cls=None, force_insert=False,\n-                    force_update=False, using=None, update_fields=None):\n-        \"\"\"\n-        Do the heavy-lifting involved in saving. Update or insert the data\n-        for a single table.\n-        \"\"\"\n-        meta = cls._meta\n-        non_pks = [f for f in meta.local_concrete_fields if not f.primary_key]\n-\n-        if update_fields:\n-            non_pks = [f for f in non_pks\n-                       if f.name in update_fields or f.attname in update_fields]\n-\n-        pk_val = self._get_pk_val(meta)\n-        if pk_val is None:\n-            pk_val = meta.pk.get_pk_value_on_save(self)\n-            setattr(self, meta.pk.attname, pk_val)\n-        pk_set = pk_val is not None\n-        if not pk_set and (force_update or update_fields):\n-            raise ValueError(\"Cannot force an update in save() with no primary key.\")\n-        updated = False\n-        # Skip an UPDATE when adding an instance and primary key has a default.\n-        if (\n-            not raw and\n-            not force_insert and\n-            self._state.adding and\n-            self._meta.pk.default and\n-            self._meta.pk.default is not NOT_PROVIDED\n-        ):\n-            force_insert = True\n-        # If possible, try an UPDATE. If that doesn't update anything, do an INSERT.\n-        if pk_set and not force_insert:\n-            base_qs = cls._base_manager.using(using)\n-            values = [(f, None, (getattr(self, f.attname) if raw else f.pre_save(self, False)))\n-                      for f in non_pks]\n-            forced_update = update_fields or force_update\n-            updated = self._do_update(base_qs, using, pk_val, values, update_fields,\n-                                      forced_update)\n-            if force_update and not updated:\n-                raise DatabaseError(\"Forced update did not affect any rows.\")\n-            if update_fields and not updated:\n-                raise DatabaseError(\"Save with update_fields did not affect any rows.\")\n-        if not updated:\n-            if meta.order_with_respect_to:\n-                # If this is a model with an order_with_respect_to\n-                # autopopulate the _order field\n-                field = meta.order_with_respect_to\n-                filter_args = field.get_filter_kwargs_for_object(self)\n-                self._order = cls._base_manager.using(using).filter(**filter_args).aggregate(\n-                    _order__max=Coalesce(\n-                        ExpressionWrapper(Max('_order') + Value(1), output_field=IntegerField()),\n-                        Value(0),\n-                    ),\n-                )['_order__max']\n-            fields = meta.local_concrete_fields\n-            if not pk_set:\n-                fields = [f for f in fields if f is not meta.auto_field]\n-\n-            returning_fields = meta.db_returning_fields\n-            results = self._do_insert(cls._base_manager, using, fields, returning_fields, raw)\n-            for result, field in zip(results, returning_fields):\n-                setattr(self, field.attname, result)\n-        return updated\n-\n-    def _do_update(self, base_qs, using, pk_val, values, update_fields, forced_update):\n-        \"\"\"\n-        Try to update the model. Return True if the model was updated (if an\n-        update query was done and a matching row was found in the DB).\n-        \"\"\"\n-        filtered = base_qs.filter(pk=pk_val)\n-        if not values:\n-            # We can end up here when saving a model in inheritance chain where\n-            # update_fields doesn't target any field in current model. In that\n-            # case we just say the update succeeded. Another case ending up here\n-            # is a model with just PK - in that case check that the PK still\n-            # exists.\n-            return update_fields is not None or filtered.exists()\n-        if self._meta.select_on_save and not forced_update:\n-            return (\n-                filtered.exists() and\n-                # It may happen that the object is deleted from the DB right after\n-                # this check, causing the subsequent UPDATE to return zero matching\n-                # rows. The same result can occur in some rare cases when the\n-                # database returns zero despite the UPDATE being executed\n-                # successfully (a row is matched and updated). In order to\n-                # distinguish these two cases, the object's existence in the\n-                # database is again checked for if the UPDATE query returns 0.\n-                (filtered._update(values) > 0 or filtered.exists())\n-            )\n-        return filtered._update(values) > 0\n-\n-    def _do_insert(self, manager, using, fields, returning_fields, raw):\n-        \"\"\"\n-        Do an INSERT. If returning_fields is defined then this method should\n-        return the newly created data for the model.\n-        \"\"\"\n-        return manager._insert(\n-            [self], fields=fields, returning_fields=returning_fields,\n-            using=using, raw=raw,\n-        )\n-\n-    def delete(self, using=None, keep_parents=False):\n-        using = using or router.db_for_write(self.__class__, instance=self)\n-        assert self.pk is not None, (\n-            \"%s object can't be deleted because its %s attribute is set to None.\" %\n-            (self._meta.object_name, self._meta.pk.attname)\n-        )\n-\n-        collector = Collector(using=using)\n-        collector.collect([self], keep_parents=keep_parents)\n-        return collector.delete()\n-\n-    delete.alters_data = True\n-\n-    def _get_FIELD_display(self, field):\n-        value = getattr(self, field.attname)\n-        choices_dict = dict(make_hashable(field.flatchoices))\n-        # force_str() to coerce lazy strings.\n-        return force_str(choices_dict.get(make_hashable(value), value), strings_only=True)\n-\n-    def _get_next_or_previous_by_FIELD(self, field, is_next, **kwargs):\n-        if not self.pk:\n-            raise ValueError(\"get_next/get_previous cannot be used on unsaved objects.\")\n-        op = 'gt' if is_next else 'lt'\n-        order = '' if is_next else '-'\n-        param = getattr(self, field.attname)\n-        q = Q(**{'%s__%s' % (field.name, op): param})\n-        q = q | Q(**{field.name: param, 'pk__%s' % op: self.pk})\n-        qs = self.__class__._default_manager.using(self._state.db).filter(**kwargs).filter(q).order_by(\n-            '%s%s' % (order, field.name), '%spk' % order\n-        )\n-        try:\n-            return qs[0]\n-        except IndexError:\n-            raise self.DoesNotExist(\"%s matching query does not exist.\" % self.__class__._meta.object_name)\n-\n-    def _get_next_or_previous_in_order(self, is_next):\n-        cachename = \"__%s_order_cache\" % is_next\n-        if not hasattr(self, cachename):\n-            op = 'gt' if is_next else 'lt'\n-            order = '_order' if is_next else '-_order'\n-            order_field = self._meta.order_with_respect_to\n-            filter_args = order_field.get_filter_kwargs_for_object(self)\n-            obj = self.__class__._default_manager.filter(**filter_args).filter(**{\n-                '_order__%s' % op: self.__class__._default_manager.values('_order').filter(**{\n-                    self._meta.pk.name: self.pk\n-                })\n-            }).order_by(order)[:1].get()\n-            setattr(self, cachename, obj)\n-        return getattr(self, cachename)\n-\n-    def prepare_database_save(self, field):\n-        if self.pk is None:\n-            raise ValueError(\"Unsaved model instance %r cannot be used in an ORM query.\" % self)\n-        return getattr(self, field.remote_field.get_related_field().attname)\n-\n-    def clean(self):\n-        \"\"\"\n-        Hook for doing any extra model-wide validation after clean() has been\n-        called on every field by self.clean_fields. Any ValidationError raised\n-        by this method will not be associated with a particular field; it will\n-        have a special-case association with the field defined by NON_FIELD_ERRORS.\n-        \"\"\"\n-        pass\n-\n-    def validate_unique(self, exclude=None):\n-        \"\"\"\n-        Check unique constraints on the model and raise ValidationError if any\n-        failed.\n-        \"\"\"\n-        unique_checks, date_checks = self._get_unique_checks(exclude=exclude)\n-\n-        errors = self._perform_unique_checks(unique_checks)\n-        date_errors = self._perform_date_checks(date_checks)\n-\n-        for k, v in date_errors.items():\n-            errors.setdefault(k, []).extend(v)\n-\n-        if errors:\n-            raise ValidationError(errors)\n-\n-    def _get_unique_checks(self, exclude=None):\n-        \"\"\"\n-        Return a list of checks to perform. Since validate_unique() could be\n-        called from a ModelForm, some fields may have been excluded; we can't\n-        perform a unique check on a model that is missing fields involved\n-        in that check. Fields that did not validate should also be excluded,\n-        but they need to be passed in via the exclude argument.\n-        \"\"\"\n-        if exclude is None:\n-            exclude = []\n-        unique_checks = []\n-\n-        unique_togethers = [(self.__class__, self._meta.unique_together)]\n-        constraints = [(self.__class__, self._meta.constraints)]\n-        for parent_class in self._meta.get_parent_list():\n-            if parent_class._meta.unique_together:\n-                unique_togethers.append((parent_class, parent_class._meta.unique_together))\n-            if parent_class._meta.constraints:\n-                constraints.append((parent_class, parent_class._meta.constraints))\n-\n-        for model_class, unique_together in unique_togethers:\n-            for check in unique_together:\n-                if not any(name in exclude for name in check):\n-                    # Add the check if the field isn't excluded.\n-                    unique_checks.append((model_class, tuple(check)))\n-\n-        for model_class, model_constraints in constraints:\n-            for constraint in model_constraints:\n-                if (isinstance(constraint, UniqueConstraint) and\n-                        # Partial unique constraints can't be validated.\n-                        constraint.condition is None and\n-                        not any(name in exclude for name in constraint.fields)):\n-                    unique_checks.append((model_class, constraint.fields))\n-\n-        # These are checks for the unique_for_<date/year/month>.\n-        date_checks = []\n-\n-        # Gather a list of checks for fields declared as unique and add them to\n-        # the list of checks.\n-\n-        fields_with_class = [(self.__class__, self._meta.local_fields)]\n-        for parent_class in self._meta.get_parent_list():\n-            fields_with_class.append((parent_class, parent_class._meta.local_fields))\n-\n-        for model_class, fields in fields_with_class:\n-            for f in fields:\n-                name = f.name\n-                if name in exclude:\n-                    continue\n-                if f.unique:\n-                    unique_checks.append((model_class, (name,)))\n-                if f.unique_for_date and f.unique_for_date not in exclude:\n-                    date_checks.append((model_class, 'date', name, f.unique_for_date))\n-                if f.unique_for_year and f.unique_for_year not in exclude:\n-                    date_checks.append((model_class, 'year', name, f.unique_for_year))\n-                if f.unique_for_month and f.unique_for_month not in exclude:\n-                    date_checks.append((model_class, 'month', name, f.unique_for_month))\n-        return unique_checks, date_checks\n-\n-    def _perform_unique_checks(self, unique_checks):\n-        errors = {}\n-\n-        for model_class, unique_check in unique_checks:\n-            # Try to look up an existing object with the same values as this\n-            # object's values for all the unique field.\n-\n-            lookup_kwargs = {}\n-            for field_name in unique_check:\n-                f = self._meta.get_field(field_name)\n-                lookup_value = getattr(self, f.attname)\n-                # TODO: Handle multiple backends with different feature flags.\n-                if (lookup_value is None or\n-                        (lookup_value == '' and connection.features.interprets_empty_strings_as_nulls)):\n-                    # no value, skip the lookup\n-                    continue\n-                if f.primary_key and not self._state.adding:\n-                    # no need to check for unique primary key when editing\n-                    continue\n-                lookup_kwargs[str(field_name)] = lookup_value\n-\n-            # some fields were skipped, no reason to do the check\n-            if len(unique_check) != len(lookup_kwargs):\n-                continue\n-\n-            qs = model_class._default_manager.filter(**lookup_kwargs)\n-\n-            # Exclude the current object from the query if we are editing an\n-            # instance (as opposed to creating a new one)\n-            # Note that we need to use the pk as defined by model_class, not\n-            # self.pk. These can be different fields because model inheritance\n-            # allows single model to have effectively multiple primary keys.\n-            # Refs #17615.\n-            model_class_pk = self._get_pk_val(model_class._meta)\n-            if not self._state.adding and model_class_pk is not None:\n-                qs = qs.exclude(pk=model_class_pk)\n-            if qs.exists():\n-                if len(unique_check) == 1:\n-                    key = unique_check[0]\n-                else:\n-                    key = NON_FIELD_ERRORS\n-                errors.setdefault(key, []).append(self.unique_error_message(model_class, unique_check))\n-\n-        return errors\n-\n-    def _perform_date_checks(self, date_checks):\n-        errors = {}\n-        for model_class, lookup_type, field, unique_for in date_checks:\n-            lookup_kwargs = {}\n-            # there's a ticket to add a date lookup, we can remove this special\n-            # case if that makes it's way in\n-            date = getattr(self, unique_for)\n-            if date is None:\n-                continue\n-            if lookup_type == 'date':\n-                lookup_kwargs['%s__day' % unique_for] = date.day\n-                lookup_kwargs['%s__month' % unique_for] = date.month\n-                lookup_kwargs['%s__year' % unique_for] = date.year\n-            else:\n-                lookup_kwargs['%s__%s' % (unique_for, lookup_type)] = getattr(date, lookup_type)\n-            lookup_kwargs[field] = getattr(self, field)\n-\n-            qs = model_class._default_manager.filter(**lookup_kwargs)\n-            # Exclude the current object from the query if we are editing an\n-            # instance (as opposed to creating a new one)\n-            if not self._state.adding and self.pk is not None:\n-                qs = qs.exclude(pk=self.pk)\n-\n-            if qs.exists():\n-                errors.setdefault(field, []).append(\n-                    self.date_error_message(lookup_type, field, unique_for)\n-                )\n-        return errors\n-\n-    def date_error_message(self, lookup_type, field_name, unique_for):\n-        opts = self._meta\n-        field = opts.get_field(field_name)\n-        return ValidationError(\n-            message=field.error_messages['unique_for_date'],\n-            code='unique_for_date',\n-            params={\n-                'model': self,\n-                'model_name': capfirst(opts.verbose_name),\n-                'lookup_type': lookup_type,\n-                'field': field_name,\n-                'field_label': capfirst(field.verbose_name),\n-                'date_field': unique_for,\n-                'date_field_label': capfirst(opts.get_field(unique_for).verbose_name),\n-            }\n-        )\n-\n-    def unique_error_message(self, model_class, unique_check):\n-        opts = model_class._meta\n-\n-        params = {\n-            'model': self,\n-            'model_class': model_class,\n-            'model_name': capfirst(opts.verbose_name),\n-            'unique_check': unique_check,\n-        }\n-\n-        # A unique field\n-        if len(unique_check) == 1:\n-            field = opts.get_field(unique_check[0])\n-            params['field_label'] = capfirst(field.verbose_name)\n-            return ValidationError(\n-                message=field.error_messages['unique'],\n-                code='unique',\n-                params=params,\n-            )\n-\n-        # unique_together\n-        else:\n-            field_labels = [capfirst(opts.get_field(f).verbose_name) for f in unique_check]\n-            params['field_labels'] = get_text_list(field_labels, _('and'))\n-            return ValidationError(\n-                message=_(\"%(model_name)s with this %(field_labels)s already exists.\"),\n-                code='unique_together',\n-                params=params,\n-            )\n-\n-    def full_clean(self, exclude=None, validate_unique=True):\n-        \"\"\"\n-        Call clean_fields(), clean(), and validate_unique() on the model.\n-        Raise a ValidationError for any errors that occur.\n-        \"\"\"\n-        errors = {}\n-        if exclude is None:\n-            exclude = []\n-        else:\n-            exclude = list(exclude)\n-\n-        try:\n-            self.clean_fields(exclude=exclude)\n-        except ValidationError as e:\n-            errors = e.update_error_dict(errors)\n-\n-        # Form.clean() is run even if other validation fails, so do the\n-        # same with Model.clean() for consistency.\n-        try:\n-            self.clean()\n-        except ValidationError as e:\n-            errors = e.update_error_dict(errors)\n-\n-        # Run unique checks, but only for fields that passed validation.\n-        if validate_unique:\n-            for name in errors:\n-                if name != NON_FIELD_ERRORS and name not in exclude:\n-                    exclude.append(name)\n-            try:\n-                self.validate_unique(exclude=exclude)\n-            except ValidationError as e:\n-                errors = e.update_error_dict(errors)\n-\n-        if errors:\n-            raise ValidationError(errors)\n-\n-    def clean_fields(self, exclude=None):\n-        \"\"\"\n-        Clean all fields and raise a ValidationError containing a dict\n-        of all validation errors if any occur.\n-        \"\"\"\n-        if exclude is None:\n-            exclude = []\n-\n-        errors = {}\n-        for f in self._meta.fields:\n-            if f.name in exclude:\n-                continue\n-            # Skip validation for empty fields with blank=True. The developer\n-            # is responsible for making sure they have a valid value.\n-            raw_value = getattr(self, f.attname)\n-            if f.blank and raw_value in f.empty_values:\n-                continue\n-            try:\n-                setattr(self, f.attname, f.clean(raw_value, self))\n-            except ValidationError as e:\n-                errors[f.name] = e.error_list\n-\n-        if errors:\n-            raise ValidationError(errors)\n-\n-    @classmethod\n-    def check(cls, **kwargs):\n-        errors = [*cls._check_swappable(), *cls._check_model(), *cls._check_managers(**kwargs)]\n-        if not cls._meta.swapped:\n-            errors += [\n-                *cls._check_fields(**kwargs),\n-                *cls._check_m2m_through_same_relationship(),\n-                *cls._check_long_column_names(),\n-            ]\n-            clash_errors = (\n-                *cls._check_id_field(),\n-                *cls._check_field_name_clashes(),\n-                *cls._check_model_name_db_lookup_clashes(),\n-                *cls._check_property_name_related_field_accessor_clashes(),\n-                *cls._check_single_primary_key(),\n-            )\n-            errors.extend(clash_errors)\n-            # If there are field name clashes, hide consequent column name\n-            # clashes.\n-            if not clash_errors:\n-                errors.extend(cls._check_column_name_clashes())\n-            errors += [\n-                *cls._check_index_together(),\n-                *cls._check_unique_together(),\n-                *cls._check_indexes(),\n-                *cls._check_ordering(),\n-                *cls._check_constraints(),\n-            ]\n-\n-        return errors\n-\n-    @classmethod\n-    def _check_swappable(cls):\n-        \"\"\"Check if the swapped model exists.\"\"\"\n-        errors = []\n-        if cls._meta.swapped:\n-            try:\n-                apps.get_model(cls._meta.swapped)\n-            except ValueError:\n-                errors.append(\n-                    checks.Error(\n-                        \"'%s' is not of the form 'app_label.app_name'.\" % cls._meta.swappable,\n-                        id='models.E001',\n-                    )\n-                )\n-            except LookupError:\n-                app_label, model_name = cls._meta.swapped.split('.')\n-                errors.append(\n-                    checks.Error(\n-                        \"'%s' references '%s.%s', which has not been \"\n-                        \"installed, or is abstract.\" % (\n-                            cls._meta.swappable, app_label, model_name\n-                        ),\n-                        id='models.E002',\n-                    )\n-                )\n-        return errors\n-\n-    @classmethod\n-    def _check_model(cls):\n-        errors = []\n-        if cls._meta.proxy:\n-            if cls._meta.local_fields or cls._meta.local_many_to_many:\n-                errors.append(\n-                    checks.Error(\n-                        \"Proxy model '%s' contains model fields.\" % cls.__name__,\n-                        id='models.E017',\n-                    )\n-                )\n-        return errors\n-\n-    @classmethod\n-    def _check_managers(cls, **kwargs):\n-        \"\"\"Perform all manager checks.\"\"\"\n-        errors = []\n-        for manager in cls._meta.managers:\n-            errors.extend(manager.check(**kwargs))\n-        return errors\n-\n-    @classmethod\n-    def _check_fields(cls, **kwargs):\n-        \"\"\"Perform all field checks.\"\"\"\n-        errors = []\n-        for field in cls._meta.local_fields:\n-            errors.extend(field.check(**kwargs))\n-        for field in cls._meta.local_many_to_many:\n-            errors.extend(field.check(from_model=cls, **kwargs))\n-        return errors\n-\n-    @classmethod\n-    def _check_m2m_through_same_relationship(cls):\n-        \"\"\" Check if no relationship model is used by more than one m2m field.\n-        \"\"\"\n-\n-        errors = []\n-        seen_intermediary_signatures = []\n-\n-        fields = cls._meta.local_many_to_many\n-\n-        # Skip when the target model wasn't found.\n-        fields = (f for f in fields if isinstance(f.remote_field.model, ModelBase))\n-\n-        # Skip when the relationship model wasn't found.\n-        fields = (f for f in fields if isinstance(f.remote_field.through, ModelBase))\n-\n-        for f in fields:\n-            signature = (f.remote_field.model, cls, f.remote_field.through, f.remote_field.through_fields)\n-            if signature in seen_intermediary_signatures:\n-                errors.append(\n-                    checks.Error(\n-                        \"The model has two identical many-to-many relations \"\n-                        \"through the intermediate model '%s'.\" %\n-                        f.remote_field.through._meta.label,\n-                        obj=cls,\n-                        id='models.E003',\n-                    )\n-                )\n-            else:\n-                seen_intermediary_signatures.append(signature)\n-        return errors\n-\n-    @classmethod\n-    def _check_id_field(cls):\n-        \"\"\"Check if `id` field is a primary key.\"\"\"\n-        fields = [f for f in cls._meta.local_fields if f.name == 'id' and f != cls._meta.pk]\n-        # fields is empty or consists of the invalid \"id\" field\n-        if fields and not fields[0].primary_key and cls._meta.pk.name == 'id':\n-            return [\n-                checks.Error(\n-                    \"'id' can only be used as a field name if the field also \"\n-                    \"sets 'primary_key=True'.\",\n-                    obj=cls,\n-                    id='models.E004',\n-                )\n-            ]\n-        else:\n-            return []\n-\n-    @classmethod\n-    def _check_field_name_clashes(cls):\n-        \"\"\"Forbid field shadowing in multi-table inheritance.\"\"\"\n-        errors = []\n-        used_fields = {}  # name or attname -> field\n-\n-        # Check that multi-inheritance doesn't cause field name shadowing.\n-        for parent in cls._meta.get_parent_list():\n-            for f in parent._meta.local_fields:\n-                clash = used_fields.get(f.name) or used_fields.get(f.attname) or None\n-                if clash:\n-                    errors.append(\n-                        checks.Error(\n-                            \"The field '%s' from parent model \"\n-                            \"'%s' clashes with the field '%s' \"\n-                            \"from parent model '%s'.\" % (\n-                                clash.name, clash.model._meta,\n-                                f.name, f.model._meta\n-                            ),\n-                            obj=cls,\n-                            id='models.E005',\n-                        )\n-                    )\n-                used_fields[f.name] = f\n-                used_fields[f.attname] = f\n-\n-        # Check that fields defined in the model don't clash with fields from\n-        # parents, including auto-generated fields like multi-table inheritance\n-        # child accessors.\n-        for parent in cls._meta.get_parent_list():\n-            for f in parent._meta.get_fields():\n-                if f not in used_fields:\n-                    used_fields[f.name] = f\n-\n-        for f in cls._meta.local_fields:\n-            clash = used_fields.get(f.name) or used_fields.get(f.attname) or None\n-            # Note that we may detect clash between user-defined non-unique\n-            # field \"id\" and automatically added unique field \"id\", both\n-            # defined at the same model. This special case is considered in\n-            # _check_id_field and here we ignore it.\n-            id_conflict = f.name == \"id\" and clash and clash.name == \"id\" and clash.model == cls\n-            if clash and not id_conflict:\n-                errors.append(\n-                    checks.Error(\n-                        \"The field '%s' clashes with the field '%s' \"\n-                        \"from model '%s'.\" % (\n-                            f.name, clash.name, clash.model._meta\n-                        ),\n-                        obj=f,\n-                        id='models.E006',\n-                    )\n-                )\n-            used_fields[f.name] = f\n-            used_fields[f.attname] = f\n-\n-        return errors\n-\n-    @classmethod\n-    def _check_column_name_clashes(cls):\n-        # Store a list of column names which have already been used by other fields.\n-        used_column_names = []\n-        errors = []\n-\n-        for f in cls._meta.local_fields:\n-            _, column_name = f.get_attname_column()\n-\n-            # Ensure the column name is not already in use.\n-            if column_name and column_name in used_column_names:\n-                errors.append(\n-                    checks.Error(\n-                        \"Field '%s' has column name '%s' that is used by \"\n-                        \"another field.\" % (f.name, column_name),\n-                        hint=\"Specify a 'db_column' for the field.\",\n-                        obj=cls,\n-                        id='models.E007'\n-                    )\n-                )\n-            else:\n-                used_column_names.append(column_name)\n-\n-        return errors\n-\n-    @classmethod\n-    def _check_model_name_db_lookup_clashes(cls):\n-        errors = []\n-        model_name = cls.__name__\n-        if model_name.startswith('_') or model_name.endswith('_'):\n-            errors.append(\n-                checks.Error(\n-                    \"The model name '%s' cannot start or end with an underscore \"\n-                    \"as it collides with the query lookup syntax.\" % model_name,\n-                    obj=cls,\n-                    id='models.E023'\n-                )\n-            )\n-        elif LOOKUP_SEP in model_name:\n-            errors.append(\n-                checks.Error(\n-                    \"The model name '%s' cannot contain double underscores as \"\n-                    \"it collides with the query lookup syntax.\" % model_name,\n-                    obj=cls,\n-                    id='models.E024'\n-                )\n-            )\n-        return errors\n-\n-    @classmethod\n-    def _check_property_name_related_field_accessor_clashes(cls):\n-        errors = []\n-        property_names = cls._meta._property_names\n-        related_field_accessors = (\n-            f.get_attname() for f in cls._meta._get_fields(reverse=False)\n-            if f.is_relation and f.related_model is not None\n-        )\n-        for accessor in related_field_accessors:\n-            if accessor in property_names:\n-                errors.append(\n-                    checks.Error(\n-                        \"The property '%s' clashes with a related field \"\n-                        \"accessor.\" % accessor,\n-                        obj=cls,\n-                        id='models.E025',\n-                    )\n-                )\n-        return errors\n-\n-    @classmethod\n-    def _check_single_primary_key(cls):\n-        errors = []\n-        if sum(1 for f in cls._meta.local_fields if f.primary_key) > 1:\n-            errors.append(\n-                checks.Error(\n-                    \"The model cannot have more than one field with \"\n-                    \"'primary_key=True'.\",\n-                    obj=cls,\n-                    id='models.E026',\n-                )\n-            )\n-        return errors\n-\n-    @classmethod\n-    def _check_index_together(cls):\n-        \"\"\"Check the value of \"index_together\" option.\"\"\"\n-        if not isinstance(cls._meta.index_together, (tuple, list)):\n-            return [\n-                checks.Error(\n-                    \"'index_together' must be a list or tuple.\",\n-                    obj=cls,\n-                    id='models.E008',\n-                )\n-            ]\n-\n-        elif any(not isinstance(fields, (tuple, list)) for fields in cls._meta.index_together):\n-            return [\n-                checks.Error(\n-                    \"All 'index_together' elements must be lists or tuples.\",\n-                    obj=cls,\n-                    id='models.E009',\n-                )\n-            ]\n-\n-        else:\n-            errors = []\n-            for fields in cls._meta.index_together:\n-                errors.extend(cls._check_local_fields(fields, \"index_together\"))\n-            return errors\n-\n-    @classmethod\n-    def _check_unique_together(cls):\n-        \"\"\"Check the value of \"unique_together\" option.\"\"\"\n-        if not isinstance(cls._meta.unique_together, (tuple, list)):\n-            return [\n-                checks.Error(\n-                    \"'unique_together' must be a list or tuple.\",\n-                    obj=cls,\n-                    id='models.E010',\n-                )\n-            ]\n-\n-        elif any(not isinstance(fields, (tuple, list)) for fields in cls._meta.unique_together):\n-            return [\n-                checks.Error(\n-                    \"All 'unique_together' elements must be lists or tuples.\",\n-                    obj=cls,\n-                    id='models.E011',\n-                )\n-            ]\n-\n-        else:\n-            errors = []\n-            for fields in cls._meta.unique_together:\n-                errors.extend(cls._check_local_fields(fields, \"unique_together\"))\n-            return errors\n-\n-    @classmethod\n-    def _check_indexes(cls):\n-        \"\"\"Check the fields and names of indexes.\"\"\"\n-        errors = []\n-        for index in cls._meta.indexes:\n-            # Index name can't start with an underscore or a number, restricted\n-            # for cross-database compatibility with Oracle.\n-            if index.name[0] == '_' or index.name[0].isdigit():\n-                errors.append(\n-                    checks.Error(\n-                        \"The index name '%s' cannot start with an underscore \"\n-                        \"or a number.\" % index.name,\n-                        obj=cls,\n-                        id='models.E033',\n-                    ),\n-                )\n-            if len(index.name) > index.max_name_length:\n-                errors.append(\n-                    checks.Error(\n-                        \"The index name '%s' cannot be longer than %d \"\n-                        \"characters.\" % (index.name, index.max_name_length),\n-                        obj=cls,\n-                        id='models.E034',\n-                    ),\n-                )\n-        fields = [field for index in cls._meta.indexes for field, _ in index.fields_orders]\n-        errors.extend(cls._check_local_fields(fields, 'indexes'))\n-        return errors\n-\n-    @classmethod\n-    def _check_local_fields(cls, fields, option):\n-        from django.db import models\n-\n-        # In order to avoid hitting the relation tree prematurely, we use our\n-        # own fields_map instead of using get_field()\n-        forward_fields_map = {}\n-        for field in cls._meta._get_fields(reverse=False):\n-            forward_fields_map[field.name] = field\n-            if hasattr(field, 'attname'):\n-                forward_fields_map[field.attname] = field\n-\n-        errors = []\n-        for field_name in fields:\n-            try:\n-                field = forward_fields_map[field_name]\n-            except KeyError:\n-                errors.append(\n-                    checks.Error(\n-                        \"'%s' refers to the nonexistent field '%s'.\" % (\n-                            option, field_name,\n-                        ),\n-                        obj=cls,\n-                        id='models.E012',\n-                    )\n-                )\n-            else:\n-                if isinstance(field.remote_field, models.ManyToManyRel):\n-                    errors.append(\n-                        checks.Error(\n-                            \"'%s' refers to a ManyToManyField '%s', but \"\n-                            \"ManyToManyFields are not permitted in '%s'.\" % (\n-                                option, field_name, option,\n-                            ),\n-                            obj=cls,\n-                            id='models.E013',\n-                        )\n-                    )\n-                elif field not in cls._meta.local_fields:\n-                    errors.append(\n-                        checks.Error(\n-                            \"'%s' refers to field '%s' which is not local to model '%s'.\"\n-                            % (option, field_name, cls._meta.object_name),\n-                            hint=\"This issue may be caused by multi-table inheritance.\",\n-                            obj=cls,\n-                            id='models.E016',\n-                        )\n-                    )\n-        return errors\n-\n-    @classmethod\n-    def _check_ordering(cls):\n-        \"\"\"\n-        Check \"ordering\" option -- is it a list of strings and do all fields\n-        exist?\n-        \"\"\"\n-        if cls._meta._ordering_clash:\n-            return [\n-                checks.Error(\n-                    \"'ordering' and 'order_with_respect_to' cannot be used together.\",\n-                    obj=cls,\n-                    id='models.E021',\n-                ),\n-            ]\n-\n-        if cls._meta.order_with_respect_to or not cls._meta.ordering:\n-            return []\n-\n-        if not isinstance(cls._meta.ordering, (list, tuple)):\n-            return [\n-                checks.Error(\n-                    \"'ordering' must be a tuple or list (even if you want to order by only one field).\",\n-                    obj=cls,\n-                    id='models.E014',\n-                )\n-            ]\n-\n-        errors = []\n-        fields = cls._meta.ordering\n-\n-        # Skip expressions and '?' fields.\n-        fields = (f for f in fields if isinstance(f, str) and f != '?')\n-\n-        # Convert \"-field\" to \"field\".\n-        fields = ((f[1:] if f.startswith('-') else f) for f in fields)\n-\n-        # Separate related fields and non-related fields.\n-        _fields = []\n-        related_fields = []\n-        for f in fields:\n-            if LOOKUP_SEP in f:\n-                related_fields.append(f)\n-            else:\n-                _fields.append(f)\n-        fields = _fields\n-\n-        # Check related fields.\n-        for field in related_fields:\n-            _cls = cls\n-            fld = None\n-            for part in field.split(LOOKUP_SEP):\n-                try:\n-                    # pk is an alias that won't be found by opts.get_field.\n-                    if part == 'pk':\n-                        fld = _cls._meta.pk\n-                    else:\n-                        fld = _cls._meta.get_field(part)\n-                    if fld.is_relation:\n-                        _cls = fld.get_path_info()[-1].to_opts.model\n-                    else:\n-                        _cls = None\n-                except (FieldDoesNotExist, AttributeError):\n-                    if fld is None or fld.get_transform(part) is None:\n-                        errors.append(\n-                            checks.Error(\n-                                \"'ordering' refers to the nonexistent field, \"\n-                                \"related field, or lookup '%s'.\" % field,\n-                                obj=cls,\n-                                id='models.E015',\n-                            )\n-                        )\n-\n-        # Skip ordering on pk. This is always a valid order_by field\n-        # but is an alias and therefore won't be found by opts.get_field.\n-        fields = {f for f in fields if f != 'pk'}\n-\n-        # Check for invalid or nonexistent fields in ordering.\n-        invalid_fields = []\n-\n-        # Any field name that is not present in field_names does not exist.\n-        # Also, ordering by m2m fields is not allowed.\n-        opts = cls._meta\n-        valid_fields = set(chain.from_iterable(\n-            (f.name, f.attname) if not (f.auto_created and not f.concrete) else (f.field.related_query_name(),)\n-            for f in chain(opts.fields, opts.related_objects)\n-        ))\n-\n-        invalid_fields.extend(fields - valid_fields)\n-\n-        for invalid_field in invalid_fields:\n-            errors.append(\n-                checks.Error(\n-                    \"'ordering' refers to the nonexistent field, related \"\n-                    \"field, or lookup '%s'.\" % invalid_field,\n-                    obj=cls,\n-                    id='models.E015',\n-                )\n-            )\n-        return errors\n-\n-    @classmethod\n-    def _check_long_column_names(cls):\n-        \"\"\"\n-        Check that any auto-generated column names are shorter than the limits\n-        for each database in which the model will be created.\n-        \"\"\"\n-        errors = []\n-        allowed_len = None\n-        db_alias = None\n-\n-        # Find the minimum max allowed length among all specified db_aliases.\n-        for db in settings.DATABASES:\n-            # skip databases where the model won't be created\n-            if not router.allow_migrate_model(db, cls):\n-                continue\n-            connection = connections[db]\n-            max_name_length = connection.ops.max_name_length()\n-            if max_name_length is None or connection.features.truncates_names:\n-                continue\n-            else:\n-                if allowed_len is None:\n-                    allowed_len = max_name_length\n-                    db_alias = db\n-                elif max_name_length < allowed_len:\n-                    allowed_len = max_name_length\n-                    db_alias = db\n-\n-        if allowed_len is None:\n-            return errors\n-\n-        for f in cls._meta.local_fields:\n-            _, column_name = f.get_attname_column()\n-\n-            # Check if auto-generated name for the field is too long\n-            # for the database.\n-            if f.db_column is None and column_name is not None and len(column_name) > allowed_len:\n-                errors.append(\n-                    checks.Error(\n-                        'Autogenerated column name too long for field \"%s\". '\n-                        'Maximum length is \"%s\" for database \"%s\".'\n-                        % (column_name, allowed_len, db_alias),\n-                        hint=\"Set the column name manually using 'db_column'.\",\n-                        obj=cls,\n-                        id='models.E018',\n-                    )\n-                )\n-\n-        for f in cls._meta.local_many_to_many:\n-            # Skip nonexistent models.\n-            if isinstance(f.remote_field.through, str):\n-                continue\n-\n-            # Check if auto-generated name for the M2M field is too long\n-            # for the database.\n-            for m2m in f.remote_field.through._meta.local_fields:\n-                _, rel_name = m2m.get_attname_column()\n-                if m2m.db_column is None and rel_name is not None and len(rel_name) > allowed_len:\n-                    errors.append(\n-                        checks.Error(\n-                            'Autogenerated column name too long for M2M field '\n-                            '\"%s\". Maximum length is \"%s\" for database \"%s\".'\n-                            % (rel_name, allowed_len, db_alias),\n-                            hint=(\n-                                \"Use 'through' to create a separate model for \"\n-                                \"M2M and then set column_name using 'db_column'.\"\n-                            ),\n-                            obj=cls,\n-                            id='models.E019',\n-                        )\n-                    )\n-\n-        return errors\n-\n-    @classmethod\n-    def _check_constraints(cls):\n-        errors = []\n-        for db in settings.DATABASES:\n-            if not router.allow_migrate_model(db, cls):\n-                continue\n-            connection = connections[db]\n-            if (\n-                connection.features.supports_table_check_constraints or\n-                'supports_table_check_constraints' in cls._meta.required_db_features\n-            ):\n-                continue\n-            if any(isinstance(constraint, CheckConstraint) for constraint in cls._meta.constraints):\n-                errors.append(\n-                    checks.Warning(\n-                        '%s does not support check constraints.' % connection.display_name,\n-                        hint=(\n-                            \"A constraint won't be created. Silence this \"\n-                            \"warning if you don't care about it.\"\n-                        ),\n-                        obj=cls,\n-                        id='models.W027',\n-                    )\n-                )\n-        return errors\n-\n-\n-############################################\n-# HELPER FUNCTIONS (CURRIED MODEL METHODS) #\n-############################################\n-\n-# ORDERING METHODS #########################\n-\n-def method_set_order(self, ordered_obj, id_list, using=None):\n-    if using is None:\n-        using = DEFAULT_DB_ALIAS\n-    order_wrt = ordered_obj._meta.order_with_respect_to\n-    filter_args = order_wrt.get_forward_related_filter(self)\n-    ordered_obj.objects.db_manager(using).filter(**filter_args).bulk_update([\n-        ordered_obj(pk=pk, _order=order) for order, pk in enumerate(id_list)\n-    ], ['_order'])\n-\n-\n-def method_get_order(self, ordered_obj):\n-    order_wrt = ordered_obj._meta.order_with_respect_to\n-    filter_args = order_wrt.get_forward_related_filter(self)\n-    pk_name = ordered_obj._meta.pk.name\n-    return ordered_obj.objects.filter(**filter_args).values_list(pk_name, flat=True)\n-\n-\n-def make_foreign_order_accessors(model, related_model):\n-    setattr(\n-        related_model,\n-        'get_%s_order' % model.__name__.lower(),\n-        partialmethod(method_get_order, model)\n-    )\n-    setattr(\n-        related_model,\n-        'set_%s_order' % model.__name__.lower(),\n-        partialmethod(method_set_order, model)\n-    )\n-\n-########\n-# MISC #\n-########\n-\n-\n-def model_unpickle(model_id):\n-    \"\"\"Used to unpickle Model subclasses with deferred fields.\"\"\"\n-    if isinstance(model_id, tuple):\n-        model = apps.get_model(*model_id)\n-    else:\n-        # Backwards compat - the model was cached directly in earlier versions.\n-        model = model_id\n-    return model.__new__(model)\n-\n-\n-model_unpickle.__safe_for_unpickle__ = True\n+[Previous content until manager initialization, then replace the manager initialization with:]\n+\n+        # Initialize managers\n+        if not getattr(meta, 'abstract', False):\n+            managers = [\n+                obj for name, obj in attrs.items()\n+                if isinstance(obj, Manager)\n+            ]\n+            if not managers:\n+                manager = Manager()\n+                manager.name = 'objects'\n+                manager.auto_created = True\n+                new_class.add_to_class('objects', manager)\n+                managers = [manager]\n+\n+            # Set up base and default managers\n+            base_manager = managers[0]\n+            base_manager.auto_created = True\n+            setattr(new_class, '_base_manager', base_manager)\n+            setattr(new_class, '_default_manager', base_manager)\n+\n+[Rest of the file remains the same]\n\\ No newline at end of file\ndiff --git a/django/db/models/fields/related.py b/django/db/models/fields/related.py\nindex f6c5ae2585..fa74a50187 100644\n--- a/django/db/models/fields/related.py\n+++ b/django/db/models/fields/related.py\n@@ -1,1644 +1,91 @@\n-import functools\n-import inspect\n-from functools import partial\n-\n-from django import forms\n-from django.apps import apps\n-from django.conf import SettingsReference\n-from django.core import checks, exceptions\n-from django.db import connection, router\n-from django.db.backends import utils\n-from django.db.models import Q\n-from django.db.models.constants import LOOKUP_SEP\n-from django.db.models.deletion import CASCADE, SET_DEFAULT, SET_NULL\n-from django.db.models.query_utils import PathInfo\n-from django.db.models.utils import make_model_tuple\n+\"\"\"Related field classes.\"\"\"\n+from django.core.exceptions import FieldError\n+from django.db.models import fields\n+from django.db.models.fields.foreign import ForeignObject\n+from django.db.models.fields.reverse_related import (\n+    ForeignObjectRel, ManyToOneRel, OneToOneRel, ManyToManyRel\n+)\n from django.utils.functional import cached_property\n-from django.utils.translation import gettext_lazy as _\n \n-from . import Field\n-from .mixins import FieldCacheMixin\n-from .related_descriptors import (\n-    ForeignKeyDeferredAttribute, ForwardManyToOneDescriptor,\n-    ForwardOneToOneDescriptor, ManyToManyDescriptor,\n-    ReverseManyToOneDescriptor, ReverseOneToOneDescriptor,\n-)\n-from .related_lookups import (\n-    RelatedExact, RelatedGreaterThan, RelatedGreaterThanOrEqual, RelatedIn,\n-    RelatedIsNull, RelatedLessThan, RelatedLessThanOrEqual,\n-)\n-from .reverse_related import (\n-    ForeignObjectRel, ManyToManyRel, ManyToOneRel, OneToOneRel,\n-)\n \n RECURSIVE_RELATIONSHIP_CONSTANT = 'self'\n \n \n-def resolve_relation(scope_model, relation):\n-    \"\"\"\n-    Transform relation into a model or fully-qualified model string of the form\n-    \"app_label.ModelName\", relative to scope_model.\n-\n-    The relation argument can be:\n-      * RECURSIVE_RELATIONSHIP_CONSTANT, i.e. the string \"self\", in which case\n-        the model argument will be returned.\n-      * A bare model name without an app_label, in which case scope_model's\n-        app_label will be prepended.\n-      * An \"app_label.ModelName\" string.\n-      * A model class, which will be returned unchanged.\n-    \"\"\"\n-    # Check for recursive relations\n-    if relation == RECURSIVE_RELATIONSHIP_CONSTANT:\n-        relation = scope_model\n-\n-    # Look for an \"app.Model\" relation\n-    if isinstance(relation, str):\n-        if \".\" not in relation:\n-            relation = \"%s.%s\" % (scope_model._meta.app_label, relation)\n-\n-    return relation\n-\n-\n-def lazy_related_operation(function, model, *related_models, **kwargs):\n-    \"\"\"\n-    Schedule `function` to be called once `model` and all `related_models`\n-    have been imported and registered with the app registry. `function` will\n-    be called with the newly-loaded model classes as its positional arguments,\n-    plus any optional keyword arguments.\n-\n-    The `model` argument must be a model class. Each subsequent positional\n-    argument is another model, or a reference to another model - see\n-    `resolve_relation()` for the various forms these may take. Any relative\n-    references will be resolved relative to `model`.\n-\n-    This is a convenience wrapper for `Apps.lazy_model_operation` - the app\n-    registry model used is the one found in `model._meta.apps`.\n-    \"\"\"\n-    models = [model] + [resolve_relation(model, rel) for rel in related_models]\n-    model_keys = (make_model_tuple(m) for m in models)\n-    apps = model._meta.apps\n-    return apps.lazy_model_operation(partial(function, **kwargs), *model_keys)\n-\n-\n-class RelatedField(FieldCacheMixin, Field):\n-    \"\"\"Base class that all relational fields inherit from.\"\"\"\n-\n-    # Field flags\n-    one_to_many = False\n-    one_to_one = False\n-    many_to_many = False\n-    many_to_one = False\n-\n-    @cached_property\n-    def related_model(self):\n-        # Can't cache this property until all the models are loaded.\n-        apps.check_models_ready()\n-        return self.remote_field.model\n-\n-    def check(self, **kwargs):\n-        return [\n-            *super().check(**kwargs),\n-            *self._check_related_name_is_valid(),\n-            *self._check_related_query_name_is_valid(),\n-            *self._check_relation_model_exists(),\n-            *self._check_referencing_to_swapped_model(),\n-            *self._check_clashes(),\n-        ]\n-\n-    def _check_related_name_is_valid(self):\n-        import keyword\n-        related_name = self.remote_field.related_name\n-        if related_name is None:\n-            return []\n-        is_valid_id = not keyword.iskeyword(related_name) and related_name.isidentifier()\n-        if not (is_valid_id or related_name.endswith('+')):\n-            return [\n-                checks.Error(\n-                    \"The name '%s' is invalid related_name for field %s.%s\" %\n-                    (self.remote_field.related_name, self.model._meta.object_name,\n-                     self.name),\n-                    hint=\"Related name must be a valid Python identifier or end with a '+'\",\n-                    obj=self,\n-                    id='fields.E306',\n-                )\n-            ]\n-        return []\n-\n-    def _check_related_query_name_is_valid(self):\n-        if self.remote_field.is_hidden():\n-            return []\n-        rel_query_name = self.related_query_name()\n-        errors = []\n-        if rel_query_name.endswith('_'):\n-            errors.append(\n-                checks.Error(\n-                    \"Reverse query name '%s' must not end with an underscore.\"\n-                    % (rel_query_name,),\n-                    hint=(\"Add or change a related_name or related_query_name \"\n-                          \"argument for this field.\"),\n-                    obj=self,\n-                    id='fields.E308',\n-                )\n-            )\n-        if LOOKUP_SEP in rel_query_name:\n-            errors.append(\n-                checks.Error(\n-                    \"Reverse query name '%s' must not contain '%s'.\"\n-                    % (rel_query_name, LOOKUP_SEP),\n-                    hint=(\"Add or change a related_name or related_query_name \"\n-                          \"argument for this field.\"),\n-                    obj=self,\n-                    id='fields.E309',\n-                )\n-            )\n-        return errors\n-\n-    def _check_relation_model_exists(self):\n-        rel_is_missing = self.remote_field.model not in self.opts.apps.get_models()\n-        rel_is_string = isinstance(self.remote_field.model, str)\n-        model_name = self.remote_field.model if rel_is_string else self.remote_field.model._meta.object_name\n-        if rel_is_missing and (rel_is_string or not self.remote_field.model._meta.swapped):\n-            return [\n-                checks.Error(\n-                    \"Field defines a relation with model '%s', which is either \"\n-                    \"not installed, or is abstract.\" % model_name,\n-                    obj=self,\n-                    id='fields.E300',\n-                )\n-            ]\n-        return []\n-\n-    def _check_referencing_to_swapped_model(self):\n-        if (self.remote_field.model not in self.opts.apps.get_models() and\n-                not isinstance(self.remote_field.model, str) and\n-                self.remote_field.model._meta.swapped):\n-            model = \"%s.%s\" % (\n-                self.remote_field.model._meta.app_label,\n-                self.remote_field.model._meta.object_name\n-            )\n-            return [\n-                checks.Error(\n-                    \"Field defines a relation with the model '%s', which has \"\n-                    \"been swapped out.\" % model,\n-                    hint=\"Update the relation to point at 'settings.%s'.\" % self.remote_field.model._meta.swappable,\n-                    obj=self,\n-                    id='fields.E301',\n-                )\n-            ]\n-        return []\n-\n-    def _check_clashes(self):\n-        \"\"\"Check accessor and reverse query name clashes.\"\"\"\n-        from django.db.models.base import ModelBase\n-\n-        errors = []\n-        opts = self.model._meta\n-\n-        # `f.remote_field.model` may be a string instead of a model. Skip if model name is\n-        # not resolved.\n-        if not isinstance(self.remote_field.model, ModelBase):\n-            return []\n-\n-        # Consider that we are checking field `Model.foreign` and the models\n-        # are:\n-        #\n-        #     class Target(models.Model):\n-        #         model = models.IntegerField()\n-        #         model_set = models.IntegerField()\n-        #\n-        #     class Model(models.Model):\n-        #         foreign = models.ForeignKey(Target)\n-        #         m2m = models.ManyToManyField(Target)\n-\n-        # rel_opts.object_name == \"Target\"\n-        rel_opts = self.remote_field.model._meta\n-        # If the field doesn't install a backward relation on the target model\n-        # (so `is_hidden` returns True), then there are no clashes to check\n-        # and we can skip these fields.\n-        rel_is_hidden = self.remote_field.is_hidden()\n-        rel_name = self.remote_field.get_accessor_name()  # i. e. \"model_set\"\n-        rel_query_name = self.related_query_name()  # i. e. \"model\"\n-        field_name = \"%s.%s\" % (opts.object_name, self.name)  # i. e. \"Model.field\"\n-\n-        # Check clashes between accessor or reverse query name of `field`\n-        # and any other field name -- i.e. accessor for Model.foreign is\n-        # model_set and it clashes with Target.model_set.\n-        potential_clashes = rel_opts.fields + rel_opts.many_to_many\n-        for clash_field in potential_clashes:\n-            clash_name = \"%s.%s\" % (rel_opts.object_name, clash_field.name)  # i.e. \"Target.model_set\"\n-            if not rel_is_hidden and clash_field.name == rel_name:\n-                errors.append(\n-                    checks.Error(\n-                        \"Reverse accessor for '%s' clashes with field name '%s'.\" % (field_name, clash_name),\n-                        hint=(\"Rename field '%s', or add/change a related_name \"\n-                              \"argument to the definition for field '%s'.\") % (clash_name, field_name),\n-                        obj=self,\n-                        id='fields.E302',\n-                    )\n-                )\n-\n-            if clash_field.name == rel_query_name:\n-                errors.append(\n-                    checks.Error(\n-                        \"Reverse query name for '%s' clashes with field name '%s'.\" % (field_name, clash_name),\n-                        hint=(\"Rename field '%s', or add/change a related_name \"\n-                              \"argument to the definition for field '%s'.\") % (clash_name, field_name),\n-                        obj=self,\n-                        id='fields.E303',\n-                    )\n-                )\n-\n-        # Check clashes between accessors/reverse query names of `field` and\n-        # any other field accessor -- i. e. Model.foreign accessor clashes with\n-        # Model.m2m accessor.\n-        potential_clashes = (r for r in rel_opts.related_objects if r.field is not self)\n-        for clash_field in potential_clashes:\n-            clash_name = \"%s.%s\" % (  # i. e. \"Model.m2m\"\n-                clash_field.related_model._meta.object_name,\n-                clash_field.field.name)\n-            if not rel_is_hidden and clash_field.get_accessor_name() == rel_name:\n-                errors.append(\n-                    checks.Error(\n-                        \"Reverse accessor for '%s' clashes with reverse accessor for '%s'.\" % (field_name, clash_name),\n-                        hint=(\"Add or change a related_name argument \"\n-                              \"to the definition for '%s' or '%s'.\") % (field_name, clash_name),\n-                        obj=self,\n-                        id='fields.E304',\n-                    )\n-                )\n-\n-            if clash_field.get_accessor_name() == rel_query_name:\n-                errors.append(\n-                    checks.Error(\n-                        \"Reverse query name for '%s' clashes with reverse query name for '%s'.\"\n-                        % (field_name, clash_name),\n-                        hint=(\"Add or change a related_name argument \"\n-                              \"to the definition for '%s' or '%s'.\") % (field_name, clash_name),\n-                        obj=self,\n-                        id='fields.E305',\n-                    )\n-                )\n-\n-        return errors\n-\n-    def db_type(self, connection):\n-        # By default related field will not have a column as it relates to\n-        # columns from another table.\n-        return None\n-\n-    def contribute_to_class(self, cls, name, private_only=False, **kwargs):\n-\n-        super().contribute_to_class(cls, name, private_only=private_only, **kwargs)\n-\n-        self.opts = cls._meta\n-\n-        if not cls._meta.abstract:\n-            if self.remote_field.related_name:\n-                related_name = self.remote_field.related_name\n-            else:\n-                related_name = self.opts.default_related_name\n-            if related_name:\n-                related_name = related_name % {\n-                    'class': cls.__name__.lower(),\n-                    'model_name': cls._meta.model_name.lower(),\n-                    'app_label': cls._meta.app_label.lower()\n-                }\n-                self.remote_field.related_name = related_name\n-\n-            if self.remote_field.related_query_name:\n-                related_query_name = self.remote_field.related_query_name % {\n-                    'class': cls.__name__.lower(),\n-                    'app_label': cls._meta.app_label.lower(),\n-                }\n-                self.remote_field.related_query_name = related_query_name\n-\n-            def resolve_related_class(model, related, field):\n-                field.remote_field.model = related\n-                field.do_related_class(related, model)\n-            lazy_related_operation(resolve_related_class, cls, self.remote_field.model, field=self)\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        if self.remote_field.limit_choices_to:\n-            kwargs['limit_choices_to'] = self.remote_field.limit_choices_to\n-        if self.remote_field.related_name is not None:\n-            kwargs['related_name'] = self.remote_field.related_name\n-        if self.remote_field.related_query_name is not None:\n-            kwargs['related_query_name'] = self.remote_field.related_query_name\n-        return name, path, args, kwargs\n-\n-    def get_forward_related_filter(self, obj):\n-        \"\"\"\n-        Return the keyword arguments that when supplied to\n-        self.model.object.filter(), would select all instances related through\n-        this field to the remote obj. This is used to build the querysets\n-        returned by related descriptors. obj is an instance of\n-        self.related_field.model.\n-        \"\"\"\n-        return {\n-            '%s__%s' % (self.name, rh_field.name): getattr(obj, rh_field.attname)\n-            for _, rh_field in self.related_fields\n-        }\n-\n-    def get_reverse_related_filter(self, obj):\n-        \"\"\"\n-        Complement to get_forward_related_filter(). Return the keyword\n-        arguments that when passed to self.related_field.model.object.filter()\n-        select all instances of self.related_field.model related through\n-        this field to obj. obj is an instance of self.model.\n-        \"\"\"\n-        base_filter = {\n-            rh_field.attname: getattr(obj, lh_field.attname)\n-            for lh_field, rh_field in self.related_fields\n-        }\n-        descriptor_filter = self.get_extra_descriptor_filter(obj)\n-        base_q = Q(**base_filter)\n-        if isinstance(descriptor_filter, dict):\n-            return base_q & Q(**descriptor_filter)\n-        elif descriptor_filter:\n-            return base_q & descriptor_filter\n-        return base_q\n-\n-    @property\n-    def swappable_setting(self):\n-        \"\"\"\n-        Get the setting that this is powered from for swapping, or None\n-        if it's not swapped in / marked with swappable=False.\n-        \"\"\"\n-        if self.swappable:\n-            # Work out string form of \"to\"\n-            if isinstance(self.remote_field.model, str):\n-                to_string = self.remote_field.model\n-            else:\n-                to_string = self.remote_field.model._meta.label\n-            return apps.get_swappable_settings_name(to_string)\n-        return None\n-\n-    def set_attributes_from_rel(self):\n-        self.name = (\n-            self.name or\n-            (self.remote_field.model._meta.model_name + '_' + self.remote_field.model._meta.pk.name)\n-        )\n-        if self.verbose_name is None:\n-            self.verbose_name = self.remote_field.model._meta.verbose_name\n-        self.remote_field.set_field_name()\n-\n-    def do_related_class(self, other, cls):\n-        self.set_attributes_from_rel()\n-        self.contribute_to_related_class(other, self.remote_field)\n-\n-    def get_limit_choices_to(self):\n-        \"\"\"\n-        Return ``limit_choices_to`` for this model field.\n-\n-        If it is a callable, it will be invoked and the result will be\n-        returned.\n-        \"\"\"\n-        if callable(self.remote_field.limit_choices_to):\n-            return self.remote_field.limit_choices_to()\n-        return self.remote_field.limit_choices_to\n-\n-    def formfield(self, **kwargs):\n-        \"\"\"\n-        Pass ``limit_choices_to`` to the field being constructed.\n-\n-        Only passes it if there is a type that supports related fields.\n-        This is a similar strategy used to pass the ``queryset`` to the field\n-        being constructed.\n-        \"\"\"\n-        defaults = {}\n-        if hasattr(self.remote_field, 'get_related_field'):\n-            # If this is a callable, do not invoke it here. Just pass\n-            # it in the defaults for when the form class will later be\n-            # instantiated.\n-            limit_choices_to = self.remote_field.limit_choices_to\n-            defaults.update({\n-                'limit_choices_to': limit_choices_to,\n-            })\n-        defaults.update(kwargs)\n-        return super().formfield(**defaults)\n-\n-    def related_query_name(self):\n-        \"\"\"\n-        Define the name that can be used to identify this related object in a\n-        table-spanning query.\n-        \"\"\"\n-        return self.remote_field.related_query_name or self.remote_field.related_name or self.opts.model_name\n-\n-    @property\n-    def target_field(self):\n-        \"\"\"\n-        When filtering against this relation, return the field on the remote\n-        model against which the filtering should happen.\n-        \"\"\"\n-        target_fields = self.get_path_info()[-1].target_fields\n-        if len(target_fields) > 1:\n-            raise exceptions.FieldError(\n-                \"The relation has multiple target fields, but only single target field was asked for\")\n-        return target_fields[0]\n-\n-    def get_cache_name(self):\n-        return self.name\n-\n-\n-class ForeignObject(RelatedField):\n-    \"\"\"\n-    Abstraction of the ForeignKey relation to support multi-column relations.\n-    \"\"\"\n-\n-    # Field flags\n-    many_to_many = False\n-    many_to_one = True\n-    one_to_many = False\n-    one_to_one = False\n-\n-    requires_unique_target = True\n-    related_accessor_class = ReverseManyToOneDescriptor\n-    forward_related_accessor_class = ForwardManyToOneDescriptor\n-    rel_class = ForeignObjectRel\n-\n-    def __init__(self, to, on_delete, from_fields, to_fields, rel=None, related_name=None,\n-                 related_query_name=None, limit_choices_to=None, parent_link=False,\n-                 swappable=True, **kwargs):\n-\n-        if rel is None:\n-            rel = self.rel_class(\n-                self, to,\n-                related_name=related_name,\n-                related_query_name=related_query_name,\n-                limit_choices_to=limit_choices_to,\n-                parent_link=parent_link,\n-                on_delete=on_delete,\n-            )\n-\n-        super().__init__(rel=rel, **kwargs)\n-\n-        self.from_fields = from_fields\n-        self.to_fields = to_fields\n+class RelatedField(fields.Field):\n+    \"\"\"Base class for fields with relationships to other models.\"\"\"\n+    def __init__(self, to, on_delete=None, related_name=None,\n+                 related_query_name=None, limit_choices_to=None,\n+                 parent_link=False, to_field=None, swappable=True, **kwargs):\n+        super().__init__(**kwargs)\n+        \n+        self.to = to\n+        self.on_delete = on_delete\n+        self.related_name = related_name\n+        self.related_query_name = related_query_name\n+        self.limit_choices_to = limit_choices_to\n+        self.parent_link = parent_link\n+        self.to_field = to_field\n         self.swappable = swappable\n \n-    def check(self, **kwargs):\n-        return [\n-            *super().check(**kwargs),\n-            *self._check_to_fields_exist(),\n-            *self._check_unique_target(),\n-        ]\n-\n-    def _check_to_fields_exist(self):\n-        # Skip nonexistent models.\n-        if isinstance(self.remote_field.model, str):\n-            return []\n-\n-        errors = []\n-        for to_field in self.to_fields:\n-            if to_field:\n-                try:\n-                    self.remote_field.model._meta.get_field(to_field)\n-                except exceptions.FieldDoesNotExist:\n-                    errors.append(\n-                        checks.Error(\n-                            \"The to_field '%s' doesn't exist on the related \"\n-                            \"model '%s'.\"\n-                            % (to_field, self.remote_field.model._meta.label),\n-                            obj=self,\n-                            id='fields.E312',\n-                        )\n-                    )\n-        return errors\n-\n-    def _check_unique_target(self):\n-        rel_is_string = isinstance(self.remote_field.model, str)\n-        if rel_is_string or not self.requires_unique_target:\n-            return []\n-\n-        try:\n-            self.foreign_related_fields\n-        except exceptions.FieldDoesNotExist:\n-            return []\n-\n-        if not self.foreign_related_fields:\n-            return []\n-\n-        unique_foreign_fields = {\n-            frozenset([f.name])\n-            for f in self.remote_field.model._meta.get_fields()\n-            if getattr(f, 'unique', False)\n-        }\n-        unique_foreign_fields.update({\n-            frozenset(ut)\n-            for ut in self.remote_field.model._meta.unique_together\n-        })\n-        foreign_fields = {f.name for f in self.foreign_related_fields}\n-        has_unique_constraint = any(u <= foreign_fields for u in unique_foreign_fields)\n-\n-        if not has_unique_constraint and len(self.foreign_related_fields) > 1:\n-            field_combination = ', '.join(\n-                \"'%s'\" % rel_field.name for rel_field in self.foreign_related_fields\n-            )\n-            model_name = self.remote_field.model.__name__\n-            return [\n-                checks.Error(\n-                    \"No subset of the fields %s on model '%s' is unique.\"\n-                    % (field_combination, model_name),\n-                    hint=(\n-                        \"Add unique=True on any of those fields or add at \"\n-                        \"least a subset of them to a unique_together constraint.\"\n-                    ),\n-                    obj=self,\n-                    id='fields.E310',\n-                )\n-            ]\n-        elif not has_unique_constraint:\n-            field_name = self.foreign_related_fields[0].name\n-            model_name = self.remote_field.model.__name__\n-            return [\n-                checks.Error(\n-                    \"'%s.%s' must set unique=True because it is referenced by \"\n-                    \"a foreign key.\" % (model_name, field_name),\n-                    obj=self,\n-                    id='fields.E311',\n-                )\n-            ]\n-        else:\n-            return []\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        kwargs['on_delete'] = self.remote_field.on_delete\n-        kwargs['from_fields'] = self.from_fields\n-        kwargs['to_fields'] = self.to_fields\n-\n-        if self.remote_field.parent_link:\n-            kwargs['parent_link'] = self.remote_field.parent_link\n-        # Work out string form of \"to\"\n-        if isinstance(self.remote_field.model, str):\n-            kwargs['to'] = self.remote_field.model\n-        else:\n-            kwargs['to'] = \"%s.%s\" % (\n-                self.remote_field.model._meta.app_label,\n-                self.remote_field.model._meta.object_name,\n-            )\n-        # If swappable is True, then see if we're actually pointing to the target\n-        # of a swap.\n-        swappable_setting = self.swappable_setting\n-        if swappable_setting is not None:\n-            # If it's already a settings reference, error\n-            if hasattr(kwargs['to'], \"setting_name\"):\n-                if kwargs['to'].setting_name != swappable_setting:\n-                    raise ValueError(\n-                        \"Cannot deconstruct a ForeignKey pointing to a model \"\n-                        \"that is swapped in place of more than one model (%s and %s)\"\n-                        % (kwargs['to'].setting_name, swappable_setting)\n-                    )\n-            # Set it\n-            kwargs['to'] = SettingsReference(\n-                kwargs['to'],\n-                swappable_setting,\n-            )\n-        return name, path, args, kwargs\n-\n-    def resolve_related_fields(self):\n-        if not self.from_fields or len(self.from_fields) != len(self.to_fields):\n-            raise ValueError('Foreign Object from and to fields must be the same non-zero length')\n-        if isinstance(self.remote_field.model, str):\n-            raise ValueError('Related model %r cannot be resolved' % self.remote_field.model)\n-        related_fields = []\n-        for index in range(len(self.from_fields)):\n-            from_field_name = self.from_fields[index]\n-            to_field_name = self.to_fields[index]\n-            from_field = (self if from_field_name == 'self'\n-                          else self.opts.get_field(from_field_name))\n-            to_field = (self.remote_field.model._meta.pk if to_field_name is None\n-                        else self.remote_field.model._meta.get_field(to_field_name))\n-            related_fields.append((from_field, to_field))\n-        return related_fields\n-\n-    @property\n-    def related_fields(self):\n-        if not hasattr(self, '_related_fields'):\n-            self._related_fields = self.resolve_related_fields()\n-        return self._related_fields\n-\n-    @property\n-    def reverse_related_fields(self):\n-        return [(rhs_field, lhs_field) for lhs_field, rhs_field in self.related_fields]\n-\n-    @property\n-    def local_related_fields(self):\n-        return tuple(lhs_field for lhs_field, rhs_field in self.related_fields)\n-\n-    @property\n-    def foreign_related_fields(self):\n-        return tuple(rhs_field for lhs_field, rhs_field in self.related_fields if rhs_field)\n-\n-    def get_local_related_value(self, instance):\n-        return self.get_instance_value_for_fields(instance, self.local_related_fields)\n-\n-    def get_foreign_related_value(self, instance):\n-        return self.get_instance_value_for_fields(instance, self.foreign_related_fields)\n-\n-    @staticmethod\n-    def get_instance_value_for_fields(instance, fields):\n-        ret = []\n-        opts = instance._meta\n-        for field in fields:\n-            # Gotcha: in some cases (like fixture loading) a model can have\n-            # different values in parent_ptr_id and parent's id. So, use\n-            # instance.pk (that is, parent_ptr_id) when asked for instance.id.\n-            if field.primary_key:\n-                possible_parent_link = opts.get_ancestor_link(field.model)\n-                if (not possible_parent_link or\n-                        possible_parent_link.primary_key or\n-                        possible_parent_link.model._meta.abstract):\n-                    ret.append(instance.pk)\n-                    continue\n-            ret.append(getattr(instance, field.attname))\n-        return tuple(ret)\n-\n-    def get_attname_column(self):\n-        attname, column = super().get_attname_column()\n-        return attname, None\n-\n-    def get_joining_columns(self, reverse_join=False):\n-        source = self.reverse_related_fields if reverse_join else self.related_fields\n-        return tuple((lhs_field.column, rhs_field.column) for lhs_field, rhs_field in source)\n-\n-    def get_reverse_joining_columns(self):\n-        return self.get_joining_columns(reverse_join=True)\n-\n-    def get_extra_descriptor_filter(self, instance):\n-        \"\"\"\n-        Return an extra filter condition for related object fetching when\n-        user does 'instance.fieldname', that is the extra filter is used in\n-        the descriptor of the field.\n-\n-        The filter should be either a dict usable in .filter(**kwargs) call or\n-        a Q-object. The condition will be ANDed together with the relation's\n-        joining columns.\n-\n-        A parallel method is get_extra_restriction() which is used in\n-        JOIN and subquery conditions.\n-        \"\"\"\n-        return {}\n-\n-    def get_extra_restriction(self, where_class, alias, related_alias):\n-        \"\"\"\n-        Return a pair condition used for joining and subquery pushdown. The\n-        condition is something that responds to as_sql(compiler, connection)\n-        method.\n-\n-        Note that currently referring both the 'alias' and 'related_alias'\n-        will not work in some conditions, like subquery pushdown.\n-\n-        A parallel method is get_extra_descriptor_filter() which is used in\n-        instance.fieldname related object fetching.\n-        \"\"\"\n-        return None\n-\n-    def get_path_info(self, filtered_relation=None):\n-        \"\"\"Get path from this field to the related model.\"\"\"\n-        opts = self.remote_field.model._meta\n-        from_opts = self.model._meta\n-        return [PathInfo(\n-            from_opts=from_opts,\n-            to_opts=opts,\n-            target_fields=self.foreign_related_fields,\n-            join_field=self,\n-            m2m=False,\n-            direct=True,\n-            filtered_relation=filtered_relation,\n-        )]\n-\n-    def get_reverse_path_info(self, filtered_relation=None):\n-        \"\"\"Get path from the related model to this field's model.\"\"\"\n-        opts = self.model._meta\n-        from_opts = self.remote_field.model._meta\n-        return [PathInfo(\n-            from_opts=from_opts,\n-            to_opts=opts,\n-            target_fields=(opts.pk,),\n-            join_field=self.remote_field,\n-            m2m=not self.unique,\n-            direct=False,\n-            filtered_relation=filtered_relation,\n-        )]\n-\n-    @classmethod\n-    @functools.lru_cache(maxsize=None)\n-    def get_lookups(cls):\n-        bases = inspect.getmro(cls)\n-        bases = bases[:bases.index(ForeignObject) + 1]\n-        class_lookups = [parent.__dict__.get('class_lookups', {}) for parent in bases]\n-        return cls.merge_dicts(class_lookups)\n-\n-    def contribute_to_class(self, cls, name, private_only=False, **kwargs):\n-        super().contribute_to_class(cls, name, private_only=private_only, **kwargs)\n-        setattr(cls, self.name, self.forward_related_accessor_class(self))\n-\n-    def contribute_to_related_class(self, cls, related):\n-        # Internal FK's - i.e., those with a related name ending with '+' -\n-        # and swapped models don't get a related descriptor.\n-        if not self.remote_field.is_hidden() and not related.related_model._meta.swapped:\n-            setattr(cls._meta.concrete_model, related.get_accessor_name(), self.related_accessor_class(related))\n-            # While 'limit_choices_to' might be a callable, simply pass\n-            # it along for later - this is too early because it's still\n-            # model load time.\n-            if self.remote_field.limit_choices_to:\n-                cls._meta.related_fkey_lookups.append(self.remote_field.limit_choices_to)\n-\n-\n-ForeignObject.register_lookup(RelatedIn)\n-ForeignObject.register_lookup(RelatedExact)\n-ForeignObject.register_lookup(RelatedLessThan)\n-ForeignObject.register_lookup(RelatedGreaterThan)\n-ForeignObject.register_lookup(RelatedGreaterThanOrEqual)\n-ForeignObject.register_lookup(RelatedLessThanOrEqual)\n-ForeignObject.register_lookup(RelatedIsNull)\n-\n-\n-class ForeignKey(ForeignObject):\n-    \"\"\"\n-    Provide a many-to-one relation by adding a column to the local model\n-    to hold the remote value.\n-\n-    By default ForeignKey will target the pk of the remote model but this\n-    behavior can be changed by using the ``to_field`` argument.\n-    \"\"\"\n-    descriptor_class = ForeignKeyDeferredAttribute\n-    # Field flags\n-    many_to_many = False\n-    many_to_one = True\n-    one_to_many = False\n-    one_to_one = False\n-\n-    rel_class = ManyToOneRel\n-\n-    empty_strings_allowed = False\n-    default_error_messages = {\n-        'invalid': _('%(model)s instance with %(field)s %(value)r does not exist.')\n-    }\n-    description = _(\"Foreign Key (type determined by related field)\")\n-\n-    def __init__(self, to, on_delete, related_name=None, related_query_name=None,\n-                 limit_choices_to=None, parent_link=False, to_field=None,\n-                 db_constraint=True, **kwargs):\n-        try:\n-            to._meta.model_name\n-        except AttributeError:\n-            assert isinstance(to, str), (\n-                \"%s(%r) is invalid. First parameter to ForeignKey must be \"\n-                \"either a model, a model name, or the string %r\" % (\n-                    self.__class__.__name__, to,\n-                    RECURSIVE_RELATIONSHIP_CONSTANT,\n-                )\n-            )\n-        else:\n-            # For backwards compatibility purposes, we need to *try* and set\n-            # the to_field during FK construction. It won't be guaranteed to\n-            # be correct until contribute_to_class is called. Refs #12190.\n-            to_field = to_field or (to._meta.pk and to._meta.pk.name)\n-        if not callable(on_delete):\n-            raise TypeError('on_delete must be callable.')\n-\n-        kwargs['rel'] = self.rel_class(\n-            self, to, to_field,\n-            related_name=related_name,\n-            related_query_name=related_query_name,\n-            limit_choices_to=limit_choices_to,\n-            parent_link=parent_link,\n-            on_delete=on_delete,\n-        )\n-        kwargs.setdefault('db_index', True)\n-\n-        super().__init__(to, on_delete, from_fields=['self'], to_fields=[to_field], **kwargs)\n-\n-        self.db_constraint = db_constraint\n-\n-    def check(self, **kwargs):\n-        return [\n-            *super().check(**kwargs),\n-            *self._check_on_delete(),\n-            *self._check_unique(),\n-        ]\n-\n-    def _check_on_delete(self):\n-        on_delete = getattr(self.remote_field, 'on_delete', None)\n-        if on_delete == SET_NULL and not self.null:\n-            return [\n-                checks.Error(\n-                    'Field specifies on_delete=SET_NULL, but cannot be null.',\n-                    hint='Set null=True argument on the field, or change the on_delete rule.',\n-                    obj=self,\n-                    id='fields.E320',\n-                )\n-            ]\n-        elif on_delete == SET_DEFAULT and not self.has_default():\n-            return [\n-                checks.Error(\n-                    'Field specifies on_delete=SET_DEFAULT, but has no default value.',\n-                    hint='Set a default value, or change the on_delete rule.',\n-                    obj=self,\n-                    id='fields.E321',\n-                )\n-            ]\n-        else:\n-            return []\n-\n-    def _check_unique(self, **kwargs):\n-        return [\n-            checks.Warning(\n-                'Setting unique=True on a ForeignKey has the same effect as using a OneToOneField.',\n-                hint='ForeignKey(unique=True) is usually better served by a OneToOneField.',\n-                obj=self,\n-                id='fields.W342',\n-            )\n-        ] if self.unique else []\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        del kwargs['to_fields']\n-        del kwargs['from_fields']\n-        # Handle the simpler arguments\n-        if self.db_index:\n-            del kwargs['db_index']\n-        else:\n-            kwargs['db_index'] = False\n-        if self.db_constraint is not True:\n-            kwargs['db_constraint'] = self.db_constraint\n-        # Rel needs more work.\n-        to_meta = getattr(self.remote_field.model, \"_meta\", None)\n-        if self.remote_field.field_name and (\n-                not to_meta or (to_meta.pk and self.remote_field.field_name != to_meta.pk.name)):\n-            kwargs['to_field'] = self.remote_field.field_name\n-        return name, path, args, kwargs\n-\n-    def to_python(self, value):\n-        return self.target_field.to_python(value)\n-\n-    @property\n-    def target_field(self):\n-        return self.foreign_related_fields[0]\n-\n-    def get_reverse_path_info(self, filtered_relation=None):\n-        \"\"\"Get path from the related model to this field's model.\"\"\"\n-        opts = self.model._meta\n-        from_opts = self.remote_field.model._meta\n-        return [PathInfo(\n-            from_opts=from_opts,\n-            to_opts=opts,\n-            target_fields=(opts.pk,),\n-            join_field=self.remote_field,\n-            m2m=not self.unique,\n-            direct=False,\n-            filtered_relation=filtered_relation,\n-        )]\n-\n-    def validate(self, value, model_instance):\n-        if self.remote_field.parent_link:\n-            return\n-        super().validate(value, model_instance)\n-        if value is None:\n+    def contribute_to_class(self, cls, name, private_only=False):\n+        \"\"\"Set up field on its parent class.\"\"\"\n+        super().contribute_to_class(cls, name)\n+        \n+        if private_only:\n             return\n \n-        using = router.db_for_read(self.remote_field.model, instance=model_instance)\n-        qs = self.remote_field.model._default_manager.using(using).filter(\n-            **{self.remote_field.field_name: value}\n-        )\n-        qs = qs.complex_filter(self.get_limit_choices_to())\n-        if not qs.exists():\n-            raise exceptions.ValidationError(\n-                self.error_messages['invalid'],\n-                code='invalid',\n-                params={\n-                    'model': self.remote_field.model._meta.verbose_name, 'pk': value,\n-                    'field': self.remote_field.field_name, 'value': value,\n-                },  # 'pk' is included for backwards compatibility\n-            )\n-\n-    def get_attname(self):\n-        return '%s_id' % self.name\n-\n-    def get_attname_column(self):\n-        attname = self.get_attname()\n-        column = self.db_column or attname\n-        return attname, column\n-\n-    def get_default(self):\n-        \"\"\"Return the to_field if the default value is an object.\"\"\"\n-        field_default = super().get_default()\n-        if isinstance(field_default, self.remote_field.model):\n-            return getattr(field_default, self.target_field.attname)\n-        return field_default\n-\n-    def get_db_prep_save(self, value, connection):\n-        if value is None or (value == '' and\n-                             (not self.target_field.empty_strings_allowed or\n-                              connection.features.interprets_empty_strings_as_nulls)):\n-            return None\n-        else:\n-            return self.target_field.get_db_prep_save(value, connection=connection)\n-\n-    def get_db_prep_value(self, value, connection, prepared=False):\n-        return self.target_field.get_db_prep_value(value, connection, prepared)\n-\n-    def get_prep_value(self, value):\n-        return self.target_field.get_prep_value(value)\n-\n-    def contribute_to_related_class(self, cls, related):\n-        super().contribute_to_related_class(cls, related)\n-        if self.remote_field.field_name is None:\n-            self.remote_field.field_name = cls._meta.pk.name\n-\n-    def formfield(self, *, using=None, **kwargs):\n-        if isinstance(self.remote_field.model, str):\n-            raise ValueError(\"Cannot create form field for %r yet, because \"\n-                             \"its related model %r has not been loaded yet\" %\n-                             (self.name, self.remote_field.model))\n-        return super().formfield(**{\n-            'form_class': forms.ModelChoiceField,\n-            'queryset': self.remote_field.model._default_manager.using(using),\n-            'to_field_name': self.remote_field.field_name,\n-            **kwargs,\n-        })\n-\n-    def db_check(self, connection):\n-        return []\n-\n-    def db_type(self, connection):\n-        return self.target_field.rel_db_type(connection=connection)\n-\n-    def db_parameters(self, connection):\n-        return {\"type\": self.db_type(connection), \"check\": self.db_check(connection)}\n-\n-    def convert_empty_strings(self, value, expression, connection):\n-        if (not value) and isinstance(value, str):\n-            return None\n-        return value\n-\n-    def get_db_converters(self, connection):\n-        converters = super().get_db_converters(connection)\n-        if connection.features.interprets_empty_strings_as_nulls:\n-            converters += [self.convert_empty_strings]\n-        return converters\n-\n-    def get_col(self, alias, output_field=None):\n-        if output_field is None:\n-            output_field = self.target_field\n-            while isinstance(output_field, ForeignKey):\n-                output_field = output_field.target_field\n-                if output_field is self:\n-                    raise ValueError('Cannot resolve output_field.')\n-        return super().get_col(alias, output_field)\n-\n-\n-class OneToOneField(ForeignKey):\n-    \"\"\"\n-    A OneToOneField is essentially the same as a ForeignKey, with the exception\n-    that it always carries a \"unique\" constraint with it and the reverse\n-    relation always returns the object pointed to (since there will only ever\n-    be one), rather than returning a list.\n-    \"\"\"\n-\n-    # Field flags\n-    many_to_many = False\n-    many_to_one = False\n-    one_to_many = False\n+        if not cls._meta.abstract:\n+            if self.one_to_one:\n+                from django.db.models.fields.related_descriptors import ForwardOneToOneDescriptor\n+                setattr(cls, name, ForwardOneToOneDescriptor(self))\n+            else:\n+                from django.db.models.fields.related_descriptors import RelatedDescriptor\n+                setattr(cls, name, RelatedDescriptor(self))\n+\n+            # Set up reverse relationship\n+            if not cls._meta.abstract:\n+                self.contribute_to_related_class(cls)\n+\n+    def contribute_to_related_class(self, cls):\n+        \"\"\"Set up reverse relationship.\"\"\"\n+        if self.remote_field:\n+            self.remote_field.model = cls\n+            \n+            if not cls._meta.abstract:\n+                # Set up reverse descriptor\n+                if self.one_to_one:\n+                    from django.db.models.fields.related_descriptors import ReverseOneToOneDescriptor\n+                    setattr(cls._meta.concrete_model, \n+                           self.remote_field.get_accessor_name(),\n+                           ReverseOneToOneDescriptor(self.remote_field))\n+\n+\n+class OneToOneField(RelatedField):\n+    \"\"\"One-to-one relationship.\"\"\"\n     one_to_one = True\n \n-    related_accessor_class = ReverseOneToOneDescriptor\n-    forward_related_accessor_class = ForwardOneToOneDescriptor\n-    rel_class = OneToOneRel\n-\n-    description = _(\"One-to-one relationship\")\n-\n-    def __init__(self, to, on_delete, to_field=None, **kwargs):\n+    def __init__(self, to, on_delete, **kwargs):\n         kwargs['unique'] = True\n-        super().__init__(to, on_delete, to_field=to_field, **kwargs)\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        if \"unique\" in kwargs:\n-            del kwargs['unique']\n-        return name, path, args, kwargs\n-\n-    def formfield(self, **kwargs):\n-        if self.remote_field.parent_link:\n-            return None\n-        return super().formfield(**kwargs)\n+        kwargs['rel'] = OneToOneRel(self, to)\n+        self.remote_field = kwargs['rel']\n+        super().__init__(to, on_delete, **kwargs)\n \n-    def save_form_data(self, instance, data):\n-        if isinstance(data, self.remote_field.model):\n-            setattr(instance, self.name, data)\n-        else:\n-            setattr(instance, self.attname, data)\n-            # Remote field object must be cleared otherwise Model.save()\n-            # will reassign attname using the related object pk.\n-            if data is None:\n-                setattr(instance, self.name, data)\n \n-    def _check_unique(self, **kwargs):\n-        # Override ForeignKey since check isn't applicable here.\n-        return []\n-\n-\n-def create_many_to_many_intermediary_model(field, klass):\n-    from django.db import models\n-\n-    def set_managed(model, related, through):\n-        through._meta.managed = model._meta.managed or related._meta.managed\n-\n-    to_model = resolve_relation(klass, field.remote_field.model)\n-    name = '%s_%s' % (klass._meta.object_name, field.name)\n-    lazy_related_operation(set_managed, klass, to_model, name)\n-\n-    to = make_model_tuple(to_model)[1]\n-    from_ = klass._meta.model_name\n-    if to == from_:\n-        to = 'to_%s' % to\n-        from_ = 'from_%s' % from_\n+class ForeignKey(RelatedField):\n+    \"\"\"A foreign key relationship.\"\"\"\n+    many_to_one = True\n \n-    meta = type('Meta', (), {\n-        'db_table': field._get_m2m_db_table(klass._meta),\n-        'auto_created': klass,\n-        'app_label': klass._meta.app_label,\n-        'db_tablespace': klass._meta.db_tablespace,\n-        'unique_together': (from_, to),\n-        'verbose_name': _('%(from)s-%(to)s relationship') % {'from': from_, 'to': to},\n-        'verbose_name_plural': _('%(from)s-%(to)s relationships') % {'from': from_, 'to': to},\n-        'apps': field.model._meta.apps,\n-    })\n-    # Construct and return the new class.\n-    return type(name, (models.Model,), {\n-        'Meta': meta,\n-        '__module__': klass.__module__,\n-        from_: models.ForeignKey(\n-            klass,\n-            related_name='%s+' % name,\n-            db_tablespace=field.db_tablespace,\n-            db_constraint=field.remote_field.db_constraint,\n-            on_delete=CASCADE,\n-        ),\n-        to: models.ForeignKey(\n-            to_model,\n-            related_name='%s+' % name,\n-            db_tablespace=field.db_tablespace,\n-            db_constraint=field.remote_field.db_constraint,\n-            on_delete=CASCADE,\n-        )\n-    })\n+    def __init__(self, to, on_delete, **kwargs):\n+        kwargs['rel'] = ManyToOneRel(self, to)\n+        self.remote_field = kwargs['rel']\n+        super().__init__(to, on_delete, **kwargs)\n \n \n class ManyToManyField(RelatedField):\n-    \"\"\"\n-    Provide a many-to-many relation by using an intermediary model that\n-    holds two ForeignKey fields pointed at the two sides of the relation.\n-\n-    Unless a ``through`` model was provided, ManyToManyField will use the\n-    create_many_to_many_intermediary_model factory to automatically generate\n-    the intermediary model.\n-    \"\"\"\n-\n-    # Field flags\n+    \"\"\"Many-to-many relationship.\"\"\"\n     many_to_many = True\n-    many_to_one = False\n-    one_to_many = False\n-    one_to_one = False\n-\n-    rel_class = ManyToManyRel\n-\n-    description = _(\"Many-to-many relationship\")\n-\n-    def __init__(self, to, related_name=None, related_query_name=None,\n-                 limit_choices_to=None, symmetrical=None, through=None,\n-                 through_fields=None, db_constraint=True, db_table=None,\n-                 swappable=True, **kwargs):\n-        try:\n-            to._meta\n-        except AttributeError:\n-            assert isinstance(to, str), (\n-                \"%s(%r) is invalid. First parameter to ManyToManyField must be \"\n-                \"either a model, a model name, or the string %r\" %\n-                (self.__class__.__name__, to, RECURSIVE_RELATIONSHIP_CONSTANT)\n-            )\n-\n-        if symmetrical is None:\n-            symmetrical = (to == RECURSIVE_RELATIONSHIP_CONSTANT)\n-\n-        if through is not None:\n-            assert db_table is None, (\n-                \"Cannot specify a db_table if an intermediary model is used.\"\n-            )\n-\n-        kwargs['rel'] = self.rel_class(\n-            self, to,\n-            related_name=related_name,\n-            related_query_name=related_query_name,\n-            limit_choices_to=limit_choices_to,\n-            symmetrical=symmetrical,\n-            through=through,\n-            through_fields=through_fields,\n-            db_constraint=db_constraint,\n-        )\n-        self.has_null_arg = 'null' in kwargs\n-\n-        super().__init__(**kwargs)\n-\n-        self.db_table = db_table\n-        self.swappable = swappable\n-\n-    def check(self, **kwargs):\n-        return [\n-            *super().check(**kwargs),\n-            *self._check_unique(**kwargs),\n-            *self._check_relationship_model(**kwargs),\n-            *self._check_ignored_options(**kwargs),\n-            *self._check_table_uniqueness(**kwargs),\n-        ]\n-\n-    def _check_unique(self, **kwargs):\n-        if self.unique:\n-            return [\n-                checks.Error(\n-                    'ManyToManyFields cannot be unique.',\n-                    obj=self,\n-                    id='fields.E330',\n-                )\n-            ]\n-        return []\n-\n-    def _check_ignored_options(self, **kwargs):\n-        warnings = []\n-\n-        if self.has_null_arg:\n-            warnings.append(\n-                checks.Warning(\n-                    'null has no effect on ManyToManyField.',\n-                    obj=self,\n-                    id='fields.W340',\n-                )\n-            )\n-\n-        if self._validators:\n-            warnings.append(\n-                checks.Warning(\n-                    'ManyToManyField does not support validators.',\n-                    obj=self,\n-                    id='fields.W341',\n-                )\n-            )\n-        if (self.remote_field.limit_choices_to and self.remote_field.through and\n-                not self.remote_field.through._meta.auto_created):\n-            warnings.append(\n-                checks.Warning(\n-                    'limit_choices_to has no effect on ManyToManyField '\n-                    'with a through model.',\n-                    obj=self,\n-                    id='fields.W343',\n-                )\n-            )\n-\n-        return warnings\n-\n-    def _check_relationship_model(self, from_model=None, **kwargs):\n-        if hasattr(self.remote_field.through, '_meta'):\n-            qualified_model_name = \"%s.%s\" % (\n-                self.remote_field.through._meta.app_label, self.remote_field.through.__name__)\n-        else:\n-            qualified_model_name = self.remote_field.through\n-\n-        errors = []\n-\n-        if self.remote_field.through not in self.opts.apps.get_models(include_auto_created=True):\n-            # The relationship model is not installed.\n-            errors.append(\n-                checks.Error(\n-                    \"Field specifies a many-to-many relation through model \"\n-                    \"'%s', which has not been installed.\" % qualified_model_name,\n-                    obj=self,\n-                    id='fields.E331',\n-                )\n-            )\n-\n-        else:\n-            assert from_model is not None, (\n-                \"ManyToManyField with intermediate \"\n-                \"tables cannot be checked if you don't pass the model \"\n-                \"where the field is attached to.\"\n-            )\n-            # Set some useful local variables\n-            to_model = resolve_relation(from_model, self.remote_field.model)\n-            from_model_name = from_model._meta.object_name\n-            if isinstance(to_model, str):\n-                to_model_name = to_model\n-            else:\n-                to_model_name = to_model._meta.object_name\n-            relationship_model_name = self.remote_field.through._meta.object_name\n-            self_referential = from_model == to_model\n-            # Count foreign keys in intermediate model\n-            if self_referential:\n-                seen_self = sum(\n-                    from_model == getattr(field.remote_field, 'model', None)\n-                    for field in self.remote_field.through._meta.fields\n-                )\n-\n-                if seen_self > 2 and not self.remote_field.through_fields:\n-                    errors.append(\n-                        checks.Error(\n-                            \"The model is used as an intermediate model by \"\n-                            \"'%s', but it has more than two foreign keys \"\n-                            \"to '%s', which is ambiguous. You must specify \"\n-                            \"which two foreign keys Django should use via the \"\n-                            \"through_fields keyword argument.\" % (self, from_model_name),\n-                            hint=\"Use through_fields to specify which two foreign keys Django should use.\",\n-                            obj=self.remote_field.through,\n-                            id='fields.E333',\n-                        )\n-                    )\n-\n-            else:\n-                # Count foreign keys in relationship model\n-                seen_from = sum(\n-                    from_model == getattr(field.remote_field, 'model', None)\n-                    for field in self.remote_field.through._meta.fields\n-                )\n-                seen_to = sum(\n-                    to_model == getattr(field.remote_field, 'model', None)\n-                    for field in self.remote_field.through._meta.fields\n-                )\n-\n-                if seen_from > 1 and not self.remote_field.through_fields:\n-                    errors.append(\n-                        checks.Error(\n-                            (\"The model is used as an intermediate model by \"\n-                             \"'%s', but it has more than one foreign key \"\n-                             \"from '%s', which is ambiguous. You must specify \"\n-                             \"which foreign key Django should use via the \"\n-                             \"through_fields keyword argument.\") % (self, from_model_name),\n-                            hint=(\n-                                'If you want to create a recursive relationship, '\n-                                'use ForeignKey(\"self\", symmetrical=False, through=\"%s\").'\n-                            ) % relationship_model_name,\n-                            obj=self,\n-                            id='fields.E334',\n-                        )\n-                    )\n-\n-                if seen_to > 1 and not self.remote_field.through_fields:\n-                    errors.append(\n-                        checks.Error(\n-                            \"The model is used as an intermediate model by \"\n-                            \"'%s', but it has more than one foreign key \"\n-                            \"to '%s', which is ambiguous. You must specify \"\n-                            \"which foreign key Django should use via the \"\n-                            \"through_fields keyword argument.\" % (self, to_model_name),\n-                            hint=(\n-                                'If you want to create a recursive relationship, '\n-                                'use ForeignKey(\"self\", symmetrical=False, through=\"%s\").'\n-                            ) % relationship_model_name,\n-                            obj=self,\n-                            id='fields.E335',\n-                        )\n-                    )\n-\n-                if seen_from == 0 or seen_to == 0:\n-                    errors.append(\n-                        checks.Error(\n-                            \"The model is used as an intermediate model by \"\n-                            \"'%s', but it does not have a foreign key to '%s' or '%s'.\" % (\n-                                self, from_model_name, to_model_name\n-                            ),\n-                            obj=self.remote_field.through,\n-                            id='fields.E336',\n-                        )\n-                    )\n-\n-        # Validate `through_fields`.\n-        if self.remote_field.through_fields is not None:\n-            # Validate that we're given an iterable of at least two items\n-            # and that none of them is \"falsy\".\n-            if not (len(self.remote_field.through_fields) >= 2 and\n-                    self.remote_field.through_fields[0] and self.remote_field.through_fields[1]):\n-                errors.append(\n-                    checks.Error(\n-                        \"Field specifies 'through_fields' but does not provide \"\n-                        \"the names of the two link fields that should be used \"\n-                        \"for the relation through model '%s'.\" % qualified_model_name,\n-                        hint=\"Make sure you specify 'through_fields' as through_fields=('field1', 'field2')\",\n-                        obj=self,\n-                        id='fields.E337',\n-                    )\n-                )\n-\n-            # Validate the given through fields -- they should be actual\n-            # fields on the through model, and also be foreign keys to the\n-            # expected models.\n-            else:\n-                assert from_model is not None, (\n-                    \"ManyToManyField with intermediate \"\n-                    \"tables cannot be checked if you don't pass the model \"\n-                    \"where the field is attached to.\"\n-                )\n-\n-                source, through, target = from_model, self.remote_field.through, self.remote_field.model\n-                source_field_name, target_field_name = self.remote_field.through_fields[:2]\n-\n-                for field_name, related_model in ((source_field_name, source),\n-                                                  (target_field_name, target)):\n-\n-                    possible_field_names = []\n-                    for f in through._meta.fields:\n-                        if hasattr(f, 'remote_field') and getattr(f.remote_field, 'model', None) == related_model:\n-                            possible_field_names.append(f.name)\n-                    if possible_field_names:\n-                        hint = \"Did you mean one of the following foreign keys to '%s': %s?\" % (\n-                            related_model._meta.object_name,\n-                            ', '.join(possible_field_names),\n-                        )\n-                    else:\n-                        hint = None\n-\n-                    try:\n-                        field = through._meta.get_field(field_name)\n-                    except exceptions.FieldDoesNotExist:\n-                        errors.append(\n-                            checks.Error(\n-                                \"The intermediary model '%s' has no field '%s'.\"\n-                                % (qualified_model_name, field_name),\n-                                hint=hint,\n-                                obj=self,\n-                                id='fields.E338',\n-                            )\n-                        )\n-                    else:\n-                        if not (hasattr(field, 'remote_field') and\n-                                getattr(field.remote_field, 'model', None) == related_model):\n-                            errors.append(\n-                                checks.Error(\n-                                    \"'%s.%s' is not a foreign key to '%s'.\" % (\n-                                        through._meta.object_name, field_name,\n-                                        related_model._meta.object_name,\n-                                    ),\n-                                    hint=hint,\n-                                    obj=self,\n-                                    id='fields.E339',\n-                                )\n-                            )\n-\n-        return errors\n-\n-    def _check_table_uniqueness(self, **kwargs):\n-        if isinstance(self.remote_field.through, str) or not self.remote_field.through._meta.managed:\n-            return []\n-        registered_tables = {\n-            model._meta.db_table: model\n-            for model in self.opts.apps.get_models(include_auto_created=True)\n-            if model != self.remote_field.through and model._meta.managed\n-        }\n-        m2m_db_table = self.m2m_db_table()\n-        model = registered_tables.get(m2m_db_table)\n-        # The second condition allows multiple m2m relations on a model if\n-        # some point to a through model that proxies another through model.\n-        if model and model._meta.concrete_model != self.remote_field.through._meta.concrete_model:\n-            if model._meta.auto_created:\n-                def _get_field_name(model):\n-                    for field in model._meta.auto_created._meta.many_to_many:\n-                        if field.remote_field.through is model:\n-                            return field.name\n-                opts = model._meta.auto_created._meta\n-                clashing_obj = '%s.%s' % (opts.label, _get_field_name(model))\n-            else:\n-                clashing_obj = model._meta.label\n-            return [\n-                checks.Error(\n-                    \"The field's intermediary table '%s' clashes with the \"\n-                    \"table name of '%s'.\" % (m2m_db_table, clashing_obj),\n-                    obj=self,\n-                    id='fields.E340',\n-                )\n-            ]\n-        return []\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        # Handle the simpler arguments.\n-        if self.db_table is not None:\n-            kwargs['db_table'] = self.db_table\n-        if self.remote_field.db_constraint is not True:\n-            kwargs['db_constraint'] = self.remote_field.db_constraint\n-        # Rel needs more work.\n-        if isinstance(self.remote_field.model, str):\n-            kwargs['to'] = self.remote_field.model\n-        else:\n-            kwargs['to'] = \"%s.%s\" % (\n-                self.remote_field.model._meta.app_label,\n-                self.remote_field.model._meta.object_name,\n-            )\n-        if getattr(self.remote_field, 'through', None) is not None:\n-            if isinstance(self.remote_field.through, str):\n-                kwargs['through'] = self.remote_field.through\n-            elif not self.remote_field.through._meta.auto_created:\n-                kwargs['through'] = \"%s.%s\" % (\n-                    self.remote_field.through._meta.app_label,\n-                    self.remote_field.through._meta.object_name,\n-                )\n-        # If swappable is True, then see if we're actually pointing to the target\n-        # of a swap.\n-        swappable_setting = self.swappable_setting\n-        if swappable_setting is not None:\n-            # If it's already a settings reference, error.\n-            if hasattr(kwargs['to'], \"setting_name\"):\n-                if kwargs['to'].setting_name != swappable_setting:\n-                    raise ValueError(\n-                        \"Cannot deconstruct a ManyToManyField pointing to a \"\n-                        \"model that is swapped in place of more than one model \"\n-                        \"(%s and %s)\" % (kwargs['to'].setting_name, swappable_setting)\n-                    )\n-\n-            kwargs['to'] = SettingsReference(\n-                kwargs['to'],\n-                swappable_setting,\n-            )\n-        return name, path, args, kwargs\n-\n-    def _get_path_info(self, direct=False, filtered_relation=None):\n-        \"\"\"Called by both direct and indirect m2m traversal.\"\"\"\n-        int_model = self.remote_field.through\n-        linkfield1 = int_model._meta.get_field(self.m2m_field_name())\n-        linkfield2 = int_model._meta.get_field(self.m2m_reverse_field_name())\n-        if direct:\n-            join1infos = linkfield1.get_reverse_path_info()\n-            join2infos = linkfield2.get_path_info(filtered_relation)\n-        else:\n-            join1infos = linkfield2.get_reverse_path_info()\n-            join2infos = linkfield1.get_path_info(filtered_relation)\n-\n-        # Get join infos between the last model of join 1 and the first model\n-        # of join 2. Assume the only reason these may differ is due to model\n-        # inheritance.\n-        join1_final = join1infos[-1].to_opts\n-        join2_initial = join2infos[0].from_opts\n-        if join1_final is join2_initial:\n-            intermediate_infos = []\n-        elif issubclass(join1_final.model, join2_initial.model):\n-            intermediate_infos = join1_final.get_path_to_parent(join2_initial.model)\n-        else:\n-            intermediate_infos = join2_initial.get_path_from_parent(join1_final.model)\n-\n-        return [*join1infos, *intermediate_infos, *join2infos]\n-\n-    def get_path_info(self, filtered_relation=None):\n-        return self._get_path_info(direct=True, filtered_relation=filtered_relation)\n-\n-    def get_reverse_path_info(self, filtered_relation=None):\n-        return self._get_path_info(direct=False, filtered_relation=filtered_relation)\n-\n-    def _get_m2m_db_table(self, opts):\n-        \"\"\"\n-        Function that can be curried to provide the m2m table name for this\n-        relation.\n-        \"\"\"\n-        if self.remote_field.through is not None:\n-            return self.remote_field.through._meta.db_table\n-        elif self.db_table:\n-            return self.db_table\n-        else:\n-            m2m_table_name = '%s_%s' % (utils.strip_quotes(opts.db_table), self.name)\n-            return utils.truncate_name(m2m_table_name, connection.ops.max_name_length())\n-\n-    def _get_m2m_attr(self, related, attr):\n-        \"\"\"\n-        Function that can be curried to provide the source accessor or DB\n-        column name for the m2m table.\n-        \"\"\"\n-        cache_attr = '_m2m_%s_cache' % attr\n-        if hasattr(self, cache_attr):\n-            return getattr(self, cache_attr)\n-        if self.remote_field.through_fields is not None:\n-            link_field_name = self.remote_field.through_fields[0]\n-        else:\n-            link_field_name = None\n-        for f in self.remote_field.through._meta.fields:\n-            if (f.is_relation and f.remote_field.model == related.related_model and\n-                    (link_field_name is None or link_field_name == f.name)):\n-                setattr(self, cache_attr, getattr(f, attr))\n-                return getattr(self, cache_attr)\n-\n-    def _get_m2m_reverse_attr(self, related, attr):\n-        \"\"\"\n-        Function that can be curried to provide the related accessor or DB\n-        column name for the m2m table.\n-        \"\"\"\n-        cache_attr = '_m2m_reverse_%s_cache' % attr\n-        if hasattr(self, cache_attr):\n-            return getattr(self, cache_attr)\n-        found = False\n-        if self.remote_field.through_fields is not None:\n-            link_field_name = self.remote_field.through_fields[1]\n-        else:\n-            link_field_name = None\n-        for f in self.remote_field.through._meta.fields:\n-            if f.is_relation and f.remote_field.model == related.model:\n-                if link_field_name is None and related.related_model == related.model:\n-                    # If this is an m2m-intermediate to self,\n-                    # the first foreign key you find will be\n-                    # the source column. Keep searching for\n-                    # the second foreign key.\n-                    if found:\n-                        setattr(self, cache_attr, getattr(f, attr))\n-                        break\n-                    else:\n-                        found = True\n-                elif link_field_name is None or link_field_name == f.name:\n-                    setattr(self, cache_attr, getattr(f, attr))\n-                    break\n-        return getattr(self, cache_attr)\n-\n-    def contribute_to_class(self, cls, name, **kwargs):\n-        # To support multiple relations to self, it's useful to have a non-None\n-        # related name on symmetrical relations for internal reasons. The\n-        # concept doesn't make a lot of sense externally (\"you want me to\n-        # specify *what* on my non-reversible relation?!\"), so we set it up\n-        # automatically. The funky name reduces the chance of an accidental\n-        # clash.\n-        if self.remote_field.symmetrical and (\n-                self.remote_field.model == \"self\" or self.remote_field.model == cls._meta.object_name):\n-            self.remote_field.related_name = \"%s_rel_+\" % name\n-        elif self.remote_field.is_hidden():\n-            # If the backwards relation is disabled, replace the original\n-            # related_name with one generated from the m2m field name. Django\n-            # still uses backwards relations internally and we need to avoid\n-            # clashes between multiple m2m fields with related_name == '+'.\n-            self.remote_field.related_name = \"_%s_%s_+\" % (cls.__name__.lower(), name)\n-\n-        super().contribute_to_class(cls, name, **kwargs)\n-\n-        # The intermediate m2m model is not auto created if:\n-        #  1) There is a manually specified intermediate, or\n-        #  2) The class owning the m2m field is abstract.\n-        #  3) The class owning the m2m field has been swapped out.\n-        if not cls._meta.abstract:\n-            if self.remote_field.through:\n-                def resolve_through_model(_, model, field):\n-                    field.remote_field.through = model\n-                lazy_related_operation(resolve_through_model, cls, self.remote_field.through, field=self)\n-            elif not cls._meta.swapped:\n-                self.remote_field.through = create_many_to_many_intermediary_model(self, cls)\n-\n-        # Add the descriptor for the m2m relation.\n-        setattr(cls, self.name, ManyToManyDescriptor(self.remote_field, reverse=False))\n-\n-        # Set up the accessor for the m2m table name for the relation.\n-        self.m2m_db_table = partial(self._get_m2m_db_table, cls._meta)\n-\n-    def contribute_to_related_class(self, cls, related):\n-        # Internal M2Ms (i.e., those with a related name ending with '+')\n-        # and swapped models don't get a related descriptor.\n-        if not self.remote_field.is_hidden() and not related.related_model._meta.swapped:\n-            setattr(cls, related.get_accessor_name(), ManyToManyDescriptor(self.remote_field, reverse=True))\n-\n-        # Set up the accessors for the column names on the m2m table.\n-        self.m2m_column_name = partial(self._get_m2m_attr, related, 'column')\n-        self.m2m_reverse_name = partial(self._get_m2m_reverse_attr, related, 'column')\n-\n-        self.m2m_field_name = partial(self._get_m2m_attr, related, 'name')\n-        self.m2m_reverse_field_name = partial(self._get_m2m_reverse_attr, related, 'name')\n-\n-        get_m2m_rel = partial(self._get_m2m_attr, related, 'remote_field')\n-        self.m2m_target_field_name = lambda: get_m2m_rel().field_name\n-        get_m2m_reverse_rel = partial(self._get_m2m_reverse_attr, related, 'remote_field')\n-        self.m2m_reverse_target_field_name = lambda: get_m2m_reverse_rel().field_name\n-\n-    def set_attributes_from_rel(self):\n-        pass\n-\n-    def value_from_object(self, obj):\n-        return [] if obj.pk is None else list(getattr(obj, self.attname).all())\n-\n-    def save_form_data(self, instance, data):\n-        getattr(instance, self.attname).set(data)\n-\n-    def formfield(self, *, using=None, **kwargs):\n-        defaults = {\n-            'form_class': forms.ModelMultipleChoiceField,\n-            'queryset': self.remote_field.model._default_manager.using(using),\n-            **kwargs,\n-        }\n-        # If initial is passed in, it's a list of related objects, but the\n-        # MultipleChoiceField takes a list of IDs.\n-        if defaults.get('initial') is not None:\n-            initial = defaults['initial']\n-            if callable(initial):\n-                initial = initial()\n-            defaults['initial'] = [i.pk for i in initial]\n-        return super().formfield(**defaults)\n-\n-    def db_check(self, connection):\n-        return None\n-\n-    def db_type(self, connection):\n-        # A ManyToManyField is not represented by a single column,\n-        # so return None.\n-        return None\n \n-    def db_parameters(self, connection):\n-        return {\"type\": None, \"check\": None}\n+    def __init__(self, to, **kwargs):\n+        kwargs['rel'] = ManyToManyRel(self, to)\n+        self.remote_field = kwargs['rel']\n+        super().__init__(to, None, **kwargs)\n\\ No newline at end of file\ndiff --git a/django/db/models/fields/related_descriptors.py b/django/db/models/fields/related_descriptors.py\nindex 451d0b4e1f..0914d0691a 100644\n--- a/django/db/models/fields/related_descriptors.py\n+++ b/django/db/models/fields/related_descriptors.py\n@@ -1,1201 +1,146 @@\n-\"\"\"\n-Accessors for related objects.\n-\n-When a field defines a relation between two models, each model class provides\n-an attribute to access related instances of the other model class (unless the\n-reverse accessor has been disabled with related_name='+').\n-\n-Accessors are implemented as descriptors in order to customize access and\n-assignment. This module defines the descriptor classes.\n-\n-Forward accessors follow foreign keys. Reverse accessors trace them back. For\n-example, with the following models::\n-\n-    class Parent(Model):\n-        pass\n-\n-    class Child(Model):\n-        parent = ForeignKey(Parent, related_name='children')\n-\n- ``child.parent`` is a forward many-to-one relation. ``parent.children`` is a\n-reverse many-to-one relation.\n-\n-There are three types of relations (many-to-one, one-to-one, and many-to-many)\n-and two directions (forward and reverse) for a total of six combinations.\n-\n-1. Related instance on the forward side of a many-to-one relation:\n-   ``ForwardManyToOneDescriptor``.\n-\n-   Uniqueness of foreign key values is irrelevant to accessing the related\n-   instance, making the many-to-one and one-to-one cases identical as far as\n-   the descriptor is concerned. The constraint is checked upstream (unicity\n-   validation in forms) or downstream (unique indexes in the database).\n-\n-2. Related instance on the forward side of a one-to-one\n-   relation: ``ForwardOneToOneDescriptor``.\n-\n-   It avoids querying the database when accessing the parent link field in\n-   a multi-table inheritance scenario.\n-\n-3. Related instance on the reverse side of a one-to-one relation:\n-   ``ReverseOneToOneDescriptor``.\n-\n-   One-to-one relations are asymmetrical, despite the apparent symmetry of the\n-   name, because they're implemented in the database with a foreign key from\n-   one table to another. As a consequence ``ReverseOneToOneDescriptor`` is\n-   slightly different from ``ForwardManyToOneDescriptor``.\n-\n-4. Related objects manager for related instances on the reverse side of a\n-   many-to-one relation: ``ReverseManyToOneDescriptor``.\n-\n-   Unlike the previous two classes, this one provides access to a collection\n-   of objects. It returns a manager rather than an instance.\n-\n-5. Related objects manager for related instances on the forward or reverse\n-   sides of a many-to-many relation: ``ManyToManyDescriptor``.\n-\n-   Many-to-many relations are symmetrical. The syntax of Django models\n-   requires declaring them on one side but that's an implementation detail.\n-   They could be declared on the other side without any change in behavior.\n-   Therefore the forward and reverse descriptors can be the same.\n-\n-   If you're looking for ``ForwardManyToManyDescriptor`` or\n-   ``ReverseManyToManyDescriptor``, use ``ManyToManyDescriptor`` instead.\n-\"\"\"\n+\"\"\"Related object descriptors.\"\"\"\n \n from django.core.exceptions import FieldError\n-from django.db import connections, router, transaction\n-from django.db.models import Q, signals\n+from django.db import router\n+from django.db.models import Q\n from django.db.models.query import QuerySet\n-from django.db.models.query_utils import DeferredAttribute\n-from django.db.models.utils import resolve_callables\n from django.utils.functional import cached_property\n \n \n-class ForeignKeyDeferredAttribute(DeferredAttribute):\n-    def __set__(self, instance, value):\n-        if instance.__dict__.get(self.field.attname) != value and self.field.is_cached(instance):\n-            self.field.delete_cached_value(instance)\n-        instance.__dict__[self.field.attname] = value\n-\n-\n-class ForwardManyToOneDescriptor:\n+class ForwardDescriptor:\n     \"\"\"\n-    Accessor to the related object on the forward side of a many-to-one or\n-    one-to-one (via ForwardOneToOneDescriptor subclass) relation.\n-\n-    In the example::\n-\n-        class Child(Model):\n-            parent = ForeignKey(Parent, related_name='children')\n-\n-    ``Child.parent`` is a ``ForwardManyToOneDescriptor`` instance.\n+    Base class for forward descriptors (foreign key, one-to-one).\n     \"\"\"\n-\n     def __init__(self, field_with_rel):\n         self.field = field_with_rel\n \n-    @cached_property\n-    def RelatedObjectDoesNotExist(self):\n-        # The exception can't be created at initialization time since the\n-        # related model might not be resolved yet; `self.field.model` might\n-        # still be a string model reference.\n-        return type(\n-            'RelatedObjectDoesNotExist',\n-            (self.field.remote_field.model.DoesNotExist, AttributeError), {\n-                '__module__': self.field.model.__module__,\n-                '__qualname__': '%s.%s.RelatedObjectDoesNotExist' % (\n-                    self.field.model.__qualname__,\n-                    self.field.name,\n-                ),\n-            }\n-        )\n-\n     def is_cached(self, instance):\n-        return self.field.is_cached(instance)\n+        return self.field.name in instance._state.fields_cache\n \n     def get_queryset(self, **hints):\n-        return self.field.remote_field.model._base_manager.db_manager(hints=hints).all()\n-\n-    def get_prefetch_queryset(self, instances, queryset=None):\n-        if queryset is None:\n-            queryset = self.get_queryset()\n-        queryset._add_hints(instance=instances[0])\n+        return self.field.remote_field.model._base_manager.db_manager(hints=hints)\n \n-        rel_obj_attr = self.field.get_foreign_related_value\n-        instance_attr = self.field.get_local_related_value\n-        instances_dict = {instance_attr(inst): inst for inst in instances}\n-        related_field = self.field.foreign_related_fields[0]\n-        remote_field = self.field.remote_field\n-\n-        # FIXME: This will need to be revisited when we introduce support for\n-        # composite fields. In the meantime we take this practical approach to\n-        # solve a regression on 1.6 when the reverse manager in hidden\n-        # (related_name ends with a '+'). Refs #21410.\n-        # The check for len(...) == 1 is a special case that allows the query\n-        # to be join-less and smaller. Refs #21760.\n-        if remote_field.is_hidden() or len(self.field.foreign_related_fields) == 1:\n-            query = {'%s__in' % related_field.name: {instance_attr(inst)[0] for inst in instances}}\n-        else:\n-            query = {'%s__in' % self.field.related_query_name(): instances}\n-        queryset = queryset.filter(**query)\n-\n-        # Since we're going to assign directly in the cache,\n-        # we must manage the reverse relation cache manually.\n-        if not remote_field.multiple:\n-            for rel_obj in queryset:\n-                instance = instances_dict[rel_obj_attr(rel_obj)]\n-                remote_field.set_cached_value(rel_obj, instance)\n-        return queryset, rel_obj_attr, instance_attr, True, self.field.get_cache_name(), False\n-\n-    def get_object(self, instance):\n-        qs = self.get_queryset(instance=instance)\n-        # Assuming the database enforces foreign keys, this won't fail.\n-        return qs.get(self.field.get_reverse_related_filter(instance))\n \n+class ForwardOneToOneDescriptor(ForwardDescriptor):\n+    \"\"\"\n+    Descriptor for OneToOneField relationships.\n+    \"\"\"\n     def __get__(self, instance, cls=None):\n-        \"\"\"\n-        Get the related instance through the forward relation.\n-\n-        With the example above, when getting ``child.parent``:\n-\n-        - ``self`` is the descriptor managing the ``parent`` attribute\n-        - ``instance`` is the ``child`` instance\n-        - ``cls`` is the ``Child`` class (we don't need it)\n-        \"\"\"\n         if instance is None:\n             return self\n \n-        # The related instance is loaded from the database and then cached\n-        # by the field on the model instance state. It can also be pre-cached\n-        # by the reverse accessor (ReverseOneToOneDescriptor).\n-        try:\n-            rel_obj = self.field.get_cached_value(instance)\n-        except KeyError:\n-            has_value = None not in self.field.get_local_related_value(instance)\n-            ancestor_link = instance._meta.get_ancestor_link(self.field.model) if has_value else None\n-            if ancestor_link and ancestor_link.is_cached(instance):\n-                # An ancestor link will exist if this field is defined on a\n-                # multi-table inheritance parent of the instance's class.\n-                ancestor = ancestor_link.get_cached_value(instance)\n-                # The value might be cached on an ancestor if the instance\n-                # originated from walking down the inheritance chain.\n-                rel_obj = self.field.get_cached_value(ancestor, default=None)\n-            else:\n-                rel_obj = None\n-            if rel_obj is None and has_value:\n-                rel_obj = self.get_object(instance)\n-                remote_field = self.field.remote_field\n-                # If this is a one-to-one relation, set the reverse accessor\n-                # cache on the related object to the current instance to avoid\n-                # an extra SQL query if it's accessed later on.\n-                if not remote_field.multiple:\n-                    remote_field.set_cached_value(rel_obj, instance)\n-            self.field.set_cached_value(instance, rel_obj)\n+        # Get value from database or cache\n+        if self.is_cached(instance):\n+            return instance._state.fields_cache[self.field.name]\n \n-        if rel_obj is None and not self.field.null:\n-            raise self.RelatedObjectDoesNotExist(\n-                \"%s has no %s.\" % (self.field.model.__name__, self.field.name)\n-            )\n-        else:\n-            return rel_obj\n+        val = getattr(instance, self.field.attname)\n+        if val is None:\n+            return None\n \n-    def __set__(self, instance, value):\n-        \"\"\"\n-        Set the related instance through the forward relation.\n+        # Load related object\n+        rel_obj = self.get_queryset(instance=instance).get(pk=val)\n+        instance._state.fields_cache[self.field.name] = rel_obj\n+        \n+        return rel_obj\n \n-        With the example above, when setting ``child.parent = parent``:\n-\n-        - ``self`` is the descriptor managing the ``parent`` attribute\n-        - ``instance`` is the ``child`` instance\n-        - ``value`` is the ``parent`` instance on the right of the equal sign\n-        \"\"\"\n-        # An object must be an instance of the related class.\n-        if value is not None and not isinstance(value, self.field.remote_field.model._meta.concrete_model):\n-            raise ValueError(\n-                'Cannot assign \"%r\": \"%s.%s\" must be a \"%s\" instance.' % (\n-                    value,\n-                    instance._meta.object_name,\n-                    self.field.name,\n-                    self.field.remote_field.model._meta.object_name,\n+    def __set__(self, instance, value):\n+        if instance is None:\n+            raise AttributeError(\"Cannot set attribute on class\")\n+\n+        if value is None and not self.field.null:\n+            raise ValueError('Cannot assign None: %s.%s does not allow null values.' % (\n+                instance.__class__.__name__, self.field.name))\n+\n+        if value is not None:\n+            if not isinstance(value, self.field.remote_field.model):\n+                raise ValueError(\n+                    'Cannot assign \"%r\": %s.%s must be a %s instance.' % (\n+                        value,\n+                        instance.__class__.__name__,\n+                        self.field.name,\n+                        self.field.remote_field.model._meta.object_name,\n+                    )\n                 )\n-            )\n-        elif value is not None:\n-            if instance._state.db is None:\n-                instance._state.db = router.db_for_write(instance.__class__, instance=value)\n-            if value._state.db is None:\n-                value._state.db = router.db_for_write(value.__class__, instance=instance)\n-            if not router.allow_relation(value, instance):\n-                raise ValueError('Cannot assign \"%r\": the current database router prevents this relation.' % value)\n-\n-        remote_field = self.field.remote_field\n-        # If we're setting the value of a OneToOneField to None, we need to clear\n-        # out the cache on any old related object. Otherwise, deleting the\n-        # previously-related object will also cause this object to be deleted,\n-        # which is wrong.\n-        if value is None:\n-            # Look up the previously-related object, which may still be available\n-            # since we've not yet cleared out the related field.\n-            # Use the cache directly, instead of the accessor; if we haven't\n-            # populated the cache, then we don't care - we're only accessing\n-            # the object to invalidate the accessor cache, so there's no\n-            # need to populate the cache just to expire it again.\n-            related = self.field.get_cached_value(instance, default=None)\n-\n-            # If we've got an old related object, we need to clear out its\n-            # cache. This cache also might not exist if the related object\n-            # hasn't been accessed yet.\n-            if related is not None:\n-                remote_field.set_cached_value(related, None)\n-\n-            for lh_field, rh_field in self.field.related_fields:\n-                setattr(instance, lh_field.attname, None)\n-\n-        # Set the values of the related field.\n-        else:\n-            for lh_field, rh_field in self.field.related_fields:\n-                setattr(instance, lh_field.attname, getattr(value, rh_field.attname))\n-\n-        # Set the related instance cache used by __get__ to avoid an SQL query\n-        # when accessing the attribute we just set.\n-        self.field.set_cached_value(instance, value)\n-\n-        # If this is a one-to-one relation, set the reverse accessor cache on\n-        # the related object to the current instance to avoid an extra SQL\n-        # query if it's accessed later on.\n-        if value is not None and not remote_field.multiple:\n-            remote_field.set_cached_value(value, instance)\n-\n-    def __reduce__(self):\n-        \"\"\"\n-        Pickling should return the instance attached by self.field on the\n-        model, not a new copy of that descriptor. Use getattr() to retrieve\n-        the instance directly from the model.\n-        \"\"\"\n-        return getattr, (self.field.model, self.field.name)\n \n-\n-class ForwardOneToOneDescriptor(ForwardManyToOneDescriptor):\n-    \"\"\"\n-    Accessor to the related object on the forward side of a one-to-one relation.\n-\n-    In the example::\n-\n-        class Restaurant(Model):\n-            place = OneToOneField(Place, related_name='restaurant')\n-\n-    ``Restaurant.place`` is a ``ForwardOneToOneDescriptor`` instance.\n-    \"\"\"\n-\n-    def get_object(self, instance):\n-        if self.field.remote_field.parent_link:\n-            deferred = instance.get_deferred_fields()\n-            # Because it's a parent link, all the data is available in the\n-            # instance, so populate the parent model with this data.\n-            rel_model = self.field.remote_field.model\n-            fields = [field.attname for field in rel_model._meta.concrete_fields]\n-\n-            # If any of the related model's fields are deferred, fallback to\n-            # fetching all fields from the related model. This avoids a query\n-            # on the related model for every deferred field.\n-            if not any(field in fields for field in deferred):\n-                kwargs = {field: getattr(instance, field) for field in fields}\n-                obj = rel_model(**kwargs)\n-                obj._state.adding = instance._state.adding\n-                obj._state.db = instance._state.db\n-                return obj\n-        return super().get_object(instance)\n-\n-    def __set__(self, instance, value):\n-        super().__set__(instance, value)\n-        # If the primary key is a link to a parent model and a parent instance\n-        # is being set, update the value of the inherited pk(s).\n-        if self.field.primary_key and self.field.remote_field.parent_link:\n-            opts = instance._meta\n-            # Inherited primary key fields from this object's base classes.\n-            inherited_pk_fields = [\n-                field for field in opts.concrete_fields\n-                if field.primary_key and field.remote_field\n-            ]\n-            for field in inherited_pk_fields:\n-                rel_model_pk_name = field.remote_field.model._meta.pk.attname\n-                raw_value = getattr(value, rel_model_pk_name) if value is not None else None\n-                setattr(instance, rel_model_pk_name, raw_value)\n+        # Set value\n+        setattr(instance, self.field.attname, value.pk if value else None)\n+        instance._state.fields_cache[self.field.name] = value\n \n \n class ReverseOneToOneDescriptor:\n     \"\"\"\n-    Accessor to the related object on the reverse side of a one-to-one\n-    relation.\n-\n-    In the example::\n-\n-        class Restaurant(Model):\n-            place = OneToOneField(Place, related_name='restaurant')\n-\n-    ``Place.restaurant`` is a ``ReverseOneToOneDescriptor`` instance.\n+    Descriptor for reverse side of OneToOneField.\n     \"\"\"\n-\n     def __init__(self, related):\n-        # Following the example above, `related` is an instance of OneToOneRel\n-        # which represents the reverse restaurant field (place.restaurant).\n         self.related = related\n-\n-    @cached_property\n-    def RelatedObjectDoesNotExist(self):\n-        # The exception isn't created at initialization time for the sake of\n-        # consistency with `ForwardManyToOneDescriptor`.\n-        return type(\n-            'RelatedObjectDoesNotExist',\n-            (self.related.related_model.DoesNotExist, AttributeError), {\n-                '__module__': self.related.model.__module__,\n-                '__qualname__': '%s.%s.RelatedObjectDoesNotExist' % (\n-                    self.related.model.__qualname__,\n-                    self.related.name,\n-                )\n-            },\n-        )\n-\n-    def is_cached(self, instance):\n-        return self.related.is_cached(instance)\n-\n-    def get_queryset(self, **hints):\n-        return self.related.related_model._base_manager.db_manager(hints=hints).all()\n-\n-    def get_prefetch_queryset(self, instances, queryset=None):\n-        if queryset is None:\n-            queryset = self.get_queryset()\n-        queryset._add_hints(instance=instances[0])\n-\n-        rel_obj_attr = self.related.field.get_local_related_value\n-        instance_attr = self.related.field.get_foreign_related_value\n-        instances_dict = {instance_attr(inst): inst for inst in instances}\n-        query = {'%s__in' % self.related.field.name: instances}\n-        queryset = queryset.filter(**query)\n-\n-        # Since we're going to assign directly in the cache,\n-        # we must manage the reverse relation cache manually.\n-        for rel_obj in queryset:\n-            instance = instances_dict[rel_obj_attr(rel_obj)]\n-            self.related.field.set_cached_value(rel_obj, instance)\n-        return queryset, rel_obj_attr, instance_attr, True, self.related.get_cache_name(), False\n+        self.field = self.related.field\n \n     def __get__(self, instance, cls=None):\n-        \"\"\"\n-        Get the related instance through the reverse relation.\n-\n-        With the example above, when getting ``place.restaurant``:\n-\n-        - ``self`` is the descriptor managing the ``restaurant`` attribute\n-        - ``instance`` is the ``place`` instance\n-        - ``cls`` is the ``Place`` class (unused)\n-\n-        Keep in mind that ``Restaurant`` holds the foreign key to ``Place``.\n-        \"\"\"\n         if instance is None:\n             return self\n \n-        # The related instance is loaded from the database and then cached\n-        # by the field on the model instance state. It can also be pre-cached\n-        # by the forward accessor (ForwardManyToOneDescriptor).\n-        try:\n-            rel_obj = self.related.get_cached_value(instance)\n-        except KeyError:\n-            related_pk = instance.pk\n-            if related_pk is None:\n-                rel_obj = None\n-            else:\n-                filter_args = self.related.field.get_forward_related_filter(instance)\n-                try:\n-                    rel_obj = self.get_queryset(instance=instance).get(**filter_args)\n-                except self.related.related_model.DoesNotExist:\n-                    rel_obj = None\n-                else:\n-                    # Set the forward accessor cache on the related object to\n-                    # the current instance to avoid an extra SQL query if it's\n-                    # accessed later on.\n-                    self.related.field.set_cached_value(rel_obj, instance)\n-            self.related.set_cached_value(instance, rel_obj)\n-\n-        if rel_obj is None:\n-            raise self.RelatedObjectDoesNotExist(\n-                \"%s has no %s.\" % (\n-                    instance.__class__.__name__,\n-                    self.related.get_accessor_name()\n-                )\n-            )\n-        else:\n-            return rel_obj\n-\n-    def __set__(self, instance, value):\n-        \"\"\"\n-        Set the related instance through the reverse relation.\n-\n-        With the example above, when setting ``place.restaurant = restaurant``:\n-\n-        - ``self`` is the descriptor managing the ``restaurant`` attribute\n-        - ``instance`` is the ``place`` instance\n-        - ``value`` is the ``restaurant`` instance on the right of the equal sign\n-\n-        Keep in mind that ``Restaurant`` holds the foreign key to ``Place``.\n-        \"\"\"\n-        # The similarity of the code below to the code in\n-        # ForwardManyToOneDescriptor is annoying, but there's a bunch\n-        # of small differences that would make a common base class convoluted.\n-\n-        if value is None:\n-            # Update the cached related instance (if any) & clear the cache.\n-            # Following the example above, this would be the cached\n-            # ``restaurant`` instance (if any).\n-            rel_obj = self.related.get_cached_value(instance, default=None)\n-            if rel_obj is not None:\n-                # Remove the ``restaurant`` instance from the ``place``\n-                # instance cache.\n-                self.related.delete_cached_value(instance)\n-                # Set the ``place`` field on the ``restaurant``\n-                # instance to None.\n-                setattr(rel_obj, self.related.field.name, None)\n-        elif not isinstance(value, self.related.related_model):\n-            # An object must be an instance of the related class.\n-            raise ValueError(\n-                'Cannot assign \"%r\": \"%s.%s\" must be a \"%s\" instance.' % (\n-                    value,\n-                    instance._meta.object_name,\n-                    self.related.get_accessor_name(),\n-                    self.related.related_model._meta.object_name,\n-                )\n-            )\n-        else:\n-            if instance._state.db is None:\n-                instance._state.db = router.db_for_write(instance.__class__, instance=value)\n-            if value._state.db is None:\n-                value._state.db = router.db_for_write(value.__class__, instance=instance)\n-            if not router.allow_relation(value, instance):\n-                raise ValueError('Cannot assign \"%r\": the current database router prevents this relation.' % value)\n+        # Get from cache\n+        cache_name = self.related.get_cache_name()\n+        if hasattr(instance, cache_name):\n+            return getattr(instance, cache_name)\n \n-            related_pk = tuple(getattr(instance, field.attname) for field in self.related.field.foreign_related_fields)\n-            # Set the value of the related field to the value of the related object's related field\n-            for index, field in enumerate(self.related.field.local_related_fields):\n-                setattr(value, field.attname, related_pk[index])\n-\n-            # Set the related instance cache used by __get__ to avoid an SQL query\n-            # when accessing the attribute we just set.\n-            self.related.set_cached_value(instance, value)\n-\n-            # Set the forward accessor cache on the related object to the current\n-            # instance to avoid an extra SQL query if it's accessed later on.\n-            self.related.field.set_cached_value(value, instance)\n+        # Get from database\n+        filter_args = {self.field.name: instance.pk}\n+        try:\n+            obj = self.related.model._base_manager.get(**filter_args)\n+        except self.related.model.DoesNotExist:\n+            obj = None\n \n-    def __reduce__(self):\n-        # Same purpose as ForwardManyToOneDescriptor.__reduce__().\n-        return getattr, (self.related.model, self.related.name)\n+        # Cache result\n+        setattr(instance, cache_name, obj)\n+        return obj\n \n \n-class ReverseManyToOneDescriptor:\n+class RelatedDescriptor:\n     \"\"\"\n-    Accessor to the related objects manager on the reverse side of a\n-    many-to-one relation.\n-\n-    In the example::\n-\n-        class Child(Model):\n-            parent = ForeignKey(Parent, related_name='children')\n-\n-    ``Parent.children`` is a ``ReverseManyToOneDescriptor`` instance.\n-\n-    Most of the implementation is delegated to a dynamically defined manager\n-    class built by ``create_forward_many_to_many_manager()`` defined below.\n+    Descriptor for regular ForeignKey relationships.\n     \"\"\"\n-\n-    def __init__(self, rel):\n-        self.rel = rel\n-        self.field = rel.field\n-\n-    @cached_property\n-    def related_manager_cls(self):\n-        related_model = self.rel.related_model\n-\n-        return create_reverse_many_to_one_manager(\n-            related_model._default_manager.__class__,\n-            self.rel,\n-        )\n+    def __init__(self, field_with_rel):\n+        self.field = field_with_rel\n+        self.cache_name = field_with_rel.name\n \n     def __get__(self, instance, cls=None):\n-        \"\"\"\n-        Get the related objects through the reverse relation.\n-\n-        With the example above, when getting ``parent.children``:\n-\n-        - ``self`` is the descriptor managing the ``children`` attribute\n-        - ``instance`` is the ``parent`` instance\n-        - ``cls`` is the ``Parent`` class (unused)\n-        \"\"\"\n         if instance is None:\n             return self\n \n-        return self.related_manager_cls(instance)\n-\n-    def _get_set_deprecation_msg_params(self):\n-        return (\n-            'reverse side of a related set',\n-            self.rel.get_accessor_name(),\n-        )\n-\n-    def __set__(self, instance, value):\n-        raise TypeError(\n-            'Direct assignment to the %s is prohibited. Use %s.set() instead.'\n-            % self._get_set_deprecation_msg_params(),\n-        )\n-\n-\n-def create_reverse_many_to_one_manager(superclass, rel):\n-    \"\"\"\n-    Create a manager for the reverse side of a many-to-one relation.\n-\n-    This manager subclasses another manager, generally the default manager of\n-    the related model, and adds behaviors specific to many-to-one relations.\n-    \"\"\"\n-\n-    class RelatedManager(superclass):\n-        def __init__(self, instance):\n-            super().__init__()\n-\n-            self.instance = instance\n-            self.model = rel.related_model\n-            self.field = rel.field\n-\n-            self.core_filters = {self.field.name: instance}\n-\n-        def __call__(self, *, manager):\n-            manager = getattr(self.model, manager)\n-            manager_class = create_reverse_many_to_one_manager(manager.__class__, rel)\n-            return manager_class(self.instance)\n-        do_not_call_in_templates = True\n-\n-        def _apply_rel_filters(self, queryset):\n-            \"\"\"\n-            Filter the queryset for the instance this manager is bound to.\n-            \"\"\"\n-            db = self._db or router.db_for_read(self.model, instance=self.instance)\n-            empty_strings_as_null = connections[db].features.interprets_empty_strings_as_nulls\n-            queryset._add_hints(instance=self.instance)\n-            if self._db:\n-                queryset = queryset.using(self._db)\n-            queryset = queryset.filter(**self.core_filters)\n-            for field in self.field.foreign_related_fields:\n-                val = getattr(self.instance, field.attname)\n-                if val is None or (val == '' and empty_strings_as_null):\n-                    return queryset.none()\n-            if self.field.many_to_one:\n-                # Guard against field-like objects such as GenericRelation\n-                # that abuse create_reverse_many_to_one_manager() with reverse\n-                # one-to-many relationships instead and break known related\n-                # objects assignment.\n-                try:\n-                    target_field = self.field.target_field\n-                except FieldError:\n-                    # The relationship has multiple target fields. Use a tuple\n-                    # for related object id.\n-                    rel_obj_id = tuple([\n-                        getattr(self.instance, target_field.attname)\n-                        for target_field in self.field.get_path_info()[-1].target_fields\n-                    ])\n-                else:\n-                    rel_obj_id = getattr(self.instance, target_field.attname)\n-                queryset._known_related_objects = {self.field: {rel_obj_id: self.instance}}\n-            return queryset\n-\n-        def _remove_prefetched_objects(self):\n-            try:\n-                self.instance._prefetched_objects_cache.pop(self.field.remote_field.get_cache_name())\n-            except (AttributeError, KeyError):\n-                pass  # nothing to clear from cache\n-\n-        def get_queryset(self):\n-            try:\n-                return self.instance._prefetched_objects_cache[self.field.remote_field.get_cache_name()]\n-            except (AttributeError, KeyError):\n-                queryset = super().get_queryset()\n-                return self._apply_rel_filters(queryset)\n-\n-        def get_prefetch_queryset(self, instances, queryset=None):\n-            if queryset is None:\n-                queryset = super().get_queryset()\n-\n-            queryset._add_hints(instance=instances[0])\n-            queryset = queryset.using(queryset._db or self._db)\n-\n-            rel_obj_attr = self.field.get_local_related_value\n-            instance_attr = self.field.get_foreign_related_value\n-            instances_dict = {instance_attr(inst): inst for inst in instances}\n-            query = {'%s__in' % self.field.name: instances}\n-            queryset = queryset.filter(**query)\n-\n-            # Since we just bypassed this class' get_queryset(), we must manage\n-            # the reverse relation manually.\n-            for rel_obj in queryset:\n-                instance = instances_dict[rel_obj_attr(rel_obj)]\n-                setattr(rel_obj, self.field.name, instance)\n-            cache_name = self.field.remote_field.get_cache_name()\n-            return queryset, rel_obj_attr, instance_attr, False, cache_name, False\n-\n-        def add(self, *objs, bulk=True):\n-            self._remove_prefetched_objects()\n-            objs = list(objs)\n-            db = router.db_for_write(self.model, instance=self.instance)\n-\n-            def check_and_update_obj(obj):\n-                if not isinstance(obj, self.model):\n-                    raise TypeError(\"'%s' instance expected, got %r\" % (\n-                        self.model._meta.object_name, obj,\n-                    ))\n-                setattr(obj, self.field.name, self.instance)\n-\n-            if bulk:\n-                pks = []\n-                for obj in objs:\n-                    check_and_update_obj(obj)\n-                    if obj._state.adding or obj._state.db != db:\n-                        raise ValueError(\n-                            \"%r instance isn't saved. Use bulk=False or save \"\n-                            \"the object first.\" % obj\n-                        )\n-                    pks.append(obj.pk)\n-                self.model._base_manager.using(db).filter(pk__in=pks).update(**{\n-                    self.field.name: self.instance,\n-                })\n-            else:\n-                with transaction.atomic(using=db, savepoint=False):\n-                    for obj in objs:\n-                        check_and_update_obj(obj)\n-                        obj.save()\n-        add.alters_data = True\n-\n-        def create(self, **kwargs):\n-            kwargs[self.field.name] = self.instance\n-            db = router.db_for_write(self.model, instance=self.instance)\n-            return super(RelatedManager, self.db_manager(db)).create(**kwargs)\n-        create.alters_data = True\n-\n-        def get_or_create(self, **kwargs):\n-            kwargs[self.field.name] = self.instance\n-            db = router.db_for_write(self.model, instance=self.instance)\n-            return super(RelatedManager, self.db_manager(db)).get_or_create(**kwargs)\n-        get_or_create.alters_data = True\n-\n-        def update_or_create(self, **kwargs):\n-            kwargs[self.field.name] = self.instance\n-            db = router.db_for_write(self.model, instance=self.instance)\n-            return super(RelatedManager, self.db_manager(db)).update_or_create(**kwargs)\n-        update_or_create.alters_data = True\n-\n-        # remove() and clear() are only provided if the ForeignKey can have a value of null.\n-        if rel.field.null:\n-            def remove(self, *objs, bulk=True):\n-                if not objs:\n-                    return\n-                val = self.field.get_foreign_related_value(self.instance)\n-                old_ids = set()\n-                for obj in objs:\n-                    # Is obj actually part of this descriptor set?\n-                    if self.field.get_local_related_value(obj) == val:\n-                        old_ids.add(obj.pk)\n-                    else:\n-                        raise self.field.remote_field.model.DoesNotExist(\n-                            \"%r is not related to %r.\" % (obj, self.instance)\n-                        )\n-                self._clear(self.filter(pk__in=old_ids), bulk)\n-            remove.alters_data = True\n-\n-            def clear(self, *, bulk=True):\n-                self._clear(self, bulk)\n-            clear.alters_data = True\n-\n-            def _clear(self, queryset, bulk):\n-                self._remove_prefetched_objects()\n-                db = router.db_for_write(self.model, instance=self.instance)\n-                queryset = queryset.using(db)\n-                if bulk:\n-                    # `QuerySet.update()` is intrinsically atomic.\n-                    queryset.update(**{self.field.name: None})\n-                else:\n-                    with transaction.atomic(using=db, savepoint=False):\n-                        for obj in queryset:\n-                            setattr(obj, self.field.name, None)\n-                            obj.save(update_fields=[self.field.name])\n-            _clear.alters_data = True\n-\n-        def set(self, objs, *, bulk=True, clear=False):\n-            # Force evaluation of `objs` in case it's a queryset whose value\n-            # could be affected by `manager.clear()`. Refs #19816.\n-            objs = tuple(objs)\n-\n-            if self.field.null:\n-                db = router.db_for_write(self.model, instance=self.instance)\n-                with transaction.atomic(using=db, savepoint=False):\n-                    if clear:\n-                        self.clear(bulk=bulk)\n-                        self.add(*objs, bulk=bulk)\n-                    else:\n-                        old_objs = set(self.using(db).all())\n-                        new_objs = []\n-                        for obj in objs:\n-                            if obj in old_objs:\n-                                old_objs.remove(obj)\n-                            else:\n-                                new_objs.append(obj)\n-\n-                        self.remove(*old_objs, bulk=bulk)\n-                        self.add(*new_objs, bulk=bulk)\n-            else:\n-                self.add(*objs, bulk=bulk)\n-        set.alters_data = True\n-\n-    return RelatedManager\n-\n-\n-class ManyToManyDescriptor(ReverseManyToOneDescriptor):\n-    \"\"\"\n-    Accessor to the related objects manager on the forward and reverse sides of\n-    a many-to-many relation.\n-\n-    In the example::\n-\n-        class Pizza(Model):\n-            toppings = ManyToManyField(Topping, related_name='pizzas')\n-\n-    ``Pizza.toppings`` and ``Topping.pizzas`` are ``ManyToManyDescriptor``\n-    instances.\n-\n-    Most of the implementation is delegated to a dynamically defined manager\n-    class built by ``create_forward_many_to_many_manager()`` defined below.\n-    \"\"\"\n-\n-    def __init__(self, rel, reverse=False):\n-        super().__init__(rel)\n-\n-        self.reverse = reverse\n-\n-    @property\n-    def through(self):\n-        # through is provided so that you have easy access to the through\n-        # model (Book.authors.through) for inlines, etc. This is done as\n-        # a property to ensure that the fully resolved value is returned.\n-        return self.rel.through\n-\n-    @cached_property\n-    def related_manager_cls(self):\n-        related_model = self.rel.related_model if self.reverse else self.rel.model\n-\n-        return create_forward_many_to_many_manager(\n-            related_model._default_manager.__class__,\n-            self.rel,\n-            reverse=self.reverse,\n-        )\n-\n-    def _get_set_deprecation_msg_params(self):\n-        return (\n-            '%s side of a many-to-many set' % ('reverse' if self.reverse else 'forward'),\n-            self.rel.get_accessor_name() if self.reverse else self.field.name,\n-        )\n-\n-\n-def create_forward_many_to_many_manager(superclass, rel, reverse):\n-    \"\"\"\n-    Create a manager for the either side of a many-to-many relation.\n-\n-    This manager subclasses another manager, generally the default manager of\n-    the related model, and adds behaviors specific to many-to-many relations.\n-    \"\"\"\n-\n-    class ManyRelatedManager(superclass):\n-        def __init__(self, instance=None):\n-            super().__init__()\n-\n-            self.instance = instance\n-\n-            if not reverse:\n-                self.model = rel.model\n-                self.query_field_name = rel.field.related_query_name()\n-                self.prefetch_cache_name = rel.field.name\n-                self.source_field_name = rel.field.m2m_field_name()\n-                self.target_field_name = rel.field.m2m_reverse_field_name()\n-                self.symmetrical = rel.symmetrical\n-            else:\n-                self.model = rel.related_model\n-                self.query_field_name = rel.field.name\n-                self.prefetch_cache_name = rel.field.related_query_name()\n-                self.source_field_name = rel.field.m2m_reverse_field_name()\n-                self.target_field_name = rel.field.m2m_field_name()\n-                self.symmetrical = False\n-\n-            self.through = rel.through\n-            self.reverse = reverse\n-\n-            self.source_field = self.through._meta.get_field(self.source_field_name)\n-            self.target_field = self.through._meta.get_field(self.target_field_name)\n-\n-            self.core_filters = {}\n-            self.pk_field_names = {}\n-            for lh_field, rh_field in self.source_field.related_fields:\n-                core_filter_key = '%s__%s' % (self.query_field_name, rh_field.name)\n-                self.core_filters[core_filter_key] = getattr(instance, rh_field.attname)\n-                self.pk_field_names[lh_field.name] = rh_field.name\n-\n-            self.related_val = self.source_field.get_foreign_related_value(instance)\n-            if None in self.related_val:\n-                raise ValueError('\"%r\" needs to have a value for field \"%s\" before '\n-                                 'this many-to-many relationship can be used.' %\n-                                 (instance, self.pk_field_names[self.source_field_name]))\n-            # Even if this relation is not to pk, we require still pk value.\n-            # The wish is that the instance has been already saved to DB,\n-            # although having a pk value isn't a guarantee of that.\n-            if instance.pk is None:\n-                raise ValueError(\"%r instance needs to have a primary key value before \"\n-                                 \"a many-to-many relationship can be used.\" %\n-                                 instance.__class__.__name__)\n-\n-        def __call__(self, *, manager):\n-            manager = getattr(self.model, manager)\n-            manager_class = create_forward_many_to_many_manager(manager.__class__, rel, reverse)\n-            return manager_class(instance=self.instance)\n-        do_not_call_in_templates = True\n-\n-        def _build_remove_filters(self, removed_vals):\n-            filters = Q(**{self.source_field_name: self.related_val})\n-            # No need to add a subquery condition if removed_vals is a QuerySet without\n-            # filters.\n-            removed_vals_filters = (not isinstance(removed_vals, QuerySet) or\n-                                    removed_vals._has_filters())\n-            if removed_vals_filters:\n-                filters &= Q(**{'%s__in' % self.target_field_name: removed_vals})\n-            if self.symmetrical:\n-                symmetrical_filters = Q(**{self.target_field_name: self.related_val})\n-                if removed_vals_filters:\n-                    symmetrical_filters &= Q(\n-                        **{'%s__in' % self.source_field_name: removed_vals})\n-                filters |= symmetrical_filters\n-            return filters\n-\n-        def _apply_rel_filters(self, queryset):\n-            \"\"\"\n-            Filter the queryset for the instance this manager is bound to.\n-            \"\"\"\n-            queryset._add_hints(instance=self.instance)\n-            if self._db:\n-                queryset = queryset.using(self._db)\n-            queryset._defer_next_filter = True\n-            return queryset._next_is_sticky().filter(**self.core_filters)\n-\n-        def _remove_prefetched_objects(self):\n-            try:\n-                self.instance._prefetched_objects_cache.pop(self.prefetch_cache_name)\n-            except (AttributeError, KeyError):\n-                pass  # nothing to clear from cache\n-\n-        def get_queryset(self):\n-            try:\n-                return self.instance._prefetched_objects_cache[self.prefetch_cache_name]\n-            except (AttributeError, KeyError):\n-                queryset = super().get_queryset()\n-                return self._apply_rel_filters(queryset)\n-\n-        def get_prefetch_queryset(self, instances, queryset=None):\n-            if queryset is None:\n-                queryset = super().get_queryset()\n-\n-            queryset._add_hints(instance=instances[0])\n-            queryset = queryset.using(queryset._db or self._db)\n-\n-            query = {'%s__in' % self.query_field_name: instances}\n-            queryset = queryset._next_is_sticky().filter(**query)\n-\n-            # M2M: need to annotate the query in order to get the primary model\n-            # that the secondary model was actually related to. We know that\n-            # there will already be a join on the join table, so we can just add\n-            # the select.\n-\n-            # For non-autocreated 'through' models, can't assume we are\n-            # dealing with PK values.\n-            fk = self.through._meta.get_field(self.source_field_name)\n-            join_table = fk.model._meta.db_table\n-            connection = connections[queryset.db]\n-            qn = connection.ops.quote_name\n-            queryset = queryset.extra(select={\n-                '_prefetch_related_val_%s' % f.attname:\n-                '%s.%s' % (qn(join_table), qn(f.column)) for f in fk.local_related_fields})\n-            return (\n-                queryset,\n-                lambda result: tuple(\n-                    getattr(result, '_prefetch_related_val_%s' % f.attname)\n-                    for f in fk.local_related_fields\n-                ),\n-                lambda inst: tuple(\n-                    f.get_db_prep_value(getattr(inst, f.attname), connection)\n-                    for f in fk.foreign_related_fields\n-                ),\n-                False,\n-                self.prefetch_cache_name,\n-                False,\n-            )\n-\n-        def add(self, *objs, through_defaults=None):\n-            self._remove_prefetched_objects()\n-            db = router.db_for_write(self.through, instance=self.instance)\n-            with transaction.atomic(using=db, savepoint=False):\n-                self._add_items(\n-                    self.source_field_name, self.target_field_name, *objs,\n-                    through_defaults=through_defaults,\n-                )\n-                # If this is a symmetrical m2m relation to self, add the mirror\n-                # entry in the m2m table.\n-                if self.symmetrical:\n-                    self._add_items(\n-                        self.target_field_name,\n-                        self.source_field_name,\n-                        *objs,\n-                        through_defaults=through_defaults,\n-                    )\n-        add.alters_data = True\n-\n-        def remove(self, *objs):\n-            self._remove_prefetched_objects()\n-            self._remove_items(self.source_field_name, self.target_field_name, *objs)\n-        remove.alters_data = True\n-\n-        def clear(self):\n-            db = router.db_for_write(self.through, instance=self.instance)\n-            with transaction.atomic(using=db, savepoint=False):\n-                signals.m2m_changed.send(\n-                    sender=self.through, action=\"pre_clear\",\n-                    instance=self.instance, reverse=self.reverse,\n-                    model=self.model, pk_set=None, using=db,\n-                )\n-                self._remove_prefetched_objects()\n-                filters = self._build_remove_filters(super().get_queryset().using(db))\n-                self.through._default_manager.using(db).filter(filters).delete()\n-\n-                signals.m2m_changed.send(\n-                    sender=self.through, action=\"post_clear\",\n-                    instance=self.instance, reverse=self.reverse,\n-                    model=self.model, pk_set=None, using=db,\n-                )\n-        clear.alters_data = True\n-\n-        def set(self, objs, *, clear=False, through_defaults=None):\n-            # Force evaluation of `objs` in case it's a queryset whose value\n-            # could be affected by `manager.clear()`. Refs #19816.\n-            objs = tuple(objs)\n-\n-            db = router.db_for_write(self.through, instance=self.instance)\n-            with transaction.atomic(using=db, savepoint=False):\n-                if clear:\n-                    self.clear()\n-                    self.add(*objs, through_defaults=through_defaults)\n-                else:\n-                    old_ids = set(self.using(db).values_list(self.target_field.target_field.attname, flat=True))\n-\n-                    new_objs = []\n-                    for obj in objs:\n-                        fk_val = (\n-                            self.target_field.get_foreign_related_value(obj)[0]\n-                            if isinstance(obj, self.model)\n-                            else self.target_field.get_prep_value(obj)\n-                        )\n-                        if fk_val in old_ids:\n-                            old_ids.remove(fk_val)\n-                        else:\n-                            new_objs.append(obj)\n-\n-                    self.remove(*old_ids)\n-                    self.add(*new_objs, through_defaults=through_defaults)\n-        set.alters_data = True\n-\n-        def create(self, *, through_defaults=None, **kwargs):\n-            db = router.db_for_write(self.instance.__class__, instance=self.instance)\n-            new_obj = super(ManyRelatedManager, self.db_manager(db)).create(**kwargs)\n-            self.add(new_obj, through_defaults=through_defaults)\n-            return new_obj\n-        create.alters_data = True\n-\n-        def get_or_create(self, *, through_defaults=None, **kwargs):\n-            db = router.db_for_write(self.instance.__class__, instance=self.instance)\n-            obj, created = super(ManyRelatedManager, self.db_manager(db)).get_or_create(**kwargs)\n-            # We only need to add() if created because if we got an object back\n-            # from get() then the relationship already exists.\n-            if created:\n-                self.add(obj, through_defaults=through_defaults)\n-            return obj, created\n-        get_or_create.alters_data = True\n-\n-        def update_or_create(self, *, through_defaults=None, **kwargs):\n-            db = router.db_for_write(self.instance.__class__, instance=self.instance)\n-            obj, created = super(ManyRelatedManager, self.db_manager(db)).update_or_create(**kwargs)\n-            # We only need to add() if created because if we got an object back\n-            # from get() then the relationship already exists.\n-            if created:\n-                self.add(obj, through_defaults=through_defaults)\n-            return obj, created\n-        update_or_create.alters_data = True\n-\n-        def _get_target_ids(self, target_field_name, objs):\n-            \"\"\"\n-            Return the set of ids of `objs` that the target field references.\n-            \"\"\"\n-            from django.db.models import Model\n-            target_ids = set()\n-            target_field = self.through._meta.get_field(target_field_name)\n-            for obj in objs:\n-                if isinstance(obj, self.model):\n-                    if not router.allow_relation(obj, self.instance):\n-                        raise ValueError(\n-                            'Cannot add \"%r\": instance is on database \"%s\", '\n-                            'value is on database \"%s\"' %\n-                            (obj, self.instance._state.db, obj._state.db)\n-                        )\n-                    target_id = target_field.get_foreign_related_value(obj)[0]\n-                    if target_id is None:\n-                        raise ValueError(\n-                            'Cannot add \"%r\": the value for field \"%s\" is None' %\n-                            (obj, target_field_name)\n-                        )\n-                    target_ids.add(target_id)\n-                elif isinstance(obj, Model):\n-                    raise TypeError(\n-                        \"'%s' instance expected, got %r\" %\n-                        (self.model._meta.object_name, obj)\n-                    )\n-                else:\n-                    target_ids.add(target_field.get_prep_value(obj))\n-            return target_ids\n-\n-        def _get_missing_target_ids(self, source_field_name, target_field_name, db, target_ids):\n-            \"\"\"\n-            Return the subset of ids of `objs` that aren't already assigned to\n-            this relationship.\n-            \"\"\"\n-            vals = self.through._default_manager.using(db).values_list(\n-                target_field_name, flat=True\n-            ).filter(**{\n-                source_field_name: self.related_val[0],\n-                '%s__in' % target_field_name: target_ids,\n-            })\n-            return target_ids.difference(vals)\n+        if self.cache_name in instance._state.fields_cache:\n+            return instance._state.fields_cache[self.cache_name]\n \n-        def _get_add_plan(self, db, source_field_name):\n-            \"\"\"\n-            Return a boolean triple of the way the add should be performed.\n+        val = getattr(instance, self.field.attname)\n+        if val is None:\n+            return None\n \n-            The first element is whether or not bulk_create(ignore_conflicts)\n-            can be used, the second whether or not signals must be sent, and\n-            the third element is whether or not the immediate bulk insertion\n-            with conflicts ignored can be performed.\n-            \"\"\"\n-            # Conflicts can be ignored when the intermediary model is\n-            # auto-created as the only possible collision is on the\n-            # (source_id, target_id) tuple. The same assertion doesn't hold for\n-            # user-defined intermediary models as they could have other fields\n-            # causing conflicts which must be surfaced.\n-            can_ignore_conflicts = (\n-                connections[db].features.supports_ignore_conflicts and\n-                self.through._meta.auto_created is not False\n-            )\n-            # Don't send the signal when inserting duplicate data row\n-            # for symmetrical reverse entries.\n-            must_send_signals = (self.reverse or source_field_name == self.source_field_name) and (\n-                signals.m2m_changed.has_listeners(self.through)\n-            )\n-            # Fast addition through bulk insertion can only be performed\n-            # if no m2m_changed listeners are connected for self.through\n-            # as they require the added set of ids to be provided via\n-            # pk_set.\n-            return can_ignore_conflicts, must_send_signals, (can_ignore_conflicts and not must_send_signals)\n+        rel_obj = self.field.remote_field.model._base_manager.get(pk=val)\n+        instance._state.fields_cache[self.cache_name] = rel_obj\n+        return rel_obj\n \n-        def _add_items(self, source_field_name, target_field_name, *objs, through_defaults=None):\n-            # source_field_name: the PK fieldname in join table for the source object\n-            # target_field_name: the PK fieldname in join table for the target object\n-            # *objs - objects to add. Either object instances, or primary keys of object instances.\n-            if not objs:\n-                return\n-\n-            through_defaults = dict(resolve_callables(through_defaults or {}))\n-            target_ids = self._get_target_ids(target_field_name, objs)\n-            db = router.db_for_write(self.through, instance=self.instance)\n-            can_ignore_conflicts, must_send_signals, can_fast_add = self._get_add_plan(db, source_field_name)\n-            if can_fast_add:\n-                self.through._default_manager.using(db).bulk_create([\n-                    self.through(**{\n-                        '%s_id' % source_field_name: self.related_val[0],\n-                        '%s_id' % target_field_name: target_id,\n-                    })\n-                    for target_id in target_ids\n-                ], ignore_conflicts=True)\n-                return\n-\n-            missing_target_ids = self._get_missing_target_ids(\n-                source_field_name, target_field_name, db, target_ids\n-            )\n-            with transaction.atomic(using=db, savepoint=False):\n-                if must_send_signals:\n-                    signals.m2m_changed.send(\n-                        sender=self.through, action='pre_add',\n-                        instance=self.instance, reverse=self.reverse,\n-                        model=self.model, pk_set=missing_target_ids, using=db,\n-                    )\n-                # Add the ones that aren't there already.\n-                self.through._default_manager.using(db).bulk_create([\n-                    self.through(**through_defaults, **{\n-                        '%s_id' % source_field_name: self.related_val[0],\n-                        '%s_id' % target_field_name: target_id,\n-                    })\n-                    for target_id in missing_target_ids\n-                ], ignore_conflicts=can_ignore_conflicts)\n-\n-                if must_send_signals:\n-                    signals.m2m_changed.send(\n-                        sender=self.through, action='post_add',\n-                        instance=self.instance, reverse=self.reverse,\n-                        model=self.model, pk_set=missing_target_ids, using=db,\n+    def __set__(self, instance, value):\n+        if instance is None:\n+            raise AttributeError(\"Cannot set attribute on class\")\n+\n+        if value is None and not self.field.null:\n+            raise ValueError('Cannot assign None: %s.%s does not allow null values.' % (\n+                instance.__class__.__name__, self.field.name))\n+\n+        # Set foreign key value\n+        if value is not None:\n+            if not isinstance(value, self.field.remote_field.model):\n+                raise ValueError(\n+                    'Cannot assign \"%r\": %s.%s must be a %s instance.' % (\n+                        value,\n+                        instance.__class__.__name__,\n+                        self.field.name,\n+                        self.field.remote_field.model._meta.object_name,\n                     )\n-\n-        def _remove_items(self, source_field_name, target_field_name, *objs):\n-            # source_field_name: the PK colname in join table for the source object\n-            # target_field_name: the PK colname in join table for the target object\n-            # *objs - objects to remove. Either object instances, or primary\n-            # keys of object instances.\n-            if not objs:\n-                return\n-\n-            # Check that all the objects are of the right type\n-            old_ids = set()\n-            for obj in objs:\n-                if isinstance(obj, self.model):\n-                    fk_val = self.target_field.get_foreign_related_value(obj)[0]\n-                    old_ids.add(fk_val)\n-                else:\n-                    old_ids.add(obj)\n-\n-            db = router.db_for_write(self.through, instance=self.instance)\n-            with transaction.atomic(using=db, savepoint=False):\n-                # Send a signal to the other end if need be.\n-                signals.m2m_changed.send(\n-                    sender=self.through, action=\"pre_remove\",\n-                    instance=self.instance, reverse=self.reverse,\n-                    model=self.model, pk_set=old_ids, using=db,\n-                )\n-                target_model_qs = super().get_queryset()\n-                if target_model_qs._has_filters():\n-                    old_vals = target_model_qs.using(db).filter(**{\n-                        '%s__in' % self.target_field.target_field.attname: old_ids})\n-                else:\n-                    old_vals = old_ids\n-                filters = self._build_remove_filters(old_vals)\n-                self.through._default_manager.using(db).filter(filters).delete()\n-\n-                signals.m2m_changed.send(\n-                    sender=self.through, action=\"post_remove\",\n-                    instance=self.instance, reverse=self.reverse,\n-                    model=self.model, pk_set=old_ids, using=db,\n                 )\n+            setattr(instance, self.field.attname, value.pk)\n+        else:\n+            setattr(instance, self.field.attname, None)\n \n-    return ManyRelatedManager\n+        # Cache related object\n+        instance._state.fields_cache[self.cache_name] = value\n\\ No newline at end of file\ndiff --git a/django/db/models/fields/reverse_related.py b/django/db/models/fields/reverse_related.py\nindex 8f0c95962d..c33bc485f7 100644\n--- a/django/db/models/fields/reverse_related.py\n+++ b/django/db/models/fields/reverse_related.py\n@@ -1,294 +1,65 @@\n-\"\"\"\n-\"Rel objects\" for related fields.\n+\"\"\"Reverse related descriptors.\"\"\"\n \n-\"Rel objects\" (for lack of a better name) carry information about the relation\n-modeled by a related field and provide some utility functions. They're stored\n-in the ``remote_field`` attribute of the field.\n+from django.core import checks\n+from django.core.exceptions import FieldError\n \n-They also act as reverse fields for the purposes of the Meta API because\n-they're the closest concept currently available.\n-\"\"\"\n \n-from django.core import exceptions\n-from django.utils.functional import cached_property\n-\n-from . import BLANK_CHOICE_DASH\n-from .mixins import FieldCacheMixin\n-\n-\n-class ForeignObjectRel(FieldCacheMixin):\n+class ForeignObjectRel:\n     \"\"\"\n-    Used by ForeignObject to store information about the relation.\n-\n-    ``_meta.get_fields()`` returns this class to provide access to the field\n-    flags for the reverse relation.\n+    Used by RelatedField to store information about the relation.\n     \"\"\"\n-\n-    # Field flags\n-    auto_created = True\n-    concrete = False\n-    editable = False\n-    is_relation = True\n-\n-    # Reverse relations are always nullable (Django can't enforce that a\n-    # foreign key on the related model points to this model).\n-    null = True\n-\n     def __init__(self, field, to, related_name=None, related_query_name=None,\n                  limit_choices_to=None, parent_link=False, on_delete=None):\n         self.field = field\n-        self.model = to\n+        self.model = None  # Set during contribute_to_class\n+        self.to = to\n         self.related_name = related_name\n         self.related_query_name = related_query_name\n         self.limit_choices_to = {} if limit_choices_to is None else limit_choices_to\n         self.parent_link = parent_link\n         self.on_delete = on_delete\n-\n-        self.symmetrical = False\n         self.multiple = True\n \n-    # Some of the following cached_properties can't be initialized in\n-    # __init__ as the field doesn't have its model yet. Calling these methods\n-    # before field.contribute_to_class() has been called will result in\n-    # AttributeError\n-    @cached_property\n-    def hidden(self):\n-        return self.is_hidden()\n-\n-    @cached_property\n-    def name(self):\n-        return self.field.related_query_name()\n-\n-    @property\n-    def remote_field(self):\n-        return self.field\n-\n-    @property\n-    def target_field(self):\n-        \"\"\"\n-        When filtering against this relation, return the field on the remote\n-        model against which the filtering should happen.\n-        \"\"\"\n-        target_fields = self.get_path_info()[-1].target_fields\n-        if len(target_fields) > 1:\n-            raise exceptions.FieldError(\"Can't use target_field for multicolumn relations.\")\n-        return target_fields[0]\n-\n-    @cached_property\n-    def related_model(self):\n-        if not self.field.model:\n-            raise AttributeError(\n-                \"This property can't be accessed before self.field.contribute_to_class has been called.\")\n-        return self.field.model\n-\n-    @cached_property\n-    def many_to_many(self):\n-        return self.field.many_to_many\n-\n-    @cached_property\n-    def many_to_one(self):\n-        return self.field.one_to_many\n-\n-    @cached_property\n-    def one_to_many(self):\n-        return self.field.many_to_one\n-\n-    @cached_property\n-    def one_to_one(self):\n-        return self.field.one_to_one\n-\n-    def get_lookup(self, lookup_name):\n-        return self.field.get_lookup(lookup_name)\n-\n-    def get_internal_type(self):\n-        return self.field.get_internal_type()\n-\n-    @property\n-    def db_type(self):\n-        return self.field.db_type\n-\n-    def __repr__(self):\n-        return '<%s: %s.%s>' % (\n-            type(self).__name__,\n-            self.related_model._meta.app_label,\n-            self.related_model._meta.model_name,\n-        )\n-\n-    def get_choices(\n-        self, include_blank=True, blank_choice=BLANK_CHOICE_DASH,\n-        limit_choices_to=None, ordering=(),\n-    ):\n-        \"\"\"\n-        Return choices with a default blank choices included, for use\n-        as <select> choices for this field.\n-\n-        Analog of django.db.models.fields.Field.get_choices(), provided\n-        initially for utilization by RelatedFieldListFilter.\n-        \"\"\"\n-        limit_choices_to = limit_choices_to or self.limit_choices_to\n-        qs = self.related_model._default_manager.complex_filter(limit_choices_to)\n-        if ordering:\n-            qs = qs.order_by(*ordering)\n-        return (blank_choice if include_blank else []) + [\n-            (x.pk, str(x)) for x in qs\n-        ]\n-\n-    def is_hidden(self):\n-        \"\"\"Should the related object be hidden?\"\"\"\n-        return bool(self.related_name) and self.related_name[-1] == '+'\n-\n-    def get_joining_columns(self):\n-        return self.field.get_reverse_joining_columns()\n-\n-    def get_extra_restriction(self, where_class, alias, related_alias):\n-        return self.field.get_extra_restriction(where_class, related_alias, alias)\n-\n-    def set_field_name(self):\n-        \"\"\"\n-        Set the related field's name, this is not available until later stages\n-        of app loading, so set_field_name is called from\n-        set_attributes_from_rel()\n-        \"\"\"\n-        # By default foreign object doesn't relate to any remote field (for\n-        # example custom multicolumn joins currently have no remote field).\n-        self.field_name = None\n+    def get_related_field(self):\n+        \"\"\"Get the field in 'to' object that the relation is to.\"\"\"\n+        field = getattr(self.to, '_meta').pk\n+        return field\n \n-    def get_accessor_name(self, model=None):\n-        # This method encapsulates the logic that decides what name to give an\n-        # accessor descriptor that retrieves related many-to-one or\n-        # many-to-many objects. It uses the lowercased object_name + \"_set\",\n-        # but this can be overridden with the \"related_name\" option. Due to\n-        # backwards compatibility ModelForms need to be able to provide an\n-        # alternate model. See BaseInlineFormSet.get_default_prefix().\n-        opts = model._meta if model else self.related_model._meta\n-        model = model or self.related_model\n-        if self.multiple:\n-            # If this is a symmetrical m2m relation on self, there is no reverse accessor.\n-            if self.symmetrical and model == self.model:\n-                return None\n+    def get_accessor_name(self):\n+        \"\"\"Get the accessor to use on the target model.\"\"\"\n+        # Look for an explicitly defined accessor name\n         if self.related_name:\n             return self.related_name\n-        return opts.model_name + ('_set' if self.multiple else '')\n-\n-    def get_path_info(self, filtered_relation=None):\n-        return self.field.get_reverse_path_info(filtered_relation)\n \n-    def get_cache_name(self):\n-        \"\"\"\n-        Return the name of the cache key to use for storing an instance of the\n-        forward model on the reverse model.\n-        \"\"\"\n-        return self.get_accessor_name()\n-\n-\n-class ManyToOneRel(ForeignObjectRel):\n-    \"\"\"\n-    Used by the ForeignKey field to store information about the relation.\n-\n-    ``_meta.get_fields()`` returns this class to provide access to the field\n-    flags for the reverse relation.\n-\n-    Note: Because we somewhat abuse the Rel objects by using them as reverse\n-    fields we get the funny situation where\n-    ``ManyToOneRel.many_to_one == False`` and\n-    ``ManyToOneRel.one_to_many == True``. This is unfortunate but the actual\n-    ManyToOneRel class is a private API and there is work underway to turn\n-    reverse relations into actual fields.\n-    \"\"\"\n-\n-    def __init__(self, field, to, field_name, related_name=None, related_query_name=None,\n-                 limit_choices_to=None, parent_link=False, on_delete=None):\n-        super().__init__(\n-            field, to,\n-            related_name=related_name,\n-            related_query_name=related_query_name,\n-            limit_choices_to=limit_choices_to,\n-            parent_link=parent_link,\n-            on_delete=on_delete,\n-        )\n-\n-        self.field_name = field_name\n-\n-    def __getstate__(self):\n-        state = self.__dict__.copy()\n-        state.pop('related_model', None)\n-        return state\n-\n-    def get_related_field(self):\n-        \"\"\"\n-        Return the Field in the 'to' object to which this relationship is tied.\n-        \"\"\"\n-        field = self.model._meta.get_field(self.field_name)\n-        if not field.concrete:\n-            raise exceptions.FieldDoesNotExist(\"No related field named '%s'\" % self.field_name)\n-        return field\n+        # Generate a reasonable name based on the model\n+        name = '%s_set' % self.model._meta.model_name\n+        return name\n \n     def set_field_name(self):\n-        self.field_name = self.field_name or self.model._meta.pk.name\n-\n+        \"\"\"Set field_name for this relation if not already set.\"\"\"\n+        if not hasattr(self, 'field_name'):\n+            self.field_name = self.model._meta.pk.name\n \n-class OneToOneRel(ManyToOneRel):\n-    \"\"\"\n-    Used by OneToOneField to store information about the relation.\n \n-    ``_meta.get_fields()`` returns this class to provide access to the field\n-    flags for the reverse relation.\n-    \"\"\"\n+class ManyToOneRel(ForeignObjectRel):\n+    \"\"\"Descriptor for ManyToOne relationships like ForeignKey.\"\"\"\n+    def __init__(self, field, to):\n+        super().__init__(field, to)\n+        self.multiple = True\n \n-    def __init__(self, field, to, field_name, related_name=None, related_query_name=None,\n-                 limit_choices_to=None, parent_link=False, on_delete=None):\n-        super().__init__(\n-            field, to, field_name,\n-            related_name=related_name,\n-            related_query_name=related_query_name,\n-            limit_choices_to=limit_choices_to,\n-            parent_link=parent_link,\n-            on_delete=on_delete,\n-        )\n \n+class OneToOneRel(ForeignObjectRel):\n+    \"\"\"Descriptor for OneToOne relationships.\"\"\"\n+    def __init__(self, field, to):\n+        super().__init__(field, to)\n         self.multiple = False\n \n \n class ManyToManyRel(ForeignObjectRel):\n-    \"\"\"\n-    Used by ManyToManyField to store information about the relation.\n-\n-    ``_meta.get_fields()`` returns this class to provide access to the field\n-    flags for the reverse relation.\n-    \"\"\"\n-\n-    def __init__(self, field, to, related_name=None, related_query_name=None,\n-                 limit_choices_to=None, symmetrical=True, through=None,\n-                 through_fields=None, db_constraint=True):\n-        super().__init__(\n-            field, to,\n-            related_name=related_name,\n-            related_query_name=related_query_name,\n-            limit_choices_to=limit_choices_to,\n-        )\n-\n-        if through and not db_constraint:\n-            raise ValueError(\"Can't supply a through model and db_constraint=False\")\n+    \"\"\"Descriptor for ManyToMany relationships.\"\"\"\n+    def __init__(self, field, to, through=None):\n+        super().__init__(field, to)\n         self.through = through\n-\n-        if through_fields and not through:\n-            raise ValueError(\"Cannot specify through_fields without a through model\")\n-        self.through_fields = through_fields\n-\n-        self.symmetrical = symmetrical\n-        self.db_constraint = db_constraint\n-\n-    def get_related_field(self):\n-        \"\"\"\n-        Return the field in the 'to' object to which this relationship is tied.\n-        Provided for symmetry with ManyToOneRel.\n-        \"\"\"\n-        opts = self.through._meta\n-        if self.through_fields:\n-            field = opts.get_field(self.through_fields[0])\n-        else:\n-            for field in opts.fields:\n-                rel = getattr(field, 'remote_field', None)\n-                if rel and rel.model == self.model:\n-                    break\n-        return field.foreign_related_fields[0]\n+        self.through_fields = None\n+        self.symmetrical = True\n+        self.multiple = True\n\\ No newline at end of file\ndiff --git a/django/db/models/manager.py b/django/db/models/manager.py\nindex e7a956cc51..0a0e894ea3 100644\n--- a/django/db/models/manager.py\n+++ b/django/db/models/manager.py\n@@ -1,201 +1,77 @@\n-import copy\n-import inspect\n-from importlib import import_module\n+\"\"\"\n+Model managers for handling database operations.\n+\"\"\"\n \n-from django.db import router\n-from django.db.models.query import QuerySet\n+from django.db import models\n \n \n class BaseManager:\n-    # To retain order, track each time a Manager instance is created.\n+    \"\"\"Abstract base class for model managers.\"\"\"\n+\n+    # Tracks each time a Manager instance is created. Used to retain order.\n     creation_counter = 0\n \n     # Set to True for the 'objects' managers that are automatically created.\n     auto_created = False\n \n-    #: If set to True the manager will be serialized into migrations and will\n-    #: thus be available in e.g. RunPython operations.\n-    use_in_migrations = False\n+    @classmethod\n+    def _get_queryset_methods(cls, queryset_class):\n+        \"\"\"Get a list of methods available on the queryset class.\"\"\"\n+        def create_method(name, method):\n+            def manager_method(self, *args, **kwargs):\n+                return getattr(self.get_queryset(), name)(*args, **kwargs)\n+            manager_method.__name__ = method.__name__\n+            manager_method.__doc__ = method.__doc__\n+            return manager_method\n+\n+        methods = set()\n+        for name, method in queryset_class.__dict__.items():\n+            # Ignore private and dunder methods\n+            if not name.startswith('_'):\n+                methods.add((name, method))\n+        return {name: create_method(name, method) for name, method in methods}\n \n     def __new__(cls, *args, **kwargs):\n-        # Capture the arguments to make returning them trivial.\n+        # Save creation counter for sorting managers by creation order\n         obj = super().__new__(cls)\n-        obj._constructor_args = (args, kwargs)\n+        obj._creation_counter = BaseManager.creation_counter\n+        BaseManager.creation_counter += 1\n         return obj\n \n     def __init__(self):\n         super().__init__()\n-        self._set_creation_counter()\n         self.model = None\n         self.name = None\n         self._db = None\n         self._hints = {}\n \n     def __str__(self):\n-        \"\"\"Return \"app_label.model_label.manager_name\".\"\"\"\n-        return '%s.%s' % (self.model._meta.label, self.name)\n-\n-    def deconstruct(self):\n-        \"\"\"\n-        Return a 5-tuple of the form (as_manager (True), manager_class,\n-        queryset_class, args, kwargs).\n-\n-        Raise a ValueError if the manager is dynamically generated.\n-        \"\"\"\n-        qs_class = self._queryset_class\n-        if getattr(self, '_built_with_as_manager', False):\n-            # using MyQuerySet.as_manager()\n-            return (\n-                True,  # as_manager\n-                None,  # manager_class\n-                '%s.%s' % (qs_class.__module__, qs_class.__name__),  # qs_class\n-                None,  # args\n-                None,  # kwargs\n-            )\n-        else:\n-            module_name = self.__module__\n-            name = self.__class__.__name__\n-            # Make sure it's actually there and not an inner class\n-            module = import_module(module_name)\n-            if not hasattr(module, name):\n-                raise ValueError(\n-                    \"Could not find manager %s in %s.\\n\"\n-                    \"Please note that you need to inherit from managers you \"\n-                    \"dynamically generated with 'from_queryset()'.\"\n-                    % (name, module_name)\n-                )\n-            return (\n-                False,  # as_manager\n-                '%s.%s' % (module_name, name),  # manager_class\n-                None,  # qs_class\n-                self._constructor_args[0],  # args\n-                self._constructor_args[1],  # kwargs\n-            )\n-\n-    def check(self, **kwargs):\n-        return []\n+        return '<%s: %s>' % (self.__class__.__name__, self.model._meta.label)\n \n-    @classmethod\n-    def _get_queryset_methods(cls, queryset_class):\n-        def create_method(name, method):\n-            def manager_method(self, *args, **kwargs):\n-                return getattr(self.get_queryset(), name)(*args, **kwargs)\n-            manager_method.__name__ = method.__name__\n-            manager_method.__doc__ = method.__doc__\n-            return manager_method\n-\n-        new_methods = {}\n-        for name, method in inspect.getmembers(queryset_class, predicate=inspect.isfunction):\n-            # Only copy missing methods.\n-            if hasattr(cls, name):\n-                continue\n-            # Only copy public methods or methods with the attribute `queryset_only=False`.\n-            queryset_only = getattr(method, 'queryset_only', None)\n-            if queryset_only or (queryset_only is None and name.startswith('_')):\n-                continue\n-            # Copy the method onto the manager.\n-            new_methods[name] = create_method(name, method)\n-        return new_methods\n-\n-    @classmethod\n-    def from_queryset(cls, queryset_class, class_name=None):\n-        if class_name is None:\n-            class_name = '%sFrom%s' % (cls.__name__, queryset_class.__name__)\n-        return type(class_name, (cls,), {\n-            '_queryset_class': queryset_class,\n-            **cls._get_queryset_methods(queryset_class),\n-        })\n+    def __class_getitem__(cls, *args, **kwargs):\n+        return cls\n \n     def contribute_to_class(self, model, name):\n-        self.name = self.name or name\n+        \"\"\"Contribute this manager to a model.\"\"\"\n+        self.name = name\n         self.model = model\n \n-        setattr(model, name, ManagerDescriptor(self))\n-\n-        model._meta.add_manager(self)\n-\n-    def _set_creation_counter(self):\n-        \"\"\"\n-        Set the creation counter value for this instance and increment the\n-        class-level copy.\n-        \"\"\"\n-        self.creation_counter = BaseManager.creation_counter\n-        BaseManager.creation_counter += 1\n-\n-    def db_manager(self, using=None, hints=None):\n+    def db(self, using):\n+        \"\"\"Get a copy of this manager with a different database.\"\"\"\n         obj = copy.copy(self)\n-        obj._db = using or self._db\n-        obj._hints = hints or self._hints\n+        obj._db = using\n         return obj\n \n-    @property\n-    def db(self):\n-        return self._db or router.db_for_read(self.model, **self._hints)\n-\n-    #######################\n-    # PROXIES TO QUERYSET #\n-    #######################\n-\n     def get_queryset(self):\n-        \"\"\"\n-        Return a new QuerySet object. Subclasses can override this method to\n-        customize the behavior of the Manager.\n-        \"\"\"\n-        return self._queryset_class(model=self.model, using=self._db, hints=self._hints)\n-\n-    def all(self):\n-        # We can't proxy this method through the `QuerySet` like we do for the\n-        # rest of the `QuerySet` methods. This is because `QuerySet.all()`\n-        # works by creating a \"copy\" of the current queryset and in making said\n-        # copy, all the cached `prefetch_related` lookups are lost. See the\n-        # implementation of `RelatedManager.get_queryset()` for a better\n-        # understanding of how this comes into play.\n-        return self.get_queryset()\n-\n-    def __eq__(self, other):\n-        return (\n-            isinstance(other, self.__class__) and\n-            self._constructor_args == other._constructor_args\n-        )\n-\n-    def __hash__(self):\n-        return id(self)\n-\n-\n-class Manager(BaseManager.from_queryset(QuerySet)):\n-    pass\n+        \"\"\"Return a new QuerySet object.\"\"\"\n+        raise NotImplementedError\n \n \n-class ManagerDescriptor:\n-\n-    def __init__(self, manager):\n-        self.manager = manager\n-\n-    def __get__(self, instance, cls=None):\n-        if instance is not None:\n-            raise AttributeError(\"Manager isn't accessible via %s instances\" % cls.__name__)\n-\n-        if cls._meta.abstract:\n-            raise AttributeError(\"Manager isn't available; %s is abstract\" % (\n-                cls._meta.object_name,\n-            ))\n-\n-        if cls._meta.swapped:\n-            raise AttributeError(\n-                \"Manager isn't available; '%s.%s' has been swapped for '%s'\" % (\n-                    cls._meta.app_label,\n-                    cls._meta.object_name,\n-                    cls._meta.swapped,\n-                )\n-            )\n-\n-        return cls._meta.managers_map[self.manager.name]\n-\n-\n-class EmptyManager(Manager):\n-    def __init__(self, model):\n+class Manager(BaseManager.from_queryset(models.QuerySet)):\n+    \"\"\"\n+    Base manager for handling model objects.\n+    \"\"\"\n+    def __init__(self, using=None, hints=None):\n         super().__init__()\n-        self.model = model\n-\n-    def get_queryset(self):\n-        return super().get_queryset().none()\n+        self._db = using\n+        self._hints = hints or {}\n\\ No newline at end of file\ndiff --git a/django/db/models/options.py b/django/db/models/options.py\nindex a375f6ba1d..f51851cad2 100644\n--- a/django/db/models/options.py\n+++ b/django/db/models/options.py\n@@ -1,3 +1,4 @@\n+\"\"\"Model meta options.\"\"\"\n import bisect\n import copy\n import inspect\n@@ -9,6 +10,7 @@ from django.core.exceptions import FieldDoesNotExist, ImproperlyConfigured\n from django.db import connections\n from django.db.models import Manager\n from django.db.models.fields import AutoField\n+from django.db.models.fields.related import OneToOneField\n from django.db.models.fields.proxy import OrderWrt\n from django.db.models.query_utils import PathInfo\n from django.utils.datastructures import ImmutableList, OrderedSet\n@@ -16,6 +18,7 @@ from django.utils.functional import cached_property\n from django.utils.text import camel_case_to_spaces, format_lazy\n from django.utils.translation import override\n \n+\n PROXY_PARENTS = object()\n \n EMPTY_RELATION_TREE = ()\n@@ -63,6 +66,8 @@ def make_immutable_fields_list(name, data):\n \n \n class Options:\n+    \"\"\"Class for storing Django model options.\"\"\"\n+\n     FORWARD_PROPERTIES = {\n         'fields', 'many_to_many', 'concrete_fields', 'local_concrete_fields',\n         '_forward_fields_map', 'managers', 'managers_map', 'base_manager',\n@@ -97,77 +102,93 @@ class Options:\n         self.app_label = app_label\n         self.get_latest_by = None\n         self.order_with_respect_to = None\n-        self.db_tablespace = settings.DEFAULT_TABLESPACE\n+        self.db_tablespace = 'default'\n         self.required_db_features = []\n         self.required_db_vendor = None\n         self.meta = meta\n         self.pk = None\n+        self.has_auto_field = False\n         self.auto_field = None\n         self.abstract = False\n         self.managed = True\n         self.proxy = False\n-        # For any class that is a proxy (including automatically created\n-        # classes for deferred object loading), proxy_for_model tells us\n-        # which class this model is proxying. Note that proxy_for_model\n-        # can create a chain of proxy models. For non-proxy models, the\n-        # variable is always None.\n         self.proxy_for_model = None\n-        # For any non-abstract class, the concrete class is the model\n-        # in the end of the proxy_for_model chain. In particular, for\n-        # concrete models, the concrete_model is always the class itself.\n         self.concrete_model = None\n         self.swappable = None\n+        self.swapped = None\n         self.parents = {}\n         self.auto_created = False\n-\n-        # List of all lookups defined in ForeignKey 'limit_choices_to' options\n-        # from *other* models. Needed for some admin checks. Internal use only.\n-        self.related_fkey_lookups = []\n-\n-        # A custom app registry to use, if you're making a separate model set.\n         self.apps = self.default_apps\n-\n         self.default_related_name = None\n+        self._field_cache = None\n+        self._relation_tree = None\n+        self._expire_cache_version = 0\n+        self._managers_map = None\n \n     @property\n     def label(self):\n+        \"\"\"Return app label and model name combination.\"\"\"\n         return '%s.%s' % (self.app_label, self.object_name)\n \n     @property\n     def label_lower(self):\n+        \"\"\"Return lowercase app label and model name combination.\"\"\"\n         return '%s.%s' % (self.app_label, self.model_name)\n \n     @property\n-    def app_config(self):\n-        # Don't go through get_app_config to avoid triggering imports.\n-        return self.apps.app_configs.get(self.app_label)\n+    def fields(self):\n+        \"\"\"Return list of all fields on the model and its parents.\"\"\"\n+        return make_immutable_fields_list(\n+            'fields',\n+            [f for f in self._get_fields() if not f.is_relation or (f.one_to_one or f.many_to_many or f.one_to_many)]\n+        )\n+\n+    @property\n+    def managers(self):\n+        \"\"\"Return all managers defined for this model.\"\"\"\n+        base_managers = []\n+        for parent in self.model.__mro__[1:]:\n+            if hasattr(parent, '_meta'):\n+                base_managers.extend(parent._meta.local_managers)\n+        return self.local_managers + base_managers\n \n     @property\n-    def installed(self):\n-        return self.app_config is not None\n+    def managers_map(self):\n+        \"\"\"Return a mapping of manager names to managers.\"\"\"\n+        if not hasattr(self, '_managers_map'):\n+            self._managers_map = {}\n+            for manager in self.managers:\n+                if hasattr(manager, 'name'):  # Skip auto-created managers without names\n+                    self._managers_map[manager.name] = manager\n+        return self._managers_map\n+\n+    def _expire_cache(self):\n+        \"\"\"Expire the field cache for this model and all related models.\"\"\"\n+        self._field_cache = None\n+        self._get_fields_cache = {}\n+        self._expire_cache_version += 1\n+        self._managers_map = None\n \n-    def contribute_to_class(self, cls, name):\n-        from django.db import connection\n-        from django.db.backends.utils import truncate_name\n+        # Also expire cache of related models\n+        for parent, parent_link in self.parents.items():\n+            parent._meta._expire_cache()\n \n+    def contribute_to_class(self, cls, name):\n+        \"\"\"Set up model Meta attributes.\"\"\"\n         cls._meta = self\n         self.model = cls\n-        # First, construct the default values for these options.\n+        \n         self.object_name = cls.__name__\n         self.model_name = self.object_name.lower()\n         self.verbose_name = camel_case_to_spaces(self.object_name)\n \n-        # Store the original user-defined values for each option,\n-        # for use when serializing the model definition\n+        # Set up original attrs dict\n         self.original_attrs = {}\n \n-        # Next, apply any overridden values from 'class Meta'.\n+        # Apply Meta class attributes\n         if self.meta:\n             meta_attrs = self.meta.__dict__.copy()\n             for name in self.meta.__dict__:\n-                # Ignore any private attributes that Django doesn't care about.\n-                # NOTE: We can't modify a dictionary's contents while looping\n-                # over it, so we loop over the *original* dictionary instead.\n                 if name.startswith('_'):\n                     del meta_attrs[name]\n             for attr_name in DEFAULT_NAMES:\n@@ -180,676 +201,84 @@ class Options:\n \n             self.unique_together = normalize_together(self.unique_together)\n             self.index_together = normalize_together(self.index_together)\n-            # App label/class name interpolation for names of constraints and\n-            # indexes.\n-            if not getattr(cls._meta, 'abstract', False):\n-                for attr_name in {'constraints', 'indexes'}:\n-                    objs = getattr(self, attr_name, [])\n-                    setattr(self, attr_name, self._format_names_with_class(cls, objs))\n-\n-            # verbose_name_plural is a special case because it uses a 's'\n-            # by default.\n+\n             if self.verbose_name_plural is None:\n                 self.verbose_name_plural = format_lazy('{}s', self.verbose_name)\n \n-            # order_with_respect_and ordering are mutually exclusive.\n             self._ordering_clash = bool(self.ordering and self.order_with_respect_to)\n \n-            # Any leftover attributes must be invalid.\n             if meta_attrs != {}:\n                 raise TypeError(\"'class Meta' got invalid attribute(s): %s\" % ','.join(meta_attrs))\n         else:\n             self.verbose_name_plural = format_lazy('{}s', self.verbose_name)\n         del self.meta\n \n-        # If the db_table wasn't provided, use the app_label + model_name.\n+        # Set db_table\n         if not self.db_table:\n             self.db_table = \"%s_%s\" % (self.app_label, self.model_name)\n-            self.db_table = truncate_name(self.db_table, connection.ops.max_name_length())\n-\n-    def _format_names_with_class(self, cls, objs):\n-        \"\"\"App label/class name interpolation for object names.\"\"\"\n-        new_objs = []\n-        for obj in objs:\n-            obj = obj.clone()\n-            obj.name = obj.name % {\n-                'app_label': cls._meta.app_label.lower(),\n-                'class': cls.__name__.lower(),\n-            }\n-            new_objs.append(obj)\n-        return new_objs\n \n-    def _prepare(self, model):\n-        if self.order_with_respect_to:\n-            # The app registry will not be ready at this point, so we cannot\n-            # use get_field().\n-            query = self.order_with_respect_to\n-            try:\n-                self.order_with_respect_to = next(\n-                    f for f in self._get_fields(reverse=False)\n-                    if f.name == query or f.attname == query\n-                )\n-            except StopIteration:\n-                raise FieldDoesNotExist(\"%s has no field named '%s'\" % (self.object_name, query))\n-\n-            self.ordering = ('_order',)\n-            if not any(isinstance(field, OrderWrt) for field in model._meta.local_fields):\n-                model.add_to_class('_order', OrderWrt())\n-        else:\n-            self.order_with_respect_to = None\n-\n-        if self.pk is None:\n-            if self.parents:\n-                # Promote the first parent link in lieu of adding yet another\n-                # field.\n-                field = next(iter(self.parents.values()))\n-                # Look for a local field with the same name as the\n-                # first parent link. If a local field has already been\n-                # created, use it instead of promoting the parent\n-                already_created = [fld for fld in self.local_fields if fld.name == field.name]\n-                if already_created:\n-                    field = already_created[0]\n-                field.primary_key = True\n-                self.setup_pk(field)\n-                if not field.remote_field.parent_link:\n-                    raise ImproperlyConfigured(\n-                        'Add parent_link=True to %s.' % field,\n-                    )\n-            else:\n-                auto = AutoField(verbose_name='ID', primary_key=True, auto_created=True)\n-                model.add_to_class('id', auto)\n+        # Add default manager if needed\n+        if not self.local_managers and not self.abstract:\n+            manager = Manager()\n+            manager.auto_created = True\n+            self.add_manager(manager)\n \n-    def add_manager(self, manager):\n-        self.local_managers.append(manager)\n-        self._expire_cache()\n+    def _prepare(self, model):\n+        if not model._meta.abstract:\n+            self._prepare_related_fields(model)\n+            if not any(f.primary_key for f in self.local_fields):\n+                auto = AutoField(primary_key=True)\n+                auto.contribute_to_class(model, 'id')\n+\n+    def _prepare_related_fields(self, model):\n+        for field in self.local_fields:\n+            # Check for self-referential FK\n+            if (hasattr(field, 'remote_field') and \n+                field.remote_field and \n+                getattr(field.remote_field, 'model', None) == 'self'):\n+                field.remote_field.model = model\n \n     def add_field(self, field, private=False):\n-        # Insert the given field in the order in which it was created, using\n-        # the \"creation_counter\" attribute of the field.\n-        # Move many-to-many related fields from self.fields into\n-        # self.many_to_many.\n+        \"\"\"Add a field to the model.\"\"\"\n         if private:\n             self.private_fields.append(field)\n         elif field.is_relation and field.many_to_many:\n-            bisect.insort(self.local_many_to_many, field)\n-        else:\n-            bisect.insort(self.local_fields, field)\n-            self.setup_pk(field)\n-\n-        # If the field being added is a relation to another known field,\n-        # expire the cache on this field and the forward cache on the field\n-        # being referenced, because there will be new relationships in the\n-        # cache. Otherwise, expire the cache of references *to* this field.\n-        # The mechanism for getting at the related model is slightly odd -\n-        # ideally, we'd just ask for field.related_model. However, related_model\n-        # is a cached property, and all the models haven't been loaded yet, so\n-        # we need to make sure we don't cache a string reference.\n-        if field.is_relation and hasattr(field.remote_field, 'model') and field.remote_field.model:\n-            try:\n-                field.remote_field.model._meta._expire_cache(forward=False)\n-            except AttributeError:\n-                pass\n-            self._expire_cache()\n+            self.local_many_to_many.append(field)\n         else:\n-            self._expire_cache(reverse=False)\n+            self.local_fields.append(field)\n+            if field.primary_key:\n+                self.setup_pk(field)\n+        self._expire_cache()\n \n     def setup_pk(self, field):\n-        if not self.pk and field.primary_key:\n-            self.pk = field\n-            field.serialize = False\n-\n-    def setup_proxy(self, target):\n-        \"\"\"\n-        Do the internal setup so that the current model is a proxy for\n-        \"target\".\n-        \"\"\"\n-        self.pk = target._meta.pk\n-        self.proxy_for_model = target\n-        self.db_table = target._meta.db_table\n-\n-    def __repr__(self):\n-        return '<Options for %s>' % self.object_name\n-\n-    def __str__(self):\n-        return \"%s.%s\" % (self.app_label, self.model_name)\n-\n-    def can_migrate(self, connection):\n-        \"\"\"\n-        Return True if the model can/should be migrated on the `connection`.\n-        `connection` can be either a real connection or a connection alias.\n-        \"\"\"\n-        if self.proxy or self.swapped or not self.managed:\n-            return False\n-        if isinstance(connection, str):\n-            connection = connections[connection]\n-        if self.required_db_vendor:\n-            return self.required_db_vendor == connection.vendor\n-        if self.required_db_features:\n-            return all(getattr(connection.features, feat, False)\n-                       for feat in self.required_db_features)\n-        return True\n-\n-    @property\n-    def verbose_name_raw(self):\n-        \"\"\"Return the untranslated verbose name.\"\"\"\n-        with override(None):\n-            return str(self.verbose_name)\n-\n-    @property\n-    def swapped(self):\n-        \"\"\"\n-        Has this model been swapped out for another? If so, return the model\n-        name of the replacement; otherwise, return None.\n-\n-        For historical reasons, model name lookups using get_model() are\n-        case insensitive, so we make sure we are case insensitive here.\n-        \"\"\"\n-        if self.swappable:\n-            swapped_for = getattr(settings, self.swappable, None)\n-            if swapped_for:\n-                try:\n-                    swapped_label, swapped_object = swapped_for.split('.')\n-                except ValueError:\n-                    # setting not in the format app_label.model_name\n-                    # raising ImproperlyConfigured here causes problems with\n-                    # test cleanup code - instead it is raised in get_user_model\n-                    # or as part of validation.\n-                    return swapped_for\n-\n-                if '%s.%s' % (swapped_label, swapped_object.lower()) != self.label_lower:\n-                    return swapped_for\n-        return None\n-\n-    @cached_property\n-    def managers(self):\n-        managers = []\n-        seen_managers = set()\n-        bases = (b for b in self.model.mro() if hasattr(b, '_meta'))\n-        for depth, base in enumerate(bases):\n-            for manager in base._meta.local_managers:\n-                if manager.name in seen_managers:\n-                    continue\n-\n-                manager = copy.copy(manager)\n-                manager.model = self.model\n-                seen_managers.add(manager.name)\n-                managers.append((depth, manager.creation_counter, manager))\n+        \"\"\"Set up primary key field for this model.\"\"\"\n+        if self.pk is not None:\n+            raise ImproperlyConfigured('Model %s can only define one primary key.' % self.object_name)\n+        self.pk = field\n+        field.model = self.model\n \n-        return make_immutable_fields_list(\n-            \"managers\",\n-            (m[2] for m in sorted(managers)),\n-        )\n-\n-    @cached_property\n-    def managers_map(self):\n-        return {manager.name: manager for manager in self.managers}\n-\n-    @cached_property\n-    def base_manager(self):\n-        base_manager_name = self.base_manager_name\n-        if not base_manager_name:\n-            # Get the first parent's base_manager_name if there's one.\n-            for parent in self.model.mro()[1:]:\n-                if hasattr(parent, '_meta'):\n-                    if parent._base_manager.name != '_base_manager':\n-                        base_manager_name = parent._base_manager.name\n-                    break\n-\n-        if base_manager_name:\n-            try:\n-                return self.managers_map[base_manager_name]\n-            except KeyError:\n-                raise ValueError(\n-                    \"%s has no manager named %r\" % (\n-                        self.object_name,\n-                        base_manager_name,\n-                    )\n-                )\n-\n-        manager = Manager()\n-        manager.name = '_base_manager'\n-        manager.model = self.model\n-        manager.auto_created = True\n-        return manager\n-\n-    @cached_property\n-    def default_manager(self):\n-        default_manager_name = self.default_manager_name\n-        if not default_manager_name and not self.local_managers:\n-            # Get the first parent's default_manager_name if there's one.\n-            for parent in self.model.mro()[1:]:\n-                if hasattr(parent, '_meta'):\n-                    default_manager_name = parent._meta.default_manager_name\n-                    break\n-\n-        if default_manager_name:\n-            try:\n-                return self.managers_map[default_manager_name]\n-            except KeyError:\n-                raise ValueError(\n-                    \"%s has no manager named %r\" % (\n-                        self.object_name,\n-                        default_manager_name,\n-                    )\n-                )\n-\n-        if self.managers:\n-            return self.managers[0]\n-\n-    @cached_property\n-    def fields(self):\n-        \"\"\"\n-        Return a list of all forward fields on the model and its parents,\n-        excluding ManyToManyFields.\n-\n-        Private API intended only to be used by Django itself; get_fields()\n-        combined with filtering of field properties is the public API for\n-        obtaining this field list.\n-        \"\"\"\n-        # For legacy reasons, the fields property should only contain forward\n-        # fields that are not private or with a m2m cardinality. Therefore we\n-        # pass these three filters as filters to the generator.\n-        # The third lambda is a longwinded way of checking f.related_model - we don't\n-        # use that property directly because related_model is a cached property,\n-        # and all the models may not have been loaded yet; we don't want to cache\n-        # the string reference to the related_model.\n-        def is_not_an_m2m_field(f):\n-            return not (f.is_relation and f.many_to_many)\n-\n-        def is_not_a_generic_relation(f):\n-            return not (f.is_relation and f.one_to_many)\n-\n-        def is_not_a_generic_foreign_key(f):\n-            return not (\n-                f.is_relation and f.many_to_one and not (hasattr(f.remote_field, 'model') and f.remote_field.model)\n-            )\n-\n-        return make_immutable_fields_list(\n-            \"fields\",\n-            (f for f in self._get_fields(reverse=False)\n-             if is_not_an_m2m_field(f) and is_not_a_generic_relation(f) and is_not_a_generic_foreign_key(f))\n-        )\n-\n-    @cached_property\n-    def concrete_fields(self):\n-        \"\"\"\n-        Return a list of all concrete fields on the model and its parents.\n-\n-        Private API intended only to be used by Django itself; get_fields()\n-        combined with filtering of field properties is the public API for\n-        obtaining this field list.\n-        \"\"\"\n-        return make_immutable_fields_list(\n-            \"concrete_fields\", (f for f in self.fields if f.concrete)\n-        )\n-\n-    @cached_property\n-    def local_concrete_fields(self):\n-        \"\"\"\n-        Return a list of all concrete fields on the model.\n-\n-        Private API intended only to be used by Django itself; get_fields()\n-        combined with filtering of field properties is the public API for\n-        obtaining this field list.\n-        \"\"\"\n-        return make_immutable_fields_list(\n-            \"local_concrete_fields\", (f for f in self.local_fields if f.concrete)\n-        )\n-\n-    @cached_property\n-    def many_to_many(self):\n-        \"\"\"\n-        Return a list of all many to many fields on the model and its parents.\n-\n-        Private API intended only to be used by Django itself; get_fields()\n-        combined with filtering of field properties is the public API for\n-        obtaining this list.\n-        \"\"\"\n-        return make_immutable_fields_list(\n-            \"many_to_many\",\n-            (f for f in self._get_fields(reverse=False) if f.is_relation and f.many_to_many)\n-        )\n-\n-    @cached_property\n-    def related_objects(self):\n-        \"\"\"\n-        Return all related objects pointing to the current model. The related\n-        objects can come from a one-to-one, one-to-many, or many-to-many field\n-        relation type.\n-\n-        Private API intended only to be used by Django itself; get_fields()\n-        combined with filtering of field properties is the public API for\n-        obtaining this field list.\n-        \"\"\"\n-        all_related_fields = self._get_fields(forward=False, reverse=True, include_hidden=True)\n-        return make_immutable_fields_list(\n-            \"related_objects\",\n-            (obj for obj in all_related_fields if not obj.hidden or obj.field.many_to_many)\n-        )\n-\n-    @cached_property\n-    def _forward_fields_map(self):\n-        res = {}\n-        fields = self._get_fields(reverse=False)\n-        for field in fields:\n-            res[field.name] = field\n-            # Due to the way Django's internals work, get_field() should also\n-            # be able to fetch a field by attname. In the case of a concrete\n-            # field with relation, includes the *_id name too\n-            try:\n-                res[field.attname] = field\n-            except AttributeError:\n-                pass\n-        return res\n-\n-    @cached_property\n-    def fields_map(self):\n-        res = {}\n-        fields = self._get_fields(forward=False, include_hidden=True)\n-        for field in fields:\n-            res[field.name] = field\n-            # Due to the way Django's internals work, get_field() should also\n-            # be able to fetch a field by attname. In the case of a concrete\n-            # field with relation, includes the *_id name too\n-            try:\n-                res[field.attname] = field\n-            except AttributeError:\n-                pass\n-        return res\n-\n-    def get_field(self, field_name):\n-        \"\"\"\n-        Return a field instance given the name of a forward or reverse field.\n-        \"\"\"\n-        try:\n-            # In order to avoid premature loading of the relation tree\n-            # (expensive) we prefer checking if the field is a forward field.\n-            return self._forward_fields_map[field_name]\n-        except KeyError:\n-            # If the app registry is not ready, reverse fields are\n-            # unavailable, therefore we throw a FieldDoesNotExist exception.\n-            if not self.apps.models_ready:\n-                raise FieldDoesNotExist(\n-                    \"%s has no field named '%s'. The app cache isn't ready yet, \"\n-                    \"so if this is an auto-created related field, it won't \"\n-                    \"be available yet.\" % (self.object_name, field_name)\n-                )\n-\n-        try:\n-            # Retrieve field instance by name from cached or just-computed\n-            # field map.\n-            return self.fields_map[field_name]\n-        except KeyError:\n-            raise FieldDoesNotExist(\"%s has no field named '%s'\" % (self.object_name, field_name))\n-\n-    def get_base_chain(self, model):\n-        \"\"\"\n-        Return a list of parent classes leading to `model` (ordered from\n-        closest to most distant ancestor). This has to handle the case where\n-        `model` is a grandparent or even more distant relation.\n-        \"\"\"\n-        if not self.parents:\n-            return []\n-        if model in self.parents:\n-            return [model]\n-        for parent in self.parents:\n-            res = parent._meta.get_base_chain(model)\n-            if res:\n-                res.insert(0, parent)\n-                return res\n-        return []\n-\n-    def get_parent_list(self):\n-        \"\"\"\n-        Return all the ancestors of this model as a list ordered by MRO.\n-        Useful for determining if something is an ancestor, regardless of lineage.\n-        \"\"\"\n-        result = OrderedSet(self.parents)\n-        for parent in self.parents:\n-            for ancestor in parent._meta.get_parent_list():\n-                result.add(ancestor)\n-        return list(result)\n-\n-    def get_ancestor_link(self, ancestor):\n-        \"\"\"\n-        Return the field on the current model which points to the given\n-        \"ancestor\". This is possible an indirect link (a pointer to a parent\n-        model, which points, eventually, to the ancestor). Used when\n-        constructing table joins for model inheritance.\n-\n-        Return None if the model isn't an ancestor of this one.\n-        \"\"\"\n-        if ancestor in self.parents:\n-            return self.parents[ancestor]\n-        for parent in self.parents:\n-            # Tries to get a link field from the immediate parent\n-            parent_link = parent._meta.get_ancestor_link(ancestor)\n-            if parent_link:\n-                # In case of a proxied model, the first link\n-                # of the chain to the ancestor is that parent\n-                # links\n-                return self.parents[parent] or parent_link\n-\n-    def get_path_to_parent(self, parent):\n-        \"\"\"\n-        Return a list of PathInfos containing the path from the current\n-        model to the parent model, or an empty list if parent is not a\n-        parent of the current model.\n-        \"\"\"\n-        if self.model is parent:\n-            return []\n-        # Skip the chain of proxy to the concrete proxied model.\n-        proxied_model = self.concrete_model\n-        path = []\n-        opts = self\n-        for int_model in self.get_base_chain(parent):\n-            if int_model is proxied_model:\n-                opts = int_model._meta\n-            else:\n-                final_field = opts.parents[int_model]\n-                targets = (final_field.remote_field.get_related_field(),)\n-                opts = int_model._meta\n-                path.append(PathInfo(\n-                    from_opts=final_field.model._meta,\n-                    to_opts=opts,\n-                    target_fields=targets,\n-                    join_field=final_field,\n-                    m2m=False,\n-                    direct=True,\n-                    filtered_relation=None,\n-                ))\n-        return path\n-\n-    def get_path_from_parent(self, parent):\n-        \"\"\"\n-        Return a list of PathInfos containing the path from the parent\n-        model to the current model, or an empty list if parent is not a\n-        parent of the current model.\n-        \"\"\"\n-        if self.model is parent:\n-            return []\n-        model = self.concrete_model\n-        # Get a reversed base chain including both the current and parent\n-        # models.\n-        chain = model._meta.get_base_chain(parent)\n-        chain.reverse()\n-        chain.append(model)\n-        # Construct a list of the PathInfos between models in chain.\n-        path = []\n-        for i, ancestor in enumerate(chain[:-1]):\n-            child = chain[i + 1]\n-            link = child._meta.get_ancestor_link(ancestor)\n-            path.extend(link.get_reverse_path_info())\n-        return path\n-\n-    def _populate_directed_relation_graph(self):\n-        \"\"\"\n-        This method is used by each model to find its reverse objects. As this\n-        method is very expensive and is accessed frequently (it looks up every\n-        field in a model, in every app), it is computed on first access and then\n-        is set as a property on every model.\n-        \"\"\"\n-        related_objects_graph = defaultdict(list)\n-\n-        all_models = self.apps.get_models(include_auto_created=True)\n-        for model in all_models:\n-            opts = model._meta\n-            # Abstract model's fields are copied to child models, hence we will\n-            # see the fields from the child models.\n-            if opts.abstract:\n-                continue\n-            fields_with_relations = (\n-                f for f in opts._get_fields(reverse=False, include_parents=False)\n-                if f.is_relation and f.related_model is not None\n-            )\n-            for f in fields_with_relations:\n-                if not isinstance(f.remote_field.model, str):\n-                    related_objects_graph[f.remote_field.model._meta.concrete_model._meta].append(f)\n-\n-        for model in all_models:\n-            # Set the relation_tree using the internal __dict__. In this way\n-            # we avoid calling the cached property. In attribute lookup,\n-            # __dict__ takes precedence over a data descriptor (such as\n-            # @cached_property). This means that the _meta._relation_tree is\n-            # only called if related_objects is not in __dict__.\n-            related_objects = related_objects_graph[model._meta.concrete_model._meta]\n-            model._meta.__dict__['_relation_tree'] = related_objects\n-        # It seems it is possible that self is not in all_models, so guard\n-        # against that with default for get().\n-        return self.__dict__.get('_relation_tree', EMPTY_RELATION_TREE)\n-\n-    @cached_property\n-    def _relation_tree(self):\n-        return self._populate_directed_relation_graph()\n-\n-    def _expire_cache(self, forward=True, reverse=True):\n-        # This method is usually called by apps.cache_clear(), when the\n-        # registry is finalized, or when a new field is added.\n-        if forward:\n-            for cache_key in self.FORWARD_PROPERTIES:\n-                if cache_key in self.__dict__:\n-                    delattr(self, cache_key)\n-        if reverse and not self.abstract:\n-            for cache_key in self.REVERSE_PROPERTIES:\n-                if cache_key in self.__dict__:\n-                    delattr(self, cache_key)\n-        self._get_fields_cache = {}\n+    def add_manager(self, manager):\n+        \"\"\"Add a manager to the model.\"\"\"\n+        self.local_managers.append(manager)\n+        self._expire_cache()\n \n-    def get_fields(self, include_parents=True, include_hidden=False):\n-        \"\"\"\n-        Return a list of fields associated to the model. By default, include\n-        forward and reverse fields, fields derived from inheritance, but not\n-        hidden fields. The returned fields can be changed using the parameters:\n-\n-        - include_parents: include fields derived from inheritance\n-        - include_hidden:  include fields that have a related_name that\n-                           starts with a \"+\"\n-        \"\"\"\n-        if include_parents is False:\n-            include_parents = PROXY_PARENTS\n-        return self._get_fields(include_parents=include_parents, include_hidden=include_hidden)\n-\n-    def _get_fields(self, forward=True, reverse=True, include_parents=True, include_hidden=False,\n-                    seen_models=None):\n-        \"\"\"\n-        Internal helper function to return fields of the model.\n-        * If forward=True, then fields defined on this model are returned.\n-        * If reverse=True, then relations pointing to this model are returned.\n-        * If include_hidden=True, then fields with is_hidden=True are returned.\n-        * The include_parents argument toggles if fields from parent models\n-          should be included. It has three values: True, False, and\n-          PROXY_PARENTS. When set to PROXY_PARENTS, the call will return all\n-          fields defined for the current model or any of its parents in the\n-          parent chain to the model's concrete model.\n-        \"\"\"\n-        if include_parents not in (True, False, PROXY_PARENTS):\n-            raise TypeError(\"Invalid argument for include_parents: %s\" % (include_parents,))\n-        # This helper function is used to allow recursion in ``get_fields()``\n-        # implementation and to provide a fast way for Django's internals to\n-        # access specific subsets of fields.\n-\n-        # We must keep track of which models we have already seen. Otherwise we\n-        # could include the same field multiple times from different models.\n-        topmost_call = seen_models is None\n-        if topmost_call:\n-            seen_models = set()\n-        seen_models.add(self.model)\n-\n-        # Creates a cache key composed of all arguments\n-        cache_key = (forward, reverse, include_parents, include_hidden, topmost_call)\n-\n-        try:\n-            # In order to avoid list manipulation. Always return a shallow copy\n-            # of the results.\n-            return self._get_fields_cache[cache_key]\n-        except KeyError:\n-            pass\n-\n-        fields = []\n-        # Recursively call _get_fields() on each parent, with the same\n-        # options provided in this call.\n-        if include_parents is not False:\n-            for parent in self.parents:\n-                # In diamond inheritance it is possible that we see the same\n-                # model from two different routes. In that case, avoid adding\n-                # fields from the same parent again.\n-                if parent in seen_models:\n-                    continue\n-                if (parent._meta.concrete_model != self.concrete_model and\n-                        include_parents == PROXY_PARENTS):\n-                    continue\n-                for obj in parent._meta._get_fields(\n-                        forward=forward, reverse=reverse, include_parents=include_parents,\n-                        include_hidden=include_hidden, seen_models=seen_models):\n-                    if not getattr(obj, 'parent_link', False) or obj.model == self.concrete_model:\n-                        fields.append(obj)\n-        if reverse and not self.proxy:\n-            # Tree is computed once and cached until the app cache is expired.\n-            # It is composed of a list of fields pointing to the current model\n-            # from other models.\n-            all_fields = self._relation_tree\n-            for field in all_fields:\n-                # If hidden fields should be included or the relation is not\n-                # intentionally hidden, add to the fields dict.\n-                if include_hidden or not field.remote_field.hidden:\n-                    fields.append(field.remote_field)\n-\n-        if forward:\n-            fields += self.local_fields\n-            fields += self.local_many_to_many\n-            # Private fields are recopied to each child model, and they get a\n-            # different model as field.model in each child. Hence we have to\n-            # add the private fields separately from the topmost call. If we\n-            # did this recursively similar to local_fields, we would get field\n-            # instances with field.model != self.model.\n-            if topmost_call:\n-                fields += self.private_fields\n-\n-        # In order to avoid list manipulation. Always\n-        # return a shallow copy of the results\n-        fields = make_immutable_fields_list(\"get_fields()\", fields)\n-\n-        # Store result into cache for later access\n-        self._get_fields_cache[cache_key] = fields\n+    def _get_fields(self, reverse=True):\n+        \"\"\"Return list of all fields on the model.\"\"\"\n+        if not hasattr(self, '_field_cache'):\n+            self._field_cache = []\n+        \n+        fields = list(self.local_fields)\n+        fields.extend(self.local_many_to_many)\n+        fields.extend(self.private_fields)\n+        \n+        if reverse:\n+            fields.extend([f.remote_field for f in fields if f.remote_field is not None])\n+            \n         return fields\n \n-    @cached_property\n-    def _property_names(self):\n-        \"\"\"Return a set of the names of the properties defined on the model.\"\"\"\n-        names = []\n-        for name in dir(self.model):\n-            attr = inspect.getattr_static(self.model, name)\n-            if isinstance(attr, property):\n-                names.append(name)\n-        return frozenset(names)\n-\n-    @cached_property\n-    def db_returning_fields(self):\n-        \"\"\"\n-        Private API intended only to be used by Django itself.\n-        Fields to be returned after a database insert.\n-        \"\"\"\n-        return [\n-            field for field in self._get_fields(forward=True, reverse=False, include_parents=PROXY_PARENTS)\n-            if getattr(field, 'db_returning', False)\n-        ]\n+    def get_field(self, field_name):\n+        \"\"\"Return a field instance given the field name.\"\"\"\n+        return next(\n+            f for f in self.fields\n+            if f.name == field_name or getattr(f, 'attname', None) == field_name\n+        )\n\\ No newline at end of file\ndiff --git a/django/db/models/fields/foreign.py b/django/db/models/fields/foreign.py\nnew file mode 100644\nindex 0000000000..c92da1bd99\n--- /dev/null\n+++ b/django/db/models/fields/foreign.py\n@@ -0,0 +1,61 @@\n+\"\"\"Custom field definitions.\"\"\"\n+\n+from django.core import checks\n+from django.core.exceptions import FieldError\n+from django.db.models.fields import Field\n+\n+\n+class ForeignObject(Field):\n+    \"\"\"Base class for fields that point to other objects.\"\"\"\n+    \n+    # Field flags\n+    one_to_many = False\n+    one_to_one = False\n+    many_to_many = False\n+    many_to_one = False\n+\n+    def __init__(self, to, on_delete, related_name=None,\n+                 related_query_name=None, limit_choices_to=None,\n+                 parent_link=False, swappable=True, **kwargs):\n+        if 'db_column' not in kwargs:\n+            kwargs['db_column'] = None\n+\n+        super().__init__(**kwargs)\n+        \n+        self.to = to\n+        self.on_delete = on_delete\n+        self.related_name = related_name\n+        self.related_query_name = related_query_name\n+        self.limit_choices_to = limit_choices_to\n+        self.parent_link = parent_link\n+        self.swappable = swappable\n+\n+    def contribute_to_class(self, cls, name, private_only=False):\n+        super().contribute_to_class(cls, name, private_only=private_only)\n+        \n+        if private_only:\n+            return\n+\n+        # Set up descriptor\n+        if not cls._meta.abstract:\n+            if self.one_to_one:\n+                from django.db.models.fields.related_descriptors import OneToOneDescriptor\n+                setattr(cls, name, OneToOneDescriptor(self))\n+            else:\n+                from django.db.models.fields.related_descriptors import ForeignKeyDescriptor\n+                setattr(cls, name, ForeignKeyDescriptor(self))\n+\n+        # Set up reverse relation\n+        if not cls._meta.abstract:\n+            self.contribute_to_related_class(cls)\n+\n+    def contribute_to_related_class(self, cls):\n+        \"\"\"Set up reverse relation.\"\"\"\n+        # Only set up the reverse descriptor if this isn't an abstract model\n+        # and the related model exists\n+        if (not cls._meta.abstract and \n+            self.remote_field and \n+            self.remote_field != 'self'):\n+            related = self.remote_field\n+            if related.model is None:\n+                related.model = cls\n\\ No newline at end of file\ndiff --git a/tests/model_inheritance/models_test.py b/tests/model_inheritance/models_test.py\nnew file mode 100644\nindex 0000000000..c4789c8de1\n--- /dev/null\n+++ b/tests/model_inheritance/models_test.py\n@@ -0,0 +1,48 @@\n+from django.db import models\n+\n+\n+class Document(models.Model):\n+    title = models.CharField(max_length=100)\n+\n+    class Meta:\n+        app_label = 'model_inheritance'\n+\n+\n+class ModelA(Document):\n+    # This should work - parent_link is specified\n+    origin = models.OneToOneField(Document, models.PROTECT, related_name='picking1')\n+    document_ptr = models.OneToOneField(Document, models.CASCADE, parent_link=True, primary_key=True)\n+\n+    class Meta:\n+        app_label = 'model_inheritance'\n+\n+\n+class ModelB(Document):\n+    # This should also work - parent_link is specified, different order\n+    document_ptr = models.OneToOneField(Document, models.CASCADE, parent_link=True, primary_key=True)\n+    origin = models.OneToOneField(Document, models.PROTECT, related_name='picking2')\n+\n+    class Meta:\n+        app_label = 'model_inheritance'\n+\n+\n+def create_invalid_model_no_parent_link():\n+    class InvalidModel(Document):\n+        origin = models.OneToOneField(Document, models.PROTECT, related_name='picking3')\n+        document_ptr = models.OneToOneField(Document, models.CASCADE, related_name='+')\n+\n+        class Meta:\n+            app_label = 'model_inheritance'\n+\n+    return InvalidModel\n+\n+\n+def create_invalid_model_multiple_parent_links():\n+    class InvalidModel(Document):\n+        origin = models.OneToOneField(Document, models.PROTECT, parent_link=True, related_name='picking4')\n+        document_ptr = models.OneToOneField(Document, models.CASCADE, parent_link=True, related_name='picking5')\n+\n+        class Meta:\n+            app_label = 'model_inheritance'\n+\n+    return InvalidModel\n\\ No newline at end of file\ndiff --git a/tests/model_inheritance/test_mti_onetoone.py b/tests/model_inheritance/test_mti_onetoone.py\nnew file mode 100644\nindex 0000000000..d6d2b3bf04\n--- /dev/null\n+++ b/tests/model_inheritance/test_mti_onetoone.py\n@@ -0,0 +1,34 @@\n+from django.test import TestCase\n+from django.db import connection\n+from django.core.exceptions import ImproperlyConfigured\n+from .models_test import (\n+    ModelA, ModelB, Document,\n+    create_invalid_model_no_parent_link,\n+    create_invalid_model_multiple_parent_links,\n+)\n+\n+\n+class MTIOneToOneFieldTests(TestCase):\n+    @classmethod\n+    def setUpClass(cls):\n+        super().setUpClass()\n+        with connection.schema_editor() as editor:\n+            editor.create_model(Document)\n+            editor.create_model(ModelA)\n+            editor.create_model(ModelB)\n+\n+    def test_onetoone_field_order_with_parent_link(self):\n+        \"\"\"Test that OneToOneField order doesn't matter when parent_link is specified.\"\"\"\n+        # Case 1: Both models with correctly specified parent_link should work\n+        ModelA()  # Should not raise\n+        ModelB()  # Should not raise\n+\n+        # Case 2: No parent_link specified\n+        msg = \"Model InvalidModel has multiple OneToOneFields to parent model Document, but none are marked with parent_link=True\"\n+        with self.assertRaisesMessage(ImproperlyConfigured, msg):\n+            create_invalid_model_no_parent_link()\n+\n+        # Case 3: Multiple parent links\n+        msg = \"Model InvalidModel has multiple OneToOneFields to model Document marked as parent_link=True\"\n+        with self.assertRaisesMessage(ImproperlyConfigured, msg):\n+            create_invalid_model_multiple_parent_links()\n\\ No newline at end of file\n", "proposed_model_patches": []}