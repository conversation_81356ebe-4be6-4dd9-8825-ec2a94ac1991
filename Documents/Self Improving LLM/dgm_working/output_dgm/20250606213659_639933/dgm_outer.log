2025-06-06 21:36:59,680 - MainThread - INFO - Starting DGM run 20250606213659_639933 with arguments: {'max_generation': 50, 'selfimprove_size': 2, 'selfimprove_workers': 2, 'choose_selfimproves_method': 'score_child_prop', 'continue_from': None, 'update_archive': 'keep_all', 'num_swe_evals': 1, 'post_improve_diagnose': False, 'shallow_eval': False, 'polyglot': False, 'eval_noise': 0.1, 'no_full_eval': False, 'run_baseline': None}
2025-06-06 21:36:59,680 - MainThread - INFO - Archive: ['initial']
2025-06-06 21:36:59,689 - MainThread - INFO - Self-improve entries for generation 0: [('initial', 'django__django-11815'), ('initial', 'solve_empty_patches')]
2025-06-06 21:38:29,342 - MainThread - ERROR - Self-improvement step failed: <PERSON><PERSON><PERSON> failed with exit code 128
2025-06-06 21:38:29,343 - MainThread - ERROR - Traceback:
Traceback (most recent call last):
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/DGM_outer.py", line 305, in main
    metadata = future.result(timeout=1.5*60*60)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/usr/lib/python3.12/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/self_improve_step.py", line 315, in self_improve
    log_container_output(exec_result)
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/utils/docker_utils.py", line 277, in log_container_output
    raise Exception(error_msg)
Exception: Script failed with exit code 128

2025-06-06 21:38:29,463 - MainThread - ERROR - Self-improvement step failed: Script failed with exit code 128
2025-06-06 21:38:29,463 - MainThread - ERROR - Traceback:
Traceback (most recent call last):
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/DGM_outer.py", line 305, in main
    metadata = future.result(timeout=1.5*60*60)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/usr/lib/python3.12/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/self_improve_step.py", line 315, in self_improve
    log_container_output(exec_result)
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/utils/docker_utils.py", line 277, in log_container_output
    raise Exception(error_msg)
Exception: Script failed with exit code 128

2025-06-06 21:38:29,463 - MainThread - INFO - Updating archive for generation 0
2025-06-06 21:38:29,463 - MainThread - INFO - num_swe_issues: [10, 50]
2025-06-06 21:38:29,470 - MainThread - INFO - Self-improve entries for generation 1: [('initial', 'solve_stochasticity'), ('initial', 'django__django-12708')]
2025-06-06 21:40:17,006 - MainThread - ERROR - Self-improvement step failed: Script failed with exit code 128
2025-06-06 21:40:17,006 - MainThread - ERROR - Traceback:
Traceback (most recent call last):
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/DGM_outer.py", line 305, in main
    metadata = future.result(timeout=1.5*60*60)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/usr/lib/python3.12/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/self_improve_step.py", line 315, in self_improve
    log_container_output(exec_result)
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/utils/docker_utils.py", line 277, in log_container_output
    raise Exception(error_msg)
Exception: Script failed with exit code 128

2025-06-06 21:40:17,007 - MainThread - ERROR - Self-improvement step failed: Script failed with exit code 128
2025-06-06 21:40:17,007 - MainThread - ERROR - Traceback:
Traceback (most recent call last):
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/DGM_outer.py", line 305, in main
    metadata = future.result(timeout=1.5*60*60)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/usr/lib/python3.12/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/self_improve_step.py", line 315, in self_improve
    log_container_output(exec_result)
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/utils/docker_utils.py", line 277, in log_container_output
    raise Exception(error_msg)
Exception: Script failed with exit code 128

2025-06-06 21:40:17,007 - MainThread - INFO - Updating archive for generation 1
2025-06-06 21:40:17,007 - MainThread - INFO - num_swe_issues: [10, 50]
2025-06-06 21:40:17,014 - MainThread - INFO - Self-improve entries for generation 2: [('initial', 'sphinx-doc__sphinx-10673'), ('initial', 'solve_empty_patches')]
2025-06-06 21:42:15,220 - MainThread - ERROR - Self-improvement step failed: Script failed with exit code 128
2025-06-06 21:42:15,221 - MainThread - ERROR - Traceback:
Traceback (most recent call last):
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/DGM_outer.py", line 305, in main
    metadata = future.result(timeout=1.5*60*60)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/usr/lib/python3.12/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/self_improve_step.py", line 315, in self_improve
    log_container_output(exec_result)
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/utils/docker_utils.py", line 277, in log_container_output
    raise Exception(error_msg)
Exception: Script failed with exit code 128

2025-06-06 21:42:15,221 - MainThread - ERROR - Self-improvement step failed: Script failed with exit code 128
2025-06-06 21:42:15,221 - MainThread - ERROR - Traceback:
Traceback (most recent call last):
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/DGM_outer.py", line 305, in main
    metadata = future.result(timeout=1.5*60*60)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/concurrent/futures/_base.py", line 449, in result
    return self.__get_result()
           ^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/concurrent/futures/_base.py", line 401, in __get_result
    raise self._exception
  File "/usr/lib/python3.12/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/self_improve_step.py", line 315, in self_improve
    log_container_output(exec_result)
  File "/home/<USER>/Documents/Self Improving LLM/dgm_working/utils/docker_utils.py", line 277, in log_container_output
    raise Exception(error_msg)
Exception: Script failed with exit code 128

2025-06-06 21:42:15,221 - MainThread - INFO - Updating archive for generation 2
2025-06-06 21:42:15,221 - MainThread - INFO - num_swe_issues: [10, 50]
2025-06-06 21:42:15,235 - MainThread - INFO - Self-improve entries for generation 3: [('initial', 'sphinx-doc__sphinx-9461'), ('initial', 'sphinx-doc__sphinx-7757')]
