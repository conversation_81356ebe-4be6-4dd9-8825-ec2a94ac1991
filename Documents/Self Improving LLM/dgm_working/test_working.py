#!/usr/bin/env python3
"""
Quick test to verify the working DGM setup.
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

def test_imports():
    """Test that all required modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        import llm
        import llm_withtools
        import coding_agent
        import self_improve_step
        from tools import load_all_tools
        print("   ✅ All core modules imported successfully")
        return True
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False

def test_ollama_connection():
    """Test connection to Ollama."""
    print("🤖 Testing Ollama connection...")
    
    try:
        from llm import create_client
        client, model = create_client('qwen3:30b-a3b')
        print(f"   ✅ Connected to Ollama with model: {model}")
        return True
    except Exception as e:
        print(f"   ❌ Ollama connection failed: {e}")
        return False

def test_tools():
    """Test that tools can be loaded and used."""
    print("🔧 Testing tools...")
    
    try:
        from tools import load_all_tools
        tools = load_all_tools()
        
        if not tools:
            print("   ❌ No tools loaded")
            return False
            
        print(f"   ✅ Loaded {len(tools)} tools:")
        for tool in tools:
            print(f"      - {tool['info']['name']}")
        return True
    except Exception as e:
        print(f"   ❌ Tools test failed: {e}")
        return False

def test_tool_usage():
    """Test actual tool usage with qwen3:30b-a3b."""
    print("🛠️ Testing tool usage...")
    
    try:
        from llm_withtools import chat_with_agent
        
        # Create a temporary file for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            test_file = Path(temp_dir) / "test.py"
            test_file.write_text("print('hello world')")
            
            # Test message
            msg = f"""Please modify the file {test_file} to print 'hello working' instead.

Use the editor tool with these parameters:
- command: "edit"
- path: "{test_file}"
- file_text: "print('hello working')"
"""
            
            # Call the agent
            msg_history = chat_with_agent(
                msg=msg,
                model='qwen3:30b-a3b',
                msg_history=[],
                logging=lambda x: None  # Silent logging for test
            )
            
            # Check if file was modified
            new_content = test_file.read_text()
            if 'working' in new_content:
                print("   ✅ Tool usage successful - file was modified")
                return True
            else:
                print("   ❌ Tool usage failed - file was not modified")
                return False
                
    except Exception as e:
        print(f"   ❌ Tool usage test failed: {e}")
        return False

def test_docker():
    """Test Docker availability."""
    print("🐳 Testing Docker...")
    
    try:
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"   ✅ Docker available: {result.stdout.strip()}")
            return True
        else:
            print("   ❌ Docker not working")
            return False
    except Exception as e:
        print(f"   ❌ Docker test failed: {e}")
        return False

def test_git():
    """Test Git configuration."""
    print("🔧 Testing Git configuration...")
    
    try:
        name = subprocess.run(['git', 'config', '--global', 'user.name'], 
                            capture_output=True, text=True).stdout.strip()
        email = subprocess.run(['git', 'config', '--global', 'user.email'], 
                             capture_output=True, text=True).stdout.strip()
        
        if name and email:
            print(f"   ✅ Git configured: {name} <{email}>")
            return True
        else:
            print("   ❌ Git not properly configured")
            return False
    except Exception as e:
        print(f"   ❌ Git test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 **DGM WORKING DIRECTORY TEST**")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Ollama Connection", test_ollama_connection),
        ("Tools Loading", test_tools),
        ("Tool Usage", test_tool_usage),
        ("Docker", test_docker),
        ("Git", test_git),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
            print()
        except Exception as e:
            print(f"   ❌ {test_name} test crashed: {e}")
            results.append(False)
            print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("📊 **TEST SUMMARY**")
    print("-" * 30)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! DGM working directory is ready!")
        print("\n🚀 You can now run:")
        print("   python DGM_outer.py --max_generation 10 --selfimprove_size 2")
        return True
    else:
        print("⚠️  Some tests failed. Please check the setup.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
