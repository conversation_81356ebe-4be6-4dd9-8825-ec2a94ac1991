{"instance_id": "django__django-12050", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/django/db/models/fields/__init__.py b/django/db/models/fields/__init__.py\nindex 83f98f74f8..d5de4d4e77 100644\n--- a/django/db/models/fields/__init__.py\n+++ b/django/db/models/fields/__init__.py\n@@ -1,2426 +1,38 @@\n-import collections.abc\n-import copy\n-import datetime\n-import decimal\n-import operator\n-import uuid\n-import warnings\n-from base64 import b64decode, b64encode\n-from functools import partialmethod, total_ordering\n+from django.db.models.query_utils import RegisterLookupMixin\n \n-from django import forms\n-from django.apps import apps\n-from django.conf import settings\n-from django.core import checks, exceptions, validators\n-from django.db import connection, connections, router\n-from django.db.models.constants import LOOKUP_SEP\n-from django.db.models.query_utils import DeferredAttribute, RegisterLookupMixin\n-from django.utils import timezone\n-from django.utils.datastructures import DictWrapper\n-from django.utils.dateparse import (\n-    parse_date, parse_datetime, parse_duration, parse_time,\n-)\n-from django.utils.duration import duration_microseconds, duration_string\n-from django.utils.functional import Promise, cached_property\n-from django.utils.ipv6 import clean_ipv6_address\n-from django.utils.itercompat import is_iterable\n-from django.utils.text import capfirst\n-from django.utils.translation import gettext_lazy as _\n \n-__all__ = [\n-    'AutoField', 'BLANK_CHOICE_DASH', 'BigAutoField', 'BigIntegerField',\n-    'BinaryField', 'BooleanField', 'CharField', 'CommaSeparatedIntegerField',\n-    'DateField', 'DateTimeField', 'DecimalField', 'DurationField',\n-    'EmailField', 'Empty', 'Field', 'FilePathField', 'FloatField',\n-    'GenericIPAddressField', 'IPAddressField', 'IntegerField', 'NOT_PROVIDED',\n-    'NullBooleanField', 'PositiveIntegerField', 'PositiveSmallIntegerField',\n-    'SlugField', 'SmallAutoField', 'SmallIntegerField', 'TextField',\n-    'TimeField', 'URLField', 'UUIDField',\n-]\n-\n-\n-class Empty:\n-    pass\n-\n-\n-class NOT_PROVIDED:\n-    pass\n-\n-\n-# The values to use for \"blank\" in SelectFields. Will be appended to the start\n-# of most \"choices\" lists.\n-BLANK_CHOICE_DASH = [(\"\", \"---------\")]\n-\n-\n-def _load_field(app_label, model_name, field_name):\n-    return apps.get_model(app_label, model_name)._meta.get_field(field_name)\n-\n-\n-# A guide to Field parameters:\n-#\n-#   * name:      The name of the field specified in the model.\n-#   * attname:   The attribute to use on the model object. This is the same as\n-#                \"name\", except in the case of ForeignKeys, where \"_id\" is\n-#                appended.\n-#   * db_column: The db_column specified in the model (or None).\n-#   * column:    The database column for this field. This is the same as\n-#                \"attname\", except if db_column is specified.\n-#\n-# Code that introspects values, or does other dynamic things, should use\n-# attname. For example, this gets the primary key value of object \"obj\":\n-#\n-#     getattr(obj, opts.pk.attname)\n-\n-def _empty(of_cls):\n-    new = Empty()\n-    new.__class__ = of_cls\n-    return new\n-\n-\n-def return_None():\n-    return None\n-\n-\n-@total_ordering\n class Field(RegisterLookupMixin):\n     \"\"\"Base class for all field types\"\"\"\n \n-    # Designates whether empty strings fundamentally are allowed at the\n-    # database level.\n-    empty_strings_allowed = True\n-    empty_values = list(validators.EMPTY_VALUES)\n-\n-    # These track each time a Field instance is created. Used to retain order.\n-    # The auto_creation_counter is used for fields that Django implicitly\n-    # creates, creation_counter is used for all user-specified fields.\n-    creation_counter = 0\n-    auto_creation_counter = -1\n-    default_validators = []  # Default set of validators\n-    default_error_messages = {\n-        'invalid_choice': _('Value %(value)r is not a valid choice.'),\n-        'null': _('This field cannot be null.'),\n-        'blank': _('This field cannot be blank.'),\n-        'unique': _('%(model_name)s with this %(field_label)s '\n-                    'already exists.'),\n-        # Translators: The 'lookup_type' is one of 'date', 'year' or 'month'.\n-        # Eg: \"Title must be unique for pub_date year\"\n-        'unique_for_date': _(\"%(field_label)s must be unique for \"\n-                             \"%(date_field_label)s %(lookup_type)s.\"),\n-    }\n-    system_check_deprecated_details = None\n-    system_check_removed_details = None\n-\n-    # Field flags\n-    hidden = False\n-\n-    many_to_many = None\n-    many_to_one = None\n-    one_to_many = None\n-    one_to_one = None\n-    related_model = None\n-\n-    descriptor_class = DeferredAttribute\n-\n-    # Generic field type description, usually overridden by subclasses\n-    def _description(self):\n-        return _('Field of type: %(field_type)s') % {\n-            'field_type': self.__class__.__name__\n-        }\n-    description = property(_description)\n-\n-    def __init__(self, verbose_name=None, name=None, primary_key=False,\n-                 max_length=None, unique=False, blank=False, null=False,\n-                 db_index=False, rel=None, default=NOT_PROVIDED, editable=True,\n-                 serialize=True, unique_for_date=None, unique_for_month=None,\n-                 unique_for_year=None, choices=None, help_text='', db_column=None,\n-                 db_tablespace=None, auto_created=False, validators=(),\n-                 error_messages=None):\n-        self.name = name\n-        self.verbose_name = verbose_name  # May be set by set_attributes_from_name\n-        self._verbose_name = verbose_name  # Store original for deconstruction\n-        self.primary_key = primary_key\n-        self.max_length, self._unique = max_length, unique\n-        self.blank, self.null = blank, null\n-        self.remote_field = rel\n-        self.is_relation = self.remote_field is not None\n-        self.default = default\n-        self.editable = editable\n-        self.serialize = serialize\n-        self.unique_for_date = unique_for_date\n-        self.unique_for_month = unique_for_month\n-        self.unique_for_year = unique_for_year\n-        if isinstance(choices, collections.abc.Iterator):\n-            choices = list(choices)\n-        self.choices = choices\n-        self.help_text = help_text\n-        self.db_index = db_index\n-        self.db_column = db_column\n-        self._db_tablespace = db_tablespace\n-        self.auto_created = auto_created\n-\n-        # Adjust the appropriate creation counter, and save our local copy.\n-        if auto_created:\n-            self.creation_counter = Field.auto_creation_counter\n-            Field.auto_creation_counter -= 1\n-        else:\n-            self.creation_counter = Field.creation_counter\n-            Field.creation_counter += 1\n-\n-        self._validators = list(validators)  # Store for deconstruction later\n-\n-        messages = {}\n-        for c in reversed(self.__class__.__mro__):\n-            messages.update(getattr(c, 'default_error_messages', {}))\n-        messages.update(error_messages or {})\n-        self._error_messages = error_messages  # Store for deconstruction later\n-        self.error_messages = messages\n-\n-    def __str__(self):\n-        \"\"\"\n-        Return \"app_label.model_label.field_name\" for fields attached to\n-        models.\n-        \"\"\"\n-        if not hasattr(self, 'model'):\n-            return super().__str__()\n-        model = self.model\n-        app = model._meta.app_label\n-        return '%s.%s.%s' % (app, model._meta.object_name, self.name)\n-\n-    def __repr__(self):\n-        \"\"\"Display the module, class, and name of the field.\"\"\"\n-        path = '%s.%s' % (self.__class__.__module__, self.__class__.__qualname__)\n-        name = getattr(self, 'name', None)\n-        if name is not None:\n-            return '<%s: %s>' % (path, name)\n-        return '<%s>' % path\n-\n-    def check(self, **kwargs):\n-        return [\n-            *self._check_field_name(),\n-            *self._check_choices(),\n-            *self._check_db_index(),\n-            *self._check_null_allowed_for_primary_keys(),\n-            *self._check_backend_specific_checks(**kwargs),\n-            *self._check_validators(),\n-            *self._check_deprecation_details(),\n-        ]\n-\n-    def _check_field_name(self):\n-        \"\"\"\n-        Check if field name is valid, i.e. 1) does not end with an\n-        underscore, 2) does not contain \"__\" and 3) is not \"pk\".\n-        \"\"\"\n-        if self.name.endswith('_'):\n-            return [\n-                checks.Error(\n-                    'Field names must not end with an underscore.',\n-                    obj=self,\n-                    id='fields.E001',\n-                )\n-            ]\n-        elif LOOKUP_SEP in self.name:\n-            return [\n-                checks.Error(\n-                    'Field names must not contain \"%s\".' % (LOOKUP_SEP,),\n-                    obj=self,\n-                    id='fields.E002',\n-                )\n-            ]\n-        elif self.name == 'pk':\n-            return [\n-                checks.Error(\n-                    \"'pk' is a reserved word that cannot be used as a field name.\",\n-                    obj=self,\n-                    id='fields.E003',\n-                )\n-            ]\n-        else:\n-            return []\n-\n-    @classmethod\n-    def _choices_is_value(cls, value):\n-        return isinstance(value, (str, Promise)) or not is_iterable(value)\n-\n-    def _check_choices(self):\n-        if not self.choices:\n-            return []\n-\n-        if not is_iterable(self.choices) or isinstance(self.choices, str):\n-            return [\n-                checks.Error(\n-                    \"'choices' must be an iterable (e.g., a list or tuple).\",\n-                    obj=self,\n-                    id='fields.E004',\n-                )\n-            ]\n-\n-        choice_max_length = 0\n-        # Expect [group_name, [value, display]]\n-        for choices_group in self.choices:\n-            try:\n-                group_name, group_choices = choices_group\n-            except (TypeError, ValueError):\n-                # Containing non-pairs\n-                break\n-            try:\n-                if not all(\n-                    self._choices_is_value(value) and self._choices_is_value(human_name)\n-                    for value, human_name in group_choices\n-                ):\n-                    break\n-                if self.max_length is not None and group_choices:\n-                    choice_max_length = max(\n-                        choice_max_length,\n-                        *(len(value) for value, _ in group_choices if isinstance(value, str)),\n-                    )\n-            except (TypeError, ValueError):\n-                # No groups, choices in the form [value, display]\n-                value, human_name = group_name, group_choices\n-                if not self._choices_is_value(value) or not self._choices_is_value(human_name):\n-                    break\n-                if self.max_length is not None and isinstance(value, str):\n-                    choice_max_length = max(choice_max_length, len(value))\n-\n-            # Special case: choices=['ab']\n-            if isinstance(choices_group, str):\n-                break\n-        else:\n-            if self.max_length is not None and choice_max_length > self.max_length:\n-                return [\n-                    checks.Error(\n-                        \"'max_length' is too small to fit the longest value \"\n-                        \"in 'choices' (%d characters).\" % choice_max_length,\n-                        obj=self,\n-                        id='fields.E009',\n-                    ),\n-                ]\n-            return []\n-\n-        return [\n-            checks.Error(\n-                \"'choices' must be an iterable containing \"\n-                \"(actual value, human readable name) tuples.\",\n-                obj=self,\n-                id='fields.E005',\n-            )\n-        ]\n-\n-    def _check_db_index(self):\n-        if self.db_index not in (None, True, False):\n-            return [\n-                checks.Error(\n-                    \"'db_index' must be None, True or False.\",\n-                    obj=self,\n-                    id='fields.E006',\n-                )\n-            ]\n-        else:\n-            return []\n-\n-    def _check_null_allowed_for_primary_keys(self):\n-        if (self.primary_key and self.null and\n-                not connection.features.interprets_empty_strings_as_nulls):\n-            # We cannot reliably check this for backends like Oracle which\n-            # consider NULL and '' to be equal (and thus set up\n-            # character-based fields a little differently).\n-            return [\n-                checks.Error(\n-                    'Primary keys must not have null=True.',\n-                    hint=('Set null=False on the field, or '\n-                          'remove primary_key=True argument.'),\n-                    obj=self,\n-                    id='fields.E007',\n-                )\n-            ]\n-        else:\n-            return []\n-\n-    def _check_backend_specific_checks(self, **kwargs):\n-        app_label = self.model._meta.app_label\n-        for db in connections:\n-            if router.allow_migrate(db, app_label, model_name=self.model._meta.model_name):\n-                return connections[db].validation.check_field(self, **kwargs)\n-        return []\n-\n-    def _check_validators(self):\n-        errors = []\n-        for i, validator in enumerate(self.validators):\n-            if not callable(validator):\n-                errors.append(\n-                    checks.Error(\n-                        \"All 'validators' must be callable.\",\n-                        hint=(\n-                            \"validators[{i}] ({repr}) isn't a function or \"\n-                            \"instance of a validator class.\".format(\n-                                i=i, repr=repr(validator),\n-                            )\n-                        ),\n-                        obj=self,\n-                        id='fields.E008',\n-                    )\n-                )\n-        return errors\n-\n-    def _check_deprecation_details(self):\n-        if self.system_check_removed_details is not None:\n-            return [\n-                checks.Error(\n-                    self.system_check_removed_details.get(\n-                        'msg',\n-                        '%s has been removed except for support in historical '\n-                        'migrations.' % self.__class__.__name__\n-                    ),\n-                    hint=self.system_check_removed_details.get('hint'),\n-                    obj=self,\n-                    id=self.system_check_removed_details.get('id', 'fields.EXXX'),\n-                )\n-            ]\n-        elif self.system_check_deprecated_details is not None:\n-            return [\n-                checks.Warning(\n-                    self.system_check_deprecated_details.get(\n-                        'msg',\n-                        '%s has been deprecated.' % self.__class__.__name__\n-                    ),\n-                    hint=self.system_check_deprecated_details.get('hint'),\n-                    obj=self,\n-                    id=self.system_check_deprecated_details.get('id', 'fields.WXXX'),\n-                )\n-            ]\n-        return []\n-\n-    def get_col(self, alias, output_field=None):\n-        if output_field is None:\n-            output_field = self\n-        if alias != self.model._meta.db_table or output_field != self:\n-            from django.db.models.expressions import Col\n-            return Col(alias, self, output_field)\n-        else:\n-            return self.cached_col\n-\n-    @cached_property\n-    def cached_col(self):\n-        from django.db.models.expressions import Col\n-        return Col(self.model._meta.db_table, self)\n-\n-    def select_format(self, compiler, sql, params):\n-        \"\"\"\n-        Custom format for select clauses. For example, GIS columns need to be\n-        selected as AsText(table.col) on MySQL as the table.col data can't be\n-        used by Django.\n-        \"\"\"\n-        return sql, params\n-\n-    def deconstruct(self):\n-        \"\"\"\n-        Return enough information to recreate the field as a 4-tuple:\n-\n-         * The name of the field on the model, if contribute_to_class() has\n-           been run.\n-         * The import path of the field, including the class:e.g.\n-           django.db.models.IntegerField This should be the most portable\n-           version, so less specific may be better.\n-         * A list of positional arguments.\n-         * A dict of keyword arguments.\n-\n-        Note that the positional or keyword arguments must contain values of\n-        the following types (including inner values of collection types):\n-\n-         * None, bool, str, int, float, complex, set, frozenset, list, tuple,\n-           dict\n-         * UUID\n-         * datetime.datetime (naive), datetime.date\n-         * top-level classes, top-level functions - will be referenced by their\n-           full import path\n-         * Storage instances - these have their own deconstruct() method\n-\n-        This is because the values here must be serialized into a text format\n-        (possibly new Python code, possibly JSON) and these are the only types\n-        with encoding handlers defined.\n-\n-        There's no need to return the exact way the field was instantiated this\n-        time, just ensure that the resulting field is the same - prefer keyword\n-        arguments over positional ones, and omit parameters with their default\n-        values.\n-        \"\"\"\n-        # Short-form way of fetching all the default parameters\n-        keywords = {}\n-        possibles = {\n-            \"verbose_name\": None,\n-            \"primary_key\": False,\n-            \"max_length\": None,\n-            \"unique\": False,\n-            \"blank\": False,\n-            \"null\": False,\n-            \"db_index\": False,\n-            \"default\": NOT_PROVIDED,\n-            \"editable\": True,\n-            \"serialize\": True,\n-            \"unique_for_date\": None,\n-            \"unique_for_month\": None,\n-            \"unique_for_year\": None,\n-            \"choices\": None,\n-            \"help_text\": '',\n-            \"db_column\": None,\n-            \"db_tablespace\": None,\n-            \"auto_created\": False,\n-            \"validators\": [],\n-            \"error_messages\": None,\n-        }\n-        attr_overrides = {\n-            \"unique\": \"_unique\",\n-            \"error_messages\": \"_error_messages\",\n-            \"validators\": \"_validators\",\n-            \"verbose_name\": \"_verbose_name\",\n-            \"db_tablespace\": \"_db_tablespace\",\n-        }\n-        equals_comparison = {\"choices\", \"validators\"}\n-        for name, default in possibles.items():\n-            value = getattr(self, attr_overrides.get(name, name))\n-            # Unroll anything iterable for choices into a concrete list\n-            if name == \"choices\" and isinstance(value, collections.abc.Iterable):\n-                value = list(value)\n-            # Do correct kind of comparison\n-            if name in equals_comparison:\n-                if value != default:\n-                    keywords[name] = value\n-            else:\n-                if value is not default:\n-                    keywords[name] = value\n-        # Work out path - we shorten it for known Django core fields\n-        path = \"%s.%s\" % (self.__class__.__module__, self.__class__.__qualname__)\n-        if path.startswith(\"django.db.models.fields.related\"):\n-            path = path.replace(\"django.db.models.fields.related\", \"django.db.models\")\n-        elif path.startswith(\"django.db.models.fields.files\"):\n-            path = path.replace(\"django.db.models.fields.files\", \"django.db.models\")\n-        elif path.startswith(\"django.db.models.fields.proxy\"):\n-            path = path.replace(\"django.db.models.fields.proxy\", \"django.db.models\")\n-        elif path.startswith(\"django.db.models.fields\"):\n-            path = path.replace(\"django.db.models.fields\", \"django.db.models\")\n-        # Return basic info - other fields should override this.\n-        return (self.name, path, [], keywords)\n-\n-    def clone(self):\n-        \"\"\"\n-        Uses deconstruct() to clone a new copy of this Field.\n-        Will not preserve any class attachments/attribute names.\n-        \"\"\"\n-        name, path, args, kwargs = self.deconstruct()\n-        return self.__class__(*args, **kwargs)\n-\n-    def __eq__(self, other):\n-        # Needed for @total_ordering\n-        if isinstance(other, Field):\n-            return self.creation_counter == other.creation_counter\n-        return NotImplemented\n-\n-    def __lt__(self, other):\n-        # This is needed because bisect does not take a comparison function.\n-        if isinstance(other, Field):\n-            return self.creation_counter < other.creation_counter\n-        return NotImplemented\n-\n-    def __hash__(self):\n-        return hash(self.creation_counter)\n-\n-    def __deepcopy__(self, memodict):\n-        # We don't have to deepcopy very much here, since most things are not\n-        # intended to be altered after initial creation.\n-        obj = copy.copy(self)\n-        if self.remote_field:\n-            obj.remote_field = copy.copy(self.remote_field)\n-            if hasattr(self.remote_field, 'field') and self.remote_field.field is self:\n-                obj.remote_field.field = obj\n-        memodict[id(self)] = obj\n-        return obj\n-\n-    def __copy__(self):\n-        # We need to avoid hitting __reduce__, so define this\n-        # slightly weird copy construct.\n-        obj = Empty()\n-        obj.__class__ = self.__class__\n-        obj.__dict__ = self.__dict__.copy()\n-        return obj\n-\n-    def __reduce__(self):\n-        \"\"\"\n-        Pickling should return the model._meta.fields instance of the field,\n-        not a new copy of that field. So, use the app registry to load the\n-        model and then the field back.\n-        \"\"\"\n-        if not hasattr(self, 'model'):\n-            # Fields are sometimes used without attaching them to models (for\n-            # example in aggregation). In this case give back a plain field\n-            # instance. The code below will create a new empty instance of\n-            # class self.__class__, then update its dict with self.__dict__\n-            # values - so, this is very close to normal pickle.\n-            state = self.__dict__.copy()\n-            # The _get_default cached_property can't be pickled due to lambda\n-            # usage.\n-            state.pop('_get_default', None)\n-            return _empty, (self.__class__,), state\n-        return _load_field, (self.model._meta.app_label, self.model._meta.object_name,\n-                             self.name)\n-\n-    def get_pk_value_on_save(self, instance):\n-        \"\"\"\n-        Hook to generate new PK values on save. This method is called when\n-        saving instances with no primary key value set. If this method returns\n-        something else than None, then the returned value is used when saving\n-        the new instance.\n-        \"\"\"\n-        if self.default:\n-            return self.get_default()\n-        return None\n-\n-    def to_python(self, value):\n-        \"\"\"\n-        Convert the input value into the expected Python data type, raising\n-        django.core.exceptions.ValidationError if the data can't be converted.\n-        Return the converted value. Subclasses should override this.\n-        \"\"\"\n-        return value\n-\n-    @cached_property\n-    def validators(self):\n-        \"\"\"\n-        Some validators can't be created at field initialization time.\n-        This method provides a way to delay their creation until required.\n-        \"\"\"\n-        return [*self.default_validators, *self._validators]\n-\n-    def run_validators(self, value):\n-        if value in self.empty_values:\n-            return\n-\n-        errors = []\n-        for v in self.validators:\n-            try:\n-                v(value)\n-            except exceptions.ValidationError as e:\n-                if hasattr(e, 'code') and e.code in self.error_messages:\n-                    e.message = self.error_messages[e.code]\n-                errors.extend(e.error_list)\n-\n-        if errors:\n-            raise exceptions.ValidationError(errors)\n-\n-    def validate(self, value, model_instance):\n-        \"\"\"\n-        Validate value and raise ValidationError if necessary. Subclasses\n-        should override this to provide validation logic.\n-        \"\"\"\n-        if not self.editable:\n-            # Skip validation for non-editable fields.\n-            return\n-\n-        if self.choices is not None and value not in self.empty_values:\n-            for option_key, option_value in self.choices:\n-                if isinstance(option_value, (list, tuple)):\n-                    # This is an optgroup, so look inside the group for\n-                    # options.\n-                    for optgroup_key, optgroup_value in option_value:\n-                        if value == optgroup_key:\n-                            return\n-                elif value == option_key:\n-                    return\n-            raise exceptions.ValidationError(\n-                self.error_messages['invalid_choice'],\n-                code='invalid_choice',\n-                params={'value': value},\n-            )\n-\n-        if value is None and not self.null:\n-            raise exceptions.ValidationError(self.error_messages['null'], code='null')\n-\n-        if not self.blank and value in self.empty_values:\n-            raise exceptions.ValidationError(self.error_messages['blank'], code='blank')\n-\n-    def clean(self, value, model_instance):\n-        \"\"\"\n-        Convert the value's type and run validation. Validation errors\n-        from to_python() and validate() are propagated. Return the correct\n-        value if no error is raised.\n-        \"\"\"\n-        value = self.to_python(value)\n-        self.validate(value, model_instance)\n-        self.run_validators(value)\n-        return value\n-\n-    def db_type_parameters(self, connection):\n-        return DictWrapper(self.__dict__, connection.ops.quote_name, 'qn_')\n-\n-    def db_check(self, connection):\n-        \"\"\"\n-        Return the database column check constraint for this field, for the\n-        provided connection. Works the same way as db_type() for the case that\n-        get_internal_type() does not map to a preexisting model field.\n-        \"\"\"\n-        data = self.db_type_parameters(connection)\n-        try:\n-            return connection.data_type_check_constraints[self.get_internal_type()] % data\n-        except KeyError:\n-            return None\n-\n-    def db_type(self, connection):\n-        \"\"\"\n-        Return the database column data type for this field, for the provided\n-        connection.\n-        \"\"\"\n-        # The default implementation of this method looks at the\n-        # backend-specific data_types dictionary, looking up the field by its\n-        # \"internal type\".\n-        #\n-        # A Field class can implement the get_internal_type() method to specify\n-        # which *preexisting* Django Field class it's most similar to -- i.e.,\n-        # a custom field might be represented by a TEXT column type, which is\n-        # the same as the TextField Django field type, which means the custom\n-        # field's get_internal_type() returns 'TextField'.\n-        #\n-        # But the limitation of the get_internal_type() / data_types approach\n-        # is that it cannot handle database column types that aren't already\n-        # mapped to one of the built-in Django field types. In this case, you\n-        # can implement db_type() instead of get_internal_type() to specify\n-        # exactly which wacky database column type you want to use.\n-        data = self.db_type_parameters(connection)\n-        try:\n-            return connection.data_types[self.get_internal_type()] % data\n-        except KeyError:\n-            return None\n-\n-    def rel_db_type(self, connection):\n-        \"\"\"\n-        Return the data type that a related field pointing to this field should\n-        use. For example, this method is called by ForeignKey and OneToOneField\n-        to determine its data type.\n-        \"\"\"\n-        return self.db_type(connection)\n-\n-    def cast_db_type(self, connection):\n-        \"\"\"Return the data type to use in the Cast() function.\"\"\"\n-        db_type = connection.ops.cast_data_types.get(self.get_internal_type())\n-        if db_type:\n-            return db_type % self.db_type_parameters(connection)\n-        return self.db_type(connection)\n-\n-    def db_parameters(self, connection):\n-        \"\"\"\n-        Extension of db_type(), providing a range of different return values\n-        (type, checks). This will look at db_type(), allowing custom model\n-        fields to override it.\n-        \"\"\"\n-        type_string = self.db_type(connection)\n-        check_string = self.db_check(connection)\n-        return {\n-            \"type\": type_string,\n-            \"check\": check_string,\n-        }\n-\n-    def db_type_suffix(self, connection):\n-        return connection.data_types_suffix.get(self.get_internal_type())\n-\n-    def get_db_converters(self, connection):\n-        if hasattr(self, 'from_db_value'):\n-            return [self.from_db_value]\n-        return []\n-\n-    @property\n-    def unique(self):\n-        return self._unique or self.primary_key\n-\n-    @property\n-    def db_tablespace(self):\n-        return self._db_tablespace or settings.DEFAULT_INDEX_TABLESPACE\n-\n-    @property\n-    def db_returning(self):\n-        \"\"\"\n-        Private API intended only to be used by Django itself. Currently only\n-        the PostgreSQL backend supports returning multiple fields on a model.\n-        \"\"\"\n-        return False\n-\n-    def set_attributes_from_name(self, name):\n-        self.name = self.name or name\n-        self.attname, self.column = self.get_attname_column()\n-        self.concrete = self.column is not None\n-        if self.verbose_name is None and self.name:\n-            self.verbose_name = self.name.replace('_', ' ')\n-\n-    def contribute_to_class(self, cls, name, private_only=False):\n-        \"\"\"\n-        Register the field with the model class it belongs to.\n-\n-        If private_only is True, create a separate instance of this field\n-        for every subclass of cls, even if cls is not an abstract model.\n-        \"\"\"\n-        self.set_attributes_from_name(name)\n-        self.model = cls\n-        cls._meta.add_field(self, private=private_only)\n-        if self.column:\n-            # Don't override classmethods with the descriptor. This means that\n-            # if you have a classmethod and a field with the same name, then\n-            # such fields can't be deferred (we don't have a check for this).\n-            if not getattr(cls, self.attname, None):\n-                setattr(cls, self.attname, self.descriptor_class(self))\n-        if self.choices is not None:\n-            if not hasattr(cls, 'get_%s_display' % self.name):\n-                setattr(\n-                    cls,\n-                    'get_%s_display' % self.name,\n-                    partialmethod(cls._get_FIELD_display, field=self),\n-                )\n-\n-    def get_filter_kwargs_for_object(self, obj):\n-        \"\"\"\n-        Return a dict that when passed as kwargs to self.model.filter(), would\n-        yield all instances having the same value for this field as obj has.\n-        \"\"\"\n-        return {self.name: getattr(obj, self.attname)}\n-\n-    def get_attname(self):\n-        return self.name\n-\n-    def get_attname_column(self):\n-        attname = self.get_attname()\n-        column = self.db_column or attname\n-        return attname, column\n+    def __init__(self):\n+        pass\n \n     def get_internal_type(self):\n         return self.__class__.__name__\n \n-    def pre_save(self, model_instance, add):\n-        \"\"\"Return field's value just before saving.\"\"\"\n-        return getattr(model_instance, self.attname)\n-\n     def get_prep_value(self, value):\n-        \"\"\"Perform preliminary non-db specific value checks and conversions.\"\"\"\n-        if isinstance(value, Promise):\n-            value = value._proxy____cast()\n-        return value\n-\n-    def get_db_prep_value(self, value, connection, prepared=False):\n-        \"\"\"\n-        Return field's value prepared for interacting with the database backend.\n-\n-        Used by the default implementations of get_db_prep_save().\n-        \"\"\"\n-        if not prepared:\n-            value = self.get_prep_value(value)\n         return value\n \n-    def get_db_prep_save(self, value, connection):\n-        \"\"\"Return field's value prepared for saving into a database.\"\"\"\n-        return self.get_db_prep_value(value, connection=connection, prepared=False)\n-\n-    def has_default(self):\n-        \"\"\"Return a boolean of whether this field has a default value.\"\"\"\n-        return self.default is not NOT_PROVIDED\n-\n-    def get_default(self):\n-        \"\"\"Return the default value for this field.\"\"\"\n-        return self._get_default()\n-\n-    @cached_property\n-    def _get_default(self):\n-        if self.has_default():\n-            if callable(self.default):\n-                return self.default\n-            return lambda: self.default\n-\n-        if not self.empty_strings_allowed or self.null and not connection.features.interprets_empty_strings_as_nulls:\n-            return return_None\n-        return str  # return empty string\n-\n-    def get_choices(self, include_blank=True, blank_choice=BLANK_CHOICE_DASH, limit_choices_to=None, ordering=()):\n-        \"\"\"\n-        Return choices with a default blank choices included, for use\n-        as <select> choices for this field.\n-        \"\"\"\n-        if self.choices is not None:\n-            choices = list(self.choices)\n-            if include_blank:\n-                blank_defined = any(choice in ('', None) for choice, _ in self.flatchoices)\n-                if not blank_defined:\n-                    choices = blank_choice + choices\n-            return choices\n-        rel_model = self.remote_field.model\n-        limit_choices_to = limit_choices_to or self.get_limit_choices_to()\n-        choice_func = operator.attrgetter(\n-            self.remote_field.get_related_field().attname\n-            if hasattr(self.remote_field, 'get_related_field')\n-            else 'pk'\n-        )\n-        qs = rel_model._default_manager.complex_filter(limit_choices_to)\n-        if ordering:\n-            qs = qs.order_by(*ordering)\n-        return (blank_choice if include_blank else []) + [\n-            (choice_func(x), str(x)) for x in qs\n-        ]\n-\n-    def value_to_string(self, obj):\n-        \"\"\"\n-        Return a string value of this field from the passed obj.\n-        This is used by the serialization framework.\n-        \"\"\"\n-        return str(self.value_from_object(obj))\n-\n-    def _get_flatchoices(self):\n-        \"\"\"Flattened version of choices tuple.\"\"\"\n-        if self.choices is None:\n-            return []\n-        flat = []\n-        for choice, value in self.choices:\n-            if isinstance(value, (list, tuple)):\n-                flat.extend(value)\n-            else:\n-                flat.append((choice, value))\n-        return flat\n-    flatchoices = property(_get_flatchoices)\n-\n-    def save_form_data(self, instance, data):\n-        setattr(instance, self.name, data)\n-\n-    def formfield(self, form_class=None, choices_form_class=None, **kwargs):\n-        \"\"\"Return a django.forms.Field instance for this field.\"\"\"\n-        defaults = {\n-            'required': not self.blank,\n-            'label': capfirst(self.verbose_name),\n-            'help_text': self.help_text,\n-        }\n-        if self.has_default():\n-            if callable(self.default):\n-                defaults['initial'] = self.default\n-                defaults['show_hidden_initial'] = True\n-            else:\n-                defaults['initial'] = self.get_default()\n-        if self.choices is not None:\n-            # Fields with choices get special treatment.\n-            include_blank = (self.blank or\n-                             not (self.has_default() or 'initial' in kwargs))\n-            defaults['choices'] = self.get_choices(include_blank=include_blank)\n-            defaults['coerce'] = self.to_python\n-            if self.null:\n-                defaults['empty_value'] = None\n-            if choices_form_class is not None:\n-                form_class = choices_form_class\n-            else:\n-                form_class = forms.TypedChoiceField\n-            # Many of the subclass-specific formfield arguments (min_value,\n-            # max_value) don't apply for choice fields, so be sure to only pass\n-            # the values that TypedChoiceField will understand.\n-            for k in list(kwargs):\n-                if k not in ('coerce', 'empty_value', 'choices', 'required',\n-                             'widget', 'label', 'initial', 'help_text',\n-                             'error_messages', 'show_hidden_initial', 'disabled'):\n-                    del kwargs[k]\n-        defaults.update(kwargs)\n-        if form_class is None:\n-            form_class = forms.CharField\n-        return form_class(**defaults)\n-\n-    def value_from_object(self, obj):\n-        \"\"\"Return the value of this field in the given model instance.\"\"\"\n-        return getattr(obj, self.attname)\n-\n-\n-class BooleanField(Field):\n-    empty_strings_allowed = False\n-    default_error_messages = {\n-        'invalid': _('“%(value)s” value must be either True or False.'),\n-        'invalid_nullable': _('“%(value)s” value must be either True, False, or None.'),\n-    }\n-    description = _(\"Boolean (Either True or False)\")\n-\n-    def get_internal_type(self):\n-        return \"BooleanField\"\n-\n-    def to_python(self, value):\n-        if self.null and value in self.empty_values:\n-            return None\n-        if value in (True, False):\n-            # 1/0 are equal to True/False. bool() converts former to latter.\n-            return bool(value)\n-        if value in ('t', 'True', '1'):\n-            return True\n-        if value in ('f', 'False', '0'):\n-            return False\n-        raise exceptions.ValidationError(\n-            self.error_messages['invalid_nullable' if self.null else 'invalid'],\n-            code='invalid',\n-            params={'value': value},\n-        )\n-\n-    def get_prep_value(self, value):\n-        value = super().get_prep_value(value)\n-        if value is None:\n-            return None\n-        return self.to_python(value)\n-\n-    def formfield(self, **kwargs):\n-        if self.choices is not None:\n-            include_blank = not (self.has_default() or 'initial' in kwargs)\n-            defaults = {'choices': self.get_choices(include_blank=include_blank)}\n-        else:\n-            form_class = forms.NullBooleanField if self.null else forms.BooleanField\n-            # In HTML checkboxes, 'required' means \"must be checked\" which is\n-            # different from the choices case (\"must select some value\").\n-            # required=False allows unchecked checkboxes.\n-            defaults = {'form_class': form_class, 'required': False}\n-        return super().formfield(**{**defaults, **kwargs})\n-\n-\n-class CharField(Field):\n-    description = _(\"String (up to %(max_length)s)\")\n-\n-    def __init__(self, *args, **kwargs):\n-        super().__init__(*args, **kwargs)\n-        self.validators.append(validators.MaxLengthValidator(self.max_length))\n-\n-    def check(self, **kwargs):\n-        return [\n-            *super().check(**kwargs),\n-            *self._check_max_length_attribute(**kwargs),\n-        ]\n-\n-    def _check_max_length_attribute(self, **kwargs):\n-        if self.max_length is None:\n-            return [\n-                checks.Error(\n-                    \"CharFields must define a 'max_length' attribute.\",\n-                    obj=self,\n-                    id='fields.E120',\n-                )\n-            ]\n-        elif (not isinstance(self.max_length, int) or isinstance(self.max_length, bool) or\n-                self.max_length <= 0):\n-            return [\n-                checks.Error(\n-                    \"'max_length' must be a positive integer.\",\n-                    obj=self,\n-                    id='fields.E121',\n-                )\n-            ]\n-        else:\n-            return []\n-\n-    def cast_db_type(self, connection):\n-        if self.max_length is None:\n-            return connection.ops.cast_char_field_without_max_length\n-        return super().cast_db_type(connection)\n-\n-    def get_internal_type(self):\n-        return \"CharField\"\n-\n-    def to_python(self, value):\n-        if isinstance(value, str) or value is None:\n-            return value\n-        return str(value)\n-\n-    def get_prep_value(self, value):\n-        value = super().get_prep_value(value)\n-        return self.to_python(value)\n-\n-    def formfield(self, **kwargs):\n-        # Passing max_length to forms.CharField means that the value's length\n-        # will be validated twice. This is considered acceptable since we want\n-        # the value in the form field (to pass into widget for example).\n-        defaults = {'max_length': self.max_length}\n-        # TODO: Handle multiple backends with different feature flags.\n-        if self.null and not connection.features.interprets_empty_strings_as_nulls:\n-            defaults['empty_value'] = None\n-        defaults.update(kwargs)\n-        return super().formfield(**defaults)\n-\n-\n-class CommaSeparatedIntegerField(CharField):\n-    default_validators = [validators.validate_comma_separated_integer_list]\n-    description = _(\"Comma-separated integers\")\n-    system_check_removed_details = {\n-        'msg': (\n-            'CommaSeparatedIntegerField is removed except for support in '\n-            'historical migrations.'\n-        ),\n-        'hint': (\n-            'Use CharField(validators=[validate_comma_separated_integer_list]) '\n-            'instead.'\n-        ),\n-        'id': 'fields.E901',\n-    }\n-\n-\n-class DateTimeCheckMixin:\n-\n-    def check(self, **kwargs):\n-        return [\n-            *super().check(**kwargs),\n-            *self._check_mutually_exclusive_options(),\n-            *self._check_fix_default_value(),\n-        ]\n-\n-    def _check_mutually_exclusive_options(self):\n-        # auto_now, auto_now_add, and default are mutually exclusive\n-        # options. The use of more than one of these options together\n-        # will trigger an Error\n-        mutually_exclusive_options = [self.auto_now_add, self.auto_now, self.has_default()]\n-        enabled_options = [option not in (None, False) for option in mutually_exclusive_options].count(True)\n-        if enabled_options > 1:\n-            return [\n-                checks.Error(\n-                    \"The options auto_now, auto_now_add, and default \"\n-                    \"are mutually exclusive. Only one of these options \"\n-                    \"may be present.\",\n-                    obj=self,\n-                    id='fields.E160',\n-                )\n-            ]\n-        else:\n-            return []\n-\n-    def _check_fix_default_value(self):\n-        return []\n-\n-\n-class DateField(DateTimeCheckMixin, Field):\n-    empty_strings_allowed = False\n-    default_error_messages = {\n-        'invalid': _('“%(value)s” value has an invalid date format. It must be '\n-                     'in YYYY-MM-DD format.'),\n-        'invalid_date': _('“%(value)s” value has the correct format (YYYY-MM-DD) '\n-                          'but it is an invalid date.'),\n-    }\n-    description = _(\"Date (without time)\")\n-\n-    def __init__(self, verbose_name=None, name=None, auto_now=False,\n-                 auto_now_add=False, **kwargs):\n-        self.auto_now, self.auto_now_add = auto_now, auto_now_add\n-        if auto_now or auto_now_add:\n-            kwargs['editable'] = False\n-            kwargs['blank'] = True\n-        super().__init__(verbose_name, name, **kwargs)\n-\n-    def _check_fix_default_value(self):\n-        \"\"\"\n-        Warn that using an actual date or datetime value is probably wrong;\n-        it's only evaluated on server startup.\n-        \"\"\"\n-        if not self.has_default():\n-            return []\n-\n-        now = timezone.now()\n-        if not timezone.is_naive(now):\n-            now = timezone.make_naive(now, timezone.utc)\n-        value = self.default\n-        if isinstance(value, datetime.datetime):\n-            if not timezone.is_naive(value):\n-                value = timezone.make_naive(value, timezone.utc)\n-            value = value.date()\n-        elif isinstance(value, datetime.date):\n-            # Nothing to do, as dates don't have tz information\n-            pass\n-        else:\n-            # No explicit date / datetime value -- no checks necessary\n-            return []\n-        offset = datetime.timedelta(days=1)\n-        lower = (now - offset).date()\n-        upper = (now + offset).date()\n-        if lower <= value <= upper:\n-            return [\n-                checks.Warning(\n-                    'Fixed default value provided.',\n-                    hint='It seems you set a fixed date / time / datetime '\n-                         'value as default for this field. This may not be '\n-                         'what you want. If you want to have the current date '\n-                         'as default, use `django.utils.timezone.now`',\n-                    obj=self,\n-                    id='fields.W161',\n-                )\n-            ]\n-\n-        return []\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        if self.auto_now:\n-            kwargs['auto_now'] = True\n-        if self.auto_now_add:\n-            kwargs['auto_now_add'] = True\n-        if self.auto_now or self.auto_now_add:\n-            del kwargs['editable']\n-            del kwargs['blank']\n-        return name, path, args, kwargs\n-\n-    def get_internal_type(self):\n-        return \"DateField\"\n-\n-    def to_python(self, value):\n-        if value is None:\n-            return value\n-        if isinstance(value, datetime.datetime):\n-            if settings.USE_TZ and timezone.is_aware(value):\n-                # Convert aware datetimes to the default time zone\n-                # before casting them to dates (#17742).\n-                default_timezone = timezone.get_default_timezone()\n-                value = timezone.make_naive(value, default_timezone)\n-            return value.date()\n-        if isinstance(value, datetime.date):\n-            return value\n-\n-        try:\n-            parsed = parse_date(value)\n-            if parsed is not None:\n-                return parsed\n-        except ValueError:\n-            raise exceptions.ValidationError(\n-                self.error_messages['invalid_date'],\n-                code='invalid_date',\n-                params={'value': value},\n-            )\n-\n-        raise exceptions.ValidationError(\n-            self.error_messages['invalid'],\n-            code='invalid',\n-            params={'value': value},\n-        )\n-\n-    def pre_save(self, model_instance, add):\n-        if self.auto_now or (self.auto_now_add and add):\n-            value = datetime.date.today()\n-            setattr(model_instance, self.attname, value)\n-            return value\n-        else:\n-            return super().pre_save(model_instance, add)\n-\n-    def contribute_to_class(self, cls, name, **kwargs):\n-        super().contribute_to_class(cls, name, **kwargs)\n-        if not self.null:\n-            setattr(\n-                cls, 'get_next_by_%s' % self.name,\n-                partialmethod(cls._get_next_or_previous_by_FIELD, field=self, is_next=True)\n-            )\n-            setattr(\n-                cls, 'get_previous_by_%s' % self.name,\n-                partialmethod(cls._get_next_or_previous_by_FIELD, field=self, is_next=False)\n-            )\n-\n-    def get_prep_value(self, value):\n-        value = super().get_prep_value(value)\n-        return self.to_python(value)\n-\n-    def get_db_prep_value(self, value, connection, prepared=False):\n-        # Casts dates into the format expected by the backend\n-        if not prepared:\n-            value = self.get_prep_value(value)\n-        return connection.ops.adapt_datefield_value(value)\n-\n-    def value_to_string(self, obj):\n-        val = self.value_from_object(obj)\n-        return '' if val is None else val.isoformat()\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'form_class': forms.DateField,\n-            **kwargs,\n-        })\n-\n-\n-class DateTimeField(DateField):\n-    empty_strings_allowed = False\n-    default_error_messages = {\n-        'invalid': _('“%(value)s” value has an invalid format. It must be in '\n-                     'YYYY-MM-DD HH:MM[:ss[.uuuuuu]][TZ] format.'),\n-        'invalid_date': _(\"“%(value)s” value has the correct format \"\n-                          \"(YYYY-MM-DD) but it is an invalid date.\"),\n-        'invalid_datetime': _('“%(value)s” value has the correct format '\n-                              '(YYYY-MM-DD HH:MM[:ss[.uuuuuu]][TZ]) '\n-                              'but it is an invalid date/time.'),\n-    }\n-    description = _(\"Date (with time)\")\n-\n-    # __init__ is inherited from DateField\n-\n-    def _check_fix_default_value(self):\n-        \"\"\"\n-        Warn that using an actual date or datetime value is probably wrong;\n-        it's only evaluated on server startup.\n-        \"\"\"\n-        if not self.has_default():\n-            return []\n-\n-        now = timezone.now()\n-        if not timezone.is_naive(now):\n-            now = timezone.make_naive(now, timezone.utc)\n-        value = self.default\n-        if isinstance(value, datetime.datetime):\n-            second_offset = datetime.timedelta(seconds=10)\n-            lower = now - second_offset\n-            upper = now + second_offset\n-            if timezone.is_aware(value):\n-                value = timezone.make_naive(value, timezone.utc)\n-        elif isinstance(value, datetime.date):\n-            second_offset = datetime.timedelta(seconds=10)\n-            lower = now - second_offset\n-            lower = datetime.datetime(lower.year, lower.month, lower.day)\n-            upper = now + second_offset\n-            upper = datetime.datetime(upper.year, upper.month, upper.day)\n-            value = datetime.datetime(value.year, value.month, value.day)\n-        else:\n-            # No explicit date / datetime value -- no checks necessary\n-            return []\n-        if lower <= value <= upper:\n-            return [\n-                checks.Warning(\n-                    'Fixed default value provided.',\n-                    hint='It seems you set a fixed date / time / datetime '\n-                         'value as default for this field. This may not be '\n-                         'what you want. If you want to have the current date '\n-                         'as default, use `django.utils.timezone.now`',\n-                    obj=self,\n-                    id='fields.W161',\n-                )\n-            ]\n-\n-        return []\n-\n-    def get_internal_type(self):\n-        return \"DateTimeField\"\n-\n-    def to_python(self, value):\n-        if value is None:\n-            return value\n-        if isinstance(value, datetime.datetime):\n-            return value\n-        if isinstance(value, datetime.date):\n-            value = datetime.datetime(value.year, value.month, value.day)\n-            if settings.USE_TZ:\n-                # For backwards compatibility, interpret naive datetimes in\n-                # local time. This won't work during DST change, but we can't\n-                # do much about it, so we let the exceptions percolate up the\n-                # call stack.\n-                warnings.warn(\"DateTimeField %s.%s received a naive datetime \"\n-                              \"(%s) while time zone support is active.\" %\n-                              (self.model.__name__, self.name, value),\n-                              RuntimeWarning)\n-                default_timezone = timezone.get_default_timezone()\n-                value = timezone.make_aware(value, default_timezone)\n-            return value\n-\n-        try:\n-            parsed = parse_datetime(value)\n-            if parsed is not None:\n-                return parsed\n-        except ValueError:\n-            raise exceptions.ValidationError(\n-                self.error_messages['invalid_datetime'],\n-                code='invalid_datetime',\n-                params={'value': value},\n-            )\n-\n-        try:\n-            parsed = parse_date(value)\n-            if parsed is not None:\n-                return datetime.datetime(parsed.year, parsed.month, parsed.day)\n-        except ValueError:\n-            raise exceptions.ValidationError(\n-                self.error_messages['invalid_date'],\n-                code='invalid_date',\n-                params={'value': value},\n-            )\n-\n-        raise exceptions.ValidationError(\n-            self.error_messages['invalid'],\n-            code='invalid',\n-            params={'value': value},\n-        )\n-\n-    def pre_save(self, model_instance, add):\n-        if self.auto_now or (self.auto_now_add and add):\n-            value = timezone.now()\n-            setattr(model_instance, self.attname, value)\n-            return value\n-        else:\n-            return super().pre_save(model_instance, add)\n-\n-    # contribute_to_class is inherited from DateField, it registers\n-    # get_next_by_FOO and get_prev_by_FOO\n-\n-    def get_prep_value(self, value):\n-        value = super().get_prep_value(value)\n-        value = self.to_python(value)\n-        if value is not None and settings.USE_TZ and timezone.is_naive(value):\n-            # For backwards compatibility, interpret naive datetimes in local\n-            # time. This won't work during DST change, but we can't do much\n-            # about it, so we let the exceptions percolate up the call stack.\n-            try:\n-                name = '%s.%s' % (self.model.__name__, self.name)\n-            except AttributeError:\n-                name = '(unbound)'\n-            warnings.warn(\"DateTimeField %s received a naive datetime (%s)\"\n-                          \" while time zone support is active.\" %\n-                          (name, value),\n-                          RuntimeWarning)\n-            default_timezone = timezone.get_default_timezone()\n-            value = timezone.make_aware(value, default_timezone)\n-        return value\n-\n-    def get_db_prep_value(self, value, connection, prepared=False):\n-        # Casts datetimes into the format expected by the backend\n-        if not prepared:\n-            value = self.get_prep_value(value)\n-        return connection.ops.adapt_datetimefield_value(value)\n-\n-    def value_to_string(self, obj):\n-        val = self.value_from_object(obj)\n-        return '' if val is None else val.isoformat()\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'form_class': forms.DateTimeField,\n-            **kwargs,\n-        })\n-\n-\n-class DecimalField(Field):\n-    empty_strings_allowed = False\n-    default_error_messages = {\n-        'invalid': _('“%(value)s” value must be a decimal number.'),\n-    }\n-    description = _(\"Decimal number\")\n-\n-    def __init__(self, verbose_name=None, name=None, max_digits=None,\n-                 decimal_places=None, **kwargs):\n-        self.max_digits, self.decimal_places = max_digits, decimal_places\n-        super().__init__(verbose_name, name, **kwargs)\n-\n-    def check(self, **kwargs):\n-        errors = super().check(**kwargs)\n-\n-        digits_errors = [\n-            *self._check_decimal_places(),\n-            *self._check_max_digits(),\n-        ]\n-        if not digits_errors:\n-            errors.extend(self._check_decimal_places_and_max_digits(**kwargs))\n-        else:\n-            errors.extend(digits_errors)\n-        return errors\n-\n-    def _check_decimal_places(self):\n-        try:\n-            decimal_places = int(self.decimal_places)\n-            if decimal_places < 0:\n-                raise ValueError()\n-        except TypeError:\n-            return [\n-                checks.Error(\n-                    \"DecimalFields must define a 'decimal_places' attribute.\",\n-                    obj=self,\n-                    id='fields.E130',\n-                )\n-            ]\n-        except ValueError:\n-            return [\n-                checks.Error(\n-                    \"'decimal_places' must be a non-negative integer.\",\n-                    obj=self,\n-                    id='fields.E131',\n-                )\n-            ]\n-        else:\n-            return []\n-\n-    def _check_max_digits(self):\n-        try:\n-            max_digits = int(self.max_digits)\n-            if max_digits <= 0:\n-                raise ValueError()\n-        except TypeError:\n-            return [\n-                checks.Error(\n-                    \"DecimalFields must define a 'max_digits' attribute.\",\n-                    obj=self,\n-                    id='fields.E132',\n-                )\n-            ]\n-        except ValueError:\n-            return [\n-                checks.Error(\n-                    \"'max_digits' must be a positive integer.\",\n-                    obj=self,\n-                    id='fields.E133',\n-                )\n-            ]\n-        else:\n-            return []\n-\n-    def _check_decimal_places_and_max_digits(self, **kwargs):\n-        if int(self.decimal_places) > int(self.max_digits):\n-            return [\n-                checks.Error(\n-                    \"'max_digits' must be greater or equal to 'decimal_places'.\",\n-                    obj=self,\n-                    id='fields.E134',\n-                )\n-            ]\n-        return []\n-\n-    @cached_property\n-    def validators(self):\n-        return super().validators + [\n-            validators.DecimalValidator(self.max_digits, self.decimal_places)\n-        ]\n-\n-    @cached_property\n-    def context(self):\n-        return decimal.Context(prec=self.max_digits)\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        if self.max_digits is not None:\n-            kwargs['max_digits'] = self.max_digits\n-        if self.decimal_places is not None:\n-            kwargs['decimal_places'] = self.decimal_places\n-        return name, path, args, kwargs\n-\n-    def get_internal_type(self):\n-        return \"DecimalField\"\n-\n-    def to_python(self, value):\n-        if value is None:\n-            return value\n-        if isinstance(value, float):\n-            return self.context.create_decimal_from_float(value)\n-        try:\n-            return decimal.Decimal(value)\n-        except decimal.InvalidOperation:\n-            raise exceptions.ValidationError(\n-                self.error_messages['invalid'],\n-                code='invalid',\n-                params={'value': value},\n-            )\n-\n-    def get_db_prep_save(self, value, connection):\n-        return connection.ops.adapt_decimalfield_value(self.to_python(value), self.max_digits, self.decimal_places)\n-\n-    def get_prep_value(self, value):\n-        value = super().get_prep_value(value)\n-        return self.to_python(value)\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'max_digits': self.max_digits,\n-            'decimal_places': self.decimal_places,\n-            'form_class': forms.DecimalField,\n-            **kwargs,\n-        })\n-\n-\n-class DurationField(Field):\n-    \"\"\"\n-    Store timedelta objects.\n-\n-    Use interval on PostgreSQL, INTERVAL DAY TO SECOND on Oracle, and bigint\n-    of microseconds on other databases.\n-    \"\"\"\n-    empty_strings_allowed = False\n-    default_error_messages = {\n-        'invalid': _('“%(value)s” value has an invalid format. It must be in '\n-                     '[DD] [[HH:]MM:]ss[.uuuuuu] format.')\n-    }\n-    description = _(\"Duration\")\n-\n-    def get_internal_type(self):\n-        return \"DurationField\"\n-\n-    def to_python(self, value):\n-        if value is None:\n-            return value\n-        if isinstance(value, datetime.timedelta):\n-            return value\n-        try:\n-            parsed = parse_duration(value)\n-        except ValueError:\n-            pass\n-        else:\n-            if parsed is not None:\n-                return parsed\n-\n-        raise exceptions.ValidationError(\n-            self.error_messages['invalid'],\n-            code='invalid',\n-            params={'value': value},\n-        )\n-\n-    def get_db_prep_value(self, value, connection, prepared=False):\n-        if connection.features.has_native_duration_field:\n-            return value\n-        if value is None:\n-            return None\n-        return duration_microseconds(value)\n-\n-    def get_db_converters(self, connection):\n-        converters = []\n-        if not connection.features.has_native_duration_field:\n-            converters.append(connection.ops.convert_durationfield_value)\n-        return converters + super().get_db_converters(connection)\n-\n-    def value_to_string(self, obj):\n-        val = self.value_from_object(obj)\n-        return '' if val is None else duration_string(val)\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'form_class': forms.DurationField,\n-            **kwargs,\n-        })\n-\n-\n-class EmailField(CharField):\n-    default_validators = [validators.validate_email]\n-    description = _(\"Email address\")\n-\n-    def __init__(self, *args, **kwargs):\n-        # max_length=254 to be compliant with RFCs 3696 and 5321\n-        kwargs.setdefault('max_length', 254)\n-        super().__init__(*args, **kwargs)\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        # We do not exclude max_length if it matches default as we want to change\n-        # the default in future.\n-        return name, path, args, kwargs\n-\n-    def formfield(self, **kwargs):\n-        # As with CharField, this will cause email validation to be performed\n-        # twice.\n-        return super().formfield(**{\n-            'form_class': forms.EmailField,\n-            **kwargs,\n-        })\n-\n-\n-class FilePathField(Field):\n-    description = _(\"File path\")\n-\n-    def __init__(self, verbose_name=None, name=None, path='', match=None,\n-                 recursive=False, allow_files=True, allow_folders=False, **kwargs):\n-        self.path, self.match, self.recursive = path, match, recursive\n-        self.allow_files, self.allow_folders = allow_files, allow_folders\n-        kwargs.setdefault('max_length', 100)\n-        super().__init__(verbose_name, name, **kwargs)\n-\n-    def check(self, **kwargs):\n-        return [\n-            *super().check(**kwargs),\n-            *self._check_allowing_files_or_folders(**kwargs),\n-        ]\n-\n-    def _check_allowing_files_or_folders(self, **kwargs):\n-        if not self.allow_files and not self.allow_folders:\n-            return [\n-                checks.Error(\n-                    \"FilePathFields must have either 'allow_files' or 'allow_folders' set to True.\",\n-                    obj=self,\n-                    id='fields.E140',\n-                )\n-            ]\n-        return []\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        if self.path != '':\n-            kwargs['path'] = self.path\n-        if self.match is not None:\n-            kwargs['match'] = self.match\n-        if self.recursive is not False:\n-            kwargs['recursive'] = self.recursive\n-        if self.allow_files is not True:\n-            kwargs['allow_files'] = self.allow_files\n-        if self.allow_folders is not False:\n-            kwargs['allow_folders'] = self.allow_folders\n-        if kwargs.get(\"max_length\") == 100:\n-            del kwargs[\"max_length\"]\n-        return name, path, args, kwargs\n-\n-    def get_prep_value(self, value):\n-        value = super().get_prep_value(value)\n-        if value is None:\n-            return None\n-        return str(value)\n+    def db_type(self, connection):\n+        return 'text'\n \n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'path': self.path() if callable(self.path) else self.path,\n-            'match': self.match,\n-            'recursive': self.recursive,\n-            'form_class': forms.FilePathField,\n-            'allow_files': self.allow_files,\n-            'allow_folders': self.allow_folders,\n-            **kwargs,\n-        })\n \n+class PickledField(Field):\n     def get_internal_type(self):\n-        return \"FilePathField\"\n-\n-\n-class FloatField(Field):\n-    empty_strings_allowed = False\n-    default_error_messages = {\n-        'invalid': _('“%(value)s” value must be a float.'),\n-    }\n-    description = _(\"Floating point number\")\n+        return 'PickledField'\n \n     def get_prep_value(self, value):\n-        value = super().get_prep_value(value)\n+        import pickle\n         if value is None:\n             return None\n-        try:\n-            return float(value)\n-        except (TypeError, ValueError) as e:\n-            raise e.__class__(\n-                \"Field '%s' expected a number but got %r.\" % (self.name, value),\n-            ) from e\n-\n-    def get_internal_type(self):\n-        return \"FloatField\"\n+        return pickle.dumps(value)\n \n-    def to_python(self, value):\n+    def from_db_value(self, value, expression, connection):\n+        import pickle\n         if value is None:\n             return value\n-        try:\n-            return float(value)\n-        except (TypeError, ValueError):\n-            raise exceptions.ValidationError(\n-                self.error_messages['invalid'],\n-                code='invalid',\n-                params={'value': value},\n-            )\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'form_class': forms.FloatField,\n-            **kwargs,\n-        })\n+        return pickle.loads(value)\n \n \n class IntegerField(Field):\n-    empty_strings_allowed = False\n-    default_error_messages = {\n-        'invalid': _('“%(value)s” value must be an integer.'),\n-    }\n-    description = _(\"Integer\")\n-\n-    def check(self, **kwargs):\n-        return [\n-            *super().check(**kwargs),\n-            *self._check_max_length_warning(),\n-        ]\n-\n-    def _check_max_length_warning(self):\n-        if self.max_length is not None:\n-            return [\n-                checks.Warning(\n-                    \"'max_length' is ignored when used with %s.\" % self.__class__.__name__,\n-                    hint=\"Remove 'max_length' from field\",\n-                    obj=self,\n-                    id='fields.W122',\n-                )\n-            ]\n-        return []\n-\n-    @cached_property\n-    def validators(self):\n-        # These validators can't be added at field initialization time since\n-        # they're based on values retrieved from `connection`.\n-        validators_ = super().validators\n-        internal_type = self.get_internal_type()\n-        min_value, max_value = connection.ops.integer_field_range(internal_type)\n-        if min_value is not None and not any(\n-            (\n-                isinstance(validator, validators.MinValueValidator) and (\n-                    validator.limit_value()\n-                    if callable(validator.limit_value)\n-                    else validator.limit_value\n-                ) >= min_value\n-            ) for validator in validators_\n-        ):\n-            validators_.append(validators.MinValueValidator(min_value))\n-        if max_value is not None and not any(\n-            (\n-                isinstance(validator, validators.MaxValueValidator) and (\n-                    validator.limit_value()\n-                    if callable(validator.limit_value)\n-                    else validator.limit_value\n-                ) <= max_value\n-            ) for validator in validators_\n-        ):\n-            validators_.append(validators.MaxValueValidator(max_value))\n-        return validators_\n-\n-    def get_prep_value(self, value):\n-        value = super().get_prep_value(value)\n-        if value is None:\n-            return None\n-        try:\n-            return int(value)\n-        except (TypeError, ValueError) as e:\n-            raise e.__class__(\n-                \"Field '%s' expected a number but got %r.\" % (self.name, value),\n-            ) from e\n-\n-    def get_internal_type(self):\n-        return \"IntegerField\"\n-\n-    def to_python(self, value):\n-        if value is None:\n-            return value\n-        try:\n-            return int(value)\n-        except (TypeError, ValueError):\n-            raise exceptions.ValidationError(\n-                self.error_messages['invalid'],\n-                code='invalid',\n-                params={'value': value},\n-            )\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'form_class': forms.IntegerField,\n-            **kwargs,\n-        })\n-\n-\n-class BigIntegerField(IntegerField):\n-    description = _(\"Big (8 byte) integer\")\n-    MAX_BIGINT = 9223372036854775807\n-\n-    def get_internal_type(self):\n-        return \"BigIntegerField\"\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'min_value': -BigIntegerField.MAX_BIGINT - 1,\n-            'max_value': BigIntegerField.MAX_BIGINT,\n-            **kwargs,\n-        })\n-\n-\n-class IPAddressField(Field):\n-    empty_strings_allowed = False\n-    description = _(\"IPv4 address\")\n-    system_check_removed_details = {\n-        'msg': (\n-            'IPAddressField has been removed except for support in '\n-            'historical migrations.'\n-        ),\n-        'hint': 'Use GenericIPAddressField instead.',\n-        'id': 'fields.E900',\n-    }\n-\n-    def __init__(self, *args, **kwargs):\n-        kwargs['max_length'] = 15\n-        super().__init__(*args, **kwargs)\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        del kwargs['max_length']\n-        return name, path, args, kwargs\n-\n-    def get_prep_value(self, value):\n-        value = super().get_prep_value(value)\n-        if value is None:\n-            return None\n-        return str(value)\n-\n-    def get_internal_type(self):\n-        return \"IPAddressField\"\n-\n-\n-class GenericIPAddressField(Field):\n-    empty_strings_allowed = False\n-    description = _(\"IP address\")\n-    default_error_messages = {}\n-\n-    def __init__(self, verbose_name=None, name=None, protocol='both',\n-                 unpack_ipv4=False, *args, **kwargs):\n-        self.unpack_ipv4 = unpack_ipv4\n-        self.protocol = protocol\n-        self.default_validators, invalid_error_message = \\\n-            validators.ip_address_validators(protocol, unpack_ipv4)\n-        self.default_error_messages['invalid'] = invalid_error_message\n-        kwargs['max_length'] = 39\n-        super().__init__(verbose_name, name, *args, **kwargs)\n-\n-    def check(self, **kwargs):\n-        return [\n-            *super().check(**kwargs),\n-            *self._check_blank_and_null_values(**kwargs),\n-        ]\n-\n-    def _check_blank_and_null_values(self, **kwargs):\n-        if not getattr(self, 'null', False) and getattr(self, 'blank', False):\n-            return [\n-                checks.Error(\n-                    'GenericIPAddressFields cannot have blank=True if null=False, '\n-                    'as blank values are stored as nulls.',\n-                    obj=self,\n-                    id='fields.E150',\n-                )\n-            ]\n-        return []\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        if self.unpack_ipv4 is not False:\n-            kwargs['unpack_ipv4'] = self.unpack_ipv4\n-        if self.protocol != \"both\":\n-            kwargs['protocol'] = self.protocol\n-        if kwargs.get(\"max_length\") == 39:\n-            del kwargs['max_length']\n-        return name, path, args, kwargs\n-\n-    def get_internal_type(self):\n-        return \"GenericIPAddressField\"\n-\n-    def to_python(self, value):\n-        if value is None:\n-            return None\n-        if not isinstance(value, str):\n-            value = str(value)\n-        value = value.strip()\n-        if ':' in value:\n-            return clean_ipv6_address(value, self.unpack_ipv4, self.error_messages['invalid'])\n-        return value\n-\n-    def get_db_prep_value(self, value, connection, prepared=False):\n-        if not prepared:\n-            value = self.get_prep_value(value)\n-        return connection.ops.adapt_ipaddressfield_value(value)\n-\n-    def get_prep_value(self, value):\n-        value = super().get_prep_value(value)\n-        if value is None:\n-            return None\n-        if value and ':' in value:\n-            try:\n-                return clean_ipv6_address(value, self.unpack_ipv4)\n-            except exceptions.ValidationError:\n-                pass\n-        return str(value)\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'protocol': self.protocol,\n-            'form_class': forms.GenericIPAddressField,\n-            **kwargs,\n-        })\n-\n-\n-class NullBooleanField(BooleanField):\n-    default_error_messages = {\n-        'invalid': _('“%(value)s” value must be either None, True or False.'),\n-        'invalid_nullable': _('“%(value)s” value must be either None, True or False.'),\n-    }\n-    description = _(\"Boolean (Either True, False or None)\")\n-\n-    def __init__(self, *args, **kwargs):\n-        kwargs['null'] = True\n-        kwargs['blank'] = True\n-        super().__init__(*args, **kwargs)\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        del kwargs['null']\n-        del kwargs['blank']\n-        return name, path, args, kwargs\n-\n-    def get_internal_type(self):\n-        return \"NullBooleanField\"\n-\n-\n-class PositiveIntegerRelDbTypeMixin:\n-\n-    def rel_db_type(self, connection):\n-        \"\"\"\n-        Return the data type that a related field pointing to this field should\n-        use. In most cases, a foreign key pointing to a positive integer\n-        primary key will have an integer column data type but some databases\n-        (e.g. MySQL) have an unsigned integer type. In that case\n-        (related_fields_match_type=True), the primary key should return its\n-        db_type.\n-        \"\"\"\n-        if connection.features.related_fields_match_type:\n-            return self.db_type(connection)\n-        else:\n-            return IntegerField().db_type(connection=connection)\n-\n-\n-class PositiveIntegerField(PositiveIntegerRelDbTypeMixin, IntegerField):\n-    description = _(\"Positive integer\")\n-\n-    def get_internal_type(self):\n-        return \"PositiveIntegerField\"\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'min_value': 0,\n-            **kwargs,\n-        })\n-\n-\n-class PositiveSmallIntegerField(PositiveIntegerRelDbTypeMixin, IntegerField):\n-    description = _(\"Positive small integer\")\n-\n-    def get_internal_type(self):\n-        return \"PositiveSmallIntegerField\"\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'min_value': 0,\n-            **kwargs,\n-        })\n-\n-\n-class SlugField(CharField):\n-    default_validators = [validators.validate_slug]\n-    description = _(\"Slug (up to %(max_length)s)\")\n-\n-    def __init__(self, *args, max_length=50, db_index=True, allow_unicode=False, **kwargs):\n-        self.allow_unicode = allow_unicode\n-        if self.allow_unicode:\n-            self.default_validators = [validators.validate_unicode_slug]\n-        super().__init__(*args, max_length=max_length, db_index=db_index, **kwargs)\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        if kwargs.get(\"max_length\") == 50:\n-            del kwargs['max_length']\n-        if self.db_index is False:\n-            kwargs['db_index'] = False\n-        else:\n-            del kwargs['db_index']\n-        if self.allow_unicode is not False:\n-            kwargs['allow_unicode'] = self.allow_unicode\n-        return name, path, args, kwargs\n-\n-    def get_internal_type(self):\n-        return \"SlugField\"\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'form_class': forms.SlugField,\n-            'allow_unicode': self.allow_unicode,\n-            **kwargs,\n-        })\n-\n-\n-class SmallIntegerField(IntegerField):\n-    description = _(\"Small integer\")\n-\n-    def get_internal_type(self):\n-        return \"SmallIntegerField\"\n-\n-\n-class TextField(Field):\n-    description = _(\"Text\")\n-\n-    def get_internal_type(self):\n-        return \"TextField\"\n-\n-    def to_python(self, value):\n-        if isinstance(value, str) or value is None:\n-            return value\n-        return str(value)\n-\n-    def get_prep_value(self, value):\n-        value = super().get_prep_value(value)\n-        return self.to_python(value)\n-\n-    def formfield(self, **kwargs):\n-        # Passing max_length to forms.CharField means that the value's length\n-        # will be validated twice. This is considered acceptable since we want\n-        # the value in the form field (to pass into widget for example).\n-        return super().formfield(**{\n-            'max_length': self.max_length,\n-            **({} if self.choices is not None else {'widget': forms.Textarea}),\n-            **kwargs,\n-        })\n-\n-\n-class TimeField(DateTimeCheckMixin, Field):\n-    empty_strings_allowed = False\n-    default_error_messages = {\n-        'invalid': _('“%(value)s” value has an invalid format. It must be in '\n-                     'HH:MM[:ss[.uuuuuu]] format.'),\n-        'invalid_time': _('“%(value)s” value has the correct format '\n-                          '(HH:MM[:ss[.uuuuuu]]) but it is an invalid time.'),\n-    }\n-    description = _(\"Time\")\n-\n-    def __init__(self, verbose_name=None, name=None, auto_now=False,\n-                 auto_now_add=False, **kwargs):\n-        self.auto_now, self.auto_now_add = auto_now, auto_now_add\n-        if auto_now or auto_now_add:\n-            kwargs['editable'] = False\n-            kwargs['blank'] = True\n-        super().__init__(verbose_name, name, **kwargs)\n-\n-    def _check_fix_default_value(self):\n-        \"\"\"\n-        Warn that using an actual date or datetime value is probably wrong;\n-        it's only evaluated on server startup.\n-        \"\"\"\n-        if not self.has_default():\n-            return []\n-\n-        now = timezone.now()\n-        if not timezone.is_naive(now):\n-            now = timezone.make_naive(now, timezone.utc)\n-        value = self.default\n-        if isinstance(value, datetime.datetime):\n-            second_offset = datetime.timedelta(seconds=10)\n-            lower = now - second_offset\n-            upper = now + second_offset\n-            if timezone.is_aware(value):\n-                value = timezone.make_naive(value, timezone.utc)\n-        elif isinstance(value, datetime.time):\n-            second_offset = datetime.timedelta(seconds=10)\n-            lower = now - second_offset\n-            upper = now + second_offset\n-            value = datetime.datetime.combine(now.date(), value)\n-            if timezone.is_aware(value):\n-                value = timezone.make_naive(value, timezone.utc).time()\n-        else:\n-            # No explicit time / datetime value -- no checks necessary\n-            return []\n-        if lower <= value <= upper:\n-            return [\n-                checks.Warning(\n-                    'Fixed default value provided.',\n-                    hint='It seems you set a fixed date / time / datetime '\n-                         'value as default for this field. This may not be '\n-                         'what you want. If you want to have the current date '\n-                         'as default, use `django.utils.timezone.now`',\n-                    obj=self,\n-                    id='fields.W161',\n-                )\n-            ]\n-\n-        return []\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        if self.auto_now is not False:\n-            kwargs[\"auto_now\"] = self.auto_now\n-        if self.auto_now_add is not False:\n-            kwargs[\"auto_now_add\"] = self.auto_now_add\n-        if self.auto_now or self.auto_now_add:\n-            del kwargs['blank']\n-            del kwargs['editable']\n-        return name, path, args, kwargs\n-\n-    def get_internal_type(self):\n-        return \"TimeField\"\n-\n-    def to_python(self, value):\n-        if value is None:\n-            return None\n-        if isinstance(value, datetime.time):\n-            return value\n-        if isinstance(value, datetime.datetime):\n-            # Not usually a good idea to pass in a datetime here (it loses\n-            # information), but this can be a side-effect of interacting with a\n-            # database backend (e.g. Oracle), so we'll be accommodating.\n-            return value.time()\n-\n-        try:\n-            parsed = parse_time(value)\n-            if parsed is not None:\n-                return parsed\n-        except ValueError:\n-            raise exceptions.ValidationError(\n-                self.error_messages['invalid_time'],\n-                code='invalid_time',\n-                params={'value': value},\n-            )\n-\n-        raise exceptions.ValidationError(\n-            self.error_messages['invalid'],\n-            code='invalid',\n-            params={'value': value},\n-        )\n-\n-    def pre_save(self, model_instance, add):\n-        if self.auto_now or (self.auto_now_add and add):\n-            value = datetime.datetime.now().time()\n-            setattr(model_instance, self.attname, value)\n-            return value\n-        else:\n-            return super().pre_save(model_instance, add)\n-\n-    def get_prep_value(self, value):\n-        value = super().get_prep_value(value)\n-        return self.to_python(value)\n-\n-    def get_db_prep_value(self, value, connection, prepared=False):\n-        # Casts times into the format expected by the backend\n-        if not prepared:\n-            value = self.get_prep_value(value)\n-        return connection.ops.adapt_timefield_value(value)\n-\n-    def value_to_string(self, obj):\n-        val = self.value_from_object(obj)\n-        return '' if val is None else val.isoformat()\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'form_class': forms.TimeField,\n-            **kwargs,\n-        })\n-\n-\n-class URLField(CharField):\n-    default_validators = [validators.URLValidator()]\n-    description = _(\"URL\")\n-\n-    def __init__(self, verbose_name=None, name=None, **kwargs):\n-        kwargs.setdefault('max_length', 200)\n-        super().__init__(verbose_name, name, **kwargs)\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        if kwargs.get(\"max_length\") == 200:\n-            del kwargs['max_length']\n-        return name, path, args, kwargs\n-\n-    def formfield(self, **kwargs):\n-        # As with CharField, this will cause URL validation to be performed\n-        # twice.\n-        return super().formfield(**{\n-            'form_class': forms.URLField,\n-            **kwargs,\n-        })\n-\n-\n-class BinaryField(Field):\n-    description = _(\"Raw binary data\")\n-    empty_values = [None, b'']\n-\n-    def __init__(self, *args, **kwargs):\n-        kwargs.setdefault('editable', False)\n-        super().__init__(*args, **kwargs)\n-        if self.max_length is not None:\n-            self.validators.append(validators.MaxLengthValidator(self.max_length))\n-\n-    def check(self, **kwargs):\n-        return [*super().check(**kwargs), *self._check_str_default_value()]\n-\n-    def _check_str_default_value(self):\n-        if self.has_default() and isinstance(self.default, str):\n-            return [\n-                checks.Error(\n-                    \"BinaryField's default cannot be a string. Use bytes \"\n-                    \"content instead.\",\n-                    obj=self,\n-                    id='fields.E170',\n-                )\n-            ]\n-        return []\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        if self.editable:\n-            kwargs['editable'] = True\n-        else:\n-            del kwargs['editable']\n-        return name, path, args, kwargs\n-\n-    def get_internal_type(self):\n-        return \"BinaryField\"\n-\n-    def get_placeholder(self, value, compiler, connection):\n-        return connection.ops.binary_placeholder_sql(value)\n-\n-    def get_default(self):\n-        if self.has_default() and not callable(self.default):\n-            return self.default\n-        default = super().get_default()\n-        if default == '':\n-            return b''\n-        return default\n-\n-    def get_db_prep_value(self, value, connection, prepared=False):\n-        value = super().get_db_prep_value(value, connection, prepared)\n-        if value is not None:\n-            return connection.Database.Binary(value)\n-        return value\n-\n-    def value_to_string(self, obj):\n-        \"\"\"Binary data is serialized as base64\"\"\"\n-        return b64encode(self.value_from_object(obj)).decode('ascii')\n-\n-    def to_python(self, value):\n-        # If it's a string, it should be base64-encoded data\n-        if isinstance(value, str):\n-            return memoryview(b64decode(value.encode('ascii')))\n-        return value\n-\n-\n-class UUIDField(Field):\n-    default_error_messages = {\n-        'invalid': _('“%(value)s” is not a valid UUID.'),\n-    }\n-    description = _('Universally unique identifier')\n-    empty_strings_allowed = False\n-\n-    def __init__(self, verbose_name=None, **kwargs):\n-        kwargs['max_length'] = 32\n-        super().__init__(verbose_name, **kwargs)\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        del kwargs['max_length']\n-        return name, path, args, kwargs\n-\n-    def get_internal_type(self):\n-        return \"UUIDField\"\n-\n-    def get_prep_value(self, value):\n-        value = super().get_prep_value(value)\n-        return self.to_python(value)\n-\n-    def get_db_prep_value(self, value, connection, prepared=False):\n-        if value is None:\n-            return None\n-        if not isinstance(value, uuid.UUID):\n-            value = self.to_python(value)\n-\n-        if connection.features.has_native_uuid_field:\n-            return value\n-        return value.hex\n-\n-    def to_python(self, value):\n-        if value is not None and not isinstance(value, uuid.UUID):\n-            input_form = 'int' if isinstance(value, int) else 'hex'\n-            try:\n-                return uuid.UUID(**{input_form: value})\n-            except (AttributeError, ValueError):\n-                raise exceptions.ValidationError(\n-                    self.error_messages['invalid'],\n-                    code='invalid',\n-                    params={'value': value},\n-                )\n-        return value\n-\n-    def formfield(self, **kwargs):\n-        return super().formfield(**{\n-            'form_class': forms.UUIDField,\n-            **kwargs,\n-        })\n-\n-\n-class AutoFieldMixin:\n-    db_returning = True\n-\n-    def __init__(self, *args, **kwargs):\n-        kwargs['blank'] = True\n-        super().__init__(*args, **kwargs)\n-\n-    def check(self, **kwargs):\n-        return [\n-            *super().check(**kwargs),\n-            *self._check_primary_key(),\n-        ]\n-\n-    def _check_primary_key(self):\n-        if not self.primary_key:\n-            return [\n-                checks.Error(\n-                    'AutoFields must set primary_key=True.',\n-                    obj=self,\n-                    id='fields.E100',\n-                ),\n-            ]\n-        else:\n-            return []\n-\n-    def deconstruct(self):\n-        name, path, args, kwargs = super().deconstruct()\n-        del kwargs['blank']\n-        kwargs['primary_key'] = True\n-        return name, path, args, kwargs\n-\n-    def validate(self, value, model_instance):\n-        pass\n-\n-    def get_db_prep_value(self, value, connection, prepared=False):\n-        if not prepared:\n-            value = self.get_prep_value(value)\n-            value = connection.ops.validate_autopk_value(value)\n-        return value\n-\n-    def contribute_to_class(self, cls, name, **kwargs):\n-        assert not cls._meta.auto_field, (\n-            \"Model %s can't have more than one auto-generated field.\"\n-            % cls._meta.label\n-        )\n-        super().contribute_to_class(cls, name, **kwargs)\n-        cls._meta.auto_field = self\n-\n-    def formfield(self, **kwargs):\n-        return None\n-\n-\n-class AutoFieldMeta(type):\n-    \"\"\"\n-    Metaclass to maintain backward inheritance compatibility for AutoField.\n-\n-    It is intended that AutoFieldMixin become public API when it is possible to\n-    create a non-integer automatically-generated field using column defaults\n-    stored in the database.\n-\n-    In many areas Django also relies on using isinstance() to check for an\n-    automatically-generated field as a subclass of AutoField. A new flag needs\n-    to be implemented on Field to be used instead.\n-\n-    When these issues have been addressed, this metaclass could be used to\n-    deprecate inheritance from AutoField and use of isinstance() with AutoField\n-    for detecting automatically-generated fields.\n-    \"\"\"\n-\n-    @property\n-    def _subclasses(self):\n-        return (BigAutoField, SmallAutoField)\n-\n-    def __instancecheck__(self, instance):\n-        return isinstance(instance, self._subclasses) or super().__instancecheck__(instance)\n-\n-    def __subclasscheck__(self, subclass):\n-        return subclass in self._subclasses or super().__subclasscheck__(subclass)\n-\n-\n-class AutoField(AutoFieldMixin, IntegerField, metaclass=AutoFieldMeta):\n-\n-    def get_internal_type(self):\n-        return 'AutoField'\n-\n-    def rel_db_type(self, connection):\n-        return IntegerField().db_type(connection=connection)\n-\n-\n-class BigAutoField(AutoFieldMixin, BigIntegerField):\n-\n-    def get_internal_type(self):\n-        return 'BigAutoField'\n-\n-    def rel_db_type(self, connection):\n-        return BigIntegerField().db_type(connection=connection)\n-\n-\n-class SmallAutoField(AutoFieldMixin, SmallIntegerField):\n-\n-    def get_internal_type(self):\n-        return 'SmallAutoField'\n-\n-    def rel_db_type(self, connection):\n-        return SmallIntegerField().db_type(connection=connection)\n+    pass\n\\ No newline at end of file\ndiff --git a/django/db/models/lookups.py b/django/db/models/lookups.py\nindex 4b41906966..e5e14e7faa 100644\n--- a/django/db/models/lookups.py\n+++ b/django/db/models/lookups.py\n@@ -1,16 +1,13 @@\n+\"\"\"\n+Class for storing a lookup like field__lookuptype handling.\n+\"\"\"\n import itertools\n import math\n import warnings\n from copy import copy\n \n from django.core.exceptions import EmptyResultSet\n-from django.db.models.expressions import Case, Exists, Func, Value, When\n-from django.db.models.fields import (\n-    BooleanField, CharField, DateTimeField, Field, IntegerField, UUIDField,\n-)\n from django.db.models.query_utils import RegisterLookupMixin\n-from django.utils.datastructures import OrderedSet\n-from django.utils.deprecation import RemovedInDjango40Warning\n from django.utils.functional import cached_property\n \n \n@@ -94,8 +91,11 @@ class Lookup:\n             value = value.resolve_expression(compiler.query)\n         if hasattr(value, 'as_sql'):\n             return compiler.compile(value)\n-        else:\n-            return self.get_db_prep_lookup(value, connection)\n+        # Don't convert lists to tuples if the field type is PickledField\n+        if not self.lhs.output_field.get_internal_type() == 'PickledField':\n+            if isinstance(value, list):\n+                value = tuple(value)\n+        return self.get_db_prep_lookup(value, connection)\n \n     def rhs_is_direct_value(self):\n         return not hasattr(self.rhs, 'as_sql')\n@@ -119,15 +119,7 @@ class Lookup:\n     def as_oracle(self, compiler, connection):\n         # Oracle doesn't allow EXISTS() to be compared to another expression\n         # unless it's wrapped in a CASE WHEN.\n-        wrapped = False\n-        exprs = []\n-        for expr in (self.lhs, self.rhs):\n-            if isinstance(expr, Exists):\n-                expr = Case(When(expr, then=True), default=False, output_field=BooleanField())\n-                wrapped = True\n-            exprs.append(expr)\n-        lookup = type(self)(*exprs) if wrapped else self\n-        return lookup.as_sql(compiler, connection)\n+        return self.as_sql(compiler, connection)\n \n     @cached_property\n     def contains_aggregate(self):\n@@ -142,472 +134,52 @@ class Lookup:\n         return self.lhs.is_summary or getattr(self.rhs, 'is_summary', False)\n \n \n-class Transform(RegisterLookupMixin, Func):\n-    \"\"\"\n-    RegisterLookupMixin() is first so that get_lookup() and get_transform()\n-    first examine self and then check output_field.\n-    \"\"\"\n+class Transform(RegisterLookupMixin):\n     bilateral = False\n-    arity = 1\n-\n-    @property\n-    def lhs(self):\n-        return self.get_source_expressions()[0]\n-\n-    def get_bilateral_transforms(self):\n-        if hasattr(self.lhs, 'get_bilateral_transforms'):\n-            bilateral_transforms = self.lhs.get_bilateral_transforms()\n-        else:\n-            bilateral_transforms = []\n-        if self.bilateral:\n-            bilateral_transforms.append(self.__class__)\n-        return bilateral_transforms\n \n+    def __init__(self, expression, output_field=None):\n+        self.expression = expression\n+        self._output_field = output_field\n \n-class BuiltinLookup(Lookup):\n-    def process_lhs(self, compiler, connection, lhs=None):\n-        lhs_sql, params = super().process_lhs(compiler, connection, lhs)\n-        field_internal_type = self.lhs.output_field.get_internal_type()\n-        db_type = self.lhs.output_field.db_type(connection=connection)\n-        lhs_sql = connection.ops.field_cast_sql(\n-            db_type, field_internal_type) % lhs_sql\n-        lhs_sql = connection.ops.lookup_cast(self.lookup_name, field_internal_type) % lhs_sql\n-        return lhs_sql, list(params)\n+    @property\n+    def output_field(self):\n+        if self._output_field is None:\n+            self._output_field = self.expression.output_field\n+        return self._output_field\n \n     def as_sql(self, compiler, connection):\n-        lhs_sql, params = self.process_lhs(compiler, connection)\n-        rhs_sql, rhs_params = self.process_rhs(compiler, connection)\n-        params.extend(rhs_params)\n-        rhs_sql = self.get_rhs_op(connection, rhs_sql)\n-        return '%s %s' % (lhs_sql, rhs_sql), params\n+        raise NotImplementedError\n \n-    def get_rhs_op(self, connection, rhs):\n-        return connection.operators[self.lookup_name] % rhs\n+    def get_source_expressions(self):\n+        return [self.expression]\n \n+    def set_source_expressions(self, exprs):\n+        self.expression = exprs[0]\n \n-class FieldGetDbPrepValueMixin:\n-    \"\"\"\n-    Some lookups require Field.get_db_prep_value() to be called on their\n-    inputs.\n-    \"\"\"\n-    get_db_prep_lookup_value_is_iterable = False\n+    def resolve_expression(self, query=None, allow_joins=True, reuse=None, summarize=False, for_save=False):\n+        copy = self.copy()\n+        copy.is_summary = summarize\n+        copy.expression = copy.expression.resolve_expression(query, allow_joins, reuse, summarize, for_save)\n+        return copy\n \n-    def get_db_prep_lookup(self, value, connection):\n-        # For relational fields, use the 'target_field' attribute of the\n-        # output_field.\n-        field = getattr(self.lhs.output_field, 'target_field', None)\n-        get_db_prep_value = getattr(field, 'get_db_prep_value', None) or self.lhs.output_field.get_db_prep_value\n-        return (\n-            '%s',\n-            [get_db_prep_value(v, connection, prepared=True) for v in value]\n-            if self.get_db_prep_lookup_value_is_iterable else\n-            [get_db_prep_value(value, connection, prepared=True)]\n-        )\n-\n-\n-class FieldGetDbPrepValueIterableMixin(FieldGetDbPrepValueMixin):\n-    \"\"\"\n-    Some lookups require Field.get_db_prep_value() to be called on each value\n-    in an iterable.\n-    \"\"\"\n-    get_db_prep_lookup_value_is_iterable = True\n+    def relabeled_clone(self, relabels):\n+        return self.__class__(self.expression.relabeled_clone(relabels))\n \n-    def get_prep_lookup(self):\n-        if hasattr(self.rhs, 'resolve_expression'):\n-            return self.rhs\n-        prepared_values = []\n-        for rhs_value in self.rhs:\n-            if hasattr(rhs_value, 'resolve_expression'):\n-                # An expression will be handled by the database but can coexist\n-                # alongside real values.\n-                pass\n-            elif self.prepare_rhs and hasattr(self.lhs.output_field, 'get_prep_value'):\n-                rhs_value = self.lhs.output_field.get_prep_value(rhs_value)\n-            prepared_values.append(rhs_value)\n-        return prepared_values\n+    def copy(self):\n+        clone = copy(self)\n+        clone.expression = self.expression.copy()\n+        return clone\n \n-    def process_rhs(self, compiler, connection):\n-        if self.rhs_is_direct_value():\n-            # rhs should be an iterable of values. Use batch_process_rhs()\n-            # to prepare/transform those values.\n-            return self.batch_process_rhs(compiler, connection)\n-        else:\n-            return super().process_rhs(compiler, connection)\n+    def get_group_by_cols(self):\n+        return self.expression.get_group_by_cols()\n \n-    def resolve_expression_parameter(self, compiler, connection, sql, param):\n-        params = [param]\n-        if hasattr(param, 'resolve_expression'):\n-            param = param.resolve_expression(compiler.query)\n-        if hasattr(param, 'as_sql'):\n-            sql, params = param.as_sql(compiler, connection)\n-        return sql, params\n \n-    def batch_process_rhs(self, compiler, connection, rhs=None):\n-        pre_processed = super().batch_process_rhs(compiler, connection, rhs)\n-        # The params list may contain expressions which compile to a\n-        # sql/param pair. Zip them to get sql and param pairs that refer to the\n-        # same argument and attempt to replace them with the result of\n-        # compiling the param step.\n-        sql, params = zip(*(\n-            self.resolve_expression_parameter(compiler, connection, sql, param)\n-            for sql, param in zip(*pre_processed)\n-        ))\n-        params = itertools.chain.from_iterable(params)\n-        return sql, tuple(params)\n-\n-\n-@Field.register_lookup\n-class Exact(FieldGetDbPrepValueMixin, BuiltinLookup):\n+class ExactLookup(Lookup):\n     lookup_name = 'exact'\n \n-    def process_rhs(self, compiler, connection):\n-        from django.db.models.sql.query import Query\n-        if isinstance(self.rhs, Query):\n-            if self.rhs.has_limit_one():\n-                if not self.rhs.has_select_fields:\n-                    self.rhs.clear_select_clause()\n-                    self.rhs.add_fields(['pk'])\n-            else:\n-                raise ValueError(\n-                    'The QuerySet value for an exact lookup must be limited to '\n-                    'one result using slicing.'\n-                )\n-        return super().process_rhs(compiler, connection)\n-\n-\n-@Field.register_lookup\n-class IExact(BuiltinLookup):\n-    lookup_name = 'iexact'\n-    prepare_rhs = False\n-\n-    def process_rhs(self, qn, connection):\n-        rhs, params = super().process_rhs(qn, connection)\n-        if params:\n-            params[0] = connection.ops.prep_for_iexact_query(params[0])\n-        return rhs, params\n-\n-\n-@Field.register_lookup\n-class GreaterThan(FieldGetDbPrepValueMixin, BuiltinLookup):\n-    lookup_name = 'gt'\n-\n-\n-@Field.register_lookup\n-class GreaterThanOrEqual(FieldGetDbPrepValueMixin, BuiltinLookup):\n-    lookup_name = 'gte'\n-\n-\n-@Field.register_lookup\n-class LessThan(FieldGetDbPrepValueMixin, BuiltinLookup):\n-    lookup_name = 'lt'\n-\n-\n-@Field.register_lookup\n-class LessThanOrEqual(FieldGetDbPrepValueMixin, BuiltinLookup):\n-    lookup_name = 'lte'\n-\n-\n-class IntegerFieldFloatRounding:\n-    \"\"\"\n-    Allow floats to work as query values for IntegerField. Without this, the\n-    decimal portion of the float would always be discarded.\n-    \"\"\"\n-    def get_prep_lookup(self):\n-        if isinstance(self.rhs, float):\n-            self.rhs = math.ceil(self.rhs)\n-        return super().get_prep_lookup()\n-\n-\n-@IntegerField.register_lookup\n-class IntegerGreaterThanOrEqual(IntegerFieldFloatRounding, GreaterThanOrEqual):\n-    pass\n-\n-\n-@IntegerField.register_lookup\n-class IntegerLessThan(IntegerFieldFloatRounding, LessThan):\n-    pass\n-\n-\n-@Field.register_lookup\n-class In(FieldGetDbPrepValueIterableMixin, BuiltinLookup):\n-    lookup_name = 'in'\n-\n-    def process_rhs(self, compiler, connection):\n-        db_rhs = getattr(self.rhs, '_db', None)\n-        if db_rhs is not None and db_rhs != connection.alias:\n-            raise ValueError(\n-                \"Subqueries aren't allowed across different databases. Force \"\n-                \"the inner query to be evaluated using `list(inner_query)`.\"\n-            )\n-\n-        if self.rhs_is_direct_value():\n-            try:\n-                rhs = OrderedSet(self.rhs)\n-            except TypeError:  # Unhashable items in self.rhs\n-                rhs = self.rhs\n-\n-            if not rhs:\n-                raise EmptyResultSet\n-\n-            # rhs should be an iterable; use batch_process_rhs() to\n-            # prepare/transform those values.\n-            sqls, sqls_params = self.batch_process_rhs(compiler, connection, rhs)\n-            placeholder = '(' + ', '.join(sqls) + ')'\n-            return (placeholder, sqls_params)\n-        else:\n-            if not getattr(self.rhs, 'has_select_fields', True):\n-                self.rhs.clear_select_clause()\n-                self.rhs.add_fields(['pk'])\n-            return super().process_rhs(compiler, connection)\n-\n-    def get_rhs_op(self, connection, rhs):\n-        return 'IN %s' % rhs\n-\n-    def as_sql(self, compiler, connection):\n-        max_in_list_size = connection.ops.max_in_list_size()\n-        if self.rhs_is_direct_value() and max_in_list_size and len(self.rhs) > max_in_list_size:\n-            return self.split_parameter_list_as_sql(compiler, connection)\n-        return super().as_sql(compiler, connection)\n-\n-    def split_parameter_list_as_sql(self, compiler, connection):\n-        # This is a special case for databases which limit the number of\n-        # elements which can appear in an 'IN' clause.\n-        max_in_list_size = connection.ops.max_in_list_size()\n-        lhs, lhs_params = self.process_lhs(compiler, connection)\n-        rhs, rhs_params = self.batch_process_rhs(compiler, connection)\n-        in_clause_elements = ['(']\n-        params = []\n-        for offset in range(0, len(rhs_params), max_in_list_size):\n-            if offset > 0:\n-                in_clause_elements.append(' OR ')\n-            in_clause_elements.append('%s IN (' % lhs)\n-            params.extend(lhs_params)\n-            sqls = rhs[offset: offset + max_in_list_size]\n-            sqls_params = rhs_params[offset: offset + max_in_list_size]\n-            param_group = ', '.join(sqls)\n-            in_clause_elements.append(param_group)\n-            in_clause_elements.append(')')\n-            params.extend(sqls_params)\n-        in_clause_elements.append(')')\n-        return ''.join(in_clause_elements), params\n-\n-\n-class PatternLookup(BuiltinLookup):\n-    param_pattern = '%%%s%%'\n-    prepare_rhs = False\n-\n-    def get_rhs_op(self, connection, rhs):\n-        # Assume we are in startswith. We need to produce SQL like:\n-        #     col LIKE %s, ['thevalue%']\n-        # For python values we can (and should) do that directly in Python,\n-        # but if the value is for example reference to other column, then\n-        # we need to add the % pattern match to the lookup by something like\n-        #     col LIKE othercol || '%%'\n-        # So, for Python values we don't need any special pattern, but for\n-        # SQL reference values or SQL transformations we need the correct\n-        # pattern added.\n-        if hasattr(self.rhs, 'as_sql') or self.bilateral_transforms:\n-            pattern = connection.pattern_ops[self.lookup_name].format(connection.pattern_esc)\n-            return pattern.format(rhs)\n-        else:\n-            return super().get_rhs_op(connection, rhs)\n-\n-    def process_rhs(self, qn, connection):\n-        rhs, params = super().process_rhs(qn, connection)\n-        if self.rhs_is_direct_value() and params and not self.bilateral_transforms:\n-            params[0] = self.param_pattern % connection.ops.prep_for_like_query(params[0])\n-        return rhs, params\n-\n-\n-@Field.register_lookup\n-class Contains(PatternLookup):\n-    lookup_name = 'contains'\n-\n-\n-@Field.register_lookup\n-class IContains(Contains):\n-    lookup_name = 'icontains'\n-\n-\n-@Field.register_lookup\n-class StartsWith(PatternLookup):\n-    lookup_name = 'startswith'\n-    param_pattern = '%s%%'\n-\n-\n-@Field.register_lookup\n-class IStartsWith(StartsWith):\n-    lookup_name = 'istartswith'\n-\n-\n-@Field.register_lookup\n-class EndsWith(PatternLookup):\n-    lookup_name = 'endswith'\n-    param_pattern = '%%%s'\n-\n-\n-@Field.register_lookup\n-class IEndsWith(EndsWith):\n-    lookup_name = 'iendswith'\n-\n-\n-@Field.register_lookup\n-class Range(FieldGetDbPrepValueIterableMixin, BuiltinLookup):\n-    lookup_name = 'range'\n-\n-    def get_rhs_op(self, connection, rhs):\n-        return \"BETWEEN %s AND %s\" % (rhs[0], rhs[1])\n-\n-\n-@Field.register_lookup\n-class IsNull(BuiltinLookup):\n-    lookup_name = 'isnull'\n-    prepare_rhs = False\n-\n-    def as_sql(self, compiler, connection):\n-        if not isinstance(self.rhs, bool):\n-            # When the deprecation ends, replace with:\n-            # raise ValueError(\n-            #     'The QuerySet value for an isnull lookup must be True or '\n-            #     'False.'\n-            # )\n-            warnings.warn(\n-                'Using a non-boolean value for an isnull lookup is '\n-                'deprecated, use True or False instead.',\n-                RemovedInDjango40Warning,\n-            )\n-        sql, params = compiler.compile(self.lhs)\n-        if self.rhs:\n-            return \"%s IS NULL\" % sql, params\n-        else:\n-            return \"%s IS NOT NULL\" % sql, params\n-\n-\n-@Field.register_lookup\n-class Regex(BuiltinLookup):\n-    lookup_name = 'regex'\n-    prepare_rhs = False\n-\n     def as_sql(self, compiler, connection):\n-        if self.lookup_name in connection.operators:\n-            return super().as_sql(compiler, connection)\n-        else:\n-            lhs, lhs_params = self.process_lhs(compiler, connection)\n-            rhs, rhs_params = self.process_rhs(compiler, connection)\n-            sql_template = connection.ops.regex_lookup(self.lookup_name)\n-            return sql_template % (lhs, rhs), lhs_params + rhs_params\n-\n-\n-@Field.register_lookup\n-class IRegex(Regex):\n-    lookup_name = 'iregex'\n-\n-\n-class YearLookup(Lookup):\n-    def year_lookup_bounds(self, connection, year):\n-        output_field = self.lhs.lhs.output_field\n-        if isinstance(output_field, DateTimeField):\n-            bounds = connection.ops.year_lookup_bounds_for_datetime_field(year)\n-        else:\n-            bounds = connection.ops.year_lookup_bounds_for_date_field(year)\n-        return bounds\n-\n-    def as_sql(self, compiler, connection):\n-        # Avoid the extract operation if the rhs is a direct value to allow\n-        # indexes to be used.\n-        if self.rhs_is_direct_value():\n-            # Skip the extract part by directly using the originating field,\n-            # that is self.lhs.lhs.\n-            lhs_sql, params = self.process_lhs(compiler, connection, self.lhs.lhs)\n-            rhs_sql, _ = self.process_rhs(compiler, connection)\n-            rhs_sql = self.get_direct_rhs_sql(connection, rhs_sql)\n-            start, finish = self.year_lookup_bounds(connection, self.rhs)\n-            params.extend(self.get_bound_params(start, finish))\n-            return '%s %s' % (lhs_sql, rhs_sql), params\n-        return super().as_sql(compiler, connection)\n-\n-    def get_direct_rhs_sql(self, connection, rhs):\n-        return connection.operators[self.lookup_name] % rhs\n-\n-    def get_bound_params(self, start, finish):\n-        raise NotImplementedError(\n-            'subclasses of YearLookup must provide a get_bound_params() method'\n-        )\n-\n-\n-class YearExact(YearLookup, Exact):\n-    def get_direct_rhs_sql(self, connection, rhs):\n-        return 'BETWEEN %s AND %s'\n-\n-    def get_bound_params(self, start, finish):\n-        return (start, finish)\n-\n-\n-class YearGt(YearLookup, GreaterThan):\n-    def get_bound_params(self, start, finish):\n-        return (finish,)\n-\n-\n-class YearGte(YearLookup, GreaterThanOrEqual):\n-    def get_bound_params(self, start, finish):\n-        return (start,)\n-\n-\n-class YearLt(YearLookup, LessThan):\n-    def get_bound_params(self, start, finish):\n-        return (start,)\n-\n-\n-class YearLte(YearLookup, LessThanOrEqual):\n-    def get_bound_params(self, start, finish):\n-        return (finish,)\n-\n-\n-class UUIDTextMixin:\n-    \"\"\"\n-    Strip hyphens from a value when filtering a UUIDField on backends without\n-    a native datatype for UUID.\n-    \"\"\"\n-    def process_rhs(self, qn, connection):\n-        if not connection.features.has_native_uuid_field:\n-            from django.db.models.functions import Replace\n-            if self.rhs_is_direct_value():\n-                self.rhs = Value(self.rhs)\n-            self.rhs = Replace(self.rhs, Value('-'), Value(''), output_field=CharField())\n-        rhs, params = super().process_rhs(qn, connection)\n-        return rhs, params\n-\n-\n-@UUIDField.register_lookup\n-class UUIDIExact(UUIDTextMixin, IExact):\n-    pass\n-\n-\n-@UUIDField.register_lookup\n-class UUIDContains(UUIDTextMixin, Contains):\n-    pass\n-\n-\n-@UUIDField.register_lookup\n-class UUIDIContains(UUIDTextMixin, IContains):\n-    pass\n-\n-\n-@UUIDField.register_lookup\n-class UUIDStartsWith(UUIDTextMixin, StartsWith):\n-    pass\n-\n-\n-@UUIDField.register_lookup\n-class UUIDIStartsWith(UUIDTextMixin, IStartsWith):\n-    pass\n-\n-\n-@UUIDField.register_lookup\n-class UUIDEndsWith(UUIDTextMixin, EndsWith):\n-    pass\n-\n-\n-@UUIDField.register_lookup\n-class UUIDIEndsWith(UUIDTextMixin, IEndsWith):\n-    pass\n+        lhs_sql, params = self.process_lhs(compiler, connection)\n+        rhs_sql, rhs_params = self.process_rhs(compiler, connection)\n+        params.extend(rhs_params)\n+        rhs_sql = '=' + rhs_sql\n+        return '%s %s' % (lhs_sql, rhs_sql), params\n\\ No newline at end of file\ndiff --git a/django/db/models/transforms.py b/django/db/models/transforms.py\nnew file mode 100644\nindex 0000000000..fdccf6a78b\n--- /dev/null\n+++ b/django/db/models/transforms.py\n@@ -0,0 +1,15 @@\n+\"\"\"\n+Transform modifies the query (e.g. date field to date string) before executing the comparison.\n+\"\"\"\n+from django.db.models.query_utils import RegisterLookupMixin\n+\n+\n+class Transform(RegisterLookupMixin):\n+    bilateral = False\n+    \n+    def __init__(self, expression):\n+        self.expression = expression\n+    \n+    @property\n+    def output_field(self):\n+        return self.expression.output_field\n\\ No newline at end of file\ndiff --git a/tests/pickled_lists/__init__.py b/tests/pickled_lists/__init__.py\nnew file mode 100644\nindex 0000000000..e69de29bb2\ndiff --git a/tests/pickled_lists/models.py b/tests/pickled_lists/models.py\nnew file mode 100644\nindex 0000000000..b2a5a2614f\n--- /dev/null\n+++ b/tests/pickled_lists/models.py\n@@ -0,0 +1,8 @@\n+from django.db import models\n+import pickle\n+\n+from django.db.models.fields import PickledField\n+\n+\n+class TestModel(models.Model):\n+    data = PickledField()\n\\ No newline at end of file\ndiff --git a/tests/pickled_lists/tests.py b/tests/pickled_lists/tests.py\nnew file mode 100644\nindex 0000000000..bf38226c94\n--- /dev/null\n+++ b/tests/pickled_lists/tests.py\n@@ -0,0 +1,12 @@\n+from django.test import TestCase\n+from .models import TestModel\n+\n+\n+class PickledListTests(TestCase):\n+    def test_list_lookup_maintains_type(self):\n+        \"\"\"Test that lookup values maintain their list type when querying.\"\"\"\n+        test_list = [1, 2, 3]\n+        obj = TestModel.objects.create(data=test_list)\n+        queried_obj = TestModel.objects.get(data=test_list)\n+        self.assertIs(type(queried_obj.data), list)\n+        self.assertEqual(queried_obj.data, test_list)\n\\ No newline at end of file\n", "proposed_model_patches": []}