#!/usr/bin/env python3
"""
Test script to verify direct Ollama connection works with OpenAI-compatible API.
"""

import openai
import sys

def test_direct_ollama():
    """Test direct connection to Ollama using OpenAI-compatible API."""
    print("Testing direct Ollama connection...")
    
    try:
        # Create OpenAI client pointing to Ollama
        client = openai.OpenAI(
            api_key="ollama",  # Ollama doesn't need a real API key
            base_url="http://localhost:11434/v1"  # Ollama OpenAI-compatible endpoint
        )
        
        # Test a simple completion
        response = client.chat.completions.create(
            model="qwen3:30b-a3b",
            messages=[
                {"role": "user", "content": "Hello! Please respond with 'Hello from qwen3:30b-a3b via direct Ollama connection'"}
            ],
            max_tokens=50
        )
        
        content = response.choices[0].message.content
        print(f"✓ Direct Ollama connection successful!")
        print(f"Response: {content}")
        return True
        
    except Exception as e:
        print(f"✗ Direct Ollama connection failed: {e}")
        return False

def main():
    """Run the test."""
    print("=== Direct Ollama Connection Test ===\n")
    
    if test_direct_ollama():
        print("\n✓ Direct Ollama connection is working!")
        print("DGM can now use your local qwen3:30b-a3b model directly.")
        return 0
    else:
        print("\n✗ Direct Ollama connection failed.")
        print("Please make sure Ollama is running: ollama serve")
        return 1

if __name__ == "__main__":
    sys.exit(main())
