#!/bin/bash

# Start LiteLL<PERSON> proxy to route requests to local Ollama server
# This script starts the LiteLLM proxy that will act as a bridge between
# the DGM project and your local Ollama server running qwen3:30b-a3b

echo "Starting LiteLLM proxy for Ollama integration..."

# Check if litellm is installed
if ! command -v litellm &> /dev/null; then
    echo "LiteLLM is not installed. Installing..."
    pip install litellm
fi

# Check if Ollama is running
if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo "Warning: Ollama server doesn't seem to be running on localhost:11434"
    echo "Please make sure Ollama is running with: ollama serve"
    echo "And that you have the qwen3:30b-a3b model available with: ollama pull qwen3:30b-a3b"
fi

# Start LiteLLM proxy
echo "Starting LiteLLM proxy on port 4000..."
litellm --config litellm_config.yaml --port 4000 --host 0.0.0.0

echo "LiteLLM proxy started. You can now run the DGM project."
echo "The proxy will route requests to your local Ollama server."
