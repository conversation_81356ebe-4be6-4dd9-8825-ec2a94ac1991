+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=2
++ CONDA_SHLVL=2
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_1=/opt/miniconda3
++ CONDA_PREFIX_1=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ cd /testbed
+ git config --global --add safe.directory /testbed
+ cd /testbed
+ git status
On branch master
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   tox.ini

Untracked files:
  (use "git add <file>..." to include in what will be committed)
	setup.py.orig

no changes added to commit (use "git add" and/or "git commit -a")
+ git show
commit aca3f825f2e4a8817190f3c885a242a285aa0dba
Merge: e9e4aa8cf a6dcbc7b6
Author: Takeshi KOMIYA <<EMAIL>>
Date:   Fri Apr 10 01:13:03 2020 +0900

    Merge pull request #7442 from tk0miya/7435_typehints_not_suppressed_for_class
    
    Fix #7435: autodoc_typehints doesn't suppress typehints for classes/methods

diff --cc CHANGES
index 0ac1502c5,c0370d82d..e0923b433
--- a/CHANGES
+++ b/CHANGES
@@@ -22,9 -22,9 +22,11 @@@ Bugs fixe
  * #7418: std domain: duplication warning for glossary terms is case insensitive
  * #7438: C++, fix merging overloaded functions in parallel builds.
  * #7422: autodoc: fails with ValueError when using autodoc_mock_imports
+ * #7435: autodoc: ``autodoc_typehints='description'`` doesn't suppress typehints
+   in signature for classes/methods
  * #7423: crashed when giving a non-string object to logger
 +* #7479: html theme: Do not include xmlns attribute with HTML 5 doctype
 +* #7426: html theme: Escape some links in HTML templates
  
  Testing
  --------
+ git diff aca3f825f2e4a8817190f3c885a242a285aa0dba
diff --git a/tox.ini b/tox.ini
index d9f040544..bf39854b6 100644
--- a/tox.ini
+++ b/tox.ini
@@ -28,7 +28,7 @@ extras =
 setenv =
     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils
 commands=
-    pytest --durations 25 {posargs}
+    pytest -rA --durations 25 {posargs}
 
 [testenv:flake8]
 basepython = python3
+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z x ']'
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1='(testbed) '
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=3
+++ CONDA_SHLVL=3
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=4
++ CONDA_SHLVL=4
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_3=/opt/miniconda3
++ CONDA_PREFIX_3=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ python -m pip install -e '.[test]'
Obtaining file:///testbed
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: sphinxcontrib-applehelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (1.0.4)
Requirement already satisfied: sphinxcontrib-devhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (1.0.2)
Requirement already satisfied: sphinxcontrib-jsmath in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (1.0.1)
Requirement already satisfied: sphinxcontrib-htmlhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (2.0.1)
Requirement already satisfied: sphinxcontrib-serializinghtml in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (1.1.5)
Requirement already satisfied: sphinxcontrib-qthelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (1.0.3)
Requirement already satisfied: Jinja2>=2.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (2.11.3)
Requirement already satisfied: Pygments>=2.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (2.19.1)
Requirement already satisfied: docutils>=0.12 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (0.21.2)
Requirement already satisfied: snowballstemmer>=1.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (2.2.0)
Requirement already satisfied: babel>=1.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (2.17.0)
Requirement already satisfied: alabaster<0.8,>=0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (0.7.11)
Requirement already satisfied: imagesize in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (1.4.1)
Requirement already satisfied: requests>=2.5.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (2.32.3)
Requirement already satisfied: setuptools in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (75.8.0)
Requirement already satisfied: packaging in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (24.2)
Requirement already satisfied: pytest in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (8.3.5)
Requirement already satisfied: pytest-cov in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (6.0.0)
Requirement already satisfied: html5lib in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (1.1)
Requirement already satisfied: typed_ast in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (1.5.5)
Requirement already satisfied: cython in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.0.1.dev20250315) (3.0.12)
Requirement already satisfied: MarkupSafe>=0.23 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Jinja2>=2.3->Sphinx==3.0.1.dev20250315) (2.0.1)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.0.1.dev20250315) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.0.1.dev20250315) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.0.1.dev20250315) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.0.1.dev20250315) (2025.1.31)
Requirement already satisfied: six>=1.9 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.0.1.dev20250315) (1.17.0)
Requirement already satisfied: webencodings in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.0.1.dev20250315) (0.5.1)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.0.1.dev20250315) (1.2.2)
Requirement already satisfied: iniconfig in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.0.1.dev20250315) (2.0.0)
Requirement already satisfied: pluggy<2,>=1.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.0.1.dev20250315) (1.5.0)
Requirement already satisfied: tomli>=1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.0.1.dev20250315) (2.2.1)
Requirement already satisfied: coverage>=7.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from coverage[toml]>=7.5->pytest-cov->Sphinx==3.0.1.dev20250315) (7.6.12)
Installing collected packages: Sphinx
  Attempting uninstall: Sphinx
    Found existing installation: Sphinx 3.0.1.dev20250313
    Uninstalling Sphinx-3.0.1.dev20250313:
      Successfully uninstalled Sphinx-3.0.1.dev20250313
  DEPRECATION: Legacy editable install of Sphinx[test]==3.0.1.dev20250315 from file:///testbed (setup.py develop) is deprecated. pip 25.1 will enforce this behaviour change. A possible replacement is to add a pyproject.toml or enable --use-pep517, and use setuptools >= 64. If the resulting installation is not behaving as expected, try using --config-settings editable_mode=compat. Please consult the setuptools documentation for more information. Discussion can be found at https://github.com/pypa/pip/issues/11457
  Running setup.py develop for Sphinx
Successfully installed Sphinx
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
+ git checkout aca3f825f2e4a8817190f3c885a242a285aa0dba tests/test_domain_py.py
Updated 0 paths from 20b75ba95
+ git apply -v -
Checking patch tests/test_domain_py.py...
Applied patch tests/test_domain_py.py cleanly.
+ tox --current-env -epy39 -v -- tests/test_domain_py.py
py39: commands[0]> pytest -rA --durations 25 tests/test_domain_py.py
============================= test session starts ==============================
platform linux -- Python 3.9.21, pytest-8.3.5, pluggy-1.5.0
cachedir: .tox/py39/.pytest_cache
libraries: Sphinx-3.0.1+/aca3f825f, docutils-0.21.2
base tempdir: /tmp/pytest-of-root/pytest-0
rootdir: /testbed
configfile: setup.cfg
plugins: cov-6.0.0
collected 28 items

tests/test_domain_py.py ......F.....................                     [100%]

=================================== FAILURES ===================================
____________________________ test_parse_annotation _____________________________

    def test_parse_annotation():
        doctree = _parse_annotation("int")
        assert_node(doctree, ([pending_xref, "int"],))
        assert_node(doctree[0], pending_xref, refdomain="py", reftype="class", reftarget="int")
    
        doctree = _parse_annotation("List[int]")
        assert_node(doctree, ([pending_xref, "List"],
                              [desc_sig_punctuation, "["],
                              [pending_xref, "int"],
                              [desc_sig_punctuation, "]"]))
    
        doctree = _parse_annotation("Tuple[int, int]")
        assert_node(doctree, ([pending_xref, "Tuple"],
                              [desc_sig_punctuation, "["],
                              [pending_xref, "int"],
                              [desc_sig_punctuation, ", "],
                              [pending_xref, "int"],
                              [desc_sig_punctuation, "]"]))
    
        doctree = _parse_annotation("Callable[[int, int], int]")
        assert_node(doctree, ([pending_xref, "Callable"],
                              [desc_sig_punctuation, "["],
                              [desc_sig_punctuation, "["],
                              [pending_xref, "int"],
                              [desc_sig_punctuation, ", "],
                              [pending_xref, "int"],
                              [desc_sig_punctuation, "]"],
                              [desc_sig_punctuation, ", "],
                              [pending_xref, "int"],
                              [desc_sig_punctuation, "]"]))
    
        # None type makes an object-reference (not a class reference)
        doctree = _parse_annotation("None")
        assert_node(doctree, ([pending_xref, "None"],))
>       assert_node(doctree[0], pending_xref, refdomain="py", reftype="obj", reftarget="None")

tests/test_domain_py.py:273: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

node = <pending_xref: <#text: 'None'>>
cls = <class 'sphinx.addnodes.pending_xref'>, xpath = ''
kwargs = {'refdomain': 'py', 'reftarget': 'None', 'reftype': 'obj'}
key = 'reftype', value = 'obj'

    def assert_node(node: Node, cls: Any = None, xpath: str = "", **kwargs: Any) -> None:
        if cls:
            if isinstance(cls, list):
                assert_node(node, cls[0], xpath=xpath, **kwargs)
                if cls[1:]:
                    if isinstance(cls[1], tuple):
                        assert_node(node, cls[1], xpath=xpath, **kwargs)
                    else:
                        assert isinstance(node, nodes.Element), \
                            'The node%s does not have any children' % xpath
                        assert len(node) == 1, \
                            'The node%s has %d child nodes, not one' % (xpath, len(node))
                        assert_node(node[0], cls[1:], xpath=xpath + "[0]", **kwargs)
            elif isinstance(cls, tuple):
                assert isinstance(node, (list, nodes.Element)), \
                    'The node%s does not have any items' % xpath
                assert len(node) == len(cls), \
                    'The node%s has %d child nodes, not %r' % (xpath, len(node), len(cls))
                for i, nodecls in enumerate(cls):
                    path = xpath + "[%d]" % i
                    assert_node(node[i], nodecls, xpath=path, **kwargs)
            elif isinstance(cls, str):
                assert node == cls, 'The node %r is not %r: %r' % (xpath, cls, node)
            else:
                assert isinstance(node, cls), \
                    'The node%s is not subclass of %r: %r' % (xpath, cls, node)
    
        if kwargs:
            assert isinstance(node, nodes.Element), \
                'The node%s does not have any attributes' % xpath
    
            for key, value in kwargs.items():
                assert key in node, \
                    'The node%s does not have %r attribute: %r' % (xpath, key, node)
>               assert node[key] == value, \
                    'The node%s[%s] is not %r: %r' % (xpath, key, value, node[key])
E               AssertionError: The node[reftype] is not 'obj': 'class'

sphinx/testing/util.py:86: AssertionError
=============================== warnings summary ===============================
sphinx/util/docutils.py:45
  /testbed/sphinx/util/docutils.py:45: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
    __version_info__ = tuple(LooseVersion(docutils.__version__).version)

sphinx/registry.py:22
  /testbed/sphinx/registry.py:22: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
    from pkg_resources import iter_entry_points

../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('sphinxcontrib')`.
  Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
    declare_namespace(pkg)

sphinx/directives/patches.py:15
  /testbed/sphinx/directives/patches.py:15: DeprecationWarning: The `docutils.parsers.rst.directive.html` module will be removed in Docutils 2.0. Since Docutils 0.18, the "Meta" node is defined in `docutils.parsers.rst.directives.misc`.
    from docutils.parsers.rst.directives import images, html, tables

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/__init__.py:210: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse():  # type: Node

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/i18n.py:88: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.translatable):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/__init__.py:110: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for ref in self.document.traverse(nodes.substitution_reference):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/__init__.py:131: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.target):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/__init__.py:150: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.block_quote):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/__init__.py:175: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.Element):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/__init__.py:222: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.index):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/references.py:30: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.substitution_definition):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/__init__.py:189: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.section):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/__init__.py:279: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.doctest_block):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/domains/citation.py:117: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.citation):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/domains/citation.py:136: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.citation_reference):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/builders/latex/transforms.py:37: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: nodes.Element

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/__init__.py:291: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: Element

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/util/compat.py:44: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.index):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/domains/index.py:52: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in document.traverse(addnodes.index):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/domains/math.py:85: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    self.data['has_equations'][docname] = any(document.traverse(math_node))

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/environment/collectors/asset.py:47: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/environment/collectors/asset.py:124: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(addnodes.download_reference):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/environment/collectors/title.py:46: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.section):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/__init__.py:301: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.system_message):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/__init__.py:384: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.manpage):

tests/test_domain_py.py: 28 warnings
  /testbed/sphinx/transforms/i18n.py:484: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for inline in self.document.traverse(matcher):  # type: nodes.inline

tests/test_domain_py.py: 16 warnings
  /testbed/sphinx/domains/cpp.py:6895: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(AliasNode):

tests/test_domain_py.py: 16 warnings
  /testbed/sphinx/transforms/post_transforms/__init__.py:71: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.pending_xref):

tests/test_domain_py.py: 42 warnings
  /testbed/sphinx/util/nodes.py:596: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in document.traverse(addnodes.only):

tests/test_domain_py.py: 32 warnings
  /testbed/sphinx/transforms/post_transforms/images.py:36: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.image):

tests/test_domain_py.py: 16 warnings
  /testbed/sphinx/transforms/post_transforms/__init__.py:214: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.desc_sig_element):

tests/test_domain_py.py: 16 warnings
  /testbed/sphinx/builders/latex/transforms.py:595: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.title):

tests/test_domain_py.py: 16 warnings
  /testbed/sphinx/builders/latex/transforms.py:597: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for i, index in enumerate(node.traverse(addnodes.index)):

tests/test_domain_py.py: 16 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:44: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.highlightlang):

tests/test_domain_py.py: 16 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:99: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for lbnode in self.document.traverse(nodes.literal_block):  # type: nodes.literal_block

tests/test_domain_py.py: 16 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:103: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for dbnode in self.document.traverse(nodes.doctest_block):  # type: nodes.doctest_block

tests/test_domain_py.py: 16 warnings
  /testbed/sphinx/environment/__init__.py:541: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for toctreenode in doctree.traverse(addnodes.toctree):

tests/test_domain_py.py: 22 warnings
  /testbed/sphinx/environment/adapters/toctree.py:204: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for subtocnode in toc.traverse(addnodes.toctree):

tests/test_domain_py.py: 11 warnings
  /testbed/sphinx/environment/adapters/toctree.py:262: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for refnode in newnode.traverse(nodes.reference):

tests/test_domain_py.py: 12 warnings
  /testbed/sphinx/util/nodes.py:348: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for img in node.traverse(nodes.image):

tests/test_domain_py.py: 12 warnings
  /testbed/sphinx/util/nodes.py:350: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for raw in node.traverse(nodes.raw):

tests/test_domain_py.py::test_domain_py_xrefs
  /testbed/tests/test_domain_py.py:81: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    refnodes = list(doctree.traverse(pending_xref))

tests/test_domain_py.py::test_domain_py_xrefs
  /testbed/tests/test_domain_py.py:99: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    refnodes = list(doctree.traverse(pending_xref))

tests/test_domain_py.py::test_domain_py_xrefs
  /testbed/tests/test_domain_py.py:128: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    refnodes = list(doctree.traverse(pending_xref))

tests/test_domain_py.py::test_resolve_xref_for_properties
  /testbed/sphinx/builders/html/__init__.py:415: DeprecationWarning: The frontend.OptionParser class will be replaced by a subclass of argparse.ArgumentParser in Docutils 0.21 or later.
    self.docsettings = OptionParser(

tests/test_domain_py.py: 72 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/optparse.py:1000: DeprecationWarning: The frontend.Option class will be removed in Docutils 0.21 or later.
    option = self.option_class(*args, **kwargs)

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /testbed/sphinx/builders/__init__.py:182: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /testbed/sphinx/builders/html/__init__.py:827: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /testbed/sphinx/environment/adapters/toctree.py:313: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in toc.traverse(nodes.reference):

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:114: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.
    _gaq.push(['_setAllowLinker', true]);

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/about.html:70: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/about.html:99: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /testbed/sphinx/environment/adapters/toctree.py:327: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for toctreenode in doctree.traverse(addnodes.toctree):

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:215: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:238: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
  <template>:33: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
  <template>:224: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
  <template>:386: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
  <template>:401: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py: 24 warnings
  /testbed/sphinx/ext/todo.py:98: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for todo in document.traverse(todo_node):

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
==================================== PASSES ====================================
_____________________________ test_domain_py_xrefs _____________________________
----------------------------- Captured stdout call -----------------------------
[<pending_xref: <literal...>>, <pending_xref: <literal...>>]
<pending_xref py:class="B" py:module="test.extra" refdoc="module_option" refdomain="py" refexplicit="False" reftarget="foo" reftype="meth" refwarn="False"><literal classes="xref py py-meth">foo()</literal></pending_xref>
<pending_xref py:class="B" py:module="test.extra" refdoc="module_option" refdomain="py" refexplicit="False" reftarget="foo" reftype="meth" refwarn="False"><literal classes="xref py py-meth">foo()</literal></pending_xref>
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: dummy
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/dummy
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m
[01mbuilding [dummy]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 4 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[ 25%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 50%] [35mmodule[39;49;00m                                               
[01mreading sources... [39;49;00m[ 75%] [35mmodule_option[39;49;00m                                        
[01mreading sources... [39;49;00m[100%] [35mroles[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 25%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 50%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 75%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-py/module_option.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-py/module.rst:49: WARNING: more than one target found for cross-reference 'ModTopLevel': module_a.submodule.ModTopLevel, module_b.submodule.ModTopLevel[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: dummy
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/dummy
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [dummy]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 25%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 50%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 75%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-py/module.rst:49: WARNING: more than one target found for cross-reference 'ModTopLevel': module_a.submodule.ModTopLevel, module_b.submodule.ModTopLevel[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 25%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 50%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 75%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 
[01mgenerating indices... [39;49;00m genindex py-modindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-py/module.rst:49: WARNING: more than one target found for cross-reference 'ModTopLevel': module_a.submodule.ModTopLevel, module_b.submodule.ModTopLevel[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: dummy
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/dummy
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [dummy]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 25%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 50%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 75%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-py/module.rst:49: WARNING: more than one target found for cross-reference 'ModTopLevel': module_a.submodule.ModTopLevel, module_b.submodule.ModTopLevel[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:1: WARNING: duplicate object description of hello, other instance in index, use :noindex: for one of them[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:1: WARNING: duplicate object description of hello, other instance in index, use :noindex: for one of them[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:1: WARNING: duplicate object description of hello, other instance in index, use :noindex: for one of them[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.0.1+/aca3f825f[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

============================= slowest 25 durations =============================
0.26s setup    tests/test_domain_py.py::test_domain_py_xrefs
0.16s call     tests/test_domain_py.py::test_resolve_xref_for_properties
0.07s call     tests/test_domain_py.py::test_domain_py_xrefs
0.04s setup    tests/test_domain_py.py::test_pyfunction_signature
0.03s call     tests/test_domain_py.py::test_pyfunction_signature_full
0.01s call     tests/test_domain_py.py::test_pyfunction_signature_full_py38
0.01s setup    tests/test_domain_py.py::test_resolve_xref_for_properties
0.01s setup    tests/test_domain_py.py::test_pyobject_prefix
0.01s setup    tests/test_domain_py.py::test_pystaticmethod
0.01s setup    tests/test_domain_py.py::test_exceptions_module_is_ignored
0.01s setup    tests/test_domain_py.py::test_pyexception_signature
0.01s setup    tests/test_domain_py.py::test_pydata_signature
0.01s setup    tests/test_domain_py.py::test_pyfunction_signature_full
0.01s setup    tests/test_domain_py.py::test_optional_pyfunction_signature
0.01s setup    tests/test_domain_py.py::test_pyattribute
0.01s setup    tests/test_domain_py.py::test_pyfunction_signature_full_py38
0.01s setup    tests/test_domain_py.py::test_pydata
0.01s setup    tests/test_domain_py.py::test_pydata_signature_old
0.01s setup    tests/test_domain_py.py::test_pyclassmethod
0.01s setup    tests/test_domain_py.py::test_pymethod_options
0.01s setup    tests/test_domain_py.py::test_pyfunction
0.01s setup    tests/test_domain_py.py::test_pydecorator_signature
0.01s setup    tests/test_domain_py.py::test_pydecoratormethod_signature
0.01s setup    tests/test_domain_py.py::test_module_index
0.01s setup    tests/test_domain_py.py::test_module_index_submodule
=========================== short test summary info ============================
PASSED tests/test_domain_py.py::test_function_signatures
PASSED tests/test_domain_py.py::test_domain_py_xrefs
PASSED tests/test_domain_py.py::test_domain_py_objects
PASSED tests/test_domain_py.py::test_resolve_xref_for_properties
PASSED tests/test_domain_py.py::test_domain_py_find_obj
PASSED tests/test_domain_py.py::test_get_full_qualified_name
PASSED tests/test_domain_py.py::test_pyfunction_signature
PASSED tests/test_domain_py.py::test_pyfunction_signature_full
PASSED tests/test_domain_py.py::test_pyfunction_signature_full_py38
PASSED tests/test_domain_py.py::test_optional_pyfunction_signature
PASSED tests/test_domain_py.py::test_pyexception_signature
PASSED tests/test_domain_py.py::test_exceptions_module_is_ignored
PASSED tests/test_domain_py.py::test_pydata_signature
PASSED tests/test_domain_py.py::test_pydata_signature_old
PASSED tests/test_domain_py.py::test_pyobject_prefix
PASSED tests/test_domain_py.py::test_pydata
PASSED tests/test_domain_py.py::test_pyfunction
PASSED tests/test_domain_py.py::test_pymethod_options
PASSED tests/test_domain_py.py::test_pyclassmethod
PASSED tests/test_domain_py.py::test_pystaticmethod
PASSED tests/test_domain_py.py::test_pyattribute
PASSED tests/test_domain_py.py::test_pydecorator_signature
PASSED tests/test_domain_py.py::test_pydecoratormethod_signature
PASSED tests/test_domain_py.py::test_module_index
PASSED tests/test_domain_py.py::test_module_index_submodule
PASSED tests/test_domain_py.py::test_module_index_not_collapsed
PASSED tests/test_domain_py.py::test_modindex_common_prefix
FAILED tests/test_domain_py.py::test_parse_annotation - AssertionError: The n...
================= 1 failed, 27 passed, 1084 warnings in 1.11s ==================
py39: exit 1 (1.63 seconds) /testbed> pytest -rA --durations 25 tests/test_domain_py.py pid=107
  py39: FAIL code 1 (1.64=setup[0.01]+cmd[1.63] seconds)
  evaluation failed :( (1.74 seconds)
+ git checkout aca3f825f2e4a8817190f3c885a242a285aa0dba tests/test_domain_py.py
Updated 1 path from 20b75ba95
