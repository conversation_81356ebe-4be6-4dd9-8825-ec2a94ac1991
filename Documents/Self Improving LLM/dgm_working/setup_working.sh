#!/bin/bash

# Setup script for DGM Working Version
# This script sets up the working Darwin Gödel Machine environment

set -e

echo "🚀 Setting up Darwin Gödel Machine - Working Version"
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "DGM_outer.py" ]; then
    echo "❌ Error: Please run this script from the dgm_working directory"
    exit 1
fi

# Check Python version
echo "🐍 Checking Python version..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Error: Python 3 is required"
    exit 1
fi

# Check if Ollama is running
echo "🤖 Checking Ollama..."
if ! command -v ollama &> /dev/null; then
    echo "❌ Error: Ollama is not installed or not in PATH"
    echo "   Please install Ollama first: https://ollama.ai"
    exit 1
fi

# Check if qwen3:30b-a3b model is available
echo "🔍 Checking for qwen3:30b-a3b model..."
if ! ollama list | grep -q "qwen3:30b-a3b"; then
    echo "❌ Error: qwen3:30b-a3b model not found"
    echo "   Please install it with: ollama pull qwen3:30b-a3b"
    exit 1
fi

# Check Docker
echo "🐳 Checking Docker..."
if ! command -v docker &> /dev/null; then
    echo "❌ Error: Docker is not installed"
    echo "   Please install Docker first"
    exit 1
fi

# Test Docker permissions
if ! docker run hello-world &> /dev/null; then
    echo "❌ Error: Docker permission issue"
    echo "   Please add your user to the docker group:"
    echo "   sudo usermod -aG docker $USER"
    echo "   Then log out and log back in"
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install requirements
echo "📚 Installing Python dependencies..."
pip install -r requirements.txt

# Test Ollama connection
echo "🧪 Testing Ollama connection..."
if ! curl -s http://localhost:11434/api/tags | grep -q "qwen3:30b-a3b"; then
    echo "⚠️  Warning: Could not connect to Ollama or model not found"
    echo "   Make sure Ollama is running: ollama serve"
    echo "   And the model is installed: ollama pull qwen3:30b-a3b"
fi

# Create output directory
echo "📁 Creating output directory..."
mkdir -p output_dgm

# Set up git (required for DGM)
echo "🔧 Checking git configuration..."
if [ -z "$(git config --global user.name)" ]; then
    echo "⚠️  Git user.name not set. Setting default..."
    git config --global user.name "DGM User"
fi

if [ -z "$(git config --global user.email)" ]; then
    echo "⚠️  Git user.email not set. Setting default..."
    git config --global user.email "<EMAIL>"
fi

echo ""
echo "✅ Setup complete!"
echo ""
echo "🎯 Quick Start:"
echo "   source venv/bin/activate"
echo "   python DGM_outer.py --max_generation 10 --selfimprove_size 2"
echo ""
echo "📊 Monitor progress:"
echo "   tail -f output_dgm/*/dgm_outer.log"
echo ""
echo "🎉 Ready to run Darwin Gödel Machine with qwen3:30b-a3b!"
