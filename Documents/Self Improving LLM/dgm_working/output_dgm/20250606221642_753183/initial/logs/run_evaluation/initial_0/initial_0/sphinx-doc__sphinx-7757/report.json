{"sphinx-doc__sphinx-7757": {"patch_is_None": false, "patch_exists": true, "patch_successfully_applied": true, "resolved": false, "tests_status": {"FAIL_TO_PASS": {"success": [], "failure": ["tests/test_util_inspect.py::test_signature_from_str_positionaly_only_args"]}, "PASS_TO_PASS": {"success": ["tests/test_util_inspect.py::test_signature", "tests/test_util_inspect.py::test_signature_partial", "tests/test_util_inspect.py::test_signature_methods", "tests/test_util_inspect.py::test_signature_partialmethod", "tests/test_util_inspect.py::test_signature_annotations", "tests/test_util_inspect.py::test_signature_annotations_py38", "tests/test_util_inspect.py::test_signature_from_str_basic", "tests/test_util_inspect.py::test_signature_from_str_default_values", "tests/test_util_inspect.py::test_signature_from_str_annotations", "tests/test_util_inspect.py::test_signature_from_str_complex_annotations", "tests/test_util_inspect.py::test_signature_from_str_kwon<PERSON>_args", "tests/test_util_inspect.py::test_signature_from_str_invalid", "tests/test_util_inspect.py::test_safe_getattr_with_default", "tests/test_util_inspect.py::test_safe_getattr_with_exception", "tests/test_util_inspect.py::test_safe_getattr_with_property_exception", "tests/test_util_inspect.py::test_safe_getattr_with___dict___override", "tests/test_util_inspect.py::test_dictionary_sorting", "tests/test_util_inspect.py::test_set_sorting", "tests/test_util_inspect.py::test_set_sorting_fallback", "tests/test_util_inspect.py::test_frozenset_sorting", "tests/test_util_inspect.py::test_frozenset_sorting_fallback", "tests/test_util_inspect.py::test_dict_customtype", "tests/test_util_inspect.py::test_isclassmethod", "tests/test_util_inspect.py::test_isstaticmethod", "tests/test_util_inspect.py::test_iscoroutinefunction", "tests/test_util_inspect.py::test_isfunction", "tests/test_util_inspect.py::test_isbuiltin", "tests/test_util_inspect.py::test_isdescriptor", "tests/test_util_inspect.py::test_isattributedescriptor", "tests/test_util_inspect.py::test_isproperty", "tests/test_util_inspect.py::test_unpartial", "tests/test_util_inspect.py::test_getdoc_inherited_decorated_method", "tests/test_util_inspect.py::test_is_builtin_class_method"], "failure": []}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}}}