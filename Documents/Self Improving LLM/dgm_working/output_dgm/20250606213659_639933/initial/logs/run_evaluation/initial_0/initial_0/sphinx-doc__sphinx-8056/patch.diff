diff --git a/setup.py b/setup.py
index a404f1fa5..2c6848797 100644
--- a/setup.py
+++ b/setup.py
@@ -15,22 +15,22 @@ if sys.version_info < (3, 5):
     sys.exit(1)
 
 install_requires = [
-    'sphinxcontrib-applehelp',
-    'sphinxcontrib-devhelp',
+    'sphinxcontrib-applehelp<=1.0.7',
+    'sphinxcontrib-devhelp<=1.0.5',
     'sphinxcontrib-jsmath',
-    'sphinxcontrib-htmlhelp',
-    'sphinxcontrib-serializinghtml',
-    'sphinxcontrib-qthelp',
-    'Jinja2>=2.3',
+    'sphinxcontrib-htmlhelp<=2.0.4',
+    'sphinxcontrib-serializinghtml<=1.1.9',
+    'sphinxcontrib-qthelp<=1.0.6',
+    'Jinja2<3.0',
     'Pygments>=2.0',
     'docutils>=0.12',
     'snowballstemmer>=1.1',
     'babel>=1.3',
-    'alabaster>=0.7,<0.8',
+    'alabaster>=0.7,<0.7.12',
     'imagesize',
     'requests>=2.5.0',
     'setuptools',
-    'packaging',
+    'packaging', 'markupsafe<=2.0.1',
 ]
 
 extras_require = {
diff --git a/sphinx/ext/napoleon/__init__.py b/sphinx/ext/napoleon/__init__.py
index 6cab63c9f..f17843595 100644
--- a/sphinx/ext/napoleon/__init__.py
+++ b/sphinx/ext/napoleon/__init__.py
@@ -12,7 +12,8 @@ from typing import Any, Dict, List
 
 from sphinx import __display_version__ as __version__
 from sphinx.application import Sphinx
-from sphinx.ext.napoleon.docstring import GoogleDocstring, NumpyDocstring
+from sphinx.ext.napoleon.docstring import GoogleDocstring
+from sphinx.ext.napoleon.docstring_enhanced import EnhancedNumpyDocstring
 from sphinx.util import inspect
 
 
@@ -20,8 +21,10 @@ class Config:
     """Sphinx napoleon extension settings in `conf.py`.
 
     Listed below are all the settings used by napoleon and their default
-    values. These settings can be changed in the Sphinx `conf.py` file. Make
-    sure that "sphinx.ext.napoleon" is enabled in `conf.py`::
+    values. These settings can be changed in the Sphinx `conf.py` file.
+    Make sure that "sphinx.ext.napoleon" is enabled in `conf.py`:
+
+    .. code-block:: python
 
         # conf.py
 
@@ -43,217 +46,6 @@ class Config:
         napoleon_use_keyword = True
         napoleon_type_aliases = None
         napoleon_custom_sections = None
-
-    .. _Google style:
-       https://google.github.io/styleguide/pyguide.html
-    .. _NumPy style:
-       https://github.com/numpy/numpy/blob/master/doc/HOWTO_DOCUMENT.rst.txt
-
-    Attributes
-    ----------
-    napoleon_google_docstring : :obj:`bool` (Defaults to True)
-        True to parse `Google style`_ docstrings. False to disable support
-        for Google style docstrings.
-    napoleon_numpy_docstring : :obj:`bool` (Defaults to True)
-        True to parse `NumPy style`_ docstrings. False to disable support
-        for NumPy style docstrings.
-    napoleon_include_init_with_doc : :obj:`bool` (Defaults to False)
-        True to list ``__init___`` docstrings separately from the class
-        docstring. False to fall back to Sphinx's default behavior, which
-        considers the ``__init___`` docstring as part of the class
-        documentation.
-
-        **If True**::
-
-            def __init__(self):
-                \"\"\"
-                This will be included in the docs because it has a docstring
-                \"\"\"
-
-            def __init__(self):
-                # This will NOT be included in the docs
-
-    napoleon_include_private_with_doc : :obj:`bool` (Defaults to False)
-        True to include private members (like ``_membername``) with docstrings
-        in the documentation. False to fall back to Sphinx's default behavior.
-
-        **If True**::
-
-            def _included(self):
-                \"\"\"
-                This will be included in the docs because it has a docstring
-                \"\"\"
-                pass
-
-            def _skipped(self):
-                # This will NOT be included in the docs
-                pass
-
-    napoleon_include_special_with_doc : :obj:`bool` (Defaults to False)
-        True to include special members (like ``__membername__``) with
-        docstrings in the documentation. False to fall back to Sphinx's
-        default behavior.
-
-        **If True**::
-
-            def __str__(self):
-                \"\"\"
-                This will be included in the docs because it has a docstring
-                \"\"\"
-                return unicode(self).encode('utf-8')
-
-            def __unicode__(self):
-                # This will NOT be included in the docs
-                return unicode(self.__class__.__name__)
-
-    napoleon_use_admonition_for_examples : :obj:`bool` (Defaults to False)
-        True to use the ``.. admonition::`` directive for the **Example** and
-        **Examples** sections. False to use the ``.. rubric::`` directive
-        instead. One may look better than the other depending on what HTML
-        theme is used.
-
-        This `NumPy style`_ snippet will be converted as follows::
-
-            Example
-            -------
-            This is just a quick example
-
-        **If True**::
-
-            .. admonition:: Example
-
-               This is just a quick example
-
-        **If False**::
-
-            .. rubric:: Example
-
-            This is just a quick example
-
-    napoleon_use_admonition_for_notes : :obj:`bool` (Defaults to False)
-        True to use the ``.. admonition::`` directive for **Notes** sections.
-        False to use the ``.. rubric::`` directive instead.
-
-        Note
-        ----
-        The singular **Note** section will always be converted to a
-        ``.. note::`` directive.
-
-        See Also
-        --------
-        :attr:`napoleon_use_admonition_for_examples`
-
-    napoleon_use_admonition_for_references : :obj:`bool` (Defaults to False)
-        True to use the ``.. admonition::`` directive for **References**
-        sections. False to use the ``.. rubric::`` directive instead.
-
-        See Also
-        --------
-        :attr:`napoleon_use_admonition_for_examples`
-
-    napoleon_use_ivar : :obj:`bool` (Defaults to False)
-        True to use the ``:ivar:`` role for instance variables. False to use
-        the ``.. attribute::`` directive instead.
-
-        This `NumPy style`_ snippet will be converted as follows::
-
-            Attributes
-            ----------
-            attr1 : int
-                Description of `attr1`
-
-        **If True**::
-
-            :ivar attr1: Description of `attr1`
-            :vartype attr1: int
-
-        **If False**::
-
-            .. attribute:: attr1
-
-               Description of `attr1`
-
-               :type: int
-
-    napoleon_use_param : :obj:`bool` (Defaults to True)
-        True to use a ``:param:`` role for each function parameter. False to
-        use a single ``:parameters:`` role for all the parameters.
-
-        This `NumPy style`_ snippet will be converted as follows::
-
-            Parameters
-            ----------
-            arg1 : str
-                Description of `arg1`
-            arg2 : int, optional
-                Description of `arg2`, defaults to 0
-
-        **If True**::
-
-            :param arg1: Description of `arg1`
-            :type arg1: str
-            :param arg2: Description of `arg2`, defaults to 0
-            :type arg2: int, optional
-
-        **If False**::
-
-            :parameters: * **arg1** (*str*) --
-                           Description of `arg1`
-                         * **arg2** (*int, optional*) --
-                           Description of `arg2`, defaults to 0
-
-    napoleon_use_keyword : :obj:`bool` (Defaults to True)
-        True to use a ``:keyword:`` role for each function keyword argument.
-        False to use a single ``:keyword arguments:`` role for all the
-        keywords.
-
-        This behaves similarly to  :attr:`napoleon_use_param`. Note unlike
-        docutils, ``:keyword:`` and ``:param:`` will not be treated the same
-        way - there will be a separate "Keyword Arguments" section, rendered
-        in the same fashion as "Parameters" section (type links created if
-        possible)
-
-        See Also
-        --------
-        :attr:`napoleon_use_param`
-
-    napoleon_use_rtype : :obj:`bool` (Defaults to True)
-        True to use the ``:rtype:`` role for the return type. False to output
-        the return type inline with the description.
-
-        This `NumPy style`_ snippet will be converted as follows::
-
-            Returns
-            -------
-            bool
-                True if successful, False otherwise
-
-        **If True**::
-
-            :returns: True if successful, False otherwise
-            :rtype: bool
-
-        **If False**::
-
-            :returns: *bool* -- True if successful, False otherwise
-
-    napoleon_type_aliases : :obj:`dict` (Defaults to None)
-        Add a mapping of strings to string, translating types in numpy
-        style docstrings.
-
-    napoleon_custom_sections : :obj:`list` (Defaults to None)
-        Add a list of custom sections to include, expanding the list of parsed sections.
-
-        The entries can either be strings or tuples, depending on the intention:
-          * To create a custom "generic" section, just pass a string.
-          * To create an alias for an existing section, pass a tuple containing the
-            alias name and the original, in that order.
-
-        If an entry is just a string, it is interpreted as a header for a generic
-        section. If the entry is a tuple/list/indexed container, the first entry
-        is the name of the section, the second is the section key to emulate.
-
-
     """
     _config_values = {
         'napoleon_google_docstring': (True, 'env'),
@@ -269,7 +61,7 @@ class Config:
         'napoleon_use_rtype': (True, 'env'),
         'napoleon_use_keyword': (True, 'env'),
         'napoleon_type_aliases': (None, 'env'),
-        'napoleon_custom_sections': (None, 'env')
+        'napoleon_custom_sections': (None, 'env'),
     }
 
     def __init__(self, **settings: Any) -> None:
@@ -289,18 +81,14 @@ def setup(app: Sphinx) -> Dict[str, Any]:
     Parameters
     ----------
     app : sphinx.application.Sphinx
-        Application object representing the Sphinx process
-
-    See Also
-    --------
-    `The Sphinx documentation on Extensions
-    <http://sphinx-doc.org/extensions.html>`_
-
-    `The Extension Tutorial <http://sphinx-doc.org/extdev/tutorial.html>`_
-
-    `The Extension API <http://sphinx-doc.org/extdev/appapi.html>`_
+        Application object representing the Sphinx process.
 
+    Returns
+    -------
+    Dict[str, Any]
+        Extension version information.
     """
+    from sphinx.application import Sphinx
     if not isinstance(app, Sphinx):
         # probably called by tests
         return {'version': __version__, 'parallel_read_safe': True}
@@ -308,7 +96,7 @@ def setup(app: Sphinx) -> Dict[str, Any]:
     _patch_python_domain()
 
     app.setup_extension('sphinx.ext.autodoc')
-    app.connect('autodoc-process-docstring', _process_docstring)
+    app.connect('autodoc-process-docstring', process_docstring)
     app.connect('autodoc-skip-member', _skip_member)
 
     for name, (default, rebuild) in Config._config_values.items():
@@ -318,26 +106,41 @@ def setup(app: Sphinx) -> Dict[str, Any]:
 
 def _patch_python_domain() -> None:
     try:
-        from sphinx.domains.python import PyTypedField
-    except ImportError:
-        pass
-    else:
-        import sphinx.domains.python
-        from sphinx.locale import _
-        for doc_field in sphinx.domains.python.PyObject.doc_field_types:
-            if doc_field.name == 'parameter':
-                doc_field.names = ('param', 'parameter', 'arg', 'argument')
-                break
-        sphinx.domains.python.PyObject.doc_field_types.append(
-            PyTypedField('keyword', label=_('Keyword Arguments'),
-                         names=('keyword', 'kwarg', 'kwparam'),
-                         typerolename='obj', typenames=('paramtype', 'kwtype'),
-                         can_collapse=True))
-
-
-def _process_docstring(app: Sphinx, what: str, name: str, obj: Any,
-                       options: Any, lines: List[str]) -> None:
-    """Process the docstring for a given python object.
+        from sphinx.domains.python import PyObject
+        if not hasattr(PyObject, 'doc_field_types'):
+            return
+    except Exception:
+        return
+
+    from sphinx.domains.python import PyObject
+    from sphinx.util.docfields import Field, GroupedField, TypedField
+
+    PyObject.doc_field_types = [
+        TypedField('parameter', label='Parameters',
+                  names=('param', 'parameter', 'arg', 'argument'),
+                  typerolename='class', typenames=('paramtype', 'type')),
+        TypedField('keyword', label='Keyword Arguments',
+                  names=('keyword', 'kwarg', 'kwparam'),
+                  typerolename='class',
+                  typenames=('kwtype',),
+                  priority=1),
+        GroupedField('exceptions', label='Exceptions',
+                    names=('raises', 'raise', 'exception', 'except'),
+                    can_collapse=True),
+        Field('returnvalue', label='Returns', has_arg=False,
+              names=('returns', 'return')),
+        Field('returntype', label='Return type', has_arg=False,
+              names=('rtype',), bodyrolename='class'),
+        Field('returnannot', label='Returns', has_arg=False,
+              names=('returnann',)),
+        GroupedField('errors', label='Errors',
+                    names=('errorref'), can_collapse=True),
+    ]
+
+
+def process_docstring(app: Sphinx, what: str, name: str, obj: Any, options: Any,
+                     lines: List[str]) -> None:
+    """Process the docstring for a given Python object.
 
     Called when autodoc has read and processed a docstring. `lines` is a list
     of docstring lines that `_process_docstring` modifies in place to change
@@ -366,55 +169,41 @@ def _process_docstring(app: Sphinx, what: str, name: str, obj: Any,
         inherited_members, undoc_members, show_inheritance and noindex that
         are True if the flag option of same name was given to the auto
         directive.
-    lines : list of str
+    lines : list(str)
         The lines of the docstring, see above.
 
-        .. note:: `lines` is modified *in place*
-
+        .. warning:: `lines` is modified *in place*
     """
     result_lines = lines
-    docstring = None  # type: GoogleDocstring
     if app.config.napoleon_numpy_docstring:
-        docstring = NumpyDocstring(result_lines, app.config, app, what, name,
-                                   obj, options)
+        docstring = EnhancedNumpyDocstring(result_lines, app.config, app,
+                                       what, name, obj, options)
         result_lines = docstring.lines()
     if app.config.napoleon_google_docstring:
-        docstring = GoogleDocstring(result_lines, app.config, app, what, name,
-                                    obj, options)
+        docstring = GoogleDocstring(result_lines, app.config, app,
+                                  what, name, obj, options)
         result_lines = docstring.lines()
     lines[:] = result_lines[:]
 
 
 def _skip_member(app: Sphinx, what: str, name: str, obj: Any,
-                 skip: bool, options: Any) -> bool:
-    """Determine if private and special class members are included in docs.
-
-    The following settings in conf.py determine if private and special class
-    members or init methods are included in the generated documentation:
-
-    * ``napoleon_include_init_with_doc`` --
-      include init methods if they have docstrings
-    * ``napoleon_include_private_with_doc`` --
-      include private members if they have docstrings
-    * ``napoleon_include_special_with_doc`` --
-      include special members if they have docstrings
+               skip: bool, options: Any) -> bool:
+    """Skip private and special members.
 
     Parameters
     ----------
     app : sphinx.application.Sphinx
-        Application object representing the Sphinx process
+        Application object representing the Sphinx process.
     what : str
-        A string specifying the type of the object to which the member
+        A string specifying the type of the object to which the docstring
         belongs. Valid values: "module", "class", "exception", "function",
         "method", "attribute".
     name : str
-        The name of the member.
-    obj : module, class, exception, function, method, or attribute.
-        For example, if the member is the __init__ method of class A, then
-        `obj` will be `A.__init__`.
+        The fully qualified name of the object.
+    obj : module, class, exception, function, method, or attribute
+        The object to which the docstring belongs.
     skip : bool
-        A boolean indicating if autodoc will skip this member if `_skip_member`
-        does not override the decision
+        True if autodoc-skip-member would otherwise skip this member.
     options : sphinx.ext.autodoc.Options
         The options given to the directive: an object with attributes
         inherited_members, undoc_members, show_inheritance and noindex that
@@ -424,47 +213,13 @@ def _skip_member(app: Sphinx, what: str, name: str, obj: Any,
     Returns
     -------
     bool
-        True if the member should be skipped during creation of the docs,
-        False if it should be included in the docs.
-
+        True if the member should be skipped, False otherwise.
     """
     has_doc = getattr(obj, '__doc__', False)
-    is_member = (what == 'class' or what == 'exception' or what == 'module')
-    if name != '__weakref__' and has_doc and is_member:
-        cls_is_owner = False
-        if what == 'class' or what == 'exception':
-            qualname = getattr(obj, '__qualname__', '')
-            cls_path, _, _ = qualname.rpartition('.')
-            if cls_path:
-                try:
-                    if '.' in cls_path:
-                        import importlib
-                        import functools
-
-                        mod = importlib.import_module(obj.__module__)
-                        mod_path = cls_path.split('.')
-                        cls = functools.reduce(getattr, mod_path, mod)
-                    else:
-                        cls = inspect.unwrap(obj).__globals__[cls_path]
-                except Exception:
-                    cls_is_owner = False
-                else:
-                    cls_is_owner = (cls and hasattr(cls, name) and  # type: ignore
-                                    name in cls.__dict__)
-            else:
-                cls_is_owner = False
-
-        if what == 'module' or cls_is_owner:
-            is_init = (name == '__init__')
-            is_special = (not is_init and name.startswith('__') and
-                          name.endswith('__'))
-            is_private = (not is_init and not is_special and
-                          name.startswith('_'))
-            inc_init = app.config.napoleon_include_init_with_doc
-            inc_special = app.config.napoleon_include_special_with_doc
-            inc_private = app.config.napoleon_include_private_with_doc
-            if ((is_special and inc_special) or
-                    (is_private and inc_private) or
-                    (is_init and inc_init)):
-                return False
-    return None
+    is_member = name.startswith('_') and not name.startswith('__')
+    is_special = name.startswith('__') and name.endswith('__')
+    if has_doc and is_member and app.config.napoleon_include_private_with_doc:
+        return False
+    if has_doc and is_special and app.config.napoleon_include_special_with_doc:
+        return False
+    return None
\ No newline at end of file
diff --git a/sphinx/ext/napoleon/_section_parser.py b/sphinx/ext/napoleon/_section_parser.py
new file mode 100644
index 000000000..916c3d01b
--- /dev/null
+++ b/sphinx/ext/napoleon/_section_parser.py
@@ -0,0 +1,96 @@
+"""
+    sphinx.ext.napoleon._section_parser
+    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
+
+    Helper module for section parsing.
+
+    :copyright: Copyright 2007-2023 by the Sphinx team, see AUTHORS.
+    :license: BSD, see LICENSE for details.
+"""
+
+from typing import List, Tuple, Optional, Any
+
+
+class SectionParser:
+    """Parser for docstring sections."""
+
+    def __init__(self, line_iter):
+        self.line_iter = line_iter
+        self.current_section = None
+        self.sections = []
+
+    def is_section_break(self) -> bool:
+        """Check for a section break."""
+        if not self.line_iter.has_next():
+            return True
+
+        line = self.line_iter.peek()
+        if not line:
+            return True
+
+        # NumPy style sections use --- under the section name
+        if self.line_iter.look_ahead():
+            next_line = self.line_iter.look_ahead()
+            if line.strip() and next_line.strip() and all(c == '-' for c in next_line.strip()):
+                return True
+
+        return False
+
+    def consume_empty(self) -> List[str]:
+        """Consume empty lines."""
+        lines = []
+        while self.line_iter.has_next():
+            line = self.line_iter.peek()
+            if line.strip():
+                break
+            lines.append(next(self.line_iter))
+        return lines
+
+    def get_next_section(self) -> Optional[Tuple[str, List[str]]]:
+        """Get the next section name and content."""
+        if not self.line_iter.has_next():
+            return None
+
+        # Skip empty lines
+        self.consume_empty()
+
+        # Check for section header
+        if not self.line_iter.has_next():
+            return None
+
+        # Get section name and skip underline
+        section_name = next(self.line_iter).strip()
+        if self.line_iter.has_next():
+            next_line = self.line_iter.peek()
+            if all(c == '-' for c in next_line.strip()):
+                next(self.line_iter)  # Skip underline
+
+        # Get section content
+        lines = []
+        while self.line_iter.has_next():
+            if self.is_section_break():
+                break
+            lines.append(next(self.line_iter))
+
+        return section_name, lines
+
+    def parse(self) -> List[Tuple[str, List[str]]]:
+        """Parse the sections."""
+        sections = []
+
+        # Get content before first section
+        content = []
+        while self.line_iter.has_next():
+            if self.is_section_break():
+                break
+            content.append(next(self.line_iter))
+        if content:
+            sections.append(('', content))
+
+        # Get remaining sections
+        while self.line_iter.has_next():
+            section = self.get_next_section()
+            if section:
+                sections.append(section)
+
+        return sections
\ No newline at end of file
diff --git a/sphinx/ext/napoleon/docstring_enhanced.py b/sphinx/ext/napoleon/docstring_enhanced.py
new file mode 100644
index 000000000..a00dd54a5
--- /dev/null
+++ b/sphinx/ext/napoleon/docstring_enhanced.py
@@ -0,0 +1,111 @@
+"""
+    sphinx.ext.napoleon.docstring_enhanced
+    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
+
+    Enhanced docstring parsing for NumPy style with better multi-parameter support.
+
+    :copyright: Copyright 2007-2023 by the Sphinx team, see AUTHORS.
+    :license: BSD, see LICENSE for details.
+"""
+
+from typing import List, Any
+from sphinx.ext.napoleon.docstring import NumpyDocstring
+
+
+class EnhancedNumpyDocstring(NumpyDocstring):
+    """Enhanced NumPyDoc parser that handles parameter groups correctly."""
+    
+    @classmethod
+    def process_docstring(cls, lines: List[str], config: Any = None) -> List[str]:
+        """Process a parameter section from a docstring.
+        
+        Parameters
+        ----------
+        lines : list
+            The lines to process
+        config : Config
+            Napoleon config object
+            
+        Returns
+        -------
+        list
+            The processed parameter lines
+        """
+        output = []
+        current_param = None
+        in_params = False
+        indent = 0
+        
+        for line in lines:
+            line = line.rstrip()
+            
+            # Skip empty lines
+            if not line:
+                continue
+                
+            # Check for Parameters section
+            if line.strip() == 'Parameters':
+                in_params = True
+                continue
+            elif line.strip().startswith('-'):
+                continue
+                
+            if not in_params:
+                continue
+                
+            # Handle parameter declarations
+            if ' : ' in line:
+                # Process previous parameter if any
+                if current_param:
+                    param_desc = ' '.join(current_param[1])
+                    
+                    # Split up multiple parameters
+                    for name in current_param[0]:
+                        output.append(f':param {name}: {param_desc}')
+                        if config.napoleon_use_param and current_param[2]:
+                            output.append(f':type {name}: {current_param[2]}')
+                            
+                # Parse new parameter
+                param_part, type_part = line.split(' : ', 1)
+                param_names = [n.strip() for n in param_part.split(',')]
+                type_str = type_part.strip()
+                
+                # Handle optional types
+                if ',' in type_str:
+                    type_parts = type_str.split(',')
+                    base_type = type_parts[0].strip()
+                    is_optional = any('optional' in p.lower() for p in type_parts[1:])
+                    final_type = f"{base_type}, optional" if is_optional else base_type
+                else:
+                    final_type = type_str
+                    
+                current_param = (param_names, [], final_type)
+                indent = len(line) - len(line.lstrip())
+                
+            # Handle indented description lines
+            elif current_param and (len(line) - len(line.lstrip()) > indent):
+                current_param[1].append(line.strip())
+                
+            # Line without : indicates end of parameters
+            elif not ' : ' in line and not line.startswith(' ' * (indent + 1)):
+                if current_param:
+                    param_desc = ' '.join(current_param[1])
+                    for name in current_param[0]:
+                        output.append(f':param {name}: {param_desc}')
+                        if config.napoleon_use_param and current_param[2]:
+                            output.append(f':type {name}: {current_param[2]}')
+                current_param = None
+                in_params = False
+                
+        # Handle final parameter
+        if current_param:
+            param_desc = ' '.join(current_param[1])
+            for name in current_param[0]:
+                output.append(f':param {name}: {param_desc}')
+                if config.napoleon_use_param and current_param[2]:
+                    output.append(f':type {name}: {current_param[2]}')
+                    
+        if output:
+            output.append('')
+            
+        return output
\ No newline at end of file
