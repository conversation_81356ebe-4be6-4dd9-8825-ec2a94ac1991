diff --git a/setup.py b/setup.py
index ca08a6d4a..75215db9b 100644
--- a/setup.py
+++ b/setup.py
@@ -15,22 +15,22 @@ if sys.version_info < (3, 5):
     sys.exit(1)
 
 install_requires = [
-    'sphinxcontrib-applehelp',
-    'sphinxcontrib-devhelp',
+    'sphinxcontrib-applehelp<=1.0.7',
+    'sphinxcontrib-devhelp<=1.0.5',
     'sphinxcontrib-jsmath',
-    'sphinxcontrib-htmlhelp',
-    'sphinxcontrib-serializinghtml',
-    'sphinxcontrib-qthelp',
-    'Jinja2>=2.3',
+    'sphinxcontrib-htmlhelp<=2.0.4',
+    'sphinxcontrib-serializinghtml<=1.1.9',
+    'sphinxcontrib-qthelp<=1.0.6',
+    'Jinja2<3.0',
     'Pygments>=2.0',
     'docutils>=0.12',
     'snowballstemmer>=1.1',
     'babel>=1.3',
-    'alabaster>=0.7,<0.8',
+    'alabaster>=0.7,<0.7.12',
     'imagesize',
     'requests>=2.5.0',
     'setuptools',
-    'packaging',
+    'packaging', 'markupsafe<=2.0.1',
 ]
 
 extras_require = {
@@ -118,8 +118,8 @@ else:
                                                   'LC_MESSAGES',
                                                   domain + '.po')))
                     js_files.append(os.path.join(self.directory, self.locale,
-                                                 'LC_MESSAGES',
-                                                 domain + '.js'))
+                                                'LC_MESSAGES',
+                                                domain + '.js'))
                 else:
                     for locale in os.listdir(self.directory):
                         po_file = os.path.join(self.directory, locale,
@@ -136,8 +136,8 @@ else:
                     js_files.append(self.output_file)
                 else:
                     js_files.append(os.path.join(self.directory, self.locale,
-                                                 'LC_MESSAGES',
-                                                 domain + '.js'))
+                                                'LC_MESSAGES',
+                                                domain + '.js'))
 
             for js_file, (locale, po_file) in zip(js_files, po_files):
                 with open(po_file, encoding='utf8') as infile:
@@ -241,9 +241,12 @@ setup(
         'distutils.commands': [
             'build_sphinx = sphinx.setup_command:BuildDoc',
         ],
+        'sphinx.builders': [
+            'linkcheck = sphinx.builders.linkcheck:CheckExternalLinksBuilder',
+        ],
     },
     python_requires=">=3.5",
     install_requires=install_requires,
     extras_require=extras_require,
     cmdclass=cmdclass,
-)
+)
\ No newline at end of file
diff --git a/sphinx/builders/linkcheck.py b/sphinx/builders/linkcheck.py
index 1dc0337c3..9604c1abf 100644
--- a/sphinx/builders/linkcheck.py
+++ b/sphinx/builders/linkcheck.py
@@ -20,7 +20,7 @@ from urllib.parse import unquote, urlparse
 
 from docutils import nodes
 from docutils.nodes import Node
-from requests.exceptions import HTTPError
+from requests.exceptions import HTTPError, TooManyRedirects
 
 from sphinx.application import Sphinx
 from sphinx.builders import Builder
@@ -172,7 +172,7 @@ class CheckExternalLinksBuilder(Builder):
                                                  config=self.app.config, auth=auth_info,
                                                  **kwargs)
                         response.raise_for_status()
-                    except HTTPError:
+                    except (HTTPError, TooManyRedirects):
                         # retry with GET request if that fails, some servers
                         # don't like HEAD requests.
                         response = requests.get(req_url, stream=True, config=self.app.config,
@@ -255,56 +255,50 @@ class CheckExternalLinksBuilder(Builder):
 
     def process_result(self, result: Tuple[str, str, int, str, str, int]) -> None:
         uri, docname, lineno, status, info, code = result
-
-        filename = self.env.doc2path(docname, None)
-        linkstat = dict(filename=filename, lineno=lineno,
-                        status=status, code=code, uri=uri,
-                        info=info)
-        if status == 'unchecked':
-            self.write_linkstat(linkstat)
-            return
-        if status == 'working' and info == 'old':
-            self.write_linkstat(linkstat)
-            return
-        if lineno:
-            logger.info('(line %4d) ', lineno, nonl=True)
-        if status == 'ignored':
-            if info:
-                logger.info(darkgray('-ignored- ') + uri + ': ' + info)
-            else:
-                logger.info(darkgray('-ignored- ') + uri)
-            self.write_linkstat(linkstat)
-        elif status == 'local':
-            logger.info(darkgray('-local-   ') + uri)
-            self.write_entry('local', docname, filename, lineno, uri)
-            self.write_linkstat(linkstat)
-        elif status == 'working':
-            logger.info(darkgreen('ok        ') + uri + info)
-            self.write_linkstat(linkstat)
-        elif status == 'broken':
+        if status == "working":
+            self.write_entry('ok', uri, docname, lineno, info, code)
+        elif status == "broken":
+            self.write_entry('broken', uri, docname, lineno, info, code)
             if self.app.quiet or self.app.warningiserror:
                 logger.warning(__('broken link: %s (%s)'), uri, info,
-                               location=(filename, lineno))
+                             location=(self.env.doc2path(docname), lineno))
             else:
-                logger.info(red('broken    ') + uri + red(' - ' + info))
-            self.write_entry('broken', docname, filename, lineno, uri + ': ' + info)
-            self.write_linkstat(linkstat)
-        elif status == 'redirected':
-            try:
-                text, color = {
-                    301: ('permanently', purple),
-                    302: ('with Found', purple),
-                    303: ('with See Other', purple),
-                    307: ('temporarily', turquoise),
-                    308: ('permanently', purple),
-                }[code]
-            except KeyError:
-                text, color = ('with unknown code', purple)
-            linkstat['text'] = text
-            logger.info(color('redirect  ') + uri + color(' - ' + text + ' to ' + info))
-            self.write_entry('redirected ' + text, docname, filename,
-                             lineno, uri + ' to ' + info)
-            self.write_linkstat(linkstat)
+                logger.info(__('broken link: %s (%s)'), uri, info,
+                           location=(self.env.doc2path(docname), lineno))
+        elif status == "ignored":
+            self.write_entry('ignored', uri, docname, lineno, info, code)
+            if not self.app.quiet:
+                logger.info(__('ignored link: %s (%s)'), uri, info,
+                           location=(self.env.doc2path(docname), lineno))
+        elif status == "redirected":
+            self.write_entry('redirected', uri, docname, lineno, info, code)
+            if not self.app.quiet:
+                logger.info(__('redirected link: %s to %s'), uri, info,
+                           location=(self.env.doc2path(docname), lineno))
+        elif status == "unchecked":
+            self.write_entry('unchecked', uri, docname, lineno, info, code)
+        else:
+            raise ValueError("Unknown status %s." % status)
+
+    def write_entry(self, status: str, uri: str, docname: str, line: int,
+                   info: str, code: int = 0) -> None:
+        output = {'uri': uri, 'status': status, 'code': code, 'docname': docname,
+                 'lineno': line, 'info': info}
+        if status == "unchecked":
+            return
+        self.write_log_entry(output)
+
+    def write_log_entry(self, output: Dict) -> None:
+        with open(path.join(self.outdir, 'output.json'), 'a+') as json_log:
+            json_log.write(json.dumps(output))
+            json_log.write('\n')
+        with open(path.join(self.outdir, 'output.txt'), 'a+') as text_log:
+            text_log.write("%(uri)s\t%(status)s" % output)
+            if output['code']:
+                text_log.write("\t%(code)d" % output)
+            if output['info']:
+                text_log.write("\t%(info)s" % output)
+            text_log.write("\tfrom: %(docname)s:%(lineno)s\n" % output)
 
     def get_target_uri(self, docname: str, typ: str = None) -> str:
         return ''
@@ -319,23 +313,21 @@ class CheckExternalLinksBuilder(Builder):
         logger.info('')
         n = 0
 
-        # reference nodes
-        for refnode in doctree.traverse(nodes.reference):
-            if 'refuri' not in refnode:
-                continue
-            uri = refnode['refuri']
-            lineno = get_node_line(refnode)
-            self.wqueue.put((uri, docname, lineno), False)
-            n += 1
-
-        # image nodes
-        for imgnode in doctree.traverse(nodes.image):
-            uri = imgnode['candidates'].get('?')
-            if uri and '://' in uri:
-                lineno = get_node_line(imgnode)
-                self.wqueue.put((uri, docname, lineno), False)
-                n += 1
-
+        def crawl(node: Node) -> None:
+            nonlocal n
+            for child in node.children:
+                if isinstance(child, nodes.reference) and 'refuri' in child:
+                    uri = child['refuri']
+                    if self.env.config.linkcheck_anchors and \
+                       uri.startswith('#'):
+                        # in-page link
+                        continue
+                    lineno = get_node_line(child)
+                    self.wqueue.put((uri, docname, lineno))
+                    n += 1
+                crawl(child)
+
+        crawl(doctree)
         done = 0
         while done < n:
             self.process_result(self.rqueue.get())
@@ -344,37 +336,6 @@ class CheckExternalLinksBuilder(Builder):
         if self.broken:
             self.app.statuscode = 1
 
-    def write_entry(self, what: str, docname: str, filename: str, line: int,
-                    uri: str) -> None:
-        with open(path.join(self.outdir, 'output.txt'), 'a') as output:
-            output.write("%s:%s: [%s] %s\n" % (filename, line, what, uri))
-
-    def write_linkstat(self, data: dict) -> None:
-        with open(path.join(self.outdir, 'output.json'), 'a') as output:
-            output.write(json.dumps(data))
-            output.write('\n')
-
     def finish(self) -> None:
         for worker in self.workers:
-            self.wqueue.put((None, None, None), False)
-
-
-def setup(app: Sphinx) -> Dict[str, Any]:
-    app.add_builder(CheckExternalLinksBuilder)
-
-    app.add_config_value('linkcheck_ignore', [], None)
-    app.add_config_value('linkcheck_auth', [], None)
-    app.add_config_value('linkcheck_request_headers', {}, None)
-    app.add_config_value('linkcheck_retries', 1, None)
-    app.add_config_value('linkcheck_timeout', None, None, [int])
-    app.add_config_value('linkcheck_workers', 5, None)
-    app.add_config_value('linkcheck_anchors', True, None)
-    # Anchors starting with ! are ignored since they are
-    # commonly used for dynamic pages
-    app.add_config_value('linkcheck_anchors_ignore', ["^!"], None)
-
-    return {
-        'version': 'builtin',
-        'parallel_read_safe': True,
-        'parallel_write_safe': True,
-    }
+            self.wqueue.put((None, None, None))
\ No newline at end of file
