#!/usr/bin/env python3
"""
Live DGM Monitor - Updates every 30 seconds
"""

import os
import time
import subprocess
from quick_stats import get_dgm_stats

def clear_screen():
    """Clear the terminal screen."""
    os.system('clear' if os.name == 'posix' else 'cls')

def main():
    """Main monitoring loop."""
    print("🚀 Starting Live DGM Monitor...")
    print("Press Ctrl+C to stop monitoring")
    time.sleep(2)
    
    try:
        while True:
            clear_screen()
            get_dgm_stats()
            
            # Check if DGM is still running
            try:
                result = subprocess.run(['pgrep', '-f', 'DGM_outer.py'], 
                                      capture_output=True, text=True)
                if not result.stdout.strip():
                    print("\n⚠️  DGM process not found - it may have completed or crashed")
                    print("Monitor will continue checking...")
            except:
                pass
            
            print(f"\n⏱️  Next update in 30 seconds... (Ctrl+C to stop)")
            time.sleep(30)
            
    except KeyboardInterrupt:
        print("\n\n🛑 Live monitoring stopped by user")
        print("You can run 'python quick_stats.py' for a one-time status check")

if __name__ == "__main__":
    main()
