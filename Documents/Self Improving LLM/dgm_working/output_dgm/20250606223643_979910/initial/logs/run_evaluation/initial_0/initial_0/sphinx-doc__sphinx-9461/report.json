{"sphinx-doc__sphinx-9461": {"patch_is_None": false, "patch_exists": true, "patch_successfully_applied": true, "resolved": false, "tests_status": {"FAIL_TO_PASS": {"success": [], "failure": ["tests/test_domain_py.py::test_pyproperty", "tests/test_ext_autodoc_autoclass.py::test_properties", "tests/test_ext_autodoc_autoproperty.py::test_class_properties"]}, "PASS_TO_PASS": {"success": [], "failure": ["tests/test_domain_py.py::test_function_signatures", "tests/test_domain_py.py::test_domain_py_xrefs", "tests/test_domain_py.py::test_domain_py_xrefs_abbreviations", "tests/test_domain_py.py::test_domain_py_objects", "tests/test_domain_py.py::test_resolve_xref_for_properties", "tests/test_domain_py.py::test_domain_py_find_obj", "tests/test_domain_py.py::test_domain_py_canonical", "tests/test_domain_py.py::test_get_full_qualified_name", "tests/test_domain_py.py::test_parse_annotation", "tests/test_domain_py.py::test_pyfunction_signature", "tests/test_domain_py.py::test_pyfunction_signature_full", "tests/test_domain_py.py::test_pyfunction_signature_full_py38", "tests/test_domain_py.py::test_pyfunction_with_number_literals", "tests/test_domain_py.py::test_pyfunction_with_union_type_operator", "tests/test_domain_py.py::test_optional_pyfunction_signature", "tests/test_domain_py.py::test_pyexception_signature", "tests/test_domain_py.py::test_pydata_signature", "tests/test_domain_py.py::test_pydata_signature_old", "tests/test_domain_py.py::test_pydata_with_union_type_operator", "tests/test_domain_py.py::test_pyobject_prefix", "tests/test_domain_py.py::test_pydata", "tests/test_domain_py.py::test_pyfunction", "tests/test_domain_py.py::test_pyclass_options", "tests/test_domain_py.py::test_pymethod_options", "tests/test_domain_py.py::test_pyclassmethod", "tests/test_domain_py.py::test_pystaticmethod", "tests/test_domain_py.py::test_pyattribute", "tests/test_domain_py.py::test_pydecorator_signature", "tests/test_domain_py.py::test_pydecoratormethod_signature", "tests/test_domain_py.py::test_canonical", "tests/test_domain_py.py::test_canonical_definition_overrides", "tests/test_domain_py.py::test_canonical_definition_skip", "tests/test_domain_py.py::test_canonical_duplicated", "tests/test_domain_py.py::test_info_field_list", "tests/test_domain_py.py::test_info_field_list_piped_type", "tests/test_domain_py.py::test_info_field_list_var", "tests/test_domain_py.py::test_module_index", "tests/test_domain_py.py::test_module_index_submodule", "tests/test_domain_py.py::test_module_index_not_collapsed", "tests/test_domain_py.py::test_modindex_common_prefix", "tests/test_domain_py.py::test_noindexentry", "tests/test_domain_py.py::test_python_python_use_unqualified_type_names", "tests/test_domain_py.py::test_python_python_use_unqualified_type_names_disabled", "tests/test_domain_py.py::test_warn_missing_reference", "tests/test_ext_autodoc_autoclass.py::test_classes", "tests/test_ext_autodoc_autoclass.py::test_instance_variable", "tests/test_ext_autodoc_autoclass.py::test_inherited_instance_variable", "tests/test_ext_autodoc_autoclass.py::test_uninitialized_attributes", "tests/test_ext_autodoc_autoclass.py::test_undocumented_uninitialized_attributes", "tests/test_ext_autodoc_autoclass.py::test_decorators", "tests/test_ext_autodoc_autoclass.py::test_slots_attribute", "tests/test_ext_autodoc_autoclass.py::test_show_inheritance_for_subclass_of_generic_type", "tests/test_ext_autodoc_autoclass.py::test_autodoc_process_bases", "tests/test_ext_autodoc_autoclass.py::test_class_doc_from_class", "tests/test_ext_autodoc_autoclass.py::test_class_doc_from_init", "tests/test_ext_autodoc_autoclass.py::test_class_doc_from_both", "tests/test_ext_autodoc_autoclass.py::test_class_alias", "tests/test_ext_autodoc_autoclass.py::test_class_alias_having_doccomment", "tests/test_ext_autodoc_autoproperty.py::test_properties"]}, "FAIL_TO_FAIL": {"success": [], "failure": []}, "PASS_TO_FAIL": {"success": [], "failure": []}}}}