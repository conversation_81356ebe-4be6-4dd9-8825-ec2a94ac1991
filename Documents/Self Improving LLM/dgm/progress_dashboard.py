#!/usr/bin/env python3
"""
Real-time DGM Progress Dashboard
Monitors ongoing DGM runs and shows live progress.
"""

import os
import time
import json
import glob
from datetime import datetime
import re

def get_latest_generation(run_dir):
    """Get the latest generation number from log file."""
    log_file = os.path.join(run_dir, "dgm_outer.log")
    latest_gen = -1
    
    if os.path.exists(log_file):
        with open(log_file, 'r') as f:
            for line in f:
                if "Self-improve entries for generation" in line:
                    match = re.search(r'generation (\d+):', line)
                    if match:
                        latest_gen = max(latest_gen, int(match.group(1)))
    
    return latest_gen

def get_run_status(run_dir):
    """Check if a run is still active."""
    log_file = os.path.join(run_dir, "dgm_outer.log")
    
    if not os.path.exists(log_file):
        return "No log file"
    
    # Check if log file was modified recently (within last 5 minutes)
    mod_time = os.path.getmtime(log_file)
    current_time = time.time()
    
    if current_time - mod_time < 300:  # 5 minutes
        return "🟢 ACTIVE"
    else:
        return "🔴 STOPPED"

def count_successful_runs(run_dir):
    """Count successful self-improvement runs."""
    metadata_files = glob.glob(os.path.join(run_dir, "**/metadata.json"), recursive=True)
    
    successful = 0
    total = 0
    
    for metadata_file in metadata_files:
        try:
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
            
            total += 1
            if metadata.get('overall_performance'):
                successful += 1
                
        except:
            continue
    
    return successful, total

def display_dashboard():
    """Display the progress dashboard."""
    os.system('clear' if os.name == 'posix' else 'cls')
    
    print("🚀 " + "="*60)
    print("   DARWIN GÖDEL MACHINE - LIVE PROGRESS DASHBOARD")
    print("="*64)
    print(f"📅 Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Find all runs
    output_dir = "output_dgm"
    if not os.path.exists(output_dir):
        print("❌ No output_dgm directory found.")
        return
    
    run_dirs = [d for d in os.listdir(output_dir) if os.path.isdir(os.path.join(output_dir, d))]
    if not run_dirs:
        print("❌ No DGM runs found.")
        return
    
    # Sort by timestamp
    run_dirs.sort(reverse=True)
    
    print("📊 **ACTIVE RUNS**")
    print("-" * 64)
    
    for i, run_dir in enumerate(run_dirs[:3]):  # Show top 3 most recent
        full_path = os.path.join(output_dir, run_dir)
        status = get_run_status(full_path)
        latest_gen = get_latest_generation(full_path)
        successful, total = count_successful_runs(full_path)
        
        print(f"🔬 Run: {run_dir}")
        print(f"   Status: {status}")
        print(f"   Latest Generation: {latest_gen + 1 if latest_gen >= 0 else 0}")
        print(f"   Success Rate: {successful}/{total} ({successful/total*100:.1f}%)" if total > 0 else "   Success Rate: N/A")
        
        if status == "🟢 ACTIVE":
            print(f"   🔄 Currently evolving...")
        
        print()
    
    # Show overall statistics
    print("📈 **OVERALL STATISTICS**")
    print("-" * 64)
    
    total_generations = 0
    total_successful = 0
    total_attempts = 0
    
    for run_dir in run_dirs:
        full_path = os.path.join(output_dir, run_dir)
        latest_gen = get_latest_generation(full_path)
        successful, attempts = count_successful_runs(full_path)
        
        total_generations += (latest_gen + 1 if latest_gen >= 0 else 0)
        total_successful += successful
        total_attempts += attempts
    
    print(f"🔄 Total Generations Across All Runs: {total_generations}")
    print(f"✅ Total Successful Self-Improvements: {total_successful}")
    print(f"📊 Overall Success Rate: {total_successful/total_attempts*100:.1f}%" if total_attempts > 0 else "📊 Overall Success Rate: N/A")
    print(f"🏆 Runs Completed: {len([r for r in run_dirs if get_run_status(os.path.join(output_dir, r)) == '🔴 STOPPED'])}")
    print(f"🟢 Active Runs: {len([r for r in run_dirs if get_run_status(os.path.join(output_dir, r)) == '🟢 ACTIVE'])}")
    
    print()
    print("💡 **COMMANDS**")
    print("-" * 64)
    print("• Start new run: python DGM_outer.py --max_generation 50")
    print(f"• Resume latest: python DGM_outer.py --continue_from {run_dirs[0]}" if run_dirs else "")
    print("• Detailed analysis: python analyze_progress.py")
    print("• Press Ctrl+C to exit dashboard")

def main():
    """Main dashboard loop."""
    print("🚀 Starting DGM Progress Dashboard...")
    print("Press Ctrl+C to exit")
    
    try:
        while True:
            display_dashboard()
            time.sleep(10)  # Update every 10 seconds
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped. Happy evolving!")

if __name__ == "__main__":
    main()
