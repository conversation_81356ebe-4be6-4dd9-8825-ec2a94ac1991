{"instance_id": "sphinx-doc__sphinx-10449", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/sphinx/ext/autodoc/typehints.py b/sphinx/ext/autodoc/typehints.py\nindex 06768168e..53f949beb 100644\n--- a/sphinx/ext/autodoc/typehints.py\n+++ b/sphinx/ext/autodoc/typehints.py\n@@ -28,7 +28,8 @@ def record_typehints(app: Sphinx, objtype: str, name: str, obj: Any,\n             for param in sig.parameters.values():\n                 if param.annotation is not param.empty:\n                     annotation[param.name] = typing.stringify(param.annotation, mode)\n-            if sig.return_annotation is not sig.empty:\n+            if sig.return_annotation is not sig.empty and objtype != 'class':\n+                # Skip return annotation for classes to avoid showing __init__ return type\n                 annotation['return'] = typing.stringify(sig.return_annotation, mode)\n     except (TypeError, ValueError):\n         pass\n@@ -187,4 +188,4 @@ def setup(app: Sphinx) -> Dict[str, Any]:\n         'version': 'builtin',\n         'parallel_read_safe': True,\n         'parallel_write_safe': True,\n-    }\n+    }\n\\ No newline at end of file\ndiff --git a/tox.ini b/tox.ini\nindex 105a02597..90ada0821 100644\n--- a/tox.ini\n+++ b/tox.ini\n@@ -29,7 +29,7 @@ setenv =\n     PYTHONWARNINGS = all\n     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes\n commands=\n-    python -X dev -X warn_default_encoding -m pytest --durations 25 {posargs}\n+    python -X dev -X warn_default_encoding -m pytest -rA --durations 25 {posargs}\n \n [testenv:du-latest]\n commands =\n", "proposed_model_patches": []}