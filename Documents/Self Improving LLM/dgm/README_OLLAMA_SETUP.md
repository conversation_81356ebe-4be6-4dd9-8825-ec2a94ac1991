# Darwin Gödel Machine - Local Ollama Integration

This setup configures the Darwin Gödel Machine (DGM) project to use your local Ollama server with the qwen3:30b-a3b model instead of remote APIs like OpenAI or Anthropic.

## Overview

The integration uses LiteLLM as a proxy to route requests from the DGM project to your local Ollama server. This allows you to:

- Run DGM experiments completely locally
- Use your preferred qwen3:30b-a3b model
- Avoid API costs and rate limits
- Have full control over the model and data

## Architecture

```
DGM Project → LiteLLM Proxy → Ollama Server → qwen3:30b-a3b Model
```

## Prerequisites

1. **Ollama installed and running**
   ```bash
   # Install Ollama (if not already installed)
   curl -fsSL https://ollama.ai/install.sh | sh
   
   # Start Ollama server
   ollama serve
   ```

2. **qwen3:30b-a3b model available**
   ```bash
   # Pull the model (this may take some time)
   ollama pull qwen3:30b-a3b
   ```

3. **Python 3.8+ with pip**

## Quick Setup

1. **Run the setup script:**
   ```bash
   chmod +x setup_dgm_ollama.sh
   ./setup_dgm_ollama.sh
   ```

2. **Start the LiteLLM proxy:**
   ```bash
   chmod +x start_litellm.sh
   ./start_litellm.sh
   ```

3. **In another terminal, run DGM:**
   ```bash
   cd dgm
   source venv/bin/activate
   python DGM_outer.py
   ```

## Manual Setup

If you prefer to set up manually:

1. **Install dependencies:**
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

2. **Start LiteLLM proxy:**
   ```bash
   litellm --config litellm_config.yaml --port 4000 --host 0.0.0.0
   ```

3. **Run DGM:**
   ```bash
   python DGM_outer.py
   ```

## Configuration Files

- `litellm_config.yaml`: LiteLLM configuration routing to Ollama
- `llm.py`: Modified to support qwen models via LiteLLM
- `llm_withtools.py`: Updated model constants to use qwen3:30b-a3b

## Model Configuration

The system is configured to use:
- **Primary model**: `qwen3:30b-a3b` (via LiteLLM proxy)
- **Diagnosis model**: `qwen3:30b-a3b` (configurable in `self_improve_step.py`)

## Troubleshooting

### LiteLLM Proxy Issues
- Ensure Ollama is running on `localhost:11434`
- Check that qwen3:30b-a3b model is available: `ollama list`
- Verify LiteLLM proxy is running on `localhost:4000`

### Model Performance
- The qwen3:30b-a3b is an MoE model with 3B active parameters
- It should run efficiently despite the 30B total parameter count
- Adjust timeout settings if needed for longer responses

### Memory Issues
- Ensure sufficient RAM for the model
- Monitor system resources during experiments
- Consider reducing batch sizes if needed

## Running Experiments

You can now run DGM experiments as described in the original README:

```bash
# Basic run
python DGM_outer.py

# With custom parameters
python DGM_outer.py --max_generation 20 --selfimprove_size 3

# Polyglot benchmark
python DGM_outer.py --polyglot
```

## Benefits of Local Setup

- **Privacy**: All data stays local
- **Cost**: No API fees
- **Control**: Full control over model and parameters
- **Speed**: No network latency for API calls
- **Reliability**: No dependency on external services

## Notes

- The system maintains compatibility with the original DGM architecture
- You can switch back to remote APIs by changing the model constants
- LiteLLM provides extensive logging for debugging
- The setup supports the full DGM feature set including tool usage
