LICENSE
README.md
pyproject.toml
setup.cfg
setup.py
swebench/__init__.py
swebench.egg-info/PKG-INFO
swebench.egg-info/SOURCES.txt
swebench.egg-info/dependency_links.txt
swebench.egg-info/requires.txt
swebench.egg-info/top_level.txt
swebench/collect/__init__.py
swebench/collect/build_dataset.py
swebench/collect/build_dataset_ft.py
swebench/collect/get_tasks_pipeline.py
swebench/collect/get_top_pypi.py
swebench/collect/print_pulls.py
swebench/collect/utils.py
swebench/harness/__init__.py
swebench/harness/constants.py
swebench/harness/docker_build.py
swebench/harness/docker_utils.py
swebench/harness/dockerfiles.py
swebench/harness/grading.py
swebench/harness/log_parsers.py
swebench/harness/prepare_images.py
swebench/harness/remove_containers.py
swebench/harness/run_evaluation.py
swebench/harness/test_spec.py
swebench/harness/utils.py
swebench/inference/__init__.py
swebench/inference/run_api.py
swebench/inference/run_live.py
swebench/inference/run_llama.py
swebench/inference/llamao/__init__.py
swebench/inference/llamao/distributed_attention.py
swebench/inference/llamao/modeling_flash_llama.py
swebench/inference/make_datasets/__init__.py
swebench/inference/make_datasets/bm25_retrieval.py
swebench/inference/make_datasets/create_instance.py
swebench/inference/make_datasets/create_text_dataset.py
swebench/inference/make_datasets/eval_retrieval.py
swebench/inference/make_datasets/tokenize_dataset.py
swebench/inference/make_datasets/utils.py
swebench/versioning/__init__.py
swebench/versioning/constants.py
swebench/versioning/get_versions.py
swebench/versioning/utils.py
tests/test_cli.py
tests/test_collect_cli.py
tests/test_evaluation.py