{"instance_id": "sphinx-doc__sphinx-9230", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/setup.py b/setup.py\nindex 2538952e3..94e1d8b32 100644\n--- a/setup.py\n+++ b/setup.py\n@@ -15,22 +15,22 @@ if sys.version_info < (3, 6):\n     sys.exit(1)\n \n install_requires = [\n-    'sphinxcontrib-applehelp',\n-    'sphinxcontrib-devhelp',\n+    'sphinxcontrib-applehelp<=1.0.7',\n+    'sphinxcontrib-devhelp<=1.0.5',\n     'sphinxcontrib-jsmath',\n-    'sphinxcontrib-htmlhelp',\n-    'sphinxcontrib-serializinghtml',\n-    'sphinxcontrib-qthelp',\n-    'Jinja2>=2.3',\n+    'sphinxcontrib-htmlhelp<=2.0.4',\n+    'sphinxcontrib-serializinghtml<=1.1.9',\n+    'sphinxcontrib-qthelp<=1.0.6',\n+    'Jinja2<3.0',\n     'Pygments>=2.0',\n     'docutils>=0.14,<0.18',\n     'snowballstemmer>=1.1',\n     'babel>=1.3',\n-    'alabaster>=0.7,<0.8',\n+    'alabaster>=0.7,<0.7.12',\n     'imagesize',\n     'requests>=2.5.0',\n     'setuptools',\n-    'packaging',\n+    'packaging', 'markupsafe<=2.0.1',\n ]\n \n extras_require = {\ndiff --git a/sphinx/domains/python.py b/sphinx/domains/python.py\nindex 7d39d80ed..22f687ba0 100644\n--- a/sphinx/domains/python.py\n+++ b/sphinx/domains/python.py\n@@ -42,195 +42,6 @@ from sphinx.util.typing import OptionSpec, TextlikeNode\n \n logger = logging.getLogger(__name__)\n \n-\n-# REs for Python signatures\n-py_sig_re = re.compile(\n-    r'''^ ([\\w.]*\\.)?            # class name(s)\n-          (\\w+)  \\s*             # thing name\n-          (?: \\(\\s*(.*)\\s*\\)     # optional: arguments\n-           (?:\\s* -> \\s* (.*))?  #           return annotation\n-          )? $                   # and nothing more\n-          ''', re.VERBOSE)\n-\n-\n-pairindextypes = {\n-    'module':    _('module'),\n-    'keyword':   _('keyword'),\n-    'operator':  _('operator'),\n-    'object':    _('object'),\n-    'exception': _('exception'),\n-    'statement': _('statement'),\n-    'builtin':   _('built-in function'),\n-}\n-\n-\n-class ObjectEntry(NamedTuple):\n-    docname: str\n-    node_id: str\n-    objtype: str\n-    aliased: bool\n-\n-\n-class ModuleEntry(NamedTuple):\n-    docname: str\n-    node_id: str\n-    synopsis: str\n-    platform: str\n-    deprecated: bool\n-\n-\n-def type_to_xref(text: str, env: BuildEnvironment = None) -> addnodes.pending_xref:\n-    \"\"\"Convert a type string to a cross reference node.\"\"\"\n-    if text == 'None':\n-        reftype = 'obj'\n-    else:\n-        reftype = 'class'\n-\n-    if env:\n-        kwargs = {'py:module': env.ref_context.get('py:module'),\n-                  'py:class': env.ref_context.get('py:class')}\n-    else:\n-        kwargs = {}\n-\n-    if env.config.python_use_unqualified_type_names:\n-        # Note: It would be better to use qualname to describe the object to support support\n-        # nested classes.  But python domain can't access the real python object because this\n-        # module should work not-dynamically.\n-        shortname = text.split('.')[-1]\n-        contnodes: List[Node] = [pending_xref_condition('', shortname, condition='resolved'),\n-                                 pending_xref_condition('', text, condition='*')]\n-    else:\n-        contnodes = [nodes.Text(text)]\n-\n-    return pending_xref('', *contnodes,\n-                        refdomain='py', reftype=reftype, reftarget=text, **kwargs)\n-\n-\n-def _parse_annotation(annotation: str, env: BuildEnvironment = None) -> List[Node]:\n-    \"\"\"Parse type annotation.\"\"\"\n-    def unparse(node: ast.AST) -> List[Node]:\n-        if isinstance(node, ast.Attribute):\n-            return [nodes.Text(\"%s.%s\" % (unparse(node.value)[0], node.attr))]\n-        elif isinstance(node, ast.BinOp):\n-            result: List[Node] = unparse(node.left)\n-            result.extend(unparse(node.op))\n-            result.extend(unparse(node.right))\n-            return result\n-        elif isinstance(node, ast.BitOr):\n-            return [nodes.Text(' '), addnodes.desc_sig_punctuation('', '|'), nodes.Text(' ')]\n-        elif isinstance(node, ast.Constant):  # type: ignore\n-            if node.value is Ellipsis:\n-                return [addnodes.desc_sig_punctuation('', \"...\")]\n-            else:\n-                return [nodes.Text(node.value)]\n-        elif isinstance(node, ast.Expr):\n-            return unparse(node.value)\n-        elif isinstance(node, ast.Index):\n-            return unparse(node.value)\n-        elif isinstance(node, ast.List):\n-            result = [addnodes.desc_sig_punctuation('', '[')]\n-            for elem in node.elts:\n-                result.extend(unparse(elem))\n-                result.append(addnodes.desc_sig_punctuation('', ', '))\n-            result.pop()\n-            result.append(addnodes.desc_sig_punctuation('', ']'))\n-            return result\n-        elif isinstance(node, ast.Module):\n-            return sum((unparse(e) for e in node.body), [])\n-        elif isinstance(node, ast.Name):\n-            return [nodes.Text(node.id)]\n-        elif isinstance(node, ast.Subscript):\n-            result = unparse(node.value)\n-            result.append(addnodes.desc_sig_punctuation('', '['))\n-            result.extend(unparse(node.slice))\n-            result.append(addnodes.desc_sig_punctuation('', ']'))\n-            return result\n-        elif isinstance(node, ast.Tuple):\n-            if node.elts:\n-                result = []\n-                for elem in node.elts:\n-                    result.extend(unparse(elem))\n-                    result.append(addnodes.desc_sig_punctuation('', ', '))\n-                result.pop()\n-            else:\n-                result = [addnodes.desc_sig_punctuation('', '('),\n-                          addnodes.desc_sig_punctuation('', ')')]\n-\n-            return result\n-        else:\n-            if sys.version_info < (3, 8):\n-                if isinstance(node, ast.Ellipsis):\n-                    return [addnodes.desc_sig_punctuation('', \"...\")]\n-                elif isinstance(node, ast.NameConstant):\n-                    return [nodes.Text(node.value)]\n-\n-            raise SyntaxError  # unsupported syntax\n-\n-    if env is None:\n-        warnings.warn(\"The env parameter for _parse_annotation becomes required now.\",\n-                      RemovedInSphinx50Warning, stacklevel=2)\n-\n-    try:\n-        tree = ast_parse(annotation)\n-        result = unparse(tree)\n-        for i, node in enumerate(result):\n-            if isinstance(node, nodes.Text) and node.strip():\n-                result[i] = type_to_xref(str(node), env)\n-        return result\n-    except SyntaxError:\n-        return [type_to_xref(annotation, env)]\n-\n-\n-def _parse_arglist(arglist: str, env: BuildEnvironment = None) -> addnodes.desc_parameterlist:\n-    \"\"\"Parse a list of arguments using AST parser\"\"\"\n-    params = addnodes.desc_parameterlist(arglist)\n-    sig = signature_from_str('(%s)' % arglist)\n-    last_kind = None\n-    for param in sig.parameters.values():\n-        if param.kind != param.POSITIONAL_ONLY and last_kind == param.POSITIONAL_ONLY:\n-            # PEP-570: Separator for Positional Only Parameter: /\n-            params += addnodes.desc_parameter('', '', addnodes.desc_sig_operator('', '/'))\n-        if param.kind == param.KEYWORD_ONLY and last_kind in (param.POSITIONAL_OR_KEYWORD,\n-                                                              param.POSITIONAL_ONLY,\n-                                                              None):\n-            # PEP-3102: Separator for Keyword Only Parameter: *\n-            params += addnodes.desc_parameter('', '', addnodes.desc_sig_operator('', '*'))\n-\n-        node = addnodes.desc_parameter()\n-        if param.kind == param.VAR_POSITIONAL:\n-            node += addnodes.desc_sig_operator('', '*')\n-            node += addnodes.desc_sig_name('', param.name)\n-        elif param.kind == param.VAR_KEYWORD:\n-            node += addnodes.desc_sig_operator('', '**')\n-            node += addnodes.desc_sig_name('', param.name)\n-        else:\n-            node += addnodes.desc_sig_name('', param.name)\n-\n-        if param.annotation is not param.empty:\n-            children = _parse_annotation(param.annotation, env)\n-            node += addnodes.desc_sig_punctuation('', ':')\n-            node += nodes.Text(' ')\n-            node += addnodes.desc_sig_name('', '', *children)  # type: ignore\n-        if param.default is not param.empty:\n-            if param.annotation is not param.empty:\n-                node += nodes.Text(' ')\n-                node += addnodes.desc_sig_operator('', '=')\n-                node += nodes.Text(' ')\n-            else:\n-                node += addnodes.desc_sig_operator('', '=')\n-            node += nodes.inline('', param.default, classes=['default_value'],\n-                                 support_smartquotes=False)\n-\n-        params += node\n-        last_kind = param.kind\n-\n-    if last_kind == Parameter.POSITIONAL_ONLY:\n-        # PEP-570: Separator for Positional Only Parameter: /\n-        params += addnodes.desc_parameter('', '', addnodes.desc_sig_operator('', '/'))\n-\n-    return params\n-\n-\n def _pseudo_parse_arglist(signode: desc_signature, arglist: str) -> None:\n     \"\"\"\"Parse\" a list of arguments separated by commas.\n \n@@ -258,7 +69,7 @@ def _pseudo_parse_arglist(signode: desc_signature, arglist: str) -> None:\n                 ends_open += 1\n                 argument = argument[:-1].strip()\n             if argument:\n-                stack[-1] += addnodes.desc_parameter(argument, argument)\n+                stack[-1] += addnodes.desc_parameter('', '', addnodes.desc_sig_name('', argument))\n             while ends_open:\n                 stack.append(addnodes.desc_optional())\n                 stack[-2] += stack[-1]\n@@ -268,1132 +79,139 @@ def _pseudo_parse_arglist(signode: desc_signature, arglist: str) -> None:\n                 ends_close -= 1\n         if len(stack) != 1:\n             raise IndexError\n-    except IndexError:\n+    except (IndexError, TypeError):\n         # if there are too few or too many elements on the stack, just give up\n-        # and treat the whole argument list as one argument, discarding the\n-        # already partially populated paramlist node\n-        paramlist = addnodes.desc_parameterlist()\n-        paramlist += addnodes.desc_parameter(arglist, arglist)\n-        signode += paramlist\n-    else:\n-        signode += paramlist\n-\n-\n-# This override allows our inline type specifiers to behave like :class: link\n-# when it comes to handling \".\" and \"~\" prefixes.\n-class PyXrefMixin:\n-    def make_xref(self, rolename: str, domain: str, target: str,\n-                  innernode: Type[TextlikeNode] = nodes.emphasis,\n-                  contnode: Node = None, env: BuildEnvironment = None) -> Node:\n-        result = super().make_xref(rolename, domain, target,  # type: ignore\n-                                   innernode, contnode, env)\n-        result['refspecific'] = True\n-        result['py:module'] = env.ref_context.get('py:module')\n-        result['py:class'] = env.ref_context.get('py:class')\n-        if target.startswith(('.', '~')):\n-            prefix, result['reftarget'] = target[0], target[1:]\n-            if prefix == '.':\n-                text = target[1:]\n-            elif prefix == '~':\n-                text = target.split('.')[-1]\n-            for node in result.traverse(nodes.Text):\n-                node.parent[node.parent.index(node)] = nodes.Text(text)\n-                break\n-        return result\n-\n-    def make_xrefs(self, rolename: str, domain: str, target: str,\n-                   innernode: Type[TextlikeNode] = nodes.emphasis,\n-                   contnode: Node = None, env: BuildEnvironment = None) -> List[Node]:\n-        delims = r'(\\s*[\\[\\]\\(\\),](?:\\s*or\\s)?\\s*|\\s+or\\s+|\\.\\.\\.)'\n-        delims_re = re.compile(delims)\n-        sub_targets = re.split(delims, target)\n-\n-        split_contnode = bool(contnode and contnode.astext() == target)\n-\n-        results = []\n-        for sub_target in filter(None, sub_targets):\n-            if split_contnode:\n-                contnode = nodes.Text(sub_target)\n-\n-            if delims_re.match(sub_target):\n-                results.append(contnode or innernode(sub_target, sub_target))\n-            else:\n-                results.append(self.make_xref(rolename, domain, sub_target,\n-                                              innernode, contnode, env))\n-\n-        return results\n-\n-\n-class PyField(PyXrefMixin, Field):\n-    def make_xref(self, rolename: str, domain: str, target: str,\n-                  innernode: Type[TextlikeNode] = nodes.emphasis,\n-                  contnode: Node = None, env: BuildEnvironment = None) -> Node:\n-        if rolename == 'class' and target == 'None':\n-            # None is not a type, so use obj role instead.\n-            rolename = 'obj'\n-\n-        return super().make_xref(rolename, domain, target, innernode, contnode, env)\n-\n-\n-class PyGroupedField(PyXrefMixin, GroupedField):\n-    pass\n-\n-\n-class PyTypedField(PyXrefMixin, TypedField):\n-    def make_xref(self, rolename: str, domain: str, target: str,\n-                  innernode: Type[TextlikeNode] = nodes.emphasis,\n-                  contnode: Node = None, env: BuildEnvironment = None) -> Node:\n-        if rolename == 'class' and target == 'None':\n-            # None is not a type, so use obj role instead.\n-            rolename = 'obj'\n-\n-        return super().make_xref(rolename, domain, target, innernode, contnode, env)\n-\n-\n-class PyObject(ObjectDescription[Tuple[str, str]]):\n-    \"\"\"\n-    Description of a general Python object.\n-\n-    :cvar allow_nesting: Class is an object that allows for nested namespaces\n-    :vartype allow_nesting: bool\n-    \"\"\"\n-    option_spec: OptionSpec = {\n-        'noindex': directives.flag,\n-        'noindexentry': directives.flag,\n-        'module': directives.unchanged,\n-        'canonical': directives.unchanged,\n-        'annotation': directives.unchanged,\n-    }\n-\n-    doc_field_types = [\n-        PyTypedField('parameter', label=_('Parameters'),\n-                     names=('param', 'parameter', 'arg', 'argument',\n-                            'keyword', 'kwarg', 'kwparam'),\n-                     typerolename='class', typenames=('paramtype', 'type'),\n-                     can_collapse=True),\n-        PyTypedField('variable', label=_('Variables'),\n-                     names=('var', 'ivar', 'cvar'),\n-                     typerolename='class', typenames=('vartype',),\n-                     can_collapse=True),\n-        PyGroupedField('exceptions', label=_('Raises'), rolename='exc',\n-                       names=('raises', 'raise', 'exception', 'except'),\n-                       can_collapse=True),\n-        Field('returnvalue', label=_('Returns'), has_arg=False,\n-              names=('returns', 'return')),\n-        PyField('returntype', label=_('Return type'), has_arg=False,\n-                names=('rtype',), bodyrolename='class'),\n-    ]\n-\n-    allow_nesting = False\n-\n-    def get_signature_prefix(self, sig: str) -> str:\n-        \"\"\"May return a prefix to put before the object name in the\n-        signature.\n-        \"\"\"\n-        return ''\n-\n-    def needs_arglist(self) -> bool:\n-        \"\"\"May return true if an empty argument list is to be generated even if\n-        the document contains none.\n-        \"\"\"\n-        return False\n-\n-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:\n-        \"\"\"Transform a Python signature into RST nodes.\n-\n-        Return (fully qualified name of the thing, classname if any).\n-\n-        If inside a class, the current class name is handled intelligently:\n-        * it is stripped from the displayed name if present\n-        * it is added to the full name (return value) if not present\n-        \"\"\"\n-        m = py_sig_re.match(sig)\n-        if m is None:\n-            raise ValueError\n-        prefix, name, arglist, retann = m.groups()\n-\n-        # determine module and class name (if applicable), as well as full name\n-        modname = self.options.get('module', self.env.ref_context.get('py:module'))\n-        classname = self.env.ref_context.get('py:class')\n-        if classname:\n-            add_module = False\n-            if prefix and (prefix == classname or\n-                           prefix.startswith(classname + \".\")):\n-                fullname = prefix + name\n-                # class name is given again in the signature\n-                prefix = prefix[len(classname):].lstrip('.')\n-            elif prefix:\n-                # class name is given in the signature, but different\n-                # (shouldn't happen)\n-                fullname = classname + '.' + prefix + name\n-            else:\n-                # class name is not given in the signature\n-                fullname = classname + '.' + name\n-        else:\n-            add_module = True\n-            if prefix:\n-                classname = prefix.rstrip('.')\n-                fullname = prefix + name\n-            else:\n-                classname = ''\n-                fullname = name\n-\n-        signode['module'] = modname\n-        signode['class'] = classname\n-        signode['fullname'] = fullname\n-\n-        sig_prefix = self.get_signature_prefix(sig)\n-        if sig_prefix:\n-            signode += addnodes.desc_annotation(sig_prefix, sig_prefix)\n-\n-        if prefix:\n-            signode += addnodes.desc_addname(prefix, prefix)\n-        elif add_module and self.env.config.add_module_names:\n-            if modname and modname != 'exceptions':\n-                # exceptions are a special case, since they are documented in the\n-                # 'exceptions' module.\n-                nodetext = modname + '.'\n-                signode += addnodes.desc_addname(nodetext, nodetext)\n-\n-        signode += addnodes.desc_name(name, name)\n-        if arglist:\n-            try:\n-                signode += _parse_arglist(arglist, self.env)\n-            except SyntaxError:\n-                # fallback to parse arglist original parser.\n-                # it supports to represent optional arguments (ex. \"func(foo [, bar])\")\n-                _pseudo_parse_arglist(signode, arglist)\n-            except NotImplementedError as exc:\n-                logger.warning(\"could not parse arglist (%r): %s\", arglist, exc,\n-                               location=signode)\n-                _pseudo_parse_arglist(signode, arglist)\n-        else:\n-            if self.needs_arglist():\n-                # for callables, add an empty parameter list\n-                signode += addnodes.desc_parameterlist()\n-\n-        if retann:\n-            children = _parse_annotation(retann, self.env)\n-            signode += addnodes.desc_returns(retann, '', *children)\n-\n-        anno = self.options.get('annotation')\n-        if anno:\n-            signode += addnodes.desc_annotation(' ' + anno, ' ' + anno)\n-\n-        return fullname, prefix\n-\n-    def get_index_text(self, modname: str, name: Tuple[str, str]) -> str:\n-        \"\"\"Return the text for the index entry of the object.\"\"\"\n-        raise NotImplementedError('must be implemented in subclasses')\n-\n-    def add_target_and_index(self, name_cls: Tuple[str, str], sig: str,\n-                             signode: desc_signature) -> None:\n-        modname = self.options.get('module', self.env.ref_context.get('py:module'))\n-        fullname = (modname + '.' if modname else '') + name_cls[0]\n-        node_id = make_id(self.env, self.state.document, '', fullname)\n-        signode['ids'].append(node_id)\n-\n-        # Assign old styled node_id(fullname) not to break old hyperlinks (if possible)\n-        # Note: Will removed in Sphinx-5.0  (RemovedInSphinx50Warning)\n-        if node_id != fullname and fullname not in self.state.document.ids:\n-            signode['ids'].append(fullname)\n-\n-        self.state.document.note_explicit_target(signode)\n-\n-        domain = cast(PythonDomain, self.env.get_domain('py'))\n-        domain.note_object(fullname, self.objtype, node_id, location=signode)\n-\n-        canonical_name = self.options.get('canonical')\n-        if canonical_name:\n-            domain.note_object(canonical_name, self.objtype, node_id, aliased=True,\n-                               location=signode)\n-\n-        if 'noindexentry' not in self.options:\n-            indextext = self.get_index_text(modname, name_cls)\n-            if indextext:\n-                self.indexnode['entries'].append(('single', indextext, node_id, '', None))\n-\n-    def before_content(self) -> None:\n-        \"\"\"Handle object nesting before content\n-\n-        :py:class:`PyObject` represents Python language constructs. For\n-        constructs that are nestable, such as a Python classes, this method will\n-        build up a stack of the nesting hierarchy so that it can be later\n-        de-nested correctly, in :py:meth:`after_content`.\n-\n-        For constructs that aren't nestable, the stack is bypassed, and instead\n-        only the most recent object is tracked. This object prefix name will be\n-        removed with :py:meth:`after_content`.\n-        \"\"\"\n-        prefix = None\n-        if self.names:\n-            # fullname and name_prefix come from the `handle_signature` method.\n-            # fullname represents the full object name that is constructed using\n-            # object nesting and explicit prefixes. `name_prefix` is the\n-            # explicit prefix given in a signature\n-            (fullname, name_prefix) = self.names[-1]\n-            if self.allow_nesting:\n-                prefix = fullname\n-            elif name_prefix:\n-                prefix = name_prefix.strip('.')\n-        if prefix:\n-            self.env.ref_context['py:class'] = prefix\n-            if self.allow_nesting:\n-                classes = self.env.ref_context.setdefault('py:classes', [])\n-                classes.append(prefix)\n-        if 'module' in self.options:\n-            modules = self.env.ref_context.setdefault('py:modules', [])\n-            modules.append(self.env.ref_context.get('py:module'))\n-            self.env.ref_context['py:module'] = self.options['module']\n-\n-    def after_content(self) -> None:\n-        \"\"\"Handle object de-nesting after content\n-\n-        If this class is a nestable object, removing the last nested class prefix\n-        ends further nesting in the object.\n-\n-        If this class is not a nestable object, the list of classes should not\n-        be altered as we didn't affect the nesting levels in\n-        :py:meth:`before_content`.\n-        \"\"\"\n-        classes = self.env.ref_context.setdefault('py:classes', [])\n-        if self.allow_nesting:\n-            try:\n-                classes.pop()\n-            except IndexError:\n-                pass\n-        self.env.ref_context['py:class'] = (classes[-1] if len(classes) > 0\n-                                            else None)\n-        if 'module' in self.options:\n-            modules = self.env.ref_context.setdefault('py:modules', [])\n-            if modules:\n-                self.env.ref_context['py:module'] = modules.pop()\n-            else:\n-                self.env.ref_context.pop('py:module')\n-\n-\n-class PyFunction(PyObject):\n-    \"\"\"Description of a function.\"\"\"\n-\n-    option_spec: OptionSpec = PyObject.option_spec.copy()\n-    option_spec.update({\n-        'async': directives.flag,\n-    })\n-\n-    def get_signature_prefix(self, sig: str) -> str:\n-        if 'async' in self.options:\n-            return 'async '\n-        else:\n-            return ''\n-\n-    def needs_arglist(self) -> bool:\n-        return True\n-\n-    def add_target_and_index(self, name_cls: Tuple[str, str], sig: str,\n-                             signode: desc_signature) -> None:\n-        super().add_target_and_index(name_cls, sig, signode)\n-        if 'noindexentry' not in self.options:\n-            modname = self.options.get('module', self.env.ref_context.get('py:module'))\n-            node_id = signode['ids'][0]\n-\n-            name, cls = name_cls\n-            if modname:\n-                text = _('%s() (in module %s)') % (name, modname)\n-                self.indexnode['entries'].append(('single', text, node_id, '', None))\n-            else:\n-                text = '%s; %s()' % (pairindextypes['builtin'], name)\n-                self.indexnode['entries'].append(('pair', text, node_id, '', None))\n-\n-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:\n-        # add index in own add_target_and_index() instead.\n-        return None\n-\n-\n-class PyDecoratorFunction(PyFunction):\n-    \"\"\"Description of a decorator.\"\"\"\n-\n-    def run(self) -> List[Node]:\n-        # a decorator function is a function after all\n-        self.name = 'py:function'\n-        return super().run()\n-\n-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:\n-        ret = super().handle_signature(sig, signode)\n-        signode.insert(0, addnodes.desc_addname('@', '@'))\n-        return ret\n-\n-    def needs_arglist(self) -> bool:\n-        return False\n-\n-\n-class PyVariable(PyObject):\n-    \"\"\"Description of a variable.\"\"\"\n-\n-    option_spec: OptionSpec = PyObject.option_spec.copy()\n-    option_spec.update({\n-        'type': directives.unchanged,\n-        'value': directives.unchanged,\n-    })\n-\n-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:\n-        fullname, prefix = super().handle_signature(sig, signode)\n-\n-        typ = self.options.get('type')\n-        if typ:\n-            annotations = _parse_annotation(typ, self.env)\n-            signode += addnodes.desc_annotation(typ, '', nodes.Text(': '), *annotations)\n-\n-        value = self.options.get('value')\n-        if value:\n-            signode += addnodes.desc_annotation(value, ' = ' + value)\n-\n-        return fullname, prefix\n-\n-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:\n-        name, cls = name_cls\n-        if modname:\n-            return _('%s (in module %s)') % (name, modname)\n-        else:\n-            return _('%s (built-in variable)') % name\n-\n-\n-class PyClasslike(PyObject):\n-    \"\"\"\n-    Description of a class-like object (classes, interfaces, exceptions).\n-    \"\"\"\n-\n-    option_spec: OptionSpec = PyObject.option_spec.copy()\n-    option_spec.update({\n-        'final': directives.flag,\n-    })\n-\n-    allow_nesting = True\n-\n-    def get_signature_prefix(self, sig: str) -> str:\n-        if 'final' in self.options:\n-            return 'final %s ' % self.objtype\n-        else:\n-            return '%s ' % self.objtype\n-\n-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:\n-        if self.objtype == 'class':\n-            if not modname:\n-                return _('%s (built-in class)') % name_cls[0]\n-            return _('%s (class in %s)') % (name_cls[0], modname)\n-        elif self.objtype == 'exception':\n-            return name_cls[0]\n-        else:\n-            return ''\n-\n-\n-class PyMethod(PyObject):\n-    \"\"\"Description of a method.\"\"\"\n-\n-    option_spec: OptionSpec = PyObject.option_spec.copy()\n-    option_spec.update({\n-        'abstractmethod': directives.flag,\n-        'async': directives.flag,\n-        'classmethod': directives.flag,\n-        'final': directives.flag,\n-        'property': directives.flag,\n-        'staticmethod': directives.flag,\n-    })\n-\n-    def needs_arglist(self) -> bool:\n-        if 'property' in self.options:\n-            return False\n-        else:\n-            return True\n-\n-    def get_signature_prefix(self, sig: str) -> str:\n-        prefix = []\n-        if 'final' in self.options:\n-            prefix.append('final')\n-        if 'abstractmethod' in self.options:\n-            prefix.append('abstract')\n-        if 'async' in self.options:\n-            prefix.append('async')\n-        if 'classmethod' in self.options:\n-            prefix.append('classmethod')\n-        if 'property' in self.options:\n-            prefix.append('property')\n-        if 'staticmethod' in self.options:\n-            prefix.append('static')\n-\n-        if prefix:\n-            return ' '.join(prefix) + ' '\n-        else:\n-            return ''\n-\n-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:\n-        name, cls = name_cls\n-        try:\n-            clsname, methname = name.rsplit('.', 1)\n-            if modname and self.env.config.add_module_names:\n-                clsname = '.'.join([modname, clsname])\n-        except ValueError:\n-            if modname:\n-                return _('%s() (in module %s)') % (name, modname)\n-            else:\n-                return '%s()' % name\n-\n-        if 'classmethod' in self.options:\n-            return _('%s() (%s class method)') % (methname, clsname)\n-        elif 'property' in self.options:\n-            return _('%s() (%s property)') % (methname, clsname)\n-        elif 'staticmethod' in self.options:\n-            return _('%s() (%s static method)') % (methname, clsname)\n-        else:\n-            return _('%s() (%s method)') % (methname, clsname)\n-\n-\n-class PyClassMethod(PyMethod):\n-    \"\"\"Description of a classmethod.\"\"\"\n-\n-    option_spec: OptionSpec = PyObject.option_spec.copy()\n-\n-    def run(self) -> List[Node]:\n-        self.name = 'py:method'\n-        self.options['classmethod'] = True\n-\n-        return super().run()\n-\n-\n-class PyStaticMethod(PyMethod):\n-    \"\"\"Description of a staticmethod.\"\"\"\n-\n-    option_spec: OptionSpec = PyObject.option_spec.copy()\n-\n-    def run(self) -> List[Node]:\n-        self.name = 'py:method'\n-        self.options['staticmethod'] = True\n-\n-        return super().run()\n-\n-\n-class PyDecoratorMethod(PyMethod):\n-    \"\"\"Description of a decoratormethod.\"\"\"\n-\n-    def run(self) -> List[Node]:\n-        self.name = 'py:method'\n-        return super().run()\n-\n-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:\n-        ret = super().handle_signature(sig, signode)\n-        signode.insert(0, addnodes.desc_addname('@', '@'))\n-        return ret\n-\n-    def needs_arglist(self) -> bool:\n-        return False\n-\n-\n-class PyAttribute(PyObject):\n-    \"\"\"Description of an attribute.\"\"\"\n-\n-    option_spec: OptionSpec = PyObject.option_spec.copy()\n-    option_spec.update({\n-        'type': directives.unchanged,\n-        'value': directives.unchanged,\n-    })\n-\n-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:\n-        fullname, prefix = super().handle_signature(sig, signode)\n-\n-        typ = self.options.get('type')\n-        if typ:\n-            annotations = _parse_annotation(typ, self.env)\n-            signode += addnodes.desc_annotation(typ, '', nodes.Text(': '), *annotations)\n-\n-        value = self.options.get('value')\n-        if value:\n-            signode += addnodes.desc_annotation(value, ' = ' + value)\n-\n-        return fullname, prefix\n-\n-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:\n-        name, cls = name_cls\n-        try:\n-            clsname, attrname = name.rsplit('.', 1)\n-            if modname and self.env.config.add_module_names:\n-                clsname = '.'.join([modname, clsname])\n-        except ValueError:\n-            if modname:\n-                return _('%s (in module %s)') % (name, modname)\n-            else:\n-                return name\n-\n-        return _('%s (%s attribute)') % (attrname, clsname)\n-\n-\n-class PyProperty(PyObject):\n-    \"\"\"Description of an attribute.\"\"\"\n-\n-    option_spec = PyObject.option_spec.copy()\n-    option_spec.update({\n-        'abstractmethod': directives.flag,\n-        'type': directives.unchanged,\n-    })\n-\n-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:\n-        fullname, prefix = super().handle_signature(sig, signode)\n-\n-        typ = self.options.get('type')\n-        if typ:\n-            signode += addnodes.desc_annotation(typ, ': ' + typ)\n-\n-        return fullname, prefix\n-\n-    def get_signature_prefix(self, sig: str) -> str:\n-        prefix = ['property']\n-        if 'abstractmethod' in self.options:\n-            prefix.insert(0, 'abstract')\n-\n-        return ' '.join(prefix) + ' '\n-\n-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:\n-        name, cls = name_cls\n-        try:\n-            clsname, attrname = name.rsplit('.', 1)\n-            if modname and self.env.config.add_module_names:\n-                clsname = '.'.join([modname, clsname])\n-        except ValueError:\n-            if modname:\n-                return _('%s (in module %s)') % (name, modname)\n-            else:\n-                return name\n-\n-        return _('%s (%s property)') % (attrname, clsname)\n-\n-\n-class PyDecoratorMixin:\n-    \"\"\"\n-    Mixin for decorator directives.\n-    \"\"\"\n-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:\n-        for cls in self.__class__.__mro__:\n-            if cls.__name__ != 'DirectiveAdapter':\n-                warnings.warn('PyDecoratorMixin is deprecated. '\n-                              'Please check the implementation of %s' % cls,\n-                              RemovedInSphinx50Warning, stacklevel=2)\n-                break\n-        else:\n-            warnings.warn('PyDecoratorMixin is deprecated',\n-                          RemovedInSphinx50Warning, stacklevel=2)\n-\n-        ret = super().handle_signature(sig, signode)  # type: ignore\n-        signode.insert(0, addnodes.desc_addname('@', '@'))\n-        return ret\n-\n-    def needs_arglist(self) -> bool:\n-        return False\n-\n-\n-class PyModule(SphinxDirective):\n-    \"\"\"\n-    Directive to mark description of a new module.\n-    \"\"\"\n-\n-    has_content = False\n-    required_arguments = 1\n-    optional_arguments = 0\n-    final_argument_whitespace = False\n-    option_spec: OptionSpec = {\n-        'platform': lambda x: x,\n-        'synopsis': lambda x: x,\n-        'noindex': directives.flag,\n-        'deprecated': directives.flag,\n-    }\n-\n-    def run(self) -> List[Node]:\n-        domain = cast(PythonDomain, self.env.get_domain('py'))\n-\n-        modname = self.arguments[0].strip()\n-        noindex = 'noindex' in self.options\n-        self.env.ref_context['py:module'] = modname\n-        ret: List[Node] = []\n-        if not noindex:\n-            # note module to the domain\n-            node_id = make_id(self.env, self.state.document, 'module', modname)\n-            target = nodes.target('', '', ids=[node_id], ismod=True)\n-            self.set_source_info(target)\n-\n-            # Assign old styled node_id not to break old hyperlinks (if possible)\n-            # Note: Will removed in Sphinx-5.0  (RemovedInSphinx50Warning)\n-            old_node_id = self.make_old_id(modname)\n-            if node_id != old_node_id and old_node_id not in self.state.document.ids:\n-                target['ids'].append(old_node_id)\n-\n-            self.state.document.note_explicit_target(target)\n-\n-            domain.note_module(modname,\n-                               node_id,\n-                               self.options.get('synopsis', ''),\n-                               self.options.get('platform', ''),\n-                               'deprecated' in self.options)\n-            domain.note_object(modname, 'module', node_id, location=target)\n-\n-            # the platform and synopsis aren't printed; in fact, they are only\n-            # used in the modindex currently\n-            ret.append(target)\n-            indextext = '%s; %s' % (pairindextypes['module'], modname)\n-            inode = addnodes.index(entries=[('pair', indextext, node_id, '', None)])\n-            ret.append(inode)\n-        return ret\n-\n-    def make_old_id(self, name: str) -> str:\n-        \"\"\"Generate old styled node_id.\n-\n-        Old styled node_id is incompatible with docutils' node_id.\n-        It can contain dots and hyphens.\n-\n-        .. note:: Old styled node_id was mainly used until Sphinx-3.0.\n-        \"\"\"\n-        return 'module-%s' % name\n-\n-\n-class PyCurrentModule(SphinxDirective):\n-    \"\"\"\n-    This directive is just to tell Sphinx that we're documenting\n-    stuff in module foo, but links to module foo won't lead here.\n-    \"\"\"\n-\n-    has_content = False\n-    required_arguments = 1\n-    optional_arguments = 0\n-    final_argument_whitespace = False\n-    option_spec: OptionSpec = {}\n-\n-    def run(self) -> List[Node]:\n-        modname = self.arguments[0].strip()\n-        if modname == 'None':\n-            self.env.ref_context.pop('py:module', None)\n-        else:\n-            self.env.ref_context['py:module'] = modname\n-        return []\n-\n-\n-class PyXRefRole(XRefRole):\n-    def process_link(self, env: BuildEnvironment, refnode: Element,\n-                     has_explicit_title: bool, title: str, target: str) -> Tuple[str, str]:\n-        refnode['py:module'] = env.ref_context.get('py:module')\n-        refnode['py:class'] = env.ref_context.get('py:class')\n-        if not has_explicit_title:\n-            title = title.lstrip('.')    # only has a meaning for the target\n-            target = target.lstrip('~')  # only has a meaning for the title\n-            # if the first character is a tilde, don't display the module/class\n-            # parts of the contents\n-            if title[0:1] == '~':\n-                title = title[1:]\n-                dot = title.rfind('.')\n-                if dot != -1:\n-                    title = title[dot + 1:]\n-        # if the first character is a dot, search more specific namespaces first\n-        # else search builtins first\n-        if target[0:1] == '.':\n-            target = target[1:]\n-            refnode['refspecific'] = True\n-        return title, target\n-\n-\n-def filter_meta_fields(app: Sphinx, domain: str, objtype: str, content: Element) -> None:\n-    \"\"\"Filter ``:meta:`` field from its docstring.\"\"\"\n-    if domain != 'py':\n+        # and reset the parameter list\n+        signode.clear()\n+        signode += addnodes.desc_parameterlist()\n         return\n+    signode += paramlist\n+\n+def handle_dict_type(text: str, env: BuildEnvironment = None) -> addnodes.desc_type:\n+    \"\"\"Handle dictionary type annotations like dict(str, str).\"\"\"\n+    parts = text[5:-1].split(',')\n+    if len(parts) == 2:\n+        # Create nodes for the dictionary structure\n+        dict_ref = pending_xref('', nodes.Text('dict'),\n+                              refdomain='py', reftype='class', reftarget='dict')\n+        \n+        # Add opening parenthesis\n+        result = [dict_ref, addnodes.desc_sig_punctuation('', '(')]\n+        \n+        # Process first type parameter\n+        key_type = type_to_xref(parts[0].strip(), env)\n+        result.append(key_type)\n+        result.append(addnodes.desc_sig_punctuation('', ','))\n+        result.append(nodes.Text(' '))\n+        \n+        # Process second type parameter\n+        value_type = type_to_xref(parts[1].strip(), env)\n+        result.append(value_type)\n+        result.append(addnodes.desc_sig_punctuation('', ')'))\n+        \n+        return addnodes.desc_type('', '', *result)\n+    \n+    # Fallback for malformed dictionary types\n+    return addnodes.desc_type('', '', type_to_xref('dict', env))\n \n-    for node in content:\n-        if isinstance(node, nodes.field_list):\n-            fields = cast(List[nodes.field], node)\n-            for field in fields:\n-                field_name = cast(nodes.field_body, field[0]).astext().strip()\n-                if field_name == 'meta' or field_name.startswith('meta '):\n-                    node.remove(field)\n-                    break\n-\n-\n-class PythonModuleIndex(Index):\n-    \"\"\"\n-    Index subclass to provide the Python module index.\n-    \"\"\"\n-\n-    name = 'modindex'\n-    localname = _('Python Module Index')\n-    shortname = _('modules')\n-\n-    def generate(self, docnames: Iterable[str] = None\n-                 ) -> Tuple[List[Tuple[str, List[IndexEntry]]], bool]:\n-        content: Dict[str, List[IndexEntry]] = {}\n-        # list of prefixes to ignore\n-        ignores: List[str] = self.domain.env.config['modindex_common_prefix']\n-        ignores = sorted(ignores, key=len, reverse=True)\n-        # list of all modules, sorted by module name\n-        modules = sorted(self.domain.data['modules'].items(),\n-                         key=lambda x: x[0].lower())\n-        # sort out collapsable modules\n-        prev_modname = ''\n-        num_toplevels = 0\n-        for modname, (docname, node_id, synopsis, platforms, deprecated) in modules:\n-            if docnames and docname not in docnames:\n-                continue\n-\n-            for ignore in ignores:\n-                if modname.startswith(ignore):\n-                    modname = modname[len(ignore):]\n-                    stripped = ignore\n-                    break\n-            else:\n-                stripped = ''\n-\n-            # we stripped the whole module name?\n-            if not modname:\n-                modname, stripped = stripped, ''\n-\n-            entries = content.setdefault(modname[0].lower(), [])\n-\n-            package = modname.split('.')[0]\n-            if package != modname:\n-                # it's a submodule\n-                if prev_modname == package:\n-                    # first submodule - make parent a group head\n-                    if entries:\n-                        last = entries[-1]\n-                        entries[-1] = IndexEntry(last[0], 1, last[2], last[3],\n-                                                 last[4], last[5], last[6])\n-                elif not prev_modname.startswith(package):\n-                    # submodule without parent in list, add dummy entry\n-                    entries.append(IndexEntry(stripped + package, 1, '', '', '', '', ''))\n-                subtype = 2\n-            else:\n-                num_toplevels += 1\n-                subtype = 0\n-\n-            qualifier = _('Deprecated') if deprecated else ''\n-            entries.append(IndexEntry(stripped + modname, subtype, docname,\n-                                      node_id, platforms, qualifier, synopsis))\n-            prev_modname = modname\n-\n-        # apply heuristics when to collapse modindex at page load:\n-        # only collapse if number of toplevel modules is larger than\n-        # number of submodules\n-        collapse = len(modules) - num_toplevels < num_toplevels\n-\n-        # sort by first letter\n-        sorted_content = sorted(content.items())\n-\n-        return sorted_content, collapse\n-\n-\n-class PythonDomain(Domain):\n-    \"\"\"Python language domain.\"\"\"\n-    name = 'py'\n-    label = 'Python'\n-    object_types: Dict[str, ObjType] = {\n-        'function':     ObjType(_('function'),      'func', 'obj'),\n-        'data':         ObjType(_('data'),          'data', 'obj'),\n-        'class':        ObjType(_('class'),         'class', 'exc', 'obj'),\n-        'exception':    ObjType(_('exception'),     'exc', 'class', 'obj'),\n-        'method':       ObjType(_('method'),        'meth', 'obj'),\n-        'classmethod':  ObjType(_('class method'),  'meth', 'obj'),\n-        'staticmethod': ObjType(_('static method'), 'meth', 'obj'),\n-        'attribute':    ObjType(_('attribute'),     'attr', 'obj'),\n-        'property':     ObjType(_('property'),      'attr', '_prop', 'obj'),\n-        'module':       ObjType(_('module'),        'mod', 'obj'),\n-    }\n-\n-    directives = {\n-        'function':        PyFunction,\n-        'data':            PyVariable,\n-        'class':           PyClasslike,\n-        'exception':       PyClasslike,\n-        'method':          PyMethod,\n-        'classmethod':     PyClassMethod,\n-        'staticmethod':    PyStaticMethod,\n-        'attribute':       PyAttribute,\n-        'property':        PyProperty,\n-        'module':          PyModule,\n-        'currentmodule':   PyCurrentModule,\n-        'decorator':       PyDecoratorFunction,\n-        'decoratormethod': PyDecoratorMethod,\n-    }\n-    roles = {\n-        'data':  PyXRefRole(),\n-        'exc':   PyXRefRole(),\n-        'func':  PyXRefRole(fix_parens=True),\n-        'class': PyXRefRole(),\n-        'const': PyXRefRole(),\n-        'attr':  PyXRefRole(),\n-        'meth':  PyXRefRole(fix_parens=True),\n-        'mod':   PyXRefRole(),\n-        'obj':   PyXRefRole(),\n-    }\n-    initial_data: Dict[str, Dict[str, Tuple[Any]]] = {\n-        'objects': {},  # fullname -> docname, objtype\n-        'modules': {},  # modname -> docname, synopsis, platform, deprecated\n-    }\n-    indices = [\n-        PythonModuleIndex,\n-    ]\n-\n-    @property\n-    def objects(self) -> Dict[str, ObjectEntry]:\n-        return self.data.setdefault('objects', {})  # fullname -> ObjectEntry\n-\n-    def note_object(self, name: str, objtype: str, node_id: str,\n-                    aliased: bool = False, location: Any = None) -> None:\n-        \"\"\"Note a python object for cross reference.\n-\n-        .. versionadded:: 2.1\n-        \"\"\"\n-        if name in self.objects:\n-            other = self.objects[name]\n-            if other.aliased and aliased is False:\n-                # The original definition found. Override it!\n-                pass\n-            elif other.aliased is False and aliased:\n-                # The original definition is already registered.\n-                return\n-            else:\n-                # duplicated\n-                logger.warning(__('duplicate object description of %s, '\n-                                  'other instance in %s, use :noindex: for one of them'),\n-                               name, other.docname, location=location)\n-        self.objects[name] = ObjectEntry(self.env.docname, node_id, objtype, aliased)\n-\n-    @property\n-    def modules(self) -> Dict[str, ModuleEntry]:\n-        return self.data.setdefault('modules', {})  # modname -> ModuleEntry\n-\n-    def note_module(self, name: str, node_id: str, synopsis: str,\n-                    platform: str, deprecated: bool) -> None:\n-        \"\"\"Note a python module for cross reference.\n-\n-        .. versionadded:: 2.1\n-        \"\"\"\n-        self.modules[name] = ModuleEntry(self.env.docname, node_id,\n-                                         synopsis, platform, deprecated)\n-\n-    def clear_doc(self, docname: str) -> None:\n-        for fullname, obj in list(self.objects.items()):\n-            if obj.docname == docname:\n-                del self.objects[fullname]\n-        for modname, mod in list(self.modules.items()):\n-            if mod.docname == docname:\n-                del self.modules[modname]\n-\n-    def merge_domaindata(self, docnames: List[str], otherdata: Dict) -> None:\n-        # XXX check duplicates?\n-        for fullname, obj in otherdata['objects'].items():\n-            if obj.docname in docnames:\n-                self.objects[fullname] = obj\n-        for modname, mod in otherdata['modules'].items():\n-            if mod.docname in docnames:\n-                self.modules[modname] = mod\n-\n-    def find_obj(self, env: BuildEnvironment, modname: str, classname: str,\n-                 name: str, type: str, searchmode: int = 0\n-                 ) -> List[Tuple[str, ObjectEntry]]:\n-        \"\"\"Find a Python object for \"name\", perhaps using the given module\n-        and/or classname.  Returns a list of (name, object entry) tuples.\n-        \"\"\"\n-        # skip parens\n-        if name[-2:] == '()':\n-            name = name[:-2]\n-\n-        if not name:\n-            return []\n-\n-        matches: List[Tuple[str, ObjectEntry]] = []\n-\n-        newname = None\n-        if searchmode == 1:\n-            if type is None:\n-                objtypes = list(self.object_types)\n-            else:\n-                objtypes = self.objtypes_for_role(type)\n-            if objtypes is not None:\n-                if modname and classname:\n-                    fullname = modname + '.' + classname + '.' + name\n-                    if fullname in self.objects and self.objects[fullname].objtype in objtypes:\n-                        newname = fullname\n-                if not newname:\n-                    if modname and modname + '.' + name in self.objects and \\\n-                       self.objects[modname + '.' + name].objtype in objtypes:\n-                        newname = modname + '.' + name\n-                    elif name in self.objects and self.objects[name].objtype in objtypes:\n-                        newname = name\n-                    else:\n-                        # \"fuzzy\" searching mode\n-                        searchname = '.' + name\n-                        matches = [(oname, self.objects[oname]) for oname in self.objects\n-                                   if oname.endswith(searchname) and\n-                                   self.objects[oname].objtype in objtypes]\n-        else:\n-            # NOTE: searching for exact match, object type is not considered\n-            if name in self.objects:\n-                newname = name\n-            elif type == 'mod':\n-                # only exact matches allowed for modules\n-                return []\n-            elif classname and classname + '.' + name in self.objects:\n-                newname = classname + '.' + name\n-            elif modname and modname + '.' + name in self.objects:\n-                newname = modname + '.' + name\n-            elif modname and classname and \\\n-                    modname + '.' + classname + '.' + name in self.objects:\n-                newname = modname + '.' + classname + '.' + name\n-        if newname is not None:\n-            matches.append((newname, self.objects[newname]))\n-        return matches\n-\n-    def resolve_xref(self, env: BuildEnvironment, fromdocname: str, builder: Builder,\n-                     type: str, target: str, node: pending_xref, contnode: Element\n-                     ) -> Optional[Element]:\n-        modname = node.get('py:module')\n-        clsname = node.get('py:class')\n-        searchmode = 1 if node.hasattr('refspecific') else 0\n-        matches = self.find_obj(env, modname, clsname, target,\n-                                type, searchmode)\n-\n-        if not matches and type == 'attr':\n-            # fallback to meth (for property; Sphinx-2.4.x)\n-            # this ensures that `:attr:` role continues to refer to the old property entry\n-            # that defined by ``method`` directive in old reST files.\n-            matches = self.find_obj(env, modname, clsname, target, 'meth', searchmode)\n-        if not matches and type == 'meth':\n-            # fallback to attr (for property)\n-            # this ensures that `:meth:` in the old reST files can refer to the property\n-            # entry that defined by ``property`` directive.\n-            #\n-            # Note: _prop is a secret role only for internal look-up.\n-            matches = self.find_obj(env, modname, clsname, target, '_prop', searchmode)\n-\n-        if not matches:\n-            return None\n-        elif len(matches) > 1:\n-            canonicals = [m for m in matches if not m[1].aliased]\n-            if len(canonicals) == 1:\n-                matches = canonicals\n-            else:\n-                logger.warning(__('more than one target found for cross-reference %r: %s'),\n-                               target, ', '.join(match[0] for match in matches),\n-                               type='ref', subtype='python', location=node)\n-        name, obj = matches[0]\n-\n-        if obj[2] == 'module':\n-            return self._make_module_refnode(builder, fromdocname, name, contnode)\n-        else:\n-            # determine the content of the reference by conditions\n-            content = find_pending_xref_condition(node, 'resolved')\n-            if content:\n-                children = content.children\n-            else:\n-                # if not found, use contnode\n-                children = [contnode]\n-\n-            return make_refnode(builder, fromdocname, obj[0], obj[1], children, name)\n-\n-    def resolve_any_xref(self, env: BuildEnvironment, fromdocname: str, builder: Builder,\n-                         target: str, node: pending_xref, contnode: Element\n-                         ) -> List[Tuple[str, Element]]:\n-        modname = node.get('py:module')\n-        clsname = node.get('py:class')\n-        results: List[Tuple[str, Element]] = []\n-\n-        # always search in \"refspecific\" mode with the :any: role\n-        matches = self.find_obj(env, modname, clsname, target, None, 1)\n-        for name, obj in matches:\n-            if obj[2] == 'module':\n-                results.append(('py:mod',\n-                                self._make_module_refnode(builder, fromdocname,\n-                                                          name, contnode)))\n-            else:\n-                # determine the content of the reference by conditions\n-                content = find_pending_xref_condition(node, 'resolved')\n-                if content:\n-                    children = content.children\n-                else:\n-                    # if not found, use contnode\n-                    children = [contnode]\n-\n-                results.append(('py:' + self.role_for_objtype(obj[2]),\n-                                make_refnode(builder, fromdocname, obj[0], obj[1],\n-                                             children, name)))\n-        return results\n-\n-    def _make_module_refnode(self, builder: Builder, fromdocname: str, name: str,\n-                             contnode: Node) -> Element:\n-        # get additional info for modules\n-        module = self.modules[name]\n-        title = name\n-        if module.synopsis:\n-            title += ': ' + module.synopsis\n-        if module.deprecated:\n-            title += _(' (deprecated)')\n-        if module.platform:\n-            title += ' (' + module.platform + ')'\n-        return make_refnode(builder, fromdocname, module.docname, module.node_id,\n-                            contnode, title)\n-\n-    def get_objects(self) -> Iterator[Tuple[str, str, str, str, str, int]]:\n-        for modname, mod in self.modules.items():\n-            yield (modname, modname, 'module', mod.docname, mod.node_id, 0)\n-        for refname, obj in self.objects.items():\n-            if obj.objtype != 'module':  # modules are already handled\n-                if obj.aliased:\n-                    # aliased names are not full-text searchable.\n-                    yield (refname, refname, obj.objtype, obj.docname, obj.node_id, -1)\n-                else:\n-                    yield (refname, refname, obj.objtype, obj.docname, obj.node_id, 1)\n-\n-    def get_full_qualified_name(self, node: Element) -> Optional[str]:\n-        modname = node.get('py:module')\n-        clsname = node.get('py:class')\n-        target = node.get('reftarget')\n-        if target is None:\n-            return None\n-        else:\n-            return '.'.join(filter(None, [modname, clsname, target]))\n-\n-\n-def builtin_resolver(app: Sphinx, env: BuildEnvironment,\n-                     node: pending_xref, contnode: Element) -> Element:\n-    \"\"\"Do not emit nitpicky warnings for built-in types.\"\"\"\n-    def istyping(s: str) -> bool:\n-        if s.startswith('typing.'):\n-            s = s.split('.', 1)[1]\n-\n-        return s in typing.__all__  # type: ignore\n+def type_to_xref(text: str, env: BuildEnvironment = None) -> addnodes.pending_xref:\n+    \"\"\"Convert a type string to a cross reference node.\"\"\"\n+    # Special handling for dictionary type annotations\n+    if text.startswith('dict(') and text.endswith(')'):\n+        return handle_dict_type(text, env)\n \n-    content = find_pending_xref_condition(node, 'resolved')\n-    if content:\n-        contnode = content.children[0]  # type: ignore\n+    if text == 'None':\n+        reftype = 'obj'\n+    else:\n+        reftype = 'class'\n \n-    if node.get('refdomain') != 'py':\n-        return None\n-    elif node.get('reftype') in ('class', 'obj') and node.get('reftarget') == 'None':\n-        return contnode\n-    elif node.get('reftype') in ('class', 'exc'):\n-        reftarget = node.get('reftarget')\n-        if inspect.isclass(getattr(builtins, reftarget, None)):\n-            # built-in class\n-            return contnode\n-        elif istyping(reftarget):\n-            # typing class\n-            return contnode\n+    contnodes: List[Node] = [nodes.Text(text)]\n+    if env:\n+        try:\n+            if getattr(env.config, 'python_use_unqualified_type_names', False):\n+                shortname = text.split('.')[-1]\n+                contnodes = [pending_xref_condition('', shortname, condition='resolved'),\n+                           pending_xref_condition('', text, condition='*')]\n+        except AttributeError:\n+            pass\n \n-    return None\n+    return pending_xref('', *contnodes,\n+                       refdomain='py', reftype=reftype, reftarget=text)\n \n+def _parse_annotation(annotation: str, env: BuildEnvironment = None) -> List[Node]:\n+    \"\"\"Parse type annotation.\"\"\"\n+    # Special handling for dictionary types\n+    if annotation.startswith('dict(') and annotation.endswith(')'):\n+        return [handle_dict_type(annotation, env)]\n \n-def setup(app: Sphinx) -> Dict[str, Any]:\n-    app.setup_extension('sphinx.directives')\n+    if env is None:\n+        warnings.warn(\"The env parameter for _parse_annotation becomes required now.\",\n+                      RemovedInSphinx50Warning, stacklevel=2)\n \n-    app.add_domain(PythonDomain)\n-    app.add_config_value('python_use_unqualified_type_names', False, 'env')\n-    app.connect('object-description-transform', filter_meta_fields)\n-    app.connect('missing-reference', builtin_resolver, priority=900)\n+    try:\n+        tree = ast_parse(annotation)\n+        result = unparse(tree)\n+        for i, node in enumerate(result):\n+            if isinstance(node, nodes.Text) and node.strip():\n+                result[i] = type_to_xref(str(node), env)\n+        return result\n+    except SyntaxError:\n+        return [handle_dict_type(annotation, env) if annotation.startswith('dict(') \n+               else type_to_xref(annotation, env)]\n+\n+def unparse(node: ast.AST) -> List[Node]:\n+    \"\"\"Convert an AST node to docutils nodes.\"\"\"\n+    if isinstance(node, ast.Attribute):\n+        return [nodes.Text(\"%s.%s\" % (unparse(node.value)[0], node.attr))]\n+    elif isinstance(node, ast.BinOp):\n+        result: List[Node] = unparse(node.left)\n+        result.extend(unparse(node.op))\n+        result.extend(unparse(node.right))\n+        return result\n+    elif isinstance(node, ast.BitOr):\n+        return [nodes.Text(' '), addnodes.desc_sig_punctuation('', '|'), nodes.Text(' ')]\n+    elif isinstance(node, ast.Constant):  # type: ignore\n+        if node.value is Ellipsis:\n+            return [addnodes.desc_sig_punctuation('', \"...\")]\n+        else:\n+            return [nodes.Text(str(node.value))]\n+    elif isinstance(node, ast.Expr):\n+        return unparse(node.value)\n+    elif isinstance(node, ast.Index):\n+        return unparse(node.value)\n+    elif isinstance(node, ast.List):\n+        result = [addnodes.desc_sig_punctuation('', '[')]\n+        for elem in node.elts:\n+            result.extend(unparse(elem))\n+            result.append(addnodes.desc_sig_punctuation('', ', '))\n+        result.pop()\n+        result.append(addnodes.desc_sig_punctuation('', ']'))\n+        return result\n+    elif isinstance(node, ast.Module):\n+        return sum((unparse(e) for e in node.body), [])\n+    elif isinstance(node, ast.Name):\n+        return [nodes.Text(node.id)]\n+    elif isinstance(node, ast.Subscript):\n+        result = unparse(node.value)\n+        result.append(addnodes.desc_sig_punctuation('', '['))\n+        result.extend(unparse(node.slice))\n+        result.append(addnodes.desc_sig_punctuation('', ']'))\n+        return result\n+    elif isinstance(node, ast.Tuple):\n+        if node.elts:\n+            result = []\n+            for elem in node.elts:\n+                result.extend(unparse(elem))\n+                result.append(addnodes.desc_sig_punctuation('', ', '))\n+            result.pop()\n+            return result\n+        return [addnodes.desc_sig_punctuation('', '('),\n+                addnodes.desc_sig_punctuation('', ')')]\n+    else:\n+        if sys.version_info < (3, 8):\n+            if isinstance(node, ast.Ellipsis):\n+                return [addnodes.desc_sig_punctuation('', \"...\")]\n+            elif isinstance(node, ast.NameConstant):\n+                return [nodes.Text(str(node.value))]\n \n-    return {\n-        'version': 'builtin',\n-        'env_version': 3,\n-        'parallel_read_safe': True,\n-        'parallel_write_safe': True,\n-    }\n+        raise SyntaxError  # unsupported syntax\n\\ No newline at end of file\ndiff --git a/tox.ini b/tox.ini\nindex a363e187f..3b8bc12d0 100644\n--- a/tox.ini\n+++ b/tox.ini\n@@ -27,7 +27,7 @@ setenv =\n     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils,ignore::DeprecationWarning:pip._vendor.packaging.version\n     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes\n commands=\n-    python -X dev -m pytest --durations 25 {posargs}\n+    python -X dev -m pytest -rA --durations 25 {posargs}\n \n [testenv:flake8]\n basepython = python3\ndiff --git a/tests/roots/test-docstring-dict-param/conf.py b/tests/roots/test-docstring-dict-param/conf.py\nnew file mode 100644\nindex *********..ee4ec8330\n--- /dev/null\n+++ b/tests/roots/test-docstring-dict-param/conf.py\n@@ -0,0 +1,4 @@\n+extensions = [\n+    'sphinx.ext.autodoc',\n+    'sphinx.ext.napoleon',\n+]\n\\ No newline at end of file\ndiff --git a/tests/roots/test-docstring-dict-param/index.rst b/tests/roots/test-docstring-dict-param/index.rst\nnew file mode 100644\nindex *********..c5b2cdd16\n--- /dev/null\n+++ b/tests/roots/test-docstring-dict-param/index.rst\n@@ -0,0 +1,6 @@\n+Test Dict Parameter\n+================\n+\n+.. py:function:: test_func(opc_meta)\n+   \n+   :param dict(str, str) opc_meta: (optional)\n\\ No newline at end of file\ndiff --git a/tests/roots/test-docstring_dict_param/conf.py b/tests/roots/test-docstring_dict_param/conf.py\nnew file mode 100644\nindex *********..73b0f7bd7\n--- /dev/null\n+++ b/tests/roots/test-docstring_dict_param/conf.py\n@@ -0,0 +1 @@\n+extensions = ['sphinx.ext.autodoc']\n\\ No newline at end of file\ndiff --git a/tests/roots/test-docstring_dict_param/index.rst b/tests/roots/test-docstring_dict_param/index.rst\nnew file mode 100644\nindex *********..c5b2cdd16\n--- /dev/null\n+++ b/tests/roots/test-docstring_dict_param/index.rst\n@@ -0,0 +1,6 @@\n+Test Dict Parameter\n+================\n+\n+.. py:function:: test_func(opc_meta)\n+   \n+   :param dict(str, str) opc_meta: (optional)\n\\ No newline at end of file\ndiff --git a/tests/test_dict_type_parsing.py b/tests/test_dict_type_parsing.py\nnew file mode 100644\nindex *********..d962b66d1\n--- /dev/null\n+++ b/tests/test_dict_type_parsing.py\n@@ -0,0 +1,60 @@\n+\"\"\"Test parsing of dictionary type annotations.\"\"\"\n+\n+import pytest\n+from docutils.nodes import Text\n+from sphinx.addnodes import desc_type, desc_sig_punctuation, pending_xref\n+from sphinx.domains.python import _parse_annotation, handle_dict_type\n+from sphinx.environment import BuildEnvironment\n+from sphinx.config import Config\n+\n+class DummyConfig:\n+    python_use_unqualified_type_names = False\n+\n+def test_parse_dict_annotation():\n+    \"\"\"Test parsing of dict type annotations.\"\"\"\n+    annotation = 'dict(str, str)'\n+    config = DummyConfig()\n+    env = BuildEnvironment()\n+    env.config = config\n+    \n+    result = _parse_annotation(annotation, env)\n+    \n+    # Verify we got a result\n+    assert result\n+    assert len(result) == 1\n+    assert isinstance(result[0], desc_type)\n+    \n+    # Extract all text nodes to verify content\n+    def get_text(node):\n+        if isinstance(node, Text):\n+            return node.astext()\n+        elif isinstance(node, desc_sig_punctuation):\n+            return node.astext()\n+        elif isinstance(node, pending_xref):\n+            return node.astext()\n+        text = ''\n+        for child in node.children:\n+            text += get_text(child)\n+        return text\n+    \n+    text = get_text(result[0])\n+    \n+    # Check for expected components\n+    assert 'dict' in text\n+    assert text.count('str') == 2  # Two str type parameters\n+    assert '(' in text and ')' in text  # parentheses\n+    assert ',' in text  # comma separator\n+\n+def test_malformed_dict_annotation():\n+    \"\"\"Test handling of malformed dictionary type annotations.\"\"\"\n+    annotation = 'dict(str)'  # Missing second parameter\n+    config = DummyConfig()\n+    env = BuildEnvironment()\n+    env.config = config\n+    \n+    result = _parse_annotation(annotation, env)\n+    \n+    # Should still return valid nodes\n+    assert result\n+    text = result[0].astext()\n+    assert 'dict' in text\n\\ No newline at end of file\ndiff --git a/tests/test_docstring_dict_param.py b/tests/test_docstring_dict_param.py\nnew file mode 100644\nindex *********..3522d375c\n--- /dev/null\n+++ b/tests/test_docstring_dict_param.py\n@@ -0,0 +1,14 @@\n+\"\"\"Test docstring parameter rendering with dict types.\"\"\"\n+\n+import pytest\n+from sphinx.domains.python import _parse_annotation\n+\n+\*********************('text', testroot='docstring-dict-param')\n+def test_dict_param_rendering(app, status, warning):\n+    app.builder.build_all()\n+    \n+    text = (app.outdir / 'index.txt').read_text()\n+    # Text should contain the properly formatted parameter\n+    assert 'dict(str, str) opc_meta' in text\n+    assert '(optional)' in text\n\\ No newline at end of file\n", "proposed_model_patches": []}