+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=2
++ CONDA_SHLVL=2
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_1=/opt/miniconda3
++ CONDA_PREFIX_1=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ cd /testbed
+ git config --global --add safe.directory /testbed
+ cd /testbed
+ git status
On branch master
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   tox.ini

Untracked files:
  (use "git add <file>..." to include in what will be committed)
	setup.py.orig

no changes added to commit (use "git add" and/or "git commit -a")
+ git show
commit 2e506c5ab457cba743bb47eb5b8c8eb9dd51d23d
Merge: 017af4ce2 25fc47e6b
Author: Takeshi KOMIYA <<EMAIL>>
Date:   Sat May 2 01:46:02 2020 +0900

    Merge pull request #7571 from tk0miya/7559_misdetects_async
    
    Fix #7559: autodoc: misdetects a sync function is async

+ git diff 2e506c5ab457cba743bb47eb5b8c8eb9dd51d23d
diff --git a/tox.ini b/tox.ini
index d9f040544..bf39854b6 100644
--- a/tox.ini
+++ b/tox.ini
@@ -28,7 +28,7 @@ extras =
 setenv =
     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils
 commands=
-    pytest --durations 25 {posargs}
+    pytest -rA --durations 25 {posargs}
 
 [testenv:flake8]
 basepython = python3
+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z x ']'
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1='(testbed) '
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=3
+++ CONDA_SHLVL=3
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=4
++ CONDA_SHLVL=4
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_3=/opt/miniconda3
++ CONDA_PREFIX_3=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ python -m pip install -e '.[test]'
Obtaining file:///testbed
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: sphinxcontrib-applehelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.0.4)
Requirement already satisfied: sphinxcontrib-devhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.0.2)
Requirement already satisfied: sphinxcontrib-jsmath in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.0.1)
Requirement already satisfied: sphinxcontrib-htmlhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (2.0.1)
Requirement already satisfied: sphinxcontrib-serializinghtml in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.1.5)
Requirement already satisfied: sphinxcontrib-qthelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.0.3)
Requirement already satisfied: Jinja2>=2.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (2.11.3)
Requirement already satisfied: Pygments>=2.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (2.19.1)
Requirement already satisfied: docutils>=0.12 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (0.21.2)
Requirement already satisfied: snowballstemmer>=1.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (2.2.0)
Requirement already satisfied: babel>=1.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (2.17.0)
Requirement already satisfied: alabaster<0.8,>=0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (0.7.11)
Requirement already satisfied: imagesize in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.4.1)
Requirement already satisfied: requests>=2.5.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (2.32.3)
Requirement already satisfied: setuptools in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (75.8.0)
Requirement already satisfied: packaging in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (24.2)
Requirement already satisfied: pytest in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (8.3.4)
Requirement already satisfied: pytest-cov in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (6.0.0)
Requirement already satisfied: html5lib in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.1)
Requirement already satisfied: typed_ast in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (1.5.5)
Requirement already satisfied: cython in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.1.0.dev20250315) (3.0.11)
Requirement already satisfied: MarkupSafe>=0.23 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Jinja2>=2.3->Sphinx==3.1.0.dev20250315) (2.0.1)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.1.0.dev20250315) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.1.0.dev20250315) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.1.0.dev20250315) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.1.0.dev20250315) (2025.1.31)
Requirement already satisfied: six>=1.9 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.1.0.dev20250315) (1.17.0)
Requirement already satisfied: webencodings in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.1.0.dev20250315) (0.5.1)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.1.0.dev20250315) (1.2.2)
Requirement already satisfied: iniconfig in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.1.0.dev20250315) (2.0.0)
Requirement already satisfied: pluggy<2,>=1.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.1.0.dev20250315) (1.5.0)
Requirement already satisfied: tomli>=1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.1.0.dev20250315) (2.2.1)
Requirement already satisfied: coverage>=7.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from coverage[toml]>=7.5->pytest-cov->Sphinx==3.1.0.dev20250315) (7.6.10)
Installing collected packages: Sphinx
  Attempting uninstall: Sphinx
    Found existing installation: Sphinx 3.1.0.dev20250204
    Uninstalling Sphinx-3.1.0.dev20250204:
      Successfully uninstalled Sphinx-3.1.0.dev20250204
  DEPRECATION: Legacy editable install of Sphinx[test]==3.1.0.dev20250315 from file:///testbed (setup.py develop) is deprecated. pip 25.1 will enforce this behaviour change. A possible replacement is to add a pyproject.toml or enable --use-pep517, and use setuptools >= 64. If the resulting installation is not behaving as expected, try using --config-settings editable_mode=compat. Please consult the setuptools documentation for more information. Discussion can be found at https://github.com/pypa/pip/issues/11457
  Running setup.py develop for Sphinx
Successfully installed Sphinx
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
+ git checkout 2e506c5ab457cba743bb47eb5b8c8eb9dd51d23d tests/test_domain_cpp.py
Updated 0 paths from e3c9d6e97
+ git apply -v -
Checking patch tests/test_domain_cpp.py...
Applied patch tests/test_domain_cpp.py cleanly.
+ tox --current-env -epy39 -v -- tests/test_domain_cpp.py
py39: commands[0]> pytest -rA --durations 25 tests/test_domain_cpp.py
============================= test session starts ==============================
platform linux -- Python 3.9.21, pytest-8.3.4, pluggy-1.5.0
cachedir: .tox/py39/.pytest_cache
libraries: Sphinx-3.1.0+/2e506c5ab, docutils-0.21.2
base tempdir: /tmp/pytest-of-root/pytest-0
rootdir: /testbed
configfile: setup.cfg
plugins: cov-6.0.0
collected 25 items

tests/test_domain_cpp.py .F.......................                       [100%]

=================================== FAILURES ===================================
_______________________________ test_expressions _______________________________

    def test_expressions():
        def exprCheck(expr, id, id4=None):
            ids = 'IE1CIA%s_1aE'
            idDict = {2: ids % expr, 3: ids % id}
            if id4 is not None:
                idDict[4] = ids % id4
            check('class', 'template<> C<a[%s]>' % expr, idDict)
    
            class Config:
                cpp_id_attributes = ["id_attr"]
                cpp_paren_attributes = ["paren_attr"]
    
            parser = DefinitionParser(expr, location=None,
                                      config=Config())
            parser.allowFallbackExpressionParsing = False
            ast = parser.parse_expression()
            res = str(ast)
            if res != expr:
                print("")
                print("Input:    ", expr)
                print("Result:   ", res)
                raise DefinitionError("")
        # primary
        exprCheck('nullptr', 'LDnE')
        exprCheck('true', 'L1E')
        exprCheck('false', 'L0E')
        ints = ['5', '0', '075', '0x0123456789ABCDEF', '0XF', '0b1', '0B1']
        unsignedSuffix = ['', 'u', 'U']
        longSuffix = ['', 'l', 'L', 'll', 'LL']
        for i in ints:
            for u in unsignedSuffix:
                for l in longSuffix:
                    expr = i + u + l
                    exprCheck(expr, 'L' + expr + 'E')
                    expr = i + l + u
                    exprCheck(expr, 'L' + expr + 'E')
        decimalFloats = ['5e42', '5e+42', '5e-42',
                      '5.', '5.e42', '5.e+42', '5.e-42',
                      '.5', '.5e42', '.5e+42', '.5e-42',
                      '5.0', '5.0e42', '5.0e+42', '5.0e-42']
        hexFloats = ['ApF', 'Ap+F', 'Ap-F',
                     'A.', 'A.pF', 'A.p+F', 'A.p-F',
                     '.A', '.ApF', '.Ap+F', '.Ap-F',
                     'A.B', 'A.BpF', 'A.Bp+F', 'A.Bp-F']
        for suffix in ['', 'f', 'F', 'l', 'L']:
            for e in decimalFloats:
                expr = e + suffix
                exprCheck(expr, 'L' + expr + 'E')
            for e in hexFloats:
                expr = "0x" + e + suffix
                exprCheck(expr, 'L' + expr + 'E')
        exprCheck('"abc\\"cba"', 'LA8_KcE')  # string
        exprCheck('this', 'fpT')
        # character literals
        charPrefixAndIds = [('', 'c'), ('u8', 'c'), ('u', 'Ds'), ('U', 'Di'), ('L', 'w')]
        chars = [('a', '97'), ('\\n', '10'), ('\\012', '10'), ('\\0', '0'),
                 ('\\x0a', '10'), ('\\x0A', '10'), ('\\u0a42', '2626'), ('\\u0A42', '2626'),
                 ('\\U0001f34c', '127820'), ('\\U0001F34C', '127820')]
        for p, t in charPrefixAndIds:
            for c, val in chars:
                exprCheck("{}'{}'".format(p, c), t + val)
        # user-defined literals
        for i in ints:
>           exprCheck(i + '_udl', 'clL_Zli4_udlEL' + i + 'EE')

tests/test_domain_cpp.py:176: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
tests/test_domain_cpp.py:119: in exprCheck
    check('class', 'template<> C<a[%s]>' % expr, idDict)
tests/test_domain_cpp.py:88: in check
    _check(name, input, idDict, output)
tests/test_domain_cpp.py:38: in _check
    ast = parse(name, input)
tests/test_domain_cpp.py:29: in parse
    parser.assert_end()
sphinx/util/cfamily.py:348: in assert_end
    self.fail('Expected end of definition.')
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <sphinx.domains.cpp.DefinitionParser object at 0x7d3cdd9ed040>
msg = 'Expected end of definition.'

    def fail(self, msg: str) -> None:
        errors = []
        indicator = '-' * self.pos + '^'
        exMain = DefinitionError(
            'Invalid %s declaration: %s [error at %d]\n  %s\n  %s' %
            (self.language, msg, self.pos, self.definition, indicator))
        errors.append((exMain, "Main error"))
        for err in self.otherErrors:
            errors.append((err, "Potential other error"))
        self.otherErrors = []
>       raise self._make_multi_error(errors, '')
E       sphinx.util.cfamily.DefinitionError: 
E       Main error:
E         Invalid C++ declaration: Expected end of definition. [error at 12]
E           template<> C<a[5_udl]>
E           ------------^
E       Potential other error:
E         Error in parsing template argument list.
E         If type argument:
E           Error in declarator or parameters-and-qualifiers
E           If pointer to member declarator:
E             Main error:
E               Invalid C++ declaration: Expected identifier in nested name. [error at 14]
E                 template<> C<a[5_udl]>
E                 --------------^
E             Potential other error:
E               Error in parsing template argument list.
E               If type argument:
E                 Error in declarator or parameters-and-qualifiers
E                 If pointer to member declarator:
E                   Invalid C++ declaration: Expected identifier in nested name. [error at 14]
E                     template<> C<a[5_udl]>
E                     --------------^
E                 If declarator-id:
E                   Invalid C++ declaration: Expected ']' in end of array operator. [error at 16]
E                     template<> C<a[5_udl]>
E                     ----------------^
E               If non-type argument:
E                 Invalid C++ declaration: Expected ']' in end of postfix expression. [error at 16]
E                   template<> C<a[5_udl]>
E                   ----------------^
E           If declarator-id:
E             Invalid C++ declaration: Expected ']' in end of array operator. [error at 16]
E               template<> C<a[5_udl]>
E               ----------------^
E         If non-type argument:
E           Invalid C++ declaration: Expected ']' in end of postfix expression. [error at 16]
E             template<> C<a[5_udl]>
E             ----------------^

sphinx/util/cfamily.py:279: DefinitionError
=============================== warnings summary ===============================
sphinx/util/docutils.py:45
  /testbed/sphinx/util/docutils.py:45: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
    __version_info__ = tuple(LooseVersion(docutils.__version__).version)

sphinx/registry.py:22
  /testbed/sphinx/registry.py:22: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
    from pkg_resources import iter_entry_points

../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('sphinxcontrib')`.
  Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
    declare_namespace(pkg)

sphinx/directives/patches.py:15
  /testbed/sphinx/directives/patches.py:15: DeprecationWarning: The `docutils.parsers.rst.directive.html` module will be removed in Docutils 2.0. Since Docutils 0.18, the "Meta" node is defined in `docutils.parsers.rst.directives.misc`.
    from docutils.parsers.rst.directives import images, html, tables

tests/test_domain_cpp.py: 826 warnings
  /testbed/sphinx/domains/cpp.py:838: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(txt, txt))

tests/test_domain_cpp.py: 10 warnings
  /testbed/sphinx/domains/cpp.py:855: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(txt, txt))

tests/test_domain_cpp.py: 104 warnings
  /testbed/sphinx/domains/cpp.py:882: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(txt, txt))

tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
tests/test_domain_cpp.py::test_member_definitions
  /testbed/sphinx/domains/cpp.py:2283: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(' : ', ' : '))

tests/test_domain_cpp.py::test_class_definitions
tests/test_domain_cpp.py::test_class_definitions
  /testbed/sphinx/domains/cpp.py:971: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text('(', '('))

tests/test_domain_cpp.py::test_class_definitions
tests/test_domain_cpp.py::test_class_definitions
  /testbed/sphinx/domains/cpp.py:973: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(')', ')'))

tests/test_domain_cpp.py: 18 warnings
  /testbed/sphinx/util/cfamily.py:135: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(txt, txt))

tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
  /testbed/sphinx/util/cfamily.py:169: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(txt, txt))

tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
  /testbed/sphinx/util/cfamily.py:182: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(self.id, self.id))

tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
tests/test_domain_cpp.py::test_attributes
  /testbed/sphinx/util/cfamily.py:197: DeprecationWarning: nodes.Text: initialization argument "rawsource" is ignored and will be removed in Docutils 2.0.
    signode.append(nodes.Text(txt, txt))

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:210: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse():  # type: Node

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/i18n.py:88: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.translatable):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:110: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for ref in self.document.traverse(nodes.substitution_reference):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:131: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.target):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:150: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.block_quote):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:175: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.Element):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:222: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.index):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/references.py:30: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.substitution_definition):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:189: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.section):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:279: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.doctest_block):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/domains/citation.py:117: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.citation):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/domains/citation.py:136: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.citation_reference):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/builders/latex/transforms.py:37: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: nodes.Element

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:291: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: Element

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/util/compat.py:44: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.index):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/domains/index.py:52: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in document.traverse(addnodes.index):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/domains/math.py:85: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    self.data['has_equations'][docname] = any(document.traverse(math_node))

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/environment/collectors/asset.py:47: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/environment/collectors/asset.py:124: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(addnodes.download_reference):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/environment/collectors/title.py:46: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.section):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:301: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.system_message):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/__init__.py:384: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.manpage):

tests/test_domain_cpp.py: 65 warnings
  /testbed/sphinx/transforms/i18n.py:484: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for inline in self.document.traverse(matcher):  # type: nodes.inline

tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
tests/test_domain_cpp.py::test_xref_consistency
  /testbed/sphinx/builders/html/__init__.py:415: DeprecationWarning: The frontend.OptionParser class will be replaced by a subclass of argparse.ArgumentParser in Docutils 0.21 or later.
    self.docsettings = OptionParser(

tests/test_domain_cpp.py: 648 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/optparse.py:1000: DeprecationWarning: The frontend.Option class will be removed in Docutils 0.21 or later.
    option = self.option_class(*args, **kwargs)

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/domains/cpp.py:6768: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(AliasNode):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/transforms/post_transforms/__init__.py:71: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.pending_xref):

tests/test_domain_cpp.py: 234 warnings
  /testbed/sphinx/util/nodes.py:596: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in document.traverse(addnodes.only):

tests/test_domain_cpp.py: 234 warnings
  /testbed/sphinx/transforms/post_transforms/images.py:36: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.image):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/transforms/post_transforms/__init__.py:214: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.desc_sig_element):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/builders/latex/transforms.py:595: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.title):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:44: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.highlightlang):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:99: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for lbnode in self.document.traverse(nodes.literal_block):  # type: nodes.literal_block

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:103: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for dbnode in self.document.traverse(nodes.doctest_block):  # type: nodes.doctest_block

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/environment/__init__.py:541: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for toctreenode in doctree.traverse(addnodes.toctree):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/builders/__init__.py:182: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/builders/html/__init__.py:827: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_domain_cpp.py: 117 warnings
  /testbed/sphinx/environment/adapters/toctree.py:313: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in toc.traverse(nodes.reference):

tests/test_domain_cpp.py: 135 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:114: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.
    _gaq.push(['_setAllowLinker', true]);

tests/test_domain_cpp.py: 135 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/about.html:70: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py: 135 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/about.html:99: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py: 135 warnings
  /testbed/sphinx/environment/adapters/toctree.py:327: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for toctreenode in doctree.traverse(addnodes.toctree):

tests/test_domain_cpp.py: 135 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:215: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py: 135 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:238: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py: 45 warnings
  /testbed/sphinx/builders/latex/transforms.py:597: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for i, index in enumerate(node.traverse(addnodes.index)):

tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
tests/test_domain_cpp.py::test_xref_consistency
  <template>:33: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
tests/test_domain_cpp.py::test_xref_consistency
  <template>:224: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
tests/test_domain_cpp.py::test_xref_consistency
  <template>:386: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
tests/test_domain_cpp.py::test_xref_consistency
  <template>:401: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_cpp.py: 234 warnings
  /testbed/sphinx/util/nodes.py:348: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for img in node.traverse(nodes.image):

tests/test_domain_cpp.py: 234 warnings
  /testbed/sphinx/util/nodes.py:350: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for raw in node.traverse(nodes.raw):

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
==================================== PASSES ====================================
________________________________ test_templates ________________________________
------------------------------ Captured log call -------------------------------
WARNING  sphinx.sphinx.util.cfamily:logging.py:125 Too many template argument lists compared to parameter lists. Argument lists: 1, Parameter lists: 0, Extra empty parameters lists prepended: 1. Declaration:
	A<T>
WARNING  sphinx.sphinx.util.cfamily:logging.py:125 Too many template argument lists compared to parameter lists. Argument lists: 1, Parameter lists: 0, Extra empty parameters lists prepended: 1. Declaration:
	A<T>
___________________ test_build_domain_cpp_multi_decl_lookup ____________________
----------------------------- Captured stdout call -----------------------------
Filtered warnings for file 'lookup-key-overload':
Filtered warnings for file 'multi-decl-lookup':
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 13 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[  7%] [35manon-dup-decl[39;49;00m                                        
[01mreading sources... [39;49;00m[ 15%] [35many-role[39;49;00m                                             
[01mreading sources... [39;49;00m[ 23%] [35mbackslash[39;49;00m                                            
[01mreading sources... [39;49;00m[ 30%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 38%] [35mlookup-key-overload[39;49;00m                                  
[01mreading sources... [39;49;00m[ 46%] [35mmulti-decl-lookup[39;49;00m                                    
[01mreading sources... [39;49;00m[ 53%] [35mroles[39;49;00m                                                
[01mreading sources... [39;49;00m[ 61%] [35mroles-targets-ok[39;49;00m                                     
[01mreading sources... [39;49;00m[ 69%] [35mroles-targets-warn[39;49;00m                                   
[01mreading sources... [39;49;00m[ 76%] [35mroles2[39;49;00m                                               
[01mreading sources... [39;49;00m[ 84%] [35msemicolon[39;49;00m                                            
[01mreading sources... [39;49;00m[ 92%] [35mwarn-template-param-qualified-name[39;49;00m                   
[01mreading sources... [39;49;00m[100%] [35mxref_consistency[39;49;00m                                     
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:3: WARNING: Duplicate declaration, A[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/any-role.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/lookup-key-overload.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/multi-decl-lookup.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/semicolon.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/xref_consistency.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:2: WARNING: cpp:identifier reference target not found: @a[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:3: WARNING: cpp:identifier reference target not found: @b[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:45: WARNING: cpp:identifier reference target not found: MySpecificEnum[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:50: WARNING: cpp:identifier reference target not found: paren_5[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:51: WARNING: cpp:identifier reference target not found: paren_6[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:52: WARNING: cpp:identifier reference target not found: paren_7[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:53: WARNING: cpp:identifier reference target not found: paren_8[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst:49: WARNING: cpp:var reference target not found: Variables[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a enumerator (A).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:7: WARNING: cpp:type reference target not found: T::typeWarn[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:11: WARNING: cpp:type reference target not found: T::U::typeWarn[39;49;00m

___________ test_build_domain_cpp_warn_template_param_qualified_name ___________
----------------------------- Captured stdout call -----------------------------
Filtered warnings for file 'warn-template-param-qualified-name':
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:7: WARNING: cpp:type reference target not found: T::typeWarn[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:11: WARNING: cpp:type reference target not found: T::U::typeWarn[39;49;00m
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:2: WARNING: cpp:identifier reference target not found: @a[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:3: WARNING: cpp:identifier reference target not found: @b[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:45: WARNING: cpp:identifier reference target not found: MySpecificEnum[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:50: WARNING: cpp:identifier reference target not found: paren_5[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:51: WARNING: cpp:identifier reference target not found: paren_6[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:52: WARNING: cpp:identifier reference target not found: paren_7[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:53: WARNING: cpp:identifier reference target not found: paren_8[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst:49: WARNING: cpp:var reference target not found: Variables[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a enumerator (A).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:7: WARNING: cpp:type reference target not found: T::typeWarn[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:11: WARNING: cpp:type reference target not found: T::U::typeWarn[39;49;00m

______________________ test_build_domain_cpp_backslash_ok ______________________
----------------------------- Captured stdout call -----------------------------
Filtered warnings for file 'backslash':
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst:1: WARNING: Parsing of expression failed. Using fallback parser. Error was:
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[config changed ('strip_signature_backslash')] 13 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[  7%] [35manon-dup-decl[39;49;00m                                        
[01mreading sources... [39;49;00m[ 15%] [35many-role[39;49;00m                                             
[01mreading sources... [39;49;00m[ 23%] [35mbackslash[39;49;00m                                            
[01mreading sources... [39;49;00m[ 30%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 38%] [35mlookup-key-overload[39;49;00m                                  
[01mreading sources... [39;49;00m[ 46%] [35mmulti-decl-lookup[39;49;00m                                    
[01mreading sources... [39;49;00m[ 53%] [35mroles[39;49;00m                                                
[01mreading sources... [39;49;00m[ 61%] [35mroles-targets-ok[39;49;00m                                     
[01mreading sources... [39;49;00m[ 69%] [35mroles-targets-warn[39;49;00m                                   
[01mreading sources... [39;49;00m[ 76%] [35mroles2[39;49;00m                                               
[01mreading sources... [39;49;00m[ 84%] [35msemicolon[39;49;00m                                            
[01mreading sources... [39;49;00m[ 92%] [35mwarn-template-param-qualified-name[39;49;00m                   
[01mreading sources... [39;49;00m[100%] [35mxref_consistency[39;49;00m                                     
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst:1: WARNING: Parsing of expression failed. Using fallback parser. Error was:
Error in postfix expression, expected primary expression or type.
If primary expression:
  Invalid C++ declaration: Expected identifier in nested name. [error at 9]
    char c = '\'
    ---------^
If type:
  Invalid C++ declaration: Expected identifier in nested name. [error at 9]
    char c = '\'
    ---------^
[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/any-role.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/lookup-key-overload.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/multi-decl-lookup.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/semicolon.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/xref_consistency.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:2: WARNING: cpp:identifier reference target not found: @a[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:3: WARNING: cpp:identifier reference target not found: @b[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:45: WARNING: cpp:identifier reference target not found: MySpecificEnum[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:50: WARNING: cpp:identifier reference target not found: paren_5[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:51: WARNING: cpp:identifier reference target not found: paren_6[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:52: WARNING: cpp:identifier reference target not found: paren_7[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:53: WARNING: cpp:identifier reference target not found: paren_8[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst:49: WARNING: cpp:var reference target not found: Variables[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:7: WARNING: cpp:type reference target not found: T::typeWarn[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:11: WARNING: cpp:type reference target not found: T::U::typeWarn[39;49;00m

_______________________ test_build_domain_cpp_semicolon ________________________
----------------------------- Captured stdout call -----------------------------
Filtered warnings for file 'semicolon':
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[config changed ('strip_signature_backslash')] 13 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[  7%] [35manon-dup-decl[39;49;00m                                        
[01mreading sources... [39;49;00m[ 15%] [35many-role[39;49;00m                                             
[01mreading sources... [39;49;00m[ 23%] [35mbackslash[39;49;00m                                            
[01mreading sources... [39;49;00m[ 30%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 38%] [35mlookup-key-overload[39;49;00m                                  
[01mreading sources... [39;49;00m[ 46%] [35mmulti-decl-lookup[39;49;00m                                    
[01mreading sources... [39;49;00m[ 53%] [35mroles[39;49;00m                                                
[01mreading sources... [39;49;00m[ 61%] [35mroles-targets-ok[39;49;00m                                     
[01mreading sources... [39;49;00m[ 69%] [35mroles-targets-warn[39;49;00m                                   
[01mreading sources... [39;49;00m[ 76%] [35mroles2[39;49;00m                                               
[01mreading sources... [39;49;00m[ 84%] [35msemicolon[39;49;00m                                            
[01mreading sources... [39;49;00m[ 92%] [35mwarn-template-param-qualified-name[39;49;00m                   
[01mreading sources... [39;49;00m[100%] [35mxref_consistency[39;49;00m                                     
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/any-role.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/lookup-key-overload.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/multi-decl-lookup.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/semicolon.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/xref_consistency.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:2: WARNING: cpp:identifier reference target not found: @a[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:3: WARNING: cpp:identifier reference target not found: @b[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:45: WARNING: cpp:identifier reference target not found: MySpecificEnum[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:50: WARNING: cpp:identifier reference target not found: paren_5[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:51: WARNING: cpp:identifier reference target not found: paren_6[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:52: WARNING: cpp:identifier reference target not found: paren_7[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:53: WARNING: cpp:identifier reference target not found: paren_8[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst:49: WARNING: cpp:var reference target not found: Variables[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:7: WARNING: cpp:type reference target not found: T::typeWarn[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:11: WARNING: cpp:type reference target not found: T::U::typeWarn[39;49;00m

_____________________ test_build_domain_cpp_anon_dup_decl ______________________
----------------------------- Captured stdout call -----------------------------
Filtered warnings for file 'anon-dup-decl':
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:2: WARNING: cpp:identifier reference target not found: @a[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:3: WARNING: cpp:identifier reference target not found: @b[39;49;00m
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:2: WARNING: cpp:identifier reference target not found: @a[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst:3: WARNING: cpp:identifier reference target not found: @b[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:45: WARNING: cpp:identifier reference target not found: MySpecificEnum[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:50: WARNING: cpp:identifier reference target not found: paren_5[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:51: WARNING: cpp:identifier reference target not found: paren_6[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:52: WARNING: cpp:identifier reference target not found: paren_7[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/index.rst:53: WARNING: cpp:identifier reference target not found: paren_8[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst:49: WARNING: cpp:var reference target not found: Variables[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:7: WARNING: cpp:type reference target not found: T::typeWarn[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst:11: WARNING: cpp:type reference target not found: T::U::typeWarn[39;49;00m

____________________ test_build_domain_cpp_misuse_of_roles _____________________
----------------------------- Captured stdout call -----------------------------
Filtered warnings for file 'roles-targets-ok':
Filtered warnings for file 'roles-targets-warn':
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
Expected warnings:
WARNING: cpp:class targets a concept (
WARNING: cpp:class targets a enum (
WARNING: cpp:class targets a enumerator (
WARNING: cpp:class targets a function (
WARNING: cpp:class targets a functionParam (
WARNING: cpp:class targets a member (
WARNING: cpp:class targets a type (
WARNING: cpp:class targets a union (
WARNING: cpp:concept targets a class (
WARNING: cpp:concept targets a enum (
WARNING: cpp:concept targets a enumerator (
WARNING: cpp:concept targets a function (
WARNING: cpp:concept targets a functionParam (
WARNING: cpp:concept targets a member (
WARNING: cpp:concept targets a type (
WARNING: cpp:concept targets a union (
WARNING: cpp:enum targets a class (
WARNING: cpp:enum targets a concept (
WARNING: cpp:enum targets a enumerator (
WARNING: cpp:enum targets a function (
WARNING: cpp:enum targets a functionParam (
WARNING: cpp:enum targets a member (
WARNING: cpp:enum targets a type (
WARNING: cpp:enum targets a union (
WARNING: cpp:enumerator targets a class (
WARNING: cpp:enumerator targets a concept (
WARNING: cpp:enumerator targets a enum (
WARNING: cpp:enumerator targets a function (
WARNING: cpp:enumerator targets a functionParam (
WARNING: cpp:enumerator targets a member (
WARNING: cpp:enumerator targets a type (
WARNING: cpp:enumerator targets a union (
WARNING: cpp:func targets a class (
WARNING: cpp:func targets a concept (
WARNING: cpp:func targets a enum (
WARNING: cpp:func targets a enumerator (
WARNING: cpp:func targets a functionParam (
WARNING: cpp:func targets a member (
WARNING: cpp:func targets a type (
WARNING: cpp:func targets a union (
WARNING: cpp:member targets a class (
WARNING: cpp:member targets a concept (
WARNING: cpp:member targets a enum (
WARNING: cpp:member targets a enumerator (
WARNING: cpp:member targets a function (
WARNING: cpp:member targets a type (
WARNING: cpp:member targets a union (
WARNING: cpp:struct targets a concept (
WARNING: cpp:struct targets a enum (
WARNING: cpp:struct targets a enumerator (
WARNING: cpp:struct targets a function (
WARNING: cpp:struct targets a functionParam (
WARNING: cpp:struct targets a member (
WARNING: cpp:struct targets a type (
WARNING: cpp:struct targets a union (
WARNING: cpp:type targets a concept (
WARNING: cpp:type targets a enumerator (
WARNING: cpp:type targets a functionParam (
WARNING: cpp:type targets a member (
WARNING: cpp:union targets a class (
WARNING: cpp:union targets a concept (
WARNING: cpp:union targets a enum (
WARNING: cpp:union targets a enumerator (
WARNING: cpp:union targets a function (
WARNING: cpp:union targets a functionParam (
WARNING: cpp:union targets a member (
WARNING: cpp:union targets a type (
WARNING: cpp:var targets a class (
WARNING: cpp:var targets a concept (
WARNING: cpp:var targets a enum (
WARNING: cpp:var targets a enumerator (
WARNING: cpp:var targets a function (
WARNING: cpp:var targets a type (
WARNING: cpp:var targets a union (
Actual warnings:
WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[config changed ('add_function_parentheses')] 13 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[  7%] [35manon-dup-decl[39;49;00m                                        
[01mreading sources... [39;49;00m[ 15%] [35many-role[39;49;00m                                             
[01mreading sources... [39;49;00m[ 23%] [35mbackslash[39;49;00m                                            
[01mreading sources... [39;49;00m[ 30%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 38%] [35mlookup-key-overload[39;49;00m                                  
[01mreading sources... [39;49;00m[ 46%] [35mmulti-decl-lookup[39;49;00m                                    
[01mreading sources... [39;49;00m[ 53%] [35mroles[39;49;00m                                                
[01mreading sources... [39;49;00m[ 61%] [35mroles-targets-ok[39;49;00m                                     
[01mreading sources... [39;49;00m[ 69%] [35mroles-targets-warn[39;49;00m                                   
[01mreading sources... [39;49;00m[ 76%] [35mroles2[39;49;00m                                               
[01mreading sources... [39;49;00m[ 84%] [35msemicolon[39;49;00m                                            
[01mreading sources... [39;49;00m[ 92%] [35mwarn-template-param-qualified-name[39;49;00m                   
[01mreading sources... [39;49;00m[100%] [35mxref_consistency[39;49;00m                                     
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/any-role.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/lookup-key-overload.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/multi-decl-lookup.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/semicolon.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/xref_consistency.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-cpp
# outdir: /tmp/pytest-of-root/pytest-0/domain-cpp/_build/html
# status: 
[01mRunning Sphinx v3.1.0+/2e506c5ab[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[config changed ('add_function_parentheses')] 13 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[  7%] [35manon-dup-decl[39;49;00m                                        
[01mreading sources... [39;49;00m[ 15%] [35many-role[39;49;00m                                             
[01mreading sources... [39;49;00m[ 23%] [35mbackslash[39;49;00m                                            
[01mreading sources... [39;49;00m[ 30%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 38%] [35mlookup-key-overload[39;49;00m                                  
[01mreading sources... [39;49;00m[ 46%] [35mmulti-decl-lookup[39;49;00m                                    
[01mreading sources... [39;49;00m[ 53%] [35mroles[39;49;00m                                                
[01mreading sources... [39;49;00m[ 61%] [35mroles-targets-ok[39;49;00m                                     
[01mreading sources... [39;49;00m[ 69%] [35mroles-targets-warn[39;49;00m                                   
[01mreading sources... [39;49;00m[ 76%] [35mroles2[39;49;00m                                               
[01mreading sources... [39;49;00m[ 84%] [35msemicolon[39;49;00m                                            
[01mreading sources... [39;49;00m[ 92%] [35mwarn-template-param-qualified-name[39;49;00m                   
[01mreading sources... [39;49;00m[100%] [35mxref_consistency[39;49;00m                                     
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[  7%] [32manon-dup-decl[39;49;00m                                         
[01mwriting output... [39;49;00m[ 15%] [32many-role[39;49;00m                                              
[01mwriting output... [39;49;00m[ 23%] [32mbackslash[39;49;00m                                             
[01mwriting output... [39;49;00m[ 30%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 38%] [32mlookup-key-overload[39;49;00m                                   
[01mwriting output... [39;49;00m[ 46%] [32mmulti-decl-lookup[39;49;00m                                     
[01mwriting output... [39;49;00m[ 53%] [32mroles[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 61%] [32mroles-targets-ok[39;49;00m                                      
[01mwriting output... [39;49;00m[ 69%] [32mroles-targets-warn[39;49;00m                                    
[01mwriting output... [39;49;00m[ 76%] [32mroles2[39;49;00m                                                
[01mwriting output... [39;49;00m[ 84%] [32msemicolon[39;49;00m                                             
[01mwriting output... [39;49;00m[ 92%] [32mwarn-template-param-qualified-name[39;49;00m                    
[01mwriting output... [39;49;00m[100%] [32mxref_consistency[39;49;00m                                      
[01mgenerating indices... [39;49;00m genindexdone
[01mwriting additional pages... [39;49;00m searchdone
[01mcopying static files... ... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/anon-dup-decl.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/any-role.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/backslash.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/lookup-key-overload.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/multi-decl-lookup.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-ok.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/semicolon.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/warn-template-param-qualified-name.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/xref_consistency.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:union targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:func targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:member targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:var targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:concept targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enum targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:7: WARNING: cpp:enumerator targets a class (RolesTargetsWarn::Class).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:class targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:struct targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:func targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:member targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:var targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:concept targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enum targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:20: WARNING: cpp:enumerator targets a union (RolesTargetsWarn::Union).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:class targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:struct targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:union targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:member targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:var targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:concept targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enum targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:33: WARNING: cpp:enumerator targets a function (RolesTargetsWarn::Function).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:class targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:struct targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:union targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:func targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:type targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:concept targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enum targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:46: WARNING: cpp:enumerator targets a member (RolesTargetsWarn::Variable).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:class targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:struct targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:union targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:func targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:member targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:var targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:concept targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enum targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:59: WARNING: cpp:enumerator targets a type (RolesTargetsWarn::Type).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:class targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:struct targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:union targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:func targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:member targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:var targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:type targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enum targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:72: WARNING: cpp:enumerator targets a concept (RolesTargetsWarn::Concept).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:class targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:struct targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:union targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:func targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:member targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:var targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:concept targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:85: WARNING: cpp:enumerator targets a enum (RolesTargetsWarn::Enum).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:class targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:struct targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:union targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:func targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:member targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:var targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:type targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:concept targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:98: WARNING: cpp:enum targets a enumerator (RolesTargetsWarn::Enum::Enumerator).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:class targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:struct targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:union targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:func targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:type targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:concept targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enum targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles-targets-warn.rst:149: WARNING: cpp:enumerator targets a functionParam (RolesTargetsWarn::FunctionParams::FunctionParam).[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-cpp/roles2.rst:5: WARNING: cpp:func targets a class (A).[39;49;00m

============================= slowest 25 durations =============================
0.64s call     tests/test_domain_cpp.py::test_expressions
0.41s call     tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
0.41s call     tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
0.41s call     tests/test_domain_cpp.py::test_xref_consistency
0.41s call     tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
0.41s call     tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
0.29s call     tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
0.29s call     tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
0.28s call     tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
0.28s call     tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
0.22s setup    tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
0.10s call     tests/test_domain_cpp.py::test_function_definitions
0.04s call     tests/test_domain_cpp.py::test_templates
0.03s call     tests/test_domain_cpp.py::test_fundamental_types
0.03s call     tests/test_domain_cpp.py::test_type_definitions
0.03s call     tests/test_domain_cpp.py::test_operators
0.03s call     tests/test_domain_cpp.py::test_initializers
0.01s call     tests/test_domain_cpp.py::test_class_definitions
0.01s setup    tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
0.01s setup    tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
0.01s setup    tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
0.01s setup    tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
0.01s setup    tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
0.01s setup    tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
0.01s call     tests/test_domain_cpp.py::test_member_definitions
=========================== short test summary info ============================
PASSED tests/test_domain_cpp.py::test_fundamental_types
PASSED tests/test_domain_cpp.py::test_type_definitions
PASSED tests/test_domain_cpp.py::test_concept_definitions
PASSED tests/test_domain_cpp.py::test_member_definitions
PASSED tests/test_domain_cpp.py::test_function_definitions
PASSED tests/test_domain_cpp.py::test_operators
PASSED tests/test_domain_cpp.py::test_class_definitions
PASSED tests/test_domain_cpp.py::test_union_definitions
PASSED tests/test_domain_cpp.py::test_enum_definitions
PASSED tests/test_domain_cpp.py::test_anon_definitions
PASSED tests/test_domain_cpp.py::test_templates
PASSED tests/test_domain_cpp.py::test_template_args
PASSED tests/test_domain_cpp.py::test_initializers
PASSED tests/test_domain_cpp.py::test_attributes
PASSED tests/test_domain_cpp.py::test_xref_parsing
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_multi_decl_lookup
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_warn_template_param_qualified_name
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_backslash_ok
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_semicolon
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_anon_dup_decl
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_misuse_of_roles
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_True
PASSED tests/test_domain_cpp.py::test_build_domain_cpp_with_add_function_parentheses_is_False
PASSED tests/test_domain_cpp.py::test_xref_consistency
FAILED tests/test_domain_cpp.py::test_expressions - sphinx.util.cfamily.Defin...
================= 1 failed, 24 passed, 6261 warnings in 4.71s ==================
py39: exit 1 (5.25 seconds) /testbed> pytest -rA --durations 25 tests/test_domain_cpp.py pid=107
  py39: FAIL code 1 (5.26=setup[0.01]+cmd[5.25] seconds)
  evaluation failed :( (5.35 seconds)
+ git checkout 2e506c5ab457cba743bb47eb5b8c8eb9dd51d23d tests/test_domain_cpp.py
Updated 1 path from e3c9d6e97
