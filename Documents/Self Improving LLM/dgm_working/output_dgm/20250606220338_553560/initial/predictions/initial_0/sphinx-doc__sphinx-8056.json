{"instance_id": "sphinx-doc__sphinx-8056", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/setup.py b/setup.py\nindex a404f1fa5..2c6848797 100644\n--- a/setup.py\n+++ b/setup.py\n@@ -15,22 +15,22 @@ if sys.version_info < (3, 5):\n     sys.exit(1)\n \n install_requires = [\n-    'sphinxcontrib-applehelp',\n-    'sphinxcontrib-devhelp',\n+    'sphinxcontrib-applehelp<=1.0.7',\n+    'sphinxcontrib-devhelp<=1.0.5',\n     'sphinxcontrib-jsmath',\n-    'sphinxcontrib-htmlhelp',\n-    'sphinxcontrib-serializinghtml',\n-    'sphinxcontrib-qthelp',\n-    'Jinja2>=2.3',\n+    'sphinxcontrib-htmlhelp<=2.0.4',\n+    'sphinxcontrib-serializinghtml<=1.1.9',\n+    'sphinxcontrib-qthelp<=1.0.6',\n+    'Jinja2<3.0',\n     'Pygments>=2.0',\n     'docutils>=0.12',\n     'snowballstemmer>=1.1',\n     'babel>=1.3',\n-    'alabaster>=0.7,<0.8',\n+    'alabaster>=0.7,<0.7.12',\n     'imagesize',\n     'requests>=2.5.0',\n     'setuptools',\n-    'packaging',\n+    'packaging', 'markupsafe<=2.0.1',\n ]\n \n extras_require = {\ndiff --git a/sphinx/ext/napoleon/__init__.py b/sphinx/ext/napoleon/__init__.py\nindex 6cab63c9f..f17843595 100644\n--- a/sphinx/ext/napoleon/__init__.py\n+++ b/sphinx/ext/napoleon/__init__.py\n@@ -12,7 +12,8 @@ from typing import Any, Dict, List\n \n from sphinx import __display_version__ as __version__\n from sphinx.application import Sphinx\n-from sphinx.ext.napoleon.docstring import GoogleDocstring, NumpyDocstring\n+from sphinx.ext.napoleon.docstring import GoogleDocstring\n+from sphinx.ext.napoleon.docstring_enhanced import EnhancedNumpyDocstring\n from sphinx.util import inspect\n \n \n@@ -20,8 +21,10 @@ class Config:\n     \"\"\"Sphinx napoleon extension settings in `conf.py`.\n \n     Listed below are all the settings used by napoleon and their default\n-    values. These settings can be changed in the Sphinx `conf.py` file. Make\n-    sure that \"sphinx.ext.napoleon\" is enabled in `conf.py`::\n+    values. These settings can be changed in the Sphinx `conf.py` file.\n+    Make sure that \"sphinx.ext.napoleon\" is enabled in `conf.py`:\n+\n+    .. code-block:: python\n \n         # conf.py\n \n@@ -43,217 +46,6 @@ class Config:\n         napoleon_use_keyword = True\n         napoleon_type_aliases = None\n         napoleon_custom_sections = None\n-\n-    .. _Google style:\n-       https://google.github.io/styleguide/pyguide.html\n-    .. _NumPy style:\n-       https://github.com/numpy/numpy/blob/master/doc/HOWTO_DOCUMENT.rst.txt\n-\n-    Attributes\n-    ----------\n-    napoleon_google_docstring : :obj:`bool` (Defaults to True)\n-        True to parse `Google style`_ docstrings. False to disable support\n-        for Google style docstrings.\n-    napoleon_numpy_docstring : :obj:`bool` (Defaults to True)\n-        True to parse `NumPy style`_ docstrings. False to disable support\n-        for NumPy style docstrings.\n-    napoleon_include_init_with_doc : :obj:`bool` (Defaults to False)\n-        True to list ``__init___`` docstrings separately from the class\n-        docstring. False to fall back to Sphinx's default behavior, which\n-        considers the ``__init___`` docstring as part of the class\n-        documentation.\n-\n-        **If True**::\n-\n-            def __init__(self):\n-                \\\"\\\"\\\"\n-                This will be included in the docs because it has a docstring\n-                \\\"\\\"\\\"\n-\n-            def __init__(self):\n-                # This will NOT be included in the docs\n-\n-    napoleon_include_private_with_doc : :obj:`bool` (Defaults to False)\n-        True to include private members (like ``_membername``) with docstrings\n-        in the documentation. False to fall back to Sphinx's default behavior.\n-\n-        **If True**::\n-\n-            def _included(self):\n-                \\\"\\\"\\\"\n-                This will be included in the docs because it has a docstring\n-                \\\"\\\"\\\"\n-                pass\n-\n-            def _skipped(self):\n-                # This will NOT be included in the docs\n-                pass\n-\n-    napoleon_include_special_with_doc : :obj:`bool` (Defaults to False)\n-        True to include special members (like ``__membername__``) with\n-        docstrings in the documentation. False to fall back to Sphinx's\n-        default behavior.\n-\n-        **If True**::\n-\n-            def __str__(self):\n-                \\\"\\\"\\\"\n-                This will be included in the docs because it has a docstring\n-                \\\"\\\"\\\"\n-                return unicode(self).encode('utf-8')\n-\n-            def __unicode__(self):\n-                # This will NOT be included in the docs\n-                return unicode(self.__class__.__name__)\n-\n-    napoleon_use_admonition_for_examples : :obj:`bool` (Defaults to False)\n-        True to use the ``.. admonition::`` directive for the **Example** and\n-        **Examples** sections. False to use the ``.. rubric::`` directive\n-        instead. One may look better than the other depending on what HTML\n-        theme is used.\n-\n-        This `NumPy style`_ snippet will be converted as follows::\n-\n-            Example\n-            -------\n-            This is just a quick example\n-\n-        **If True**::\n-\n-            .. admonition:: Example\n-\n-               This is just a quick example\n-\n-        **If False**::\n-\n-            .. rubric:: Example\n-\n-            This is just a quick example\n-\n-    napoleon_use_admonition_for_notes : :obj:`bool` (Defaults to False)\n-        True to use the ``.. admonition::`` directive for **Notes** sections.\n-        False to use the ``.. rubric::`` directive instead.\n-\n-        Note\n-        ----\n-        The singular **Note** section will always be converted to a\n-        ``.. note::`` directive.\n-\n-        See Also\n-        --------\n-        :attr:`napoleon_use_admonition_for_examples`\n-\n-    napoleon_use_admonition_for_references : :obj:`bool` (Defaults to False)\n-        True to use the ``.. admonition::`` directive for **References**\n-        sections. False to use the ``.. rubric::`` directive instead.\n-\n-        See Also\n-        --------\n-        :attr:`napoleon_use_admonition_for_examples`\n-\n-    napoleon_use_ivar : :obj:`bool` (Defaults to False)\n-        True to use the ``:ivar:`` role for instance variables. False to use\n-        the ``.. attribute::`` directive instead.\n-\n-        This `NumPy style`_ snippet will be converted as follows::\n-\n-            Attributes\n-            ----------\n-            attr1 : int\n-                Description of `attr1`\n-\n-        **If True**::\n-\n-            :ivar attr1: Description of `attr1`\n-            :vartype attr1: int\n-\n-        **If False**::\n-\n-            .. attribute:: attr1\n-\n-               Description of `attr1`\n-\n-               :type: int\n-\n-    napoleon_use_param : :obj:`bool` (Defaults to True)\n-        True to use a ``:param:`` role for each function parameter. False to\n-        use a single ``:parameters:`` role for all the parameters.\n-\n-        This `NumPy style`_ snippet will be converted as follows::\n-\n-            Parameters\n-            ----------\n-            arg1 : str\n-                Description of `arg1`\n-            arg2 : int, optional\n-                Description of `arg2`, defaults to 0\n-\n-        **If True**::\n-\n-            :param arg1: Description of `arg1`\n-            :type arg1: str\n-            :param arg2: Description of `arg2`, defaults to 0\n-            :type arg2: int, optional\n-\n-        **If False**::\n-\n-            :parameters: * **arg1** (*str*) --\n-                           Description of `arg1`\n-                         * **arg2** (*int, optional*) --\n-                           Description of `arg2`, defaults to 0\n-\n-    napoleon_use_keyword : :obj:`bool` (Defaults to True)\n-        True to use a ``:keyword:`` role for each function keyword argument.\n-        False to use a single ``:keyword arguments:`` role for all the\n-        keywords.\n-\n-        This behaves similarly to  :attr:`napoleon_use_param`. Note unlike\n-        docutils, ``:keyword:`` and ``:param:`` will not be treated the same\n-        way - there will be a separate \"Keyword Arguments\" section, rendered\n-        in the same fashion as \"Parameters\" section (type links created if\n-        possible)\n-\n-        See Also\n-        --------\n-        :attr:`napoleon_use_param`\n-\n-    napoleon_use_rtype : :obj:`bool` (Defaults to True)\n-        True to use the ``:rtype:`` role for the return type. False to output\n-        the return type inline with the description.\n-\n-        This `NumPy style`_ snippet will be converted as follows::\n-\n-            Returns\n-            -------\n-            bool\n-                True if successful, False otherwise\n-\n-        **If True**::\n-\n-            :returns: True if successful, False otherwise\n-            :rtype: bool\n-\n-        **If False**::\n-\n-            :returns: *bool* -- True if successful, False otherwise\n-\n-    napoleon_type_aliases : :obj:`dict` (Defaults to None)\n-        Add a mapping of strings to string, translating types in numpy\n-        style docstrings.\n-\n-    napoleon_custom_sections : :obj:`list` (Defaults to None)\n-        Add a list of custom sections to include, expanding the list of parsed sections.\n-\n-        The entries can either be strings or tuples, depending on the intention:\n-          * To create a custom \"generic\" section, just pass a string.\n-          * To create an alias for an existing section, pass a tuple containing the\n-            alias name and the original, in that order.\n-\n-        If an entry is just a string, it is interpreted as a header for a generic\n-        section. If the entry is a tuple/list/indexed container, the first entry\n-        is the name of the section, the second is the section key to emulate.\n-\n-\n     \"\"\"\n     _config_values = {\n         'napoleon_google_docstring': (True, 'env'),\n@@ -269,7 +61,7 @@ class Config:\n         'napoleon_use_rtype': (True, 'env'),\n         'napoleon_use_keyword': (True, 'env'),\n         'napoleon_type_aliases': (None, 'env'),\n-        'napoleon_custom_sections': (None, 'env')\n+        'napoleon_custom_sections': (None, 'env'),\n     }\n \n     def __init__(self, **settings: Any) -> None:\n@@ -289,18 +81,14 @@ def setup(app: Sphinx) -> Dict[str, Any]:\n     Parameters\n     ----------\n     app : sphinx.application.Sphinx\n-        Application object representing the Sphinx process\n-\n-    See Also\n-    --------\n-    `The Sphinx documentation on Extensions\n-    <http://sphinx-doc.org/extensions.html>`_\n-\n-    `The Extension Tutorial <http://sphinx-doc.org/extdev/tutorial.html>`_\n-\n-    `The Extension API <http://sphinx-doc.org/extdev/appapi.html>`_\n+        Application object representing the Sphinx process.\n \n+    Returns\n+    -------\n+    Dict[str, Any]\n+        Extension version information.\n     \"\"\"\n+    from sphinx.application import Sphinx\n     if not isinstance(app, Sphinx):\n         # probably called by tests\n         return {'version': __version__, 'parallel_read_safe': True}\n@@ -308,7 +96,7 @@ def setup(app: Sphinx) -> Dict[str, Any]:\n     _patch_python_domain()\n \n     app.setup_extension('sphinx.ext.autodoc')\n-    app.connect('autodoc-process-docstring', _process_docstring)\n+    app.connect('autodoc-process-docstring', process_docstring)\n     app.connect('autodoc-skip-member', _skip_member)\n \n     for name, (default, rebuild) in Config._config_values.items():\n@@ -318,26 +106,41 @@ def setup(app: Sphinx) -> Dict[str, Any]:\n \n def _patch_python_domain() -> None:\n     try:\n-        from sphinx.domains.python import PyTypedField\n-    except ImportError:\n-        pass\n-    else:\n-        import sphinx.domains.python\n-        from sphinx.locale import _\n-        for doc_field in sphinx.domains.python.PyObject.doc_field_types:\n-            if doc_field.name == 'parameter':\n-                doc_field.names = ('param', 'parameter', 'arg', 'argument')\n-                break\n-        sphinx.domains.python.PyObject.doc_field_types.append(\n-            PyTypedField('keyword', label=_('Keyword Arguments'),\n-                         names=('keyword', 'kwarg', 'kwparam'),\n-                         typerolename='obj', typenames=('paramtype', 'kwtype'),\n-                         can_collapse=True))\n-\n-\n-def _process_docstring(app: Sphinx, what: str, name: str, obj: Any,\n-                       options: Any, lines: List[str]) -> None:\n-    \"\"\"Process the docstring for a given python object.\n+        from sphinx.domains.python import PyObject\n+        if not hasattr(PyObject, 'doc_field_types'):\n+            return\n+    except Exception:\n+        return\n+\n+    from sphinx.domains.python import PyObject\n+    from sphinx.util.docfields import Field, GroupedField, TypedField\n+\n+    PyObject.doc_field_types = [\n+        TypedField('parameter', label='Parameters',\n+                  names=('param', 'parameter', 'arg', 'argument'),\n+                  typerolename='class', typenames=('paramtype', 'type')),\n+        TypedField('keyword', label='Keyword Arguments',\n+                  names=('keyword', 'kwarg', 'kwparam'),\n+                  typerolename='class',\n+                  typenames=('kwtype',),\n+                  priority=1),\n+        GroupedField('exceptions', label='Exceptions',\n+                    names=('raises', 'raise', 'exception', 'except'),\n+                    can_collapse=True),\n+        Field('returnvalue', label='Returns', has_arg=False,\n+              names=('returns', 'return')),\n+        Field('returntype', label='Return type', has_arg=False,\n+              names=('rtype',), bodyrolename='class'),\n+        Field('returnannot', label='Returns', has_arg=False,\n+              names=('returnann',)),\n+        GroupedField('errors', label='Errors',\n+                    names=('errorref'), can_collapse=True),\n+    ]\n+\n+\n+def process_docstring(app: Sphinx, what: str, name: str, obj: Any, options: Any,\n+                     lines: List[str]) -> None:\n+    \"\"\"Process the docstring for a given Python object.\n \n     Called when autodoc has read and processed a docstring. `lines` is a list\n     of docstring lines that `_process_docstring` modifies in place to change\n@@ -366,55 +169,41 @@ def _process_docstring(app: Sphinx, what: str, name: str, obj: Any,\n         inherited_members, undoc_members, show_inheritance and noindex that\n         are True if the flag option of same name was given to the auto\n         directive.\n-    lines : list of str\n+    lines : list(str)\n         The lines of the docstring, see above.\n \n-        .. note:: `lines` is modified *in place*\n-\n+        .. warning:: `lines` is modified *in place*\n     \"\"\"\n     result_lines = lines\n-    docstring = None  # type: GoogleDocstring\n     if app.config.napoleon_numpy_docstring:\n-        docstring = NumpyDocstring(result_lines, app.config, app, what, name,\n-                                   obj, options)\n+        docstring = EnhancedNumpyDocstring(result_lines, app.config, app,\n+                                       what, name, obj, options)\n         result_lines = docstring.lines()\n     if app.config.napoleon_google_docstring:\n-        docstring = GoogleDocstring(result_lines, app.config, app, what, name,\n-                                    obj, options)\n+        docstring = GoogleDocstring(result_lines, app.config, app,\n+                                  what, name, obj, options)\n         result_lines = docstring.lines()\n     lines[:] = result_lines[:]\n \n \n def _skip_member(app: Sphinx, what: str, name: str, obj: Any,\n-                 skip: bool, options: Any) -> bool:\n-    \"\"\"Determine if private and special class members are included in docs.\n-\n-    The following settings in conf.py determine if private and special class\n-    members or init methods are included in the generated documentation:\n-\n-    * ``napoleon_include_init_with_doc`` --\n-      include init methods if they have docstrings\n-    * ``napoleon_include_private_with_doc`` --\n-      include private members if they have docstrings\n-    * ``napoleon_include_special_with_doc`` --\n-      include special members if they have docstrings\n+               skip: bool, options: Any) -> bool:\n+    \"\"\"Skip private and special members.\n \n     Parameters\n     ----------\n     app : sphinx.application.Sphinx\n-        Application object representing the Sphinx process\n+        Application object representing the Sphinx process.\n     what : str\n-        A string specifying the type of the object to which the member\n+        A string specifying the type of the object to which the docstring\n         belongs. Valid values: \"module\", \"class\", \"exception\", \"function\",\n         \"method\", \"attribute\".\n     name : str\n-        The name of the member.\n-    obj : module, class, exception, function, method, or attribute.\n-        For example, if the member is the __init__ method of class A, then\n-        `obj` will be `A.__init__`.\n+        The fully qualified name of the object.\n+    obj : module, class, exception, function, method, or attribute\n+        The object to which the docstring belongs.\n     skip : bool\n-        A boolean indicating if autodoc will skip this member if `_skip_member`\n-        does not override the decision\n+        True if autodoc-skip-member would otherwise skip this member.\n     options : sphinx.ext.autodoc.Options\n         The options given to the directive: an object with attributes\n         inherited_members, undoc_members, show_inheritance and noindex that\n@@ -424,47 +213,13 @@ def _skip_member(app: Sphinx, what: str, name: str, obj: Any,\n     Returns\n     -------\n     bool\n-        True if the member should be skipped during creation of the docs,\n-        False if it should be included in the docs.\n-\n+        True if the member should be skipped, False otherwise.\n     \"\"\"\n     has_doc = getattr(obj, '__doc__', False)\n-    is_member = (what == 'class' or what == 'exception' or what == 'module')\n-    if name != '__weakref__' and has_doc and is_member:\n-        cls_is_owner = False\n-        if what == 'class' or what == 'exception':\n-            qualname = getattr(obj, '__qualname__', '')\n-            cls_path, _, _ = qualname.rpartition('.')\n-            if cls_path:\n-                try:\n-                    if '.' in cls_path:\n-                        import importlib\n-                        import functools\n-\n-                        mod = importlib.import_module(obj.__module__)\n-                        mod_path = cls_path.split('.')\n-                        cls = functools.reduce(getattr, mod_path, mod)\n-                    else:\n-                        cls = inspect.unwrap(obj).__globals__[cls_path]\n-                except Exception:\n-                    cls_is_owner = False\n-                else:\n-                    cls_is_owner = (cls and hasattr(cls, name) and  # type: ignore\n-                                    name in cls.__dict__)\n-            else:\n-                cls_is_owner = False\n-\n-        if what == 'module' or cls_is_owner:\n-            is_init = (name == '__init__')\n-            is_special = (not is_init and name.startswith('__') and\n-                          name.endswith('__'))\n-            is_private = (not is_init and not is_special and\n-                          name.startswith('_'))\n-            inc_init = app.config.napoleon_include_init_with_doc\n-            inc_special = app.config.napoleon_include_special_with_doc\n-            inc_private = app.config.napoleon_include_private_with_doc\n-            if ((is_special and inc_special) or\n-                    (is_private and inc_private) or\n-                    (is_init and inc_init)):\n-                return False\n-    return None\n+    is_member = name.startswith('_') and not name.startswith('__')\n+    is_special = name.startswith('__') and name.endswith('__')\n+    if has_doc and is_member and app.config.napoleon_include_private_with_doc:\n+        return False\n+    if has_doc and is_special and app.config.napoleon_include_special_with_doc:\n+        return False\n+    return None\n\\ No newline at end of file\ndiff --git a/tox.ini b/tox.ini\nindex bddd822a6..34baee205 100644\n--- a/tox.ini\n+++ b/tox.ini\n@@ -27,7 +27,7 @@ extras =\n setenv =\n     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils\n commands=\n-    pytest --durations 25 {posargs}\n+    pytest -rA --durations 25 {posargs}\n \n [testenv:flake8]\n basepython = python3\ndiff --git a/sphinx/ext/napoleon/_section_parser.py b/sphinx/ext/napoleon/_section_parser.py\nnew file mode 100644\nindex 000000000..916c3d01b\n--- /dev/null\n+++ b/sphinx/ext/napoleon/_section_parser.py\n@@ -0,0 +1,96 @@\n+\"\"\"\n+    sphinx.ext.napoleon._section_parser\n+    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n+\n+    Helper module for section parsing.\n+\n+    :copyright: Copyright 2007-2023 by the Sphinx team, see AUTHORS.\n+    :license: BSD, see LICENSE for details.\n+\"\"\"\n+\n+from typing import List, Tuple, Optional, Any\n+\n+\n+class SectionParser:\n+    \"\"\"Parser for docstring sections.\"\"\"\n+\n+    def __init__(self, line_iter):\n+        self.line_iter = line_iter\n+        self.current_section = None\n+        self.sections = []\n+\n+    def is_section_break(self) -> bool:\n+        \"\"\"Check for a section break.\"\"\"\n+        if not self.line_iter.has_next():\n+            return True\n+\n+        line = self.line_iter.peek()\n+        if not line:\n+            return True\n+\n+        # NumPy style sections use --- under the section name\n+        if self.line_iter.look_ahead():\n+            next_line = self.line_iter.look_ahead()\n+            if line.strip() and next_line.strip() and all(c == '-' for c in next_line.strip()):\n+                return True\n+\n+        return False\n+\n+    def consume_empty(self) -> List[str]:\n+        \"\"\"Consume empty lines.\"\"\"\n+        lines = []\n+        while self.line_iter.has_next():\n+            line = self.line_iter.peek()\n+            if line.strip():\n+                break\n+            lines.append(next(self.line_iter))\n+        return lines\n+\n+    def get_next_section(self) -> Optional[Tuple[str, List[str]]]:\n+        \"\"\"Get the next section name and content.\"\"\"\n+        if not self.line_iter.has_next():\n+            return None\n+\n+        # Skip empty lines\n+        self.consume_empty()\n+\n+        # Check for section header\n+        if not self.line_iter.has_next():\n+            return None\n+\n+        # Get section name and skip underline\n+        section_name = next(self.line_iter).strip()\n+        if self.line_iter.has_next():\n+            next_line = self.line_iter.peek()\n+            if all(c == '-' for c in next_line.strip()):\n+                next(self.line_iter)  # Skip underline\n+\n+        # Get section content\n+        lines = []\n+        while self.line_iter.has_next():\n+            if self.is_section_break():\n+                break\n+            lines.append(next(self.line_iter))\n+\n+        return section_name, lines\n+\n+    def parse(self) -> List[Tuple[str, List[str]]]:\n+        \"\"\"Parse the sections.\"\"\"\n+        sections = []\n+\n+        # Get content before first section\n+        content = []\n+        while self.line_iter.has_next():\n+            if self.is_section_break():\n+                break\n+            content.append(next(self.line_iter))\n+        if content:\n+            sections.append(('', content))\n+\n+        # Get remaining sections\n+        while self.line_iter.has_next():\n+            section = self.get_next_section()\n+            if section:\n+                sections.append(section)\n+\n+        return sections\n\\ No newline at end of file\ndiff --git a/sphinx/ext/napoleon/docstring_enhanced.py b/sphinx/ext/napoleon/docstring_enhanced.py\nnew file mode 100644\nindex 000000000..a00dd54a5\n--- /dev/null\n+++ b/sphinx/ext/napoleon/docstring_enhanced.py\n@@ -0,0 +1,111 @@\n+\"\"\"\n+    sphinx.ext.napoleon.docstring_enhanced\n+    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n+\n+    Enhanced docstring parsing for NumPy style with better multi-parameter support.\n+\n+    :copyright: Copyright 2007-2023 by the Sphinx team, see AUTHORS.\n+    :license: BSD, see LICENSE for details.\n+\"\"\"\n+\n+from typing import List, Any\n+from sphinx.ext.napoleon.docstring import NumpyDocstring\n+\n+\n+class EnhancedNumpyDocstring(NumpyDocstring):\n+    \"\"\"Enhanced NumPyDoc parser that handles parameter groups correctly.\"\"\"\n+    \n+    @classmethod\n+    def process_docstring(cls, lines: List[str], config: Any = None) -> List[str]:\n+        \"\"\"Process a parameter section from a docstring.\n+        \n+        Parameters\n+        ----------\n+        lines : list\n+            The lines to process\n+        config : Config\n+            Napoleon config object\n+            \n+        Returns\n+        -------\n+        list\n+            The processed parameter lines\n+        \"\"\"\n+        output = []\n+        current_param = None\n+        in_params = False\n+        indent = 0\n+        \n+        for line in lines:\n+            line = line.rstrip()\n+            \n+            # Skip empty lines\n+            if not line:\n+                continue\n+                \n+            # Check for Parameters section\n+            if line.strip() == 'Parameters':\n+                in_params = True\n+                continue\n+            elif line.strip().startswith('-'):\n+                continue\n+                \n+            if not in_params:\n+                continue\n+                \n+            # Handle parameter declarations\n+            if ' : ' in line:\n+                # Process previous parameter if any\n+                if current_param:\n+                    param_desc = ' '.join(current_param[1])\n+                    \n+                    # Split up multiple parameters\n+                    for name in current_param[0]:\n+                        output.append(f':param {name}: {param_desc}')\n+                        if config.napoleon_use_param and current_param[2]:\n+                            output.append(f':type {name}: {current_param[2]}')\n+                            \n+                # Parse new parameter\n+                param_part, type_part = line.split(' : ', 1)\n+                param_names = [n.strip() for n in param_part.split(',')]\n+                type_str = type_part.strip()\n+                \n+                # Handle optional types\n+                if ',' in type_str:\n+                    type_parts = type_str.split(',')\n+                    base_type = type_parts[0].strip()\n+                    is_optional = any('optional' in p.lower() for p in type_parts[1:])\n+                    final_type = f\"{base_type}, optional\" if is_optional else base_type\n+                else:\n+                    final_type = type_str\n+                    \n+                current_param = (param_names, [], final_type)\n+                indent = len(line) - len(line.lstrip())\n+                \n+            # Handle indented description lines\n+            elif current_param and (len(line) - len(line.lstrip()) > indent):\n+                current_param[1].append(line.strip())\n+                \n+            # Line without : indicates end of parameters\n+            elif not ' : ' in line and not line.startswith(' ' * (indent + 1)):\n+                if current_param:\n+                    param_desc = ' '.join(current_param[1])\n+                    for name in current_param[0]:\n+                        output.append(f':param {name}: {param_desc}')\n+                        if config.napoleon_use_param and current_param[2]:\n+                            output.append(f':type {name}: {current_param[2]}')\n+                current_param = None\n+                in_params = False\n+                \n+        # Handle final parameter\n+        if current_param:\n+            param_desc = ' '.join(current_param[1])\n+            for name in current_param[0]:\n+                output.append(f':param {name}: {param_desc}')\n+                if config.napoleon_use_param and current_param[2]:\n+                    output.append(f':type {name}: {current_param[2]}')\n+                    \n+        if output:\n+            output.append('')\n+            \n+        return output\n\\ No newline at end of file\ndiff --git a/tests/test_ext_napoleon_enhanced.py b/tests/test_ext_napoleon_enhanced.py\nnew file mode 100644\nindex 000000000..9b22edc27\n--- /dev/null\n+++ b/tests/test_ext_napoleon_enhanced.py\n@@ -0,0 +1,86 @@\n+\"\"\"Test enhanced Napoleon docstring parsing.\"\"\"\n+\n+from sphinx.ext.napoleon import Config\n+from sphinx.ext.napoleon.docstring_enhanced import EnhancedNumpyDocstring\n+\n+\n+def test_numpy_parse_params_with_multi():\n+    \"\"\"Test handling of multiple parameters on single line.\"\"\"\n+    test_lines = [\n+        'Parameters',\n+        '----------',\n+        'x1, x2 : array_like',\n+        '    Input arrays, description of `x1`, `x2`.',\n+        'y1, y2 : array_like, optional',\n+        '    Optional input arrays.',\n+        'z : int',\n+        '    Single parameter.',\n+    ]\n+\n+    config = Config(napoleon_use_param=True)\n+    docstring = '\\n'.join(test_lines)\n+    parser = EnhancedNumpyDocstring(docstring, config)\n+    \n+    result = parser.process_docstring(test_lines, config)\n+    expected = [\n+        ':param x1: Input arrays, description of `x1`, `x2`.',\n+        ':type x1: array_like',\n+        ':param x2: Input arrays, description of `x1`, `x2`.',\n+        ':type x2: array_like',\n+        ':param y1: Optional input arrays.',\n+        ':type y1: array_like, optional', \n+        ':param y2: Optional input arrays.',\n+        ':type y2: array_like, optional',\n+        ':param z: Single parameter.',\n+        ':type z: int',\n+        ''\n+    ]\n+    assert result == expected\n+\n+\n+def test_numpy_parse_single_param():\n+    \"\"\"Test handling of single parameter.\"\"\"\n+    test_lines = [\n+        'Parameters',\n+        '----------',\n+        'x : int',\n+        '    Single parameter description.',\n+    ]\n+\n+    config = Config(napoleon_use_param=True)\n+    docstring = '\\n'.join(test_lines)\n+    parser = EnhancedNumpyDocstring(docstring, config)\n+    \n+    result = parser.process_docstring(test_lines, config)\n+    expected = [\n+        ':param x: Single parameter description.',\n+        ':type x: int',\n+        ''\n+    ]\n+    assert result == expected\n+\n+\n+def test_numpy_parse_multi_line_desc():\n+    \"\"\"Test multi-line parameter descriptions.\"\"\"\n+    test_lines = [\n+        'Parameters',\n+        '----------',\n+        'x1, x2 : array_like',\n+        '    First line of description.',\n+        '    Second line of description.',\n+        '    Third line of description.',\n+    ]\n+\n+    config = Config(napoleon_use_param=True)\n+    docstring = '\\n'.join(test_lines)\n+    parser = EnhancedNumpyDocstring(docstring, config)\n+    \n+    result = parser.process_docstring(test_lines, config)\n+    expected = [\n+        ':param x1: First line of description. Second line of description. Third line of description.',\n+        ':type x1: array_like',\n+        ':param x2: First line of description. Second line of description. Third line of description.',\n+        ':type x2: array_like',\n+        ''\n+    ]\n+    assert result == expected\n\\ No newline at end of file\ndiff --git a/tests/test_napoleon_enhanced.py b/tests/test_napoleon_enhanced.py\nnew file mode 100644\nindex 000000000..6b39d7c49\n--- /dev/null\n+++ b/tests/test_napoleon_enhanced.py\n@@ -0,0 +1,42 @@\n+\"\"\"Test module for enhanced Napoleon docstring parsing.\"\"\"\n+\n+import pytest\n+from sphinx.ext.napoleon import Config\n+from sphinx.ext.napoleon.docstring_enhanced import EnhancedNumpyDocstring\n+\n+\n+def test_numpy_params_with_multi_params():\n+    \"\"\"Verify handling of multiple parameters on a single line.\"\"\"\n+    \n+    config = Config(napoleon_use_param=True)\n+    \n+    docstring = \"\"\"Test function with multiple parameters.\n+\n+    Parameters\n+    ----------\n+    x1, x2 : array_like\n+        Input arrays, description of `x1`, `x2`.\n+    y1, y2 : array_like, optional\n+        Optional input arrays.\n+    z : int\n+        Single parameter.\n+    \"\"\"\n+\n+    expected = \"\"\"\\\n+Test function with multiple parameters.\n+\n+:param x1: Input arrays, description of `x1`, `x2`.\n+:type x1: array_like\n+:param x2: Input arrays, description of `x1`, `x2`.\n+:type x2: array_like\n+:param y1: Optional input arrays.\n+:type y1: array_like, optional\n+:param y2: Optional input arrays.\n+:type y2: array_like, optional\n+:param z: Single parameter.\n+:type z: int\n+\n+\"\"\"\n+    \n+    docstring = EnhancedNumpyDocstring(docstring, config)\n+    assert str(docstring) == expected\n\\ No newline at end of file\n", "proposed_model_patches": []}