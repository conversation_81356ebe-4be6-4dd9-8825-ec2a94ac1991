+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=2
++ CONDA_SHLVL=2
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_1=/opt/miniconda3
++ CONDA_PREFIX_1=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ cd /testbed
+ git config --global --add safe.directory /testbed
+ cd /testbed
+ git status
On branch master
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   sphinx/ext/autodoc/importer.py
	modified:   tox.ini

Untracked files:
  (use "git add <file>..." to include in what will be committed)
	setup.py.orig

no changes added to commit (use "git add" and/or "git commit -a")
+ git show
commit dd1615c59dc6fff633e27dbb3861f2d27e1fb976
Merge: e3dc78236 0dbbf1dd5
Author: Takeshi KOMIYA <<EMAIL>>
Date:   Thu Dec 17 22:35:35 2020 +0900

    Merge pull request #8543 from tk0miya/8534_autoattribute_alias_derived
    
    Fix #8534: autoattribute failed to document a commented attribute in alias dervied class

+ git diff dd1615c59dc6fff633e27dbb3861f2d27e1fb976
diff --git a/sphinx/ext/autodoc/importer.py b/sphinx/ext/autodoc/importer.py
index d6e73d36a..6af221418 100644
--- a/sphinx/ext/autodoc/importer.py
+++ b/sphinx/ext/autodoc/importer.py
@@ -17,7 +17,7 @@ from sphinx.deprecation import RemovedInSphinx40Warning, deprecated_alias
 from sphinx.pycode import ModuleAnalyzer
 from sphinx.util import logging
 from sphinx.util.inspect import (getannotations, getmro, getslots, isclass, isenumclass,
-                                 safe_getattr)
+                                safe_getattr)
 
 if False:
     # For type annotation
@@ -257,83 +257,62 @@ def get_class_members(subject: Any, objpath: List[str], attrgetter: Callable,
     from sphinx.ext.autodoc import INSTANCEATTR
 
     # the members directly defined in the class
-    obj_dict = attrgetter(subject, '__dict__', {})
-
     members = {}  # type: Dict[str, ClassAttribute]
 
-    # enum members
-    if isenumclass(subject):
-        for name, value in subject.__members__.items():
-            if name not in members:
-                members[name] = ClassAttribute(subject, name, value)
+    # traverse MRO to get all attributes, including inherited ones
+    for cls in getmro(subject):
+        obj_dict = attrgetter(cls, '__dict__', {})
 
-        superclass = subject.__mro__[1]
-        for name in obj_dict:
-            if name not in superclass.__dict__:
-                value = safe_getattr(subject, name)
-                members[name] = ClassAttribute(subject, name, value)
+        # enum members
+        if isenumclass(cls):
+            for name, value in cls.__members__.items():
+                if name not in members:
+                    members[name] = ClassAttribute(cls, name, value)
 
-    # members in __slots__
-    try:
-        __slots__ = getslots(subject)
-        if __slots__:
-            from sphinx.ext.autodoc import SLOTSATTR
+            superclass = cls.__mro__[1]
+            for name in obj_dict:
+                if name not in superclass.__dict__:
+                    value = safe_getattr(cls, name)
+                    members[name] = ClassAttribute(cls, name, value)
 
-            for name, docstring in __slots__.items():
-                members[name] = ClassAttribute(subject, name, SLOTSATTR, docstring)
-    except (AttributeError, TypeError, ValueError):
-        pass
-
-    # other members
-    for name in dir(subject):
+        # members in __slots__
         try:
-            value = attrgetter(subject, name)
-            unmangled = unmangle(subject, name)
-            if unmangled and unmangled not in members:
-                if name in obj_dict:
-                    members[unmangled] = ClassAttribute(subject, unmangled, value)
-                else:
-                    members[unmangled] = ClassAttribute(None, unmangled, value)
-        except AttributeError:
-            continue
+            __slots__ = getslots(cls)
+            if __slots__:
+                from sphinx.ext.autodoc import SLOTSATTR
+
+                for name, docstring in __slots__.items():
+                    members[name] = ClassAttribute(cls, name, SLOTSATTR, docstring)
+        except (AttributeError, TypeError, ValueError):
+            pass
+
+        # other members
+        for name in dir(cls):
+            if name not in members:  # don't override members from derived class
+                try:
+                    value = attrgetter(cls, name)
+                    docstring = getattr(value, '__doc__', None)
+                    name = unmangle(cls, name)
+                    if name:
+                        members[name] = ClassAttribute(cls, name, value, docstring)
+                except AttributeError:
+                    continue
 
     # annotation only member (ex. attr: int)
-    for cls in getmro(subject):
+    for c in getmro(subject):
         try:
-            for name in getannotations(cls):
-                name = unmangle(cls, name)
+            for name in getannotations(c):
+                name = unmangle(c, name)
                 if name and name not in members:
-                    members[name] = ClassAttribute(cls, name, INSTANCEATTR)
+                    members[name] = ClassAttribute(c, name, INSTANCEATTR)
         except AttributeError:
             pass
 
     if analyzer:
         # append instance attributes (cf. self.attr1) if analyzer knows
         namespace = '.'.join(objpath)
-        for (ns, name), docstring in analyzer.attr_docs.items():
+        for (ns, name) in analyzer.find_attr_docs():
             if namespace == ns and name not in members:
-                members[name] = ClassAttribute(subject, name, INSTANCEATTR,
-                                               '\n'.join(docstring))
-
-    return members
-
+                members[name] = ClassAttribute(subject, name, INSTANCEATTR)
 
-from sphinx.ext.autodoc.mock import (MockFinder, MockLoader, _MockModule, _MockObject,  # NOQA
-                                     mock)
-
-deprecated_alias('sphinx.ext.autodoc.importer',
-                 {
-                     '_MockModule': _MockModule,
-                     '_MockObject': _MockObject,
-                     'MockFinder': MockFinder,
-                     'MockLoader': MockLoader,
-                     'mock': mock,
-                 },
-                 RemovedInSphinx40Warning,
-                 {
-                     '_MockModule': 'sphinx.ext.autodoc.mock._MockModule',
-                     '_MockObject': 'sphinx.ext.autodoc.mock._MockObject',
-                     'MockFinder': 'sphinx.ext.autodoc.mock.MockFinder',
-                     'MockLoader': 'sphinx.ext.autodoc.mock.MockLoader',
-                     'mock': 'sphinx.ext.autodoc.mock.mock',
-                 })
+    return members
\ No newline at end of file
diff --git a/tox.ini b/tox.ini
index dbb705a3a..9f4fc3a32 100644
--- a/tox.ini
+++ b/tox.ini
@@ -28,7 +28,7 @@ setenv =
     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils
     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes
 commands=
-    python -X dev -m pytest --durations 25 {posargs}
+    python -X dev -m pytest -rA --durations 25 {posargs}
 
 [testenv:flake8]
 basepython = python3
+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z x ']'
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1='(testbed) '
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=3
+++ CONDA_SHLVL=3
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=4
++ CONDA_SHLVL=4
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_3=/opt/miniconda3
++ CONDA_PREFIX_3=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ python -m pip install -e '.[test]'
Obtaining file:///testbed
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: sphinxcontrib-applehelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.0.4)
Requirement already satisfied: sphinxcontrib-devhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.0.2)
Requirement already satisfied: sphinxcontrib-jsmath in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.0.1)
Requirement already satisfied: sphinxcontrib-htmlhelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (2.0.1)
Requirement already satisfied: sphinxcontrib-serializinghtml in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.1.5)
Requirement already satisfied: sphinxcontrib-qthelp in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.0.3)
Requirement already satisfied: Jinja2>=2.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (2.11.3)
Requirement already satisfied: Pygments>=2.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (2.19.1)
Requirement already satisfied: docutils>=0.12 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (0.21.2)
Requirement already satisfied: snowballstemmer>=1.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (2.2.0)
Requirement already satisfied: babel>=1.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (2.17.0)
Requirement already satisfied: alabaster<0.8,>=0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (0.7.11)
Requirement already satisfied: imagesize in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.4.1)
Requirement already satisfied: requests>=2.5.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (2.32.3)
Requirement already satisfied: setuptools in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (75.8.0)
Requirement already satisfied: packaging in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (24.2)
Requirement already satisfied: pytest in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (8.3.4)
Requirement already satisfied: pytest-cov in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (6.0.0)
Requirement already satisfied: html5lib in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.1)
Requirement already satisfied: cython in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (3.0.11)
Requirement already satisfied: MarkupSafe>=0.23 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Jinja2>=2.3->Sphinx==3.4.0.dev20250315) (2.0.1)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.4.0.dev20250315) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.4.0.dev20250315) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.4.0.dev20250315) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.4.0.dev20250315) (2025.1.31)
Requirement already satisfied: six>=1.9 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.4.0.dev20250315) (1.17.0)
Requirement already satisfied: webencodings in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.4.0.dev20250315) (0.5.1)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.4.0.dev20250315) (1.2.2)
Requirement already satisfied: iniconfig in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.4.0.dev20250315) (2.0.0)
Requirement already satisfied: pluggy<2,>=1.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.4.0.dev20250315) (1.5.0)
Requirement already satisfied: tomli>=1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.4.0.dev20250315) (2.2.1)
Requirement already satisfied: coverage>=7.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from coverage[toml]>=7.5->pytest-cov->Sphinx==3.4.0.dev20250315) (7.6.10)
Installing collected packages: Sphinx
  Attempting uninstall: Sphinx
    Found existing installation: Sphinx 3.4.0.dev20250204
    Uninstalling Sphinx-3.4.0.dev20250204:
      Successfully uninstalled Sphinx-3.4.0.dev20250204
  DEPRECATION: Legacy editable install of Sphinx[test]==3.4.0.dev20250315 from file:///testbed (setup.py develop) is deprecated. pip 25.1 will enforce this behaviour change. A possible replacement is to add a pyproject.toml or enable --use-pep517, and use setuptools >= 64. If the resulting installation is not behaving as expected, try using --config-settings editable_mode=compat. Please consult the setuptools documentation for more information. Discussion can be found at https://github.com/pypa/pip/issues/11457
  Running setup.py develop for Sphinx
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
Successfully installed Sphinx
+ git checkout dd1615c59dc6fff633e27dbb3861f2d27e1fb976 tests/test_ext_autodoc_autoclass.py
Updated 0 paths from 557f988e2
+ git apply -v -
Checking patch tests/roots/test-ext-autodoc/target/instance_variable.py...
Checking patch tests/test_ext_autodoc_autoclass.py...
Applied patch tests/roots/test-ext-autodoc/target/instance_variable.py cleanly.
Applied patch tests/test_ext_autodoc_autoclass.py cleanly.
+ tox --current-env -epy39 -v -- tests/roots/test-ext-autodoc/target/instance_variable.py tests/test_ext_autodoc_autoclass.py
py39: commands[0]> python -X dev -m pytest -rA --durations 25 tests/roots/test-ext-autodoc/target/instance_variable.py tests/test_ext_autodoc_autoclass.py
[1m============================= test session starts ==============================[0m
platform linux -- Python 3.9.21, pytest-8.3.4, pluggy-1.5.0
cachedir: .tox/py39/.pytest_cache
libraries: Sphinx-3.4.0+/dd1615c59, docutils-0.21.2
base tempdir: /tmp/pytest-of-root/pytest-0
rootdir: /testbed
configfile: setup.cfg
plugins: cov-6.0.0
collected 6 items

tests/test_ext_autodoc_autoclass.py [32m.[0m[32m.[0m[31mF[0m[32m.[0m[32m.[0m[32m.[0m[31m                               [100%][0m

=================================== FAILURES ===================================
[31m[1m_______________________ test_inherited_instance_variable _______________________[0m

app = <SphinxTestApp buildername='html'>

    [0m[37m@pytest[39;49;00m.mark.sphinx([33m'[39;49;00m[33mhtml[39;49;00m[33m'[39;49;00m, testroot=[33m'[39;49;00m[33mext-autodoc[39;49;00m[33m'[39;49;00m)[90m[39;49;00m
    [94mdef[39;49;00m[90m [39;49;00m[92mtest_inherited_instance_variable[39;49;00m(app):[90m[39;49;00m
        options = {[33m'[39;49;00m[33mmembers[39;49;00m[33m'[39;49;00m: [94mTrue[39;49;00m,[90m[39;49;00m
                   [33m'[39;49;00m[33minherited-members[39;49;00m[33m'[39;49;00m: [94mTrue[39;49;00m}[90m[39;49;00m
        actual = do_autodoc(app, [33m'[39;49;00m[33mclass[39;49;00m[33m'[39;49;00m, [33m'[39;49;00m[33mtarget.instance_variable.Bar[39;49;00m[33m'[39;49;00m, options)[90m[39;49;00m
>       [94massert[39;49;00m [96mlist[39;49;00m(actual) == [[90m[39;49;00m
            [33m'[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m.. py:class:: Bar()[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m   :module: target.instance_variable[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m   .. py:attribute:: Bar.attr1[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m      :module: target.instance_variable[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m      docstring foo[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m   .. py:attribute:: Bar.attr2[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m      :module: target.instance_variable[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m      docstring bar[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m   .. py:attribute:: Bar.attr3[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m      :module: target.instance_variable[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m      docstring bar[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
            [33m'[39;49;00m[33m'[39;49;00m,[90m[39;49;00m
        ][90m[39;49;00m
[1m[31mE       AssertionError: assert ['', '.. py:c...r.attr2', ...] == ['', '.. py:c...r.attr1', ...][0m
[1m[31mE         [0m
[1m[31mE         At index 5 diff: [0m[33m'[39;49;00m[33m   .. py:attribute:: Bar.attr2[39;49;00m[33m'[39;49;00m[90m[39;49;00m != [0m[33m'[39;49;00m[33m   .. py:attribute:: Bar.attr1[39;49;00m[33m'[39;49;00m[90m[39;49;00m[0m
[1m[31mE         Right contains 6 more items, first extra item: [0m[33m'[39;49;00m[33m'[39;49;00m[90m[39;49;00m[0m
[1m[31mE         Use -v to get more diff[0m

[1m[31mtests/test_ext_autodoc_autoclass.py[0m:83: AssertionError
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/dd1615c59[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

[33m=============================== warnings summary ===============================[0m
sphinx/util/docutils.py:45
  /testbed/sphinx/util/docutils.py:45: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
    __version_info__ = tuple(LooseVersion(docutils.__version__).version)

sphinx/registry.py:22
  /testbed/sphinx/registry.py:22: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
    from pkg_resources import iter_entry_points

../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('sphinxcontrib')`.
  Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
    declare_namespace(pkg)

sphinx/directives/patches.py:14
  /testbed/sphinx/directives/patches.py:14: DeprecationWarning: The `docutils.parsers.rst.directive.html` module will be removed in Docutils 2.0. Since Docutils 0.18, the "Meta" node is defined in `docutils.parsers.rst.directives.misc`.
    from docutils.parsers.rst.directives import html, images, tables

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
==================================== PASSES ====================================
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/dd1615c59[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/dd1615c59[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/dd1615c59[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/dd1615c59[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/ext-autodoc
# outdir: /tmp/pytest-of-root/pytest-0/ext-autodoc/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/dd1615c59[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

============================= slowest 25 durations =============================
0.34s setup    tests/test_ext_autodoc_autoclass.py::test_classes
0.04s call     tests/test_ext_autodoc_autoclass.py::test_inherited_instance_variable
0.02s setup    tests/test_ext_autodoc_autoclass.py::test_decorators
0.01s setup    tests/test_ext_autodoc_autoclass.py::test_slots_attribute
0.01s setup    tests/test_ext_autodoc_autoclass.py::test_instance_variable
0.01s setup    tests/test_ext_autodoc_autoclass.py::test_show_inheritance_for_subclass_of_generic_type
0.01s setup    tests/test_ext_autodoc_autoclass.py::test_inherited_instance_variable
0.01s call     tests/test_ext_autodoc_autoclass.py::test_classes
0.01s call     tests/test_ext_autodoc_autoclass.py::test_decorators
0.01s call     tests/test_ext_autodoc_autoclass.py::test_instance_variable

(8 durations < 0.005s hidden.  Use -vv to show these durations.)
[36m[1m=========================== short test summary info ============================[0m
[32mPASSED[0m tests/test_ext_autodoc_autoclass.py::[1mtest_classes[0m
[32mPASSED[0m tests/test_ext_autodoc_autoclass.py::[1mtest_instance_variable[0m
[32mPASSED[0m tests/test_ext_autodoc_autoclass.py::[1mtest_decorators[0m
[32mPASSED[0m tests/test_ext_autodoc_autoclass.py::[1mtest_slots_attribute[0m
[32mPASSED[0m tests/test_ext_autodoc_autoclass.py::[1mtest_show_inheritance_for_subclass_of_generic_type[0m
[31mFAILED[0m tests/test_ext_autodoc_autoclass.py::[1mtest_inherited_instance_variable[0m - AssertionError: assert ['', '.. py:c...r.attr2', ...] == ['', '.. py:c...r....
[31m=================== [31m[1m1 failed[0m, [32m5 passed[0m, [33m7 warnings[0m[31m in 0.76s[0m[31m ====================[0m
py39: exit 1 (1.34 seconds) /testbed> python -X dev -m pytest -rA --durations 25 tests/roots/test-ext-autodoc/target/instance_variable.py tests/test_ext_autodoc_autoclass.py pid=105
  py39: FAIL code 1 (1.35=setup[0.01]+cmd[1.34] seconds)
  evaluation failed :( (1.43 seconds)
+ git checkout dd1615c59dc6fff633e27dbb3861f2d27e1fb976 tests/test_ext_autodoc_autoclass.py
Updated 1 path from 557f988e2
