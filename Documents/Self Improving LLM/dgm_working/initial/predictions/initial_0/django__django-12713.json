{"instance_id": "django__django-12713", "model_name_or_path": "nerf_editwholefiles_med_0", "model_patch": "diff --git a/django/contrib/admin/options.py b/django/contrib/admin/options.py\nindex 69a5fbf389..33cadfd0c8 100644\n--- a/django/contrib/admin/options.py\n+++ b/django/contrib/admin/options.py\n@@ -230,7 +230,6 @@ class BaseModelAdmin(metaclass=forms.MediaDefiningClass):\n                 kwargs['widget'] = widgets.AdminRadioSelect(attrs={\n                     'class': get_ul_class(self.radio_fields[db_field.name]),\n                 })\n-                kwargs['empty_label'] = _('None') if db_field.blank else None\n \n         if 'queryset' not in kwargs:\n             queryset = self.get_field_queryset(db, db_field, request)\n@@ -240,25 +239,19 @@ class BaseModelAdmin(metaclass=forms.MediaDefiningClass):\n         return db_field.formfield(**kwargs)\n \n     def formfield_for_manytomany(self, db_field, request, **kwargs):\n-        \"\"\"\n-        Get a form Field for a ManyToManyField.\n-        \"\"\"\n-        # If it uses an intermediary model that isn't auto created, don't show\n-        # a field in admin.\n+    def formfield_for_manytomany(self, db_field, request, **kwargs):\n         if not db_field.remote_field.through._meta.auto_created:\n             return None\n         db = kwargs.get('using')\n \n-        autocomplete_fields = self.get_autocomplete_fields(request)\n-        if db_field.name in autocomplete_fields:\n-            kwargs['widget'] = AutocompleteSelectMultiple(db_field.remote_field, self.admin_site, using=db)\n-        elif db_field.name in self.raw_id_fields:\n-            kwargs['widget'] = widgets.ManyToManyRawIdWidget(db_field.remote_field, self.admin_site, using=db)\n-        elif db_field.name in [*self.filter_vertical, *self.filter_horizontal]:\n-            kwargs['widget'] = widgets.FilteredSelectMultiple(\n-                db_field.verbose_name,\n-                db_field.name in self.filter_vertical\n-            )\n+        if 'widget' not in kwargs:\n+            autocomplete_fields = self.get_autocomplete_fields(request)\n+            if db_field.name in autocomplete_fields:\n+                kwargs['widget'] = AutocompleteSelectMultiple(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in self.raw_id_fields:\n+                kwargs['widget'] = widgets.ManyToManyRawIdWidget(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in [*self.filter_vertical, *self.filter_horizontal]:\n+                kwargs['widget'] = widgets.FilteredSelectMultiple(db_field.verbose_name, db_field.name in self.filter_vertical)\n \n         if 'queryset' not in kwargs:\n             queryset = self.get_field_queryset(db, db_field, request)\n@@ -266,1919 +259,16 @@ class BaseModelAdmin(metaclass=forms.MediaDefiningClass):\n                 kwargs['queryset'] = queryset\n \n         form_field = db_field.formfield(**kwargs)\n-        if (isinstance(form_field.widget, SelectMultiple) and\n-                not isinstance(form_field.widget, (CheckboxSelectMultiple, AutocompleteSelectMultiple))):\n-            msg = _('Hold down “Control”, or “Command” on a Mac, to select more than one.')\n+        if (isinstance(form_field.widget, SelectMultiple) and not isinstance(form_field.widget, (CheckboxSelectMultiple, AutocompleteSelectMultiple))):\n+            msg = _('Hold down \"Control\", or \"Command\" on a Mac, to select more than one.')\n             help_text = form_field.help_text\n             form_field.help_text = format_lazy('{} {}', help_text, msg) if help_text else msg\n         return form_field\n \n+\n     def get_autocomplete_fields(self, request):\n         \"\"\"\n         Return a list of ForeignKey and/or ManyToMany fields which should use\n         an autocomplete widget.\n         \"\"\"\n-        return self.autocomplete_fields\n-\n-    def get_view_on_site_url(self, obj=None):\n-        if obj is None or not self.view_on_site:\n-            return None\n-\n-        if callable(self.view_on_site):\n-            return self.view_on_site(obj)\n-        elif self.view_on_site and hasattr(obj, 'get_absolute_url'):\n-            # use the ContentType lookup if view_on_site is True\n-            return reverse('admin:view_on_site', kwargs={\n-                'content_type_id': get_content_type_for_model(obj).pk,\n-                'object_id': obj.pk\n-            })\n-\n-    def get_empty_value_display(self):\n-        \"\"\"\n-        Return the empty_value_display set on ModelAdmin or AdminSite.\n-        \"\"\"\n-        try:\n-            return mark_safe(self.empty_value_display)\n-        except AttributeError:\n-            return mark_safe(self.admin_site.empty_value_display)\n-\n-    def get_exclude(self, request, obj=None):\n-        \"\"\"\n-        Hook for specifying exclude.\n-        \"\"\"\n-        return self.exclude\n-\n-    def get_fields(self, request, obj=None):\n-        \"\"\"\n-        Hook for specifying fields.\n-        \"\"\"\n-        if self.fields:\n-            return self.fields\n-        # _get_form_for_get_fields() is implemented in subclasses.\n-        form = self._get_form_for_get_fields(request, obj)\n-        return [*form.base_fields, *self.get_readonly_fields(request, obj)]\n-\n-    def get_fieldsets(self, request, obj=None):\n-        \"\"\"\n-        Hook for specifying fieldsets.\n-        \"\"\"\n-        if self.fieldsets:\n-            return self.fieldsets\n-        return [(None, {'fields': self.get_fields(request, obj)})]\n-\n-    def get_inlines(self, request, obj):\n-        \"\"\"Hook for specifying custom inlines.\"\"\"\n-        return self.inlines\n-\n-    def get_ordering(self, request):\n-        \"\"\"\n-        Hook for specifying field ordering.\n-        \"\"\"\n-        return self.ordering or ()  # otherwise we might try to *None, which is bad ;)\n-\n-    def get_readonly_fields(self, request, obj=None):\n-        \"\"\"\n-        Hook for specifying custom readonly fields.\n-        \"\"\"\n-        return self.readonly_fields\n-\n-    def get_prepopulated_fields(self, request, obj=None):\n-        \"\"\"\n-        Hook for specifying custom prepopulated fields.\n-        \"\"\"\n-        return self.prepopulated_fields\n-\n-    def get_queryset(self, request):\n-        \"\"\"\n-        Return a QuerySet of all model instances that can be edited by the\n-        admin site. This is used by changelist_view.\n-        \"\"\"\n-        qs = self.model._default_manager.get_queryset()\n-        # TODO: this should be handled by some parameter to the ChangeList.\n-        ordering = self.get_ordering(request)\n-        if ordering:\n-            qs = qs.order_by(*ordering)\n-        return qs\n-\n-    def get_sortable_by(self, request):\n-        \"\"\"Hook for specifying which fields can be sorted in the changelist.\"\"\"\n-        return self.sortable_by if self.sortable_by is not None else self.get_list_display(request)\n-\n-    def lookup_allowed(self, lookup, value):\n-        from django.contrib.admin.filters import SimpleListFilter\n-\n-        model = self.model\n-        # Check FKey lookups that are allowed, so that popups produced by\n-        # ForeignKeyRawIdWidget, on the basis of ForeignKey.limit_choices_to,\n-        # are allowed to work.\n-        for fk_lookup in model._meta.related_fkey_lookups:\n-            # As ``limit_choices_to`` can be a callable, invoke it here.\n-            if callable(fk_lookup):\n-                fk_lookup = fk_lookup()\n-            if (lookup, value) in widgets.url_params_from_lookup_dict(fk_lookup).items():\n-                return True\n-\n-        relation_parts = []\n-        prev_field = None\n-        for part in lookup.split(LOOKUP_SEP):\n-            try:\n-                field = model._meta.get_field(part)\n-            except FieldDoesNotExist:\n-                # Lookups on nonexistent fields are ok, since they're ignored\n-                # later.\n-                break\n-            # It is allowed to filter on values that would be found from local\n-            # model anyways. For example, if you filter on employee__department__id,\n-            # then the id value would be found already from employee__department_id.\n-            if not prev_field or (prev_field.is_relation and\n-                                  field not in prev_field.get_path_info()[-1].target_fields):\n-                relation_parts.append(part)\n-            if not getattr(field, 'get_path_info', None):\n-                # This is not a relational field, so further parts\n-                # must be transforms.\n-                break\n-            prev_field = field\n-            model = field.get_path_info()[-1].to_opts.model\n-\n-        if len(relation_parts) <= 1:\n-            # Either a local field filter, or no fields at all.\n-            return True\n-        valid_lookups = {self.date_hierarchy}\n-        for filter_item in self.list_filter:\n-            if isinstance(filter_item, type) and issubclass(filter_item, SimpleListFilter):\n-                valid_lookups.add(filter_item.parameter_name)\n-            elif isinstance(filter_item, (list, tuple)):\n-                valid_lookups.add(filter_item[0])\n-            else:\n-                valid_lookups.add(filter_item)\n-\n-        # Is it a valid relational lookup?\n-        return not {\n-            LOOKUP_SEP.join(relation_parts),\n-            LOOKUP_SEP.join(relation_parts + [part])\n-        }.isdisjoint(valid_lookups)\n-\n-    def to_field_allowed(self, request, to_field):\n-        \"\"\"\n-        Return True if the model associated with this admin should be\n-        allowed to be referenced by the specified field.\n-        \"\"\"\n-        opts = self.model._meta\n-\n-        try:\n-            field = opts.get_field(to_field)\n-        except FieldDoesNotExist:\n-            return False\n-\n-        # Always allow referencing the primary key since it's already possible\n-        # to get this information from the change view URL.\n-        if field.primary_key:\n-            return True\n-\n-        # Allow reverse relationships to models defining m2m fields if they\n-        # target the specified field.\n-        for many_to_many in opts.many_to_many:\n-            if many_to_many.m2m_target_field_name() == to_field:\n-                return True\n-\n-        # Make sure at least one of the models registered for this site\n-        # references this field through a FK or a M2M relationship.\n-        registered_models = set()\n-        for model, admin in self.admin_site._registry.items():\n-            registered_models.add(model)\n-            for inline in admin.inlines:\n-                registered_models.add(inline.model)\n-\n-        related_objects = (\n-            f for f in opts.get_fields(include_hidden=True)\n-            if (f.auto_created and not f.concrete)\n-        )\n-        for related_object in related_objects:\n-            related_model = related_object.related_model\n-            remote_field = related_object.field.remote_field\n-            if (any(issubclass(model, related_model) for model in registered_models) and\n-                    hasattr(remote_field, 'get_related_field') and\n-                    remote_field.get_related_field() == field):\n-                return True\n-\n-        return False\n-\n-    def has_add_permission(self, request):\n-        \"\"\"\n-        Return True if the given request has permission to add an object.\n-        Can be overridden by the user in subclasses.\n-        \"\"\"\n-        opts = self.opts\n-        codename = get_permission_codename('add', opts)\n-        return request.user.has_perm(\"%s.%s\" % (opts.app_label, codename))\n-\n-    def has_change_permission(self, request, obj=None):\n-        \"\"\"\n-        Return True if the given request has permission to change the given\n-        Django model instance, the default implementation doesn't examine the\n-        `obj` parameter.\n-\n-        Can be overridden by the user in subclasses. In such case it should\n-        return True if the given request has permission to change the `obj`\n-        model instance. If `obj` is None, this should return True if the given\n-        request has permission to change *any* object of the given type.\n-        \"\"\"\n-        opts = self.opts\n-        codename = get_permission_codename('change', opts)\n-        return request.user.has_perm(\"%s.%s\" % (opts.app_label, codename))\n-\n-    def has_delete_permission(self, request, obj=None):\n-        \"\"\"\n-        Return True if the given request has permission to change the given\n-        Django model instance, the default implementation doesn't examine the\n-        `obj` parameter.\n-\n-        Can be overridden by the user in subclasses. In such case it should\n-        return True if the given request has permission to delete the `obj`\n-        model instance. If `obj` is None, this should return True if the given\n-        request has permission to delete *any* object of the given type.\n-        \"\"\"\n-        opts = self.opts\n-        codename = get_permission_codename('delete', opts)\n-        return request.user.has_perm(\"%s.%s\" % (opts.app_label, codename))\n-\n-    def has_view_permission(self, request, obj=None):\n-        \"\"\"\n-        Return True if the given request has permission to view the given\n-        Django model instance. The default implementation doesn't examine the\n-        `obj` parameter.\n-\n-        If overridden by the user in subclasses, it should return True if the\n-        given request has permission to view the `obj` model instance. If `obj`\n-        is None, it should return True if the request has permission to view\n-        any object of the given type.\n-        \"\"\"\n-        opts = self.opts\n-        codename_view = get_permission_codename('view', opts)\n-        codename_change = get_permission_codename('change', opts)\n-        return (\n-            request.user.has_perm('%s.%s' % (opts.app_label, codename_view)) or\n-            request.user.has_perm('%s.%s' % (opts.app_label, codename_change))\n-        )\n-\n-    def has_view_or_change_permission(self, request, obj=None):\n-        return self.has_view_permission(request, obj) or self.has_change_permission(request, obj)\n-\n-    def has_module_permission(self, request):\n-        \"\"\"\n-        Return True if the given request has any permission in the given\n-        app label.\n-\n-        Can be overridden by the user in subclasses. In such case it should\n-        return True if the given request has permission to view the module on\n-        the admin index page and access the module's index page. Overriding it\n-        does not restrict access to the add, change or delete views. Use\n-        `ModelAdmin.has_(add|change|delete)_permission` for that.\n-        \"\"\"\n-        return request.user.has_module_perms(self.opts.app_label)\n-\n-\n-class ModelAdmin(BaseModelAdmin):\n-    \"\"\"Encapsulate all admin options and functionality for a given model.\"\"\"\n-\n-    list_display = ('__str__',)\n-    list_display_links = ()\n-    list_filter = ()\n-    list_select_related = False\n-    list_per_page = 100\n-    list_max_show_all = 200\n-    list_editable = ()\n-    search_fields = ()\n-    date_hierarchy = None\n-    save_as = False\n-    save_as_continue = True\n-    save_on_top = False\n-    paginator = Paginator\n-    preserve_filters = True\n-    inlines = []\n-\n-    # Custom templates (designed to be over-ridden in subclasses)\n-    add_form_template = None\n-    change_form_template = None\n-    change_list_template = None\n-    delete_confirmation_template = None\n-    delete_selected_confirmation_template = None\n-    object_history_template = None\n-    popup_response_template = None\n-\n-    # Actions\n-    actions = []\n-    action_form = helpers.ActionForm\n-    actions_on_top = True\n-    actions_on_bottom = False\n-    actions_selection_counter = True\n-    checks_class = ModelAdminChecks\n-\n-    def __init__(self, model, admin_site):\n-        self.model = model\n-        self.opts = model._meta\n-        self.admin_site = admin_site\n-        super().__init__()\n-\n-    def __str__(self):\n-        return \"%s.%s\" % (self.model._meta.app_label, self.__class__.__name__)\n-\n-    def get_inline_instances(self, request, obj=None):\n-        inline_instances = []\n-        for inline_class in self.get_inlines(request, obj):\n-            inline = inline_class(self.model, self.admin_site)\n-            if request:\n-                if not (inline.has_view_or_change_permission(request, obj) or\n-                        inline.has_add_permission(request, obj) or\n-                        inline.has_delete_permission(request, obj)):\n-                    continue\n-                if not inline.has_add_permission(request, obj):\n-                    inline.max_num = 0\n-            inline_instances.append(inline)\n-\n-        return inline_instances\n-\n-    def get_urls(self):\n-        from django.urls import path\n-\n-        def wrap(view):\n-            def wrapper(*args, **kwargs):\n-                return self.admin_site.admin_view(view)(*args, **kwargs)\n-            wrapper.model_admin = self\n-            return update_wrapper(wrapper, view)\n-\n-        info = self.model._meta.app_label, self.model._meta.model_name\n-\n-        return [\n-            path('', wrap(self.changelist_view), name='%s_%s_changelist' % info),\n-            path('add/', wrap(self.add_view), name='%s_%s_add' % info),\n-            path('autocomplete/', wrap(self.autocomplete_view), name='%s_%s_autocomplete' % info),\n-            path('<path:object_id>/history/', wrap(self.history_view), name='%s_%s_history' % info),\n-            path('<path:object_id>/delete/', wrap(self.delete_view), name='%s_%s_delete' % info),\n-            path('<path:object_id>/change/', wrap(self.change_view), name='%s_%s_change' % info),\n-            # For backwards compatibility (was the change url before 1.9)\n-            path('<path:object_id>/', wrap(RedirectView.as_view(\n-                pattern_name='%s:%s_%s_change' % ((self.admin_site.name,) + info)\n-            ))),\n-        ]\n-\n-    @property\n-    def urls(self):\n-        return self.get_urls()\n-\n-    @property\n-    def media(self):\n-        extra = '' if settings.DEBUG else '.min'\n-        js = [\n-            'vendor/jquery/jquery%s.js' % extra,\n-            'jquery.init.js',\n-            'core.js',\n-            'admin/RelatedObjectLookups.js',\n-            'actions%s.js' % extra,\n-            'urlify.js',\n-            'prepopulate%s.js' % extra,\n-            'vendor/xregexp/xregexp%s.js' % extra,\n-        ]\n-        return forms.Media(js=['admin/js/%s' % url for url in js])\n-\n-    def get_model_perms(self, request):\n-        \"\"\"\n-        Return a dict of all perms for this model. This dict has the keys\n-        ``add``, ``change``, ``delete``, and ``view`` mapping to the True/False\n-        for each of those actions.\n-        \"\"\"\n-        return {\n-            'add': self.has_add_permission(request),\n-            'change': self.has_change_permission(request),\n-            'delete': self.has_delete_permission(request),\n-            'view': self.has_view_permission(request),\n-        }\n-\n-    def _get_form_for_get_fields(self, request, obj):\n-        return self.get_form(request, obj, fields=None)\n-\n-    def get_form(self, request, obj=None, change=False, **kwargs):\n-        \"\"\"\n-        Return a Form class for use in the admin add view. This is used by\n-        add_view and change_view.\n-        \"\"\"\n-        if 'fields' in kwargs:\n-            fields = kwargs.pop('fields')\n-        else:\n-            fields = flatten_fieldsets(self.get_fieldsets(request, obj))\n-        excluded = self.get_exclude(request, obj)\n-        exclude = [] if excluded is None else list(excluded)\n-        readonly_fields = self.get_readonly_fields(request, obj)\n-        exclude.extend(readonly_fields)\n-        # Exclude all fields if it's a change form and the user doesn't have\n-        # the change permission.\n-        if change and hasattr(request, 'user') and not self.has_change_permission(request, obj):\n-            exclude.extend(fields)\n-        if excluded is None and hasattr(self.form, '_meta') and self.form._meta.exclude:\n-            # Take the custom ModelForm's Meta.exclude into account only if the\n-            # ModelAdmin doesn't define its own.\n-            exclude.extend(self.form._meta.exclude)\n-        # if exclude is an empty list we pass None to be consistent with the\n-        # default on modelform_factory\n-        exclude = exclude or None\n-\n-        # Remove declared form fields which are in readonly_fields.\n-        new_attrs = dict.fromkeys(f for f in readonly_fields if f in self.form.declared_fields)\n-        form = type(self.form.__name__, (self.form,), new_attrs)\n-\n-        defaults = {\n-            'form': form,\n-            'fields': fields,\n-            'exclude': exclude,\n-            'formfield_callback': partial(self.formfield_for_dbfield, request=request),\n-            **kwargs,\n-        }\n-\n-        if defaults['fields'] is None and not modelform_defines_fields(defaults['form']):\n-            defaults['fields'] = forms.ALL_FIELDS\n-\n-        try:\n-            return modelform_factory(self.model, **defaults)\n-        except FieldError as e:\n-            raise FieldError(\n-                '%s. Check fields/fieldsets/exclude attributes of class %s.'\n-                % (e, self.__class__.__name__)\n-            )\n-\n-    def get_changelist(self, request, **kwargs):\n-        \"\"\"\n-        Return the ChangeList class for use on the changelist page.\n-        \"\"\"\n-        from django.contrib.admin.views.main import ChangeList\n-        return ChangeList\n-\n-    def get_changelist_instance(self, request):\n-        \"\"\"\n-        Return a `ChangeList` instance based on `request`. May raise\n-        `IncorrectLookupParameters`.\n-        \"\"\"\n-        list_display = self.get_list_display(request)\n-        list_display_links = self.get_list_display_links(request, list_display)\n-        # Add the action checkboxes if any actions are available.\n-        if self.get_actions(request):\n-            list_display = ['action_checkbox', *list_display]\n-        sortable_by = self.get_sortable_by(request)\n-        ChangeList = self.get_changelist(request)\n-        return ChangeList(\n-            request,\n-            self.model,\n-            list_display,\n-            list_display_links,\n-            self.get_list_filter(request),\n-            self.date_hierarchy,\n-            self.get_search_fields(request),\n-            self.get_list_select_related(request),\n-            self.list_per_page,\n-            self.list_max_show_all,\n-            self.list_editable,\n-            self,\n-            sortable_by,\n-        )\n-\n-    def get_object(self, request, object_id, from_field=None):\n-        \"\"\"\n-        Return an instance matching the field and value provided, the primary\n-        key is used if no field is provided. Return ``None`` if no match is\n-        found or the object_id fails validation.\n-        \"\"\"\n-        queryset = self.get_queryset(request)\n-        model = queryset.model\n-        field = model._meta.pk if from_field is None else model._meta.get_field(from_field)\n-        try:\n-            object_id = field.to_python(object_id)\n-            return queryset.get(**{field.name: object_id})\n-        except (model.DoesNotExist, ValidationError, ValueError):\n-            return None\n-\n-    def get_changelist_form(self, request, **kwargs):\n-        \"\"\"\n-        Return a Form class for use in the Formset on the changelist page.\n-        \"\"\"\n-        defaults = {\n-            'formfield_callback': partial(self.formfield_for_dbfield, request=request),\n-            **kwargs,\n-        }\n-        if defaults.get('fields') is None and not modelform_defines_fields(defaults.get('form')):\n-            defaults['fields'] = forms.ALL_FIELDS\n-\n-        return modelform_factory(self.model, **defaults)\n-\n-    def get_changelist_formset(self, request, **kwargs):\n-        \"\"\"\n-        Return a FormSet class for use on the changelist page if list_editable\n-        is used.\n-        \"\"\"\n-        defaults = {\n-            'formfield_callback': partial(self.formfield_for_dbfield, request=request),\n-            **kwargs,\n-        }\n-        return modelformset_factory(\n-            self.model, self.get_changelist_form(request), extra=0,\n-            fields=self.list_editable, **defaults\n-        )\n-\n-    def get_formsets_with_inlines(self, request, obj=None):\n-        \"\"\"\n-        Yield formsets and the corresponding inlines.\n-        \"\"\"\n-        for inline in self.get_inline_instances(request, obj):\n-            yield inline.get_formset(request, obj), inline\n-\n-    def get_paginator(self, request, queryset, per_page, orphans=0, allow_empty_first_page=True):\n-        return self.paginator(queryset, per_page, orphans, allow_empty_first_page)\n-\n-    def log_addition(self, request, object, message):\n-        \"\"\"\n-        Log that an object has been successfully added.\n-\n-        The default implementation creates an admin LogEntry object.\n-        \"\"\"\n-        from django.contrib.admin.models import LogEntry, ADDITION\n-        return LogEntry.objects.log_action(\n-            user_id=request.user.pk,\n-            content_type_id=get_content_type_for_model(object).pk,\n-            object_id=object.pk,\n-            object_repr=str(object),\n-            action_flag=ADDITION,\n-            change_message=message,\n-        )\n-\n-    def log_change(self, request, object, message):\n-        \"\"\"\n-        Log that an object has been successfully changed.\n-\n-        The default implementation creates an admin LogEntry object.\n-        \"\"\"\n-        from django.contrib.admin.models import LogEntry, CHANGE\n-        return LogEntry.objects.log_action(\n-            user_id=request.user.pk,\n-            content_type_id=get_content_type_for_model(object).pk,\n-            object_id=object.pk,\n-            object_repr=str(object),\n-            action_flag=CHANGE,\n-            change_message=message,\n-        )\n-\n-    def log_deletion(self, request, object, object_repr):\n-        \"\"\"\n-        Log that an object will be deleted. Note that this method must be\n-        called before the deletion.\n-\n-        The default implementation creates an admin LogEntry object.\n-        \"\"\"\n-        from django.contrib.admin.models import LogEntry, DELETION\n-        return LogEntry.objects.log_action(\n-            user_id=request.user.pk,\n-            content_type_id=get_content_type_for_model(object).pk,\n-            object_id=object.pk,\n-            object_repr=object_repr,\n-            action_flag=DELETION,\n-        )\n-\n-    def action_checkbox(self, obj):\n-        \"\"\"\n-        A list_display column containing a checkbox widget.\n-        \"\"\"\n-        return helpers.checkbox.render(helpers.ACTION_CHECKBOX_NAME, str(obj.pk))\n-    action_checkbox.short_description = mark_safe('<input type=\"checkbox\" id=\"action-toggle\">')\n-\n-    def _get_base_actions(self):\n-        \"\"\"Return the list of actions, prior to any request-based filtering.\"\"\"\n-        actions = []\n-\n-        # Gather actions from the admin site first\n-        for (name, func) in self.admin_site.actions:\n-            description = getattr(func, 'short_description', name.replace('_', ' '))\n-            actions.append((func, name, description))\n-        # Add actions from this ModelAdmin.\n-        actions.extend(self.get_action(action) for action in self.actions or [])\n-        # get_action might have returned None, so filter any of those out.\n-        return filter(None, actions)\n-\n-    def _filter_actions_by_permissions(self, request, actions):\n-        \"\"\"Filter out any actions that the user doesn't have access to.\"\"\"\n-        filtered_actions = []\n-        for action in actions:\n-            callable = action[0]\n-            if not hasattr(callable, 'allowed_permissions'):\n-                filtered_actions.append(action)\n-                continue\n-            permission_checks = (\n-                getattr(self, 'has_%s_permission' % permission)\n-                for permission in callable.allowed_permissions\n-            )\n-            if any(has_permission(request) for has_permission in permission_checks):\n-                filtered_actions.append(action)\n-        return filtered_actions\n-\n-    def get_actions(self, request):\n-        \"\"\"\n-        Return a dictionary mapping the names of all actions for this\n-        ModelAdmin to a tuple of (callable, name, description) for each action.\n-        \"\"\"\n-        # If self.actions is set to None that means actions are disabled on\n-        # this page.\n-        if self.actions is None or IS_POPUP_VAR in request.GET:\n-            return {}\n-        actions = self._filter_actions_by_permissions(request, self._get_base_actions())\n-        return {name: (func, name, desc) for func, name, desc in actions}\n-\n-    def get_action_choices(self, request, default_choices=models.BLANK_CHOICE_DASH):\n-        \"\"\"\n-        Return a list of choices for use in a form object.  Each choice is a\n-        tuple (name, description).\n-        \"\"\"\n-        choices = [] + default_choices\n-        for func, name, description in self.get_actions(request).values():\n-            choice = (name, description % model_format_dict(self.opts))\n-            choices.append(choice)\n-        return choices\n-\n-    def get_action(self, action):\n-        \"\"\"\n-        Return a given action from a parameter, which can either be a callable,\n-        or the name of a method on the ModelAdmin.  Return is a tuple of\n-        (callable, name, description).\n-        \"\"\"\n-        # If the action is a callable, just use it.\n-        if callable(action):\n-            func = action\n-            action = action.__name__\n-\n-        # Next, look for a method. Grab it off self.__class__ to get an unbound\n-        # method instead of a bound one; this ensures that the calling\n-        # conventions are the same for functions and methods.\n-        elif hasattr(self.__class__, action):\n-            func = getattr(self.__class__, action)\n-\n-        # Finally, look for a named method on the admin site\n-        else:\n-            try:\n-                func = self.admin_site.get_action(action)\n-            except KeyError:\n-                return None\n-\n-        if hasattr(func, 'short_description'):\n-            description = func.short_description\n-        else:\n-            description = capfirst(action.replace('_', ' '))\n-        return func, action, description\n-\n-    def get_list_display(self, request):\n-        \"\"\"\n-        Return a sequence containing the fields to be displayed on the\n-        changelist.\n-        \"\"\"\n-        return self.list_display\n-\n-    def get_list_display_links(self, request, list_display):\n-        \"\"\"\n-        Return a sequence containing the fields to be displayed as links\n-        on the changelist. The list_display parameter is the list of fields\n-        returned by get_list_display().\n-        \"\"\"\n-        if self.list_display_links or self.list_display_links is None or not list_display:\n-            return self.list_display_links\n-        else:\n-            # Use only the first item in list_display as link\n-            return list(list_display)[:1]\n-\n-    def get_list_filter(self, request):\n-        \"\"\"\n-        Return a sequence containing the fields to be displayed as filters in\n-        the right sidebar of the changelist page.\n-        \"\"\"\n-        return self.list_filter\n-\n-    def get_list_select_related(self, request):\n-        \"\"\"\n-        Return a list of fields to add to the select_related() part of the\n-        changelist items query.\n-        \"\"\"\n-        return self.list_select_related\n-\n-    def get_search_fields(self, request):\n-        \"\"\"\n-        Return a sequence containing the fields to be searched whenever\n-        somebody submits a search query.\n-        \"\"\"\n-        return self.search_fields\n-\n-    def get_search_results(self, request, queryset, search_term):\n-        \"\"\"\n-        Return a tuple containing a queryset to implement the search\n-        and a boolean indicating if the results may contain duplicates.\n-        \"\"\"\n-        # Apply keyword searches.\n-        def construct_search(field_name):\n-            if field_name.startswith('^'):\n-                return \"%s__istartswith\" % field_name[1:]\n-            elif field_name.startswith('='):\n-                return \"%s__iexact\" % field_name[1:]\n-            elif field_name.startswith('@'):\n-                return \"%s__search\" % field_name[1:]\n-            # Use field_name if it includes a lookup.\n-            opts = queryset.model._meta\n-            lookup_fields = field_name.split(LOOKUP_SEP)\n-            # Go through the fields, following all relations.\n-            prev_field = None\n-            for path_part in lookup_fields:\n-                if path_part == 'pk':\n-                    path_part = opts.pk.name\n-                try:\n-                    field = opts.get_field(path_part)\n-                except FieldDoesNotExist:\n-                    # Use valid query lookups.\n-                    if prev_field and prev_field.get_lookup(path_part):\n-                        return field_name\n-                else:\n-                    prev_field = field\n-                    if hasattr(field, 'get_path_info'):\n-                        # Update opts to follow the relation.\n-                        opts = field.get_path_info()[-1].to_opts\n-            # Otherwise, use the field with icontains.\n-            return \"%s__icontains\" % field_name\n-\n-        use_distinct = False\n-        search_fields = self.get_search_fields(request)\n-        if search_fields and search_term:\n-            orm_lookups = [construct_search(str(search_field))\n-                           for search_field in search_fields]\n-            for bit in search_term.split():\n-                or_queries = [models.Q(**{orm_lookup: bit})\n-                              for orm_lookup in orm_lookups]\n-                queryset = queryset.filter(reduce(operator.or_, or_queries))\n-            use_distinct |= any(lookup_needs_distinct(self.opts, search_spec) for search_spec in orm_lookups)\n-\n-        return queryset, use_distinct\n-\n-    def get_preserved_filters(self, request):\n-        \"\"\"\n-        Return the preserved filters querystring.\n-        \"\"\"\n-        match = request.resolver_match\n-        if self.preserve_filters and match:\n-            opts = self.model._meta\n-            current_url = '%s:%s' % (match.app_name, match.url_name)\n-            changelist_url = 'admin:%s_%s_changelist' % (opts.app_label, opts.model_name)\n-            if current_url == changelist_url:\n-                preserved_filters = request.GET.urlencode()\n-            else:\n-                preserved_filters = request.GET.get('_changelist_filters')\n-\n-            if preserved_filters:\n-                return urlencode({'_changelist_filters': preserved_filters})\n-        return ''\n-\n-    def construct_change_message(self, request, form, formsets, add=False):\n-        \"\"\"\n-        Construct a JSON structure describing changes from a changed object.\n-        \"\"\"\n-        return construct_change_message(form, formsets, add)\n-\n-    def message_user(self, request, message, level=messages.INFO, extra_tags='',\n-                     fail_silently=False):\n-        \"\"\"\n-        Send a message to the user. The default implementation\n-        posts a message using the django.contrib.messages backend.\n-\n-        Exposes almost the same API as messages.add_message(), but accepts the\n-        positional arguments in a different order to maintain backwards\n-        compatibility. For convenience, it accepts the `level` argument as\n-        a string rather than the usual level number.\n-        \"\"\"\n-        if not isinstance(level, int):\n-            # attempt to get the level if passed a string\n-            try:\n-                level = getattr(messages.constants, level.upper())\n-            except AttributeError:\n-                levels = messages.constants.DEFAULT_TAGS.values()\n-                levels_repr = ', '.join('`%s`' % l for l in levels)\n-                raise ValueError(\n-                    'Bad message level string: `%s`. Possible values are: %s'\n-                    % (level, levels_repr)\n-                )\n-\n-        messages.add_message(request, level, message, extra_tags=extra_tags, fail_silently=fail_silently)\n-\n-    def save_form(self, request, form, change):\n-        \"\"\"\n-        Given a ModelForm return an unsaved instance. ``change`` is True if\n-        the object is being changed, and False if it's being added.\n-        \"\"\"\n-        return form.save(commit=False)\n-\n-    def save_model(self, request, obj, form, change):\n-        \"\"\"\n-        Given a model instance save it to the database.\n-        \"\"\"\n-        obj.save()\n-\n-    def delete_model(self, request, obj):\n-        \"\"\"\n-        Given a model instance delete it from the database.\n-        \"\"\"\n-        obj.delete()\n-\n-    def delete_queryset(self, request, queryset):\n-        \"\"\"Given a queryset, delete it from the database.\"\"\"\n-        queryset.delete()\n-\n-    def save_formset(self, request, form, formset, change):\n-        \"\"\"\n-        Given an inline formset save it to the database.\n-        \"\"\"\n-        formset.save()\n-\n-    def save_related(self, request, form, formsets, change):\n-        \"\"\"\n-        Given the ``HttpRequest``, the parent ``ModelForm`` instance, the\n-        list of inline formsets and a boolean value based on whether the\n-        parent is being added or changed, save the related objects to the\n-        database. Note that at this point save_form() and save_model() have\n-        already been called.\n-        \"\"\"\n-        form.save_m2m()\n-        for formset in formsets:\n-            self.save_formset(request, form, formset, change=change)\n-\n-    def render_change_form(self, request, context, add=False, change=False, form_url='', obj=None):\n-        opts = self.model._meta\n-        app_label = opts.app_label\n-        preserved_filters = self.get_preserved_filters(request)\n-        form_url = add_preserved_filters({'preserved_filters': preserved_filters, 'opts': opts}, form_url)\n-        view_on_site_url = self.get_view_on_site_url(obj)\n-        has_editable_inline_admin_formsets = False\n-        for inline in context['inline_admin_formsets']:\n-            if inline.has_add_permission or inline.has_change_permission or inline.has_delete_permission:\n-                has_editable_inline_admin_formsets = True\n-                break\n-        context.update({\n-            'add': add,\n-            'change': change,\n-            'has_view_permission': self.has_view_permission(request, obj),\n-            'has_add_permission': self.has_add_permission(request),\n-            'has_change_permission': self.has_change_permission(request, obj),\n-            'has_delete_permission': self.has_delete_permission(request, obj),\n-            'has_editable_inline_admin_formsets': has_editable_inline_admin_formsets,\n-            'has_file_field': context['adminform'].form.is_multipart() or any(\n-                admin_formset.formset.is_multipart()\n-                for admin_formset in context['inline_admin_formsets']\n-            ),\n-            'has_absolute_url': view_on_site_url is not None,\n-            'absolute_url': view_on_site_url,\n-            'form_url': form_url,\n-            'opts': opts,\n-            'content_type_id': get_content_type_for_model(self.model).pk,\n-            'save_as': self.save_as,\n-            'save_on_top': self.save_on_top,\n-            'to_field_var': TO_FIELD_VAR,\n-            'is_popup_var': IS_POPUP_VAR,\n-            'app_label': app_label,\n-        })\n-        if add and self.add_form_template is not None:\n-            form_template = self.add_form_template\n-        else:\n-            form_template = self.change_form_template\n-\n-        request.current_app = self.admin_site.name\n-\n-        return TemplateResponse(request, form_template or [\n-            \"admin/%s/%s/change_form.html\" % (app_label, opts.model_name),\n-            \"admin/%s/change_form.html\" % app_label,\n-            \"admin/change_form.html\"\n-        ], context)\n-\n-    def response_add(self, request, obj, post_url_continue=None):\n-        \"\"\"\n-        Determine the HttpResponse for the add_view stage.\n-        \"\"\"\n-        opts = obj._meta\n-        preserved_filters = self.get_preserved_filters(request)\n-        obj_url = reverse(\n-            'admin:%s_%s_change' % (opts.app_label, opts.model_name),\n-            args=(quote(obj.pk),),\n-            current_app=self.admin_site.name,\n-        )\n-        # Add a link to the object's change form if the user can edit the obj.\n-        if self.has_change_permission(request, obj):\n-            obj_repr = format_html('<a href=\"{}\">{}</a>', urlquote(obj_url), obj)\n-        else:\n-            obj_repr = str(obj)\n-        msg_dict = {\n-            'name': opts.verbose_name,\n-            'obj': obj_repr,\n-        }\n-        # Here, we distinguish between different save types by checking for\n-        # the presence of keys in request.POST.\n-\n-        if IS_POPUP_VAR in request.POST:\n-            to_field = request.POST.get(TO_FIELD_VAR)\n-            if to_field:\n-                attr = str(to_field)\n-            else:\n-                attr = obj._meta.pk.attname\n-            value = obj.serializable_value(attr)\n-            popup_response_data = json.dumps({\n-                'value': str(value),\n-                'obj': str(obj),\n-            })\n-            return TemplateResponse(request, self.popup_response_template or [\n-                'admin/%s/%s/popup_response.html' % (opts.app_label, opts.model_name),\n-                'admin/%s/popup_response.html' % opts.app_label,\n-                'admin/popup_response.html',\n-            ], {\n-                'popup_response_data': popup_response_data,\n-            })\n-\n-        elif \"_continue\" in request.POST or (\n-                # Redirecting after \"Save as new\".\n-                \"_saveasnew\" in request.POST and self.save_as_continue and\n-                self.has_change_permission(request, obj)\n-        ):\n-            msg = _('The {name} “{obj}” was added successfully.')\n-            if self.has_change_permission(request, obj):\n-                msg += ' ' + _('You may edit it again below.')\n-            self.message_user(request, format_html(msg, **msg_dict), messages.SUCCESS)\n-            if post_url_continue is None:\n-                post_url_continue = obj_url\n-            post_url_continue = add_preserved_filters(\n-                {'preserved_filters': preserved_filters, 'opts': opts},\n-                post_url_continue\n-            )\n-            return HttpResponseRedirect(post_url_continue)\n-\n-        elif \"_addanother\" in request.POST:\n-            msg = format_html(\n-                _('The {name} “{obj}” was added successfully. You may add another {name} below.'),\n-                **msg_dict\n-            )\n-            self.message_user(request, msg, messages.SUCCESS)\n-            redirect_url = request.path\n-            redirect_url = add_preserved_filters({'preserved_filters': preserved_filters, 'opts': opts}, redirect_url)\n-            return HttpResponseRedirect(redirect_url)\n-\n-        else:\n-            msg = format_html(\n-                _('The {name} “{obj}” was added successfully.'),\n-                **msg_dict\n-            )\n-            self.message_user(request, msg, messages.SUCCESS)\n-            return self.response_post_save_add(request, obj)\n-\n-    def response_change(self, request, obj):\n-        \"\"\"\n-        Determine the HttpResponse for the change_view stage.\n-        \"\"\"\n-\n-        if IS_POPUP_VAR in request.POST:\n-            opts = obj._meta\n-            to_field = request.POST.get(TO_FIELD_VAR)\n-            attr = str(to_field) if to_field else opts.pk.attname\n-            value = request.resolver_match.kwargs['object_id']\n-            new_value = obj.serializable_value(attr)\n-            popup_response_data = json.dumps({\n-                'action': 'change',\n-                'value': str(value),\n-                'obj': str(obj),\n-                'new_value': str(new_value),\n-            })\n-            return TemplateResponse(request, self.popup_response_template or [\n-                'admin/%s/%s/popup_response.html' % (opts.app_label, opts.model_name),\n-                'admin/%s/popup_response.html' % opts.app_label,\n-                'admin/popup_response.html',\n-            ], {\n-                'popup_response_data': popup_response_data,\n-            })\n-\n-        opts = self.model._meta\n-        preserved_filters = self.get_preserved_filters(request)\n-\n-        msg_dict = {\n-            'name': opts.verbose_name,\n-            'obj': format_html('<a href=\"{}\">{}</a>', urlquote(request.path), obj),\n-        }\n-        if \"_continue\" in request.POST:\n-            msg = format_html(\n-                _('The {name} “{obj}” was changed successfully. You may edit it again below.'),\n-                **msg_dict\n-            )\n-            self.message_user(request, msg, messages.SUCCESS)\n-            redirect_url = request.path\n-            redirect_url = add_preserved_filters({'preserved_filters': preserved_filters, 'opts': opts}, redirect_url)\n-            return HttpResponseRedirect(redirect_url)\n-\n-        elif \"_saveasnew\" in request.POST:\n-            msg = format_html(\n-                _('The {name} “{obj}” was added successfully. You may edit it again below.'),\n-                **msg_dict\n-            )\n-            self.message_user(request, msg, messages.SUCCESS)\n-            redirect_url = reverse('admin:%s_%s_change' %\n-                                   (opts.app_label, opts.model_name),\n-                                   args=(obj.pk,),\n-                                   current_app=self.admin_site.name)\n-            redirect_url = add_preserved_filters({'preserved_filters': preserved_filters, 'opts': opts}, redirect_url)\n-            return HttpResponseRedirect(redirect_url)\n-\n-        elif \"_addanother\" in request.POST:\n-            msg = format_html(\n-                _('The {name} “{obj}” was changed successfully. You may add another {name} below.'),\n-                **msg_dict\n-            )\n-            self.message_user(request, msg, messages.SUCCESS)\n-            redirect_url = reverse('admin:%s_%s_add' %\n-                                   (opts.app_label, opts.model_name),\n-                                   current_app=self.admin_site.name)\n-            redirect_url = add_preserved_filters({'preserved_filters': preserved_filters, 'opts': opts}, redirect_url)\n-            return HttpResponseRedirect(redirect_url)\n-\n-        else:\n-            msg = format_html(\n-                _('The {name} “{obj}” was changed successfully.'),\n-                **msg_dict\n-            )\n-            self.message_user(request, msg, messages.SUCCESS)\n-            return self.response_post_save_change(request, obj)\n-\n-    def _response_post_save(self, request, obj):\n-        opts = self.model._meta\n-        if self.has_view_or_change_permission(request):\n-            post_url = reverse('admin:%s_%s_changelist' %\n-                               (opts.app_label, opts.model_name),\n-                               current_app=self.admin_site.name)\n-            preserved_filters = self.get_preserved_filters(request)\n-            post_url = add_preserved_filters({'preserved_filters': preserved_filters, 'opts': opts}, post_url)\n-        else:\n-            post_url = reverse('admin:index',\n-                               current_app=self.admin_site.name)\n-        return HttpResponseRedirect(post_url)\n-\n-    def response_post_save_add(self, request, obj):\n-        \"\"\"\n-        Figure out where to redirect after the 'Save' button has been pressed\n-        when adding a new object.\n-        \"\"\"\n-        return self._response_post_save(request, obj)\n-\n-    def response_post_save_change(self, request, obj):\n-        \"\"\"\n-        Figure out where to redirect after the 'Save' button has been pressed\n-        when editing an existing object.\n-        \"\"\"\n-        return self._response_post_save(request, obj)\n-\n-    def response_action(self, request, queryset):\n-        \"\"\"\n-        Handle an admin action. This is called if a request is POSTed to the\n-        changelist; it returns an HttpResponse if the action was handled, and\n-        None otherwise.\n-        \"\"\"\n-\n-        # There can be multiple action forms on the page (at the top\n-        # and bottom of the change list, for example). Get the action\n-        # whose button was pushed.\n-        try:\n-            action_index = int(request.POST.get('index', 0))\n-        except ValueError:\n-            action_index = 0\n-\n-        # Construct the action form.\n-        data = request.POST.copy()\n-        data.pop(helpers.ACTION_CHECKBOX_NAME, None)\n-        data.pop(\"index\", None)\n-\n-        # Use the action whose button was pushed\n-        try:\n-            data.update({'action': data.getlist('action')[action_index]})\n-        except IndexError:\n-            # If we didn't get an action from the chosen form that's invalid\n-            # POST data, so by deleting action it'll fail the validation check\n-            # below. So no need to do anything here\n-            pass\n-\n-        action_form = self.action_form(data, auto_id=None)\n-        action_form.fields['action'].choices = self.get_action_choices(request)\n-\n-        # If the form's valid we can handle the action.\n-        if action_form.is_valid():\n-            action = action_form.cleaned_data['action']\n-            select_across = action_form.cleaned_data['select_across']\n-            func = self.get_actions(request)[action][0]\n-\n-            # Get the list of selected PKs. If nothing's selected, we can't\n-            # perform an action on it, so bail. Except we want to perform\n-            # the action explicitly on all objects.\n-            selected = request.POST.getlist(helpers.ACTION_CHECKBOX_NAME)\n-            if not selected and not select_across:\n-                # Reminder that something needs to be selected or nothing will happen\n-                msg = _(\"Items must be selected in order to perform \"\n-                        \"actions on them. No items have been changed.\")\n-                self.message_user(request, msg, messages.WARNING)\n-                return None\n-\n-            if not select_across:\n-                # Perform the action only on the selected objects\n-                queryset = queryset.filter(pk__in=selected)\n-\n-            response = func(self, request, queryset)\n-\n-            # Actions may return an HttpResponse-like object, which will be\n-            # used as the response from the POST. If not, we'll be a good\n-            # little HTTP citizen and redirect back to the changelist page.\n-            if isinstance(response, HttpResponseBase):\n-                return response\n-            else:\n-                return HttpResponseRedirect(request.get_full_path())\n-        else:\n-            msg = _(\"No action selected.\")\n-            self.message_user(request, msg, messages.WARNING)\n-            return None\n-\n-    def response_delete(self, request, obj_display, obj_id):\n-        \"\"\"\n-        Determine the HttpResponse for the delete_view stage.\n-        \"\"\"\n-        opts = self.model._meta\n-\n-        if IS_POPUP_VAR in request.POST:\n-            popup_response_data = json.dumps({\n-                'action': 'delete',\n-                'value': str(obj_id),\n-            })\n-            return TemplateResponse(request, self.popup_response_template or [\n-                'admin/%s/%s/popup_response.html' % (opts.app_label, opts.model_name),\n-                'admin/%s/popup_response.html' % opts.app_label,\n-                'admin/popup_response.html',\n-            ], {\n-                'popup_response_data': popup_response_data,\n-            })\n-\n-        self.message_user(\n-            request,\n-            _('The %(name)s “%(obj)s” was deleted successfully.') % {\n-                'name': opts.verbose_name,\n-                'obj': obj_display,\n-            },\n-            messages.SUCCESS,\n-        )\n-\n-        if self.has_change_permission(request, None):\n-            post_url = reverse(\n-                'admin:%s_%s_changelist' % (opts.app_label, opts.model_name),\n-                current_app=self.admin_site.name,\n-            )\n-            preserved_filters = self.get_preserved_filters(request)\n-            post_url = add_preserved_filters(\n-                {'preserved_filters': preserved_filters, 'opts': opts}, post_url\n-            )\n-        else:\n-            post_url = reverse('admin:index', current_app=self.admin_site.name)\n-        return HttpResponseRedirect(post_url)\n-\n-    def render_delete_form(self, request, context):\n-        opts = self.model._meta\n-        app_label = opts.app_label\n-\n-        request.current_app = self.admin_site.name\n-        context.update(\n-            to_field_var=TO_FIELD_VAR,\n-            is_popup_var=IS_POPUP_VAR,\n-            media=self.media,\n-        )\n-\n-        return TemplateResponse(\n-            request,\n-            self.delete_confirmation_template or [\n-                \"admin/{}/{}/delete_confirmation.html\".format(app_label, opts.model_name),\n-                \"admin/{}/delete_confirmation.html\".format(app_label),\n-                \"admin/delete_confirmation.html\",\n-            ],\n-            context,\n-        )\n-\n-    def get_inline_formsets(self, request, formsets, inline_instances, obj=None):\n-        # Edit permissions on parent model are required for editable inlines.\n-        can_edit_parent = self.has_change_permission(request, obj) if obj else self.has_add_permission(request)\n-        inline_admin_formsets = []\n-        for inline, formset in zip(inline_instances, formsets):\n-            fieldsets = list(inline.get_fieldsets(request, obj))\n-            readonly = list(inline.get_readonly_fields(request, obj))\n-            if can_edit_parent:\n-                has_add_permission = inline.has_add_permission(request, obj)\n-                has_change_permission = inline.has_change_permission(request, obj)\n-                has_delete_permission = inline.has_delete_permission(request, obj)\n-            else:\n-                # Disable all edit-permissions, and overide formset settings.\n-                has_add_permission = has_change_permission = has_delete_permission = False\n-                formset.extra = formset.max_num = 0\n-            has_view_permission = inline.has_view_permission(request, obj)\n-            prepopulated = dict(inline.get_prepopulated_fields(request, obj))\n-            inline_admin_formset = helpers.InlineAdminFormSet(\n-                inline, formset, fieldsets, prepopulated, readonly, model_admin=self,\n-                has_add_permission=has_add_permission, has_change_permission=has_change_permission,\n-                has_delete_permission=has_delete_permission, has_view_permission=has_view_permission,\n-            )\n-            inline_admin_formsets.append(inline_admin_formset)\n-        return inline_admin_formsets\n-\n-    def get_changeform_initial_data(self, request):\n-        \"\"\"\n-        Get the initial form data from the request's GET params.\n-        \"\"\"\n-        initial = dict(request.GET.items())\n-        for k in initial:\n-            try:\n-                f = self.model._meta.get_field(k)\n-            except FieldDoesNotExist:\n-                continue\n-            # We have to special-case M2Ms as a list of comma-separated PKs.\n-            if isinstance(f, models.ManyToManyField):\n-                initial[k] = initial[k].split(\",\")\n-        return initial\n-\n-    def _get_obj_does_not_exist_redirect(self, request, opts, object_id):\n-        \"\"\"\n-        Create a message informing the user that the object doesn't exist\n-        and return a redirect to the admin index page.\n-        \"\"\"\n-        msg = _('%(name)s with ID “%(key)s” doesn’t exist. Perhaps it was deleted?') % {\n-            'name': opts.verbose_name,\n-            'key': unquote(object_id),\n-        }\n-        self.message_user(request, msg, messages.WARNING)\n-        url = reverse('admin:index', current_app=self.admin_site.name)\n-        return HttpResponseRedirect(url)\n-\n-    @csrf_protect_m\n-    def changeform_view(self, request, object_id=None, form_url='', extra_context=None):\n-        with transaction.atomic(using=router.db_for_write(self.model)):\n-            return self._changeform_view(request, object_id, form_url, extra_context)\n-\n-    def _changeform_view(self, request, object_id, form_url, extra_context):\n-        to_field = request.POST.get(TO_FIELD_VAR, request.GET.get(TO_FIELD_VAR))\n-        if to_field and not self.to_field_allowed(request, to_field):\n-            raise DisallowedModelAdminToField(\"The field %s cannot be referenced.\" % to_field)\n-\n-        model = self.model\n-        opts = model._meta\n-\n-        if request.method == 'POST' and '_saveasnew' in request.POST:\n-            object_id = None\n-\n-        add = object_id is None\n-\n-        if add:\n-            if not self.has_add_permission(request):\n-                raise PermissionDenied\n-            obj = None\n-\n-        else:\n-            obj = self.get_object(request, unquote(object_id), to_field)\n-\n-            if request.method == 'POST':\n-                if not self.has_change_permission(request, obj):\n-                    raise PermissionDenied\n-            else:\n-                if not self.has_view_or_change_permission(request, obj):\n-                    raise PermissionDenied\n-\n-            if obj is None:\n-                return self._get_obj_does_not_exist_redirect(request, opts, object_id)\n-\n-        fieldsets = self.get_fieldsets(request, obj)\n-        ModelForm = self.get_form(\n-            request, obj, change=not add, fields=flatten_fieldsets(fieldsets)\n-        )\n-        if request.method == 'POST':\n-            form = ModelForm(request.POST, request.FILES, instance=obj)\n-            form_validated = form.is_valid()\n-            if form_validated:\n-                new_object = self.save_form(request, form, change=not add)\n-            else:\n-                new_object = form.instance\n-            formsets, inline_instances = self._create_formsets(request, new_object, change=not add)\n-            if all_valid(formsets) and form_validated:\n-                self.save_model(request, new_object, form, not add)\n-                self.save_related(request, form, formsets, not add)\n-                change_message = self.construct_change_message(request, form, formsets, add)\n-                if add:\n-                    self.log_addition(request, new_object, change_message)\n-                    return self.response_add(request, new_object)\n-                else:\n-                    self.log_change(request, new_object, change_message)\n-                    return self.response_change(request, new_object)\n-            else:\n-                form_validated = False\n-        else:\n-            if add:\n-                initial = self.get_changeform_initial_data(request)\n-                form = ModelForm(initial=initial)\n-                formsets, inline_instances = self._create_formsets(request, form.instance, change=False)\n-            else:\n-                form = ModelForm(instance=obj)\n-                formsets, inline_instances = self._create_formsets(request, obj, change=True)\n-\n-        if not add and not self.has_change_permission(request, obj):\n-            readonly_fields = flatten_fieldsets(fieldsets)\n-        else:\n-            readonly_fields = self.get_readonly_fields(request, obj)\n-        adminForm = helpers.AdminForm(\n-            form,\n-            list(fieldsets),\n-            # Clear prepopulated fields on a view-only form to avoid a crash.\n-            self.get_prepopulated_fields(request, obj) if add or self.has_change_permission(request, obj) else {},\n-            readonly_fields,\n-            model_admin=self)\n-        media = self.media + adminForm.media\n-\n-        inline_formsets = self.get_inline_formsets(request, formsets, inline_instances, obj)\n-        for inline_formset in inline_formsets:\n-            media = media + inline_formset.media\n-\n-        if add:\n-            title = _('Add %s')\n-        elif self.has_change_permission(request, obj):\n-            title = _('Change %s')\n-        else:\n-            title = _('View %s')\n-        context = {\n-            **self.admin_site.each_context(request),\n-            'title': title % opts.verbose_name,\n-            'adminform': adminForm,\n-            'object_id': object_id,\n-            'original': obj,\n-            'is_popup': IS_POPUP_VAR in request.POST or IS_POPUP_VAR in request.GET,\n-            'to_field': to_field,\n-            'media': media,\n-            'inline_admin_formsets': inline_formsets,\n-            'errors': helpers.AdminErrorList(form, formsets),\n-            'preserved_filters': self.get_preserved_filters(request),\n-        }\n-\n-        # Hide the \"Save\" and \"Save and continue\" buttons if \"Save as New\" was\n-        # previously chosen to prevent the interface from getting confusing.\n-        if request.method == 'POST' and not form_validated and \"_saveasnew\" in request.POST:\n-            context['show_save'] = False\n-            context['show_save_and_continue'] = False\n-            # Use the change template instead of the add template.\n-            add = False\n-\n-        context.update(extra_context or {})\n-\n-        return self.render_change_form(request, context, add=add, change=not add, obj=obj, form_url=form_url)\n-\n-    def autocomplete_view(self, request):\n-        return AutocompleteJsonView.as_view(model_admin=self)(request)\n-\n-    def add_view(self, request, form_url='', extra_context=None):\n-        return self.changeform_view(request, None, form_url, extra_context)\n-\n-    def change_view(self, request, object_id, form_url='', extra_context=None):\n-        return self.changeform_view(request, object_id, form_url, extra_context)\n-\n-    def _get_edited_object_pks(self, request, prefix):\n-        \"\"\"Return POST data values of list_editable primary keys.\"\"\"\n-        pk_pattern = re.compile(\n-            r'{}-\\d+-{}$'.format(re.escape(prefix), self.model._meta.pk.name)\n-        )\n-        return [value for key, value in request.POST.items() if pk_pattern.match(key)]\n-\n-    def _get_list_editable_queryset(self, request, prefix):\n-        \"\"\"\n-        Based on POST data, return a queryset of the objects that were edited\n-        via list_editable.\n-        \"\"\"\n-        object_pks = self._get_edited_object_pks(request, prefix)\n-        queryset = self.get_queryset(request)\n-        validate = queryset.model._meta.pk.to_python\n-        try:\n-            for pk in object_pks:\n-                validate(pk)\n-        except ValidationError:\n-            # Disable the optimization if the POST data was tampered with.\n-            return queryset\n-        return queryset.filter(pk__in=object_pks)\n-\n-    @csrf_protect_m\n-    def changelist_view(self, request, extra_context=None):\n-        \"\"\"\n-        The 'change list' admin view for this model.\n-        \"\"\"\n-        from django.contrib.admin.views.main import ERROR_FLAG\n-        opts = self.model._meta\n-        app_label = opts.app_label\n-        if not self.has_view_or_change_permission(request):\n-            raise PermissionDenied\n-\n-        try:\n-            cl = self.get_changelist_instance(request)\n-        except IncorrectLookupParameters:\n-            # Wacky lookup parameters were given, so redirect to the main\n-            # changelist page, without parameters, and pass an 'invalid=1'\n-            # parameter via the query string. If wacky parameters were given\n-            # and the 'invalid=1' parameter was already in the query string,\n-            # something is screwed up with the database, so display an error\n-            # page.\n-            if ERROR_FLAG in request.GET:\n-                return SimpleTemplateResponse('admin/invalid_setup.html', {\n-                    'title': _('Database error'),\n-                })\n-            return HttpResponseRedirect(request.path + '?' + ERROR_FLAG + '=1')\n-\n-        # If the request was POSTed, this might be a bulk action or a bulk\n-        # edit. Try to look up an action or confirmation first, but if this\n-        # isn't an action the POST will fall through to the bulk edit check,\n-        # below.\n-        action_failed = False\n-        selected = request.POST.getlist(helpers.ACTION_CHECKBOX_NAME)\n-\n-        actions = self.get_actions(request)\n-        # Actions with no confirmation\n-        if (actions and request.method == 'POST' and\n-                'index' in request.POST and '_save' not in request.POST):\n-            if selected:\n-                response = self.response_action(request, queryset=cl.get_queryset(request))\n-                if response:\n-                    return response\n-                else:\n-                    action_failed = True\n-            else:\n-                msg = _(\"Items must be selected in order to perform \"\n-                        \"actions on them. No items have been changed.\")\n-                self.message_user(request, msg, messages.WARNING)\n-                action_failed = True\n-\n-        # Actions with confirmation\n-        if (actions and request.method == 'POST' and\n-                helpers.ACTION_CHECKBOX_NAME in request.POST and\n-                'index' not in request.POST and '_save' not in request.POST):\n-            if selected:\n-                response = self.response_action(request, queryset=cl.get_queryset(request))\n-                if response:\n-                    return response\n-                else:\n-                    action_failed = True\n-\n-        if action_failed:\n-            # Redirect back to the changelist page to avoid resubmitting the\n-            # form if the user refreshes the browser or uses the \"No, take\n-            # me back\" button on the action confirmation page.\n-            return HttpResponseRedirect(request.get_full_path())\n-\n-        # If we're allowing changelist editing, we need to construct a formset\n-        # for the changelist given all the fields to be edited. Then we'll\n-        # use the formset to validate/process POSTed data.\n-        formset = cl.formset = None\n-\n-        # Handle POSTed bulk-edit data.\n-        if request.method == 'POST' and cl.list_editable and '_save' in request.POST:\n-            if not self.has_change_permission(request):\n-                raise PermissionDenied\n-            FormSet = self.get_changelist_formset(request)\n-            modified_objects = self._get_list_editable_queryset(request, FormSet.get_default_prefix())\n-            formset = cl.formset = FormSet(request.POST, request.FILES, queryset=modified_objects)\n-            if formset.is_valid():\n-                changecount = 0\n-                for form in formset.forms:\n-                    if form.has_changed():\n-                        obj = self.save_form(request, form, change=True)\n-                        self.save_model(request, obj, form, change=True)\n-                        self.save_related(request, form, formsets=[], change=True)\n-                        change_msg = self.construct_change_message(request, form, None)\n-                        self.log_change(request, obj, change_msg)\n-                        changecount += 1\n-\n-                if changecount:\n-                    msg = ngettext(\n-                        \"%(count)s %(name)s was changed successfully.\",\n-                        \"%(count)s %(name)s were changed successfully.\",\n-                        changecount\n-                    ) % {\n-                        'count': changecount,\n-                        'name': model_ngettext(opts, changecount),\n-                    }\n-                    self.message_user(request, msg, messages.SUCCESS)\n-\n-                return HttpResponseRedirect(request.get_full_path())\n-\n-        # Handle GET -- construct a formset for display.\n-        elif cl.list_editable and self.has_change_permission(request):\n-            FormSet = self.get_changelist_formset(request)\n-            formset = cl.formset = FormSet(queryset=cl.result_list)\n-\n-        # Build the list of media to be used by the formset.\n-        if formset:\n-            media = self.media + formset.media\n-        else:\n-            media = self.media\n-\n-        # Build the action form and populate it with available actions.\n-        if actions:\n-            action_form = self.action_form(auto_id=None)\n-            action_form.fields['action'].choices = self.get_action_choices(request)\n-            media += action_form.media\n-        else:\n-            action_form = None\n-\n-        selection_note_all = ngettext(\n-            '%(total_count)s selected',\n-            'All %(total_count)s selected',\n-            cl.result_count\n-        )\n-\n-        context = {\n-            **self.admin_site.each_context(request),\n-            'module_name': str(opts.verbose_name_plural),\n-            'selection_note': _('0 of %(cnt)s selected') % {'cnt': len(cl.result_list)},\n-            'selection_note_all': selection_note_all % {'total_count': cl.result_count},\n-            'title': cl.title,\n-            'is_popup': cl.is_popup,\n-            'to_field': cl.to_field,\n-            'cl': cl,\n-            'media': media,\n-            'has_add_permission': self.has_add_permission(request),\n-            'opts': cl.opts,\n-            'action_form': action_form,\n-            'actions_on_top': self.actions_on_top,\n-            'actions_on_bottom': self.actions_on_bottom,\n-            'actions_selection_counter': self.actions_selection_counter,\n-            'preserved_filters': self.get_preserved_filters(request),\n-            **(extra_context or {}),\n-        }\n-\n-        request.current_app = self.admin_site.name\n-\n-        return TemplateResponse(request, self.change_list_template or [\n-            'admin/%s/%s/change_list.html' % (app_label, opts.model_name),\n-            'admin/%s/change_list.html' % app_label,\n-            'admin/change_list.html'\n-        ], context)\n-\n-    def get_deleted_objects(self, objs, request):\n-        \"\"\"\n-        Hook for customizing the delete process for the delete view and the\n-        \"delete selected\" action.\n-        \"\"\"\n-        return get_deleted_objects(objs, request, self.admin_site)\n-\n-    @csrf_protect_m\n-    def delete_view(self, request, object_id, extra_context=None):\n-        with transaction.atomic(using=router.db_for_write(self.model)):\n-            return self._delete_view(request, object_id, extra_context)\n-\n-    def _delete_view(self, request, object_id, extra_context):\n-        \"The 'delete' admin view for this model.\"\n-        opts = self.model._meta\n-        app_label = opts.app_label\n-\n-        to_field = request.POST.get(TO_FIELD_VAR, request.GET.get(TO_FIELD_VAR))\n-        if to_field and not self.to_field_allowed(request, to_field):\n-            raise DisallowedModelAdminToField(\"The field %s cannot be referenced.\" % to_field)\n-\n-        obj = self.get_object(request, unquote(object_id), to_field)\n-\n-        if not self.has_delete_permission(request, obj):\n-            raise PermissionDenied\n-\n-        if obj is None:\n-            return self._get_obj_does_not_exist_redirect(request, opts, object_id)\n-\n-        # Populate deleted_objects, a data structure of all related objects that\n-        # will also be deleted.\n-        deleted_objects, model_count, perms_needed, protected = self.get_deleted_objects([obj], request)\n-\n-        if request.POST and not protected:  # The user has confirmed the deletion.\n-            if perms_needed:\n-                raise PermissionDenied\n-            obj_display = str(obj)\n-            attr = str(to_field) if to_field else opts.pk.attname\n-            obj_id = obj.serializable_value(attr)\n-            self.log_deletion(request, obj, obj_display)\n-            self.delete_model(request, obj)\n-\n-            return self.response_delete(request, obj_display, obj_id)\n-\n-        object_name = str(opts.verbose_name)\n-\n-        if perms_needed or protected:\n-            title = _(\"Cannot delete %(name)s\") % {\"name\": object_name}\n-        else:\n-            title = _(\"Are you sure?\")\n-\n-        context = {\n-            **self.admin_site.each_context(request),\n-            'title': title,\n-            'object_name': object_name,\n-            'object': obj,\n-            'deleted_objects': deleted_objects,\n-            'model_count': dict(model_count).items(),\n-            'perms_lacking': perms_needed,\n-            'protected': protected,\n-            'opts': opts,\n-            'app_label': app_label,\n-            'preserved_filters': self.get_preserved_filters(request),\n-            'is_popup': IS_POPUP_VAR in request.POST or IS_POPUP_VAR in request.GET,\n-            'to_field': to_field,\n-            **(extra_context or {}),\n-        }\n-\n-        return self.render_delete_form(request, context)\n-\n-    def history_view(self, request, object_id, extra_context=None):\n-        \"The 'history' admin view for this model.\"\n-        from django.contrib.admin.models import LogEntry\n-        # First check if the user can see this history.\n-        model = self.model\n-        obj = self.get_object(request, unquote(object_id))\n-        if obj is None:\n-            return self._get_obj_does_not_exist_redirect(request, model._meta, object_id)\n-\n-        if not self.has_view_or_change_permission(request, obj):\n-            raise PermissionDenied\n-\n-        # Then get the history for this object.\n-        opts = model._meta\n-        app_label = opts.app_label\n-        action_list = LogEntry.objects.filter(\n-            object_id=unquote(object_id),\n-            content_type=get_content_type_for_model(model)\n-        ).select_related().order_by('action_time')\n-\n-        context = {\n-            **self.admin_site.each_context(request),\n-            'title': _('Change history: %s') % obj,\n-            'action_list': action_list,\n-            'module_name': str(capfirst(opts.verbose_name_plural)),\n-            'object': obj,\n-            'opts': opts,\n-            'preserved_filters': self.get_preserved_filters(request),\n-            **(extra_context or {}),\n-        }\n-\n-        request.current_app = self.admin_site.name\n-\n-        return TemplateResponse(request, self.object_history_template or [\n-            \"admin/%s/%s/object_history.html\" % (app_label, opts.model_name),\n-            \"admin/%s/object_history.html\" % app_label,\n-            \"admin/object_history.html\"\n-        ], context)\n-\n-    def _create_formsets(self, request, obj, change):\n-        \"Helper function to generate formsets for add/change_view.\"\n-        formsets = []\n-        inline_instances = []\n-        prefixes = {}\n-        get_formsets_args = [request]\n-        if change:\n-            get_formsets_args.append(obj)\n-        for FormSet, inline in self.get_formsets_with_inlines(*get_formsets_args):\n-            prefix = FormSet.get_default_prefix()\n-            prefixes[prefix] = prefixes.get(prefix, 0) + 1\n-            if prefixes[prefix] != 1 or not prefix:\n-                prefix = \"%s-%s\" % (prefix, prefixes[prefix])\n-            formset_params = {\n-                'instance': obj,\n-                'prefix': prefix,\n-                'queryset': inline.get_queryset(request),\n-            }\n-            if request.method == 'POST':\n-                formset_params.update({\n-                    'data': request.POST.copy(),\n-                    'files': request.FILES,\n-                    'save_as_new': '_saveasnew' in request.POST\n-                })\n-            formset = FormSet(**formset_params)\n-\n-            def user_deleted_form(request, obj, formset, index):\n-                \"\"\"Return whether or not the user deleted the form.\"\"\"\n-                return (\n-                    inline.has_delete_permission(request, obj) and\n-                    '{}-{}-DELETE'.format(formset.prefix, index) in request.POST\n-                )\n-\n-            # Bypass validation of each view-only inline form (since the form's\n-            # data won't be in request.POST), unless the form was deleted.\n-            if not inline.has_change_permission(request, obj if change else None):\n-                for index, form in enumerate(formset.initial_forms):\n-                    if user_deleted_form(request, obj, formset, index):\n-                        continue\n-                    form._errors = {}\n-                    form.cleaned_data = form.initial\n-            formsets.append(formset)\n-            inline_instances.append(inline)\n-        return formsets, inline_instances\n-\n-\n-class InlineModelAdmin(BaseModelAdmin):\n-    \"\"\"\n-    Options for inline editing of ``model`` instances.\n-\n-    Provide ``fk_name`` to specify the attribute name of the ``ForeignKey``\n-    from ``model`` to its parent. This is required if ``model`` has more than\n-    one ``ForeignKey`` to its parent.\n-    \"\"\"\n-    model = None\n-    fk_name = None\n-    formset = BaseInlineFormSet\n-    extra = 3\n-    min_num = None\n-    max_num = None\n-    template = None\n-    verbose_name = None\n-    verbose_name_plural = None\n-    can_delete = True\n-    show_change_link = False\n-    checks_class = InlineModelAdminChecks\n-    classes = None\n-\n-    def __init__(self, parent_model, admin_site):\n-        self.admin_site = admin_site\n-        self.parent_model = parent_model\n-        self.opts = self.model._meta\n-        self.has_registered_model = admin_site.is_registered(self.model)\n-        super().__init__()\n-        if self.verbose_name is None:\n-            self.verbose_name = self.model._meta.verbose_name\n-        if self.verbose_name_plural is None:\n-            self.verbose_name_plural = self.model._meta.verbose_name_plural\n-\n-    @property\n-    def media(self):\n-        extra = '' if settings.DEBUG else '.min'\n-        js = ['vendor/jquery/jquery%s.js' % extra, 'jquery.init.js',\n-              'inlines%s.js' % extra]\n-        if self.filter_vertical or self.filter_horizontal:\n-            js.extend(['SelectBox.js', 'SelectFilter2.js'])\n-        if self.classes and 'collapse' in self.classes:\n-            js.append('collapse%s.js' % extra)\n-        return forms.Media(js=['admin/js/%s' % url for url in js])\n-\n-    def get_extra(self, request, obj=None, **kwargs):\n-        \"\"\"Hook for customizing the number of extra inline forms.\"\"\"\n-        return self.extra\n-\n-    def get_min_num(self, request, obj=None, **kwargs):\n-        \"\"\"Hook for customizing the min number of inline forms.\"\"\"\n-        return self.min_num\n-\n-    def get_max_num(self, request, obj=None, **kwargs):\n-        \"\"\"Hook for customizing the max number of extra inline forms.\"\"\"\n-        return self.max_num\n-\n-    def get_formset(self, request, obj=None, **kwargs):\n-        \"\"\"Return a BaseInlineFormSet class for use in admin add/change views.\"\"\"\n-        if 'fields' in kwargs:\n-            fields = kwargs.pop('fields')\n-        else:\n-            fields = flatten_fieldsets(self.get_fieldsets(request, obj))\n-        excluded = self.get_exclude(request, obj)\n-        exclude = [] if excluded is None else list(excluded)\n-        exclude.extend(self.get_readonly_fields(request, obj))\n-        if excluded is None and hasattr(self.form, '_meta') and self.form._meta.exclude:\n-            # Take the custom ModelForm's Meta.exclude into account only if the\n-            # InlineModelAdmin doesn't define its own.\n-            exclude.extend(self.form._meta.exclude)\n-        # If exclude is an empty list we use None, since that's the actual\n-        # default.\n-        exclude = exclude or None\n-        can_delete = self.can_delete and self.has_delete_permission(request, obj)\n-        defaults = {\n-            'form': self.form,\n-            'formset': self.formset,\n-            'fk_name': self.fk_name,\n-            'fields': fields,\n-            'exclude': exclude,\n-            'formfield_callback': partial(self.formfield_for_dbfield, request=request),\n-            'extra': self.get_extra(request, obj, **kwargs),\n-            'min_num': self.get_min_num(request, obj, **kwargs),\n-            'max_num': self.get_max_num(request, obj, **kwargs),\n-            'can_delete': can_delete,\n-            **kwargs,\n-        }\n-\n-        base_model_form = defaults['form']\n-        can_change = self.has_change_permission(request, obj) if request else True\n-        can_add = self.has_add_permission(request, obj) if request else True\n-\n-        class DeleteProtectedModelForm(base_model_form):\n-\n-            def hand_clean_DELETE(self):\n-                \"\"\"\n-                We don't validate the 'DELETE' field itself because on\n-                templates it's not rendered using the field information, but\n-                just using a generic \"deletion_field\" of the InlineModelAdmin.\n-                \"\"\"\n-                if self.cleaned_data.get(DELETION_FIELD_NAME, False):\n-                    using = router.db_for_write(self._meta.model)\n-                    collector = NestedObjects(using=using)\n-                    if self.instance._state.adding:\n-                        return\n-                    collector.collect([self.instance])\n-                    if collector.protected:\n-                        objs = []\n-                        for p in collector.protected:\n-                            objs.append(\n-                                # Translators: Model verbose name and instance representation,\n-                                # suitable to be an item in a list.\n-                                _('%(class_name)s %(instance)s') % {\n-                                    'class_name': p._meta.verbose_name,\n-                                    'instance': p}\n-                            )\n-                        params = {\n-                            'class_name': self._meta.model._meta.verbose_name,\n-                            'instance': self.instance,\n-                            'related_objects': get_text_list(objs, _('and')),\n-                        }\n-                        msg = _(\"Deleting %(class_name)s %(instance)s would require \"\n-                                \"deleting the following protected related objects: \"\n-                                \"%(related_objects)s\")\n-                        raise ValidationError(msg, code='deleting_protected', params=params)\n-\n-            def is_valid(self):\n-                result = super().is_valid()\n-                self.hand_clean_DELETE()\n-                return result\n-\n-            def has_changed(self):\n-                # Protect against unauthorized edits.\n-                if not can_change and not self.instance._state.adding:\n-                    return False\n-                if not can_add and self.instance._state.adding:\n-                    return False\n-                return super().has_changed()\n-\n-        defaults['form'] = DeleteProtectedModelForm\n-\n-        if defaults['fields'] is None and not modelform_defines_fields(defaults['form']):\n-            defaults['fields'] = forms.ALL_FIELDS\n-\n-        return inlineformset_factory(self.parent_model, self.model, **defaults)\n-\n-    def _get_form_for_get_fields(self, request, obj=None):\n-        return self.get_formset(request, obj, fields=None).form\n-\n-    def get_queryset(self, request):\n-        queryset = super().get_queryset(request)\n-        if not self.has_view_or_change_permission(request):\n-            queryset = queryset.none()\n-        return queryset\n-\n-    def _has_any_perms_for_target_model(self, request, perms):\n-        \"\"\"\n-        This method is called only when the ModelAdmin's model is for an\n-        ManyToManyField's implicit through model (if self.opts.auto_created).\n-        Return True if the user has any of the given permissions ('add',\n-        'change', etc.) for the model that points to the through model.\n-        \"\"\"\n-        opts = self.opts\n-        # Find the target model of an auto-created many-to-many relationship.\n-        for field in opts.fields:\n-            if field.remote_field and field.remote_field.model != self.parent_model:\n-                opts = field.remote_field.model._meta\n-                break\n-        return any(\n-            request.user.has_perm('%s.%s' % (opts.app_label, get_permission_codename(perm, opts)))\n-            for perm in perms\n-        )\n-\n-    def has_add_permission(self, request, obj):\n-        if self.opts.auto_created:\n-            # Auto-created intermediate models don't have their own\n-            # permissions. The user needs to have the change permission for the\n-            # related model in order to be able to do anything with the\n-            # intermediate model.\n-            return self._has_any_perms_for_target_model(request, ['change'])\n-        return super().has_add_permission(request)\n-\n-    def has_change_permission(self, request, obj=None):\n-        if self.opts.auto_created:\n-            # Same comment as has_add_permission().\n-            return self._has_any_perms_for_target_model(request, ['change'])\n-        return super().has_change_permission(request)\n-\n-    def has_delete_permission(self, request, obj=None):\n-        if self.opts.auto_created:\n-            # Same comment as has_add_permission().\n-            return self._has_any_perms_for_target_model(request, ['change'])\n-        return super().has_delete_permission(request, obj)\n-\n-    def has_view_permission(self, request, obj=None):\n-        if self.opts.auto_created:\n-            # Same comment as has_add_permission(). The 'change' permission\n-            # also implies the 'view' permission.\n-            return self._has_any_perms_for_target_model(request, ['view', 'change'])\n-        return super().has_view_permission(request)\n-\n-\n-class StackedInline(InlineModelAdmin):\n-    template = 'admin/edit_inline/stacked.html'\n-\n-\n-class TabularInline(InlineModelAdmin):\n-    template = 'admin/edit_inline/tabular.html'\n+        return self.autocomplete_fields\n\\ No newline at end of file\ndiff --git a/django/contrib/admin/options.py.bak2 b/django/contrib/admin/options.py.bak2\nnew file mode 100644\nindex 0000000000..99c49d5e01\n--- /dev/null\n+++ b/django/contrib/admin/options.py.bak2\n@@ -0,0 +1,281 @@\n+import copy\n+import json\n+import operator\n+import re\n+from functools import partial, reduce, update_wrapper\n+from urllib.parse import quote as urlquote\n+\n+from django import forms\n+from django.conf import settings\n+from django.contrib import messages\n+from django.contrib.admin import helpers, widgets\n+from django.contrib.admin.checks import (\n+    BaseModelAdminChecks, InlineModelAdminChecks, ModelAdminChecks,\n+)\n+from django.contrib.admin.exceptions import DisallowedModelAdminToField\n+from django.contrib.admin.templatetags.admin_urls import add_preserved_filters\n+from django.contrib.admin.utils import (\n+    NestedObjects, construct_change_message, flatten_fieldsets,\n+    get_deleted_objects, lookup_needs_distinct, model_format_dict,\n+    model_ngettext, quote, unquote,\n+)\n+from django.contrib.admin.views.autocomplete import AutocompleteJsonView\n+from django.contrib.admin.widgets import (\n+    AutocompleteSelect, AutocompleteSelectMultiple,\n+)\n+from django.contrib.auth import get_permission_codename\n+from django.core.exceptions import (\n+    FieldDoesNotExist, FieldError, PermissionDenied, ValidationError,\n+)\n+from django.core.paginator import Paginator\n+from django.db import models, router, transaction\n+from django.db.models.constants import LOOKUP_SEP\n+from django.forms.formsets import DELETION_FIELD_NAME, all_valid\n+from django.forms.models import (\n+    BaseInlineFormSet, inlineformset_factory, modelform_defines_fields,\n+    modelform_factory, modelformset_factory,\n+)\n+from django.forms.widgets import CheckboxSelectMultiple, SelectMultiple\n+from django.http import HttpResponseRedirect\n+from django.http.response import HttpResponseBase\n+from django.template.response import SimpleTemplateResponse, TemplateResponse\n+from django.urls import reverse\n+from django.utils.decorators import method_decorator\n+from django.utils.html import format_html\n+from django.utils.http import urlencode\n+from django.utils.safestring import mark_safe\n+from django.utils.text import capfirst, format_lazy, get_text_list\n+from django.utils.translation import gettext as _, ngettext\n+from django.views.decorators.csrf import csrf_protect\n+from django.views.generic import RedirectView\n+\n+IS_POPUP_VAR = '_popup'\n+TO_FIELD_VAR = '_to_field'\n+\n+\n+HORIZONTAL, VERTICAL = 1, 2\n+\n+\n+def get_content_type_for_model(obj):\n+    # Since this module gets imported in the application's root package,\n+    # it cannot import models from other applications at the module level.\n+    from django.contrib.contenttypes.models import ContentType\n+    return ContentType.objects.get_for_model(obj, for_concrete_model=False)\n+\n+\n+def get_ul_class(radio_style):\n+    return 'radiolist' if radio_style == VERTICAL else 'radiolist inline'\n+\n+\n+class IncorrectLookupParameters(Exception):\n+    pass\n+\n+\n+# Defaults for formfield_overrides. ModelAdmin subclasses can change this\n+# by adding to ModelAdmin.formfield_overrides.\n+\n+FORMFIELD_FOR_DBFIELD_DEFAULTS = {\n+    models.DateTimeField: {\n+        'form_class': forms.SplitDateTimeField,\n+        'widget': widgets.AdminSplitDateTime\n+    },\n+    models.DateField: {'widget': widgets.AdminDateWidget},\n+    models.TimeField: {'widget': widgets.AdminTimeWidget},\n+    models.TextField: {'widget': widgets.AdminTextareaWidget},\n+    models.URLField: {'widget': widgets.AdminURLFieldWidget},\n+    models.IntegerField: {'widget': widgets.AdminIntegerFieldWidget},\n+    models.BigIntegerField: {'widget': widgets.AdminBigIntegerFieldWidget},\n+    models.CharField: {'widget': widgets.AdminTextInputWidget},\n+    models.ImageField: {'widget': widgets.AdminFileWidget},\n+    models.FileField: {'widget': widgets.AdminFileWidget},\n+    models.EmailField: {'widget': widgets.AdminEmailInputWidget},\n+    models.UUIDField: {'widget': widgets.AdminUUIDInputWidget},\n+}\n+\n+csrf_protect_m = method_decorator(csrf_protect)\n+\n+\n+class BaseModelAdmin(metaclass=forms.MediaDefiningClass):\n+    \"\"\"Functionality common to both ModelAdmin and InlineAdmin.\"\"\"\n+\n+    autocomplete_fields = ()\n+    raw_id_fields = ()\n+    fields = None\n+    exclude = None\n+    fieldsets = None\n+    form = forms.ModelForm\n+    filter_vertical = ()\n+    filter_horizontal = ()\n+    radio_fields = {}\n+    prepopulated_fields = {}\n+    formfield_overrides = {}\n+    readonly_fields = ()\n+    ordering = None\n+    sortable_by = None\n+    view_on_site = True\n+    show_full_result_count = True\n+    checks_class = BaseModelAdminChecks\n+\n+    def check(self, **kwargs):\n+        return self.checks_class().check(self, **kwargs)\n+\n+    def __init__(self):\n+        # Merge FORMFIELD_FOR_DBFIELD_DEFAULTS with the formfield_overrides\n+        # rather than simply overwriting.\n+        overrides = copy.deepcopy(FORMFIELD_FOR_DBFIELD_DEFAULTS)\n+        for k, v in self.formfield_overrides.items():\n+            overrides.setdefault(k, {}).update(v)\n+        self.formfield_overrides = overrides\n+\n+    def formfield_for_dbfield(self, db_field, request, **kwargs):\n+        \"\"\"\n+        Hook for specifying the form Field instance for a given database Field\n+        instance.\n+\n+        If kwargs are given, they're passed to the form Field's constructor.\n+        \"\"\"\n+        # If the field specifies choices, we don't need to look for special\n+        # admin widgets - we just need to use a select widget of some kind.\n+        if db_field.choices:\n+            return self.formfield_for_choice_field(db_field, request, **kwargs)\n+\n+        # ForeignKey or ManyToManyFields\n+        if isinstance(db_field, (models.ForeignKey, models.ManyToManyField)):\n+            # Combine the field kwargs with any options for formfield_overrides.\n+            # Make sure the passed in **kwargs override anything in\n+            # formfield_overrides because **kwargs is more specific, and should\n+            # always win.\n+            if db_field.__class__ in self.formfield_overrides:\n+                kwargs = {**self.formfield_overrides[db_field.__class__], **kwargs}\n+\n+            # Get the correct formfield.\n+            if isinstance(db_field, models.ForeignKey):\n+                formfield = self.formfield_for_foreignkey(db_field, request, **kwargs)\n+            elif isinstance(db_field, models.ManyToManyField):\n+                formfield = self.formfield_for_manytomany(db_field, request, **kwargs)\n+\n+            # For non-raw_id fields, wrap the widget with a wrapper that adds\n+            # extra HTML -- the \"add other\" interface -- to the end of the\n+            # rendered output. formfield can be None if it came from a\n+            # OneToOneField with parent_link=True or a M2M intermediary.\n+            if formfield and db_field.name not in self.raw_id_fields:\n+                related_modeladmin = self.admin_site._registry.get(db_field.remote_field.model)\n+                wrapper_kwargs = {}\n+                if related_modeladmin:\n+                    wrapper_kwargs.update(\n+                        can_add_related=related_modeladmin.has_add_permission(request),\n+                        can_change_related=related_modeladmin.has_change_permission(request),\n+                        can_delete_related=related_modeladmin.has_delete_permission(request),\n+                        can_view_related=related_modeladmin.has_view_permission(request),\n+                    )\n+                formfield.widget = widgets.RelatedFieldWidgetWrapper(\n+                    formfield.widget, db_field.remote_field, self.admin_site, **wrapper_kwargs\n+                )\n+\n+            return formfield\n+\n+        # If we've got overrides for the formfield defined, use 'em. **kwargs\n+        # passed to formfield_for_dbfield override the defaults.\n+        for klass in db_field.__class__.mro():\n+            if klass in self.formfield_overrides:\n+                kwargs = {**copy.deepcopy(self.formfield_overrides[klass]), **kwargs}\n+                return db_field.formfield(**kwargs)\n+\n+        # For any other type of field, just call its formfield() method.\n+        return db_field.formfield(**kwargs)\n+\n+    def formfield_for_choice_field(self, db_field, request, **kwargs):\n+        \"\"\"\n+        Get a form Field for a database Field that has declared choices.\n+        \"\"\"\n+        # If the field is named as a radio_field, use a RadioSelect\n+        if db_field.name in self.radio_fields:\n+            # Avoid stomping on custom widget/choices arguments.\n+            if 'widget' not in kwargs:\n+                kwargs['widget'] = widgets.AdminRadioSelect(attrs={\n+                    'class': get_ul_class(self.radio_fields[db_field.name]),\n+                })\n+            if 'choices' not in kwargs:\n+                kwargs['choices'] = db_field.get_choices(\n+                    include_blank=db_field.blank,\n+                    blank_choice=[('', _('None'))]\n+                )\n+        return db_field.formfield(**kwargs)\n+\n+    def get_field_queryset(self, db, db_field, request):\n+        \"\"\"\n+        If the ModelAdmin specifies ordering, the queryset should respect that\n+        ordering.  Otherwise don't specify the queryset, let the field decide\n+        (return None in that case).\n+        \"\"\"\n+        related_admin = self.admin_site._registry.get(db_field.remote_field.model)\n+        if related_admin is not None:\n+            ordering = related_admin.get_ordering(request)\n+            if ordering is not None and ordering != ():\n+                return db_field.remote_field.model._default_manager.using(db).order_by(*ordering)\n+        return None\n+\n+    def formfield_for_foreignkey(self, db_field, request, **kwargs):\n+        \"\"\"\n+        Get a form Field for a ForeignKey.\n+        \"\"\"\n+        db = kwargs.get('using')\n+\n+        if 'widget' not in kwargs:\n+            if db_field.name in self.get_autocomplete_fields(request):\n+                kwargs['widget'] = AutocompleteSelect(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in self.raw_id_fields:\n+                kwargs['widget'] = widgets.ForeignKeyRawIdWidget(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in self.radio_fields:\n+                kwargs['widget'] = widgets.AdminRadioSelect(attrs={\n+                    'class': get_ul_class(self.radio_fields[db_field.name]),\n+                })\n+\n+        if 'queryset' not in kwargs:\n+            queryset = self.get_field_queryset(db, db_field, request)\n+            if queryset is not None:\n+                kwargs['queryset'] = queryset\n+\n+        return db_field.formfield(**kwargs)\n+\n+    def formfield_for_manytomany(self, db_field, request, **kwargs):\n+    def formfield_for_manytomany(self, db_field, request, **kwargs):\n+        \"\"\"\n+        Get a form Field for a ManyToManyField.\n+        \"\"\"\n+        # If it uses an intermediary model that isnt auto created, dont show\n+        # a field in admin.\n+        if not db_field.remote_field.through._meta.auto_created:\n+            return None\n+        db = kwargs.get(\"using\")\n+\n+        if \"widget\" not in kwargs:\n+            autocomplete_fields = self.get_autocomplete_fields(request)\n+            if db_field.name in autocomplete_fields:\n+                kwargs[\"widget\"] = AutocompleteSelectMultiple(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in self.raw_id_fields:\n+                kwargs[\"widget\"] = widgets.ManyToManyRawIdWidget(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in [*self.filter_vertical, *self.filter_horizontal]:\n+                kwargs[\"widget\"] = widgets.FilteredSelectMultiple(\n+                    db_field.verbose_name,\n+                )\n+\n+        if 'queryset' not in kwargs:\n+            queryset = self.get_field_queryset(db, db_field, request)\n+            if queryset is not None:\n+                kwargs['queryset'] = queryset\n+\n+        form_field = db_field.formfield(**kwargs)\n+        if (isinstance(form_field.widget, SelectMultiple) and\n+                not isinstance(form_field.widget, (CheckboxSelectMultiple, AutocompleteSelectMultiple))):\n+            msg = _('Hold down \"Control\", or \"Command\" on a Mac, to select more than one.')\n+            help_text = form_field.help_text\n+            form_field.help_text = format_lazy('{} {}', help_text, msg) if help_text else msg\n+        return form_field\n+\n+    def get_autocomplete_fields(self, request):\n+        \"\"\"\n+        Return a list of ForeignKey and/or ManyToMany fields which should use\n+        an autocomplete widget.\n+        \"\"\"\n+        return self.autocomplete_fields\n\\ No newline at end of file\ndiff --git a/django/contrib/admin/options.py.bak3 b/django/contrib/admin/options.py.bak3\nnew file mode 100644\nindex 0000000000..99c49d5e01\n--- /dev/null\n+++ b/django/contrib/admin/options.py.bak3\n@@ -0,0 +1,281 @@\n+import copy\n+import json\n+import operator\n+import re\n+from functools import partial, reduce, update_wrapper\n+from urllib.parse import quote as urlquote\n+\n+from django import forms\n+from django.conf import settings\n+from django.contrib import messages\n+from django.contrib.admin import helpers, widgets\n+from django.contrib.admin.checks import (\n+    BaseModelAdminChecks, InlineModelAdminChecks, ModelAdminChecks,\n+)\n+from django.contrib.admin.exceptions import DisallowedModelAdminToField\n+from django.contrib.admin.templatetags.admin_urls import add_preserved_filters\n+from django.contrib.admin.utils import (\n+    NestedObjects, construct_change_message, flatten_fieldsets,\n+    get_deleted_objects, lookup_needs_distinct, model_format_dict,\n+    model_ngettext, quote, unquote,\n+)\n+from django.contrib.admin.views.autocomplete import AutocompleteJsonView\n+from django.contrib.admin.widgets import (\n+    AutocompleteSelect, AutocompleteSelectMultiple,\n+)\n+from django.contrib.auth import get_permission_codename\n+from django.core.exceptions import (\n+    FieldDoesNotExist, FieldError, PermissionDenied, ValidationError,\n+)\n+from django.core.paginator import Paginator\n+from django.db import models, router, transaction\n+from django.db.models.constants import LOOKUP_SEP\n+from django.forms.formsets import DELETION_FIELD_NAME, all_valid\n+from django.forms.models import (\n+    BaseInlineFormSet, inlineformset_factory, modelform_defines_fields,\n+    modelform_factory, modelformset_factory,\n+)\n+from django.forms.widgets import CheckboxSelectMultiple, SelectMultiple\n+from django.http import HttpResponseRedirect\n+from django.http.response import HttpResponseBase\n+from django.template.response import SimpleTemplateResponse, TemplateResponse\n+from django.urls import reverse\n+from django.utils.decorators import method_decorator\n+from django.utils.html import format_html\n+from django.utils.http import urlencode\n+from django.utils.safestring import mark_safe\n+from django.utils.text import capfirst, format_lazy, get_text_list\n+from django.utils.translation import gettext as _, ngettext\n+from django.views.decorators.csrf import csrf_protect\n+from django.views.generic import RedirectView\n+\n+IS_POPUP_VAR = '_popup'\n+TO_FIELD_VAR = '_to_field'\n+\n+\n+HORIZONTAL, VERTICAL = 1, 2\n+\n+\n+def get_content_type_for_model(obj):\n+    # Since this module gets imported in the application's root package,\n+    # it cannot import models from other applications at the module level.\n+    from django.contrib.contenttypes.models import ContentType\n+    return ContentType.objects.get_for_model(obj, for_concrete_model=False)\n+\n+\n+def get_ul_class(radio_style):\n+    return 'radiolist' if radio_style == VERTICAL else 'radiolist inline'\n+\n+\n+class IncorrectLookupParameters(Exception):\n+    pass\n+\n+\n+# Defaults for formfield_overrides. ModelAdmin subclasses can change this\n+# by adding to ModelAdmin.formfield_overrides.\n+\n+FORMFIELD_FOR_DBFIELD_DEFAULTS = {\n+    models.DateTimeField: {\n+        'form_class': forms.SplitDateTimeField,\n+        'widget': widgets.AdminSplitDateTime\n+    },\n+    models.DateField: {'widget': widgets.AdminDateWidget},\n+    models.TimeField: {'widget': widgets.AdminTimeWidget},\n+    models.TextField: {'widget': widgets.AdminTextareaWidget},\n+    models.URLField: {'widget': widgets.AdminURLFieldWidget},\n+    models.IntegerField: {'widget': widgets.AdminIntegerFieldWidget},\n+    models.BigIntegerField: {'widget': widgets.AdminBigIntegerFieldWidget},\n+    models.CharField: {'widget': widgets.AdminTextInputWidget},\n+    models.ImageField: {'widget': widgets.AdminFileWidget},\n+    models.FileField: {'widget': widgets.AdminFileWidget},\n+    models.EmailField: {'widget': widgets.AdminEmailInputWidget},\n+    models.UUIDField: {'widget': widgets.AdminUUIDInputWidget},\n+}\n+\n+csrf_protect_m = method_decorator(csrf_protect)\n+\n+\n+class BaseModelAdmin(metaclass=forms.MediaDefiningClass):\n+    \"\"\"Functionality common to both ModelAdmin and InlineAdmin.\"\"\"\n+\n+    autocomplete_fields = ()\n+    raw_id_fields = ()\n+    fields = None\n+    exclude = None\n+    fieldsets = None\n+    form = forms.ModelForm\n+    filter_vertical = ()\n+    filter_horizontal = ()\n+    radio_fields = {}\n+    prepopulated_fields = {}\n+    formfield_overrides = {}\n+    readonly_fields = ()\n+    ordering = None\n+    sortable_by = None\n+    view_on_site = True\n+    show_full_result_count = True\n+    checks_class = BaseModelAdminChecks\n+\n+    def check(self, **kwargs):\n+        return self.checks_class().check(self, **kwargs)\n+\n+    def __init__(self):\n+        # Merge FORMFIELD_FOR_DBFIELD_DEFAULTS with the formfield_overrides\n+        # rather than simply overwriting.\n+        overrides = copy.deepcopy(FORMFIELD_FOR_DBFIELD_DEFAULTS)\n+        for k, v in self.formfield_overrides.items():\n+            overrides.setdefault(k, {}).update(v)\n+        self.formfield_overrides = overrides\n+\n+    def formfield_for_dbfield(self, db_field, request, **kwargs):\n+        \"\"\"\n+        Hook for specifying the form Field instance for a given database Field\n+        instance.\n+\n+        If kwargs are given, they're passed to the form Field's constructor.\n+        \"\"\"\n+        # If the field specifies choices, we don't need to look for special\n+        # admin widgets - we just need to use a select widget of some kind.\n+        if db_field.choices:\n+            return self.formfield_for_choice_field(db_field, request, **kwargs)\n+\n+        # ForeignKey or ManyToManyFields\n+        if isinstance(db_field, (models.ForeignKey, models.ManyToManyField)):\n+            # Combine the field kwargs with any options for formfield_overrides.\n+            # Make sure the passed in **kwargs override anything in\n+            # formfield_overrides because **kwargs is more specific, and should\n+            # always win.\n+            if db_field.__class__ in self.formfield_overrides:\n+                kwargs = {**self.formfield_overrides[db_field.__class__], **kwargs}\n+\n+            # Get the correct formfield.\n+            if isinstance(db_field, models.ForeignKey):\n+                formfield = self.formfield_for_foreignkey(db_field, request, **kwargs)\n+            elif isinstance(db_field, models.ManyToManyField):\n+                formfield = self.formfield_for_manytomany(db_field, request, **kwargs)\n+\n+            # For non-raw_id fields, wrap the widget with a wrapper that adds\n+            # extra HTML -- the \"add other\" interface -- to the end of the\n+            # rendered output. formfield can be None if it came from a\n+            # OneToOneField with parent_link=True or a M2M intermediary.\n+            if formfield and db_field.name not in self.raw_id_fields:\n+                related_modeladmin = self.admin_site._registry.get(db_field.remote_field.model)\n+                wrapper_kwargs = {}\n+                if related_modeladmin:\n+                    wrapper_kwargs.update(\n+                        can_add_related=related_modeladmin.has_add_permission(request),\n+                        can_change_related=related_modeladmin.has_change_permission(request),\n+                        can_delete_related=related_modeladmin.has_delete_permission(request),\n+                        can_view_related=related_modeladmin.has_view_permission(request),\n+                    )\n+                formfield.widget = widgets.RelatedFieldWidgetWrapper(\n+                    formfield.widget, db_field.remote_field, self.admin_site, **wrapper_kwargs\n+                )\n+\n+            return formfield\n+\n+        # If we've got overrides for the formfield defined, use 'em. **kwargs\n+        # passed to formfield_for_dbfield override the defaults.\n+        for klass in db_field.__class__.mro():\n+            if klass in self.formfield_overrides:\n+                kwargs = {**copy.deepcopy(self.formfield_overrides[klass]), **kwargs}\n+                return db_field.formfield(**kwargs)\n+\n+        # For any other type of field, just call its formfield() method.\n+        return db_field.formfield(**kwargs)\n+\n+    def formfield_for_choice_field(self, db_field, request, **kwargs):\n+        \"\"\"\n+        Get a form Field for a database Field that has declared choices.\n+        \"\"\"\n+        # If the field is named as a radio_field, use a RadioSelect\n+        if db_field.name in self.radio_fields:\n+            # Avoid stomping on custom widget/choices arguments.\n+            if 'widget' not in kwargs:\n+                kwargs['widget'] = widgets.AdminRadioSelect(attrs={\n+                    'class': get_ul_class(self.radio_fields[db_field.name]),\n+                })\n+            if 'choices' not in kwargs:\n+                kwargs['choices'] = db_field.get_choices(\n+                    include_blank=db_field.blank,\n+                    blank_choice=[('', _('None'))]\n+                )\n+        return db_field.formfield(**kwargs)\n+\n+    def get_field_queryset(self, db, db_field, request):\n+        \"\"\"\n+        If the ModelAdmin specifies ordering, the queryset should respect that\n+        ordering.  Otherwise don't specify the queryset, let the field decide\n+        (return None in that case).\n+        \"\"\"\n+        related_admin = self.admin_site._registry.get(db_field.remote_field.model)\n+        if related_admin is not None:\n+            ordering = related_admin.get_ordering(request)\n+            if ordering is not None and ordering != ():\n+                return db_field.remote_field.model._default_manager.using(db).order_by(*ordering)\n+        return None\n+\n+    def formfield_for_foreignkey(self, db_field, request, **kwargs):\n+        \"\"\"\n+        Get a form Field for a ForeignKey.\n+        \"\"\"\n+        db = kwargs.get('using')\n+\n+        if 'widget' not in kwargs:\n+            if db_field.name in self.get_autocomplete_fields(request):\n+                kwargs['widget'] = AutocompleteSelect(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in self.raw_id_fields:\n+                kwargs['widget'] = widgets.ForeignKeyRawIdWidget(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in self.radio_fields:\n+                kwargs['widget'] = widgets.AdminRadioSelect(attrs={\n+                    'class': get_ul_class(self.radio_fields[db_field.name]),\n+                })\n+\n+        if 'queryset' not in kwargs:\n+            queryset = self.get_field_queryset(db, db_field, request)\n+            if queryset is not None:\n+                kwargs['queryset'] = queryset\n+\n+        return db_field.formfield(**kwargs)\n+\n+    def formfield_for_manytomany(self, db_field, request, **kwargs):\n+    def formfield_for_manytomany(self, db_field, request, **kwargs):\n+        \"\"\"\n+        Get a form Field for a ManyToManyField.\n+        \"\"\"\n+        # If it uses an intermediary model that isnt auto created, dont show\n+        # a field in admin.\n+        if not db_field.remote_field.through._meta.auto_created:\n+            return None\n+        db = kwargs.get(\"using\")\n+\n+        if \"widget\" not in kwargs:\n+            autocomplete_fields = self.get_autocomplete_fields(request)\n+            if db_field.name in autocomplete_fields:\n+                kwargs[\"widget\"] = AutocompleteSelectMultiple(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in self.raw_id_fields:\n+                kwargs[\"widget\"] = widgets.ManyToManyRawIdWidget(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in [*self.filter_vertical, *self.filter_horizontal]:\n+                kwargs[\"widget\"] = widgets.FilteredSelectMultiple(\n+                    db_field.verbose_name,\n+                )\n+\n+        if 'queryset' not in kwargs:\n+            queryset = self.get_field_queryset(db, db_field, request)\n+            if queryset is not None:\n+                kwargs['queryset'] = queryset\n+\n+        form_field = db_field.formfield(**kwargs)\n+        if (isinstance(form_field.widget, SelectMultiple) and\n+                not isinstance(form_field.widget, (CheckboxSelectMultiple, AutocompleteSelectMultiple))):\n+            msg = _('Hold down \"Control\", or \"Command\" on a Mac, to select more than one.')\n+            help_text = form_field.help_text\n+            form_field.help_text = format_lazy('{} {}', help_text, msg) if help_text else msg\n+        return form_field\n+\n+    def get_autocomplete_fields(self, request):\n+        \"\"\"\n+        Return a list of ForeignKey and/or ManyToMany fields which should use\n+        an autocomplete widget.\n+        \"\"\"\n+        return self.autocomplete_fields\n\\ No newline at end of file\ndiff --git a/django/contrib/admin/options.py.tmp b/django/contrib/admin/options.py.tmp\nnew file mode 100644\nindex 0000000000..99c49d5e01\n--- /dev/null\n+++ b/django/contrib/admin/options.py.tmp\n@@ -0,0 +1,281 @@\n+import copy\n+import json\n+import operator\n+import re\n+from functools import partial, reduce, update_wrapper\n+from urllib.parse import quote as urlquote\n+\n+from django import forms\n+from django.conf import settings\n+from django.contrib import messages\n+from django.contrib.admin import helpers, widgets\n+from django.contrib.admin.checks import (\n+    BaseModelAdminChecks, InlineModelAdminChecks, ModelAdminChecks,\n+)\n+from django.contrib.admin.exceptions import DisallowedModelAdminToField\n+from django.contrib.admin.templatetags.admin_urls import add_preserved_filters\n+from django.contrib.admin.utils import (\n+    NestedObjects, construct_change_message, flatten_fieldsets,\n+    get_deleted_objects, lookup_needs_distinct, model_format_dict,\n+    model_ngettext, quote, unquote,\n+)\n+from django.contrib.admin.views.autocomplete import AutocompleteJsonView\n+from django.contrib.admin.widgets import (\n+    AutocompleteSelect, AutocompleteSelectMultiple,\n+)\n+from django.contrib.auth import get_permission_codename\n+from django.core.exceptions import (\n+    FieldDoesNotExist, FieldError, PermissionDenied, ValidationError,\n+)\n+from django.core.paginator import Paginator\n+from django.db import models, router, transaction\n+from django.db.models.constants import LOOKUP_SEP\n+from django.forms.formsets import DELETION_FIELD_NAME, all_valid\n+from django.forms.models import (\n+    BaseInlineFormSet, inlineformset_factory, modelform_defines_fields,\n+    modelform_factory, modelformset_factory,\n+)\n+from django.forms.widgets import CheckboxSelectMultiple, SelectMultiple\n+from django.http import HttpResponseRedirect\n+from django.http.response import HttpResponseBase\n+from django.template.response import SimpleTemplateResponse, TemplateResponse\n+from django.urls import reverse\n+from django.utils.decorators import method_decorator\n+from django.utils.html import format_html\n+from django.utils.http import urlencode\n+from django.utils.safestring import mark_safe\n+from django.utils.text import capfirst, format_lazy, get_text_list\n+from django.utils.translation import gettext as _, ngettext\n+from django.views.decorators.csrf import csrf_protect\n+from django.views.generic import RedirectView\n+\n+IS_POPUP_VAR = '_popup'\n+TO_FIELD_VAR = '_to_field'\n+\n+\n+HORIZONTAL, VERTICAL = 1, 2\n+\n+\n+def get_content_type_for_model(obj):\n+    # Since this module gets imported in the application's root package,\n+    # it cannot import models from other applications at the module level.\n+    from django.contrib.contenttypes.models import ContentType\n+    return ContentType.objects.get_for_model(obj, for_concrete_model=False)\n+\n+\n+def get_ul_class(radio_style):\n+    return 'radiolist' if radio_style == VERTICAL else 'radiolist inline'\n+\n+\n+class IncorrectLookupParameters(Exception):\n+    pass\n+\n+\n+# Defaults for formfield_overrides. ModelAdmin subclasses can change this\n+# by adding to ModelAdmin.formfield_overrides.\n+\n+FORMFIELD_FOR_DBFIELD_DEFAULTS = {\n+    models.DateTimeField: {\n+        'form_class': forms.SplitDateTimeField,\n+        'widget': widgets.AdminSplitDateTime\n+    },\n+    models.DateField: {'widget': widgets.AdminDateWidget},\n+    models.TimeField: {'widget': widgets.AdminTimeWidget},\n+    models.TextField: {'widget': widgets.AdminTextareaWidget},\n+    models.URLField: {'widget': widgets.AdminURLFieldWidget},\n+    models.IntegerField: {'widget': widgets.AdminIntegerFieldWidget},\n+    models.BigIntegerField: {'widget': widgets.AdminBigIntegerFieldWidget},\n+    models.CharField: {'widget': widgets.AdminTextInputWidget},\n+    models.ImageField: {'widget': widgets.AdminFileWidget},\n+    models.FileField: {'widget': widgets.AdminFileWidget},\n+    models.EmailField: {'widget': widgets.AdminEmailInputWidget},\n+    models.UUIDField: {'widget': widgets.AdminUUIDInputWidget},\n+}\n+\n+csrf_protect_m = method_decorator(csrf_protect)\n+\n+\n+class BaseModelAdmin(metaclass=forms.MediaDefiningClass):\n+    \"\"\"Functionality common to both ModelAdmin and InlineAdmin.\"\"\"\n+\n+    autocomplete_fields = ()\n+    raw_id_fields = ()\n+    fields = None\n+    exclude = None\n+    fieldsets = None\n+    form = forms.ModelForm\n+    filter_vertical = ()\n+    filter_horizontal = ()\n+    radio_fields = {}\n+    prepopulated_fields = {}\n+    formfield_overrides = {}\n+    readonly_fields = ()\n+    ordering = None\n+    sortable_by = None\n+    view_on_site = True\n+    show_full_result_count = True\n+    checks_class = BaseModelAdminChecks\n+\n+    def check(self, **kwargs):\n+        return self.checks_class().check(self, **kwargs)\n+\n+    def __init__(self):\n+        # Merge FORMFIELD_FOR_DBFIELD_DEFAULTS with the formfield_overrides\n+        # rather than simply overwriting.\n+        overrides = copy.deepcopy(FORMFIELD_FOR_DBFIELD_DEFAULTS)\n+        for k, v in self.formfield_overrides.items():\n+            overrides.setdefault(k, {}).update(v)\n+        self.formfield_overrides = overrides\n+\n+    def formfield_for_dbfield(self, db_field, request, **kwargs):\n+        \"\"\"\n+        Hook for specifying the form Field instance for a given database Field\n+        instance.\n+\n+        If kwargs are given, they're passed to the form Field's constructor.\n+        \"\"\"\n+        # If the field specifies choices, we don't need to look for special\n+        # admin widgets - we just need to use a select widget of some kind.\n+        if db_field.choices:\n+            return self.formfield_for_choice_field(db_field, request, **kwargs)\n+\n+        # ForeignKey or ManyToManyFields\n+        if isinstance(db_field, (models.ForeignKey, models.ManyToManyField)):\n+            # Combine the field kwargs with any options for formfield_overrides.\n+            # Make sure the passed in **kwargs override anything in\n+            # formfield_overrides because **kwargs is more specific, and should\n+            # always win.\n+            if db_field.__class__ in self.formfield_overrides:\n+                kwargs = {**self.formfield_overrides[db_field.__class__], **kwargs}\n+\n+            # Get the correct formfield.\n+            if isinstance(db_field, models.ForeignKey):\n+                formfield = self.formfield_for_foreignkey(db_field, request, **kwargs)\n+            elif isinstance(db_field, models.ManyToManyField):\n+                formfield = self.formfield_for_manytomany(db_field, request, **kwargs)\n+\n+            # For non-raw_id fields, wrap the widget with a wrapper that adds\n+            # extra HTML -- the \"add other\" interface -- to the end of the\n+            # rendered output. formfield can be None if it came from a\n+            # OneToOneField with parent_link=True or a M2M intermediary.\n+            if formfield and db_field.name not in self.raw_id_fields:\n+                related_modeladmin = self.admin_site._registry.get(db_field.remote_field.model)\n+                wrapper_kwargs = {}\n+                if related_modeladmin:\n+                    wrapper_kwargs.update(\n+                        can_add_related=related_modeladmin.has_add_permission(request),\n+                        can_change_related=related_modeladmin.has_change_permission(request),\n+                        can_delete_related=related_modeladmin.has_delete_permission(request),\n+                        can_view_related=related_modeladmin.has_view_permission(request),\n+                    )\n+                formfield.widget = widgets.RelatedFieldWidgetWrapper(\n+                    formfield.widget, db_field.remote_field, self.admin_site, **wrapper_kwargs\n+                )\n+\n+            return formfield\n+\n+        # If we've got overrides for the formfield defined, use 'em. **kwargs\n+        # passed to formfield_for_dbfield override the defaults.\n+        for klass in db_field.__class__.mro():\n+            if klass in self.formfield_overrides:\n+                kwargs = {**copy.deepcopy(self.formfield_overrides[klass]), **kwargs}\n+                return db_field.formfield(**kwargs)\n+\n+        # For any other type of field, just call its formfield() method.\n+        return db_field.formfield(**kwargs)\n+\n+    def formfield_for_choice_field(self, db_field, request, **kwargs):\n+        \"\"\"\n+        Get a form Field for a database Field that has declared choices.\n+        \"\"\"\n+        # If the field is named as a radio_field, use a RadioSelect\n+        if db_field.name in self.radio_fields:\n+            # Avoid stomping on custom widget/choices arguments.\n+            if 'widget' not in kwargs:\n+                kwargs['widget'] = widgets.AdminRadioSelect(attrs={\n+                    'class': get_ul_class(self.radio_fields[db_field.name]),\n+                })\n+            if 'choices' not in kwargs:\n+                kwargs['choices'] = db_field.get_choices(\n+                    include_blank=db_field.blank,\n+                    blank_choice=[('', _('None'))]\n+                )\n+        return db_field.formfield(**kwargs)\n+\n+    def get_field_queryset(self, db, db_field, request):\n+        \"\"\"\n+        If the ModelAdmin specifies ordering, the queryset should respect that\n+        ordering.  Otherwise don't specify the queryset, let the field decide\n+        (return None in that case).\n+        \"\"\"\n+        related_admin = self.admin_site._registry.get(db_field.remote_field.model)\n+        if related_admin is not None:\n+            ordering = related_admin.get_ordering(request)\n+            if ordering is not None and ordering != ():\n+                return db_field.remote_field.model._default_manager.using(db).order_by(*ordering)\n+        return None\n+\n+    def formfield_for_foreignkey(self, db_field, request, **kwargs):\n+        \"\"\"\n+        Get a form Field for a ForeignKey.\n+        \"\"\"\n+        db = kwargs.get('using')\n+\n+        if 'widget' not in kwargs:\n+            if db_field.name in self.get_autocomplete_fields(request):\n+                kwargs['widget'] = AutocompleteSelect(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in self.raw_id_fields:\n+                kwargs['widget'] = widgets.ForeignKeyRawIdWidget(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in self.radio_fields:\n+                kwargs['widget'] = widgets.AdminRadioSelect(attrs={\n+                    'class': get_ul_class(self.radio_fields[db_field.name]),\n+                })\n+\n+        if 'queryset' not in kwargs:\n+            queryset = self.get_field_queryset(db, db_field, request)\n+            if queryset is not None:\n+                kwargs['queryset'] = queryset\n+\n+        return db_field.formfield(**kwargs)\n+\n+    def formfield_for_manytomany(self, db_field, request, **kwargs):\n+    def formfield_for_manytomany(self, db_field, request, **kwargs):\n+        \"\"\"\n+        Get a form Field for a ManyToManyField.\n+        \"\"\"\n+        # If it uses an intermediary model that isnt auto created, dont show\n+        # a field in admin.\n+        if not db_field.remote_field.through._meta.auto_created:\n+            return None\n+        db = kwargs.get(\"using\")\n+\n+        if \"widget\" not in kwargs:\n+            autocomplete_fields = self.get_autocomplete_fields(request)\n+            if db_field.name in autocomplete_fields:\n+                kwargs[\"widget\"] = AutocompleteSelectMultiple(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in self.raw_id_fields:\n+                kwargs[\"widget\"] = widgets.ManyToManyRawIdWidget(db_field.remote_field, self.admin_site, using=db)\n+            elif db_field.name in [*self.filter_vertical, *self.filter_horizontal]:\n+                kwargs[\"widget\"] = widgets.FilteredSelectMultiple(\n+                    db_field.verbose_name,\n+                )\n+\n+        if 'queryset' not in kwargs:\n+            queryset = self.get_field_queryset(db, db_field, request)\n+            if queryset is not None:\n+                kwargs['queryset'] = queryset\n+\n+        form_field = db_field.formfield(**kwargs)\n+        if (isinstance(form_field.widget, SelectMultiple) and\n+                not isinstance(form_field.widget, (CheckboxSelectMultiple, AutocompleteSelectMultiple))):\n+            msg = _('Hold down \"Control\", or \"Command\" on a Mac, to select more than one.')\n+            help_text = form_field.help_text\n+            form_field.help_text = format_lazy('{} {}', help_text, msg) if help_text else msg\n+        return form_field\n+\n+    def get_autocomplete_fields(self, request):\n+        \"\"\"\n+        Return a list of ForeignKey and/or ManyToMany fields which should use\n+        an autocomplete widget.\n+        \"\"\"\n+        return self.autocomplete_fields\n\\ No newline at end of file\n", "proposed_model_patches": []}