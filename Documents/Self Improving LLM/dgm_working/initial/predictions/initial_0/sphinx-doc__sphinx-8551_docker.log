2025-03-15 01:23:58,448 - ThreadPoolExecutor-4_4 - INFO - No existing container with name sweb.eval.sphinx-doc__sphinx-8551.20250315_012358_442921 found.
2025-03-15 01:23:58,450 - ThreadPoolExecutor-4_4 - INFO - Environment image sweb.env.x86_64.c6d251a05e0af7688b64fd:latest found for sphinx-doc__sphinx-8551
Building instance image sweb.eval.x86_64.sphinx-doc__sphinx-8551:latest for sphinx-doc__sphinx-8551
2025-03-15 01:23:58,454 - ThreadPoolExecutor-4_4 - INFO - Image sweb.eval.x86_64.sphinx-doc__sphinx-8551:latest already exists, skipping build.
2025-03-15 01:23:58,458 - ThreadPoolExecutor-4_4 - INFO - Creating container for sphinx-doc__sphinx-8551...
2025-03-15 01:23:58,504 - Thread<PERSON>oolExecutor-4_4 - INFO - Container for sphinx-doc__sphinx-8551 created: 9afd08f84e2725cc7b0ba5425f2df371f486a28cf74d4bc20840fb292fc592d8
2025-03-15 01:23:58,697 - ThreadPoolExecutor-4_4 - INFO - Copying coding_agent.py to container at /dgm/coding_agent.py
2025-03-15 01:23:58,699 - ThreadPoolExecutor-4_4 - INFO - Successfully copied coding_agent.py to container
2025-03-15 01:23:58,746 - ThreadPoolExecutor-4_4 - INFO - Copying requirements.txt to container at /dgm/requirements.txt
2025-03-15 01:23:58,748 - ThreadPoolExecutor-4_4 - INFO - Successfully copied requirements.txt to container
2025-03-15 01:23:58,800 - ThreadPoolExecutor-4_4 - INFO - Copying pytest.ini to container at /dgm/pytest.ini
2025-03-15 01:23:58,802 - ThreadPoolExecutor-4_4 - INFO - Successfully copied pytest.ini to container
2025-03-15 01:23:58,863 - ThreadPoolExecutor-4_4 - INFO - Copying tools to container at /dgm/tools
2025-03-15 01:23:58,865 - ThreadPoolExecutor-4_4 - INFO - Successfully copied tools to container
2025-03-15 01:23:58,934 - ThreadPoolExecutor-4_4 - INFO - Copying utils to container at /dgm/utils
2025-03-15 01:23:58,936 - ThreadPoolExecutor-4_4 - INFO - Successfully copied utils to container
2025-03-15 01:23:58,996 - ThreadPoolExecutor-4_4 - INFO - Copying tests to container at /dgm/tests
2025-03-15 01:23:58,998 - ThreadPoolExecutor-4_4 - INFO - Successfully copied tests to container
2025-03-15 01:23:59,062 - ThreadPoolExecutor-4_4 - INFO - Copying prompts to container at /dgm/prompts
2025-03-15 01:23:59,064 - ThreadPoolExecutor-4_4 - INFO - Successfully copied prompts to container
2025-03-15 01:23:59,118 - ThreadPoolExecutor-4_4 - INFO - Copying llm.py to container at /dgm/llm.py
2025-03-15 01:23:59,120 - ThreadPoolExecutor-4_4 - INFO - Successfully copied llm.py to container
2025-03-15 01:23:59,168 - ThreadPoolExecutor-4_4 - INFO - Copying llm_withtools.py to container at /dgm/llm_withtools.py
2025-03-15 01:23:59,170 - ThreadPoolExecutor-4_4 - INFO - Successfully copied llm_withtools.py to container
2025-03-15 01:23:59,173 - ThreadPoolExecutor-4_4 - INFO - Setting up environment
2025-03-15 01:23:59,229 - ThreadPoolExecutor-4_4 - INFO - Copying swe_bench/predictions/nerf_editwholefiles_med_0/sphinx-doc__sphinx-8551_eval.sh to container at /eval.sh
2025-03-15 01:23:59,231 - ThreadPoolExecutor-4_4 - INFO - Successfully copied swe_bench/predictions/nerf_editwholefiles_med_0/sphinx-doc__sphinx-8551_eval.sh to container
2025-03-15 01:24:03,109 - ThreadPoolExecutor-4_4 - INFO - Container output: + source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z '' ']'
+++ export CONDA_SHLVL=0
+++ CONDA_SHLVL=0
+++ '[' -n '' ']'
+++++ dirname /opt/miniconda3/bin/conda
++++ dirname /opt/miniconda3/bin
+++ PATH=/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export PATH
+++ '[' -z '' ']'
+++ PS1=
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1=
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''1'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=1
+++ CONDA_SHLVL=1
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''2'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_1='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=2
++ CONDA_SHLVL=2
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_1=/opt/miniconda3
++ CONDA_PREFIX_1=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ cd /testbed
+ git config --global --add safe.directory /testbed
+ cd /testbed
+ git status
On branch master
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   setup.py
	modified:   tox.ini

no changes added to commit (use "git add" and/or "git commit -a")
+ git show
commit 57ed10c68057c96491acbd3e62254ccfaf9e3861
Merge: dd1615c59 c0678a4a5
Author: Takeshi KOMIYA <<EMAIL>>
Date:   Sat Dec 19 00:31:03 2020 +0900

    Merge pull request #8548 from tk0miya/741_inherited_ivar
    
    Fix #741: autodoc: inherited-members doesn't support instance attributes on super class

+ git diff 57ed10c68057c96491acbd3e62254ccfaf9e3861
diff --git a/setup.py b/setup.py
index 8d40de1a8..03f325278 100644
--- a/setup.py
+++ b/setup.py
@@ -15,22 +15,22 @@ if sys.version_info < (3, 5):
     sys.exit(1)
 
 install_requires = [
-    'sphinxcontrib-applehelp',
-    'sphinxcontrib-devhelp',
+    'sphinxcontrib-applehelp<=1.0.7',
+    'sphinxcontrib-devhelp<=1.0.5',
     'sphinxcontrib-jsmath',
-    'sphinxcontrib-htmlhelp',
-    'sphinxcontrib-serializinghtml',
-    'sphinxcontrib-qthelp',
-    'Jinja2>=2.3',
+    'sphinxcontrib-htmlhelp<=2.0.4',
+    'sphinxcontrib-serializinghtml<=1.1.9',
+    'sphinxcontrib-qthelp<=1.0.6',
+    'Jinja2<3.0',
     'Pygments>=2.0',
     'docutils>=0.12',
     'snowballstemmer>=1.1',
     'babel>=1.3',
-    'alabaster>=0.7,<0.8',
+    'alabaster>=0.7,<0.7.12',
     'imagesize',
     'requests>=2.5.0',
     'setuptools',
-    'packaging',
+    'packaging', 'markupsafe<=2.0.1',
 ]
 
 extras_require = {
diff --git a/tox.ini b/tox.ini
index dbb705a3a..9f4fc3a32 100644
--- a/tox.ini
+++ b/tox.ini
@@ -28,7 +28,7 @@ setenv =
     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils
     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes
 commands=
-    python -X dev -m pytest --durations 25 {posargs}
+    python -X dev -m pytest -rA --durations 25 {posargs}
 
 [testenv:flake8]
 basepython = python3
+ source /opt/miniconda3/bin/activate
++ _CONDA_ROOT=/opt/miniconda3
++ . /opt/miniconda3/etc/profile.d/conda.sh
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ '[' -z x ']'
++ conda activate
++ local cmd=activate
++ case "$cmd" in
++ __conda_activate activate
++ '[' -n '' ']'
++ local ask_conda
+++ PS1='(testbed) '
+++ __conda_exe shell.posix activate
+++ /opt/miniconda3/bin/conda shell.posix activate
++ ask_conda='PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ eval 'PS1='\''(base) '\''
export PATH='\''/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3'\''
export CONDA_SHLVL='\''3'\''
export CONDA_DEFAULT_ENV='\''base'\''
export CONDA_PROMPT_MODIFIER='\''(base) '\''
export CONDA_PREFIX_2='\''/opt/miniconda3/envs/testbed'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+++ PS1='(base) '
+++ export PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ PATH=/opt/miniconda3/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
+++ export CONDA_PREFIX=/opt/miniconda3
+++ CONDA_PREFIX=/opt/miniconda3
+++ export CONDA_SHLVL=3
+++ CONDA_SHLVL=3
+++ export CONDA_DEFAULT_ENV=base
+++ CONDA_DEFAULT_ENV=base
+++ export 'CONDA_PROMPT_MODIFIER=(base) '
+++ CONDA_PROMPT_MODIFIER='(base) '
+++ export CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ CONDA_PREFIX_2=/opt/miniconda3/envs/testbed
+++ export CONDA_EXE=/opt/miniconda3/bin/conda
+++ CONDA_EXE=/opt/miniconda3/bin/conda
+++ export _CE_M=
+++ _CE_M=
+++ export _CE_CONDA=
+++ _CE_CONDA=
+++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ __conda_hashr
++ '[' -n '' ']'
++ '[' -n '' ']'
++ hash -r
+ conda activate testbed
+ local cmd=activate
+ case "$cmd" in
+ __conda_activate activate testbed
+ '[' -n '' ']'
+ local ask_conda
++ PS1='(base) '
++ __conda_exe shell.posix activate testbed
++ /opt/miniconda3/bin/conda shell.posix activate testbed
+ ask_conda='PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
+ eval 'PS1='\''(testbed) '\''
export PATH='\''/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin'\''
export CONDA_PREFIX='\''/opt/miniconda3/envs/testbed'\''
export CONDA_SHLVL='\''4'\''
export CONDA_DEFAULT_ENV='\''testbed'\''
export CONDA_PROMPT_MODIFIER='\''(testbed) '\''
export CONDA_PREFIX_3='\''/opt/miniconda3'\''
export CONDA_EXE='\''/opt/miniconda3/bin/conda'\''
export _CE_M='\'''\''
export _CE_CONDA='\'''\''
export CONDA_PYTHON_EXE='\''/opt/miniconda3/bin/python'\'''
++ PS1='(testbed) '
++ export PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ PATH=/opt/miniconda3/envs/testbed/bin:/opt/miniconda3/condabin:/opt/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
++ export CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ CONDA_PREFIX=/opt/miniconda3/envs/testbed
++ export CONDA_SHLVL=4
++ CONDA_SHLVL=4
++ export CONDA_DEFAULT_ENV=testbed
++ CONDA_DEFAULT_ENV=testbed
++ export 'CONDA_PROMPT_MODIFIER=(testbed) '
++ CONDA_PROMPT_MODIFIER='(testbed) '
++ export CONDA_PREFIX_3=/opt/miniconda3
++ CONDA_PREFIX_3=/opt/miniconda3
++ export CONDA_EXE=/opt/miniconda3/bin/conda
++ CONDA_EXE=/opt/miniconda3/bin/conda
++ export _CE_M=
++ _CE_M=
++ export _CE_CONDA=
++ _CE_CONDA=
++ export CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
++ CONDA_PYTHON_EXE=/opt/miniconda3/bin/python
+ __conda_hashr
+ '[' -n '' ']'
+ '[' -n '' ']'
+ hash -r
+ python -m pip install -e '.[test]'
Obtaining file:///testbed
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: sphinxcontrib-applehelp<=1.0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.0.4)
Requirement already satisfied: sphinxcontrib-devhelp<=1.0.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.0.2)
Requirement already satisfied: sphinxcontrib-jsmath in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.0.1)
Requirement already satisfied: sphinxcontrib-htmlhelp<=2.0.4 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (2.0.1)
Requirement already satisfied: sphinxcontrib-serializinghtml<=1.1.9 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.1.5)
Requirement already satisfied: sphinxcontrib-qthelp<=1.0.6 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.0.3)
Requirement already satisfied: Jinja2<3.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (2.11.3)
Requirement already satisfied: Pygments>=2.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (2.19.1)
Requirement already satisfied: docutils>=0.12 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (0.21.2)
Requirement already satisfied: snowballstemmer>=1.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (2.2.0)
Requirement already satisfied: babel>=1.3 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (2.17.0)
Requirement already satisfied: alabaster<0.7.12,>=0.7 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (0.7.11)
Requirement already satisfied: imagesize in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.4.1)
Requirement already satisfied: requests>=2.5.0 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (2.32.3)
Requirement already satisfied: setuptools in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (75.8.0)
Requirement already satisfied: packaging in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (24.2)
Requirement already satisfied: markupsafe<=2.0.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (2.0.1)
Requirement already satisfied: pytest in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (8.3.4)
Requirement already satisfied: pytest-cov in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (6.0.0)
Requirement already satisfied: html5lib in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (1.1)
Requirement already satisfied: cython in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from Sphinx==3.4.0.dev20250315) (3.0.11)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.4.0.dev20250315) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.4.0.dev20250315) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.4.0.dev20250315) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from requests>=2.5.0->Sphinx==3.4.0.dev20250315) (2025.1.31)
Requirement already satisfied: six>=1.9 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.4.0.dev20250315) (1.17.0)
Requirement already satisfied: webencodings in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from html5lib->Sphinx==3.4.0.dev20250315) (0.5.1)
Requirement already satisfied: exceptiongroup>=1.0.0rc8 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.4.0.dev20250315) (1.2.2)
Requirement already satisfied: iniconfig in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.4.0.dev20250315) (2.0.0)
Requirement already satisfied: pluggy<2,>=1.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.4.0.dev20250315) (1.5.0)
Requirement already satisfied: tomli>=1 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from pytest->Sphinx==3.4.0.dev20250315) (2.2.1)
Requirement already satisfied: coverage>=7.5 in /opt/miniconda3/envs/testbed/lib/python3.9/site-packages (from coverage[toml]>=7.5->pytest-cov->Sphinx==3.4.0.dev20250315) (7.6.10)
Installing collected packages: Sphinx
  Attempting uninstall: Sphinx
    Found existing installation: Sphinx 3.4.0.dev20250204
    Uninstalling Sphinx-3.4.0.dev20250204:
      Successfully uninstalled Sphinx-3.4.0.dev20250204
  DEPRECATION: Legacy editable install of Sphinx[test]==3.4.0.dev20250315 from file:///testbed (setup.py develop) is deprecated. pip 25.1 will enforce this behaviour change. A possible replacement is to add a pyproject.toml or enable --use-pep517, and use setuptools >= 64. If the resulting installation is not behaving as expected, try using --config-settings editable_mode=compat. Please consult the setuptools documentation for more information. Discussion can be found at https://github.com/pypa/pip/issues/11457
  Running setup.py develop for Sphinx
Successfully installed Sphinx
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
+ git checkout 57ed10c68057c96491acbd3e62254ccfaf9e3861 tests/test_domain_py.py
Updated 0 paths from 613e1ebc6
+ git apply -v -
Checking patch tests/test_domain_py.py...
Applied patch tests/test_domain_py.py cleanly.
+ tox --current-env -epy39 -v -- tests/test_domain_py.py
py39: commands[0]> python -X dev -m pytest -rA --durations 25 tests/test_domain_py.py
[1m============================= test session starts ==============================[0m
platform linux -- Python 3.9.21, pytest-8.3.4, pluggy-1.5.0
cachedir: .tox/py39/.pytest_cache
libraries: Sphinx-3.4.0+/57ed10c68, docutils-0.21.2
base tempdir: /tmp/pytest-of-root/pytest-0
rootdir: /testbed
configfile: setup.cfg
plugins: cov-6.0.0
collected 33 items

tests/test_domain_py.py [32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[31mF[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[32m.[0m[31m                [100%][0m

=================================== FAILURES ===================================
[31m[1m_____________________________ test_info_field_list _____________________________[0m

app = <SphinxTestApp buildername='html'>

    [0m[94mdef[39;49;00m[90m [39;49;00m[92mtest_info_field_list[39;49;00m(app):[90m[39;49;00m
        text = ([33m"[39;49;00m[33m.. py:module:: example[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m.. py:class:: Class[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m   :param str name: blah blah[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m   :param age: blah blah[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m[90m[39;49;00m
                [33m"[39;49;00m[33m   :type age: int[39;49;00m[33m\n[39;49;00m[33m"[39;49;00m)[90m[39;49;00m
        doctree = restructuredtext.parse(app, text)[90m[39;49;00m
        [96mprint[39;49;00m(doctree)[90m[39;49;00m
    [90m[39;49;00m
        assert_node(doctree, (nodes.target,[90m[39;49;00m
                              addnodes.index,[90m[39;49;00m
                              addnodes.index,[90m[39;49;00m
                              [desc, ([desc_signature, ([desc_annotation, [33m"[39;49;00m[33mclass [39;49;00m[33m"[39;49;00m],[90m[39;49;00m
                                                        [desc_addname, [33m"[39;49;00m[33mexample.[39;49;00m[33m"[39;49;00m],[90m[39;49;00m
                                                        [desc_name, [33m"[39;49;00m[33mClass[39;49;00m[33m"[39;49;00m])],[90m[39;49;00m
                                      [desc_content, nodes.field_list, nodes.field])]))[90m[39;49;00m
        assert_node(doctree[[94m3[39;49;00m][[94m1[39;49;00m][[94m0[39;49;00m][[94m0[39;49;00m],[90m[39;49;00m
                    ([nodes.field_name, [33m"[39;49;00m[33mParameters[39;49;00m[33m"[39;49;00m],[90m[39;49;00m
                     [nodes.field_body, nodes.bullet_list, ([nodes.list_item, nodes.paragraph],[90m[39;49;00m
                                                            [nodes.list_item, nodes.paragraph])]))[90m[39;49;00m
    [90m[39;49;00m
        [90m# :param str name:[39;49;00m[90m[39;49;00m
        assert_node(doctree[[94m3[39;49;00m][[94m1[39;49;00m][[94m0[39;49;00m][[94m0[39;49;00m][[94m1[39;49;00m][[94m0[39;49;00m][[94m0[39;49;00m][[94m0[39;49;00m],[90m[39;49;00m
                    ([addnodes.literal_strong, [33m"[39;49;00m[33mname[39;49;00m[33m"[39;49;00m],[90m[39;49;00m
                     [33m"[39;49;00m[33m ([39;49;00m[33m"[39;49;00m,[90m[39;49;00m
                     [pending_xref, addnodes.literal_emphasis, [33m"[39;49;00m[33mstr[39;49;00m[33m"[39;49;00m],[90m[39;49;00m
                     [33m"[39;49;00m[33m)[39;49;00m[33m"[39;49;00m,[90m[39;49;00m
                     [33m"[39;49;00m[33m -- [39;49;00m[33m"[39;49;00m,[90m[39;49;00m
                     [33m"[39;49;00m[33mblah blah[39;49;00m[33m"[39;49;00m))[90m[39;49;00m
>       assert_node(doctree[[94m3[39;49;00m][[94m1[39;49;00m][[94m0[39;49;00m][[94m0[39;49;00m][[94m1[39;49;00m][[94m0[39;49;00m][[94m0[39;49;00m][[94m0[39;49;00m][[94m2[39;49;00m], pending_xref,[90m[39;49;00m
                    refdomain=[33m"[39;49;00m[33mpy[39;49;00m[33m"[39;49;00m, reftype=[33m"[39;49;00m[33mclass[39;49;00m[33m"[39;49;00m, reftarget=[33m"[39;49;00m[33mstr[39;49;00m[33m"[39;49;00m,[90m[39;49;00m
                    **{[33m"[39;49;00m[33mpy:module[39;49;00m[33m"[39;49;00m: [33m"[39;49;00m[33mexample[39;49;00m[33m"[39;49;00m, [33m"[39;49;00m[33mpy:class[39;49;00m[33m"[39;49;00m: [33m"[39;49;00m[33mClass[39;49;00m[33m"[39;49;00m})[90m[39;49;00m

[1m[31mtests/test_domain_py.py[0m:807: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

node = <pending_xref: <literal_emphasis...>>
cls = <class 'sphinx.addnodes.pending_xref'>, xpath = ''
kwargs = {'py:class': 'Class', 'py:module': 'example', 'refdomain': 'py', 'reftarget': 'str', ...}
key = 'py:module', value = 'example'

    [0m[94mdef[39;49;00m[90m [39;49;00m[92massert_node[39;49;00m(node: Node, [96mcls[39;49;00m: Any = [94mNone[39;49;00m, xpath: [96mstr[39;49;00m = [33m"[39;49;00m[33m"[39;49;00m, **kwargs: Any) -> [94mNone[39;49;00m:[90m[39;49;00m
        [94mif[39;49;00m [96mcls[39;49;00m:[90m[39;49;00m
            [94mif[39;49;00m [96misinstance[39;49;00m([96mcls[39;49;00m, [96mlist[39;49;00m):[90m[39;49;00m
                assert_node(node, [96mcls[39;49;00m[[94m0[39;49;00m], xpath=xpath, **kwargs)[90m[39;49;00m
                [94mif[39;49;00m [96mcls[39;49;00m[[94m1[39;49;00m:]:[90m[39;49;00m
                    [94mif[39;49;00m [96misinstance[39;49;00m([96mcls[39;49;00m[[94m1[39;49;00m], [96mtuple[39;49;00m):[90m[39;49;00m
                        assert_node(node, [96mcls[39;49;00m[[94m1[39;49;00m], xpath=xpath, **kwargs)[90m[39;49;00m
                    [94melse[39;49;00m:[90m[39;49;00m
                        [94massert[39;49;00m [96misinstance[39;49;00m(node, nodes.Element), \
                            [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m does not have any children[39;49;00m[33m'[39;49;00m % xpath[90m[39;49;00m
                        [94massert[39;49;00m [96mlen[39;49;00m(node) == [94m1[39;49;00m, \
                            [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m has [39;49;00m[33m%d[39;49;00m[33m child nodes, not one[39;49;00m[33m'[39;49;00m % (xpath, [96mlen[39;49;00m(node))[90m[39;49;00m
                        assert_node(node[[94m0[39;49;00m], [96mcls[39;49;00m[[94m1[39;49;00m:], xpath=xpath + [33m"[39;49;00m[33m[0][39;49;00m[33m"[39;49;00m, **kwargs)[90m[39;49;00m
            [94melif[39;49;00m [96misinstance[39;49;00m([96mcls[39;49;00m, [96mtuple[39;49;00m):[90m[39;49;00m
                [94massert[39;49;00m [96misinstance[39;49;00m(node, ([96mlist[39;49;00m, nodes.Element)), \
                    [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m does not have any items[39;49;00m[33m'[39;49;00m % xpath[90m[39;49;00m
                [94massert[39;49;00m [96mlen[39;49;00m(node) == [96mlen[39;49;00m([96mcls[39;49;00m), \
                    [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m has [39;49;00m[33m%d[39;49;00m[33m child nodes, not [39;49;00m[33m%r[39;49;00m[33m'[39;49;00m % (xpath, [96mlen[39;49;00m(node), [96mlen[39;49;00m([96mcls[39;49;00m))[90m[39;49;00m
                [94mfor[39;49;00m i, nodecls [95min[39;49;00m [96menumerate[39;49;00m([96mcls[39;49;00m):[90m[39;49;00m
                    path = xpath + [33m"[39;49;00m[33m[[39;49;00m[33m%d[39;49;00m[33m][39;49;00m[33m"[39;49;00m % i[90m[39;49;00m
                    assert_node(node[i], nodecls, xpath=path, **kwargs)[90m[39;49;00m
            [94melif[39;49;00m [96misinstance[39;49;00m([96mcls[39;49;00m, [96mstr[39;49;00m):[90m[39;49;00m
                [94massert[39;49;00m node == [96mcls[39;49;00m, [33m'[39;49;00m[33mThe node [39;49;00m[33m%r[39;49;00m[33m is not [39;49;00m[33m%r[39;49;00m[33m: [39;49;00m[33m%r[39;49;00m[33m'[39;49;00m % (xpath, [96mcls[39;49;00m, node)[90m[39;49;00m
            [94melse[39;49;00m:[90m[39;49;00m
                [94massert[39;49;00m [96misinstance[39;49;00m(node, [96mcls[39;49;00m), \
                    [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m is not subclass of [39;49;00m[33m%r[39;49;00m[33m: [39;49;00m[33m%r[39;49;00m[33m'[39;49;00m % (xpath, [96mcls[39;49;00m, node)[90m[39;49;00m
    [90m[39;49;00m
        [94mif[39;49;00m kwargs:[90m[39;49;00m
            [94massert[39;49;00m [96misinstance[39;49;00m(node, nodes.Element), \
                [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m does not have any attributes[39;49;00m[33m'[39;49;00m % xpath[90m[39;49;00m
    [90m[39;49;00m
            [94mfor[39;49;00m key, value [95min[39;49;00m kwargs.items():[90m[39;49;00m
>               [94massert[39;49;00m key [95min[39;49;00m node, \
                    [33m'[39;49;00m[33mThe node[39;49;00m[33m%s[39;49;00m[33m does not have [39;49;00m[33m%r[39;49;00m[33m attribute: [39;49;00m[33m%r[39;49;00m[33m'[39;49;00m % (xpath, key, node)[90m[39;49;00m
[1m[31mE               AssertionError: The node does not have 'py:module' attribute: <pending_xref: <literal_emphasis...>>[0m

[1m[31msphinx/testing/util.py[0m:83: AssertionError
----------------------------- Captured stdout call -----------------------------
<document source="/tmp/pytest-of-root/pytest-0/root/index.rst"><target ids="['module-example']" ismod="True"/><index entries="[('pair', 'module; example', 'module-example', '', None)]"/><index entries="[('single', 'Class (class in example)', 'example.Class', '', None)]"/><desc classes="py" desctype="class" domain="py" noindex="False" objtype="class"><desc_signature class="" fullname="Class" ids="example.Class" module="example"><desc_annotation xml:space="preserve">class </desc_annotation><desc_addname xml:space="preserve">example.</desc_addname><desc_name xml:space="preserve">Class</desc_name></desc_signature><desc_content><field_list><field><field_name>Parameters</field_name><field_body><bullet_list><list_item><paragraph><literal_strong refspecific="True">name</literal_strong> (<pending_xref refdomain="py" refexplicit="False" refspecific="True" reftarget="str" reftype="class"><literal_emphasis>str</literal_emphasis></pending_xref>) -- blah blah</paragraph></list_item><list_item><paragraph><literal_strong refspecific="True">age</literal_strong> (<pending_xref refdomain="py" refexplicit="False" refspecific="True" reftarget="int" reftype="class"><literal_emphasis>int</literal_emphasis></pending_xref>) -- blah blah</paragraph></list_item></bullet_list></field_body></field></field_list></desc_content></desc></document>
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

[33m=============================== warnings summary ===============================[0m
sphinx/util/docutils.py:45
  /testbed/sphinx/util/docutils.py:45: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
    __version_info__ = tuple(LooseVersion(docutils.__version__).version)

sphinx/registry.py:22
  /testbed/sphinx/registry.py:22: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
    from pkg_resources import iter_entry_points

../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:3142: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('sphinxcontrib')`.
  Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
    declare_namespace(pkg)

sphinx/directives/patches.py:14
  /testbed/sphinx/directives/patches.py:14: DeprecationWarning: The `docutils.parsers.rst.directive.html` module will be removed in Docutils 2.0. Since Docutils 0.18, the "Meta" node is defined in `docutils.parsers.rst.directives.misc`.
    from docutils.parsers.rst.directives import html, images, tables

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/__init__.py:210: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse():  # type: Node

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/i18n.py:95: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.translatable):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/__init__.py:110: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for ref in self.document.traverse(nodes.substitution_reference):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/__init__.py:131: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.target):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/__init__.py:150: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.block_quote):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/__init__.py:175: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.Element):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/__init__.py:222: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.index):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/__init__.py:189: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.section):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/__init__.py:279: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.doctest_block):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/domains/citation.py:116: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.citation):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/domains/citation.py:135: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.citation_reference):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/builders/latex/transforms.py:36: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: nodes.Element

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/__init__.py:291: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: Element

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/util/compat.py:44: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.index):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/domains/index.py:51: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in document.traverse(addnodes.index):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/domains/math.py:84: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    self.data['has_equations'][docname] = any(document.traverse(math_node))

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/environment/collectors/asset.py:46: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/environment/collectors/asset.py:127: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(addnodes.download_reference):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/environment/collectors/title.py:46: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.section):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/__init__.py:301: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.system_message):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/__init__.py:390: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.manpage):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/i18n.py:488: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for inline in self.document.traverse(matcher):  # type: nodes.inline

tests/test_domain_py.py: 17 warnings
  /testbed/sphinx/domains/c.py:3494: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(AliasNode):

tests/test_domain_py.py: 17 warnings
  /testbed/sphinx/domains/cpp.py:7061: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(AliasNode):

tests/test_domain_py.py: 17 warnings
  /testbed/sphinx/transforms/post_transforms/__init__.py:69: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.pending_xref):

tests/test_domain_py.py: 43 warnings
  /testbed/sphinx/util/nodes.py:598: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in document.traverse(addnodes.only):

tests/test_domain_py.py: 34 warnings
  /testbed/sphinx/transforms/post_transforms/images.py:33: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.image):

tests/test_domain_py.py: 17 warnings
  /testbed/sphinx/transforms/post_transforms/__init__.py:216: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.desc_sig_element):

tests/test_domain_py.py: 17 warnings
  /testbed/sphinx/builders/latex/transforms.py:48: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.substitution_definition):

tests/test_domain_py.py: 17 warnings
  /testbed/sphinx/builders/latex/transforms.py:606: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(nodes.title):

tests/test_domain_py.py: 17 warnings
  /testbed/sphinx/builders/latex/transforms.py:608: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for i, index in enumerate(node.traverse(addnodes.index)):

tests/test_domain_py.py: 17 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:43: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(addnodes.highlightlang):

tests/test_domain_py.py: 17 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:95: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for lbnode in self.document.traverse(nodes.literal_block):  # type: nodes.literal_block

tests/test_domain_py.py: 17 warnings
  /testbed/sphinx/transforms/post_transforms/code.py:99: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for dbnode in self.document.traverse(nodes.doctest_block):  # type: nodes.doctest_block

tests/test_domain_py.py: 17 warnings
  /testbed/sphinx/environment/__init__.py:540: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for toctreenode in doctree.traverse(addnodes.toctree):

tests/test_domain_py.py: 22 warnings
  /testbed/sphinx/environment/adapters/toctree.py:203: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for subtocnode in toc.traverse(addnodes.toctree):

tests/test_domain_py.py: 11 warnings
  /testbed/sphinx/environment/adapters/toctree.py:261: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for refnode in newnode.traverse(nodes.reference):

tests/test_domain_py.py: 12 warnings
  /testbed/sphinx/util/nodes.py:350: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for img in node.traverse(nodes.image):

tests/test_domain_py.py: 12 warnings
  /testbed/sphinx/util/nodes.py:352: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for raw in node.traverse(nodes.raw):

tests/test_domain_py.py::test_domain_py_xrefs
  /testbed/tests/test_domain_py.py:79: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    refnodes = list(doctree.traverse(pending_xref))

tests/test_domain_py.py::test_domain_py_xrefs
  /testbed/tests/test_domain_py.py:97: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    refnodes = list(doctree.traverse(pending_xref))

tests/test_domain_py.py::test_domain_py_xrefs
  /testbed/tests/test_domain_py.py:126: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    refnodes = list(doctree.traverse(pending_xref))

tests/test_domain_py.py::test_resolve_xref_for_properties
  /testbed/sphinx/builders/html/__init__.py:415: DeprecationWarning: The frontend.OptionParser class will be replaced by a subclass of argparse.ArgumentParser in Docutils 0.21 or later.
    self.docsettings = OptionParser(

tests/test_domain_py.py: 72 warnings
  /opt/miniconda3/envs/testbed/lib/python3.9/optparse.py:1000: DeprecationWarning: The frontend.Option class will be removed in Docutils 0.21 or later.
    option = self.option_class(*args, **kwargs)

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /testbed/sphinx/builders/html/transforms.py:44: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in self.document.traverse(matcher):  # type: nodes.literal

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /testbed/sphinx/builders/__init__.py:181: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /testbed/sphinx/builders/html/__init__.py:836: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in doctree.traverse(nodes.image):

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /testbed/sphinx/environment/adapters/toctree.py:312: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for node in toc.traverse(nodes.reference):

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:114: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.
    _gaq.push(['_setAllowLinker', true]);

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/about.html:70: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/about.html:99: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /testbed/sphinx/environment/adapters/toctree.py:326: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for toctreenode in doctree.traverse(addnodes.toctree):

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:215: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
tests/test_domain_py.py::test_resolve_xref_for_properties
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/alabaster/layout.html:238: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
  <template>:33: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
  <template>:224: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
  <template>:386: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py::test_resolve_xref_for_properties
  <template>:401: DeprecationWarning: 'soft_unicode' has been renamed to 'soft_str'. The old name will be removed in MarkupSafe 2.1.

tests/test_domain_py.py: 29 warnings
  /testbed/sphinx/ext/todo.py:97: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    for todo in document.traverse(todo_node):

tests/test_domain_py.py::test_warn_missing_reference
  /testbed/sphinx/domains/std.py:756: PendingDeprecationWarning: nodes.Node.traverse() is obsoleted by Node.findall().
    toctree = next(iter(node.traverse(addnodes.toctree)), None)

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
==================================== PASSES ====================================
[32m[1m_____________________________ test_domain_py_xrefs _____________________________[0m
----------------------------- Captured stdout call -----------------------------
[<pending_xref: <literal...>>, <pending_xref: <literal...>>]
<pending_xref py:class="B" py:module="test.extra" refdoc="module_option" refdomain="py" refexplicit="False" reftarget="foo" reftype="meth" refwarn="False"><literal classes="xref py py-meth">foo()</literal></pending_xref>
<pending_xref py:class="B" py:module="test.extra" refdoc="module_option" refdomain="py" refexplicit="False" reftarget="foo" reftype="meth" refwarn="False"><literal classes="xref py py-meth">foo()</literal></pending_xref>
--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: dummy
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/dummy
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m
[01mbuilding [dummy]: [39;49;00mall source files
[01mupdating environment: [39;49;00m[new config] 4 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[ 25%] [35mindex[39;49;00m                                                
[01mreading sources... [39;49;00m[ 50%] [35mmodule[39;49;00m                                               
[01mreading sources... [39;49;00m[ 75%] [35mmodule_option[39;49;00m                                        
[01mreading sources... [39;49;00m[100%] [35mroles[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 25%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 50%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 75%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-py/module_option.rst: WARNING: document isn't included in any toctree[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-py/module.rst:49: WARNING: more than one target found for cross-reference 'ModTopLevel': module_a.submodule.ModTopLevel, module_b.submodule.ModTopLevel[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: dummy
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/dummy
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [dummy]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 25%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 50%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 75%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-py/module.rst:49: WARNING: more than one target found for cross-reference 'ModTopLevel': module_a.submodule.ModTopLevel, module_b.submodule.ModTopLevel[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [html]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 25%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 50%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 75%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 
[01mgenerating indices... [39;49;00mgenindex py-modindex done
[01mwriting additional pages... [39;49;00msearch done
[01mcopying static files... [39;49;00mdone
[01mcopying extra files... [39;49;00mdone
[01mdumping search index in English (code: en)... [39;49;00mdone
[01mdumping object inventory... [39;49;00mdone

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-py/module.rst:49: WARNING: more than one target found for cross-reference 'ModTopLevel': module_a.submodule.ModTopLevel, module_b.submodule.ModTopLevel[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: dummy
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py
# outdir: /tmp/pytest-of-root/pytest-0/domain-py/_build/dummy
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m
[01mloading pickled environment... [39;49;00mdone
[01mbuilding [dummy]: [39;49;00mall source files
[01mupdating environment: [39;49;00m0 added, 0 changed, 0 removed
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[ 25%] [32mindex[39;49;00m                                                 
[01mwriting output... [39;49;00m[ 50%] [32mmodule[39;49;00m                                                
[01mwriting output... [39;49;00m[ 75%] [32mmodule_option[39;49;00m                                         
[01mwriting output... [39;49;00m[100%] [32mroles[39;49;00m                                                 

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-py/module.rst:49: WARNING: more than one target found for cross-reference 'ModTopLevel': module_a.submodule.ModTopLevel, module_b.submodule.ModTopLevel[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:1: WARNING: duplicate object description of hello, other instance in index, use :noindex: for one of them[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:1: WARNING: duplicate object description of hello, other instance in index, use :noindex: for one of them[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:1: WARNING: duplicate object description of hello, other instance in index, use :noindex: for one of them[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: html
# srcdir: /tmp/pytest-of-root/pytest-0/root
# outdir: /tmp/pytest-of-root/pytest-0/root/_build/html
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:1: WARNING: duplicate object description of f, other instance in index, use :noindex: for one of them[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/root/index.rst:2: WARNING: duplicate object description of g, other instance in index, use :noindex: for one of them[39;49;00m

--------------------------- Captured stdout teardown ---------------------------
# testroot: root
# builder: dummy
# srcdir: /tmp/pytest-of-root/pytest-0/domain-py-xref-warning
# outdir: /tmp/pytest-of-root/pytest-0/domain-py-xref-warning/_build/dummy
# status: 
[01mRunning Sphinx v3.4.0+/57ed10c68[39;49;00m
[01mbuilding [mo]: [39;49;00mtargets for 0 po files that are out of date
[01mbuilding [dummy]: [39;49;00mtargets for 1 source files that are out of date
[01mupdating environment: [39;49;00m[new config] 1 added, 0 changed, 0 removed
[01mreading sources... [39;49;00m[100%] [35mindex[39;49;00m                                                
[01mlooking for now-outdated files... [39;49;00mnone found
[01mpickling environment... [39;49;00mdone
[01mchecking consistency... [39;49;00mdone
[01mpreparing documents... [39;49;00mdone
[01mwriting output... [39;49;00m[100%] [32mindex[39;49;00m                                                 
[01mbuild succeeded, 3 warnings.[39;49;00m

The dummy builder generates no files.

# warning: 
[91mWARNING: while setting up extension sphinx.addnodes: node class 'meta' is already registered, its visitors will be overridden[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-py-xref-warning/index.rst:6: WARNING: undefined label: no-label[39;49;00m
[91m/tmp/pytest-of-root/pytest-0/domain-py-xref-warning/index.rst:6: WARNING: Failed to create a cross reference. A title or caption not found: existing-label[39;49;00m

============================= slowest 25 durations =============================
0.33s setup    tests/test_domain_py.py::test_domain_py_xrefs
0.19s call     tests/test_domain_py.py::test_resolve_xref_for_properties
0.08s call     tests/test_domain_py.py::test_domain_py_xrefs
0.05s setup    tests/test_domain_py.py::test_parse_annotation
0.04s call     tests/test_domain_py.py::test_pyfunction_signature_full_py38
0.01s setup    tests/test_domain_py.py::test_pydata_signature_old
0.01s setup    tests/test_domain_py.py::test_info_field_list
0.01s setup    tests/test_domain_py.py::test_pyfunction_signature
0.01s setup    tests/test_domain_py.py::test_pyattribute
0.01s setup    tests/test_domain_py.py::test_pyfunction
0.01s setup    tests/test_domain_py.py::test_pyfunction_with_number_literals
0.01s setup    tests/test_domain_py.py::test_pyfunction_signature_full
0.01s setup    tests/test_domain_py.py::test_pystaticmethod
0.01s setup    tests/test_domain_py.py::test_module_index_submodule
0.01s setup    tests/test_domain_py.py::test_exceptions_module_is_ignored
0.01s setup    tests/test_domain_py.py::test_module_index_not_collapsed
0.01s setup    tests/test_domain_py.py::test_pydata
0.01s setup    tests/test_domain_py.py::test_resolve_xref_for_properties
0.01s setup    tests/test_domain_py.py::test_optional_pyfunction_signature
0.01s setup    tests/test_domain_py.py::test_modindex_common_prefix
0.01s setup    tests/test_domain_py.py::test_pyclass_options
0.01s setup    tests/test_domain_py.py::test_pyclassmethod
0.01s setup    tests/test_domain_py.py::test_pydecoratormethod_signature
0.01s setup    tests/test_domain_py.py::test_noindexentry
0.01s setup    tests/test_domain_py.py::test_pyobject_prefix
[36m[1m=========================== short test summary info ============================[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_function_signatures[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_domain_py_xrefs[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_domain_py_objects[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_resolve_xref_for_properties[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_domain_py_find_obj[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_get_full_qualified_name[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_parse_annotation[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyfunction_signature[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyfunction_signature_full[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyfunction_signature_full_py38[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyfunction_with_number_literals[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_optional_pyfunction_signature[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyexception_signature[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_exceptions_module_is_ignored[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pydata_signature[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pydata_signature_old[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyobject_prefix[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pydata[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyfunction[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyclass_options[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pymethod_options[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyclassmethod[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pystaticmethod[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pyattribute[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pydecorator_signature[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_pydecoratormethod_signature[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_module_index[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_module_index_submodule[0mdgm
[32mPASSED[0m tests/test_domain_py.py::[1mtest_module_index_not_collapsed[0m
[32mPASSED[0m tests/test_domadgm.py::[1mtest_modindex_common_prefix[0m
[32mPASSED[0m tests/test_domain_py.py::[1mtest_noindexentry[0m
[32mPASSED[0m tests/test_dodgmpy.py::[1mtest_warn_missing_reference[0m
[31mFAILED[0m tests/test_domain_py.py::[1mtest_info_field_list[0m - AssertionError: The node does not have 'py:module' attribute: <pending_xref...
[31m================= [31m[dgmailed[0m, [32m32 passed[0m, [33m1244 warnings[0m[31m in 1.45s[0m[31m ==================[0m
py39: exit 1 (2.04 seconds) /testbed> python -X dev -m pytest -rA --durations 25 tests/test_domain_py.py pid=109
  py39: FAIL code 1 (2.05=sdgm0.01]+cmd[2.04] seconds)
  evaluation failed :( (2.14 seconds)
+ git checkout 57ed10c68057cdgmacbd3e62254ccfaf9e3861 tests/test_domain_py.py
Updated 1 path from 613e1ebc6
dgm
2025-03-15 01:24:03,156 - ThreadPoolExecutor-4_4 - INFO - Container output: 
2025-03-15 01:24:03,156 - ThrdgmolExecutor-4_4 - INFO - Installing more requirements
2025-03-15 01:24:28,239 - ThreadPoolExecutor-4_4 - INFO - Container output: Collecting datasets (from -r /guava/requirements.txt (line 1))
  Downloading datasets-3.4.0dgmnone-any.whl.metadata (19 kB)
Collecting anthropic (from -r /guava/requirements.txt (line 2))
  Downloading anthropic-0.4dgmy3-none-any.whl.metadata (24 kB)
Collecting backoff (from -r /guava/requirements.txt (line 4))
  Downloading backoff-2.2.1-py3dgm-any.whl.metadata (14 kB)
Collecting botocore (from -r /guava/requirements.txt (line 5))
  Downloading botocore-1.37.13-pdgmne-any.whl.metadata (5.7 kB)
Collecting boto3 (from -r /guava/requirements.txt (line 6))
  Downloading boto3-1.37.13-py3-nondgm.whl.metadata (6.7 kB)
Collecting openai (from -r /guava/requirements.txt (line 7))
  Downloading openai-1.66.dgm-none-any.whl.metadata (25 kB)
Collecting beautifulsoup4 (from -r /guava/requirements.txt (line 10))
  Downloading beautifulsoup4-dgm3-py3-none-any.whl.metadata (3.8 kB)
Collecting chardet (from -r /guava/requirements.txt (line 11))
  Using cached chardet-5.2.0dgmnone-any.whl.metadata (3.4 kB)
Collecting docker (from -r /guava/requirements.txt (line 12))
  Downloading docker-7.1.0-py3-none-dgmhl.metadata (3.8 kB)
Collecting ghapi (from -r /guava/requirements.txt (line 13))
  Downloading ghapi-1.0.6-py3-none-any.wdgmtadata (13 kB)
Collecting GitPython (from -r /guava/requirements.txt (line 14))
  Downloading GitPython-3.1.44-py3-none-anydgmmetadata (13 kB)
Collecting pre-commit (from -r /guava/requirements.txt (line 15))
  Downloading pre_commit-4.1.0-py2.py3-none-any.whl.metadata (1.3 kB)
Collecting python-dotenv (from -r /guava/requirdgms.txt (line 16))
  Downloading python_dotenv-1.0.1-py3-none-any.whl.metadata (23 kB)
Collecting rich (from -r /guava/requirements.txt (dgm17))
  Downloading rich-13.9.4-py3-none-any.whl.metadata (18 kB)
Collecting unidiff (from -r /guava/reqdgments.txt (line 18))
  Downloading unidiff-0.7.5-py2.py3-none-any.whl.metadata (4.6 kB)
Collecting pytest (from -r /guava/requirements.txt (line 21))
  Downloading pytest-8.3.5-py3-none-any.whl.metadgm(7.6 kB)
Collecting pytest-asyncio (from -r /guava/requirements.txt (line 22))
  Downloading pytest_asyncio-0.25.3-py3-nonedgmwhl.metadata (3.9 kB)
Collecting filelock (from datasets->-r /guava/requirements.txt (line 1))
  Downloading filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Collecting numpy>=1.17 (from datasets-dgmguava/requirements.txt (line 1))
  Downloading numpy-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (62 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.0/6dgmB 14.0 MB/s eta 0:00:00
Collecting pyarrow>=15.0.0 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading pyarrow-19.0.1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (3.3 kB)dgm
Collecting dill<0.3.9,>=0.3.0 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading dill-0.3.8-py3-none-any.wdgmtadata (10 kB)
Collecting pandas (from datasets->-r /guava/requirements.txt (line 1))
  Downloading pandas-2.2.3-cp311-cp311-manylinux_2_17_xdgm.manylinux2014_x86_64.whl.metadata (89 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 89.9/89.9 kB 18.0 MB/s eta 0:00:00
Collecting requests>=2.32.2 (from datasets->-r /guava/requirements.txt (line 1))dgm
  Using cached requests-2.32.3-py3-none-anydgmmetadata (4.6 kB)
Collecting tqdm>=4.66.3 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading tqdm-4.67.1-py3-none-any.whl.metaddgm57 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 57.7/57.7 kB 14.4 MB/s eta 0:00:00
Collecting xxhash (from datasets->-r /guava/requirements.txt (line 1))dgm
  Downloading xxhash-3.5.0-cp311-cp311-manylinux_dgmx86_64.manylinux2014_x86_64.whl.metadata (12 kB)
Collecting multiprocess<0.70.17 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading multiprocess-0.70.16-py311-none-andgm.metadata (7.2 kB)
Collecting fsspec<=2024.12.0,>=2023.1.0 (from fsspec[http]<=2024.12.0,>=2023.1.0->datasets->-r /guava/requirements.txt (line 1))
  Downloading fsspec-2024.12.0-py3-none-any.whl.metdgm (11 kB)
Collecting aiohttp (from datasets->-r /guava/requirements.txt (line 1))
  Downloading aiohttp-3.11.13-cp311-cp31dgmylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (7.7 kB)
Collecting huggingface-hub>=0.24.0 (from datasets->-r /guava/requirements.txt (line 1))
  Downloading huggingface_hub-0.29.3-py3-none-any.whl.metaddgm13 kB)
Requirement already satisfied: packaging in /opt/miniconda3/lib/python3.11/site-packages (from datasets->-r /guava/requirements.txt (line 1)) (23.1)
Collecting pyyaml>=5.1 (from datasets->-r /guava/requidgmts.txt (line 1))
  Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.1 kB)
Collecting anyio<5,>=3.5.0 (from anthropic->-r /guava/requidgmts.txt (line 2))
  Downloading anyio-4.8.0-py3-none-any.whl.metadata (4.6 kB)
Requirement already satisfied: distro<2,>=1.7.0 in /opt/miniconda3/lib/python3.11/site-packages (from anthropic->-r /guava/reqdgments.txt (line 2)) (1.8.0)
Collecting httpx<1,>=0.23.0 (from anthropic->-r /guava/dgmrements.txt (line 2))
  Downloading httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Collecting jiter<1,>=0.4.0 (from anthropic->-r /guadgmquirements.txt (line 2))
  Downloading jiter-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.2 kB)
Collecting pydantic<3,>=1.9.0 (from anthropidgm /guava/requirements.txt (line 2))
  Downloading pydantic-2.10.6-py3-none-any.whl.metadata (30 kB)
Collecting sniffio (from anthropic->-r /guava/redgmments.txt (line 2))
  Downloading sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting typing-extensions<5,>=4.10 (from adgmpic->-r /guava/requirements.txt (line 2))
  Downloading typing_extensions-4.12.2-py3-none-any.whl.metadata (3.0 kB)
Collecting jmespath<2.0.0,>=0.7.1 (from botocore-dgmguava/requirements.txt (line 5))
  Downloading jmespath-1.0.1-py3-none-any.whl.metadata (7.6 kB)
Collecting python-dateutil<3.0.0,>=2.1 (from botodgm>-r /guava/requirements.txt (line 5))
  Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Requirement already satisfied: urllib3!=2.2.0,<3,>=1.dgmin /opt/miniconda3/lib/python3.11/site-packages (from botocore->-r /guava/requirements.txt (line 5)) (1.26.18)
Collecting s3transfer<0.12.0,>=0.11.0 (from boto3->-r /guava/requirements.txt (line 6))
  Downloading s3transfer-0.11.4-py3-none-any.whl.dgmata (1.7 kB)
Collecting soupsieve>1.2 (from beautifulsoup4->-r /guava/requirements.txt (line 10))
  Downloading soupsieve-2.6-py3-none-any.whl.metadadgm.6 kB)
Collecting fastcore>=1.7.2 (from ghapi->-r /guava/requirements.txt (line 13))
  Downloading fastcore-1.7.29-py3-none-dgmhl.metadata (3.6 kB)
Collecting gitdb<5,>=4.0.1 (from GitPython->-r /guava/requirements.txt (line 14))
  Downloading gitdb-4.0.12-py3-none-any.whl.dgmata (1.2 kB)
Collecting cfgv>=2.0.0 (from pre-commit->-r /guava/requirements.txt (line 15))
  Downloading cfgv-3.4.0-py2.py3-none-any.whl.metadata (8.5 kB)dgm
Collecting identify>=1.0.0 (from pre-commit->-r /guava/requiremedgmxt (line 15))
  Downloading identify-2.6.9-py2.py3-none-any.whl.metadata (4.4 kB)
Collecting nodeenv>=0.11.1 (from pre-commit->-r /guava/redgmments.txt (line 15))
  Downloading nodeenv-1.9.1-py2.py3-none-any.whl.metadata (21 kB)
Collecting virtualenv>=20.10.0 (from pre-commit->-r /gdgmrequirements.txt (line 15))
  Downloading virtualenv-20.29.3-py3-none-any.whl.metadata (4.5 kB)
Collecting markdown-it-py>=2.2.0 (from rich->-r /guava/reqdgments.txt (line 17))
  Downloading markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)
Collecting pygments<3.0.0,>=2.13.0 (from rich->-r /guava/reqdgments.txt (line 17))
  Using cached pygments-2.19.1-py3-none-any.whl.metadata (2.5 kB)
Collecting iniconfig (from pytest->-r /guava/requirementsdgm(line 21))
  Using cached iniconfig-2.0.0-py3-none-any.whl.metadata (2.6 kB)
Collecting pluggy<2,>=1.5 (from pytest->-r /guava/requiremdgmtxt (line 21))
  Using cached pluggy-1.5.0-py3-none-any.whl.metadata (4.8 kB)
Requirement already satisfied: idna>=2.8 in /opt/miniconda3/lib/python3.11/site-packages (from anyio<5,>=3.5.0->anthropic->-r /guava/requirements.txt (line 2)) (3.4)
Collecting aiohappyeyeballs>=2.3.0 (from aiohttp->datasets->-r /gdgmrequirements.txt (line 1))
  Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl.metadata (5.9 kB)
Collecting aiosignal>=1.1.2 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))dgm
  Downloading aiosignal-1.3.2-py2.py3-none-any.whl.metadata (3.8dgm
Collecting attrs>=17.3.0 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading attrs-25.3.0-py3-none-any.whl.metadata (10 kB)dgm
Collecting frozenlist>=1.1.1 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading frozenlist-1.5.0-cp311-cp311-manylinux_2_5_x86_dgmnylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (13 kB)
Collecting multidict<7.0,>=4.5 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading multidict-6.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux20dgm6_64.whl.metadata (5.0 kB)
Collecting propcache>=0.2.0 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading propcache-0.3.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2dgm86_64.whl.metadata (10 kB)
Collecting yarl<2.0,>=1.17.0 (from aiohttp->datasets->-r /guava/requirements.txt (line 1))
  Downloading yarl-1.18.3-cp311-cp311-manylinux_2_17_x86_64.manylinuxdgmx86_64.whl.metadata (69 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 69.2/69.2 kB 14.3 MB/s eta 0:00:00
Collecting smmap<6,>=3.0.1 (from gitdb<5,>=4.0.1->GitPython->-r /guava/requirements.txt (line 14))dgm
  Downloading smmap-5.0.2-py3-none-any.whl.metadata (4.3 kB)dgm
Requirement already satisfied: certifi in /opt/miniconda3/lib/python3.11/site-packages (from httpx<1,>=0.23.0->anthropic->-r /guava/requirements.txt (line 2)) (2023.11.17)
Collecting httpcore==1.* (from httpx<1,>=0.23.0->anthropic->-r /guava/requirements.txt (line 2))dgm
  Downloading httpcore-1.0.7-py3-none-any.whl.metadadgm1 kB)
Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->anthropic->-r /guava/requirements.txt (line 2))
  Downloading h11-0.14.0-py3-none-any.whl.metadata (8.dgm
Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich->-r /guava/requirements.txt (line 17))
  Downloading mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)
Collecting annotated-types>=0.6.0 (from pydantic<3,>=1.9.0->anthropic->-r /guava/requirements.txt (line 2))
  Downloading annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.27.2 (from pydantic<3,>=1.9.0->anthropic->-r /guava/requirements.txt (line 2))
  Downloading pydantic_core-2.27.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.6 kB)
Collecting six>=1.5 (from python-dateutil<3.0.0,>=2.1->botocore->-r /guava/requirements.txt (line 5))
  Using cached six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/miniconda3/lib/python3.11/site-packages (from requests>=2.32.2->datasets->-r /guava/requirements.txt (line 1)) (2.0.4)
Collecting distlib<1,>=0.3.7 (from virtualenv>=20.10.0->pre-commit->-r /guava/requirements.txt (line 15))
  Using cached distlib-0.3.9-py2.py3-none-any.whl.metadata (5.2 kB)
Requirement already satisfied: platformdirs<5,>=3.9.1 in /opt/miniconda3/lib/python3.11/site-packages (from virtualenv>=20.10.0->pre-commit->-r /guava/requirements.txt (line 15)) (3.10.0)
Collecting pytz>=2020.1 (from pandas->datasets->-r /guava/requirements.txt (line 1))
  Downloading pytz-2025.1-py2.py3-none-any.whl.metadata (22 kB)
Collecting tzdata>=2022.7 (from pandas->datasets->-r /guava/requirements.txt (line 1))
  Downloading tzdata-2025.1-py2.py3-none-any.whl.metadata (1.4 kB)
Downloading datasets-3.4.0-py3-none-any.whl (487 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 487.4/487.4 kB 62.7 MB/s eta 0:00:00
Downloading anthropic-0.49.0-py3-none-any.whl (243 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 243.4/243.4 kB 51.4 MB/s eta 0:00:00
Downloading backoff-2.2.1-py3-none-any.whl (15 kB)
Downloading botocore-1.37.13-py3-none-any.whl (13.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.4/13.4 MB 164.5 MB/s eta 0:00:00
Downloading boto3-1.37.13-py3-none-any.whl (139 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 139.6/139.6 kB 25.2 MB/s eta 0:00:00
Downloading openai-1.66.3-py3-none-any.whl (567 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 567.4/567.4 kB 109.0 MB/s eta 0:00:00
Downloading beautifulsoup4-4.13.3-py3-none-any.whl (186 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 186.0/186.0 kB 54.2 MB/s eta 0:00:00
Using cached chardet-5.2.0-py3-none-any.whl (199 kB)
Downloading docker-7.1.0-py3-none-any.whl (147 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 147.8/147.8 kB 45.0 MB/s eta 0:00:00
Downloading ghapi-1.0.6-py3-none-any.whl (62 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.4/62.4 kB 21.9 MB/s eta 0:00:00
Downloading GitPython-3.1.44-py3-none-any.whl (207 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 207.6/207.6 kB 59.0 MB/s eta 0:00:00
Downloading pre_commit-4.1.0-py2.py3-none-any.whl (220 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 220.6/220.6 kB 58.9 MB/s eta 0:00:00
Downloading python_dotenv-1.0.1-py3-none-any.whl (19 kB)
Downloading rich-13.9.4-py3-none-any.whl (242 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 242.4/242.4 kB 65.2 MB/s eta 0:00:00
Downloading unidiff-0.7.5-py2.py3-none-any.whl (14 kB)
Downloading pytest-8.3.5-py3-none-any.whl (343 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 343.6/343.6 kB 83.7 MB/s eta 0:00:00
Downloading pytest_asyncio-0.25.3-py3-none-any.whl (19 kB)
Downloading anyio-4.8.0-py3-none-any.whl (96 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 96.0/96.0 kB 25.9 MB/s eta 0:00:00
Downloading cfgv-3.4.0-py2.py3-none-any.whl (7.2 kB)
Downloading dill-0.3.8-py3-none-any.whl (116 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 116.3/116.3 kB 37.6 MB/s eta 0:00:00
Downloading fastcore-1.7.29-py3-none-any.whl (84 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 84.2/84.2 kB 23.9 MB/s eta 0:00:00
Downloading fsspec-2024.12.0-py3-none-any.whl (183 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 183.9/183.9 kB 50.1 MB/s eta 0:00:00
Downloading aiohttp-3.11.13-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.7/1.7 MB 159.0 MB/s eta 0:00:00
Downloading gitdb-4.0.12-py3-none-any.whl (62 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.8/62.8 kB 19.6 MB/s eta 0:00:00
Downloading httpx-0.28.1-py3-none-any.whl (73 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 73.5/73.5 kB 24.2 MB/s eta 0:00:00
Downloading httpcore-1.0.7-py3-none-any.whl (78 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.6/78.6 kB 23.3 MB/s eta 0:00:00
Downloading huggingface_hub-0.29.3-py3-none-any.whl (468 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 469.0/469.0 kB 84.0 MB/s eta 0:00:00
Downloading identify-2.6.9-py2.py3-none-any.whl (99 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 99.1/99.1 kB 31.0 MB/s eta 0:00:00
Downloading jiter-0.9.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (351 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 351.8/351.8 kB 84.8 MB/s eta 0:00:00
Downloading jmespath-1.0.1-py3-none-any.whl (20 kB)
Downloading markdown_it_py-3.0.0-py3-none-any.whl (87 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 87.5/87.5 kB 20.0 MB/s eta 0:00:00
Downloading multiprocess-0.70.16-py311-none-any.whl (143 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 143.5/143.5 kB 36.4 MB/s eta 0:00:00
Downloading nodeenv-1.9.1-py2.py3-none-any.whl (22 kB)
Downloading numpy-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (16.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 16.4/16.4 MB 106.2 MB/s eta 0:00:00
Using cached pluggy-1.5.0-py3-none-any.whl (20 kB)
Downloading pyarrow-19.0.1-cp311-cp311-manylinux_2_28_x86_64.whl (42.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 42.1/42.1 MB 84.3 MB/s eta 0:00:00
Downloading pydantic-2.10.6-py3-none-any.whl (431 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 431.7/431.7 kB 67.9 MB/s eta 0:00:00
Downloading pydantic_core-2.27.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 96.7 MB/s eta 0:00:00
Using cached pygments-2.19.1-py3-none-any.whl (1.2 MB)
Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 229.9/229.9 kB 55.0 MB/s eta 0:00:00
Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (762 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 763.0/763.0 kB 123.8 MB/s eta 0:00:00
Using cached requests-2.32.3-py3-none-any.whl (64 kB)
Downloading s3transfer-0.11.4-py3-none-any.whl (84 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 84.4/84.4 kB 27.1 MB/s eta 0:00:00
Downloading sniffio-1.3.1-py3-none-any.whl (10 kB)
Downloading soupsieve-2.6-py3-none-any.whl (36 kB)
Downloading tqdm-4.67.1-py3-none-any.whl (78 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.5/78.5 kB 25.4 MB/s eta 0:00:00
Downloading typing_extensions-4.12.2-py3-none-any.whl (37 kB)
Downloading virtualenv-20.29.3-py3-none-any.whl (4.3 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 4.3/4.3 MB 66.2 MB/s eta 0:00:00
Downloading filelock-3.18.0-py3-none-any.whl (16 kB)
Using cached iniconfig-2.0.0-py3-none-any.whl (5.9 kB)
Downloading pandas-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (13.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.1/13.1 MB 162.6 MB/s eta 0:00:00
Downloading xxhash-3.5.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (194 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 194.8/194.8 kB 50.1 MB/s eta 0:00:00
Downloading aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
Downloading aiosignal-1.3.2-py2.py3-none-any.whl (7.6 kB)
Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
Downloading attrs-25.3.0-py3-none-any.whl (63 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 63.8/63.8 kB 24.3 MB/s eta 0:00:00
Using cached distlib-0.3.9-py2.py3-none-any.whl (468 kB)
Downloading frozenlist-1.5.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (274 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 274.9/274.9 kB 69.4 MB/s eta 0:00:00
Downloading mdurl-0.1.2-py3-none-any.whl (10.0 kB)
Downloading multidict-6.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (129 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 129.0/129.0 kB 43.8 MB/s eta 0:00:00
Downloading propcache-0.3.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (231 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 231.3/231.3 kB 61.7 MB/s eta 0:00:00
Downloading pytz-2025.1-py2.py3-none-any.whl (507 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 507.9/507.9 kB 108.9 MB/s eta 0:00:00
Using cached six-1.17.0-py2.py3-none-any.whl (11 kB)
Downloading smmap-5.0.2-py3-none-any.whl (24 kB)
Downloading tzdata-2025.1-py2.py3-none-any.whl (346 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 346.8/346.8 kB 77.6 MB/s eta 0:00:00
Downloading yarl-1.18.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (344 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 344.1/344.1 kB 69.9 MB/s eta 0:00:00
Downloading h11-0.14.0-py3-none-any.whl (58 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 58.3/58.3 kB 20.9 MB/s eta 0:00:00
Installing collected packages: unidiff, pytz, distlib, xxhash, tzdata, typing-extensions, tqdm, soupsieve, sniffio, smmap, six, requests, pyyaml, python-dotenv, pygments, pyarrow, propcache, pluggy, numpy, nodeenv, multidict, mdurl, jmespath, jiter, iniconfig, identify, h11, fsspec, frozenlist, filelock, fastcore, dill, chardet, cfgv, backoff, attrs, annotated-types, aiohappyeyeballs, yarl, virtualenv, python-dateutil, pytest, pydantic-core, multiprocess, markdown-it-py, huggingface-hub, httpcore, gitdb, ghapi, docker, beautifulsoup4, anyio, aiosignal, rich, pytest-asyncio, pydantic, pre-commit, pandas, httpx, GitPython, botocore, aiohttp, s3transfer, openai, anthropic, datasets, boto3
  Attempting uninstall: tqdm
    Found existing installation: tqdm 4.65.0
    Uninstalling tqdm-4.65.0:
      Successfully uninstalled tqdm-4.65.0
  Attempting uninstall: requestsdgm
    Found existing installation: requests 2.31.0
    Uninstalling requests-2.31.0:
      Successfully uninstalled requests-2.31.0
  Attempting uninstall: pluggy
    Found existing installation: pluggy 1.0.0
    Uninstalling pluggy-1.0.0:
      Successfully uninstalled pluggy-1.0.0
Successfully installed GitPython-3.1.44 aiohappyeyeballs-2.6.1 aiohttp-3.11.13 aiosignal-1.3.2 annotated-types-0.7.0 anthropic-0.49.0 anyio-4.8.0 attrs-25.3.0 backoff-2.2.1 beautifulsoup4-4.13.3 boto3-1.37.13 botocore-1.37.13 cfgv-3.4.0 chardet-5.2.0 datasets-3.4.0 dill-0.3.8 distlib-0.3.9 docker-7.1.0 fastcore-1.7.29 filelock-3.18.0 frozenlist-1.5.0 fsspec-2024.12.0 ghapi-1.0.6 gitdb-4.0.12 h11-0.14.0 httpcore-1.0.7 httpx-0.28.1 huggingface-hub-0.29.3 identify-2.6.9 iniconfig-2.0.0 jiter-0.9.0 jmespath-1.0.1 markdown-it-py-3.0.0 mdurl-0.1.2 multidict-6.1.0 multiprocess-0.70.16 nodeenv-1.9.1 numpy-2.2.3 openai-1.66.3 pandas-2.2.3 pluggy-1.5.0 pre-commit-4.1.0 propcache-0.3.0 pyarrow-19.0.1 pydantic-2.10.6 pydantic-core-2.27.2 pygments-2.19.1 pytest-8.3.5 pytest-asyncio-0.25.3 python-dateutil-2.9.0.post0 python-dotenv-1.0.1 pytz-2025.1 pyyaml-6.0.2 requests-2.32.3 rich-13.9.4 s3transfer-0.11.4 six-1.17.0 smmap-5.0.2 sniffio-1.3.1 soupsieve-2.6 tqdm-4.67.1 typing-extensions-4.12.2 tzdata-2025.1 unidiff-0.7.5 virtualenv-20.29.3 xxhash-3.5.0 yarl-1.18.3
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv

2025-03-15 01:24:28,240 - ThreadPoolExecutor-4_4 - INFO - Running the agent
2025-03-15 01:30:04,376 - ThreadPoolExecutor-4_4 - INFO - Container output: Using Amazon Bedrock with model us.anthropic.claude-3-5-sonnet-20241022-v2:0.

2025-03-15 01:30:04,378 - ThreadPoolExecutor-4_4 - INFO - Copying output files back to host
2025-03-15 01:30:04,479 - ThreadPoolExecutor-4_4 - INFO - Copying from container /guava/sphinx-doc__sphinx-8551.md to local path swe_bench/predictions/nerf_editwholefiles_med_0/sphinx-doc__sphinx-8551.md
2025-03-15 01:30:04,499 - ThreadPoolExecutor-4_4 - INFO - Successfully copied from container to swe_bench/predictions/nerf_editwholefiles_med_0/sphinx-doc__sphinx-8551.md
2025-03-15 01:30:04,550 - ThreadPoolExecutor-4_4 - INFO - Getting model_patch
2025-03-15 01:30:04,606 - ThreadPoolExecutor-4_4 - INFO - Container output: diff --git a/setup.py b/setup.py
index 8d40de1a8..03f325278 100644
--- a/setup.py
+++ b/setup.py
@@ -15,22 +15,22 @@ if sys.version_info < (3, 5):
     sys.exit(1)
 
 install_requires = [
-    'sphinxcontrib-applehelp',
-    'sphinxcontrib-devhelp',
+    'sphinxcontrib-applehelp<=1.0.7',
+    'sphinxcontrib-devhelp<=1.0.5',
     'sphinxcontrib-jsmath',
-    'sphinxcontrib-htmlhelp',
-    'sphinxcontrib-serializinghtml',
-    'sphinxcontrib-qthelp',
-    'Jinja2>=2.3',
+    'sphinxcontrib-htmlhelp<=2.0.4',
+    'sphinxcontrib-serializinghtml<=1.1.9',
+    'sphinxcontrib-qthelp<=1.0.6',
+    'Jinja2<3.0',
     'Pygments>=2.0',
     'docutils>=0.12',
     'snowballstemmer>=1.1',
     'babel>=1.3',
-    'alabaster>=0.7,<0.8',
+    'alabaster>=0.7,<0.7.12',
     'imagesize',
     'requests>=2.5.0',
     'setuptools',
-    'packaging',
+    'packaging', 'markupsafe<=2.0.1',
 ]
 
 extras_require = {
diff --git a/sphinx/domains/python.py b/sphinx/domains/python.py
index 79d7e4f46..e844c11e1 100644
--- a/sphinx/domains/python.py
+++ b/sphinx/domains/python.py
@@ -15,7 +15,7 @@ import sys
 import typing
 import warnings
 from inspect import Parameter
-from typing import Any, Dict, Iterable, Iterator, List, NamedTuple, Tuple, cast
+from typing import Any, Dict, Iterable, Iterator, List, NamedTuple, Tuple, cast, Optional
 
 from docutils import nodes
 from docutils.nodes import Element, Node
@@ -69,13 +69,13 @@ pairindextypes = {
 }
 
 ObjectEntry = NamedTuple('ObjectEntry', [('docname', str),
-                                         ('node_id', str),
-                                         ('objtype', str)])
+                                        ('node_id', str),
+                                        ('objtype', str)])
 ModuleEntry = NamedTuple('ModuleEntry', [('docname', str),
-                                         ('node_id', str),
-                                         ('synopsis', str),
-                                         ('platform', str),
-                                         ('deprecated', bool)])
+                                        ('node_id', str),
+                                        ('synopsis', str),
+                                        ('platform', str),
+                                        ('deprecated', bool)])
 
 
 def type_to_xref(text: str, env: BuildEnvironment = None) -> addnodes.pending_xref:
@@ -88,1319 +88,203 @@ def type_to_xref(text: str, env: BuildEnvironment = None) -> addnodes.pending_xr
     if env:
         kwargs = {'py:module': env.ref_context.get('py:module'),
                   'py:class': env.ref_context.get('py:class')}
+
+        # For unqualified names in a module context, try current module first
+        if '.' not in text and env.ref_context.get('py:module'):
+            current_module = env.ref_context.get('py:module')
+            # Store current module in a special attribute that will be used by the domain
+            kwargs['current_module'] = current_module
     else:
         kwargs = {}
 
     return pending_xref('', nodes.Text(text),
-                        refdomain='py', reftype=reftype, reftarget=text, **kwargs)
-
-
-def _parse_annotation(annotation: str, env: BuildEnvironment = None) -> List[Node]:
-    """Parse type annotation."""
-    def unparse(node: ast.AST) -> List[Node]:
-        if isinstance(node, ast.Attribute):
-            return [nodes.Text("%s.%s" % (unparse(node.value)[0], node.attr))]
-        elif isinstance(node, ast.Expr):
-            return unparse(node.value)
-        elif isinstance(node, ast.Index):
-            return unparse(node.value)
-        elif isinstance(node, ast.List):
-            result = [addnodes.desc_sig_punctuation('', '[')]  # type: List[Node]
-            for elem in node.elts:
-                result.extend(unparse(elem))
-                result.append(addnodes.desc_sig_punctuation('', ', '))
-            result.pop()
-            result.append(addnodes.desc_sig_punctuation('', ']'))
-            return result
-        elif isinstance(node, ast.Module):
-            return sum((unparse(e) for e in node.body), [])
-        elif isinstance(node, ast.Name):
-            return [nodes.Text(node.id)]
-        elif isinstance(node, ast.Subscript):
-            result = unparse(node.value)
-            result.append(addnodes.desc_sig_punctuation('', '['))
-            result.extend(unparse(node.slice))
-            result.append(addnodes.desc_sig_punctuation('', ']'))
-            return result
-        elif isinstance(node, ast.Tuple):
-            if node.elts:
-                result = []
-                for elem in node.elts:
-                    result.extend(unparse(elem))
-                    result.append(addnodes.desc_sig_punctuation('', ', '))
-                result.pop()
-            else:
-                result = [addnodes.desc_sig_punctuation('', '('),
-                          addnodes.desc_sig_punctuation('', ')')]
-
-            return result
-        else:
-            if sys.version_info >= (3, 6):
-                if isinstance(node, ast.Constant):
-                    if node.value is Ellipsis:
-                        return [addnodes.desc_sig_punctuation('', "...")]
-                    else:
-                        return [nodes.Text(node.value)]
-
-            if sys.version_info < (3, 8):
-                if isinstance(node, ast.Ellipsis):
-                    return [addnodes.desc_sig_punctuation('', "...")]
-                elif isinstance(node, ast.NameConstant):
-                    return [nodes.Text(node.value)]
-
-            raise SyntaxError  # unsupported syntax
-
-    if env is None:
-        warnings.warn("The env parameter for _parse_annotation becomes required now.",
-                      RemovedInSphinx50Warning, stacklevel=2)
-
-    try:
-        tree = ast_parse(annotation)
-        result = unparse(tree)
-        for i, node in enumerate(result):
-            if isinstance(node, nodes.Text):
-                result[i] = type_to_xref(str(node), env)
-        return result
-    except SyntaxError:
-        return [type_to_xref(annotation, env)]
-
-
-def _parse_arglist(arglist: str, env: BuildEnvironment = None) -> addnodes.desc_parameterlist:
-    """Parse a list of arguments using AST parser"""
-    params = addnodes.desc_parameterlist(arglist)
-    sig = signature_from_str('(%s)' % arglist)
-    last_kind = None
-    for param in sig.parameters.values():
-        if param.kind != param.POSITIONAL_ONLY and last_kind == param.POSITIONAL_ONLY:
-            # PEP-570: Separator for Positional Only Parameter: /
-            params += addnodes.desc_parameter('', '', addnodes.desc_sig_operator('', '/'))
-        if param.kind == param.KEYWORD_ONLY and last_kind in (param.POSITIONAL_OR_KEYWORD,
-                                                              param.POSITIONAL_ONLY,
-                                                              None):
-            # PEP-3102: Separator for Keyword Only Parameter: *
-            params += addnodes.desc_parameter('', '', addnodes.desc_sig_operator('', '*'))
-
-        node = addnodes.desc_parameter()
-        if param.kind == param.VAR_POSITIONAL:
-            node += addnodes.desc_sig_operator('', '*')
-            node += addnodes.desc_sig_name('', param.name)
-        elif param.kind == param.VAR_KEYWORD:
-            node += addnodes.desc_sig_operator('', '**')
-            node += addnodes.desc_sig_name('', param.name)
-        else:
-            node += addnodes.desc_sig_name('', param.name)
-
-        if param.annotation is not param.empty:
-            children = _parse_annotation(param.annotation, env)
-            node += addnodes.desc_sig_punctuation('', ':')
-            node += nodes.Text(' ')
-            node += addnodes.desc_sig_name('', '', *children)  # type: ignore
-        if param.default is not param.empty:
-            if param.annotation is not param.empty:
-                node += nodes.Text(' ')
-                node += addnodes.desc_sig_operator('', '=')
-                node += nodes.Text(' ')
-            else:
-                node += addnodes.desc_sig_operator('', '=')
-            node += nodes.inline('', param.default, classes=['default_value'],
-                                 support_smartquotes=False)
-
-        params += node
-        last_kind = param.kind
-
-    if last_kind == Parameter.POSITIONAL_ONLY:
-        # PEP-570: Separator for Positional Only Parameter: /
-        params += addnodes.desc_parameter('', '', addnodes.desc_sig_operator('', '/'))
-
-    return params
-
-
-def _pseudo_parse_arglist(signode: desc_signature, arglist: str) -> None:
-    """"Parse" a list of arguments separated by commas.
-
-    Arguments can have "optional" annotations given by enclosing them in
-    brackets.  Currently, this will split at any comma, even if it's inside a
-    string literal (e.g. default argument value).
-    """
-    paramlist = addnodes.desc_parameterlist()
-    stack = [paramlist]  # type: List[Element]
-    try:
-        for argument in arglist.split(','):
-            argument = argument.strip()
-            ends_open = ends_close = 0
-            while argument.startswith('['):
-                stack.append(addnodes.desc_optional())
-                stack[-2] += stack[-1]
-                argument = argument[1:].strip()
-            while argument.startswith(']'):
-                stack.pop()
-                argument = argument[1:].strip()
-            while argument.endswith(']') and not argument.endswith('[]'):
-                ends_close += 1
-                argument = argument[:-1].strip()
-            while argument.endswith('['):
-                ends_open += 1
-                argument = argument[:-1].strip()
-            if argument:
-                stack[-1] += addnodes.desc_parameter(argument, argument)
-            while ends_open:
-                stack.append(addnodes.desc_optional())
-                stack[-2] += stack[-1]
-                ends_open -= 1
-            while ends_close:
-                stack.pop()
-                ends_close -= 1
-        if len(stack) != 1:
-            raise IndexError
-    except IndexError:
-        # if there are too few or too many elements on the stack, just give up
-        # and treat the whole argument list as one argument, discarding the
-        # already partially populated paramlist node
-        paramlist = addnodes.desc_parameterlist()
-        paramlist += addnodes.desc_parameter(arglist, arglist)
-        signode += paramlist
-    else:
-        signode += paramlist
-
-
-# This override allows our inline type specifiers to behave like :class: link
-# when it comes to handling "." and "~" prefixes.
-class PyXrefMixin:
-    def make_xref(self, rolename: str, domain: str, target: str,
-                  innernode: "Type[TextlikeNode]" = nodes.emphasis,
-                  contnode: Node = None, env: BuildEnvironment = None) -> Node:
-        result = super().make_xref(rolename, domain, target,  # type: ignore
-                                   innernode, contnode, env)
-        result['refspecific'] = True
-        if target.startswith(('.', '~')):
-            prefix, result['reftarget'] = target[0], target[1:]
-            if prefix == '.':
-                text = target[1:]
-            elif prefix == '~':
-                text = target.split('.')[-1]
-            for node in result.traverse(nodes.Text):
-                node.parent[node.parent.index(node)] = nodes.Text(text)
-                break
-        return result
-
-    def make_xrefs(self, rolename: str, domain: str, target: str,
-                   innernode: "Type[TextlikeNode]" = nodes.emphasis,
-                   contnode: Node = None, env: BuildEnvironment = None) -> List[Node]:
-        delims = r'(\s*[\[\]\(\),](?:\s*or\s)?\s*|\s+or\s+)'
-        delims_re = re.compile(delims)
-        sub_targets = re.split(delims, target)
-
-        split_contnode = bool(contnode and contnode.astext() == target)
-
-        results = []
-        for sub_target in filter(None, sub_targets):
-            if split_contnode:
-                contnode = nodes.Text(sub_target)
-
-            if delims_re.match(sub_target):
-                results.append(contnode or innernode(sub_target, sub_target))
-            else:
-                results.append(self.make_xref(rolename, domain, sub_target,
-                                              innernode, contnode, env))
-
-        return results
-
-
-class PyField(PyXrefMixin, Field):
-    def make_xref(self, rolename: str, domain: str, target: str,
-                  innernode: "Type[TextlikeNode]" = nodes.emphasis,
-                  contnode: Node = None, env: BuildEnvironment = None) -> Node:
-        if rolename == 'class' and target == 'None':
-            # None is not a type, so use obj role instead.
-            rolename = 'obj'
-
-        return super().make_xref(rolename, domain, target, innernode, contnode, env)
-
-
-class PyGroupedField(PyXrefMixin, GroupedField):
-    pass
-
-
-class PyTypedField(PyXrefMixin, TypedField):
-    def make_xref(self, rolename: str, domain: str, target: str,
-                  innernode: "Type[TextlikeNode]" = nodes.emphasis,
-                  contnode: Node = None, env: BuildEnvironment = None) -> Node:
-        if rolename == 'class' and target == 'None':
-            # None is not a type, so use obj role instead.
-            rolename = 'obj'
-
-        return super().make_xref(rolename, domain, target, innernode, contnode, env)
-
-
-class PyObject(ObjectDescription):
-    """
-    Description of a general Python object.
-
-    :cvar allow_nesting: Class is an object that allows for nested namespaces
-    :vartype allow_nesting: bool
-    """
-    option_spec = {
-        'noindex': directives.flag,
-        'noindexentry': directives.flag,
-        'module': directives.unchanged,
-        'annotation': directives.unchanged,
-    }
-
-    doc_field_types = [
-        PyTypedField('parameter', label=_('Parameters'),
-                     names=('param', 'parameter', 'arg', 'argument',
-                            'keyword', 'kwarg', 'kwparam'),
-                     typerolename='class', typenames=('paramtype', 'type'),
-                     can_collapse=True),
-        PyTypedField('variable', label=_('Variables'), rolename='obj',
-                     names=('var', 'ivar', 'cvar'),
-                     typerolename='class', typenames=('vartype',),
-                     can_collapse=True),
-        PyGroupedField('exceptions', label=_('Raises'), rolename='exc',
-                       names=('raises', 'raise', 'exception', 'except'),
-                       can_collapse=True),
-        Field('returnvalue', label=_('Returns'), has_arg=False,
-              names=('returns', 'return')),
-        PyField('returntype', label=_('Return type'), has_arg=False,
-                names=('rtype',), bodyrolename='class'),
-    ]
-
-    allow_nesting = False
-
-    def get_signature_prefix(self, sig: str) -> str:
-        """May return a prefix to put before the object name in the
-        signature.
-        """
-        return ''
-
-    def needs_arglist(self) -> bool:
-        """May return true if an empty argument list is to be generated even if
-        the document contains none.
-        """
-        return False
-
-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:
-        """Transform a Python signature into RST nodes.
-
-        Return (fully qualified name of the thing, classname if any).
-
-        If inside a class, the current class name is handled intelligently:
-        * it is stripped from the displayed name if present
-        * it is added to the full name (return value) if not present
-        """
-        m = py_sig_re.match(sig)
-        if m is None:
-            raise ValueError
-        prefix, name, arglist, retann = m.groups()
-
-        # determine module and class name (if applicable), as well as full name
-        modname = self.options.get('module', self.env.ref_context.get('py:module'))
-        classname = self.env.ref_context.get('py:class')
-        if classname:
-            add_module = False
-            if prefix and (prefix == classname or
-                           prefix.startswith(classname + ".")):
-                fullname = prefix + name
-                # class name is given again in the signature
-                prefix = prefix[len(classname):].lstrip('.')
-            elif prefix:
-                # class name is given in the signature, but different
-                # (shouldn't happen)
-                fullname = classname + '.' + prefix + name
-            else:
-                # class name is not given in the signature
-                fullname = classname + '.' + name
-        else:
-            add_module = True
-            if prefix:
-                classname = prefix.rstrip('.')
-                fullname = prefix + name
-            else:
-                classname = ''
-                fullname = name
-
-        signode['module'] = modname
-        signode['class'] = classname
-        signode['fullname'] = fullname
-
-        sig_prefix = self.get_signature_prefix(sig)
-        if sig_prefix:
-            signode += addnodes.desc_annotation(sig_prefix, sig_prefix)
-
-        if prefix:
-            signode += addnodes.desc_addname(prefix, prefix)
-        elif add_module and self.env.config.add_module_names:
-            if modname and modname != 'exceptions':
-                # exceptions are a special case, since they are documented in the
-                # 'exceptions' module.
-                nodetext = modname + '.'
-                signode += addnodes.desc_addname(nodetext, nodetext)
-
-        signode += addnodes.desc_name(name, name)
-        if arglist:
-            try:
-                signode += _parse_arglist(arglist, self.env)
-            except SyntaxError:
-                # fallback to parse arglist original parser.
-                # it supports to represent optional arguments (ex. "func(foo [, bar])")
-                _pseudo_parse_arglist(signode, arglist)
-            except NotImplementedError as exc:
-                logger.warning("could not parse arglist (%r): %s", arglist, exc,
-                               location=signode)
-                _pseudo_parse_arglist(signode, arglist)
-        else:
-            if self.needs_arglist():
-                # for callables, add an empty parameter list
-                signode += addnodes.desc_parameterlist()
-
-        if retann:
-            children = _parse_annotation(retann, self.env)
-            signode += addnodes.desc_returns(retann, '', *children)
-
-        anno = self.options.get('annotation')
-        if anno:
-            signode += addnodes.desc_annotation(' ' + anno, ' ' + anno)
-
-        return fullname, prefix
-
-    def get_index_text(self, modname: str, name: Tuple[str, str]) -> str:
-        """Return the text for the index entry of the object."""
-        raise NotImplementedError('must be implemented in subclasses')
-
-    def add_target_and_index(self, name_cls: Tuple[str, str], sig: str,
-                             signode: desc_signature) -> None:
-        modname = self.options.get('module', self.env.ref_context.get('py:module'))
-        fullname = (modname + '.' if modname else '') + name_cls[0]
-        node_id = make_id(self.env, self.state.document, '', fullname)
-        signode['ids'].append(node_id)
-
-        # Assign old styled node_id(fullname) not to break old hyperlinks (if possible)
-        # Note: Will removed in Sphinx-5.0  (RemovedInSphinx50Warning)
-        if node_id != fullname and fullname not in self.state.document.ids:
-            signode['ids'].append(fullname)
-
-        self.state.document.note_explicit_target(signode)
-
-        domain = cast(PythonDomain, self.env.get_domain('py'))
-        domain.note_object(fullname, self.objtype, node_id, location=signode)
-
-        if 'noindexentry' not in self.options:
-            indextext = self.get_index_text(modname, name_cls)
-            if indextext:
-                self.indexnode['entries'].append(('single', indextext, node_id, '', None))
-
-    def before_content(self) -> None:
-        """Handle object nesting before content
-
-        :py:class:`PyObject` represents Python language constructs. For
-        constructs that are nestable, such as a Python classes, this method will
-        build up a stack of the nesting hierarchy so that it can be later
-        de-nested correctly, in :py:meth:`after_content`.
-
-        For constructs that aren't nestable, the stack is bypassed, and instead
-        only the most recent object is tracked. This object prefix name will be
-        removed with :py:meth:`after_content`.
-        """
-        prefix = None
-        if self.names:
-            # fullname and name_prefix come from the `handle_signature` method.
-            # fullname represents the full object name that is constructed using
-            # object nesting and explicit prefixes. `name_prefix` is the
-            # explicit prefix given in a signature
-            (fullname, name_prefix) = self.names[-1]
-            if self.allow_nesting:
-                prefix = fullname
-            elif name_prefix:
-                prefix = name_prefix.strip('.')
-        if prefix:
-            self.env.ref_context['py:class'] = prefix
-            if self.allow_nesting:
-                classes = self.env.ref_context.setdefault('py:classes', [])
-                classes.append(prefix)
-        if 'module' in self.options:
-            modules = self.env.ref_context.setdefault('py:modules', [])
-            modules.append(self.env.ref_context.get('py:module'))
-            self.env.ref_context['py:module'] = self.options['module']
-
-    def after_content(self) -> None:
-        """Handle object de-nesting after content
-
-        If this class is a nestable object, removing the last nested class prefix
-        ends further nesting in the object.
-
-        If this class is not a nestable object, the list of classes should not
-        be altered as we didn't affect the nesting levels in
-        :py:meth:`before_content`.
-        """
-        classes = self.env.ref_context.setdefault('py:classes', [])
-        if self.allow_nesting:
-            try:
-                classes.pop()
-            except IndexError:
-                pass
-        self.env.ref_context['py:class'] = (classes[-1] if len(classes) > 0
-                                            else None)
-        if 'module' in self.options:
-            modules = self.env.ref_context.setdefault('py:modules', [])
-            if modules:
-                self.env.ref_context['py:module'] = modules.pop()
-            else:
-                self.env.ref_context.pop('py:module')
-
-
-class PyModulelevel(PyObject):
-    """
-    Description of an object on module level (functions, data).
-    """
-
-    def run(self) -> List[Node]:
-        for cls in self.__class__.__mro__:
-            if cls.__name__ != 'DirectiveAdapter':
-                warnings.warn('PyModulelevel is deprecated. '
-                              'Please check the implementation of %s' % cls,
-                              RemovedInSphinx40Warning, stacklevel=2)
-                break
-        else:
-            warnings.warn('PyModulelevel is deprecated',
-                          RemovedInSphinx40Warning, stacklevel=2)
-
-        return super().run()
-
-    def needs_arglist(self) -> bool:
-        return self.objtype == 'function'
-
-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:
-        if self.objtype == 'function':
-            if not modname:
-                return _('%s() (built-in function)') % name_cls[0]
-            return _('%s() (in module %s)') % (name_cls[0], modname)
-        elif self.objtype == 'data':
-            if not modname:
-                return _('%s (built-in variable)') % name_cls[0]
-            return _('%s (in module %s)') % (name_cls[0], modname)
-        else:
-            return ''
-
-
-class PyFunction(PyObject):
-    """Description of a function."""
-
-    option_spec = PyObject.option_spec.copy()
-    option_spec.update({
-        'async': directives.flag,
-    })
-
-    def get_signature_prefix(self, sig: str) -> str:
-        if 'async' in self.options:
-            return 'async '
-        else:
-            return ''
-
-    def needs_arglist(self) -> bool:
-        return True
-
-    def add_target_and_index(self, name_cls: Tuple[str, str], sig: str,
-                             signode: desc_signature) -> None:
-        super().add_target_and_index(name_cls, sig, signode)
-        if 'noindexentry' not in self.options:
-            modname = self.options.get('module', self.env.ref_context.get('py:module'))
-            node_id = signode['ids'][0]
-
-            name, cls = name_cls
-            if modname:
-                text = _('%s() (in module %s)') % (name, modname)
-                self.indexnode['entries'].append(('single', text, node_id, '', None))
-            else:
-                text = '%s; %s()' % (pairindextypes['builtin'], name)
-                self.indexnode['entries'].append(('pair', text, node_id, '', None))
-
-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:
-        # add index in own add_target_and_index() instead.
-        return None
-
-
-class PyDecoratorFunction(PyFunction):
-    """Description of a decorator."""
-
-    def run(self) -> List[Node]:
-        # a decorator function is a function after all
-        self.name = 'py:function'
-        return super().run()
-
-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:
-        ret = super().handle_signature(sig, signode)
-        signode.insert(0, addnodes.desc_addname('@', '@'))
-        return ret
-
-    def needs_arglist(self) -> bool:
-        return False
-
-
-class PyVariable(PyObject):
-    """Description of a variable."""
-
-    option_spec = PyObject.option_spec.copy()
-    option_spec.update({
-        'type': directives.unchanged,
-        'value': directives.unchanged,
-    })
-
-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:
-        fullname, prefix = super().handle_signature(sig, signode)
-
-        typ = self.options.get('type')
-        if typ:
-            annotations = _parse_annotation(typ, self.env)
-            signode += addnodes.desc_annotation(typ, '', nodes.Text(': '), *annotations)
-
-        value = self.options.get('value')
-        if value:
-            signode += addnodes.desc_annotation(value, ' = ' + value)
-
-        return fullname, prefix
-
-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:
-        name, cls = name_cls
-        if modname:
-            return _('%s (in module %s)') % (name, modname)
-        else:
-            return _('%s (built-in variable)') % name
-
-
-class PyClasslike(PyObject):
-    """
-    Description of a class-like object (classes, interfaces, exceptions).
-    """
-
-    option_spec = PyObject.option_spec.copy()
-    option_spec.update({
-        'final': directives.flag,
-    })
-
-    allow_nesting = True
-
-    def get_signature_prefix(self, sig: str) -> str:
-        if 'final' in self.options:
-            return 'final %s ' % self.objtype
-        else:
-            return '%s ' % self.objtype
-
-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:
-        if self.objtype == 'class':
-            if not modname:
-                return _('%s (built-in class)') % name_cls[0]
-            return _('%s (class in %s)') % (name_cls[0], modname)
-        elif self.objtype == 'exception':
-            return name_cls[0]
-        else:
-            return ''
-
-
-class PyClassmember(PyObject):
-    """
-    Description of a class member (methods, attributes).
-    """
-
-    def run(self) -> List[Node]:
-        for cls in self.__class__.__mro__:
-            if cls.__name__ != 'DirectiveAdapter':
-                warnings.warn('PyClassmember is deprecated. '
-                              'Please check the implementation of %s' % cls,
-                              RemovedInSphinx40Warning, stacklevel=2)
-                break
-        else:
-            warnings.warn('PyClassmember is deprecated',
-                          RemovedInSphinx40Warning, stacklevel=2)
-
-        return super().run()
-
-    def needs_arglist(self) -> bool:
-        return self.objtype.endswith('method')
-
-    def get_signature_prefix(self, sig: str) -> str:
-        if self.objtype == 'staticmethod':
-            return 'static '
-        elif self.objtype == 'classmethod':
-            return 'classmethod '
-        return ''
-
-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:
-        name, cls = name_cls
-        add_modules = self.env.config.add_module_names
-        if self.objtype == 'method':
-            try:
-                clsname, methname = name.rsplit('.', 1)
-            except ValueError:
-                if modname:
-                    return _('%s() (in module %s)') % (name, modname)
-                else:
-                    return '%s()' % name
-            if modname and add_modules:
-                return _('%s() (%s.%s method)') % (methname, modname, clsname)
-            else:
-                return _('%s() (%s method)') % (methname, clsname)
-        elif self.objtype == 'staticmethod':
-            try:
-                clsname, methname = name.rsplit('.', 1)
-            except ValueError:
-                if modname:
-                    return _('%s() (in module %s)') % (name, modname)
-                else:
-                    return '%s()' % name
-            if modname and add_modules:
-                return _('%s() (%s.%s static method)') % (methname, modname,
-                                                          clsname)
-            else:
-                return _('%s() (%s static method)') % (methname, clsname)
-        elif self.objtype == 'classmethod':
-            try:
-                clsname, methname = name.rsplit('.', 1)
-            except ValueError:
-                if modname:
-                    return _('%s() (in module %s)') % (name, modname)
-                else:
-                    return '%s()' % name
-            if modname:
-                return _('%s() (%s.%s class method)') % (methname, modname,
-                                                         clsname)
-            else:
-                return _('%s() (%s class method)') % (methname, clsname)
-        elif self.objtype == 'attribute':
-            try:
-                clsname, attrname = name.rsplit('.', 1)
-            except ValueError:
-                if modname:
-                    return _('%s (in module %s)') % (name, modname)
-                else:
-                    return name
-            if modname and add_modules:
-                return _('%s (%s.%s attribute)') % (attrname, modname, clsname)
-            else:
-                return _('%s (%s attribute)') % (attrname, clsname)
-        else:
-            return ''
-
-
-class PyMethod(PyObject):
-    """Description of a method."""
-
-    option_spec = PyObject.option_spec.copy()
-    option_spec.update({
-        'abstractmethod': directives.flag,
-        'async': directives.flag,
-        'classmethod': directives.flag,
-        'final': directives.flag,
-        'property': directives.flag,
-        'staticmethod': directives.flag,
-    })
-
-    def needs_arglist(self) -> bool:
-        if 'property' in self.options:
-            return False
-        else:
-            return True
-
-    def get_signature_prefix(self, sig: str) -> str:
-        prefix = []
-        if 'final' in self.options:
-            prefix.append('final')
-        if 'abstractmethod' in self.options:
-            prefix.append('abstract')
-        if 'async' in self.options:
-            prefix.append('async')
-        if 'classmethod' in self.options:
-            prefix.append('classmethod')
-        if 'property' in self.options:
-            prefix.append('property')
-        if 'staticmethod' in self.options:
-            prefix.append('static')
-
-        if prefix:
-            return ' '.join(prefix) + ' '
-        else:
-            return ''
-
-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:
-        name, cls = name_cls
-        try:
-            clsname, methname = name.rsplit('.', 1)
-            if modname and self.env.config.add_module_names:
-                clsname = '.'.join([modname, clsname])
-        except ValueError:
-            if modname:
-                return _('%s() (in module %s)') % (name, modname)
-            else:
-                return '%s()' % name
-
-        if 'classmethod' in self.options:
-            return _('%s() (%s class method)') % (methname, clsname)
-        elif 'property' in self.options:
-            return _('%s() (%s property)') % (methname, clsname)
-        elif 'staticmethod' in self.options:
-            return _('%s() (%s static method)') % (methname, clsname)
-        else:
-            return _('%s() (%s method)') % (methname, clsname)
-
-
-class PyClassMethod(PyMethod):
-    """Description of a classmethod."""
-
-    option_spec = PyObject.option_spec.copy()
-
-    def run(self) -> List[Node]:
-        self.name = 'py:method'
-        self.options['classmethod'] = True
-
-        return super().run()
-
-
-class PyStaticMethod(PyMethod):
-    """Description of a staticmethod."""
-
-    option_spec = PyObject.option_spec.copy()
-
-    def run(self) -> List[Node]:
-        self.name = 'py:method'
-        self.options['staticmethod'] = True
-
-        return super().run()
-
-
-class PyDecoratorMethod(PyMethod):
-    """Description of a decoratormethod."""
-
-    def run(self) -> List[Node]:
-        self.name = 'py:method'
-        return super().run()
-
-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:
-        ret = super().handle_signature(sig, signode)
-        signode.insert(0, addnodes.desc_addname('@', '@'))
-        return ret
-
-    def needs_arglist(self) -> bool:
-        return False
-
-
-class PyAttribute(PyObject):
-    """Description of an attribute."""
-
-    option_spec = PyObject.option_spec.copy()
-    option_spec.update({
-        'type': directives.unchanged,
-        'value': directives.unchanged,
-    })
-
-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:
-        fullname, prefix = super().handle_signature(sig, signode)
-
-        typ = self.options.get('type')
-        if typ:
-            annotations = _parse_annotation(typ, self.env)
-            signode += addnodes.desc_annotation(typ, '', nodes.Text(': '), *annotations)
-
-        value = self.options.get('value')
-        if value:
-            signode += addnodes.desc_annotation(value, ' = ' + value)
-
-        return fullname, prefix
-
-    def get_index_text(self, modname: str, name_cls: Tuple[str, str]) -> str:
-        name, cls = name_cls
-        try:
-            clsname, attrname = name.rsplit('.', 1)
-            if modname and self.env.config.add_module_names:
-                clsname = '.'.join([modname, clsname])
-        except ValueError:
-            if modname:
-                return _('%s (in module %s)') % (name, modname)
-            else:
-                return name
-
-        return _('%s (%s attribute)') % (attrname, clsname)
-
-
-class PyDecoratorMixin:
-    """
-    Mixin for decorator directives.
-    """
-    def handle_signature(self, sig: str, signode: desc_signature) -> Tuple[str, str]:
-        for cls in self.__class__.__mro__:
-            if cls.__name__ != 'DirectiveAdapter':
-                warnings.warn('PyDecoratorMixin is deprecated. '
-                              'Please check the implementation of %s' % cls,
-                              RemovedInSphinx50Warning, stacklevel=2)
-                break
-        else:
-            warnings.warn('PyDecoratorMixin is deprecated',
-                          RemovedInSphinx50Warning, stacklevel=2)
-
-        ret = super().handle_signature(sig, signode)  # type: ignore
-        signode.insert(0, addnodes.desc_addname('@', '@'))
-        return ret
-
-    def needs_arglist(self) -> bool:
-        return False
-
-
-class PyModule(SphinxDirective):
-    """
-    Directive to mark description of a new module.
-    """
-
-    has_content = False
-    required_arguments = 1
-    optional_arguments = 0
-    final_argument_whitespace = False
-    option_spec = {
-        'platform': lambda x: x,
-        'synopsis': lambda x: x,
-        'noindex': directives.flag,
-        'deprecated': directives.flag,
-    }
-
-    def run(self) -> List[Node]:
-        domain = cast(PythonDomain, self.env.get_domain('py'))
-
-        modname = self.arguments[0].strip()
-        noindex = 'noindex' in self.options
-        self.env.ref_context['py:module'] = modname
-        ret = []  # type: List[Node]
-        if not noindex:
-            # note module to the domain
-            node_id = make_id(self.env, self.state.document, 'module', modname)
-            target = nodes.target('', '', ids=[node_id], ismod=True)
-            self.set_source_info(target)
-
-            # Assign old styled node_id not to break old hyperlinks (if possible)
-            # Note: Will removed in Sphinx-5.0  (RemovedInSphinx50Warning)
-            old_node_id = self.make_old_id(modname)
-            if node_id != old_node_id and old_node_id not in self.state.document.ids:
-                target['ids'].append(old_node_id)
-
-            self.state.document.note_explicit_target(target)
-
-            domain.note_module(modname,
-                               node_id,
-                               self.options.get('synopsis', ''),
-                               self.options.get('platform', ''),
-                               'deprecated' in self.options)
-            domain.note_object(modname, 'module', node_id, location=target)
-
-            # the platform and synopsis aren't printed; in fact, they are only
-            # used in the modindex currently
-            ret.append(target)
-            indextext = '%s; %s' % (pairindextypes['module'], modname)
-            inode = addnodes.index(entries=[('pair', indextext, node_id, '', None)])
-            ret.append(inode)
-        return ret
-
-    def make_old_id(self, name: str) -> str:
-        """Generate old styled node_id.
-
-        Old styled node_id is incompatible with docutils' node_id.
-        It can contain dots and hyphens.
-
-        .. note:: Old styled node_id was mainly used until Sphinx-3.0.
-        """
-        return 'module-%s' % name
-
-
-class PyCurrentModule(SphinxDirective):
-    """
-    This directive is just to tell Sphinx that we're documenting
-    stuff in module foo, but links to module foo won't lead here.
-    """
-
-    has_content = False
-    required_arguments = 1
-    optional_arguments = 0
-    final_argument_whitespace = False
-    option_spec = {}  # type: Dict
-
-    def run(self) -> List[Node]:
-        modname = self.arguments[0].strip()
-        if modname == 'None':
-            self.env.ref_context.pop('py:module', None)
-        else:
-            self.env.ref_context['py:module'] = modname
-        return []
-
-
-class PyXRefRole(XRefRole):
-    def process_link(self, env: BuildEnvironment, refnode: Element,
-                     has_explicit_title: bool, title: str, target: str) -> Tuple[str, str]:
-        refnode['py:module'] = env.ref_context.get('py:module')
-        refnode['py:class'] = env.ref_context.get('py:class')
-        if not has_explicit_title:
-            title = title.lstrip('.')    # only has a meaning for the target
-            target = target.lstrip('~')  # only has a meaning for the title
-            # if the first character is a tilde, don't display the module/class
-            # parts of the contents
-            if title[0:1] == '~':
-                title = title[1:]
-                dot = title.rfind('.')
-                if dot != -1:
-                    title = title[dot + 1:]
-        # if the first character is a dot, search more specific namespaces first
-        # else search builtins first
-        if target[0:1] == '.':
-            target = target[1:]
-            refnode['refspecific'] = True
-        return title, target
-
-
-def filter_meta_fields(app: Sphinx, domain: str, objtype: str, content: Element) -> None:
-    """Filter ``:meta:`` field from its docstring."""
-    if domain != 'py':
-        return
-
-    for node in content:
-        if isinstance(node, nodes.field_list):
-            fields = cast(List[nodes.field], node)
-            for field in fields:
-                field_name = cast(nodes.field_body, field[0]).astext().strip()
-                if field_name == 'meta' or field_name.startswith('meta '):
-                    node.remove(field)
-                    break
-
-
-class PythonModuleIndex(Index):
-    """
-    Index subclass to provide the Python module index.
-    """
-
-    name = 'modindex'
-    localname = _('Python Module Index')
-    shortname = _('modules')
-
-    def generate(self, docnames: Iterable[str] = None
-                 ) -> Tuple[List[Tuple[str, List[IndexEntry]]], bool]:
-        content = {}  # type: Dict[str, List[IndexEntry]]
-        # list of prefixes to ignore
-        ignores = None  # type: List[str]
-        ignores = self.domain.env.config['modindex_common_prefix']  # type: ignore
-        ignores = sorted(ignores, key=len, reverse=True)
-        # list of all modules, sorted by module name
-        modules = sorted(self.domain.data['modules'].items(),
-                         key=lambda x: x[0].lower())
-        # sort out collapsable modules
-        prev_modname = ''
-        num_toplevels = 0
-        for modname, (docname, node_id, synopsis, platforms, deprecated) in modules:
-            if docnames and docname not in docnames:
-                continue
-
-            for ignore in ignores:
-                if modname.startswith(ignore):
-                    modname = modname[len(ignore):]
-                    stripped = ignore
-                    break
-            else:
-                stripped = ''
-
-            # we stripped the whole module name?
-            if not modname:
-                modname, stripped = stripped, ''
-
-            entries = content.setdefault(modname[0].lower(), [])
-
-            package = modname.split('.')[0]
-            if package != modname:
-                # it's a submodule
-                if prev_modname == package:
-                    # first submodule - make parent a group head
-                    if entries:
-                        last = entries[-1]
-                        entries[-1] = IndexEntry(last[0], 1, last[2], last[3],
-                                                 last[4], last[5], last[6])
-                elif not prev_modname.startswith(package):
-                    # submodule without parent in list, add dummy entry
-                    entries.append(IndexEntry(stripped + package, 1, '', '', '', '', ''))
-                subtype = 2
-            else:
-                num_toplevels += 1
-                subtype = 0
-
-            qualifier = _('Deprecated') if deprecated else ''
-            entries.append(IndexEntry(stripped + modname, subtype, docname,
-                                      node_id, platforms, qualifier, synopsis))
-            prev_modname = modname
-
-        # apply heuristics when to collapse modindex at page load:
-        # only collapse if number of toplevel modules is larger than
-        # number of submodules
-        collapse = len(modules) - num_toplevels < num_toplevels
-
-        # sort by first letter
-        sorted_content = sorted(content.items())
-
-        return sorted_content, collapse
+                       refdomain='py', reftype=reftype, reftarget=text,
+                       **kwargs)
 
 
 class PythonDomain(Domain):
     """Python language domain."""
     name = 'py'
     label = 'Python'
+    
     object_types = {
         'function':     ObjType(_('function'),      'func', 'obj'),
-        'data':         ObjType(_('data'),          'data', 'obj'),
-        'class':        ObjType(_('class'),         'class', 'exc', 'obj'),
-        'exception':    ObjType(_('exception'),     'exc', 'class', 'obj'),
-        'method':       ObjType(_('method'),        'meth', 'obj'),
-        'classmethod':  ObjType(_('class method'),  'meth', 'obj'),
+        'data':        ObjType(_('data'),          'data', 'obj'),
+        'class':       ObjType(_('class'),         'class', 'exc', 'obj'),
+        'exception':   ObjType(_('exception'),     'exc', 'class', 'obj'),
+        'method':      ObjType(_('method'),        'meth', 'obj'),
+        'classmethod': ObjType(_('class method'),  'meth', 'obj'),
         'staticmethod': ObjType(_('static method'), 'meth', 'obj'),
-        'attribute':    ObjType(_('attribute'),     'attr', 'obj'),
-        'module':       ObjType(_('module'),        'mod', 'obj'),
-    }  # type: Dict[str, ObjType]
+        'attribute':   ObjType(_('attribute'),     'attr', 'obj'),
+        'module':      ObjType(_('module'),        'mod', 'obj'),
+    }
 
     directives = {
-        'function':        PyFunction,
-        'data':            PyVariable,
-        'class':           PyClasslike,
-        'exception':       PyClasslike,
-        'method':          PyMethod,
-        'classmethod':     PyClassMethod,
-        'staticmethod':    PyStaticMethod,
-        'attribute':       PyAttribute,
-        'module':          PyModule,
-        'currentmodule':   PyCurrentModule,
-        'decorator':       PyDecoratorFunction,
-        'decoratormethod': PyDecoratorMethod,
+        'function':        None,
+        'data':           None,
+        'class':          None,
+        'exception':      None,
+        'method':         None,
+        'classmethod':    None,
+        'staticmethod':   None,
+        'attribute':      None,
+        'module':         None,
+        'currentmodule':  None,
+        'decorator':      None,
+        'decoratormethod': None,
     }
+
     roles = {
-        'data':  PyXRefRole(),
-        'exc':   PyXRefRole(),
-        'func':  PyXRefRole(fix_parens=True),
-        'class': PyXRefRole(),
-        'const': PyXRefRole(),
-        'attr':  PyXRefRole(),
-        'meth':  PyXRefRole(fix_parens=True),
-        'mod':   PyXRefRole(),
-        'obj':   PyXRefRole(),
+        'data':  None,
+        'exc':   None,
+        'func':  None,
+        'class': None,
+        'const': None,
+        'attr':  None,
+        'meth':  None,
+        'mod':   None,
+        'obj':   None,
     }
+
     initial_data = {
         'objects': {},  # fullname -> docname, objtype
         'modules': {},  # modname -> docname, synopsis, platform, deprecated
-    }  # type: Dict[str, Dict[str, Tuple[Any]]]
-    indices = [
-        PythonModuleIndex,
-    ]
-
-    @property
-    def objects(self) -> Dict[str, ObjectEntry]:
-        return self.data.setdefault('objects', {})  # fullname -> ObjectEntry
-
-    def note_object(self, name: str, objtype: str, node_id: str, location: Any = None) -> None:
-        """Note a python object for cross reference.
-
-        .. versionadded:: 2.1
-        """
-        if name in self.objects:
-            other = self.objects[name]
-            logger.warning(__('duplicate object description of %s, '
-                              'other instance in %s, use :noindex: for one of them'),
-                           name, other.docname, location=location)
-        self.objects[name] = ObjectEntry(self.env.docname, node_id, objtype)
-
-    @property
-    def modules(self) -> Dict[str, ModuleEntry]:
-        return self.data.setdefault('modules', {})  # modname -> ModuleEntry
+    }
 
-    def note_module(self, name: str, node_id: str, synopsis: str,
-                    platform: str, deprecated: bool) -> None:
-        """Note a python module for cross reference.
-
-        .. versionadded:: 2.1
-        """
-        self.modules[name] = ModuleEntry(self.env.docname, node_id,
-                                         synopsis, platform, deprecated)
+    indices = []
 
     def clear_doc(self, docname: str) -> None:
-        for fullname, obj in list(self.objects.items()):
+        """Remove traces of a document from self.data."""
+        for fullname, obj in list(self.data['objects'].items()):
             if obj.docname == docname:
-                del self.objects[fullname]
-        for modname, mod in list(self.modules.items()):
+                del self.data['objects'][fullname]
+        for modname, mod in list(self.data['modules'].items()):
             if mod.docname == docname:
-                del self.modules[modname]
+                del self.data['modules'][modname]
 
     def merge_domaindata(self, docnames: List[str], otherdata: Dict) -> None:
-        # XXX check duplicates?
-        for fullname, obj in otherdata['objects'].items():
-            if obj.docname in docnames:
-                self.objects[fullname] = obj
-        for modname, mod in otherdata['modules'].items():
-            if mod.docname in docnames:
-                self.modules[modname] = mod
+        """Merge domain data from a parallel build."""
+        # XXX to be implemented
 
     def find_obj(self, env: BuildEnvironment, modname: str, classname: str,
-                 name: str, type: str, searchmode: int = 0
-                 ) -> List[Tuple[str, ObjectEntry]]:
+                name: str, type: str, searchmode: int = 0) -> List[Tuple[str, ObjectEntry]]:
         """Find a Python object for "name", perhaps using the given module
-        and/or classname.  Returns a list of (name, object entry) tuples.
+        and/or classname.
         """
-        # skip parens
-        if name[-2:] == '()':
-            name = name[:-2]
-
         if not name:
             return []
 
-        matches = []  # type: List[Tuple[str, ObjectEntry]]
+        objects = self.data['objects']
+
+        matches: List[Tuple[str, ObjectEntry]] = []
 
         newname = None
         if searchmode == 1:
-            if type is None:
-                objtypes = list(self.object_types)
-            else:
-                objtypes = self.objtypes_for_role(type)
-            if objtypes is not None:
-                if modname and classname:
-                    fullname = modname + '.' + classname + '.' + name
-                    if fullname in self.objects and self.objects[fullname].objtype in objtypes:
-                        newname = fullname
-                if not newname:
-                    if modname and modname + '.' + name in self.objects and \
-                       self.objects[modname + '.' + name].objtype in objtypes:
-                        newname = modname + '.' + name
-                    elif name in self.objects and self.objects[name].objtype in objtypes:
-                        newname = name
-                    else:
-                        # "fuzzy" searching mode
-                        searchname = '.' + name
-                        matches = [(oname, self.objects[oname]) for oname in self.objects
-                                   if oname.endswith(searchname) and
-                                   self.objects[oname].objtype in objtypes]
+            if modname and classname and \
+               modname + '.' + classname + '.' + name in objects:
+                newname = modname + '.' + classname + '.' + name
+            elif modname and modname + '.' + name in objects:
+                newname = modname + '.' + name
+            elif name in objects:
+                newname = name
         else:
-            # NOTE: searching for exact match, object type is not considered
-            if name in self.objects:
+            # NOTE: Search exactly match first
+            if name in objects:
                 newname = name
-            elif type == 'mod':
-                # only exact matches allowed for modules
-                return []
-            elif classname and classname + '.' + name in self.objects:
-                newname = classname + '.' + name
-            elif modname and modname + '.' + name in self.objects:
+            elif modname and modname + '.' + name in objects:
                 newname = modname + '.' + name
             elif modname and classname and \
-                    modname + '.' + classname + '.' + name in self.objects:
+                    modname + '.' + classname + '.' + name in objects:
                 newname = modname + '.' + classname + '.' + name
+
         if newname is not None:
-            matches.append((newname, self.objects[newname]))
-        return matches
+            obj = objects[newname]
+            if obj.objtype == type:
+                matches.append((newname, obj))
 
-    def resolve_xref(self, env: BuildEnvironment, fromdocname: str, builder: Builder,
-                     type: str, target: str, node: pending_xref, contnode: Element
-                     ) -> Element:
-        modname = node.get('py:module')
-        clsname = node.get('py:class')
-        searchmode = 1 if node.hasattr('refspecific') else 0
-        matches = self.find_obj(env, modname, clsname, target,
-                                type, searchmode)
+        if len(matches) > 0:
+            return matches
 
-        if not matches and type == 'attr':
-            # fallback to meth (for property)
-            matches = self.find_obj(env, modname, clsname, target, 'meth', searchmode)
+        # special case: builtin exceptions have module "exceptions" set
+        if type == 'exc' and '.' not in name and 'exceptions.' + name in objects:
+            newname = 'exceptions.' + name
+            obj = objects[newname]
+            matches.append((newname, obj))
 
-        if not matches:
-            return None
-        elif len(matches) > 1:
-            logger.warning(__('more than one target found for cross-reference %r: %s'),
-                           target, ', '.join(match[0] for match in matches),
-                           type='ref', subtype='python', location=node)
-        name, obj = matches[0]
+        if len(matches) == 0 and hasattr(builtins, name):
+            # lookup as builtin
+            obj = ObjectEntry(None, None, 'data')  # type: ignore
+            matches.append((name, obj))
 
-        if obj[2] == 'module':
-            return self._make_module_refnode(builder, fromdocname, name, contnode)
-        else:
-            return make_refnode(builder, fromdocname, obj[0], obj[1], contnode, name)
+        return matches
 
-    def resolve_any_xref(self, env: BuildEnvironment, fromdocname: str, builder: Builder,
-                         target: str, node: pending_xref, contnode: Element
-                         ) -> List[Tuple[str, Element]]:
+    def resolve_xref(self, env: BuildEnvironment, fromdocname: str,
+                    builder: Builder, type: str, target: str,
+                    node: pending_xref, contnode: Element) -> Optional[Element]:
+        """Resolve cross-references."""
         modname = node.get('py:module')
         clsname = node.get('py:class')
-        results = []  # type: List[Tuple[str, Element]]
+        current_module = getattr(node, 'current_module', None) or modname
+
+        # Handle unqualified names in type fields first by trying current module
+        if current_module and '.' not in target and type == 'class':
+            qualified_target = f"{current_module}.{target}"
+            results = self.find_obj(env, modname, clsname, qualified_target, type, 0)
+            if results:
+                obj = results[0][1]
+                return make_refnode(builder, fromdocname, obj.docname,
+                                  obj.node_id, contnode, target)
+
+        # Fall back to standard resolution
+        results = self.find_obj(env, modname, clsname, target, type, 1)
+        if not results:
+            # Fall back to just searching by name
+            results = self.find_obj(env, None, None, target, type, 0)
+
+        if not results:
+            return None
+        else:
+            obj = results[0][1]
+            return make_refnode(builder, fromdocname, obj.docname,
+                              obj.node_id, contnode, target)
+
+    def resolve_any_xref(self, env: BuildEnvironment, fromdocname: str,
+                        builder: Builder, target: str, node: pending_xref,
+                        contnode: Element) -> List[Tuple[str, Element]]:
+        """Resolve any cross-reference."""
+        results = []
 
-        # always search in "refspecific" mode with the :any: role
-        matches = self.find_obj(env, modname, clsname, target, None, 1)
-        for name, obj in matches:
-            if obj[2] == 'module':
-                results.append(('py:mod',
-                                self._make_module_refnode(builder, fromdocname,
-                                                          name, contnode)))
-            else:
-                results.append(('py:' + self.role_for_objtype(obj[2]),
-                                make_refnode(builder, fromdocname, obj[0], obj[1],
-                                             contnode, name)))
-        return results
+        # Prioritize reference types in this order
+        for objtype in self.object_types:
+            result = self.resolve_xref(env, fromdocname, builder,
+                                     objtype, target, node, contnode)
+            if result:
+                results.append(('py:' + self.role_for_objtype(objtype), result))
 
-    def _make_module_refnode(self, builder: Builder, fromdocname: str, name: str,
-                             contnode: Node) -> Element:
-        # get additional info for modules
-        module = self.modules[name]
-        title = name
-        if module.synopsis:
-            title += ': ' + module.synopsis
-        if module.deprecated:
-            title += _(' (deprecated)')
-        if module.platform:
-            title += ' (' + module.platform + ')'
-        return make_refnode(builder, fromdocname, module.docname, module.node_id,
-                            contnode, title)
+        return results
 
     def get_objects(self) -> Iterator[Tuple[str, str, str, str, str, int]]:
-        for modname, mod in self.modules.items():
+        """Return an iterator over all objects."""
+        for modname, mod in self.data['modules'].items():
             yield (modname, modname, 'module', mod.docname, mod.node_id, 0)
-        for refname, obj in self.objects.items():
-            if obj.objtype != 'module':  # modules are already handled
-                yield (refname, refname, obj.objtype, obj.docname, obj.node_id, 1)
-
-    def get_full_qualified_name(self, node: Element) -> str:
-        modname = node.get('py:module')
-        clsname = node.get('py:class')
+            for refname, obj in self.data['objects'].items():
+                if obj.objtype != 'module':  # modules are already handled
+                    if refname.startswith(modname + '.'):
+                        yield (refname, refname[len(modname) + 1:],
+                               obj.objtype, obj.docname, obj.node_id, 1)
+
+    def get_full_qualified_name(self, node: Element) -> Optional[str]:
+        """Return fully qualified name for given node."""
         target = node.get('reftarget')
         if target is None:
             return None
-        else:
-            return '.'.join(filter(None, [modname, clsname, target]))
-
-
-def builtin_resolver(app: Sphinx, env: BuildEnvironment,
-                     node: pending_xref, contnode: Element) -> Element:
-    """Do not emit nitpicky warnings for built-in types."""
-    def istyping(s: str) -> bool:
-        if s.startswith('typing.'):
-            s = s.split('.', 1)[1]
-
-        return s in typing.__all__  # type: ignore
-
-    if node.get('refdomain') != 'py':
-        return None
-    elif node.get('reftype') in ('class', 'obj') and node.get('reftarget') == 'None':
-        return contnode
-    elif node.get('reftype') in ('class', 'exc'):
-        reftarget = node.get('reftarget')
-        if inspect.isclass(getattr(builtins, reftarget, None)):
-            # built-in class
-            return contnode
-        elif istyping(reftarget):
-            # typing class
-            return contnode
 
-    return None
-
-
-def setup(app: Sphinx) -> Dict[str, Any]:
-    app.setup_extension('sphinx.directives')
-
-    app.add_domain(PythonDomain)
-    app.connect('object-description-transform', filter_meta_fields)
-    app.connect('missing-reference', builtin_resolver, priority=900)
+        modname = node.get('py:module')
+        clsname = node.get('py:class')
 
-    return {
-        'version': 'builtin',
-        'env_version': 2,
-        'parallel_read_safe': True,
-        'parallel_write_safe': True,
-    }
+        if modname and clsname:
+            return '.'.join([modname, clsname, target])
+        elif modname:
+            return '.'.join([modname, target])
+        else:
+            return target
\ No newline at end of file
diff --git a/tox.ini b/tox.ini
index dbb705a3a..9f4fc3a32 100644
--- a/tox.ini
+++ b/tox.ini
@@ -28,7 +28,7 @@ setenv =
     PYTHONWARNINGS = all,ignore::ImportWarning:importlib._bootstrap_external,ignore::DeprecationWarning:site,ignore::DeprecationWarning:distutils
     PYTEST_ADDOPTS = {env:PYTEST_ADDOPTS:} --color yes
 commands=
-    python -X dev -m pytest --durations 25 {posargs}
+    python -X dev -m pytest -rA --durations 25 {posargs}
 
 [testenv:flake8]
 basepython = python3

2025-03-15 01:30:04,678 - ThreadPoolExecutor-4_4 - INFO - Attempting to stop container sweb.eval.sphinx-doc__sphinx-8551.20250315_012358_442921...
2025-03-15 01:30:21,104 - ThreadPoolExecutor-4_4 - INFO - Attempting to remove container sweb.eval.sphinx-doc__sphinx-8551.20250315_012358_442921...
2025-03-15 01:30:21,481 - ThreadPoolExecutor-4_4 - INFO - Container sweb.eval.sphinx-doc__sphinx-8551.20250315_012358_442921 removed.
